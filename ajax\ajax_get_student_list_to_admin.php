<?php
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');
include('../setRequest.php');
include('../class/clsStudent.php');
include('../class/clsRotation.php');
include('../class/clsAttendance.php');
include('../class/clsInteraction.php');
include('../class/clsStudentRankMaster.php');

//Get All Post Values
if (isset($_POST['rankId']))
	$rankId = ($_POST['rankId']);

if (isset($_POST['currentSchoolId']))
	$currentSchoolId = ($_POST['currentSchoolId']);

if (isset($_POST['inse']))
	$inse = ($_POST['inse']);

if (isset($_POST['isActiveCheckoff']))
	$isActiveCheckoff = ($_POST['isActiveCheckoff']);

//Read Sort and Order parameters
$draw =  $_REQUEST['draw'];
$start =  $_REQUEST['start'];
$length =  $_REQUEST['length'];
$searchExpression = '';
if (!empty($_REQUEST['search']['value'])) {
	$searchExpression =  $_REQUEST['search']['value'];
}

$orderByColumn = '';
$orderByColumnDir = '';
if (isset($_REQUEST['order'])) {
	if ($_REQUEST['order'][0]) {
		$orderByColumnNo = ($_REQUEST['order'][0]['column']);
		$orderByColumnDir = ($_REQUEST['order'][0]['dir']);

		if ($orderByColumnNo == 1) {
			$orderByColumn = 'student.lastName';
		}
		if ($orderByColumnNo == 2) {
			$orderByColumn = 'rankmaster.title';
		}
	}
}

$totalStudent = 0;

//For All Student List
$objStudent = new clsStudent();
$rowsStudentData = $objStudent->GetAllSchoolStudentsForClinical($currentSchoolId, $rankId, 0, 0, 0, $searchExpression, $orderByColumn, $orderByColumnDir);
if ($rowsStudentData) {
	$totalStudent = mysqli_num_rows($rowsStudentData);
} else {
	echo "<b>Student Not Found.</b>";
	exit;
}

//Get Filter Student List 
$rowsStudentFilter = $objStudent->GetAllSchoolStudentsForClinical($currentSchoolId, $rankId, 0, $start, $length, $searchExpression, $orderByColumn, $orderByColumnDir);

$totalStudentFilter = 0;
if ($rowsStudentFilter != '') {
	$totalStudentFilter = mysqli_num_rows($rowsStudentFilter);
}

//Return Data
$returnArray = array();
$dataArray = array();

$TotalHoursSpent = 0;
$TotalpointsAwarded = 0;
$TotalInteractionHoursSpent = 0;
$TotalApprovedTotalHours = 0;
$totalHoursTominutes = 0;
$totalOriginalHoursTominutes = 0;
$totalMinutes = 0;
$totalOriginalHoursForAllStudent = 0;
$originalTotalHours = 0;
$totalOriginalMinutes = 0;
$approvedhours = 0;


if ($totalStudentFilter > 0) {
	while ($row = mysqli_fetch_array($rowsStudentFilter)) {

		$studentId = ($row['studentId']);
		$fName = stripslashes($row['firstName']);
		$lastName = stripslashes($row['lastName']);
		$fullName =  $fName . ' ' . $lastName;
		$rank = stripslashes($row['rank']);
		$objAttendance = new clsAttendance();
		$attendanceTotal = $objAttendance->GetAttendanceTotalForAdmin($studentId);
		
		$totalSeconds = 0;
		//Original hours
		while ($rowTotal = mysqli_fetch_array($attendanceTotal)) {

			$attendanceId = ($rowTotal['attendanceId']);
			$orignalhours = stripslashes($rowTotal['orignalhours']);
			$clockInDateTime = $rowTotal['clockInDateTime'];
			$clockOutDateTime = $rowTotal['clockOutDateTime'];

			$datetime1 = new DateTime($clockInDateTime);
			$datetime2 = new DateTime($clockOutDateTime);

			if ($clockOutDateTime == '' || $clockOutDateTime == '0000-00-00 00:00:00') {
				// echo '<br>hi'.$attendanceId;
				continue;
			}

			if ($orignalhours == '') {
				// Calculate total seconds from the difference between datetime1 and datetime2
				$interval = $datetime1->diff($datetime2);
				$totalSeconds += $interval->days * 24 * 3600 + $interval->h * 3600 + $interval->i * 60;
			} else {
				// Calculate total seconds from the provided original hours
				$explodedHours = explode(':', $orignalhours);
				if (count($explodedHours) == 2) {
					$originalHours = $explodedHours[0];
					$originalMinutes = $explodedHours[1];
					$totalSeconds += $originalHours * 3600 + $originalMinutes * 60;
				} else {
					// Handle the case where the format of $orignalhours is unexpected
					// This could be due to an error in the data or a different format
					// You can log an error or handle it in a way that suits your application
				}
			}
		}

		// Calculate hours and minutes from the total seconds
		$totalHours = floor($totalSeconds / 3600);
		$totalMinutes = floor(($totalSeconds % 3600) / 60);

		$TotalOriginalHours = $totalHours . ':' . sprintf('%02d', $totalMinutes);

		//Approved hours
		$totalSecondsapprove = 0;

		$GetOriginalAttendance = $objAttendance->GetAttendanceTotalForApproved($studentId);
		while ($rowApprovedTotal = mysqli_fetch_array($GetOriginalAttendance)) {
			$approvedTotal = $rowApprovedTotal['approvedhours'];

			// Split the string into hours and minutes
			$parts = explode(':', $approvedTotal);
			if (count($parts) === 2 && is_numeric($parts[0]) && is_numeric($parts[1])) {
				$totalApproveHours = $parts[0];
				$totalApproveMinutes = $parts[1];

				// Calculate total seconds from hours and minutes
				$totalSecondsapprove += $totalApproveHours * 3600 + $totalApproveMinutes * 60;
			} else {
				// Handle invalid format
				// For example, you can log an error or skip this row
				continue;
			}
		}

		// Calculate hours and minutes from the total seconds
		$totalApproveHours = floor($totalSecondsapprove / 3600);
		$totalApproveMinutes = floor(($totalSecondsapprove % 3600) / 60);

		$ApprovedTotalHours = $totalApproveHours . ':' . $totalApproveMinutes;


		unset($objAttendance);
		$objInteraction = new clsInteraction();
		$GetTotalPoint = $objInteraction->GetInteractionPoints($studentId);
		$TotalPoint = $GetTotalPoint['TotalPoint'];
		$TotaltimeSpent = $GetTotalPoint['TotaltimeSpent'];

		//For interaction
		$TotalpointsAwarded += $TotalPoint;
		$TotalInteractionHoursSpent += $TotaltimeSpent;

		unset($objInteraction);

		if ($inse == 1) {
			$url = '<a  href="Attendance.html?studentId=' . EncodeQueryData($studentId) . '">Attendance</a>';
		} else {
			$url = '<a  href="Attendance.html?studentId=' . EncodeQueryData($studentId) . '">Attendance</a>';
			if ($isActiveCheckoff == 1) {
				$url .= '|<a href="checkoff.html?studentId=' . EncodeQueryData($studentId) . '"> Checkoffs</a>';
			} elseif ($isActiveCheckoff == 2) {
				$url .= '|<a href="checkoffusaf.html?studentId=' . EncodeQueryData($studentId) . '"> Checkoffs</a>';
			} else {
				$url .= '|<a href="checkoffs.html?studentId=' . EncodeQueryData($studentId) . '"> Checkoffs</a>';
			}
			$url .= '|<a  href="journallist.html?studentId=' . EncodeQueryData($studentId) . '">Daily Journal</a>';
			if ($isActiveCheckoff == 1) {
				$url .= '|<a  href="caseStudyList.html?studentId=' . EncodeQueryData($studentId) . '">Case Study</a>';
			}
			$url .= ' |<a href="dailyEvalList.html?studentId=' . EncodeQueryData($studentId) . '&Type=C">Daily/Weekly</a>';
			$url .= '|<a href="interaction.html?studentId=' . EncodeQueryData($studentId) . '">Dr. Interaction</a>';
			$url .= '|<a  href="incident.html?studentId=' . EncodeQueryData($studentId) . '">Incident</a>';
			$url .= '|<a href="cievaluationlist.html?studentId=' . EncodeQueryData($studentId) . '">CI Evaluation</a>';
			$url .= '|<a href="pevaluationlist.html?studentId=' . EncodeQueryData($studentId) . '">P Evaluation</a>';
			$url .= '|<a href="siteevaluationlist.html?studentId=' . EncodeQueryData($studentId) . '">Site Evaluation</a>';

			if ($isActiveCheckoff == 1) {
				$url .= '|<a href="volunteerEvalList.html?studentId=' . EncodeQueryData($studentId) . '">Volunteer Evaluation</a>';
			}

			$url .= '|<a href="summativelist.html?studentId=' . EncodeQueryData($studentId) . '">Summative</a>';
			$url .= '|<a href="midtermlist.html?studentId=' . EncodeQueryData($studentId) . '">Midterm</a>';
			$url .= '|<a href="formativelist.html?studentId=' . EncodeQueryData($studentId) . '">Formative</a>';

			if ($isActiveCheckoff == 0) {
				$url .= '|<a href="masteryList.html?studentId=' . EncodeQueryData($studentId) . '">Mastery Evaluation</a>';
			}

			if ($currentSchoolId == 75 || $currentSchoolId == 121)
				$url .= '|<a href="pefList.html?studentId=' . EncodeQueryData($studentId) . '">PEF Evaluation</a>';
			if ($currentSchoolId == 75 || $currentSchoolId == 127)
				$url .= '|<a href="floorTherapyAndICUEvaluationlist.html?studentId=' . EncodeQueryData($studentId) . '">Floor Therapy and ICU Evaluation</a>';

			$url .= '|<a href="equipmentlist.html?studentId=' . EncodeQueryData($studentId) . '">Equipment List</a>';
			$url .= '|<a href="procedurecountsNew.html?studentId=' . EncodeQueryData($studentId) . '">Procedure Count</a>';
			$url .= '|<a href="soapNoteList.html?studentId=' . EncodeQueryData($studentId) . '">Soap Note</a>';

		}

		$cardArray[0] = $fName;
		$cardArray[1] = $lastName;
		$cardArray[2] = $rank;
		$cardArray[3] = $TotaltimeSpent;
		$cardArray[4] = number_format((float)$TotalPoint, 2, '.', '');
		$cardArray[5] = $ApprovedTotalHours;
		$cardArray[6] = $TotalOriginalHours;
		$cardArray[7] = $url;

		array_push($dataArray, $cardArray);
	}
}


$returnArray['draw'] = intval($draw);

if ($searchExpression != "") {
	$returnArray['recordsTotal'] = intval($totalStudent);
	$returnArray['recordsFiltered'] = intval($totalStudent);
} else {
	$returnArray['recordsTotal'] = intval($totalStudent);
	$returnArray['recordsFiltered'] = intval($totalStudent);
}

$returnArray['data'] = $dataArray;

echo json_encode($returnArray);
exit;
