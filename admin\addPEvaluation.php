<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsRotation.php');
include('../class/clsStudent.php');
include('../class/clsFormative.php');
include('../setRequest.php');
include('../class/clsQuestionOption.php');
include('../class/clsSectionStudentName.php');
include('../class/clsClinician.php');
include('../class/clsPEvaluation.php');
include('../class/clsschoolclinicalsiteunit.php');
include('../class/clsHospitalSite.php');

$objDB = new clsDB();
$loggedUserId = $_SESSION["loggedUserId"];
$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];

$schoolId = 0;
$currentstudentId = 0;
$rotationId = 0;
$studentId = 0;
$studentfullname = '';
$pEvaluationMasterId = 0;
$clinicianId = 0;
$pevaluationrotationid = 0;
$schoolClinicalSiteUnitId = 0;
$display_to_date = date('m/d/Y');
$preceptorName = '';
if (isset($_GET['pevaluationrotationid'])) {
	$pevaluationrotationid = DecodeQueryData($_GET['pevaluationrotationid']);
}
if (isset($_GET['studentId'])) {
	$currentstudentId = DecodeQueryData($_GET['studentId']);
	$studentId = $currentstudentId;
}
if (isset($_GET['rotationId'])) {
	$rotationId = DecodeQueryData($_GET['rotationId']);
	$pevaluationrotationid = $rotationId;
}

//For Edit CI Evaluation
if (isset($_GET['pEvaluationMasterId']) && ($_GET['pevaluationrotationid'])) {
	$pEvaluationMasterId = DecodeQueryData($_GET['pEvaluationMasterId']);
	$schoolId = $currentSchoolId;
	$page_title = "Edit P Evaluation ";
	$bedCrumTitle = 'Edit';

	//For All CI Evalution List
	$objPevaluation = new clsPEvaluation();
	$rowPevaluation = $objPevaluation->GetEvaluationDetails($pEvaluationMasterId);
	if ($rowPevaluation == '') {
		header('location:pevaluationlist.html');
		exit;
	}
	$pevaluationrotationid = ($rowPevaluation['rotationId']);
	$clinicianId = ($rowPevaluation['clinicianId']);
	$schoolClinicalSiteUnitId = ($rowPevaluation['schoolClinicalSiteUnitId']);
	$evaluationDate = ($rowPevaluation['evaluationDate']);
	$evaluationDate = converFromServerTimeZone($evaluationDate, $TimeZone);
	if ($evaluationDate != '' && $evaluationDate != '0000-00-00' && $evaluationDate != '01/01/1970') {
		$evaluationDate = date("m/d/Y", strtotime($evaluationDate));
	}

	$studentId = ($rowPevaluation['studentId']);
	$preceptorName = isset($rowPevaluation['preceptorName']) ? $rowPevaluation['preceptorName'] : '';
} else {
	$schoolId = $currentSchoolId;
	$page_title = "Add P Evaluation";
	$bedCrumTitle = 'Add';
}

//For Clinicina Name
$objClinician = new clsClinician();
$Clinician = $objClinician->GetClinicianByRotation($schoolId, $pevaluationrotationid);
unset($objClinician);

//For Student Name
$objStudent = new clsStudent();
$Student = $objStudent->GetStudentsByRotation($schoolId, $pevaluationrotationid);
$Rowstudent = $objStudent->GetSingleStudent($schoolId, $currentstudentId);
$studentfullname = $Rowstudent ? $Rowstudent['firstName'] . ' ' . $Rowstudent['lastName'] : '';
unset($objStudent);

//For CI Section
$totalSection = 0;
$PevaluationSection = $objPevaluation->GetSections($schoolId,1);
if ($PevaluationSection != '') {
	$totalSection = mysqli_num_rows($PevaluationSection);
}

// //For Hospital Site
// $objSchoolClinicalSiteUnit = new clsschoolclinicalsiteunit();
// $hospitalSite = $objSchoolClinicalSiteUnit->GetAllClinicalSiteUnit($currentSchoolId);
// unset($objSchoolClinicalSiteUnit);

//Get Hospital Site
$objHospitalSite = new clsHospitalSite();
$hospitalSite = $objHospitalSite->GetAllHospitalSite($currentSchoolId);
unset($objHospitalSite);

//For Rotation Title
$objRotation = new clsRotation();
$RotationName = $objRotation->GetrotationDetails($pevaluationrotationid, $schoolId);
$rotationtitle = $RotationName ? $RotationName['title'] : '';
unset($objRotation);

$bedCrumTitle = (isset($_GET['view']) && $_GET['view'] == '1') ? 'View' : $bedCrumTitle;

?>
<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
	<!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
	<title><?php echo ($page_title); ?></title>
	<?php include('includes/headercss.php'); ?>
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />

	<style type="text/css">
		.some-class {
			float: left;
			clear: none;
		}

		.collapsible {
			cursor: pointer;
			/* padding: 15px; */
			/* border: 1px solid #181818; */
			/* background-color: #f9f9f9; */
			display: flex;
			justify-content: space-between;
			align-items: center;
			/* border-radius: 14px; */
		}

		.collapsible p {
			margin: 0;
		}

		.collapsible-arrow {
			font-size: 18px;
			transition: transform 0.3s ease;
		}

		.content {
			display: none;
			padding: 10px 0;
			/* border-top: 1px solid #ececec; */
		}

		.content.active {
			display: block;
		}

		.active.collapsible-arrow {
			transform: rotate(180deg);
		}

		.row-delete-icon {
			position: absolute;
			top: -82px;
			right: 20px;
		}

		.panel-heading {
			width: 100%;
			display: flex;
			justify-content: space-between;
			align-items: center;
		}

		.arrow-icon {
			transition: transform 0.3s;
		}

		.collapsible.expanded .arrow-icon {
			transform: rotate(180deg);
		}
	</style>

</head>

<body>
	<?php include('includes/header.php'); ?>

	<div class="row margin_zero breadcrumb-bg">
		<div class="container">
			<div class="pull-left">
				<ol class="breadcrumb">
					<li><a href="dashboard.html">Home</a></li>
					<?php if ($currentstudentId > 0) { ?>
						<li><a href="clinical.html">Clinical</a></li>
						<li><a href="clinical.html"><?php echo ($studentfullname); ?></a></li>
						<li><a href="pevaluationlist.html?studentId=<?php echo EncodeQueryData($currentstudentId); ?>">P Evaluation</a></li>
					<?php } else { ?>
						<li><a href="rotations.html">Rotations</a></li>
						<li><a href="rotations.html"><?php echo ($rotationtitle); ?></a></li>
						<li><a href="pevaluationlist.html?pevaluationrotationid=<?php echo EncodeQueryData($pevaluationrotationid); ?>">P Evaluation</a></li>
					<?php } ?>
					<li class="active"><?php echo ($bedCrumTitle); ?></li>
				</ol>
			</div>

		</div>
	</div>

	<div class="container">

		<form id="frmcievaluation" data-parsley-validate class="form-horizontal" method="POST" <?php if ($currentstudentId > 0) { ?> action="addpevaluationsubmit.html?pEvaluationMasterId=<?php echo (EncodeQueryData($pEvaluationMasterId)); ?>&studentId=<?php echo (EncodeQueryData($currentstudentId)); ?>" <?php } else { ?>action="addpevaluationsubmit.html?pEvaluationMasterId=<?php echo (EncodeQueryData($pEvaluationMasterId)); ?>
																									&pevaluationrotationid=<?php echo (EncodeQueryData($pevaluationrotationid)); ?>" <?php } ?>>

			<div class="row">
				<!-- <div class="col-md-6">
				
				<div class="form-group">
                        <label class="col-md-4 control-label" for="cboclinician">Clinician</label>
                        <div class="col-md-8">
                            <select id="cboclinician" name="cboclinician"  
									class="form-control input-md required-input select2_single"   required>
                            <option value="" selected>Select</option>
								<?php
								if ($Clinician != "") {
									while ($row = mysqli_fetch_assoc($Clinician)) {
										$selClinicianId  = $row['clinicianId'];
										$firstName  = stripslashes($row['firstName']);
										$lastName  = stripslashes($row['lastName']);
										$name = $firstName . ' ' . $lastName;
								?>
										<option value="<?php echo ($selClinicianId); ?>" <?php if ($clinicianId == $selClinicianId) { ?>  selected="true" <?php } ?>><?php echo ($name); ?></option>
                                        
										<?php

									}
								}
										?>
                            </select>
							<input type="hidden" name="rotation" value="<?php echo ($pevaluationrotationid); ?>">
                        </div>
                    </div>
					
				</div> -->


				<div class="col-md-6">

					<div class="form-group">
						<label class="col-md-12 control-label" for="cbostudent">Student</label>
						<div class="col-md-12">
							<select id="cbostudent" name="cbostudent" class="form-control input-md required-input select2_single" disabled>
								<option value="" selected>Select</option>
								<?php
								if ($Student != "") {
									while ($row = mysqli_fetch_assoc($Student)) {
										$selstudentId  = $row['studentId'];
										$firstName  = stripslashes($row['firstName']);
										$lastName  = stripslashes($row['lastName']);
										$name =	$firstName . ' ' . $lastName;
								?>
										<option value="<?php echo ($selstudentId); ?>" <?php if ($studentId == $selstudentId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>

								<?php

									}
								}
								?>
							</select>
						</div>
					</div>
					<!--- ROTATION DD END---->

				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-12 control-label" for="preceptorName">Preceptor Name</label>
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class='input-group date w-full' id='preceptorName' style="position: relative;">
								<input type='text' name="preceptorName" id="preceptorName" class="form-control input-md" value="<?php echo ($preceptorName); ?>" readonly data-parsley-errors-container="#error-preceptorName" />
							</div>
							<div id="error-preceptorName"></div>
						</div>
					</div>

				</div>
			</div>



			<div class="row">
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-12 control-label" for="evaluationDate">Evaluation Date</label>
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class='input-group date w-full relative' id='evaluationDate'>

								<input type='text' name="evaluationDate" id="evaluationDate" class="form-control input-md required-input rotation_date" value="<?php echo ($evaluationDate); ?>" required data-parsley-errors-container="#error-txtDate" placeholder="MM-DD-YYYY" />
								<span class="input-group-addon calender-icon">
									<span class="glyphicon glyphicon-calendar"></span>
								</span>
							</div>
							<div id="error-txtDate"></div>
						</div>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-12 control-label" for="cbohospitalsites">Hospital Sites</label>
						<div class="col-md-12 flex-col-reverse">
							<select id="cbohospitalsites" name="cbohospitalsites" class="form-control input-md required-input select2_single" required="true">
								<option value="" selected>Select</option>
								<?php
								if ($hospitalSite != "") {
									while ($row = mysqli_fetch_assoc($hospitalSite)) {
										$hospitalSiteId  = $row['hospitalSiteId'];
										$name  = stripslashes($row['title']);

								?>
										<option value="<?php echo ($hospitalSiteId); ?>" <?php if ($schoolClinicalSiteUnitId == $hospitalSiteId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
								<?php

									}
								}
								?>

							</select>
						</div>
						<p id="hospitalerror" style="color:#E74C3C; border: 1px solid #E8544;"></p>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-md-12">
					<div class="form-group">
						<label class="col-md-12 control-label" for="instructions:"></label>
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class="panel panel-default border-14" style="margin-bottom: 0 !important;">
								<div class="panel-body">
									<p>The preceptor's ratings are based on the following categories. The rating scale for each category is<br>
										<b>1-Strongly Disagree, 2-Disagree, 3-Neutral/Acceptable, 4-Agree, 5-Strongly Agree.</b>
									</p>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<!---- 1st SECTION div start -------->
			<div class="row">
				<div class="col-md-12">
					<div class="form-group">
						<label class="col-md-12 control-label" for="instructions:"></label>
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class="panel-group" id="posts">
								<div class="panel panel-default">
									<a class="collapsible" style="color: #000; text-decoration: none;" href="#PevaluationSection" data-toggle="collapse" data-parent="#posts" aria-expanded="false" id="collapse-link">
										<div class="panel-heading">
											<h4 class="panel-title">
												<b>Student Preceptor Evaluation</b>
											</h4>
											<span class="arrow-icon"> <i class="fa fa-angle-down" aria-hidden="true" id="collapse-icon" style="font-size: 19px;"></i></span>
										</div>
									</a>
									<div id="PevaluationSection" class="panel-collapse collapse">
										<?php
										if ($PevaluationSection) {
											while ($row = mysqli_fetch_array($PevaluationSection)) {
												$sectionMasterId = $row['sectionMasterId'];
												$title = $row['title'];
										?>
												<div class="panel-body"><?php echo '<b>' . $title . '</b>'; ?></div>
												<?php   // for question
												$totalCIevaluation = 0;
												$Pevaluationquestion = $objPevaluation->GetAllEvaluationQuestionMaster($schoolId, $sectionMasterId);

												if ($Pevaluationquestion != '') {
													$totalCIevaluation = mysqli_num_rows($Pevaluationquestion);
												}

												if ($Pevaluationquestion) {
													while ($row = mysqli_fetch_array($Pevaluationquestion)) {
														if (isset($_GET['pEvaluationMasterId'])) {
															$pEvaluationMasterId = DecodeQueryData($_GET['pEvaluationMasterId']);
														} else {
															$pEvaluationMasterId = 0;
														}

														$schoolPEvaluationQuestionId = $row['schoolPEvaluationQuestionId'];
														$questionText = $row['questionText'];
														$schoolPEvaluationQuestionType = $row['schoolPEvaluationQuestionType'];
														$qhtml = GetPEvaluationQuestionHtml($schoolPEvaluationQuestionId, $schoolPEvaluationQuestionType, $pEvaluationMasterId, $currentSchoolId);

														//Get Question Comment
														$questionComment = '';
														if ($pEvaluationMasterId > 0)
															$questionComment = $objDB->GetSingleColumnValueFromTable('pevaluationdetail', 'comment', 'pEvaluationMasterId', $pEvaluationMasterId, 'schoolPEvaluationQuestionId', $schoolPEvaluationQuestionId);

												?>
														<div class="panel-body isAllRadioButton">
															<b><?php echo ($questionText); ?></b> <br /><br />
															<?php echo $qhtml; ?>
															<?php if ($schoolPEvaluationQuestionType == 2) { ?>
																<textarea name="textarea_<?php echo $schoolPEvaluationQuestionId; ?>" id="textarea_<?php echo $schoolPEvaluationQuestionId; ?>" rows="4" class="form-control"><?php echo $questionComment; ?></textarea>
															<?php } ?>
														</div>
										<?php
													}
												}
											}
										}
										unset($objPevaluation);
										?>
									</div>

								</div>
							</div>




						</div>
					</div>
				</div>
			</div>

			<div class="row">
				<div class="form-group mx-0">
					<!-- <label class="col-md-2 control-label"></label> -->
					<div class="col-md-12" style="display: flex; justify-content: center; margin: 20px 0;gap: 15px;">
						<?php
						$rotationStatus = checkRotationStatus($pevaluationrotationid);
						if ($rotationStatus == 0) {
						?>
							<button id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
						<?php } ?>

						<?php if ($currentstudentId > 0) { ?>
							<a type="button" href="pevaluationlist.html?studentId=<?php echo EncodeQueryData($currentstudentId); ?>" class="btn btn-default">Cancel</a>
						<?php } else { ?>
							<a type="button" href="pevaluationlist.html?pevaluationrotationid=<?php echo EncodeQueryData($pevaluationrotationid); ?>" class="btn btn-default">Cancel</a>
						<?php } ?>
					</div>
				</div>
			</div>

		</form>


	</div>

	<?php include('includes/footer.php'); ?>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>

	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/jquery.cascadingdropdown.js"></script>




	<script type="text/javascript">
		$(window).load(function() {
			$(".isAllRadioButton").trigger('click');

			$('#frmcievaluation').parsley().on('field:validated', function() {
					var ok = $('.parsley-error').length === 0;
				})
				.on('form:submit', function() {
					ShowProgressAnimation();
					return true; // Don't submit form for this demo
				});

			$('#evaluationDate').datetimepicker({
				format: 'MM/DD/YYYY'
			});
			$('#studentsignitureDate').datetimepicker({
				format: 'MM/DD/YYYY',
				maxDate: new Date()
			});

			//For Searching Dropdown
			$(".select2_single").select2();
			$('#select2-cbostudent-container').addClass('required-select2');
			$('#select2-cboclinician-container').addClass('required-select2');
			$('#select2-cbohospitalsites-container').addClass('required-select2');


		});

		$(document).ready(function() {
			$(".isAllRadioButton").click(function() {
				$(".isAllRadioButton input[type=radio]:checked").each(function() {
					var checkboxName = $.trim($(this).attr('name'));
					var checkboxValue = $.trim($(this).parent().text());
					const questionArray = checkboxName.split("_");
					var questionId = questionArray[1];
					questionId = questionId.replace('[]', '');

					//Hide textarea if options are not numeric
					if ($.isNumeric(checkboxValue) == false)
						$('#textarea_' + questionId).addClass('hide');

					//Add Required to textarea
					$('#textarea_' + questionId).prop('required', false);
					$('#textarea_' + questionId).removeClass('required-input');
					if (checkboxValue <= 3) {
						$('#textarea_' + questionId).addClass('required-input');
						$('#textarea_' + questionId).prop('required', true);
					}

				});
			});
		});
	</script>
	<script>
		// Get all collapsible button elements
		var buttons = document.querySelectorAll(".collapsible");
		var contents = document.querySelectorAll(".panel-collapse");

		// Add click event listeners to all buttons
		buttons.forEach(function(button, index) {
			button.addEventListener("click", function() {
				// Check if the content is currently expanded
				var isExpanded = contents[index].style.display === "block";

				// Close all sections
				contents.forEach(function(content) {
					content.style.display = "none";
				});

				// Reset the "expanded" class for all buttons
				buttons.forEach(function(btn) {
					btn.classList.remove("expanded");
				});

				// Toggle the content for the clicked section
				if (!isExpanded) {
					contents[index].style.display = "block";
					button.classList.add("expanded");
				}
			});
		});
	</script>
</body>

</html>