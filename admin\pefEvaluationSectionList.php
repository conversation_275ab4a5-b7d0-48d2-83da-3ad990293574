<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');
include('../class/clsPEF.php');
include('../setRequest.php');

$isPEFType = '';
$loggedUserId = isset($_SESSION["loggedUserId"]) ? $_SESSION["loggedUserId"] : 0;
$loggedAsBackUserId = isset($_SESSION["loggedAsBackUserId"]) ? $_SESSION["loggedAsBackUserId"] : 0;

$directionType = isset($_GET['type']) ? $_GET['type'] : '';
if ($directionType == 'canvas')
    $canvasStatus = 1;

$activebtn = '';
$clsBtnActiveFloor = '';
$clsBtnActiveAdult = '';
$getPEFdetails = '';
$clsBtnActive = '';

$activebtn = isset($_GET['active']) ? $_GET['active'] : 'pef1';

if ($activebtn == 'pef1')
    $clsBtnActiveFloor = "active";
elseif ($activebtn == 'pef2')
    $clsBtnActiveAdult = "active";
else
    $clsBtnActive = "active";

//For PEF Section
$objPef = new clsPEF();

// For superadmin
if ($activebtn == 'pef1')
    $getPEFdetails = $objPef->GetSectionForSettings('pef1');
elseif ($activebtn == 'pef2')
    $getPEFdetails = $objPef->GetSectionForSettings('pef2');

$totalSection = 0;
if ($getPEFdetails != '') {
    $totalSection = mysqli_num_rows($getPEFdetails);
}

?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title>PEF Evaluation Section</title>
    <?php include('includes/headercss.php'); ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <?php include("includes/datatablecss.php") ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">
</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="settings.html">Settings</a></li>
                    <li class="active">PEF Evaluation Section</li>
                </ol>
            </div>
            <div class="pull-right">
                <ol class="breadcrumb">
                </ol>
            </div>
            <?php
            ?>
        </div>
    </div>

    <div class="container">
        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button>PEF Evaluation Section added successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button>PEF Evaluation Section updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
            <?php
            } else if ($_GET["status"] == "StatusUpdated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Status updated successfully.
                </div>
        <?php
            }
        }
        ?>

        <?php if ($directionType != 'canvas') { ?>
            <div class="row margin_bottom_ten">
                <div class="btn-group pull-right" role="group" aria-label="First group">
                    <a role="button" class="group-button-one btn btn-primary <?php echo $clsBtnActiveFloor; ?>" href="pefEvaluationSectionList.html?active=pef1"> PEF I </a>
                    <a role="button" class="group-button-two btn btn-primary  <?php echo $clsBtnActiveAdult; ?>" href="pefEvaluationSectionList.html?active=pef2">PEF II</a>
                </div>
            </div>
        <?php } ?>

        <div class="row margin_bottom_ten ">

            <div class="row margin_bottom_ten">
                <?php
                if ($activebtn == 'pef1') {
                    if ($loggedAsBackUserId > 0)
                        $hrefLink = 'addPEFEvaluationSection.html?isPEFType=' . EncodeQueryData(0);
                    else
                        $hrefLink = 'addPEFEvaluationSection.html?&isPEFType=' . EncodeQueryData(0);
                ?>
                    <?php if ($isCurrentSchoolSuperAdmin == 1) { ?>
                        <a class="showall btn btn-success pull-right margin_right_fifteen" href="<?php echo $hrefLink; ?>"> Add </a>
                    <?php } ?>
                <?php
                } elseif ($activebtn == 'pef2') {
                    if ($loggedAsBackUserId > 0)
                        $hrefLink = 'addPEFEvaluationSection.html?isPEFType=' . EncodeQueryData(1);
                    else
                        $hrefLink = 'addPEFEvaluationSection.html?&isPEFType=' . EncodeQueryData(1);
                ?>
                    <?php if ($isCurrentSchoolSuperAdmin == 1) { ?>
                        <a class="showall btn btn-success pull-right margin_right_fifteen" href="<?php echo $hrefLink; ?>"> Add </a>
                    <?php } ?>
                <?php } ?>
            </div>

            <div id="divTopLoading">Loading...</div>
            <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                <thead>
                    <tr>
                        <th style="text-align: center">Sort Order</th>
                        <th>Section Title</th>
                        <th class="text-center">PEF Type</th>
                        <th style="text-align: center">Action</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    if ($totalSection > 0) {
                        while ($row = mysqli_fetch_array($getPEFdetails)) {

                            $isPEFType = $row['isType'];
                            if ($isPEFType == 1)
                                $isPEF = 'PEF II';
                            else
                                $isPEF = 'PEF I';

                            $sectionMasterId = $row['sectionMasterId'];
                            $title = $row['title'];
                            $sortOrder = $row['sortOrder'];
                            $questionCount = $objPef->GetPefQuestionCountbySections($sectionMasterId);
                            $isActive = isset($row['isActive']) ? $row['isActive'] : 0;
                            $displayStatus = "";
                            $updateStatus = "0";
                            $buttoncss = "btn-primary";

                            if ($isActive == "1") {
                                $displayStatus = "Active";
                                $updateStatus = "0";
                                $buttoncss = "text-primary";
                            } else {
                                $displayStatus = "Inactive";
                                $updateStatus = "1";
                                $buttoncss = "text-warning";
                            }
                    ?>
                            <tr>
                                <td style="text-align: center"><?php echo ($sortOrder); ?></td>
                                <td><?php echo ($title); ?></td>
                                <td class="text-center"><?php echo ($isPEF); ?></td>
                                <td style="text-align: center">
                                    <?php if ($isCurrentSchoolSuperAdmin == 1) { ?>
                                        <a class="<?php echo ($buttoncss); ?>" href="updateallevaluationsectionstatus.html?isEvalType=pef&id=<?php echo (EncodeQueryData($sectionMasterId)); ?>&newStatus=<?php echo ($updateStatus); ?>&userId=<?php echo $_SESSION['loggedUserId']; ?>&isUser=<?php echo "1"; ?>&isPEFType=<?php echo $isPEFType; ?>&type=status">
                                            <?php echo ($displayStatus); ?>
                                        </a> |
                                        <a href="pefEvaluationQuestionList.html?sectionMasterId=<?php echo EncodeQueryData($sectionMasterId); ?>&isPEFType=<?php echo (EncodeQueryData($isPEFType)); ?>">Steps</a>
                                        <span class="badge"><?php echo ($questionCount); ?></span> |
                                        <a href="addPEFEvaluationSection.html?editid=<?php echo EncodeQueryData($sectionMasterId); ?>&isPEFType=<?php echo (EncodeQueryData($isPEFType)); ?>">Edit</a>
                                        | <a href="javascript:void(0);" class="deleteAjaxRow"
                                            sectionMasterId="<?php echo (EncodeQueryData($sectionMasterId)); ?>" title="<?php echo ($title); ?>">Delete</a>
                                    <?php } else { ?>
                                        <a href="pefEvaluationQuestionList.html?sectionMasterId=<?php echo EncodeQueryData($sectionMasterId); ?>&isPEFType=<?php echo EncodeQueryData($isPEFType); ?>">Steps</a>
                                        <span class="badge"><?php echo ($questionCount); ?></span> |
                                        <a href="addPEFEvaluationSection.html?editid=<?php echo EncodeQueryData($sectionMasterId); ?>&sectionMasterId=<?php echo EncodeQueryData($sectionMasterId); ?>&isPEFType=<?php echo (EncodeQueryData($isPEFType)); ?>active=<?php echo ($isPEFType == 1 ? 'pef2' : 'pef1'); ?>&view=1">View</a>
                                    <?php } ?>
                                </td>

                            </tr>
                    <?php
                        }
                    }
                    unset($objCIevaluation);
                    ?>
                </tbody>
            </table>
        </div>

        <?php include('includes/footer.php'); ?>
        <?php include("includes/datatablejs.php") ?>
        <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
        <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
        <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>

        <script type="text/javascript">
            alertify.defaults.transition = "slide";
            alertify.defaults.theme.ok = "btn btn-success";
            alertify.defaults.theme.cancel = "btn btn-danger";
            alertify.defaults.theme.input = "form-control";     

            $(window).load(function() {
                $("#divTopLoading").addClass('hide');
                $(".select2_single").select2();
            });

            var current_datatable = $("#datatable-responsive").DataTable({

            });

            //Delete Section
            $(document).on('click', '.deleteAjaxRow', function() {

                var current_datatable_row = current_datatable.row($(this).parents('tr'));
                var sectionMasterId = $(this).attr('sectionMasterId');
                var title = $(this).attr('title');
                var userId = '<?php echo EncodeQueryData($_SESSION['loggedUserId']); ?>';

                alertify.confirm('PEF Evaluation Section: ' + title, 'Continue with delete?', function() {
                    $.ajax({
                        type: "GET",
                        url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                        data: {
                            id: sectionMasterId,
                            userId: userId,
                            type: 'PefEvaluationSection'
                        },
                        success: function() {
                            current_datatable.row(current_datatable_row).remove().draw(false);
                            alertify.success('Deleted');
                        }
                    });
                }, function() {});

            });
        </script>
</body>

</html>