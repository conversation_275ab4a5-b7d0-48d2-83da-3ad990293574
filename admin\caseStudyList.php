<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsStudent.php');
include('../class/clsCaseStudy.php');
include('../class/clsRotation.php');
include('../class/clsCourses.php');

$rotationId = 0;
$schoolId = 0;
$currentstudentId = 0;
$schoolId = $currentSchoolId;
$transchooldisplayName = '';
$rotationId = 0;
$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];
$loggedUserId = isset($_SESSION["loggedUserId"]) ? $_SESSION["loggedUserId"] : 0;
$loggedUserSendRecordToCanvas = isset($_SESSION["loggedUserSendRecordToCanvas"]) ? $_SESSION["loggedUserSendRecordToCanvas"] : 0;
$canvasStatus = isset($_GET['canvasStatus']) ? $_GET['canvasStatus'] : '';

//For Check Checkoff 
$isActiveCheckoff = '';
$isActiveCheckoff = $_SESSION["isActiveCheckoff"];

$activebtn = '';
$clsBtnActiveFloor = '';
$clsBtnActiveAdult = '';
$clsBtnActive = '';
if (isset($_GET['active']))
    $activebtn = $_GET['active'];

if ($activebtn == 'floor')
    $clsBtnActiveFloor = "active";
elseif ($activebtn == 'adult')
    $clsBtnActiveAdult = "active";
elseif ($activebtn == 'PACR')
    $clsBtnActivePACR = "active";
else
    $clsBtnActive = "active";


$courseId = 0;
//For Rotation 
$encodedRotationId = '';
$encodedStudentId = '';
if (isset($_GET['rotationId'])) {
    $encodedRotationId = $_GET['rotationId'];
    $rotationId = $_GET['rotationId'];
    $rotationId = DecodeQueryData($rotationId);
} elseif (isset($_GET['studentId'])) {
    $encodedStudentId = $_GET['studentId'];
    $currentstudentId = $_GET['studentId'];
    $currentstudentId = DecodeQueryData($currentstudentId);
} else {
    $schoolId = $currentSchoolId;
    $transchooldisplayName = $currenschoolDisplayname;
}

$directionType = isset($_GET['type']) ? $_GET['type'] : '';
if ($directionType == 'canvas')
    $canvasStatus = 1;

$title = "Case Study |" . $transchooldisplayName;

//For Case Study List
$objCaseStudy = new clsCaseStudy();

$objDB = new clsDB();
$casestudySettings = '';
$casestudySettings = $objDB->GetSingleColumnValueFromTable('schools', 'casestudySettings', 'schoolId', $schoolId);
unset($objDB);

if ($activebtn == 'floor')
    $getCaseStudydetails = $objCaseStudy->GetAllFloorCaseStudy($schoolId, $rotationId, $currentstudentId, $canvasStatus);
elseif ($activebtn == 'adult')
    $getCaseStudydetails = $objCaseStudy->GetAllAdultCaseStudy($schoolId, $rotationId, $currentstudentId, $canvasStatus);
elseif ($activebtn == 'PACR')
    $getCaseStudydetails = $objCaseStudy->GetAllPACRCaseStudy($schoolId, $rotationId, $currentstudentId);
else
    $getCaseStudydetails = $objCaseStudy->GetAllCaseStudy($schoolId, $rotationId, $currentstudentId, $canvasStatus, $casestudySettings);

$totalCaseStudy = 0;
if ($getCaseStudydetails != '') {
    $totalCaseStudy = mysqli_num_rows($getCaseStudydetails);
}
unset($objCaseStudy);

//For Student Name
$objStudent = new clsStudent();
$Rowstudent = $objStudent->GetSingleStudent($schoolId, $currentstudentId);
$studentfullname = $Rowstudent ? $Rowstudent['firstName'] . ' ' . $Rowstudent['lastName'] : '';
unset($objStudent);

//For Rotation Name
$objRotation = new clsRotation();
$RotationName = $objRotation->GetrotationDetails($rotationId, $schoolId);
$rotationtitle = $RotationName ? $RotationName['title'] : '';
unset($objRotation);


?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($title); ?></title>
    <?php include('includes/headercss.php'); ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <?php include("includes/datatablecss.php") ?>

    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <style>
        .table>thead>tr>th {
            vertical-align: middle !important;
            border-bottom: 2px solid #ddd;
            background-color: rgb(45, 105, 182);
            color: #FFF;
            text-align: center;
        }
    </style>
</head>

<body>
    <?php include('includes/header.php'); ?>
    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <?php if ($directionType == 'canvas') { ?>
                        <li><a href="dashboard.html">Home</a></li>
                        <li><a href="settings.html">Settings</a></li>
                        <li class="active">Case Study</li>
                    <?php } else { ?>
                        <li><a href="dashboard.html">Home</a></li>
                        <?php if ($rotationId != '') { ?>
                            <li><a href="rotations.html">Rotations</a></li>
                            <li class="active"><a href="rotations.html"><?php echo ($rotationtitle); ?></a></li>
                        <?php } else if ($currentstudentId > 0 && $isActiveCheckoff != 2) { ?>
                            <li><a href="clinical.html">clinical</a></li>
                            <li class="active"><a href="clinical.html"><?php echo ($studentfullname); ?></a></li>
                        <?php } ?>
                        <li class="active">Case Study</li>
                    <?php } ?>
                </ol>
            </div>
        </div>
    </div>

    <div class="custom-container">

        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "Added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Case Study added successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Case Study updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Deleted") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Case Study deleted successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
        <?php
            }
        }
        ?>

        <?php if ($directionType != 'canvas') { ?>
            <div class="row margin_bottom_ten">
                <div class="col-md-12">
                    <div class=" btn-group pull-right" role="group" aria-label="First group">
                        <a role="button" class="btn btn-primary <?php echo $clsBtnActive; ?>" href="caseStudyList.html?studentId=<?php echo EncodeQueryData($currentstudentId); ?>">All</a>
                        <?php
                        $casestudySettings = explode(",", $casestudySettings);
                        if (in_array("1", $casestudySettings)) {
                        ?>
                            <a role="button" class="btn btn-primary <?php echo $clsBtnActivePACR; ?>" href="caseStudyList.html?active=PACR&studentId=<?php echo EncodeQueryData($currentstudentId); ?>"> Patient Assessment/Chart Review </a>
                        <?php }
                        if (in_array("2", $casestudySettings)) {
                        ?>
                            <a role="button" class="btn btn-primary <?php echo $clsBtnActiveFloor; ?>" href="caseStudyList.html?active=floor&studentId=<?php echo EncodeQueryData($currentstudentId); ?>"> Floor Therapy</a>
                        <?php }
                        if (in_array("3", $casestudySettings)) {
                        ?>
                            <a role="button" class="btn btn-primary  <?php echo $clsBtnActiveAdult; ?>" href="caseStudyList.html?active=adult&studentId=<?php echo EncodeQueryData($currentstudentId); ?>">Adult ICU & NICU/PICU</a>
                        <?php } ?>
                    </div>
                </div>
            </div>
        <?php } ?>

        <div id="divTopLoading">Loading...</div>
        <!-- <div class="row margin_bottom_ten">
                    <div class="col-md-8"></div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="col-md-6 control-label text-right" for="" style="margin-top:8px">Canvas Status:</label>
                            <div class="col-md-6 padding_right_zero padding_left_zero">
                                <select id="canvasStatus" name="canvasStatus" class="form-control input-md required-input select2_single" studentId="<?php echo EncodeQueryData($currentstudentId); ?>" >
                                    <option value="" selected>All</option>
                                    <option value="1" <?php if ($canvasStatus == 1) { ?> selected="true" <?php } ?>>Sent</option>
                                    <option value="0" <?php if ($canvasStatus == 0 && $canvasStatus != '') { ?> selected="true" <?php } ?>>Not Sent</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div> -->
        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr>
                    <th>Date</th>
                    <th>First Name</th>
                    <th>Last Name</th>
                    <th>Rotation</th>
                    <th>Case<br>Study</th>
                    <th>Chief Complaint/<br>Admitting Diagnosis</th>
                    <th style="text-align: center">Clinician<br>Signoff Date</th>
                    <th style="text-align: center">School<br>Signoff Date</th>
                    <th>Comments</th>
                    <th>Status</th>
                    <?php if ($directionType != 'canvas') { ?>
                        <th style="text-align: center">Action</th>
                    <?php } ?>
                    <?php if ($isActiveCanvas && $directionType != 'canvas') { ?>
                        <th class="text-center">Canvas Status</th>
                    <?php } ?>
                </tr>
            </thead>
            <tbody>
                <?php
                if ($totalCaseStudy > 0) {
                    while ($row = mysqli_fetch_array($getCaseStudydetails)) {
                        $type = $row['type'];
                        if ($type == 'floor')
                            $caseStudyName = 'Floor Therapy';
                        else if ($type == 'PACR')
                            $caseStudyName = 'PACR';
                        else
                            $caseStudyName = 'Adult ICU & NICU/PICU';


                        $DBStudentId = $row['studentId'];
                        $studentfirstName = $row['studentfirstName'];
                        $studentlastName = $row['studentlastName'];
                        $studentfullName = $studentfirstName . ' ' . $studentlastName;
                        $caseStudyId = $row['caseStudyId'];
                        $caseStudydate = $row['caseStudydate'];
                        $caseStudydate = converFromServerTimeZone($caseStudydate, $TimeZone);
                        $caseStudydate = date("m/d/Y", strtotime($caseStudydate));
                        $rotationname = $row['rotationname'];
                        $ChiefComplaint = $row['ChiefComplaint'];
                        $clinicianDate = $row['ClinicianDate'];
                        $rotationId = $row['rotationId'];
                        $rsClinicianReviewsCount = 0;
                        if ($type == 'PACR') {
                            //For Clinician Reviews
                            $objCaseStudy = new clsCaseStudy();
                            $rsClinicianReviews = $objCaseStudy->GetClinicianReviews($caseStudyId);
                            $rsClinicianReviewsCount = ($rsClinicianReviews != '') ? mysqli_num_rows($rsClinicianReviews) : 0;
                            unset($objCaseStudy);
                        }
                        if ($clinicianDate != '') {
                            $clinicianDate = converFromServerTimeZone($clinicianDate, $TimeZone);
                            $clinicianDate = date("m/d/Y", strtotime($clinicianDate));
                        }
                        $clinicianApprove = $clinicianDate;
                        if ($clinicianDate) {
                            $clinicianDate = $clinicianDate;
                        } else if ($clinicianDate == '' && $rsClinicianReviewsCount) {
                            $clinicianDate = '<p style="color: red;">Under Review</p>';
                            $isActionBtnName = 'Edit';
                            $clinicianApprove = 'Under Review';
                        } else {
                            $clinicianDate = '-';
                        }
                        $schoolDate = $row['schoolDate'];
                        if ($schoolDate != '') {
                            $schoolDate = converFromServerTimeZone($schoolDate, $TimeZone);
                            $schoolDate = date("m/d/Y", strtotime($schoolDate));
                        }
                        if ($schoolDate) {
                            $schoolDate = $schoolDate;
                        } else {
                            $schoolDate = '-';
                        }

                        $shortTitlelen = strlen($ChiefComplaint);

                        // if($shortTitlelen > 40)
                        // {

                        //     $caseChiefComplaint=substr($ChiefComplaint,0,20);
                        //     $caseChiefComplaint .= '...';

                        // }else{
                        //     $caseChiefComplaint=$ChiefComplaint;
                        // }

                        $caseChiefComplaint = wordwrap($ChiefComplaint, 40, "<br>\n", true);

                        // For Canvas
                        $isSendToCanvas  = isset($row['isSendToCanvas']) ? $row['isSendToCanvas'] : 0;

                        $isSendToCanvasClass = 'isSendRecordToCanvas';
                        $isSentToCanvasClass = 'hide';
                        if ($isSendToCanvas) {
                            $isSendToCanvasClass = 'hide';
                            $isSentToCanvasClass = '';
                        }

                        $isUserCanSendCompletedRecordToCanvas = 0;
                        if ($clinicianDate != '-' || $schoolDate != '-')
                            $isUserCanSendCompletedRecordToCanvas = 1;

                        $school_comments = $row['school_comments'];
                        $clinician_comments = $row['clinician_comments'];
                        $school_comments = ($school_comments != '') ? 'Yes' : 'No';
                        $clinician_comments = ($clinician_comments != '') ? 'Yes' : 'No';
                        // -- End Canvas --//

                        // Case Study Status
                        $caseStudyStatus = $row['status'];
                        if ($caseStudyStatus == '0') {
                            $caseStudyStatus = 'Pending';
                        } else if ($caseStudyStatus == '1') {
                            $caseStudyStatus = 'Completed';
                        }

                        $caseStudyStatusColor = '';
                        $caseStudyStatusColor = ($caseStudyStatus == 'Pending') ? 'red' : 'green';

                ?>
                        <tr>
                            <td><?php echo ($caseStudydate); ?></td>
                            <td><?php echo ($studentfirstName); ?></td>
                            <td><?php echo ($studentlastName); ?></td>
                            <td><?php echo ($rotationname); ?></td>
                            <td title="<?php echo ($ChiefComplaint); ?>"><?php echo $caseStudyName; ?></td>
                            <td><?php echo ($caseChiefComplaint); ?></td>
                            <td class="text-center"><?php echo ($clinicianDate); ?></td>
                            <td class="text-center"><?php echo ($schoolDate); ?></td>
                            <td><?php
                                echo 'Clinician: ' . $clinician_comments . '</br>School: ' . $school_comments;
                                ?></td>
                            <td class="text-center" style="color: <?php echo $caseStudyStatusColor; ?>"><?php echo $caseStudyStatus; ?></td>
                            <?php if ($directionType != 'canvas') { ?>
                                <td>
                                    <?php
                                    $rotationStatus = checkRotationStatus($rotationId);

                                    // Set default values
                                    $viewParam = $rotationStatus ? '&view=V' : '';
                                    $linkText = $rotationStatus ? 'View' : 'Edit';

                                    // Override if caseStudyStatus is Pending
                                    if ($caseStudyStatus === 'Pending') {
                                        $viewParam = '&view=V';
                                        $linkText = 'View';
                                    }

                                    // PACR-specific override
                                    if ($type === 'PACR' && $schoolDate !== '-') {
                                        $viewParam = '&view=V';
                                        $linkText = 'View';
                                    }

                                    $encodedId = EncodeQueryData($caseStudyId);
                                    ?>

                                    <?php if ($type == 'floor') { ?>
                                        <a href="addCaseStudy.html?caseStudyId=<?php echo $encodedId; ?><?php echo $viewParam; ?>"><?php echo $linkText; ?></a>
                                        | <a href="javascript:void(0);" class="deleteAjaxRowFloor" caseStudyId="<?php echo $encodedId; ?>" rotationStatus="<?php echo $rotationStatus; ?>">Delete</a>

                                    <?php } else if ($type == 'PACR') { ?>
                                        <a href="addCaseStudyPACR.html?caseStudyId=<?php echo $encodedId; ?><?php echo $viewParam; ?>"><?php echo $linkText; ?></a>
                                        | <a href="javascript:void(0);" class="deleteAjaxRowPACR" caseStudyId="<?php echo $encodedId; ?>" rotationStatus="<?php echo $rotationStatus; ?>">Delete</a>

                                    <?php } else { ?>
                                        <a href="addCaseStudyAdult.html?caseStudyId=<?php echo $encodedId; ?><?php echo $viewParam; ?>"><?php echo $linkText; ?></a>
                                        | <a href="javascript:void(0);" class="deleteAjaxRowAdult" caseStudyId="<?php echo $encodedId; ?>" rotationStatus="<?php echo $rotationStatus; ?>">Delete</a>
                                    <?php } ?>
                                </td>


                            <?php } ?>
                            <?php if ($isActiveCanvas && $directionType != 'canvas') {
                                if ($loggedUserSendRecordToCanvas && $isUserCanSendCompletedRecordToCanvas) { ?>
                                    <td class="text-center">
                                        <a href="javascript:void(0);" id="isSendToCanvas_<?php echo $caseStudyId; ?>" class="<?php echo $isSendToCanvasClass; ?>" caseStudydate="<?php echo $caseStudydate; ?>" rotation="<?php echo $rotationname; ?>" caseStudyName="<?php echo $caseStudyName; ?>" caseChiefComplaint="<?php echo $caseChiefComplaint; ?>" clinicianApprove="<?php echo $clinicianApprove; ?>" schoolApprove="<?php echo $schoolDate; ?>" caseStudyId="<?php echo $caseStudyId; ?>" studentId="<?php echo $DBStudentId; ?>" studentFullName="<?php echo $studentfullName; ?>" caseStudyType="<?php echo $type; ?>">
                                            Send to Canvas
                                        </a>
                                        <label for="" class="isSentToCanvas_<?php echo $caseStudyId; ?> <?php echo $isSentToCanvasClass; ?>">Sent</label>

                                    </td>

                                <?php } else { ?>
                                    <td class="text-center"><label for="" class=""> -
                                            <?php //if($isSendToCanvas>0) { echo 'Sent'; } else { echo 'Send to canvas'; } 
                                            ?>
                                        </label></td>

                            <?php }
                            }
                            ?>
                        </tr>
                <?php

                    }
                }
                ?>
            </tbody>
        </table>
    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";


        $(window).load(function() {
            $("#divTopLoading").addClass('hide');
        });

        $(document).on('click', '.deleteAjaxRowFloor', function() {

            // var rotationStatus = $(this).attr('rotationStatus');
            // if (rotationStatus == 1) {
            //     alertify.error('Rotation is expired');
            //     return false;
            // }
            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var caseStudyId = $(this).attr('caseStudyId');
            var userId = '<?php echo EncodeQueryData($loggedUserId); ?>';
            var isUser = 1; //for Admin

            alertify.confirm('Case Study: ', 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: caseStudyId,
                        userId: userId,
                        isUser: isUser,
                        type: 'caseStusyFloor'
                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        alertify.success('Deleted');
                    }
                });
            }, function() {});

        });

        $(document).on('click', '.deleteAjaxRowPACR', function() {

            // var rotationStatus = $(this).attr('rotationStatus');
            // if (rotationStatus == 1) {
            //     alertify.error('Rotation is expired');
            //     return false;
            // }

            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var caseStudyId = $(this).attr('caseStudyId');
            var userId = '<?php echo EncodeQueryData($loggedUserId); ?>';
            var isUser = 1; //for Admin


            alertify.confirm('Case Study: ', 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: caseStudyId,
                        userId: userId,
                        isUser: isUser,
                        type: 'caseStusyPACR'
                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        alertify.success('Deleted');
                    }
                });
            }, function() {});

        });

        $(document).on('click', '.deleteAjaxRowAdult', function() {
            // var rotationStatus = $(this).attr('rotationStatus');
            // if (rotationStatus == 1) {
            //     alertify.error('Rotation is expired');
            //     return false;
            // }
            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var caseStudyId = $(this).attr('caseStudyId');
            var userId = '<?php echo EncodeQueryData($loggedUserId); ?>';
            var isUser = 1; //for Admin


            alertify.confirm('Case Study: ', 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: caseStudyId,
                        userId: userId,
                        isUser: isUser,
                        type: 'caseStusyAdult'
                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        alertify.success('Deleted');
                    }
                });
            }, function() {});

        });

        $(".select2_single").select2();

        var current_datatable = $("#datatable-responsive").DataTable({
            "scrollX": true,
            responsive: false,
            "ordering": true,
            "order": [
                [0, "desc"]
            ],
            "aoColumns": [{
                    "sWidth": "10%"
                }, {
                    "sWidth": "10%"
                }, {
                    "sWidth": "10%"
                }, {
                    "sWidth": "20%"
                }, {
                    "sWidth": "10%"
                }, {
                    "sWidth": "10%"
                }, {
                    "sWidth": "10%"
                }, {
                    "sWidth": "10%"
                }, {
                    "sWidth": "10%"
                }, {
                    "sWidth": "10%"
                }
                <?php if ($directionType != 'canvas') { ?>, {
                        "sWidth": "10%",
                        "bSortable": false
                    }
                <?php } ?>
                <?php if ($isActiveCanvas && $directionType != 'canvas') { ?>, {
                        "sWidth": "10%"
                    }
                <?php } ?>
            ]
        });

        $(document).on('click', '.isSendRecordToCanvas', function() {

            var that = this;
            var caseStudydate = $(this).attr('caseStudydate');
            var rotation = $(this).attr('rotation');
            var caseStudyName = $(this).attr('caseStudyName');
            var caseChiefComplaint = $(this).attr('caseChiefComplaint');
            var clinicianApprove = $(this).attr('clinicianApprove');
            var schoolApprove = $(this).attr('schoolApprove');
            var caseStudyId = $(this).attr('caseStudyId');
            var caseStudyType = $(this).attr('caseStudyType');
            var studentId = $(this).attr('studentId');
            var studentFullName = $(this).attr('studentFullName');
            var schoolId = "<?php echo $currentSchoolId; ?>";

            alertify.confirm('Case Study', 'Continue with send record to Canvas?', function() {
                $(that).text('Loading..');
                $(that).prop('disabled', true);
                $.ajax({
                    type: "POST",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_send_record_to_canvas.html",
                    data: {

                        caseStudydate: caseStudydate,
                        rotation: rotation,
                        caseStudyName: caseStudyName,
                        caseChiefComplaint: caseChiefComplaint,
                        clinicianApprove: clinicianApprove,
                        schoolApprove: schoolApprove,
                        caseStudyId: caseStudyId,
                        caseStudyType: caseStudyType,
                        studentFullName: studentFullName,
                        studentId: studentId,
                        schoolId: schoolId,
                        type: 'CaseStudy'
                    },
                    success: function(response) {
                        if (response == 'Success') {
                            $(that).addClass('hide');
                            $('.isSentToCanvas_' + caseStudyId).removeClass('hide')
                            alertify.success('Record Successfully Sent to Canvas.');
                        } else {
                            alertify.success(response);
                        }

                    }
                });
            }, function() {});

        });

        $("#canvasStatus").change(function() {
            var canvasStatus = $(this).val();
            var encodedRotationId = '<?php echo $encodedRotationId; ?>';
            var encodedStudentId = '<?php echo $encodedStudentId; ?>';


            if (encodedRotationId != '') {
                if (canvasStatus)
                    window.location.href = "interaction.html?canvasStatus=" + canvasStatus + "&rotationId=" + encodedRotationId;
                else
                    window.location.href = "interaction.html?rotationId=" + encodedRotationId;
            } else if (encodedStudentId != '') {
                if (canvasStatus)
                    window.location.href = "interaction.html?canvasStatus=" + canvasStatus + "&studentId=" + encodedStudentId;
                else
                    window.location.href = "interaction.html?studentId=" + encodedStudentId;
            }

        });
    </script>


</body>

</html>