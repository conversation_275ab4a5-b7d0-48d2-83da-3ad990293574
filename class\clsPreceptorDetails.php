<?php
class clsPreceptorDetails
{
    var $id = 0;
    var $type = '';
    var $schoolId = 0;
    var $status = 0;
    var $isActive = 0;
    var $createdDate = '';
    var $createdBy = '';
    var $updatedBy = '';
    var $referenceId = 0;
    var $preceptorId = 0;
    var $signatureDate = '';

    function SavePreceptorDetails($id=0)
    {
        $objDB = new clsDB();
        $sql = '';
        if ($id > 0) {
            $sql = "UPDATE preceptordetails SET 
                schoolId = '" . addslashes($this->schoolId) . "',
                referenceId = '" . addslashes($this->referenceId) . "',
                type = '" . addslashes($this->type) . "',
                signatureDate = '" . addslashes($this->signatureDate) . "',
                status = '" . addslashes($this->status) . "',
                updatedBy = '" . addslashes($this->createdBy) . "',
                updatedDate = '" . (date("Y-m-d h:i:s")) . "'
                Where id= " . $id;
            // echo $sql;exit;
            $objDB->ExecuteQuery($sql);
        } else {
            $sql = "INSERT INTO `preceptordetails` (`schoolId`,`referenceId`,`preceptorId`, `type`,`status`,`createdBy`, `createdDate`) 
                    VALUES ('" . addslashes($this->schoolId) . "',
                            '" . addslashes($this->referenceId) . "',
                            '" . addslashes($this->preceptorId) . "',
                            '" . addslashes($this->type) . "',
                            '" . addslashes($this->status) . "',
                            '" . addslashes($this->createdBy) . "',
                            '" . (date("Y-m-d h:i:s")) . "'
                            )";
            // echo $sql;exit;
            $id = $objDB->ExecuteInsertQuery($sql);
        }
        unset($objDB);
        return $id;
    } 

    function GetfloorCaseStudyPreceptorDetails($referenceId,$type='')
    {
        $objDB = new clsDB();
        $rows = "";
        $sql = "SELECT preceptordetails.schoolId,preceptordetails.preceptorId,preceptordetails.status, preceptordetails.type,preceptordetails.signatureDate,extenal_preceptors.mobile_num,extenal_preceptors.firstName,extenal_preceptors.lastName FROM `preceptordetails` INNER JOIN casestudyfloor ON casestudyfloor.caseStudyFloorId = preceptordetails.referenceId AND preceptordetails.type = 'floorCaseStudy' INNER JOIN extenal_preceptors ON preceptordetails.preceptorId = extenal_preceptors.id WHERE preceptordetails.referenceId =" .$referenceId;

        // echo $sql;
        $rows = $objDB->GetDataRow($sql);
        unset($objDB);
        return $rows;
    }


    function GetAllPreceptorDetails($referenceId ,$type='')
    {
        $objDB = new clsDB();
        $rows = "";
        $sql = "SELECT * FROM preceptordetails WHERE referenceId=". $referenceId;

        // echo $sql;
        $rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
    }

    function GetActivitySheetPreceptorDetails($referenceId)
    {
        $objDB = new clsDB();
        $rows = "";
        $sql = "SELECT preceptordetails.schoolId,preceptordetails.preceptorId,preceptordetails.status, 
                preceptordetails.type,preceptordetails.signatureDate,extenal_preceptors.mobile_num,extenal_preceptors.firstName,
                extenal_preceptors.lastName 
                FROM `preceptordetails` 
                INNER JOIN activitysheetdetails ON activitysheetdetails.activityId = preceptordetails.referenceId 
                INNER JOIN extenal_preceptors ON preceptordetails.preceptorId = extenal_preceptors.id
                WHERE preceptordetails.referenceId =" .$referenceId;

        // echo $sql;
        $rows = $objDB->GetDataRow($sql);
        unset($objDB);
        return $rows;
    }
     function GetSoapNotePreceptorDetails($referenceId)
    {
        $objDB = new clsDB();
        $rows = "";
        $sql = "SELECT preceptordetails.schoolId,preceptordetails.preceptorId,preceptordetails.status, 
                preceptordetails.type,preceptordetails.signatureDate,extenal_preceptors.mobile_num,extenal_preceptors.firstName,
                extenal_preceptors.lastName 
                FROM `preceptordetails` 
                INNER JOIN soapnote ON soapnote.soapNoteId = preceptordetails.referenceId 
                INNER JOIN extenal_preceptors ON preceptordetails.preceptorId = extenal_preceptors.id
                WHERE preceptordetails.referenceId = $referenceId AND preceptordetails.type='soapnote'";

        // echo $sql;
        $rows = $objDB->GetDataRow($sql);
        unset($objDB);
        return $rows;
    }

}
