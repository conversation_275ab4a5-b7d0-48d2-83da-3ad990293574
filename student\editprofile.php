<?php

include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsStudent.php');
include('../class/clsSchool.php');
include('../class/clsCountryStateMaster.php');
include('../setRequest.php');

// echo "<pre>";
// print_r($_SESSION);


$title = "Edit Profile";
$objStudent = new clsStudent();
$studentId = $_SESSION['loggedStudentId'];
$schoolId = $currentSchoolId;
$row =  $objStudent->GetStudentDetails($_SESSION['loggedStudentId']);
unset($objStudent);
$recordIdNumber  = $row['recordIdNumber'] ? $row['recordIdNumber'] : '';
$firstName  = stripslashes($row['firstName']);
$lastName  = stripslashes($row['lastName']);
$email  = stripslashes($row['email']);
$userName  = stripslashes($row['username']);
$address1  = stripslashes($row['address1']);
$address2  = stripslashes($row['address2']);

$dbStateId  = ($row['stateId']);
$city  = stripslashes($row['city']);
$zip  = stripslashes($row['zip']);
$profilePic = stripslashes($row['profilePic']);
$defaultprofilePic = GetStudentImagePath($_SESSION['loggedStudentId'], $currentSchoolId, $profilePic);

//CountryState
$objCountryStateMaster = new clsCountryStateMaster();
$countries = $objCountryStateMaster->GetAllCountry();
$dbCountryId = $objCountryStateMaster->GetParentIdFromChildId($dbStateId);
unset($objCountryStateMaster);


?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($title); ?></title>
    <?php include('includes/headercss.php'); ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/css/cropper.css">

    <style>
        .form-control {
            height: 45px;
        }

        .input-group {
            width: 100%;
        }

        .required-input {
            border-left: 3px solid red !important;
        }

        .input-group-addon {
            background: transparent;
        }

        .formSubHeading {
            border-bottom: 2px solid #d9d6d657;
            padding: 3px 0;
            position: relative;
        }

        .school-logo-section {
            display: flex;
            align-items: center;
        }

        .logo-image-section {
            margin-right: 20px
        }

        .img-thumbnail {
            width: 120px;
            max-width: 120px;
            border-radius: 10px;
            padding: 6px;
        }

        input[type="file"] {
            background-color: #fff !important;
        }

        @media screen and (max-width: 500px) {

            .school-logo-section {
                align-items: start;
                flex-direction: column;
            }

            .logo-image-section {
                margin-right: 0;
                margin-bottom: 20px;
            }

        }
    </style>
</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li class="active"><?php echo ($title); ?></li>
                </ol>
            </div>

        </div>
    </div>

    <div class="container">

        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "InvalidFile") {
        ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Please check image file extension.
                </div>

            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>

            <?php
            } else if ($_GET["status"] == "Updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Profile updated successfully.
                </div>
        <?php
            }
        }
        ?>

        <form id="frmEditProfile" data-parsley-validate class="form-horizontal" method="POST" action="editprofilesubmit.html" enctype="multipart/form-data">

            <div class="row">
                <div class="col-md-12">

                    <div class="formSubHeading">User Information</div>
                    <!-- record id -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="recordIdNumber">Student ID</label>
                                <div class="col-md-12">
                                    <input id="recordIdNumber" name="recordIdNumber" value="<?php echo ($recordIdNumber); ?>" type="text" placeholder="" class="form-control input-md validateOnlynumbers" onchange="validateData('recordId');">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <!-- Text input-->
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtFirstName">First Name</label>
                                <div class="col-md-12">
                                    <input id="txtFirstName" data-parsley-pattern="^[a-zA-Z]+$" name="txtFirstName" value="<?php echo ($firstName); ?>" required type="text" placeholder="" class="form-control input-md required-input" required="">

                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <!-- Text input-->
                        <div class="col-md-6">
                            <!-- Select Basic -->
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtLastName">Last Name</label>
                                <div class="col-md-12">
                                    <input id="txtLastName" data-parsley-pattern="^[a-zA-Z]+$" name="txtLastName" value="<?php echo ($lastName); ?>" required type="text" placeholder="" class="form-control input-md required-input" required="">

                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <!-- Text input-->
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtEmail">Email</label>
                                <div class="col-md-12">
                                    <input id="txtEmail" name="txtEmail" value="<?php echo ($email); ?>" required type="email" placeholder="" class="form-control input-md required-input">

                                </div>
                            </div>
                        </div>
                        <!-- Text input-->
                        <!---div class="form-group">
                        <label class="col-md-4 control-label" for="cboGender">Gender</label>
                        <div class="col-md-8">
                            <select id="cboGender" name="cboGender" class="form-control select2_single" required>
                                <option value="">Select</option>
                                <option value="M" <?php //if($gender=="M"){
                                                    ?> selected <?php // } 
                                                                ?> >Male</option>
                                <option value="F" <?php //if($gender=="F"){
                                                    ?> selected <?php // } 
                                                                ?> >Female</option>
                            </select>
                        </div>
                    </div---->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtUsername">User Name</label>
                                <div class="col-md-12">
                                    <input id="txtUsername" name="txtUsername" value="<?php echo ($userName); ?>" required type="text" placeholder="" class="form-control input-md required-input">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="formSubHeading">Address Information</div>
                    <!-- Text input-->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtAddress1">Address 1</label>
                                <div class="col-md-12">
                                    <input id="txtAddress1" name="txtAddress1" type="text" value="<?php echo ($address1); ?>" class="form-control input-md required-input" required>

                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <!-- Text input-->
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtAddress2">Address 2</label>
                                <div class="col-md-12">
                                    <input id="txtAddress2" name="txtAddress2" type="text" placeholder="" value="<?php echo ($address2); ?>" class="form-control input-md">

                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="cboCountry">Country</label>
                                <div class="col-md-12 flex-col-reverse">
                                    <select id="cboCountry" name="cboCountry" class="form-control step1 input-md  select2_single" required>
                                        <option value="" selected>Select</option>
                                        <?php
                                        if ($countries != "") {
                                            while ($row = mysqli_fetch_assoc($countries)) {
                                                $location_id  = $row['location_id'];
                                                $name  = stripslashes($row['name']);

                                        ?>
                                                <option value="<?php echo ($location_id); ?>" <?php if ($dbCountryId == $location_id) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                                        <?php

                                            }
                                        }
                                        ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <!-- Select Basic -->
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtCity">City</label>
                                <div class="col-md-12">
                                    <input id="txtCity" name="txtCity" type="text" required value="<?php echo ($city); ?>" class="form-control input-md required-input">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <!-- Select Basic -->
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="cboState">State</label>
                                <div class="col-md-12 flex-col-reverse">
                                    <select id="cboState" name="cboState" class="form-control step2 input-md select2_single" required>
                                        <option value="" selected>Select</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <!-- Text input-->
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtZipCode">Zip Code</label>
                                <div class="col-md-12">
                                    <input id="txtZipCode" name="txtZipCode" type="text" placeholder="" value="<?php echo ($zip); ?>" class="form-control input-md">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                      <div class="col-md-12">
                    <div class="formSubHeading">Other Information</div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="filePhoto">Upload Photo</label>
                                <div class="col-md-12 school-logo-section">
                                    <!-- Hidden field to store cropped image data -->
                                    <input type="hidden" id="fileLogo" name="fileLogo" value="">
                                    <input type="hidden" id="hasCroppedImage" name="hasCroppedImage" value="0">
                                    
                                    <section class="upload-section" id="uploadSection">
                                        <div class="upload-area" id="uploadArea">
                                            <input style="visibility: hidden;" type="file" id="fileInput" accept="image/*" hidden>
                                            <div class="upload-content">
                                                <?php if ($defaultprofilePic != '') { ?>
                                                    <div style="display: flex; flex-direction: column; align-items: center; gap: 10px;">
                                                        <img src="<?php echo ($defaultprofilePic . "?randId=" . $randormRefreshId) ?>" 
                                                            style="max-width: 150px; max-height: 150px; border-radius: 8px; object-fit: contain; box-shadow: 0 4px 8px rgba(0,0,0,0.1);" 
                                                            alt="Profile Picture Preview">
                                                        <div style="text-align: center;">
                                                            <p style="color: green; font-weight: bold; margin: 5px 0; font-size: 14px;">✓ Current profile picture</p>
                                                            <span class="file-types" style="color: #666; font-size: 12px;">Click to select a different image if needed</span>
                                                        </div>
                                                    </div>
                                                <?php } else { ?>
                                                    <svg class="upload-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                                        <polyline points="7,10 12,15 17,10"></polyline>
                                                        <line x1="12" y1="15" x2="12" y2="3"></line>
                                                    </svg>
                                                    <p>Click to browse or drag and drop an image</p>
                                                    <span class="file-types">Supports: JPG, PNG, GIF, WebP</span>
                                                <?php } ?>
                                            </div>
                                        </div>
                                    </section>
                                </div>
                            </div>
                        </div>
                    </div>
                        <section class="cropper-section" id="cropperSection" style="display: none;">
                <div class="controls">
                    <div class="control-group">
                        <div class="mode-buttons">
                            <button type="button" class="mode-btn active" data-mode="rectangle">Rectangle</button>
                            <button type="button" class="mode-btn" data-mode="square">Square</button>
                        </div>
                    </div>
                    <div class="control-group">
                        <button type="button" class="action-btn" id="resetBtn">Reset</button>
                        <button type="button" class="action-btn" id="backBtn">Back</button>
                        <button type="button" class="action-btn primary" id="cropBtn">Crop & Save</button>
                    </div>
                    <div class="control-group">
                        <label>Zoom:</label>
                        <div class="zoom-controls">
                            <button type="button" class="zoom-btn" id="zoomOut">-</button>
                            <span class="zoom-level" id="zoomLevel">100%</span>
                            <button type="button" class="zoom-btn" id="zoomIn">+</button>
                            <input type="range" id="zoomSlider" min="20" max="300" value="100" class="zoom-slider">
                        </div>
                    </div>
                </div>
                <div style="display: flex; gap: 20px; align-items: flex-start;">
                    <div class="image-container" id="imageContainer">
                        <img id="previewImage" src="" alt="Preview">
                        <div class="crop-overlay" id="cropOverlay">
                            <div class="crop-selection" id="cropSelection"></div>
                        </div>
                    </div>

                    <div class="preview-section">
                        <h4>Cropped Preview</h4>
                        <canvas id="previewCanvas"></canvas>
                    </div>
                </div>
            </section>
                </div>
                
                    </div>
                </div>
                <div class="form-group m-0">
                    <!-- <label class="col-md-4 control-label"></label> -->
                    <div class="col-md-12" style="display: flex; justify-content: center; margin: 20px 0;gap: 15px;">
                        <button id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
                        <a type="button" href="dashboard.html" class="btn btn-default">Cancel</a>
                    </div>
                </div>

            </div>




    </div>

    </form>


    </div>

    <?php include('includes/footer.php'); ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/jquery.cascadingdropdown.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/cropper.js"></script>


    <script type="text/javascript">
         $(document).ready(function() {
            new ImageCropper();
            
            // Check if default profile image exists and display it
            const defaultImagePath = '<?php echo $defaultprofilePic; ?>';
            if (defaultImagePath && defaultImagePath !== '') {
                const $uploadArea = $('#uploadArea');
                
                // Add cropped state class to indicate we have an image
                $uploadArea.addClass('cropped-state');
            }
        });

        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        $(window).load(function() {

            $(".select2_single").select2();
            $('#select2-cboGender-container').addClass('required-select2');

            $('.image-preview').magnificPopup({
                type: 'image'
            });

            //  $('#frmEditProfile').parsley().on('field:validated', function() {
            //     var ok = $('.parsley-error').length === 0;
            // })
            // .on('form:submit', function() {
            //     ShowProgressAnimation();									  
            //     return true; // Don't submit form for this demo
            // });
            $('#frmEditProfile').parsley().on('field:validated', function() {
                var ok = $('.parsley-error').length === 0;
            }).on('form:submit', function(event) {
                var isRecordIdValidated = validateData('recordId');
                // var isMobileNoValidated = validateData('mobileNo');

                Promise.all([isRecordIdValidated]).then(function(values) {
                    var isRecordIdValid = values[0];
                    // var isMobileNoValid = values[1];

                    if (isRecordIdValid) {
                        // Both validations passed, submit the form
                        $('#frmEditProfile')[0].submit();
                    } else {
                        // At least one validation failed
                        console.log('Validation failed. Please check the form inputs.');
                        return false; // Prevent form submission
                    }
                }).catch(function(error) {
                    console.log('Validation error:', error);
                });

                return false; // Prevent form submission
            });

            <?php
            if ($dbCountryId == 0) {
            ?>
                $('#cboCountry').val('224').trigger('change');
            <?php
            }
            ?>

            $('#frmEditProfile').cascadingDropdown({
                selectBoxes: [{
                        selector: '.step1',
                        selected: '<?php echo ($dbCountryId); ?>'
                    },
                    {
                        selector: '.step2',
                        selected: '<?php echo ($dbStateId); ?>',
                        requires: ['.step1'],
                        requireAll: true,
                        source: function(request, response) {

                            $.getJSON('<?php echo ($dynamicOrgUrl); ?>/ajax/getStates.html', request, function(data) {
                                response($.map(data, function(item, index) {
                                    return {
                                        label: item['StateName'],
                                        value: item['StateId']
                                    };
                                }));
                            });
                        }
                    }
                ]

            });

            $("#txtUsername").change(function() {
                var currentUsername = $(this).val();
                $.ajax({
                    type: "POST",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_check_duplicate_username.html",
                    data: {
                        userId: '<?php echo (EncodeQueryData($_SESSION['loggedStudentId'])); ?>',
                        type: 'student',
                        userName: currentUsername,
                        schoolId: '<?php echo (EncodeQueryData($currentSchoolId)); ?>'
                    },
                    success: function(responseData) {
                        if (responseData == 1) {
                            alertify.error("Username not available.");
                            $("#txtUsername").val('').focus();
                        }
                    }
                });

            });

        });

        function validateData(type) {
            return new Promise(function(resolve, reject) {
                if (type == 'recordId')
                    var value = $('#recordIdNumber').val();
                else
                    var value = $('#txtPhone').val();
                var schoolId = '<?php echo $schoolId; ?>';
                var studentId = '<?php echo ($studentId); ?>';
                $.ajax({
                    url: '<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_validate_student.html',
                    type: 'POST',
                    data: {
                        value: value,
                        type: type,
                        schoolId: schoolId,
                        studentId: studentId
                    },
                    dataType: 'json',
                    success: function(response) {
                        // console.log(response);
                        var status = response['status'];
                        var userId = response['studentId'];

                        if (status == 1 && studentId != userId) {
                            if (type == 'recordId') {
                                alertify.error("Student ID is already exist");
                                if (studentId == 0)
                                    $('#recordIdNumber').val('');

                                resolve(false);
                            } else {
                                alertify.error("Phone number is already exist");
                                if (studentId == 0)
                                    $('#txtPhone').val('');
                                resolve(false);
                            }
                        } else {
                            resolve(true);
                        }
                    },
                    error: function(xhr) {
                        // Handle error
                        console.log(xhr.responseText);
                        reject(xhr.responseText);
                    }
                });
            });
        }
    </script>
</body>

</html>