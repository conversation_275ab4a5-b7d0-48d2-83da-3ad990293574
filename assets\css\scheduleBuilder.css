
        /* Add viewport-specific styles to ensure consistent sizing */
        /* html {
            font-size: 16px; 
        }

        body {
            font-size: 1rem;
            line-height: 1.5;
        } */

        /* Adjust container sizing to be more consistent */
        

        /* Fix for zoom issues */
        @media screen and (min-width: 768px) {
            html {
                font-size: 14px; /* Maintain font size at medium screens */
            }
            
            .card, .form-group, .input {
                transform: scale(1); /* Force proper scaling */
                transform-origin: top left;
            }
        }

        /* Header */
        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 800;
            color: #1f2937;
            margin-bottom: 1rem;
            letter-spacing: -0.025em;
        }

        .header p {
            font-size: 1.25rem;
            color: #6b7280;
            max-width: 32rem;
            margin: 0 auto;
        }

        /* Add these styles to fix the sizing issues */
        .schedule-container {
            max-width: 1200px !important;
            width: 90% !important;
            margin: 0 auto;
            padding: 2rem;
        }

        .card {
            width: 100%;
            /* max-width: 800px; */
            margin: 0 auto 2rem auto;
        }

        .form-group {
            margin-bottom: 1.5rem;
            width: 100%;
        }

        .input {
            height: 37px;
            font-size: 1rem;
        }

        /* .btn {
            padding: 0.75rem 1.5rem;
            font-size: 1rem;
        } */

        /* Ensure proper spacing for the progress steps */
        .progress-steps {
            width: 60%;
            /* max-width: 800px; */
            margin: 0 auto 3rem auto;
        }

        .step-circle {
            width: 3.5rem;
            height: 3.5rem;
            font-size: 1.25rem;
        }

        /* Progress Steps */
        .progress-steps {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 3rem;
            gap: 1rem;
            flex-wrap: wrap;
            width: 60%;
        }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            position: relative;
        }

        .step-circle {
            width: 3rem;
            height: 3rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
            background: #e5e7eb;
            color: #6b7280;
        }

        .step-circle.active {
            background: #5CB85C;
            color: white;
            transform: scale(1.1);
        }

        .step-circle.completed {
            background: #5CB85C;
            color: white;
        }

        .step-title {
            font-size: 0.875rem;
            font-weight: 500;
            color: #6b7280;
            margin-top: 0.5rem;
            text-align: center;
        }

        .step.active .step-title {
            color: #5CB85C;
        }

        .step-connector {
            flex: 1;
            height: 0.25rem;
            background-color: #e5e7eb;
            margin: 0 -4.3rem;
            margin-top: -30px;
        }

        .step-connector.completed {
            background: #5CB85C;
        }

        /* Cards */
        .card {
            background: rgba(255, 255, 255, 0.7);
            backdrop-filter: blur(8px);
            border-radius: 1rem;
            padding: 2rem;
            box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 2rem;
        }

        .card-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .card-description {
            color: #6b7280;
        }

        /* Form Controls */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
        }

        .input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 1px solid #d1d5db;
            border-radius: 0.5rem;
            font-size: 1rem;
            transition: all 0.2s ease;
            background: white;
            cursor: pointer;
        }

        .input:focus {
            outline: none;
            border-color: #3b82f6;
            /* box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1); */
        }

        /* Search and Actions */
        .search-actions {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
            align-items: center;
        }

        .search-container {
            position: relative;
            flex: 1;
            max-width: 20rem;
        }

        .search-icon {
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: #9ca3af;
            width: 1rem;
            height: 1rem;
        }

        .search-input {
            padding-left: 2.5rem;
        }

        .actions {
            display: flex;
            gap: 0.5rem;
        }

        /* Buttons */
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 0.5rem;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: green;
            color: white;
        }
        .button {
               border: 1px solid #5cb85c;
    background-color: #5cb85c;
    color: #fff;
    cursor: pointer;
    padding: 10px 30px;
    border-radius: 10px;
        }

/* 
        .btn-primary:hover {
            background: #2563eb;
        } */

        .btn-outline {
            background: transparent;
            border: 1px solid #d1d5db;
            color: #374151;
            font-size: 13px;
        }

        .btn-outline:hover {
            background: #f9fafb;
        }

        .btn-green {
            color: #059669;
            border-color: #bbf7d0;
        }

        .btn-green:hover {
            background: #ecfdf5;
        }

        .btn-red {
            color: #dc2626;
            border-color: #fecaca;
        }

        .btn-red:hover {
            background: #fef2f2;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* Grid */
        .grid {
            display: grid;
            gap: 1rem;
        }

        .grid-2 {
            grid-template-columns: repeat(auto-fit, minmax(15rem, 1fr));
        }

        .grid-3 {
            grid-template-columns: repeat(5, 1fr);
        }

        /* Selection Cards */
        .selection-card {
            background-color: #fff;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            padding: 10px;
            transition: all 0.2s ease;
            cursor: pointer;
            margin-bottom: 0rem;
        }
        .selection-card1 {
            background-color: #fff;
            /* border: 1px solid #e5e7eb; */
            border-radius: 0.5rem;
            /* padding: 10px; */
            transition: all 0.2s ease;
            cursor: pointer;
            margin-bottom: 0rem;
        }

        .selection-card:hover {
            border-color: #3b82f6;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
/* 
        .selection-card.selected {
            border-color: #3b82f6;
            background-color: #eff6ff;
        } */

        .selection-card-content {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .selection-card-text {
            
            flex: 1;
        }

        .selection-card-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.25rem;
        }

        .selection-card-details {
            font-size: 0.875rem;
            color: #6b7280;
        }

        .custom-checkbox {
            width: 1.5rem;
            height: 1.5rem;
            border: 2px solid #d1d5db;
            border-radius: 0.25rem;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .selection-card.selected .custom-checkbox {
            background-color: #5CB85C;
            border-color: #5CB85C;
        }

        .selection-card.selected .custom-checkbox:after {
            content: '';
            width: 0.5rem;
            height: 0.75rem;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
            margin-bottom: 0.125rem;
        }

        .selection-card-content {
            display: flex;
            align-items: flex-start;
        }

        .selection-card-text {
            flex: 1;
        }

        .selection-card-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.25rem;
        }

        .selection-card-details {
            font-size: 0.75rem;
            color: #6b7280;
            line-height: 1.4;
        }

        /* Badge */
        .badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .badge-secondary {
            background: #f3f4f6;
            color: #374151;
        }

        .badge-outline {
            border: 1px solid #d1d5db;
            background: transparent;
            color: #374151;
        }

        /* Site Type Colors */
        .badge-general { background: #dbeafe; color: #1e40af; }
        .badge-clinic { background: #dcfce7; color: #166534; }
        .badge-medical-center { background: #f3e8ff; color: #7c2d12; }
        .badge-urgent-care { background: #fee2e2; color: #991b1b; }
        .badge-hospital { background: #e0e7ff; color: #3730a3; }
        .badge-pediatrics { background: #fce7f3; color: #be185d; }
        .badge-wellness { background: #f0fdfa; color: #065f46; }

        /* Step Content */
        .step-content {
            display: none;
            max-width: 80rem;
            margin: 0 auto;
        }

        .step-content.active {
            display: block;
        }

        /* Calendar */
        .calendar {
            background: rgba(255, 255, 255, 0.7);
            backdrop-filter: blur(8px);
            border-radius: 1rem;
            padding: 2rem;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            margin-top: 2rem;
        }

        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 1rem;
            margin-top: 1rem;
        }

        .calendar-day {
            background-color: #fff;
            border-radius: 0.5rem;
            padding: 0.75rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            min-height: 120px;
            display: flex;
            flex-direction: column;
        }

        .calendar-day:hover {
            background: #f3f4f6;
        }

        .calendar-day.has-events {
            border-color: #3b82f6;
            background: #eff6ff;
        }

        .calendar-day-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .calendar-day-name {
            font-weight: 500;
            color: #6b7280;
        }

        .calendar-day-number {
            font-weight: 600;
        }

        .calendar-event {
            background: #3b82f6;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            margin-bottom: 0.25rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .has-events {
            border-left: 3px solid #3b82f6;
        }

        .site-rule {
            background-color: white  !important;
            border-radius: 0.5rem;
            padding: 8px;
            margin-bottom: 0rem;
        }

        .site-rule h4 {
            margin-top: 0;
            margin-bottom: 0.75rem;
            font-size: 1rem;
        }

        /* Navigation */
        .navigation {
            display: flex;
            justify-content: end;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #e5e7eb;
        }

        /* Loading */
        .loading {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .spinner {
            width: 3rem;
            height: 3rem;
            border: 0.25rem solid #e5e7eb;
            border-top: 0.25rem solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .grid-2, .grid-3 {
                grid-template-columns: 1fr;
            }

            .search-actions {
                flex-direction: column;
                align-items: stretch;
            }

            .actions {
                justify-content: center;
            }

            .navigation {
                flex-direction: column;
                gap: 1rem;
            }

            .calendar-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Update responsive styles for calendar */
        @media (max-width: 768px) {
            .calendar-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 480px) {
            .calendar-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Add these styles for the site rules grid layout */
        .site-rules-section {
            margin-top: 2rem;
            margin-bottom: 2rem;
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
        }

        .site-rules-container {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1rem;
        }

        .site-rule {
            background-color: #f9fafb;
            border-radius: 0.5rem;
            padding: 8px;
            border: 1px solid #e5e7eb;
            transition: all 0.2s ease;
        }

        .site-rule:hover {
            border-color: #3b82f6;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .site-rule h4 {
            margin-top: 0;
            margin-bottom: 0.75rem;
            font-size: 1rem;
            color: #1f2937;
        }

        /* Make site rules responsive */
        @media (max-width: 1024px) {
            .site-rules-container {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 640px) {
            .site-rules-container {
                grid-template-columns: 1fr;
            }
        }

        /* Add these styles to remove the input number spinners */
        /* For Chrome, Safari, Edge, Opera */
        input[type="number"]::-webkit-inner-spin-button,
        input[type="number"]::-webkit-outer-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        /* For Firefox */
        input[type="number"] {
            -moz-appearance: textfield;
        }

        /* Enhanced Calendar Styling */
        #calendar-container {
            margin-top: 2rem;
            padding: 0;
        }
        
        #calendar {
            height: auto;
            min-height: 650px;
            background: white;
            padding: 1.5rem;
            border-radius: 1rem;
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(229, 231, 235, 0.5);
        }
        
        /* FullCalendar Custom Styling */
        .fc {
            --fc-border-color: #e5e7eb;
            --fc-page-bg-color: #fff;
            --fc-neutral-bg-color: #f9fafb;
            --fc-list-event-hover-bg-color: #f3f4f6;
            --fc-today-bg-color: #eff6ff;
            font-family: inherit;
        }
        
        .fc .fc-toolbar {
            margin-bottom: 1.5rem !important;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .fc .fc-toolbar-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1f2937;
        }
        
        .fc .fc-button-primary {
            background-color: #3b82f6;
            border-color: #3b82f6;
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            font-weight: 500;
            transition: all 0.2s ease;
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        }
        
        .fc .fc-button-primary:not(:disabled):hover {
            background-color: #2563eb;
            border-color: #2563eb;
            transform: translateY(-1px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        
        .fc .fc-button-primary:not(:disabled):active {
            background-color: #1d4ed8;
            border-color: #1d4ed8;
            transform: translateY(0);
        }
        
        .fc .fc-button-primary:disabled {
            background-color: #93c5fd;
            border-color: #93c5fd;
        }
        
        .fc .fc-daygrid-day-top {
            justify-content: center;
            padding-top: 0.5rem;
        }
        
        .fc .fc-daygrid-day-number {
            font-size: 1rem;
            font-weight: 500;
            color: #4b5563;
        }
        
        .fc .fc-col-header-cell-cushion {
            font-weight: 600;
            color: #1f2937;
            padding: 0.75rem 0;
        }
        
        .fc .fc-daygrid-day.fc-day-today {
            background-color: #eff6ff;
        }
        
        .fc .fc-daygrid-day.fc-day-today .fc-daygrid-day-number {
            color: #2563eb;
            font-weight: 700;
        }
        
.fc-event {
    border-radius: 0.375rem;
    padding: 0.125rem 0.25rem !important;
    margin: 2px 0 !important; /* Reduced gap between events */
    border: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    cursor: grab;
    position: relative;
}
        
        .fc-event.fc-event-dragging {
            cursor: grabbing;
            opacity: 0.8;
        }
        
        .fc-event-mirror {
            z-index: 1000;
            pointer-events: none;
            opacity: 0.8;
        }
        
        .fc-event:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            z-index: 5;
        }
        
        .fc-event-title {
            font-weight: 500;
            font-size: 0.875rem;
            white-space: normal;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .fc-event-time {
            font-size: 0.75rem;
            opacity: 0.9;
        }
        
        /* Calendar Header */
        .calendar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .calendar-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1f2937;
        }
        
        .calendar-actions {
            display: flex;
            gap: 0.5rem;
        }
        
/* Calendar Legend */
.calendar-legend {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e5e7eb;
}

/* Remove icon styling */
.event-remove {
    position: absolute;
    top: 2px;
    right: 2px;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.2s;
    font-size: 16px;
    font-weight: bold;
    z-index: 100;
    color: white;
    background: rgba(0,0,0,0.3);
    width: 18px;
    height: 18px;
    border-radius: 9px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
}
.event-remove:hover { 
    opacity: 1;
    background: rgba(0,0,0,0.5);
}
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            color: #4b5563;
        }
        
        .legend-color {
            width: 1rem;
            height: 1rem;
            border-radius: 0.25rem;
        }
        
        /* Responsive fixes */
        @media (max-width: 768px) {
            .fc .fc-toolbar {
                flex-direction: column;
                align-items: center;
                gap: 1rem;
            }
            
            .fc .fc-toolbar-title {
                font-size: 1.25rem;
                margin: 0;
            }
            
            .fc .fc-button {
                padding: 0.375rem 0.75rem;
                font-size: 0.875rem;
            }
            
            .calendar-legend {
                justify-content: center;
            }
        }

        /* Add these styles for the holiday picker */
        .holiday-picker-container {
            display: flex;
            gap: 0.5rem;
        }

        #holiday-date-picker-div {
            display: none;
            margin-top: 0.5rem;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            padding: 0.5rem;
            background-color: white;
        }

        .selected-holiday a {
            background-color: #bfdbfe !important;
            border: 1px solid #3b82f6 !important;
            color: #1e40af !important;
            font-weight: bold !important;
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #e5e7eb;
        }

        /* Enhanced Input Styling */
        .input {
            width: 100%;
            height: 45px;
            padding: 0.75rem 1rem;
            border: none;
            border-radius: 12px !important;
            font-size: 1rem;
            transition: all 0.2s ease;
            background-color: #f6f9f9 !important;
            box-shadow: none !important;
            color: black;
        }

        .input:focus {
            outline: none;
            border-color: #3b82f6;
            /* box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important; */
        }

        .input[disabled], 
        .input[readonly] {
            background-color: #d9d9d9d1 !important;
            cursor: default;
        }

        .required-input {
            border-left: 3px solid red !important;
        }

        /* Select2 Styling to match inputs */
        .select2-container--default .select2-selection--single {
            background-color: #f6f9f9 !important;
            height: 45px !important;
            border-radius: 12px !important;
            box-shadow: none;
            border: none !important;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 45px !important;
            color: black;
        }

        .select2-container--default .select2-selection--single .select2-selection__arrow {
            position: absolute !important;
            top: 10px !important;
            right: 8px !important;
            width: 20px !important;
        }

        .select2-container--default.select2-container--disabled .select2-selection--single {
            background-color: #d9d9d9d1 !important;
            cursor: default;
        }

        /* Form Group Styling */
        .form-group {
            margin-bottom: 1.5rem;
            width: 100%;
        }

        .label {
            display: block;
            font-size: 14px !important;
            font-weight: 500 !important;
            color: #000 !important;
            margin-bottom: 0.5rem;
            text-align: left;
        }

        /* Remove number input spinners */
        input[type="number"]::-webkit-inner-spin-button,
        input[type="number"]::-webkit-outer-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        input[type="number"] {
            -moz-appearance: textfield;
        }

        /* Date input styling */
        input[type="date"] {
            position: relative;
        }

        input[type="date"]::-webkit-calendar-picker-indicator {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: auto;
            height: auto;
            color: transparent;
            background: transparent;
            cursor: pointer;
        }

        /* Textarea styling */
        textarea.form-control {
            height: auto;
            background-color: #f6f9f9 !important;
            border-radius: 12px !important;
            box-shadow: none !important;
            border: none !important;
            max-width: 100% !important;
            color: black;
        }

        /* Input group addon styling */
        .input-group-addon {
            position: absolute;
            right: 7px;
            z-index: 99;
            width: 35px;
            margin: auto;
            top: 5px;
            border-radius: 50% !important;
            height: 35px;
            color: #555;
            background: transparent;
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        /* Calendar Filters Section */
        .filters-section {
            background: rgba(249, 250, 251, 0.7);
            border-radius: 1rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(229, 231, 235, 0.7);
        }

        .filter-actions {
            display: flex;
            justify-content: flex-end;
            margin-top: 1rem;
            gap: 0.5rem;
        }
.max-students input{
    background-color: white !important;
}





  .checkbox-wrapper-31:hover .check {
    stroke-dashoffset: 0;
  }

  .checkbox-wrapper-31 {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 40px;
  }
  .checkbox-wrapper-31 .background {
    fill: #ccc;
    transition: ease all 0.6s;
    -webkit-transition: ease all 0.6s;
  }
  .checkbox-wrapper-31 .stroke {
    fill: none;
    stroke: #fff;
    stroke-miterlimit: 10;
    stroke-width: 2px;
    stroke-dashoffset: 100;
    stroke-dasharray: 100;
    transition: ease all 0.6s;
    -webkit-transition: ease all 0.6s;
  }
  .checkbox-wrapper-31 .check {
    fill: none;
    stroke: #fff;
    stroke-linecap: round;
    stroke-linejoin: round;
    stroke-width: 2px;
    stroke-dashoffset: 22;
    stroke-dasharray: 22;
    transition: ease all 0.6s;
    -webkit-transition: ease all 0.6s;
  }
  .checkbox-wrapper-31 input[type=checkbox] {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    margin: 0;
    opacity: 0;
    -appearance: none;
    -webkit-appearance: none;
  }
  .checkbox-wrapper-31 input[type=checkbox]:hover {
    cursor: pointer;
  }
  .checkbox-wrapper-31 input[type=checkbox]:checked + svg .background {
    fill: #6cbe45;
  }
  .checkbox-wrapper-31 input[type=checkbox]:checked + svg .stroke {
    stroke-dashoffset: 0;
  }
  .checkbox-wrapper-31 input[type=checkbox]:checked + svg .check {
    stroke-dashoffset: 0;
  }

 .grid-scroll-wrapper {
  max-height: 285px !important; /* adjust based on your layout */
  overflow-y: auto !important;
  padding-right: 8px; /* avoid cutting off scrollbar */
}
