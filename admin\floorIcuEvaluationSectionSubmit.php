<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsSchool.php');
include('../class/clsFloorTherapyAndICUEvaluation.php');

include('../setRequest.php');

// print_r($_POST);exit;
if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit'])) {
	$isFloorIcuType = isset($_POST['isFloorIcuType']) ? ($_POST['isFloorIcuType']) : 0;

	$sectionId = isset($_GET['editid']) ? DecodeQueryData($_GET['editid']) : 0;
	$status = ($sectionId > 0) ? 'updated' : 'added';

	$title = $_POST['txtsection'];
	$sortOrder = $_POST['txtsortorder'];

	//Save data
	$objFloorTherapy = new clsFloorTherapyAndICUEvaluation();
	$objFloorTherapy->title = $title;
	$objFloorTherapy->sortOrder = $sortOrder;
	$objFloorTherapy->isFloorIcuType = $isFloorIcuType;

	$retcheckoffsectionmasterId = $objFloorTherapy->SaveFloorIcuEvaluationSection($sectionId, $isFloorIcuType);

	if ($retcheckoffsectionmasterId > 0) {
		//Audit Log Start
		// Instantiate the Logger class
		$objLog = new clsLogger();

		// Determine the action type (EDIT or ADD) based on the presence of a journal ID
		$action = ($sectionId > 0) ? $objLog::EDIT : $objLog::ADD;
		$type = "section";
			$userType = $objLog::SUPERADMIN;

		$objFloorTherapy = new clsFloorTherapyAndICUEvaluation();

		$objFloorTherapy->saveFloorAndIcuEvaluationAuditLog($retcheckoffsectionmasterId, $_SESSION["loggedUserId"], $userType, $action, $type, $IsMobile = 0);
		unset($objFloorTherapy);

		unset($objLog);
		//Audit Log End

		if ($isFloorIcuType == 0) {
			$activeType = 'floor';
		} else {
			$activeType = 'icu';
		}

		header('location:floorIcuEvaluationSectionList.html?active=' . $activeType . '&status=' . $status);
	} else {
		header('location:addFloorIcuEvaluationSection.html?status=error');
	}
} else {
	header('location:floorIcuEvaluationSectionList.html?status=' . $status);
	exit();
}
