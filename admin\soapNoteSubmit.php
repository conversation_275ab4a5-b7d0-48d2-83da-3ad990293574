<?php
$IsMobile  = isset($_POST['IsMobile']) ? $_POST['IsMobile'] : 0;
$isPreceptor = isset($_GET['isPreceptor']) ? $_GET['isPreceptor'] : 0;

if (!$isPreceptor)
    include('includes/validateUserLogin.php');

include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsSchool.php');
include('../class/clsSystemUser.php');
include('../class/clsStudent.php');
include('../setRequest.php');
include('../class/clsSoapNote.php');
include('../class/clsPreceptorDetails.php');
include('../class/clsExternalPreceptors.php');

$objDB = new clsDB();
$objSoapNote = new clsSoapNote();
$objpreceptor = new clsPreceptorDetails();

//Get Country Code
$studentId = isset($_GET["studentId"]) ? $_GET["studentId"] : 0;

$countryCode = $objDB->GetSingleColumnValueFromTable('schools', 'countryCode', 'schoolId', $currentSchoolId);

if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit'])) {

    $isPreceptor = isset($_GET['isPreceptor']) ? $_GET['isPreceptor'] : 0;

    $soapNoteId = isset($_GET['editsoapNoteId']) ? DecodeQueryData($_GET['editsoapNoteId']) : 0;

    $date  = GetDateStringInServerFormat($_POST['soapnotedate']);
    $date = str_replace('00:00:00', '12:00 PM', $date);
    $date = date('Y-m-d H:i', strtotime($date));
    $rotationId = isset($_POST['cborotation']) ? DecodeQueryData($_POST['cborotation']) : 0;
    $isSendToPreceptor = isset($_POST['isSendToPreceptor']) ? ($_POST['isSendToPreceptor']) : 0;
    $preceptorNo = isset($_POST['preceptorNo']) ? ($_POST['preceptorNo']) : '';
    $studentSignatureDate = isset($_POST['studentDate']) ? ($_POST['studentDate']) : '';
    $clinicianSignatureDate = isset($_POST['ClinicianDate']) ? ($_POST['ClinicianDate']) : '';
    if ($clinicianSignatureDate) {

        $clinicianSignatureDate  = GetDateStringInServerFormat($_POST['ClinicianDate']);
        $clinicianSignatureDate = str_replace('00:00:00', '12:00 PM', $clinicianSignatureDate);
        $clinicianSignatureDate = date('Y-m-d H:i', strtotime($clinicianSignatureDate));
    } else {
        $clinicianSignatureDate = '';
    }
    $preceptorStatus = isset($_POST['preceptorStatus']) ? ($_POST['preceptorStatus']) : 0;

    if (isset($_POST['studentDate']) && !empty($_POST['studentDate'])) {
        $studentSignatureDate = GetDateStringInServerFormat($_POST['studentDate']);
        $studentSignatureDate = str_replace('00:00:00', '12:00 PM', $studentSignatureDate);
        $studentSignatureDate = date('Y-m-d H:i', strtotime($studentSignatureDate));
    } else {
        $studentSignatureDate = null; // Set to null if not provided
    }
    $status = ($soapNoteId > 0) ? 'updated' : 'added';
    if ($isPreceptor) {
        if ($IsMobile) {
            header('location:' . BASE_PATH . '/webRedirect.html?status=Success&type=soapnote');
            exit;
        } else {
            header('location:thankyou.html?isType=SoapNote');
            exit();
        }
    } else if ($soapNoteId > 0) {
        if ($IsMobile) {
            header('location:' . BASE_PATH . '/webRedirect.html?status=Success&type=soapnote');
            exit;
        } else {
            header('location:soapNoteList.html?rotationId=' . EncodeQueryData($rotationId) . '&status=' . $status);
            exit();
        }
    } else {
        if ($IsMobile) {
            header('location:' . BASE_PATH . '/webRedirect.html?status=Success&type=soapnote');
            exit;
        } else {
            header('location:soapNoteList.html?rotationId=' . EncodeQueryData($rotationId) . '&status=' . $status);
            exit();
        }
    }
} else {
    if ($IsMobile) {
        header('location:' . BASE_PATH . '/webRedirect.html?status=Error&type=soapnote');
        exit();
    } else {
        header('location:soapNoteList.html');
        exit();
    }
}
