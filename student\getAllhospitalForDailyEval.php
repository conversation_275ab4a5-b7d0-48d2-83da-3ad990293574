<?php
	// include('includes/validateUserLogin.php'); 
    include('../includes/config.php');
	include('../includes/commonfun.php');	
    include('../class/clsDB.php');   
	include('../class/clsHospitalSite.php');  	

if(!empty($_POST["rotationId"])) {
	$objDB = new clsDB();
		$query = '';
	$query ="SELECT hospitalsites.hospitalSiteId,hospitalsites.title 
				FROM hospitalsites 
				INNER JOIN rotation ON hospitalsites.hospitalSiteId=
													rotation.hospitalSiteId
				WHERE rotation.rotationId= '" . $_POST["rotationId"] . "'";
	// echo $query;exit;
	$rotation = $objDB->GetResultSet($query);
	$seletedHospitalSite = (isset($_POST["HospitalSite_Id"]) && $_POST["HospitalSite_Id"] > 0) ? $_POST["HospitalSite_Id"] : 0;

?>
	
<?php
	while($rows = mysqli_fetch_assoc($rotation))
    {
    	$seletedString = $seletedHospitalSite == $rows['hospitalSiteId'] ? " selected = 'selected' " : "";
		echo "<option value='{$rows['hospitalSiteId']}' {$seletedString} >{$rows["title"]}</option>";
	}
}
?>