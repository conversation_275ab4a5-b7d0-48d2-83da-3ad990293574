<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsMasteryEval.php');
include('../setRequest.php');

$schoolId = 0;

$schoolId = $currentSchoolId;
$title = "Add Mastery Evaluation Questions - " . $currenschoolDisplayname;
$page_title = "Add Mastery Evaluation Questions";
$schoolSectionId = 0;
$title = '';
$sortOrder = '';
$TopicTitleId = '';
$bedCrumTitle = 'Add';
$questionId = 0;
$sortOrder = '';
$isActiveCheckoff = $_SESSION['isActiveCheckoff'];
$questionText = '';
$sectionMasterId = 0;
$isPosition = 0;
$schoolMasteryEvaluationQuestionType = '';

if (isset($_GET['sectionMasterId'])) //Edit Mode
{
    $sectionMasterId = $_GET['sectionMasterId'];
    $sectionMasterId = DecodeQueryData($_GET['sectionMasterId']);
}
if (isset($_GET['schoolCSEvaluationQuestionId'])) //Edit Mode
{
    $schoolCSEvaluationQuestionId = $_GET['schoolCSEvaluationQuestionId'];
    $schoolCSEvaluationQuestionId = DecodeQueryData($_GET['schoolCSEvaluationQuestionId']);
}

if (isset($_GET['editid'])) //Edit Mode
{
    $questionId = DecodeQueryData($_GET['editid']);
    $page_title = "Edit Site Evaluation Questions";
    $bedCrumTitle = 'Edit';

    //For Checkoff Topic Details
    $objMasteryEval = new clsMasteryEval();

    if ($isCurrentSchoolSuperAdmin == 1) // For superadmin
    {
        $row = $objMasteryEval->GetDefaultMasteryEvaluationQuestionDetail($questionId);
        $rowsquistionoption = $objMasteryEval->GetDefaultMasteryevaluationQuestionOptionDetails($questionId);
        $schoolMasteryEvaluationQuestionType = $row['masteryQuestionType'];
    } else {
        $row = $objMasteryEval->GetMasteryEvaluationQuestionDetail($currentSchoolId, $questionId);
        $rowsquistionoption = $objMasteryEval->GetMasteryeevaluationQuestionOptionDetails($questionId);
        $schoolMasteryEvaluationQuestionType = $row['masteryQuestionType'];
    }

    unset($objMasteryEval);

    $questionText = stripslashes($row['optionText']);

    // $isPosition = $row['isPosition'];
    $sortOrder = isset($row['sortOrder']) ? stripslashes($row['sortOrder']) : '';
}

$optionDefaultArray = array("10" => "10", "8.5" => "8.5", "7" => "7", "0.5" => "0.5");

$loggedAsBackUserId = isset($_SESSION["loggedAsBackUserId"]) ? $_SESSION["loggedAsBackUserId"] : 0;
$loggedUserIsRolePrimary = isset($_SESSION["loggedUserIsRolePrimary"]) ? $_SESSION["loggedUserIsRolePrimary"] : 0;

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($page_title); ?></title>
    <?php include('includes/headercss.php'); ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <link rel="stylesheet"
        href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">

    <style>
        .select2-container {
            border-left: 3px solid red !important;
            border-radius: 12px !important;
        }
    </style>
</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="settings.html">Settings</a></li>
                    <li><a href="masteryEvaluationSectionList.html">Mastery Evaluation Section</a></li>
                    <li><a
                            href="masteryEvaluationQuestionList.html?sectionMasterId=<?php echo EncodeQueryData($sectionMasterId); ?>">Steps</a>
                    </li>
                    <li class="active"><?php echo ($bedCrumTitle); ?></li>
                </ol>
            </div>

        </div>
    </div>

    <div class="container">

        <form id="frmcheckoff" data-parsley-validate class="form-horizontal" method="POST"
            action="addmasteryEvaluationQuestionSubmit.html?editid=<?php echo (EncodeQueryData($questionId)); ?>&sectionMasterId=<?php echo (EncodeQueryData($sectionMasterId)); ?>">

            <div class="row">
                <div class="col-md-6">

                    <!-- Text input-->
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="cbotype">Question Type</label>
                        <div class="col-md-12 flex-direction-reverse">
                            <select id="cbotype" name="cbotype"
                                class="form-control input-md required-input select2_single input-md required-input"
                                required>
                                <option value="<?php echo ($schoolMasteryEvaluationQuestionType) ?> " selected>Select
                                </option>
                                <option <?php if ($schoolMasteryEvaluationQuestionType == 1) { ?> selected<?php } ?>
                                    value="1" name="questionType">Yes/No</option>
                                <option <?php if ($schoolMasteryEvaluationQuestionType == 2) { ?> selected <?php } ?>
                                    value="2" name="questionType">Long Answer</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtQuestion"> Question</label>
                        <div class="col-md-12">
                            <input id="txtQuestion" name="txtQuestion" value="<?php echo ($questionText); ?>"
                                type="text" class="form-control input-md required-input" required>

                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <?php if ($loggedAsBackUserId || $currentSchoolId != 1) { ?>
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="sortOrder"> Sort Order</label>
                            <div class="col-md-12">
                                <input id="sortOrder" name="sortOrder" value="<?php echo ($sortOrder); ?>" type="text"
                                    class="form-control input-md " <?php if (!$loggedAsBackUserId) {
                                        echo 'readonly';
                                    } ?>>
                            </div>
                        </div>
                    <?php } ?>
                </div>
                <!-- <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label mt-2" for="txtQuestion"> Option Position</label>
                        <div class="col-md-3">
                            <input id="horizontal" name="isPosition" style=" margin-top: 11px;" value="0" <?php echo ($isPosition == 0) ? "checked" : ""; ?> type="radio" class="input-md required-input ml-3" required> Horizontal
                        </div>
                        <div class="col-md-3">
                            <input id="vertical" name="isPosition" style=" margin-top: 11px;" value="1" <?php echo ($isPosition == 1) ? "checked" : ""; ?> type="radio" class=" input-md required-input" required> Vertical
                        </div>
                    </div>
                </div> -->
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group clsYesNo" style="display: none;">
                        <div class="form-group m-0">
                            <div class="col-md-12 control-label"> </div>
                            <div class="col-md-12">
                                <div class="row">
                                    <?php if ($questionId) {
                                        $optionDefaultArray = array();

                                        ?>
                                        <div class="col-md-12 textboxDiv p-0">
                                            <?php
                                            while ($row = mysqli_fetch_array($rowsquistionoption)) {
                                                // print_r($row);
                                                $optionText = $row['optionText'];
                                                $description = $row['description'];


                                                ?>
                                                <div class="singlechoiceboxdiv row m-0" style="margin-bottom: 10px;">
                                                    <input type="hidden" id="hid_anser" class="hid_anser" name="answers[]"
                                                        value="0">
                                                    <div class="col-md-6">
                                                        <label style=''>Answer</label>
                                                        <input type="text" id="txtsinglechoice"
                                                            value="<?php echo $optionText; ?>"
                                                            class="form-control input-md required-input choicebox"
                                                            name="txtsinglechoice[]" placeholder="Add an answer" required><br />
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label style=" ">Answer Description</label>
                                                        <div style="display: flex;align-items: center;">
                                                            <div style="width: 100%;">
                                                                <input type="text" id="txtsinglechoicemarks" style=""
                                                                value="<?php echo $description; ?>"
                                                                class="form-control input-md required-input choicebox"
                                                                name="txtsinglechoicemarks[]" placeholder="Answer Description" required>
                                                            </div>
                                                            <span id="Remove" style="color:red;margin-left: 10px;"
                                                                class="glyphicon glyphicon-trash Remove"></span>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php } ?>
                                        </div>
                                    <?php } else {

                                        ?>
                                        <div class="col-md-12 px-0 textboxDiv">
                                            <?php

                                            foreach ($optionDefaultArray as $key => $value) {
                                                $optionText = $value;
                                                $description = $key;

                                                ?>
                                                <div class="singlechoiceboxdiv row m-0">
                                                    <input type="hidden" id="hid_anser" class="hid_anser" name="answers[]"
                                                        value="0">
                                                    <div class="col-md-6">
                                                        <label style=''>Answer</label>
                                                        <input type="text" id="txtsinglechoice" style=''
                                                            value="<?php echo $optionText; ?>"
                                                            class="form-control input-md required-input choicebox"
                                                            name="txtsinglechoice[]" placeholder="Add an answer" required><br />
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label style=" ">Answer Description</label>
                                                        <div style="display: flex;align-items: center;">
                                                            <div style="width: 100%;">
                                                                <input type="text" id="txtsinglechoicemarks"
                                                                value="<?php //echo $description; ?>" style=""
                                                                class="form-control input-md required-input choicebox"
                                                                name="txtsinglechoicemarks[]" placeholder="Answer Description" required>
                                                            </div>
                                                            <span id="Remove"
                                                                style="color:red;margin-left: 10px;cursor: pointer;"
                                                                class="glyphicon glyphicon-trash Remove"></span><br />
                                                        </div>
                                                    </div>
                                                </div>
                                                <?php
                                            }
                                            ?>
                                        </div>
                                        <?php

                                    }
                                    ?>
                                    <div class="col-md-12"
                                        style="display: flex; justify-content: end; margin-top: 10px;">
                                        <button type="button" id="Add" class="btn btn-success">Add</button>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group clslongans" style="display: none;">
                        <div class="">
                            <label class="col-md-12 control-label" for="Answer"> Answer </label>
                            <div class="col-md-12">
                                <textarea id="longans" name="longans" rows="3" class="form-control input-md"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <!-- <label class="col-md-2 control-label"></label> -->
                <div class="col-md-12" style="display: flex; justify-content: center;margin: 20px 0;gap: 15px;">
                    <button id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
                    <a type="button"
                        href="masteryEvaluationQuestionList.html?sectionMasterId=<?php echo (EncodeQueryData($sectionMasterId)); ?>"
                        class="btn btn-default">Cancel</a>
                </div>
            </div>
        </form>

    </div>

    <?php include('includes/footer.php'); ?>

    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script type="text/javascript"
        src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
    <script type="text/javascript"
        src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="https://cdn.ckeditor.com/ckeditor5/35.0.1/classic/ckeditor.js"></script>
    <script type="text/javascript">
        $(".select2_tags").select2({
            'placeholder': 'Select'
        }); //for multiple selection

        $(window).load(function () {

            $(".select2_single").select2();


            $('#frmcheckoff').parsley().on('field:validated', function () {
                var ok = $('.parsley-error').length === 0;
            })
                .on('form:submit', function () {
                    ShowProgressAnimation();
                    return true;
                });

        });

        $(document).ready(function () {
            $("select").change(function () {
                // alert($(this).val());
                $('.clslongans').hide();
                $('.clsYesNo').hide();

                if ($(this).val() == 1){

                    $('.clsYesNo').show();
                }
                else if ($(this).val() == 2) {
                    $('.clslongans').show();

                }

            });

            $('#cbotype').trigger('change');

            $("#Add").on("click", function () {
                $(".textboxDiv").append("<div class='singlechoiceboxdiv row m-0'style='margin-bottom: 10px;'><div class='col-md-6'><label  style=''>Answer</label>	<input type='text' class='form-control input-md required-input choicebox' id='txtsinglechoice' style=''  name='txtsinglechoice[]' placeholder='Add an answer' required/><br></div><div class='col-md-6'><label style=''>Answer Description</label><div style='display: flex;align-items: center;'><div class='w-full'><input type='text' class='form-control input-md required-input choicebox' id='txtsinglechoicemarks'  style='' name='txtsinglechoicemarks[]' placeholder='Answer Description' required /><input type='hidden'  id='hid_anser' class='hid_anser' name='answers[]'  value='0'/></div><span id='Remove' style='color:red;margin-left: 10px;' class='glyphicon glyphicon-trash Remove'></span></div></div></div>");
            });

            // $(".Remove").on("click", function() {  

            $(document).on("click", ".Remove", function () {
                $(this).closest('.singlechoiceboxdiv').remove();
            });
        });
    </script>

</body>

</html>