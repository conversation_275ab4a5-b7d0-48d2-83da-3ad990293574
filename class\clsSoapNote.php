<?php
class clsSoapNote
{
	var $studentId = 0;
	var $schoolId = 0;
	var $rotationId = 0;
	var $clinicianId = 0;
	var $hospitalSiteId = 0;
	var $date = '';
	var $isSendToPreceptor = 0;
	var $patientDescription = '';
	var $cheifComplaint = '';
	var $admittingDiagnosis = '';
	var $hpi = '';
	var $pmh = '';
	var $medication = '';
	var $allergies = '';
	var $familyHistory = '';
	var $socialHistroy = '';
	var $ros = '';
	var $temperature = '';
	var $heartRate = '';
	var $bloodPressure = '';
	var $respiratoryRate = '';
	var $oxygenSaturation = '';
	var $physicalExam = '';
	var $laboratoryImaging = '';
	var $assessment = '';
	var $medicationTreatment = '';
	var $testOrder = '';
	var $referrals = '';
	var $patientEducationCounseling = '';
	var $followUp = '';
	var $createdBy = '';
	var $updatedBy = '';
	var $studentSignatureDate = '';
	var $clinicianSignatureDate = '';


	function SaveSoapNoteDetails($soapNoteId)
	{
		$objDB = new clsDB();
		$sql = '';
		$retSoapNote = 0;

		if ($soapNoteId > 0) {

			$sql = "UPDATE soapnote SET 
						 studentId = '" . addslashes($this->studentId) . "', 
						 schoolId = '" . addslashes($this->schoolId) . "', 
                         rotationId = '" . addslashes($this->rotationId) . "',
 						 clinicianId = '" . addslashes($this->clinicianId) . "',
 						 hospitalSiteId = '" . addslashes($this->hospitalSiteId) . "',
                         soapNoteDate = '" . addslashes($this->date) . "',
                         patientDescription = '" . addslashes($this->patientDescription) . "',
                         cheifComplaint= '" . addslashes($this->cheifComplaint) . "',
                         admittingDiagnosis= '" . addslashes($this->admittingDiagnosis) . "',
                         hpi = '" . addslashes($this->hpi) . "',
                         pmh= '" . addslashes($this->pmh) . "',
                         medication= '" . addslashes($this->medication) . "',
                         allergies = '" . addslashes($this->allergies) . "',
                         familyHistory = '" . addslashes($this->familyHistory) . "',
                         socialHistroy = '" . addslashes($this->socialHistroy) . "',
                         ros = '" . addslashes($this->ros) . "',
                         temperature = '" . addslashes($this->temperature) . "',
                         heartRate = '" . addslashes($this->heartRate) . "',
                         bloodPressure = '" . addslashes($this->bloodPressure) . "',
                         respiratoryRate = '" . addslashes($this->respiratoryRate) . "',
                         oxygenSaturation = '" . addslashes($this->oxygenSaturation) . "',
                         physicalExam = '" . addslashes($this->physicalExam) . "',
                         laboratoryImaging = '" . addslashes($this->laboratoryImaging) . "',
                         assessment = '" . addslashes($this->assessment) . "',
                         medicationTreatment = '" . addslashes($this->medicationTreatment) . "',
                         testOrder = '" . addslashes($this->testOrder) . "',
                         referrals = '" . addslashes($this->referrals) . "',
                         patientEducationCounseling = '" . addslashes($this->patientEducationCounseling) . "',
                         followUp = '" . addslashes($this->followUp) . "',
						 studentSignatureDate = '" . addslashes($this->studentSignatureDate) . "',
						 clinicianSignatureDate = '" . addslashes($this->clinicianSignatureDate) . "',
						 updatedBy = '" . addslashes($this->createdBy) . "',
						 updatedDate = '" . (date("Y-m-d h:i:s")) . "'
						 Where soapNoteId= " . $soapNoteId;
			// echo 'update->'.$sql;
			// exit;
			$objDB->ExecuteQuery($sql);
			$retSoapNote = $soapNoteId;
		} else {

			$sql = "INSERT INTO soapnote (studentId, schoolId, clinicianId, rotationId, hospitalSiteId, soapNoteDate, isSendToPreceptor, patientDescription, cheifComplaint, admittingDiagnosis, hpi, pmh, medication, allergies, familyHistory, socialHistroy, ros, temperature, heartRate, bloodPressure, respiratoryRate, oxygenSaturation, physicalExam, laboratoryImaging, assessment, medicationTreatment, testOrder, referrals, patientEducationCounseling, followUp, studentSignatureDate, clinicianSignatureDate, createdBy, createdDate) 
					VALUES ('" . addslashes($this->studentId) . "',
							'" . addslashes($this->schoolId) . "',
							'" . addslashes($this->clinicianId) . "',
							'" . addslashes($this->rotationId) . "',
							'" . addslashes($this->hospitalSiteId) . "',
							'" . addslashes($this->date) . "',
							'" . addslashes($this->isSendToPreceptor) . "',
							'" . addslashes($this->patientDescription) . "',
							'" . addslashes($this->cheifComplaint) . "',
							'" . addslashes($this->admittingDiagnosis) . "',
							'" . addslashes($this->hpi) . "',
							'" . addslashes($this->pmh) . "',
							'" . addslashes($this->medication) . "',
							'" . addslashes($this->allergies) . "',
                            '" . addslashes($this->familyHistory) . "',
                            '" . addslashes($this->socialHistroy) . "',
                            '" . addslashes($this->ros) . "',
                            '" . addslashes($this->temperature) . "',
                            '" . addslashes($this->heartRate) . "',
                            '" . addslashes($this->bloodPressure) . "',
                            '" . addslashes($this->respiratoryRate) . "',
                            '" . addslashes($this->oxygenSaturation) . "',
                            '" . addslashes($this->physicalExam) . "',
                            '" . addslashes($this->laboratoryImaging) . "',
                            '" . addslashes($this->assessment) . "',
                            '" . addslashes($this->medicationTreatment) . "',
                            '" . addslashes($this->testOrder) . "',
                            '" . addslashes($this->referrals) . "',
                            '" . addslashes($this->patientEducationCounseling) . "',
                            '" . addslashes($this->followUp) . "',
                            '" . addslashes($this->studentSignatureDate) . "',
                            '" . addslashes($this->clinicianSignatureDate) . "',
							'" . addslashes($this->createdBy) . "',
							'" . (date("Y-m-d h:i:s")) . "'
													
							)";
			// echo 'Insert->'.$sql;exit;
			$retSoapNote = $objDB->ExecuteInsertQuery($sql);
		}

		unset($objDB);
		return $retSoapNote;
	}

	function GetAllSoapNote($SchoolId, $studentId, $rotationId, $clinicianId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT soapnote.*,
				rotation.title AS rotationName, preceptordetails.preceptorId, preceptordetails.signatureDate, preceptordetails.status, CONCAT( clinician.firstName, ' ' ,clinician.lastName) AS clinicianName,
				CONCAT( student.firstName, ' ' ,student.lastName) AS studentName
				FROM soapnote
				INNER JOIN rotation ON soapnote.rotationId=rotation.rotationId
				LEFT JOIN preceptordetails ON preceptordetails.referenceId = soapnote.soapNoteId AND preceptordetails.type='soapnote'
				LEFT JOIN clinician ON clinician.clinicianId = soapnote.clinicianId
				INNER JOIN student ON student.studentId = soapnote.studentId
				WHERE soapnote.schoolId=" . $SchoolId . " AND soapnote.isDelete = 0";

		if ($studentId) {
			$sql	.= " AND soapnote.studentId = " . $studentId;
		}

		if ($clinicianId) {
			$sql	.= " AND soapnote.clinicianId = " . $clinicianId;
		}

		if ($rotationId) {
			$sql	.= " AND soapnote.rotationId = " . $rotationId;
		}
		$sql .= " GROUP BY soapnote.soapNoteId";
		$sql .= " ORDER BY soapnote.soapNoteDate DESC";
		// echo $sql;exit;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllSoapNoteForClinician($SchoolId, $studentId, $clinicianId, $rotationId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
			soapnote.*, 
			rotation.parentRotationId,
			rotation.locationId as rotationLocationId,
			courses.locationId,
			rotation.title AS rotationName, 
			preceptordetails.preceptorId, 
			preceptordetails.signatureDate, 
			preceptordetails.status, 
			CONCAT(clinician.firstName, ' ', clinician.lastName) AS clinicianName,
			CONCAT(student.firstName, ' ', student.lastName) AS studentName
		FROM soapnote
		LEFT JOIN rotation ON soapnote.rotationId = rotation.rotationId
		LEFT JOIN courses ON rotation.courseId = courses.courseId
		LEFT JOIN preceptordetails ON preceptordetails.referenceId = soapnote.soapNoteId AND preceptordetails.type = 'soapnote'
		LEFT JOIN clinician ON clinician.clinicianId = soapnote.clinicianId
		LEFT JOIN student ON student.studentId = soapnote.studentId
		WHERE soapnote.schoolId = $SchoolId AND soapnote.isDelete = 0";

		if ($studentId) {
			$sql .= " AND soapnote.studentId = $studentId";
		}

		if ($clinicianId) {
			$sql .= " AND soapnote.clinicianId = $clinicianId";
		}

		if ($rotationId) {
			$sql .= " AND soapnote.rotationId = $rotationId";
		}

		$sql .= " AND (
	CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate)
	OR (
		rotation.parentRotationId > 0
		AND rotation.isSchedule = 1
		AND EXISTS (
			SELECT 1 
			FROM rotation AS parent 
			WHERE parent.rotationId = rotation.parentRotationId 
			AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
		)
	)
)";

		$sql .= " GROUP BY soapnote.soapNoteId";
		$sql .= " ORDER BY soapnote.soapNoteDate DESC";

		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllSoapNoteDetails($soapNoteId, $schoolId)
	{
		$objDB = new clsDB();

		$sql = "SELECT 
                soapnote.*, 
                rotation.title AS rotationName, 
                preceptordetails.preceptorId, 
                preceptordetails.signatureDate, 
                preceptordetails.status
            FROM soapnote
            LEFT JOIN rotation ON soapnote.rotationId = rotation.rotationId
            LEFT JOIN preceptordetails ON soapnote.soapnoteId = preceptordetails.referenceId 
                AND preceptordetails.type = 'soapnote' 
            LEFT JOIN student ON soapnote.studentId = student.studentId
            WHERE soapnote.soapNoteId=" . $soapNoteId . " AND soapnote.isDelete = 0";


		if ($schoolId) {
			$sql	.= " AND soapnote.schoolId = " . $schoolId;
		}

		// echo $sql;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}

	function UpdateSoapNoteByPreceptor($referenceId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "UPDATE preceptordetails SET												 
					status='1',
					signatureDate = '" . addslashes($this->clinicianSignatureDate) . "', 
					updatedDate = '" . (date("Y-m-d h:i:s")) . "'";


		$sql .= " Where referenceId= " . $referenceId;
		// echo $sql;
		// exit;
		$referenceId = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $referenceId;
	}

	function UpdateSoapNoteByClinician($soapNoteId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "UPDATE soapnote SET
					clinicianSignatureDate = '" . addslashes($this->clinicianSignatureDate) . "', 
					 
					updatedBy = '" . ($this->updatedBy) . "', 
					updatedDate = '" . (date("Y-m-d h:i:s")) . "'";

		$sql .= " Where soapNoteId= " . $soapNoteId;
		// echo $sql;
		// exit;
		$retSoapNoteId = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $retSoapNoteId ? $soapNoteId : 0;
	}

	function UpdateSoapNoteByAdmin($soapNoteId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "UPDATE soapnote SET
					studentSignatureDate = '" . addslashes($this->studentSignatureDate) . "', 
				
					updatedBy = '" . ($this->updatedBy) . "', 
					updatedDate = '" . (date("Y-m-d h:i:s")) . "'";

		$sql .= " Where soapNoteId= " . $soapNoteId;
		// echo $sql;
		// exit;
		$retSoapNoteId = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $retSoapNoteId ? $soapNoteId : 0;
	}

	function GetAllPhysicians()
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM physicianinteraction";

		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllSoapNoteForReport($schoolId, $rotationId, $subrotationId, $studentId, $clinicianId, $startDate, $endDate, $ascdesc = '', $sortorder)
	{
		// echo $sortorder;
		$objDB = new clsDB();
		$subrotationId = str_replace(" ", ",", $subrotationId);
		$studentIdCount = ($studentId != '') ? count($studentId) : 0;
		$studentIds =  ($studentIdCount > 0) ? implode(',', $studentId) : '';

		$sql = "SELECT soapnote.*,rotation.parentRotationId,rotation.locationId as rotationLocationId,
		rotation.title AS rotationName, preceptordetails.preceptorId, preceptordetails.signatureDate, 
                preceptordetails.status, CONCAT( clinician.firstName, ' ' ,clinician.lastName) AS clinicianName,
		CONCAT( student.firstName, ' ' ,student.lastName) AS studentName, preceptordetails.preceptorId
		FROM soapnote
		LEFT JOIN rotation ON soapnote.rotationId=rotation.rotationId
		LEFT JOIN preceptordetails ON preceptordetails.referenceId = soapnote.soapNoteId  AND preceptordetails.type = 'soapnote'
		LEFT JOIN clinician ON clinician.clinicianId = soapnote.clinicianId
		LEFT JOIN student ON student.studentId = soapnote.studentId
		LEFT JOIN hospitalsites ON hospitalsites.hospitalSiteId=rotation.hospitalSiteId
        WHERE soapnote.schoolId = " . $schoolId . " AND soapnote.isDelete = 0";

		if ($rotationId > 0 && $subrotationId > 0)
			$sql .= " AND (rotation.rotationId=" . $rotationId . " OR rotation.rotationId IN ($subrotationId))";
		elseif ($rotationId > 0)
			$sql .= " AND rotation.rotationId=" . $rotationId;
		elseif ($subrotationId > 0)
			$sql .= " AND rotation.rotationId IN ($subrotationId)";

		if ($studentIdCount > 0)
			$sql .= " AND student.studentId IN ($studentIds)";

		if ($clinicianId > 0)
			$sql .= " AND clinician.clinicianId=" . $clinicianId;

		if ($startDate > 0 || $endDate > 0)
			$sql .= " AND soapnote.soapNoteDate >= '" . $startDate . "' and soapnote.soapNoteDate <= '" . $endDate . "'";


		if ($ascdesc && $sortorder == 1)
			$sql .= "  ORDER BY student.firstName " . $ascdesc;

		else if ($ascdesc && $sortorder == 2)
			$sql .= "  ORDER BY rankmaster.title " . $ascdesc;
		else if ($ascdesc && $sortorder == 5) //rotation
			$sql .= "  ORDER BY rotation.title " . $ascdesc;

		else if ($ascdesc && $sortorder == 10)  //hospital site
			$sql .= "  ORDER BY rotation.`title` " . $ascdesc;

		else if ($ascdesc && $sortorder == 14)
			$sql .= "  ORDER BY student.firstName " . $ascdesc;

		else if ($ascdesc && $sortorder == 15)
			$sql .= "  ORDER BY student.lastName " . $ascdesc;

		else if ($ascdesc && $sortorder == 7)
			$sql .= "  ORDER BY clinician.firstName " . $ascdesc;

		else if ($ascdesc && $sortorder == 8)
			$sql .= "  ORDER BY hospitalsites.title " . $ascdesc;


		else
			$sql .= " ORDER BY `soapnote`.`soapNoteDate`" . $ascdesc;
		// echo $sql;
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}
	function GetAllSoapNoteForApp($SchoolId, $studentId = 0, $clinicianId = 0, $rotationId = 0, $userType = 0, $limitstring = '', $searchText = '')
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT soapnote.*,rotation.parentRotationId,rotation.locationId as rotationLocationId,courses.locationId,
				rotation.rotationId,rotation.title AS rotationName, preceptordetails.preceptorId, preceptordetails.signatureDate, 
                preceptordetails.status, CONCAT( clinician.firstName, ' ' ,clinician.lastName) AS clinicianName,
				CONCAT( student.firstName, ' ' ,student.lastName) AS studentName
				FROM soapnote
				LEFT JOIN rotation ON soapnote.rotationId=rotation.rotationId
				LEFT JOIN courses ON rotation.courseId=courses.courseId
				LEFT JOIN preceptordetails ON preceptordetails.referenceId = soapnote.soapNoteId AND preceptordetails.type = 'soapnote'
				LEFT JOIN clinician ON clinician.clinicianId = soapnote.clinicianId
				LEFT JOIN student ON student.studentId = soapnote.studentId
				LEFT JOIN extenal_preceptors ON extenal_preceptors.id = preceptordetails.preceptorId
				WHERE soapnote.schoolId=" . $SchoolId . " AND soapnote.isDelete = 0";

		if ($studentId) {
			$sql	.= " AND soapnote.studentId = " . $studentId;
		}

		if ($clinicianId) {
			$sql	.= " AND soapnote.clinicianId = " . $clinicianId;
		}

		if ($rotationId) {
			$sql	.= " AND soapnote.rotationId = " . $rotationId;
		}
		if ($searchText != "") {
			if ($userType) {
				$sql .= " AND (rotation.title LIKE '%" . $searchText . "%') ";
			} else {

				$sql .= " AND (CONCAT(clinician.firstName, ' ', clinician.lastName) LIKE '%" . $searchText . "%' OR CONCAT(extenal_preceptors.firstName, ' ', extenal_preceptors.lastName) LIKE '%" . $searchText . "%' OR rotation.title LIKE '%" . $searchText . "%') ";
			}
		}

		$sql .= " GROUP BY soapnote.soapNoteId";
		$sql .= " ORDER BY soapnote.soapNoteDate DESC" . $limitstring;
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}


	/**
	 * Retrieves detailed information for a specific soap note log.
	 *
	 * This function fetches detailed information for an soap note log entry
	 * by its activity ID, optionally filtered by school ID. It returns data such as
	 * the rotation name, user information, clinician name, hospital site name, 
	 * school name, and preceptor name associated with the soap note.
	 *
	 * @param int $soapNoteId The ID of the soap note to retrieve details for.
	 * @param int $schoolId Optional. The school ID for filtering the results. Default is 0.
	 *
	 * @return array An associative array with the soap note log details.
	 */

	function GetAllSoapNoteDetailsForLogs($soapNoteId, $schoolId = 0)
	{
		$objDB = new clsDB();

		$sql = "SELECT soapnote.*,rotation.title as rotationName,soapnote.studentId as userId, CONCAT(student.firstName, ' ', student.lastName) AS userName, preceptordetails.preceptorId, preceptordetails.signatureDate, 
                preceptordetails.status,CONCAT(clinician.firstName, ' ', clinician.lastName) AS clinicianName,hospitalsites.title AS hospitalSiteName, schools.displayName as schoolName,CONCAT(extenal_preceptors.firstName, ' ', extenal_preceptors.lastName) AS preceptorName FROM `soapnote` 
		INNER JOIN rotation ON soapnote.`rotationId` = rotation.`rotationId` 
		INNER JOIN student ON student.studentId=soapnote.studentId
        INNER JOIN hospitalsites ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
        INNER JOIN schools ON schools.schoolId = soapnote.schoolId
		LEFT JOIN clinician ON soapnote.clinicianId = clinician.clinicianId
		LEFT JOIN preceptordetails ON soapnote.soapNoteId = preceptordetails.referenceId AND preceptordetails.type = 'soapnote'
		LEFT JOIN extenal_preceptors ON extenal_preceptors.id = preceptordetails.preceptorId
		WHERE soapNoteId =" . $soapNoteId;

		if ($schoolId) {
			$sql .= " AND soapnote.schoolId=" . $schoolId;
		}
		// echo $sql;
		// exit;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}


	/**
	 * Creates a log entry for an soap note action
	 *
	 * @param int $id ID of the soap note
	 * @param string $action Action type (Add, Edit, Delete, Signoff)
	 * @param int $userId User ID of the user performing the action
	 * @param string $userType Type of user (e.g. student, clinician, etc.)
	 *
	 * @return array An array containing the log data, the original soap note data, and any additional data
	 */
	function createSoapNoteLog($id, $action, $userId, $userType)
	{
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$objSoapNote = new clsSoapNote(); // Assuming `Attendance` class is used for `prepareAttendanceLogData`

		// Get user details based on user type
		$userDetails = getUserDetails($userId, $userType);


		$rowData = $objSoapNote->GetAllSoapNoteDetailsForLogs($id);

		if ($rowData != '')
			$logData = $objLog->generateLogData($rowData, $userType);

		$additionalData = '';

		// Populate log data with user details
		$logData['userId'] = $userDetails['userId'];
		$logData['userName'] = $userDetails['userName'];
		$logMessage = '';

		if ($action == 'Add') {
			$logMessage = $logData['userName'] . ' Added Soap Note from ' . $logData['rotationName'] . ' Rotation.';
		} else if ($action == 'Edit') {
			$logMessage = $logData['userName'] . ' Updated Soap Note from ' . $logData['rotationName'] . ' Rotation.';
		} else if ($action == 'Delete' && $userType != 'Student') {
			$logMessage = $logData['userName'] . ' deleted SOAP Note of ' . $rowData['userName'] . ' Student.';
		} else if ($action == 'Signoff' && $userType == 'Preceptor') {
			$logMessage = $logData['userName'] . ' Signoff Soap Note from ' . $logData['rotationName'] . ' Rotation.';
		} else if ($action == 'Signoff' && $userType == 'Student') {
			$logMessage = $logData['userName'] . ' Signoff Soap Note from ' . $logData['rotationName'] . ' Rotation.';
		} else if ($action == 'Signoff' && $userType == 'Clinician') {
			$logMessage = $logData['userName'] . ' Signoff Soap Note from ' . $logData['rotationName'] . ' Rotation.';
		}

		// Construct log message
		$logData['message'] = $logMessage;

		// Return the data
		return [$logData, $rowData, $additionalData];
	}

	/**
	 * Saves soap note audit log
	 *
	 * @param int $id Activity sheet ID
	 * @param int $userId User ID that is performing the action
	 * @param string $userType User type (Student, Preceptor, Admin)
	 * @param string $action Action type (Add, Edit, Delete, Signoff)
	 * @param int $isMobile Whether the action was performed on a mobile device (0 or 1)
	 *
	 * @return bool Returns true if the log was successfully saved
	 */
	function saveSoapNoteAuditLog($id, $userId, $userType, $action, $isMobile = 0)
	{
		// Instantiate the Logger and Checkoff classes
		$objLog = new clsLogger();
		$objSoapNote = new clsSoapNote();

		// Prepare log data
		[$logData, $rowData, $additionalData] = $objSoapNote->createSoapNoteLog($id, $action, $userId, $userType);
		$logData['isMobile'] = $isMobile;

		if ($action == 'Delete') {
			$objSoapNote->DeleteSoapNote($id);
		}
		// Save the log details
		$objLog->saveLogs($logData, $action, $id, 'Soap Note', $rowData, $additionalData);

		// Clean up
		unset($objLog);
		unset($objSoapNote);

		return true; // Return success or handle further actions as needed
	}

	function DeleteSoapNote($soapNoteId)
	{
		$objDB = new clsDB();
		$result = "";

		if ($soapNoteId > 0) {
			$objDB = new clsDB();
			$sql = " UPDATE soapnote 
					SET isDelete = 1, deletedDate ='" . (date("Y-m-d")) . "' WHERE soapNoteId = " . $soapNoteId;
			// echo $sql;
			// exit;
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
		return $result;
	}
}
