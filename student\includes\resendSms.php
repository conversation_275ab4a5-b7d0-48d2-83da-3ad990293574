<!-- Modal -->
<link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/student/assets/Student.css">

<style>
	.form-horizontal .form-group {
		margin-left: 0 !important;
		margin-right: 0 !important;
	}

	.button {
		border: 1px solid #5cb85c;
		background-color: #5cb85c;
		color: #fff;
		cursor: pointer;
		padding: 10px 30px;
		border-radius: 10px;
	}
	.flex-end{
		display: flex;
		justify-content: end;
	}
	.pt-0{
		padding-top: 0;
	}
	.mfp-close{
		display: none !important;
	}
    .required-input {
    	border-left: 3px solid red !important;
	}
</style>

    <div class="modal fade" id="resendModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="title">Resend Evaluation</h4>
                </div>
                <div class="modal-body">
                    <div class="modal-body pt-0">

                        <form id="resendForm" data-parsley-validate class="form-horizontal" method="POST" >

                            <div class="form-group isPreceptorDiv">
                                <table id="preceptorTable" width="100%">
                                    <tr>
                                        <td>
                                            <label for="" class="control-label"> Preceptor Phone Number</label>
                                            <input type="hidden" id="preceptorId" name="preceptorId" value="0">
                                            <input type="hidden" id="rotationId" name="rotationId" value="0">
                                            <input type="hidden" id="evaluationId" name="evaluationId" value="0">
                                            <input type="hidden" id="evaluationType" name="evaluationType" value="">
                                            <input type="hidden" id="checkoffId" name="checkoffId" value="0">
                                            <input type="hidden" id="schoolTopicId" name="schoolTopicId" value="0">
                                            <input type="hidden" id="activityId" name="activityId" value="0">
                                            <input type="hidden" id="soapNoteId" name="soapNoteId" value="0">
                                            <input type="text" name="preceptorNum" id="preceptorNum" minlength="12" maxlength="12" data-inputmask-alias="************" class=" form-control margin_right_five validateOnlynumbers" placeholder="Enter Preceptor Phone Number" required />
                                        </td>
                                    </tr>
                                </table>
                                <p>
                                    <!-- <input class="margin_top_ten btn btn-primary" type="button" value="Insert row"> -->
                                </p>
                            </div>
                            <div class="flex-end">
                            <button id="btnSendSms" name="btnSend" type="button" class="btn button btnSendSms">Send</button>
                            </div>
                        </form>

                    </div>

                    <!-- <div class="modal-footer">

                    </div> -->
                    <!-- /.modal-content -->
                </div><!-- /.modal-dialog -->
            </div>
        </div>
    </div>

    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/jquery.min.js" type="text/javascript"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/inputmask/jquery.inputmask.bundle.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/inputmask/inputmask.js"></script>