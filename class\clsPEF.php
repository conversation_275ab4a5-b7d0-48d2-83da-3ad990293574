<?php
class clsPEF
{
	var $clinicianId = 0;
	var $studentId = 0;
	var $schoolId = 0;
	var $isType = 0;
	var $rotationId = 0;
	var $createdBy = '';
	var $createdDate = '';
	var $updatedBy = '';
	var $updatedDate = '';
	var $optionText = '';
	var $sectionMasterId = 0;
	var $evaluationDate = '';
	var $isStatus = '';
	var $score = '';
	var $totalPoints = '';
	var $isPEFType = '';
	var $sortOrder = '';
	var $title = '';


	function SaveAdminPEF($studentPEFMasterId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($studentPEFMasterId > 0) {

			$sql = "UPDATE studentpefmaster SET 						
						 clinicianId = '" . addslashes($this->clinicianId) . "', 						  
						 rotationId = '" . addslashes($this->rotationId) . "',
						 evaluationDate='" . addslashes($this->evaluationDate) . "',						 
						 score='" . addslashes($this->score) . "',						 
						 isStatus='" . addslashes($this->isStatus) . "',						 
						 totalPoints='" . addslashes($this->totalPoints) . "',						 
						 updatedBy = '" . addslashes($this->createdBy) . "',
						 updatedDate = '" . (date("Y-m-d h:i:s")) . "' 
						 Where studentPEFMasterId= " . $studentPEFMasterId;
			//  echo 'update->'.$sql;exit;
			$objDB->ExecuteQuery($sql);
		} else {
			$sql = "INSERT INTO studentpefmaster (schoolId,clinicianId, studentId, rotationId,evaluationDate,score,totalPoints,isStatus,isType,
								createdBy, createdDate) 
				VALUES ('" . addslashes($this->schoolId) . "',
						'" . addslashes($this->clinicianId) . "',
						'" . addslashes($this->studentId) . "',
						'" . addslashes($this->rotationId) . "',
						'" . addslashes($this->evaluationDate) . "',						 
						'" . addslashes($this->score) . "',						 
						'" . addslashes($this->totalPoints) . "',						 
						'" . addslashes($this->isStatus) . "',
						'" . addslashes($this->isType) . "',
						'" . addslashes($this->createdBy) . "',
						'" . (date("Y-m-d h:i:s")) . "'						
						)";
			//  echo 'insert->'.$sql;exit;

			$studentPEFMasterId = $objDB->ExecuteInsertQuery($sql);
		}

		// echo '<hr>Insert->'.$sql;exit;
		unset($objDB);
		return $studentPEFMasterId;
	}

	function SaveStudentPEF($studentPEFMasterId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($studentPEFMasterId > 0) {
			// echo $score;exit;
			$sql = "UPDATE studentpefmaster SET 												  
						dateOfStudentSignature = '" . addslashes($this->dateOfStudentSignature) . "',
						studentComment = '" . addslashes($this->studentComment) . "',
						studentsigniture='" . addslashes($this->studentsigniture) . "',						 
						score='" . addslashes($this->score) . "',						 
						totalPoints='" . addslashes($this->totalPoints) . "' 
						Where studentPEFMasterId= " . $studentPEFMasterId;
			// echo 'update->'.$sql;exit;
			$objDB->ExecuteQuery($sql);
		}

		// echo '<hr>Insert->'.$sql;exit;
		unset($objDB);
		return $studentPEFMasterId;
	}

	function SavePefEvaluationSection($sectionId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($sectionId > 0) {

			$sql = "UPDATE schoolpefevalsectionmaster SET 						
						title = '" . addslashes($this->title) . "',						
						sortOrder = '" . addslashes($this->sortOrder) . "',
						isType = '" . addslashes($this->isPEFType) . "'
						Where sectionMasterId= " . $sectionId;
			//echo 'update->'.$sql;exit;
			$objDB->ExecuteQuery($sql);
		} else {
			$sql = "INSERT INTO schoolpefevalsectionmaster (title,sortOrder,isType) 
				VALUES (
						'" . addslashes($this->title) . "',
						'" . addslashes($this->sortOrder) . "',
						'" . addslashes($this->isPEFType) . "'						
						)";

			$sectionId = $objDB->ExecuteInsertQuery($sql);
		}

		unset($objDB);
		return $sectionId;
	}

	function SavePefEvaluationQuestion($questionId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($questionId > 0) {
			$sql = "UPDATE schoolpefevalmaster SET  
                optionText = '" . addslashes($this->optionText) . "',
                pefQuestionType = '" . addslashes($this->pefQuestionType) . "',
                sectionMasterId = '" . addslashes($this->sectionMasterId) . "',
                isPosition = '" . addslashes($this->isPosition) . "',
                sortOrder = '" . addslashes($this->sortOrder) . "',
                isType  = '" . addslashes($this->isPEFType) . "',
                description  = '" . addslashes($this->description) . "'
            WHERE pefQuestionId = " . $questionId;

			$objDB->ExecuteQuery($sql);
		} else {
			$sql = "INSERT INTO schoolpefevalmaster (optionText,pefQuestionType,sectionMasterId,sortOrder,isPosition,isType,description) 
				VALUES ('" . addslashes($this->optionText) . "',
						'" . addslashes($this->pefQuestionType) . "',					
						'" . addslashes($this->sectionMasterId) . "',
						'" . addslashes($this->sortOrder) . "',
						'" . addslashes($this->isPosition) . "',
						'" . addslashes($this->isPEFType) . "',
						'" . addslashes($this->description) . "'	
						)";

			$questionId = $objDB->ExecuteInsertQuery($sql);
			// echo $sql;
			// exit;
		}

		unset($objDB);
		return $questionId;
	}

	function SavePefEvaluationQuestionOptions()
	{
		$objDB = new clsDB();
		$sql = '';
		$sql = "INSERT INTO schoolpefevaldetails(pefQuestionId,optionText,pefOptionValue) 
					VALUES ('" . addslashes($this->pefQuestionId) . "',
							'" . addslashes($this->optionText) . "',					
							'" . addslashes($this->pefOptionValue) . "'					
							)";
		$objDB->ExecuteInsertQuery($sql);
		unset($objDB);
	}

	function GetPefQuestionCountbySections($sectionMasterId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT count(sectionMasterId) as count
					FROM schoolpefevalmaster 
					WHERE sectionMasterId=" . $sectionMasterId;
		$rows = $objDB->GetSingleFieldValue($sql);
		return $rows;
		unset($objDB);
	}


	function DeleteStudentPEFDetails($studentPEFMasterId)
	{
		$objDB = new clsDB();
		$sql = "DELETE  FROM studentpefevaldetails WHERE studentPEFMasterId=" . $studentPEFMasterId;
		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}



	function SaveStudentPEFDetail()
	{
		$objDB = new clsDB();

		$sql = "INSERT INTO studentpefevaldetails (studentPEFMasterId,schoolQuestionId,
						schoolOptionvalue,schoolOptionAnswerText) 
				VALUES ('" . ($this->studentPEFMasterId) . "',
						'" . ($this->schoolQuestionId) . "',
						'" . ($this->schoolOptionvalue) . "',
						'" . addslashes($this->schoolOptionAnswerText) . "'							 
					)";
		//echo 'INSERT->'.$sql;exit;
		$studentPEFDetailId = $objDB->ExecuteInsertQuery($sql);
		unset($objDB);
		return $studentPEFDetailId;
	}

	function GetAllPEF($currentSchoolId = 0, $rotationId = 0, $studentId = 0, $isType = '', $startDate = '', $endDate = '', $ascdesc = '', $sordorder = 0)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT studentpefmaster.* ,schools.schoolId, student.firstName as studentfirstName, 
		student.lastName as studentlastName, student.studentId, rotation.title as rotationName, studentpefmaster.score
		from studentpefmaster
		inner join schools on schools.schoolId = studentpefmaster.schoolId
		inner join rotation on rotation.rotationId = studentpefmaster.rotationId
		inner join student on student.studentId =  studentpefmaster.studentId
		WHERE studentpefmaster.schoolId=" . $currentSchoolId;

		if ($rotationId > 0)
			$sql .= " AND studentpefmaster.rotationId=" . $rotationId;

		if ($studentId > 0)
			$sql .= " AND studentpefmaster.studentId=" . $studentId;

		if ($isType == 'pef1')
			$sql .= " AND studentpefmaster.isType=0";

		elseif ($isType == 'pef2')
			$sql .= " AND studentpefmaster.isType=1";

		if ($startDate != '')
			$sql .= " AND date(studentpefmaster.evaluationDate) >= '" . $startDate . "'";

		if ($endDate != '')
			$sql .= " AND date(studentpefmaster.evaluationDate) <= '" . $endDate . "' ";

		if ($ascdesc && $sordorder == 14)
			$sql .= "  ORDER BY student.firstName " . $ascdesc;

		else if ($ascdesc && $sordorder == 15)
			$sql .= "  ORDER BY student.lastName " . $ascdesc;
		else
			$sql .= " ORDER BY studentpefmaster.evaluationDate desc ";

		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetStudentPEFDetails($studentPEFMasterId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT studentpefmaster.*,studentpefevaldetails.*,rotation.parentRotationId,rotation.locationId as rotationLocationId,courses.locationId
				FROM  studentpefmaster 
				LEFT JOIN studentpefevaldetails ON studentpefevaldetails.studentPEFMasterId= 
				studentpefmaster.`studentPEFMasterId`
				LEFT JOIN rotation ON studentpefmaster.rotationId=rotation.rotationId
				LEFT JOIN courses ON rotation.courseId=courses.courseId
				WHERE studentpefmaster.studentPEFMasterId=" . $studentPEFMasterId;
		// echo $sql;exit;
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}

	function GetSections($isType = '')
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT schoolpefevalsectionmaster.* FROM schoolpefevalsectionmaster WHERE isActive = 1";

		if ($isType == 'pef1')
			$sql .= " AND schoolpefevalsectionmaster.isType=0";
		elseif ($isType == 'pef2')
			$sql .= " AND schoolpefevalsectionmaster.isType=1";
			
        $sql .= " ORDER BY schoolpefevalsectionmaster.sortOrder,title ASC";
		// echo $sql;	exit;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}
	function GetSectionForSettings($isType = '')
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT schoolpefevalsectionmaster.* FROM schoolpefevalsectionmaster";

		if ($isType == 'pef1')
			$sql .= " WHERE schoolpefevalsectionmaster.isType=0";
		elseif ($isType == 'pef2')
			$sql .= " WHERE schoolpefevalsectionmaster.isType=1";

	   $sql .= " ORDER BY schoolpefevalsectionmaster.sortOrder,title ASC";
		// echo $sql;	exit;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}


	function DeletePefEvaluationSection($sectionMasterId)
	{
		$objDB = new clsDB();
		$sql = "DELETE FROM schoolpefevalsectionmaster WHERE sectionMasterId=" . $sectionMasterId;
		$result = $objDB->ExecuteQuery($sql);
		// echo $sql; exit;
		unset($objDB);
		return $result;
	}

	function DeletePefEvaluationOptions($questionId)
	{
		$objDB = new clsDB();
		$sql = "DELETE  FROM schoolpefevaldetails WHERE pefQuestionId=" . $questionId;
		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}

	function DeletePefQuestion($questionId)
	{
		$objDB = new clsDB();
		$sql = "DELETE m, d
            FROM schoolpefevalmaster m
            LEFT JOIN schoolpefevaldetails d ON m.pefQuestionId = d.pefQuestionId
            WHERE m.pefQuestionId =" . $questionId;

		$result = $objDB->ExecuteQuery($sql);
		//    echo $sql; exit;
		unset($objDB);

		return $result;
	}
	function SetPEFEvaluationStatus($sectionMasterId, $status)
	{
		if ($sectionMasterId > 0) {
			$objDB = new clsDB();
			$sql = "Update schoolpefevalsectionmaster set isActive = " . $status . " Where sectionMasterId = " . $sectionMasterId;
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
	}

	function GetPefEvaluationSectionDetail($sectionMasterId, $isType = '')
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM  schoolpefevalsectionmaster
				WHERE sectionMasterId=" . $sectionMasterId;
		if ($isType == '0')
			$sql .= " AND schoolpefevalsectionmaster.isType=0";

		else if ($isType == '1')
			$sql .= " AND schoolpefevalsectionmaster.isType=1";
		// echo $sql;exit;
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}



	function GetAllPEFQuestionMaster($sectionMasterId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM  schoolpefevalmaster
				WHERE sectionMasterId=" . $sectionMasterId . " Order By sortOrder,optionText ASC";
		//echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetPefEvaluationQuestionDetails($questionId, $isType = '')
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM  schoolpefevalmaster
					WHERE pefQuestionId=" . $questionId;
		if ($isType == '0')
			$sql .= " AND schoolpefevalmaster.isType=0";

		else if ($isType == '1')
			$sql .= " AND schoolpefevalmaster.isType=1";
		$rows = $objDB->GetDataRow($sql);
		// echo $sql; exit;
		return $rows;
		unset($objDB);
	}

	function GetPefEvaluationQuestionOptionDetails($questionId, $isType = '')
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT schoolpefevalmaster. *,schoolpefevaldetails.*
						FROM schoolpefevalmaster 
						INNER JOIN schoolpefevaldetails ON schoolpefevalmaster.pefQuestionId=
						schoolpefevaldetails.pefQuestionId
						WHERE schoolpefevalmaster.pefQuestionId=" . $questionId .
			" ORDER BY schoolpefevaldetails.pefOptionValue";

		if ($isType == '0')
			$sql .= " AND schoolpefevalmaster.isType=0";

		else if ($isType == '1')
			$sql .= " AND schoolpefevalmaster.isType=1";
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetAllPefEvaluationQuestionToSetting($sectionMasterId, $isType = '')
	{
		$objDB = new clsDB();
		$rows = "";

		$sql = "SELECT schoolpefevalmaster.*, questiontypemaster.title as questionType FROM schoolpefevalmaster
            LEFT JOIN questiontypemaster ON schoolpefevalmaster.pefQuestionType = questiontypemaster.questionType
            WHERE schoolpefevalmaster.sectionMasterId = " . intval($sectionMasterId);

		if ($isType === '0')
			$sql .= " AND schoolpefevalmaster.isType = 0";
		elseif ($isType === '1')
			$sql .= " AND schoolpefevalmaster.isType = 1";

		$sql .= " ORDER BY schoolpefevalmaster.sortOrder,optionText ASC";

		// Debug - remove or comment this out in production
		// echo $sql; 
		// exit;

		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}


	function DeleteStudentPEF($studentPEFMasterId)
	{
		$objDB = new clsDB();
		$sql = "DELETE  FROM studentpefmaster WHERE studentPEFMasterId=" . $studentPEFMasterId;
		// echo $sql;exit;
		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}

	function CopyMasterQuestionToSchool($schoolId, $questionId, $currentschoolSectionId, $sortOrder, $description = '')
	{
		$objDB = new clsDB();
		$sql = "INSERT INTO schoolpefevalmaster (optionText,pefQuestionType,schoolId,sectionMasterId,sortOrder,description) 
			
			SELECT optionText,pefQuestionType," . $schoolId . "," . $currentschoolSectionId . "," . $sortOrder . ",'" . $description . "'  FROM schoolpefevalmaster
			WHERE pefQuestionId=" . $questionId;
		// echo $sql;exit;
		$schoolQuestionId = $objDB->ExecuteInsertQuery($sql);
		unset($objDB);
		return  $schoolQuestionId;;
	}
	function CopyMasterQuestionChoicesToSchool($questionMasterId, $schoolQuestionId)
	{
		$sql = "INSERT INTO schoolpefevaldetails (pefQuestionId,optionText,pefOptionValue) 
					SELECT " . $schoolQuestionId . ",optionText,pefOptionValue
					FROM schoolpefevaldetails  WHERE pefQuestionId=" . $questionMasterId;

		$objDB = new clsDB();
		$schoolQuestionDId = $objDB->ExecuteInsertQuery($sql);
		unset($objDB);
	}


	function CopyAllPEFQuestionMaster($currentSchoolId)
	{

		$currentschoolSectionId = '';
		$this->schoolId = $currentSchoolId;
		$objDB = new clsDB();
		$savedQuestionIds = array();
		$savedSectionIds = array();

		$sql = "select * from schoolpefevalsectionmaster where schoolId = 75";
		$rowsSectionMaster = $objDB->GetResultset($sql);
		// echo $count=mysqli_num_rows($rowsSectionMaster);exit;
		if ($rowsSectionMaster != "") {
			while ($sectionMaster = mysqli_fetch_array($rowsSectionMaster)) {
				$currentschoolQuestionId = array();
				//default assignment
				$currentschoolQuestionId[] = 0;
				$sectionId = $sectionMaster['sectionMasterId'];

				$sql = "select schoolpefevalmaster.* from 
						schoolpefevaldetails 
								RIGHT JOIN schoolpefevalmaster ON  schoolpefevalmaster.pefQuestionId
																	=schoolpefevaldetails.pefQuestionId
								WHERE sectionMasterId =" . $sectionId;

				$rowsQuestionMaster = $objDB->GetResultset($sql);
				// echo $count=mysqli_num_rows($rowsQuestionMaster);exit;
				if ($rowsQuestionMaster != "") {
					while ($row = mysqli_fetch_array($rowsQuestionMaster)) {
						$masterQuestionId = $row['pefQuestionId'];

						// If already used then skipp
						if (array_key_exists($masterQuestionId, $savedQuestionIds)) {
							$currentschoolQuestionId[] = $savedQuestionIds[$masterQuestionId];
							continue;
						} else {
							$this->optionText = $row['optionText'];
							$this->pefQuestionType = $row['pefQuestionType'];
							$this->sectionMasterId = $row['sectionMasterId'];
							$this->sortOrder = $row['sortOrder'];
							$this->pefQuestionId = $masterQuestionId;
							$schoolQuestionId = $this->CopyMasterQuestionToSchool($currentSchoolId, $masterQuestionId, $row['sectionMasterId'], $row['sortOrder'], $row['description']);
							//Bind in array
							$savedQuestionIds[$masterQuestionId] = $schoolQuestionId;
							$currentschoolQuestionId[] = $schoolQuestionId;
							//-----------------------------------------------------
							//Copy Question Choices
							//-----------------------------------------------------
							$this->CopyMasterQuestionChoicesToSchool($masterQuestionId, $schoolQuestionId);
							//-----------------------------------------------------
						}
					} //while end


				} //if end
			} //1st while end
		} //1st if end 	
	}

	function GetAllPEFForApp($currentSchoolId, $rotationId = 0, $studentId = 0, $courseId, $isType = '', $generateLimitString, $searchText)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT studentpefmaster.* ,schools.schoolId, student.firstName as studentfirstName, 
		student.lastName as studentlastName, student.studentId, rotation.title as rotationName,
		courses.title as courseName, hospitalsites.hospitalSiteId, hospitalsites.title AS Hospitalsite
		from studentpefmaster
		inner join schools on schools.schoolId = studentpefmaster.schoolId
		inner join rotation on rotation.rotationId = studentpefmaster.rotationId
		inner join student on student.studentId =  studentpefmaster.studentId
		LEFT JOIN courses ON rotation.courseId=courses.courseId
		LEFT JOIN hospitalsites on hospitalsites.hospitalSiteId = rotation.hospitalSiteId
		WHERE studentpefmaster.schoolId=" . $currentSchoolId;

		if ($rotationId > 0)
			$sql .= " AND studentpefmaster.rotationId=" . $rotationId;

		if ($studentId > 0)
			$sql .= " AND studentpefmaster.studentId=" . $studentId;

		if ($isType == '1')
			$sql .= " AND studentpefmaster.isType=0";

		else if ($isType == '2')
			$sql .= " AND studentpefmaster.isType=1";

		if ($courseId > 0) {
			$sql .= "  AND rotation.courseId=" . $courseId;
		}

		if ($searchText != "") {
			$sql .= " AND (CONCAT(student.firstName, ' ', student.lastName) LIKE '%" . $searchText . "%' OR rotation.title LIKE '%" . $searchText . "%' OR studentpefmaster.isType LIKE '%" . $searchText . "%' )";
		}

		$sql .= " GROUP BY studentpefmaster.studentPEFMasterId";
		$sql .= " ORDER BY `studentpefmaster`.`evaluationDate` DESC" . $generateLimitString;

		// echo $sql;exit;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function savePEFEvalAuditLog($pefMasterId = 0, $retPEFEvalId, $clinicianId = 0, $userType, $action, $isMobile = 0, $type = '')
	{
		//    echo $userType; 
		// Instantiate the Logger and PEF Evaluation classes
		$objLog = new clsLogger();
		$objPEFEval = new clsPEF();

		// Prepare log data
		[$logData, $rowData, $additionalData] = $objPEFEval->createPEFEvalLog($retPEFEvalId, $action, $clinicianId, $userType, $type);

		$logData['isMobile'] = $isMobile;

		if ($action == 'Delete') {
			if ($type == 'section') {
				$objPEFEval->DeletePefEvaluationSection($pefMasterId);
			} else if ($type == 'step') {
				$objPEFEval->DeletePefQuestion($pefMasterId);
			} else {
				// // Initialize database object
				$objDB = new clsDB();

				// Fetch data from `studentpefevaldetails` table for the given master ID
				$evaluationDetailsResult = $objDB->GetAllSelectedDataFromTable('studentpefevaldetails', '', 'studentPEFMasterId', $retPEFEvalId);
				unset($objDB);

				if ($evaluationDetailsResult) {
					// Convert the result set into an array
					$evaluationDetailsArray = fetchResultAsArray($evaluationDetailsResult);

					// Generate the JSON array (if needed for logs or other purposes)
					$additionalData = $evaluationDetailsArray;

					$objPEFEval->DeleteStudentPEF($retPEFEvalId);
				}
			}
		}
		$moduleName = '';
		if ($type == 'section') {
			$moduleName = " Section";
		} else if ($type == 'step') {
			$moduleName = " Step";
		}
		// Save the log details
		$objLog->saveLogs($logData, $action, $retPEFEvalId, 'PEF Evaluation', $rowData, $additionalData);

		// Clean up
		unset($objLog);
		unset($objPEFEval);

		return true; // Return success or handle further actions as needed
	}



	function createPEFEvalLog($pefMasterId, $action, $userId, $userType, $type)
	{
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$objPEFEval = new clsPEF(); // Assuming `PEF Evaluation` class is used for `preparepefevalLogData`

		// Get user details based on user type
		$userDetails = getUserDetails($userId, $userType);
		// print_r($userDetails);exit;
		if ($type == 'section') {
			$rowData = $objPEFEval->GetPEFSectionDetailsForLogs($pefMasterId);
		} else if ($type == 'step') {
			$rowData = $objPEFEval->GetPEFStepDetailsForLogs($pefMasterId);
		} else {
			$rowData = $objPEFEval->GetAllPEFEvalDetailsForLogs($pefMasterId);
		}

		// echo '<pre>';
		// print_r($rowData);
		// exit;
		if ($rowData != '')
			$logData = $objLog->generateLogData($rowData, $userType);
		
		$additionalData = '';

		// Populate log data with user details
		$logData['userId'] = $userDetails['userId'];
		$logData['userName'] = $userDetails['userName'];
		$logMessage = '';
		if ($type == 'section') {
			$logData['isTypeSection'] = isset($rowData['isType']) ? (($rowData['isType'] == 0) ? 'PEF I' : 'PEF II') : '';
		}
        if ($type == 'step') {
			$logData['isTypeStep'] = isset($rowData['isType']) ? (($rowData['isType'] == 0) ? 'PEF I' : 'PEF II') : '';
		}
		if ($type == 'section') {
			if ($action == 'Add') {
				$logMessage = $logData['userName'] . ' added new section in ' . $logData['isTypeSection'] . ' Evaluation.';
			} else if ($action == 'Edit') {
				$logMessage = $logData['userName'] . ' updated the Section from ' . $logData['isTypeSection'] . ' Evaluation.';
			} else if ($action == 'Delete') {
				$logMessage = $logData['userName'] . ' deleted the section from ' . $logData['isTypeSection'] . ' Evaluation.';
			} else if ($action == 'Active') {
				$logMessage = $logData['userName'] . ' activate the section from ' . $logData['isTypeSection'] . ' Evaluation.';
			} else if ($action == 'Inactive') {
				$logMessage = $logData['userName'] . ' deactivate the section from ' . $logData['isTypeSection'] . ' Evaluation.';
			}
		} else if ($type == 'step') {

			if ($action == 'Add') {
				$logMessage = $logData['userName'] . ' added new Step in ' . $logData['isTypeStep'] . ' Evaluation Section.';
			} else if ($action == 'Edit') {
				$logMessage = $logData['userName'] . ' updated Step from ' . $logData['isTypeStep'] . ' Evaluation Section.';
			} else if ($action == 'Delete') {
				$logMessage = $logData['userName'] . ' deleted Step from ' . $logData['isTypeStep'] . ' Evaluation Section.';
			} else if ($action == 'Assign') {
				$logMessage = $logData['userName'] . ' Assign Steps in ' . $logData['isTypeStep'] . ' Evaluation Section.';
			} else if ($action == 'Unassign') {
				$logMessage = $logData['userName'] . ' Unassign Steps from ' . $logData['isTypeStep'] . ' Evaluation Section.';
			}
		} else {
			if ($action == 'Add') {
				$logMessage = $logData['userName'] . ' added PEF Evaluation for ' . $logData['rotationName'] . ' rotation.';
			} else if ($action == 'Edit') {
				$logMessage = $logData['userName'] . ' updated PEF Evaluation from ' . $logData['rotationName'] . ' rotation.';
			} else if ($action == 'Delete') {
				$logMessage = $logData['userName'] . ' deleted PEF Evaluation from ' . $logData['rotationName'] . ' rotation.';
			}
		}

		// Construct log message
		$logData['message'] = $logMessage;

		// Return the data
		return [$logData, $rowData, $additionalData];
	}

	function GetAllPEFEvalDetailsForLogs($pefMasterId, $schoolId = 0)
	{
		$objDB = new clsDB();

		$sql = "SELECT studentpefmaster.*, rotation.title as rotationName,studentpefmaster.studentId as userId,
				CONCAT(student.firstName, ' ', student.lastName) AS userName,hospitalsites.title AS hospitalSiteName,
				schools.displayName as schoolName
		FROM `studentpefmaster` 
		INNER JOIN rotation ON studentpefmaster.`rotationId` = rotation.`rotationId` 
		INNER JOIN student ON student.studentId=studentpefmaster.studentId
		LEFT JOIN hospitalsites ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
		INNER JOIN schools ON schools.schoolId = studentpefmaster.schoolId
		WHERE studentPEFMasterId =" . $pefMasterId;

		if ($schoolId) {
			$sql .= " AND rotation.schoolId=" . $schoolId;
		}
		// echo $sql;
		// exit;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}

	/**
	 * Retrieves CI evaluation details for a given CI evaluation ID and school ID.
	 *
	 * @param int $sectionMasterId The ID of the CI evaluation.
	 * @param int $schoolId The ID of the school. If not provided, all schools will be considered.
	 *
	 * @return array An array containing the log data, row data, and additional data.
	 */
	function GetPEFSectionDetailsForLogs($sectionMasterId, $isSuperAdmin = 0)
	{
		$objDB = new clsDB();
		$rows = "";

		$sql = "SELECT schoolpefevalsectionmaster.* FROM  schoolpefevalsectionmaster
				WHERE sectionMasterId=" . $sectionMasterId;

		$rows = $objDB->GetDataRow($sql);

		return $rows;
		unset($objDB);
	}


	/**
	 * Retrieves CI evaluation details for a given CI evaluation ID and school ID.
	 *
	 * @param int $pefQuestionId The ID of the CI evaluation.
	 * @param int $schoolId The ID of the school. If not provided, all schools will be considered.
	 *
	 * @return array An array containing the log data, row data, and additional data.
	 */
	function GetPEFStepDetailsForLogs($pefQuestionId, $isSuperAdmin = 0)
	{
		$objDB = new clsDB();
		$row = "";

		$sql = "SELECT schoolpefevalmaster.*,questiontypemaster.title as questionType,schoolpefevalsectionmaster.title AS SectionTitle FROM schoolpefevalmaster
				LEFT JOIN questiontypemaster ON schoolpefevalmaster.pefQuestionType=questiontypemaster.questionType
				INNER JOIN schoolpefevalsectionmaster ON schoolpefevalsectionmaster.sectionMasterId = schoolpefevalsectionmaster.sectionMasterId
				WHERE  schoolpefevalmaster.pefQuestionId=" . $pefQuestionId;

		// if($currentSchoolId == '118')
		$sql .= " ORDER BY sortOrder ASC";
		// echo $sql;exit;

		$row = $objDB->GetDataRow($sql);
		return $row;
		unset($objDB);
	}
}
