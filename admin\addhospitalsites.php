<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsHospitalSite.php');
include('../class/clsCountryStateMaster.php');
include('../class/clsRotation.php');
include('../setRequest.php');

$hospitalSiteId = 0;
$hospitalSitetitle = '';
$address1 = '';
$address2 = '';
$city = '';
$stateId = $CurrentSchoolStateId;
$zip = '';
$phone = '';
$cellPhone = '';
$email = '';
$contactPerson = '';
$schoolId = '';
$faxNumber = '';
$adminNotes = '';
$clockIn = '';
$clockOut = '';
$notificationMonth = '';
$lattitude = '';
$longitude = '';
$expireDate = '';
$hospitalDays = '';
$dailyVisits = 0;
$hospitalSiteCode = '';
$title = "Add Hospital Sites";
if (isset($_GET['schoolId'])) //Edit Mode
{
    $schoolId = $_GET['schoolId'];
    $schoolId = DecodeQueryData($schoolId);
} else {
    $schoolId = $currentSchoolId;
    //$tranSchoolDisplayname = $currenschoolDisplayname;
}

// copy the hospital site
$isCopy = isset($_GET['isCopy']) ? DecodeQueryData($_GET['isCopy']) : 0;
$bedCrumTitle = 'Add';
if (isset($_GET['id'])) //Edit Mode
{
    $hospitalSiteId = $_GET['id'];
    $hospitalSiteId = DecodeQueryData($hospitalSiteId);

    if ($isCopy) {
        $title = "Copy Hospital Sites";
        $bedCrumTitle = 'Copy';
    } else {
        $title = "Edit Hospital Sites";
        $bedCrumTitle = 'Edit';
    }
    //For Hospital Site Details
    $objHospitalSite = new clsHospitalSite();
    $row = $objHospitalSite->GetHospitalSiteDetails($hospitalSiteId);
    // echo '<pre>';
    // print_r($row);
    $hospitalSitetitle  = stripslashes($row['title']);
    unset($objHospitalSite);

    if ($row == '') {
        header('location:addhospitalsites.html');
        exit;
    }

    $TimeZone = $_SESSION["loggedUserSchoolTimeZone"];
    $hospitalSitetitle  = stripslashes($row['title']);
    $hospitalSitetitle = ($isCopy) ? "Copy of " . $hospitalSitetitle : $hospitalSitetitle;
    $address1  = stripslashes($row['address1']);
    $address2  = stripslashes($row['address2']);
    $city  = stripslashes($row['city']);
    $stateId  = stripslashes($row['stateId']);
    $zip  = stripslashes($row['zip']);
    $adminNotes  = stripslashes($row['adminNotes']);
    $faxNumber  = ($row['faxNumber']);
    $email  = ($row['email']);
    $phone  = ($row['phone']);
    $cellPhone  = ($row['cellPhone']);
    $contactPerson  = stripslashes($row['contactPerson']);
    $schoolId  = ($row['schoolId']);
    $clockIn  = ($row['clockIn']);
    $clockOut  = ($row['clockOut']);
    $initialDate  = ($row['initialDate']);
    if ($initialDate != '' && $initialDate != '0000-00-00' && $initialDate != '12/31/1969') {
        $initialDate = converFromServerTimeZone($initialDate, $TimeZone);
        $initialDate = date('m/d/Y', strtotime($initialDate));
    }
    $expireDate  = ($row['expireDate']);
    if ($expireDate != '' && $expireDate != '0000-00-00' && $expireDate != '12/31/1969') {
        $expireDate = converFromServerTimeZone($expireDate, $TimeZone);
        $expireDate = date('m/d/Y', strtotime($expireDate));
    }
    $notificationMonth  = ($row['notificationOption']);
    $notificationDate  = ($row['notificationDate']);
    if ($notificationDate != '' && $notificationDate != '0000-00-00' && $notificationDate != '12/31/1969') {
        $notificationDate = converFromServerTimeZone($notificationDate, $TimeZone);
        $notificationDate = date('m/d/Y', strtotime($notificationDate));
    }
    $objDB = new clsDB();
    $notificationId = $objDB->GetSingleColumnValueFromTable('affiliationnotification', 'months', 'affiliationId', $notificationMonth);
    $lattitude  = ($row['lattitude']);
    $longitude  = ($row['longitude']);

    $hospitalDays = ($row['hospitalDays']);
    $dailyVisits = ($row['dailyVisits']);
    $hospitalSiteCode = ($row['hospitalSiteCode']);
}

$title .= " | " . $currenschoolDisplayname;

//For Country List
$objCountryStateMaster = new clsCountryStateMaster();
$countries = $objCountryStateMaster->GetAllCountry();

//For State List
$dbCountryId = $objCountryStateMaster->GetParentIdFromChildId($stateId);
unset($objCountryStateMaster);

$buttontitle = ($isCopy) ? "Copy" : "Save";


// Check Schedule is active for school or not
$objDB = new clsDB();
$scheduleActive = $objDB->GetSingleColumnValueFromTable('schools', 'scheduleActive', 'schoolId', $currentSchoolId);
unset($objDB);

// Get notification options
$objHospitalSite = new clsHospitalSite();
$notificationOption = $objHospitalSite->GetAffiliatedNotificationOptions();
unset($objHospitalSite);

$objRotation = new clsRotation();
$AllHospitalDays = $objRotation->GetAllRepeatDays();
unset($objRotation);
$hospitalDays = ($hospitalDays != '') ? explode(',', $hospitalDays) : array();
// $hospitalDays = array();
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($title); ?></title>
    <?php include('includes/headercss.php'); ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker3.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">


</head>

<body>
    <?php include('includes/header.php'); ?>
    <style>
        #map {
            height: 300px;
            width: 100%;
            margin-left: 200px;
        }
    </style>
    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="settings.html">Settings</a></li>
                    <li><a href="hospitalsites.html">Hospital Sites</a></li>
                    <li class="active"><?php echo ($bedCrumTitle); ?></li>
                </ol>
            </div>

        </div>
    </div>

    <div class="container">

        <div class="pageheading"></div>

        <form id="frmlocation" data-parsley-validate class="form-horizontal" method="POST" action="addhospitalsitessubmit.html?id=<?php echo (EncodeQueryData($hospitalSiteId)); ?>&schoolId=<?php echo (EncodeQueryData($schoolId)); ?>&isCopy=<?php echo (EncodeQueryData($isCopy)); ?>">
            <div class="row">
                <div class="col-md-12">

                    <div class="formSubHeading">Hospital Sites Information</div>
                    <div class="row">
                        <div class="col-md-6">
                            <!-- Text input-->
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txttitle">Hospital Name</label>
                                <div class="col-md-12">
                                    <input id="txttitle" name="txttitle" value="<?php echo ($hospitalSitetitle); ?>" required type="text" placeholder="" class="form-control input-md required-input">

                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtcontactPerson">Contact Person</label>
                                <div class="col-md-12">
                                    <input id="txtcontactPerson" data-parsley-pattern="/^[a-zA-Z ,.'-]+$/i" name="txtcontactPerson" value="<?php echo ($contactPerson); ?>" required type="text" placeholder="" class="form-control input-md required-input">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <!-- Text input-->
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtEmail">Email</label>
                                <div class="col-md-12">
                                    <input id="txtEmail" name="txtEmail" value="<?php echo ($email); ?>" required type="email" placeholder="" class="form-control input-md required-input">

                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <!-- Text input-->
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtPhone">Phone</label>
                                <div class="col-md-12">
                                    <input class="form-control input-md required-input" data-inputmask-alias="************" name="txtPhone" id="txtPhone" value="<?php echo ($phone); ?>" required>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtcellPhone">Cell Phone </label>
                                <div class="col-md-12">
                                    <input class="form-control input-md" data-inputmask-alias="************" name="txtcellPhone" id="txtcellPhone" value="<?php echo ($cellPhone); ?>">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtfaxNumber">Fax Number </label>
                                <div class="col-md-12">
                                    <input class="form-control input-md" data-inputmask-alias="************" name="txtfaxNumber" id="txtfaxNumber" value="<?php echo ($faxNumber); ?>">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <?php if ($scheduleActive) { ?>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-md-12 control-label" for="clockIn">Clock In Time </label>
                                    <div class="col-md-12">
                                        <div class='input-group date w-full' id='clockIn' style="position: relative;">
                                            <input type='text' class="form-control input-md clockIn" name="clockIn" id="clockIn" value="<?php echo $clockIn; ?>" placeholder="HH-MM">

                                            <span class="input-group-addon calender-icon">
                                                <span class="glyphicon glyphicon-calendar"></span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                        <?php if ($scheduleActive) { ?>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-md-12 control-label" for="clockOut">Clock Out Time </label>
                                    <div class="col-md-12">
                                        <div class='input-group date w-full' id='clockOut' style="position: relative;">
                                            <input type='text' class="form-control input-md clockOut" name="clockOut" id="clockOut" value="<?php echo $clockOut; ?>" placeholder="HH-MM">

                                            <span class="input-group-addon calender-icon">
                                                <span class="glyphicon glyphicon-calendar"></span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                    </div>
                <?php } ?>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="initialDate">Affiliation Agreement Initial Date </label>
                            <div class="col-md-12">
                                <div class='input-group date w-full' name="initialDate1" id='initialDate1'>
                                    <input type='text' name="initialDate" id="initialDate" value="<?php echo ($initialDate); ?>" class="dateInputFormat form-control input-md initialDate" data-parsley-errors-container="#error-txtDate" placeholder="MM-DD-YYYY" />
                                    <span class="input-group-addon calender-icon">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>
                                </div>
                                <div id="error-txtDate"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="cbonotification">Notification Options</label>
                            <div class="col-md-12">
                                <select id="cbonotification" name="cbonotification" class="form-control input-md select2_single" data-parsley-errors-container="#error-notification" <?php if ($hospitalSiteId && $initialDate == '0000-00-00') echo "disabled"; ?>>
                                    <option value="" selected>Select</option>
                                    <?php
                                    if ($notificationOption != "") {
                                        while ($row = mysqli_fetch_assoc($notificationOption)) {
                                            $selid  = $row['affiliationId'];
                                            $months  = stripslashes($row['months']);
                                    ?>
                                            <option value="<?php echo ($selid); ?>" <?php if ($notificationMonth == $selid) { ?> selected="true" <?php } ?>> <?php echo ($months . ' Months'); ?>
                                            </option>
                                    <?php
                                        }
                                    }
                                    ?>
                                </select>
                                <div id="error-notification"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="expirationDate">Expiration Date </label>
                            <div class="col-md-12">
                                <div class='input-group date w-full expirationDate' name="expirationDate1">
                                    <input type='text' name="expirationDate" id="expirationDate" value="<?php echo ($expireDate); ?>" class="dateInputFormat form-control input-md expirationDate" data-parsley-errors-container="#error-expireDate" <?php if ($hospitalSiteId && $initialDate == '0000-00-00') echo "disabled"; ?> placeholder="MM-DD-YYYY" />
                                    <span class="input-group-addon calender-icon">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>
                                </div>
                                <div id="error-expireDate"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="cbonotificationdate">Notification Date </label>
                            <div class="col-md-12">
                                <div class='input-group date w-full'>
                                    <input type='text' name="cbonotificationdate" id="cbonotificationdate" value="<?php echo ($notificationDate); ?>" class="dateInputFormat form-control input-md cbonotificationdate" data-parsley-errors-container="#error-txtDate" disabled placeholder="MM-DD-YYYY" />
                                    <span class="input-group-addon calender-icon">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>
                                </div>
                                <div id="error-txtDate"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="hospitalSiteCode">Hospital Site Code</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <div class='input-group hospitalSiteCode w-full' id='hospitalSiteCode'>
                                    <input class="form-control input-md " type="text" id="hospitalSiteCode" name="hospitalSiteCode" maxlength="10" value="<?php echo ($hospitalSiteCode); ?>">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="duration">Max Students/Day visits hospital site </label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <div class='input-group Duration w-full' id='Duration'>
                                    <input class="form-control input-md parentDurationLatest" type="text" id="dailyVisits" name="dailyVisits" value="<?php echo ($dailyVisits); ?>" oninput="numberOnly(this.id);">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group HospitalDays">
                            <label class="col-md-12 control-label" for="HospitalDays">Select Repeat Days</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <div class='input-group table-responsive table-border'>

                                    <table class="points_table" style="height:100px;width: 100%;" id="datatable-responsive">
                                        <thead>
                                            <tr>
                                                <th style="min-width: 60px;width: 20%;text-align: center;""><input class=" selectall" type="checkbox" id="selectall" name="selectall[0]">All</th>
                                                <th style="min-width: 60px;width: 80%;">Repeat Days</th>

                                            </tr>
                                        </thead>
                                        <tbody class="points_table_scrollbar">
                                            <?php
                                            if ($AllHospitalDays) {

                                                while ($row = mysqli_fetch_array($AllHospitalDays)) {
                                                    $title = $row['title'];
                                                    $value = $row['value'];
                                                    $checked = "";
                                                    if (is_array($hospitalDays))
                                                        if (in_array($value, $hospitalDays)) {
                                                            $checked = 'checked="true"';
                                                        }

                                            ?>
                                                    <tr>
                                                        <td style="min-width: 60px;width: 20%;text-align: center;">
                                                            <input class="uncheck checkedcount" type="checkbox" value="<?php echo ($value); ?>" name="hospitalDays[]" <?php if (isset($_GET['id'])) {
                                                                                                                                                                            echo $checked;
                                                                                                                                                                        } else { ?> checked <?php } ?>>
                                                        </td>
                                                        <td style="min-width: 60px; width: 80%;"><?php echo ($title); ?></td>
                                                    </tr>
                                            <?php
                                                }
                                            }
                                            ?>
                                        </tbody>
                                    </table>



                                </div>
                            </div>
                        </div>




                    </div>
                </div>
                <div>
                    <div class="formSubHeading">Address Information</div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtAddress1">Address 1</label>
                                <div class="col-md-12">
                                    <input id="txtAddress1" name="txtAddress1" type="text" placeholder="" value="<?php echo ($address1); ?>" required class="form-control input-md required-input">

                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtAddress2">Address 2</label>
                                <div class="col-md-12">
                                    <input id="txtAddress2" name="txtAddress2" type="text" placeholder="" value="<?php echo ($address2); ?>" class="form-control input-md">

                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="cboCountry">Country</label>
                                <div class="col-md-12">
                                    <select id="cboCountry" name="cboCountry" class="form-control step1 input-md  select2_single" required>
                                        <option value="" selected>Select</option>
                                        <?php
                                        if ($countries != "") {
                                            while ($row = mysqli_fetch_assoc($countries)) {
                                                $location_id  = $row['location_id'];
                                                $name  = stripslashes($row['name']);

                                        ?>
                                                <option value="<?php echo ($location_id); ?>" <?php if ($dbCountryId == $location_id) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                                        <?php

                                            }
                                        }
                                        ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <!-- Select Basic -->
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtCity">City</label>
                                <div class="col-md-12">
                                    <input id="txtCity" name="txtCity" type="text" placeholder="" value="<?php echo ($city); ?>" required class="form-control input-md required-input">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <!-- Select Basic -->
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="cboState">State</label>
                                <div class="col-md-12">
                                    <select id="cboState" name="cboState" class="form-control step2 input-md select2_single">
                                        <option value="" selected>Select</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtZipCode">Zip Code</label>
                                <div class="col-md-12">
                                    <input id="txtZipCode" name="txtZipCode" type="text" placeholder="" value="<?php echo ($zip); ?>" required class="form-control input-md required-input">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label class="col-md-12 control-label">Notes</label>
                                <div class="col-md-12 col-sm-12 col-xs-12">
                                    <textarea name="adminNotes" id="adminNotes" class="form-control input-md" rows="4" cols="100"><?php echo ($adminNotes); ?></textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-4 control-label" for="txtlattitude">Lattitude </label>
                            <div class="col-md-8">
                                <input class="form-control input-md" name="txtlattitude" id="txtlattitude" value="<?php echo ($lattitude); ?>" readonly>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-4 control-label" for="txtlongitude">Longitude </label>
                            <div class="col-md-8">
                                <input class="form-control input-md" name="txtlongitude" id="txtlongitude" value="<?php echo ($longitude); ?>" readonly>
                            </div>
                        </div>
                    </div>
                </div> -->

                    <!-- <div class="col-md-6">
                    <div class="form-group">
                        <div>
                            <div id="map"></div>
                        </div>
                    </div>
                </div> -->
                    <div class="form-group">
                        <!-- <label class="col-md-2 control-label"></label> -->
                        <div class="col-md-12" style="display: flex; justify-content: center; margin: 20px 0;gap: 15px;">
                            <button id="btnSubmit" name="btnSubmit" class="btn btn-success"><?php echo $buttontitle; ?></button>
                            <a type="button" href="hospitalsites.html" class="btn btn-default">Cancel</a>
                        </div>
                    </div>
        </form>


    </div>

    <?php include('includes/footer.php'); ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/jquery.cascadingdropdown.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/inputmask/jquery.inputmask.bundle.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/inputmask/inputmask.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script> -->
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>

    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        $(window).load(function() {

            $(".select2_single").select2();
            $('#select2-cboCountry-container').addClass('required-select2');
            $('#select2-cboState-container').addClass('required-select2');

            $('#frmlocation').parsley().on('field:validated', function() {
                    var ok = $('.parsley-error').length === 0;
                })
                .on('form:submit', function() {
                    ShowProgressAnimation();
                    $('#cbonotificationdate').prop('disabled', false);
                    return true; // Don't submit form for this demo
                });

            <?php
            if ($dbCountryId == 0) {
            ?>
                $('#cboCountry').val('224').trigger('change');
            <?php
            }
            ?>

            $('#frmlocation').cascadingDropdown({
                selectBoxes: [{
                        selector: '.step1',
                        selected: '<?php echo ($dbCountryId); ?>'
                    },
                    {
                        selector: '.step2',
                        selected: '<?php echo ($stateId); ?>',
                        requires: ['.step1'],
                        requireAll: true,
                        source: function(request, response) {

                            $.getJSON('<?php echo ($dynamicOrgUrl); ?>/ajax/getStates.html', request, function(data) {
                                response($.map(data, function(item, index) {
                                    return {
                                        label: item['StateName'],
                                        value: item['StateId']
                                    };
                                }));
                            });
                        }
                    }
                ]

            });

            $('#clockOut').datetimepicker({
                format: 'hh:mm A'
            });

            $('#clockIn').datetimepicker({
                format: 'hh:mm A'
            });

            $('#initialDate').datetimepicker({
                format: 'MM/DD/YYYY'
            });

            $('#expirationDate').datetimepicker({
                format: 'MM/DD/YYYY'
            });

            $('#cbonotificationdate').datetimepicker({
                format: 'MM/DD/YYYY'
            });

            checkAll();
        });

        $('#expirationDate, #cbonotification').on('change', function() {
            $("#cbonotification").prop("required", true);
            var expirationDate = $('#expirationDate').val();
            var selectedOptionText = $('#cbonotification').find('option:selected').text();
            var numberOnly = parseInt(selectedOptionText.replace(/\D/g, ''), 10); // Parse the number as integer

            if (expirationDate != '' && numberOnly != 0 && selectedOptionText != '') {
                var expirationDate = moment($('#expirationDate').val(), 'MM/DD/YYYY');
                var calculatedDate = expirationDate.clone().subtract(numberOnly, 'months').format('MM/DD/YYYY');
                $('#cbonotificationdate').val(calculatedDate);
            }
        });


        $(document).ready(function() {

            $('#initialDate').on('change', function() {
                var initialDate = $(this).val();
                if (initialDate === "") {

                    $('#expirationDate , #cbonotificationdate').val("").prop({
                        disabled: true,
                        required: false
                    }).removeClass('required-input');

                    $('#cbonotification').val("").prop({
                        disabled: true,
                        required: false
                    }).trigger('change').removeClass('required-select2');
                    $('#select2-cbonotification-container').removeClass('required-select2');

                } else {

                    $('#expirationDate').prop({
                        'disabled': false,
                        'required': true
                    }).addClass('required-input');

                    $('#cbonotification').prop({
                        'disabled': false,
                        'required': true

                    });
                    $('#select2-cbonotification-container').addClass('required-select2');
                }

            });

            $('#btnSubmit').on('click', function() {
                var initialDate = $('#initialDate').val();

                if (initialDate) {
                    $('#expirationDate').prop({
                        'required': true
                    }).addClass('required-input');

                    $('#cbonotification').prop({
                        'required': true,
                    });
                    $('#select2-cbonotification-container').addClass('required-select2');
                } else {
                    $('#expirationDate, #cbonotification').prop('required', false);
                }
            });


        });

        function checkAll() {

            var TotalCheckboxCount = $('input[name="hospitalDays[]"]').length;

            var CheckedCheckboxCount = $('input[name="hospitalDays[]"]:checked').length;

            console.log('TotalCheckboxCount', TotalCheckboxCount);
            console.log('CheckedCheckboxCount', CheckedCheckboxCount);

            if (TotalCheckboxCount == CheckedCheckboxCount) {
                $('.selectall').prop('checked', true);
            } else {
                $('.selectall').prop('checked', false);
            }
        }

        $('.checkedcount').click(function() {
            checkAll();
        });

        $('#selectall').click(function() {
            //alert('');
            if ($(this).is(':checked')) {
                $('.uncheck').prop('checked', true);
            } else {
                $('.uncheck').removeAttr('checked');

            }
        });
    </script>
    <script type="text/javascript">
        var map;

        function initMap() {
            var latitude = 41.8781; // Chicago latitude
            var longitude = -87.6298; // Chicago longitude

            // var latitude =  <?php //echo ($latti); 
                                ?>; 
            // var longitude = <?php //echo ($long); 
                                ?>;
            var myLatLng = {
                lat: latitude,
                lng: longitude
            };

            map = new google.maps.Map(document.getElementById('map'), {
                center: myLatLng,
                zoom: 14
            });

            var marker = new google.maps.Marker({
                position: myLatLng,
                map: map,
                title: latitude + ', ' + longitude
            });

            // Add click event listener to the map
            google.maps.event.addListener(map, 'click', function(event) {
                latitude = event.latLng.lat().toFixed(8);
                longitude = event.latLng.lng().toFixed(8);

                marker.setPosition(event.latLng);
                marker.setTitle(latitude + ', ' + longitude);

                // Update input fields with new latitude and longitude values
                $('#txtlattitude').val(latitude);
                $('#txtlongitude').val(longitude);

                // You can now store the latitude and longitude values (latitude and longitude variables) in a database or use them as needed
                console.log('Latitude: ' + latitude + ', Longitude: ' + longitude);
            });
        }
    </script>
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAYOMVZkvVaT8XlkfVKrYWLsSQygRnmE2I&callback=initMap" async defer></script>
</body>

</html>