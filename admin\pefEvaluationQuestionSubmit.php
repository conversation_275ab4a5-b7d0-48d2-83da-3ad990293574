<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsSchool.php');
include('../class/clsPEF.php');
include('../setRequest.php');

//print_r($_POST);
// echo '<pre>';
// print_r($_SESSION);exit;
if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit'])) {

	$questionId = isset($_GET['editid']) ? DecodeQueryData($_GET['editid']) : 0;
	$isPEFType = isset($_POST['isPEFType']) ? ($_POST['isPEFType']) : 0;

	$sectionMasterId = isset($_GET['sectionMasterId']) ? DecodeQueryData($_GET['sectionMasterId']) : 0;

	$status = ($questionId > 0) ? 'updated' : 'added';

	$txtsinglechoice  = isset($_POST['txtsinglechoice']) ? $_POST['txtsinglechoice'] : [];
	$title = $_POST['txtQuestion'];
	$questionType = $_POST['cbotype'];
	$optionSortOrder  = isset($_POST['txtsinglechoicemarks']) ? $_POST['txtsinglechoicemarks'] : [];
	$isPosition = $_POST['isPosition'];
	$sortOrder = isset($_POST['sortOrder']) ? $_POST['sortOrder'] : NULL;
	$description = $_POST['description'];

	//Save data
	$objPEF = new clsPEF();
	$objPEF->optionText = $title;
	$objPEF->pefQuestionType = $questionType;
	$objPEF->sectionMasterId = $sectionMasterId;
	$objPEF->isPosition = $isPosition;
	$objPEF->sortOrder = $sortOrder;
	$objPEF->isPEFType = $isPEFType;
	$objPEF->description = $description;

	$retquestionId = $objPEF->SavePefEvaluationQuestion($questionId, $isPEFType);

	if ($questionId) {

		$deleteCiEvalutionOptions = $objPEF->DeletePefEvaluationOptions($questionId);
	}


	if ($questionType == 2) {
		foreach ($txtsinglechoice as $key => $value) {

			$objPEF->pefQuestionId = $retquestionId;
			$objPEF->optionText = $value;
			$objPEF->pefOptionValue = $optionSortOrder[$key];
			$retQuestionOptionId = $objPEF->SavePefEvaluationQuestionOptions($questionId);
		}
	}
	unset($objPEF);
	if ($retquestionId > 0) {
		//Audit Log Start
		// Instantiate the Logger class
		$objLog = new clsLogger();

		// Determine the action type (EDIT or ADD) based on the presence of a journal ID
		$action = ($questionId > 0) ? $objLog::EDIT : $objLog::ADD;
		$type = "step";
		$userType = $objLog::SUPERADMIN; // User type is set to Admin
		$objPEF = new clsPEF();
		$objPEF->savePEFEvalAuditLog(0, $retquestionId, $_SESSION["loggedUserId"], $userType, $action, $IsMobile = 0, $type);

		unset($objPEF);
		unset($objLog);
		//Audit Log End

		header('location:pefEvaluationQuestionList.html?status=' . $status . '&sectionMasterId=' . EncodeQueryData($sectionMasterId) . '&isPEFType=' . EncodeQueryData($isPEFType));
	} else {
		header('location:addPefEvaluationQuestions.html?sectionMasterId=' . EncodeQueryData($sectionMasterId) . '&isPEFType=' . EncodeQueryData($isPEFType) . '&status=error');
	}
} else {
	header('location:pefEvaluationQuestionList.html?sectionMasterId=' . EncodeQueryData($sectionMasterId) . '&isPEFType=' . EncodeQueryData($isPEFType) . '&status=' . $status);
	exit();
}
