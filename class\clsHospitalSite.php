<?php
class clsHospitalSite
{
	var $hospitalSiteId = 0;
	var $title = '';
	var $address1 = '';
	var $address2 = '';
	var $city = '';
	var $stateId = '';
	var $zip = '';
	var $phone = '';
	var $cellPhone = '';
	var $email = '';
	var $contactPerson = '';
	var $schoolId = '';
	var $createdBy = '';
	var $createdDate = '';
	var $updatedBy = '';
	var $updatedDate = '';
	var $clockIn = '';
	var $clockOut = '';
	var $intialDate = '';
	var $expireDate = '';
	var $notificationOption = '';
	var $notificationDate = '';
	var $lattitude = '';
	var $longitude = '';
	var $isPeerToPeer = 0;
	var $hospitalDays = '';
	var $hospitalSiteCode = '';
	var $dailyVisits = 0;

	function SaveHospitalSite($hospitalSiteId)
	{
		$objDB = new clsDB();
		$sql = '';

		if ($hospitalSiteId > 0) {

			$sql = "UPDATE hospitalsites SET 
						 title = '" . addslashes($this->title) . "', 
						 address1 = '" . addslashes($this->address1) . "', 
						 address2 = '" . addslashes($this->address2) . "', 
						 city = '" . addslashes($this->city) . "',
						 stateId = '" . addslashes($this->stateId) . "', 
						 zip = '" . addslashes($this->zip) . "',  
						 phone = '" . ($this->phone) . "',
						 cellPhone = '" . ($this->cellPhone) . "',
						 faxNumber = '" . ($this->faxNumber) . "',
						 adminNotes = '" . addslashes($this->adminNotes) . "',
						 clockIn = '" . ($this->clockIn) . "',
						 clockOut = '" . ($this->clockOut) . "',
						 email = '" . addslashes($this->email) . "',
						 contactPerson = '" . addslashes($this->contactPerson) . "',
						 schoolId = '" . addslashes($this->schoolId) . "',
						 updatedBy = '" . addslashes($this->createdBy) . "',
						 updatedDate = '" . (date("Y-m-d h:i:s")) . "',
						 initialDate = '" . addslashes($this->intialDate) . "',
						 expireDate = '" . addslashes($this->expireDate) . "',
						 notificationOption = '" . ($this->notificationOption) . "',
						 notificationDate = '" . ($this->notificationDate) . "',
						 lattitude = '" . ($this->lattitude) . "',
						 longitude = '" . ($this->longitude) . "',
						 hospitalDays = '" . ($this->hospitalDays) . "',
						 hospitalSiteCode = '" . ($this->hospitalSiteCode) . "',
						 dailyVisits = '" . ($this->dailyVisits) . "'
						 Where hospitalSiteId= " . $hospitalSiteId;
			// echo 'update' . $sql;
			// exit;
			$objDB->ExecuteQuery($sql);
		} else {
			$sql = "INSERT INTO hospitalsites (title, address1, address2, city, stateId,
			zip, phone, cellPhone, faxNumber, adminNotes, email, contactPerson, schoolId, createdBy,isPeerToPeer, createdDate, clockIn, clockOut, initialDate, expireDate,notificationDate, notificationOption, lattitude, longitude,hospitalDays,hospitalSiteCode,dailyVisits) 
				VALUES ('" . addslashes($this->title) . "',
						'" . addslashes($this->address1) . "',
						'" . addslashes($this->address2) . "',
						'" . addslashes($this->city) . "',
						'" . addslashes($this->stateId) . "',
						'" . addslashes($this->zip) . "',
						'" . ($this->phone) . "', 
						'" . ($this->cellPhone) . "', 
						'" . ($this->faxNumber) . "', 
						'" . addslashes($this->adminNotes) . "',
						'" . addslashes($this->email) . "',
						'" . addslashes($this->contactPerson) . "',
						'" . addslashes($this->schoolId) . "',
						'" . addslashes($this->createdBy) . "',
						'" . addslashes($this->isPeerToPeer) . "',
						'" . (date("Y-m-d h:i:s")) . "'	,
						'" . ($this->clockIn) . "', 
						'" . ($this->clockOut) . "',
						'" . addslashes($this->intialDate) . "',
						'" . addslashes($this->expireDate) . "',
						'" . ($this->notificationDate) . "',
						'" . ($this->notificationOption) . "',
						'" . ($this->lattitude) . "',
						'" . ($this->longitude) . "',
						'" . ($this->hospitalDays) . "',
						'" . ($this->hospitalSiteCode) . "',
						'" . ($this->dailyVisits) . "'
						)";
			// echo 'insert' . $sql;
			// exit;
			$hospitalSiteId = $objDB->ExecuteInsertQuery($sql);
		}

		unset($objDB);
		return $hospitalSiteId;
	}

	function DeleteHospitalSite($hospitalSiteId)
	{
		$objDB = new clsDB();
		$result = "";
		if ($hospitalSiteId > 0) {
			$objDB = new clsDB();
			$sql = "DELETE FROM hospitalsites WHERE hospitalSiteId = " . $hospitalSiteId;
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
		return $result;
	}

	function GetAllHospitalSite($currentSchoolId, $isActive = 0, $limitstring = '')
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
				hospitalsites.hospitalSiteId,
				hospitalsites.title,
				hospitalsites.email,
				hospitalsites.phone,
				hospitalsites.city,
				hospitalsites.contactPerson ,
				hospitalsites.isHospital ,
				hospitalsites.isSms,
				hospitalsites.isActive,
				hospitalsites.clockIn, hospitalsites.clockOut,
				countrystatemaster.countryStateMasterId,countrystatemaster.name ,
				clinicianhospitalsite.hospitalSiteId AS CIhospitalSiteId
						,rotation.hospitalSiteId AS RotationhospitalSiteId,clinicianattendance.clockInDateTime
				FROM hospitalsites
				LEFT JOIN countrystatemaster ON hospitalsites.stateId=countrystatemaster.countryStateMasterId
				LEFT JOIN clinicianhospitalsite ON hospitalsites.hospitalSiteId =
													clinicianhospitalsite.hospitalSiteId                                                             
				LEFT JOIN	rotation ON	 rotation.hospitalSiteId=hospitalsites.hospitalSiteId 
				LEFT JOIN clinicianattendance ON clinicianattendance.hospitalSiteId = hospitalsites.hospitalSiteId
				WHERE hospitalsites.schoolId=" . $currentSchoolId;
		if ($isActive == 0)
			$sql .= " AND hospitalsites.isActive = 0";

		elseif ($isActive == 2)
			$sql .= " AND hospitalsites.isActive = 1";

		$sql .= " GROUP  BY hospitalsites.hospitalSiteId
				ORDER BY hospitalsites.title ASC " . $limitstring;

		// ECHO $sql; exit;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetClinicianHopitalSitesForAPI($clinicianId, $limitstring = '')
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT cls.clinicianHospitalSiteId, cls.clinicianId, hs.hospitalSiteId, hs.title,clinicianattendance.clockInDateTime
				FROM clinicianhospitalsite cls
				INNER JOIN hospitalsites hs ON cls.hospitalSiteId = hs.hospitalSiteId
				LEFT JOIN clinicianattendance ON clinicianattendance.hospitalSiteId = cls.hospitalSiteId
				WHERE cls.clinicianId = " . $clinicianId;
		$sql .= " GROUP  BY hs.hospitalSiteId
				ORDER BY hs.title ASC " . $limitstring;
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllHospitalSiteByStudent($currentSchoolId, $studentId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT hospitalsites.* , rotation.hospitalSiteId,rotationdetails.studentId,
				rotationdetails.rotationId,rotation.rotationId
				FROM hospitalsites 
				INNER JOIN rotation ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
				LEFT JOIN rotationdetails ON rotation.rotationId=rotationdetails.rotationId
				LEFT JOIN student ON rotationdetails.studentId =student.studentId
				INNER JOIN schools ON  schools.schoolId=student.schoolId
				WHERE schools.schoolId=" . $currentSchoolId . " AND rotationdetails.studentId=" . $studentId
			.  " Group by hospitalsites.title";
		//ECHO $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}


	function GetHospitalSiteDetails($hospitalSiteId)
	{
		$objDB = new clsDB();
		$sql = "SELECT * FROM  hospitalsites WHERE hospitalSiteId=" . $hospitalSiteId;
		$row = $objDB->getDataRow($sql);
		unset($objDB);
		return $row;
	}

	function GetHospitalSiteCount($schoolId)
	{
		$returnCount = 0;
		$objDB = new clsDB();
		$sql = "SELECT COUNT(hospitalSiteId) As TotalCount FROM `hospitalsites` WHERE schoolId=" . $schoolId;

		$returnCount = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $returnCount;
	}

	function GetParentIdFromChildId($countryStateMasterId)
	{
		$parent_id  = "";
		$objDB = new clsDB();
		$sql = "SELECT parent_id  FROM countrystatemaster Where countryStateMasterId= '" . $countryStateMasterId . "'";
		$parent_id  = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $parent_id;
	}
	function DeleteSchooHospitalsites($schoolId)
	{
		$objDB = new clsDB();
		$sql = "DELETE FROM hospitalsites WHERE schoolId = " . $schoolId;
		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);

		return $result;
	}

	function GetHospitalSiteByRotation($rotationId)
	{
		$objDB = new clsDB();
		$sql = "SELECT hospitalsites.hospitalSiteId,hospitalsites.title 
				FROM  hospitalsites
				LEFT JOIN rotation ON hospitalsites.hospitalSiteId=rotation.hospitalSiteId
				WHERE rotation.rotationId=" . $rotationId;
		//ECHO $sql;
		$row = $objDB->getDataRow($sql);
		unset($objDB);
		return $row;
	}

	function GetHospitalSiteTitle($hospitalSiteId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT title FROM hospitalsites where hospitalSiteId = " . $hospitalSiteId;
		$rows = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllHospitalSiteId($schoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT hospitalSiteId FROM `hospitalsites` WHERE schoolId=" . $schoolId;
		// echo $sql; exit;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllActiveSMSHospitalSite($studentId = 0, $rotationId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT hospitalsites.hospitalSiteId,hospitalsites.title FROM hospitalsites
				inner join rotation on  rotation.hospitalSiteId = hospitalsites.hospitalSiteId
				inner JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId
				WHERE hospitalsites.isHospital = 1 AND hospitalsites.isSms = 1";

		if ($rotationId)
			$sql .= " AND rotation.rotationId= " . $rotationId;

		if ($studentId)
			$sql .= " AND rotationdetails.studentId= " . $studentId;

		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetIsHospitalStatus($hospitalSiteId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT COUNT(*) FROM hospitalsites WHERE hospitalSiteId= $hospitalSiteId AND isHospital = 1 AND isSms = 1";
		$rows = $objDB->GetSingleFieldValue($sql);
		// echo $sql;
		unset($objDB);
		return $rows;
	}
	function GetRotationsCountByHospitalSites($schoolId, $hospitalSiteId = 0)
	{
		$CurrentDate = date('Y-m-d H:i:s');

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT COUNT(*) FROM rotation LEFT JOIN courses ON rotation.courseId = courses.courseId LEFT JOIN hospitalsites ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId  WHERE rotation.schoolId=$schoolId  AND rotation.isDelete= 0 AND rotation.hospitalSiteId= $hospitalSiteId AND '" . date("Y-m-d", strtotime($CurrentDate)) . "' <= Date(rotation.endDate)  ORDER BY rotation.title asc";
		$rows = $objDB->GetSingleFieldValue($sql);
		// echo $sql;
		unset($objDB);
		return $rows;
	}
	function GetAllHospitalSiteWithClockInTime($currentSchoolId, $isActive = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
				hospitalsites.hospitalSiteId,
				hospitalsites.title,
				hospitalsites.clockIn, hospitalsites.clockOut
				FROM hospitalsites
				
				WHERE hospitalsites.schoolId=" . $currentSchoolId . " AND hospitalsites.clockIn != '' AND hospitalsites.clockOut != '' ";
		if ($isActive == 0)
			$sql .= " AND hospitalsites.isActive = 0";

		elseif ($isActive == 2)
			$sql .= " AND hospitalsites.isActive = 1";

		$sql .= " GROUP  BY hospitalsites.hospitalSiteId
				ORDER BY hospitalsites.title ASC ";

		// ECHO $sql ;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAffiliatedNotificationOptions()
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM affiliationnotification";
		// echo $sql; exit;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	function GetHospitalsiteNameById($hospitalsiteId)
	{
		$objDB = new clsDB();
		$rows = "";
		$hospitalsiteId = str_replace(" ", ",", $hospitalsiteId);
		$sql = "SELECT Group_concat(title) FROM hospitalsites where hospitalsiteId IN ($hospitalsiteId) ";
		$rows = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $rows;
	}

	function GetClinicianHopitalsiteDetailsForApp($clinicianId, $hospitalsiteId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT hospitalsites.hospitalSiteId, hospitalsites.title, clinicianattendance.attendanceId, 
		clinicianattendance.clockInDateTime, clinicianattendance.clockOutDateTime, clinicianattendance.orignalhours,
		 clinicianattendance.approvedhours, clinicianattendance.clockIn_longitude, 
		 clinicianattendance.clockIn_lattitude, clinicianattendance.clockOut_longitude, 
		 clinicianattendance.clockOut_lattitude, clinicianattendance.notes, clinicianattendance.comment, 
		 clinicianattendance.status, clinicianattendance.isException,clinicianattendance.isClockOut
		FROM hospitalsites
		INNER JOIN clinicianattendance ON hospitalsites.hospitalSiteId = clinicianattendance.hospitalSiteId
		WHERE clinicianattendance.clinicianId= " . $clinicianId . " AND hospitalsites.hospitalSiteId=" . $hospitalsiteId;

		// echo $sql;
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllHospitalSiteByRole($schoolId, $clinicianId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT hospitalsites.*, clinicianhospitalsite.clinicianId, clinicianhospitalsite.hospitalSiteId FROM hospitalsites
		LEFT JOIN clinicianhospitalsite ON hospitalsites.hospitalSiteId = clinicianhospitalsite.hospitalSiteId 
		LEFT JOIN clinician ON clinician.clinicianId = clinicianhospitalsite.clinicianId
		where hospitalsites.schoolId=" . $schoolId . " AND clinicianhospitalsite.clinicianId = " . $clinicianId . " AND hospitalsites.isActive =0";

		//ECHO $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetUpcomingAffiliateAgreement($schoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT hospitalsites.hospitalSiteId,  hospitalsites.title, hospitalsites.initialDate, hospitalsites.expireDate	
		from hospitalsites 
		INNER JOIN affiliationnotification ON hospitalsites.notificationOption = affiliationnotification.affiliationId
		where DATE(hospitalsites.notificationDate) <= CURDATE() And DATE(hospitalsites.expireDate) >= CURDATE() AND hospitalsites.schoolId= " . $schoolId;
		// echo $sql; exit;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllHospitalSites($schoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT hospitalsites.* FROM hospitalsites where hospitalsites.schoolId=" . $schoolId . " AND hospitalsites.isActive =0";

		//ECHO $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetUpcomingAffiliateAgreementForReport($schoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT hospitalsites.hospitalSiteId,  hospitalsites.title, hospitalsites.initialDate, hospitalsites.expireDate	
		from hospitalsites 
		INNER JOIN affiliationnotification ON hospitalsites.notificationOption = affiliationnotification.affiliationId
		where  hospitalsites.expireDate >= CURDATE() AND  hospitalsites.notificationDate !='' AND hospitalsites.expireDate !='' AND hospitalsites.schoolId= " . $schoolId;
		// echo $sql; exit;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	/**
	 * Gets the details of a hospital site for the logs report.
	 *
	 * @param int $id The ID of the hospital site.
	 * @param int $schoolId The ID of the school to filter by. Optional.
	 * @return array The details of the hospital site.
	 */
	function GetAllHospitalSiteDetailsForLogs($id, $schoolId = 0)
	{
		$objDB = new clsDB();

		$sql = "SELECT hospitalsites.*,schools.schoolId, schools.displayName as schoolName FROM `hospitalsites` 
        INNER JOIN schools ON schools.schoolId = hospitalsites.schoolId
		WHERE hospitalsiteId =" . $id;

		if ($schoolId) {
			$sql .= " AND hospitalsites.schoolId=" . $schoolId;
		}
		// echo $sql;exit;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}

	/**
	 * This function creates a log entry for a Hospital Site action.
	 *
	 * @param int $id The ID of the Hospital Site.
	 * @param string $action The action performed (Add, Edit, Delete, Active, Inactive).
	 * @param int $userId The ID of the user performing the action.
	 * @param string $userType The type of user performing the action.
	 * @param string $type Optional type of action for specific flags (H for hospital, A for active, S for SMS).
	 * @param int $isSuperAdmin Optional flag indicating whether the user is a super admin or not.
	 *
	 * @return array An array containing the log data, row data, and additional data.
	 */

	function createHospitalSiteLog($id, $action, $userId, $userType, $type = '', $isSuperAdmin = 0)
	{
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$obj = new clsHospitalSite(); // Assuming `Attendance` class is used for `prepareAttendanceLogData`

		// Get user details based on user type
		$userDetails = getUserDetails($userId, $userType);

		$rowData = $obj->GetAllHospitalSiteDetailsForLogs($id);
		if ($rowData != '')
			$logData = $objLog->generateLogData($rowData, $userType);

		$additionalData = '';

		// Populate log data with user details
		$logData['userId'] = $userDetails['userId'];
		$logData['userName'] = $userDetails['userName'];
		$logMessage = '';

		if ($action == 'Add') {
			$logMessage = $logData['userName'] . ' added new hospital site';
		} else if ($action == 'Edit') {
			$logMessage = $logData['userName'] . ' updated ' . $rowData['title'] . ' hospital site';
		} else if ($action == 'Delete') {
			$logMessage = $logData['userName'] . ' deleted ' . $rowData['title'] . ' hospital site';
		} else if ($action == 'Active' && $type == 'H') {
			$logMessage = $logData['userName'] . ' activate the hospital site flag of ' . $rowData['title'];
		} else if ($action == 'Inactive'  && $type == 'H') {
			$logMessage = $logData['userName'] . ' deactivate the hospital site flag of ' . $rowData['title'];
		} else if ($action == 'Active' && $type == 'A') {
			$logMessage = $logData['userName'] . ' activate the ' . $rowData['title'] . ' hospital site';
		} else if ($action == 'Inactive'  && $type == 'A') {
			$logMessage = $logData['userName'] . ' deactivate the ' . $rowData['title'] . ' hospital site';
		} else if ($action == 'Active' && $type == 'S') {
			$logMessage = $logData['userName'] . ' activate the SMS flag of ' . $rowData['title'];
		} else if ($action == 'Inactive'  && $type == 'S') {
			$logMessage = $logData['userName'] . ' deactivate the SMS flag of ' . $rowData['title'];
		}
		// Construct log message
		$logData['message'] = $logMessage;

		// Return the data
		return [$logData, $rowData, $additionalData];
	}

	/**
	 * Saves an audit log entry for actions performed on a hospital site.
	 *
	 * @param int $id The ID of the hospital site.
	 * @param int $userId The ID of the user performing the action.
	 * @param string $userType The type of user (e.g., Admin, Clinician, Student).
	 * @param string $action The action performed (e.g., Add, Edit, Delete, Active, Inactive).
	 * @param int $isMobile Optional; specifies if the action was performed from a mobile device (0 = No, 1 = Yes).
	 * @param string $type Optional; specifies the type of action or state change (e.g., H for hospital flag, A for active state, S for SMS flag).
	 * @param int $isSuperAdmin Optional; specifies if the action was performed by a super admin (0 = No, 1 = Yes).
	 *
	 * @return bool True if the log was successfully saved.
	 */

	function saveHospitalSiteAuditLog($id, $userId, $userType, $action, $isMobile = 0, $type = '', $isSuperAdmin = 0)
	{
		// Instantiate the Logger and Checkoff classes
		$objLog = new clsLogger();
		$obj = new clsHospitalSite();

		// Prepare log data
		[$logData, $rowData, $additionalData] = $obj->createHospitalSiteLog($id, $action, $userId, $userType, $type, $isSuperAdmin);
		$logData['isMobile'] = $isMobile;

		if ($action == 'Delete') {
			$obj->DeleteHospitalSite($id);
		}

		// Save the log details
		$objLog->saveLogs($logData, $action, $id, 'Hospital Site', $rowData, $additionalData);

		// Clean up
		unset($objLog);
		unset($obj);

		return true; // Return success or handle further actions as needed
	}

	function GetAllHospitalSiteForSchedule($currentSchoolId, $isActive = 0, $limitstring = '')
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
				hospitalsites.hospitalSiteId,
				hospitalsites.title,
				hospitalsites.clockIn, 
				hospitalsites.clockOut,
				hospitalsites.hospitalDays,
				hospitalsites.dailyVisits,
				hospitalsites.hospitalSiteCode,
				GROUP_CONCAT(rotationrepeatdaysmaster.title ORDER BY rotationrepeatdaysmaster.value) AS hospitalDaysTitle

				FROM hospitalsites
				LEFT JOIN rotationrepeatdaysmaster rotationrepeatdaysmaster ON FIND_IN_SET(rotationrepeatdaysmaster.value, hospitalsites.hospitalDays)
				WHERE hospitalsites.schoolId=" . $currentSchoolId;
		if ($isActive == 0)
			$sql .= " AND hospitalsites.isActive = 0";

		elseif ($isActive == 2)
			$sql .= " AND hospitalsites.isActive = 1";

		$sql .= " GROUP  BY hospitalsites.hospitalSiteId
				ORDER BY hospitalsites.title ASC " . $limitstring;

		// ECHO $sql; exit;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
}
