<?php 
	include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
    include('../class/clsSchool.php');      	
    include('../class/clsPEvaluation.php');
	
	include('../setRequest.php'); 
	
		//print_r($_POST);
	if($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit']))
	{
		
		$questionId = isset($_GET['editid']) ? DecodeQueryData($_GET['editid']) : 0;

		$sectionMasterId = isset($_GET['sectionMasterId']) ? DecodeQueryData($_GET['sectionMasterId']) : 0;
		$status = ($questionId > 0) ? 'updated' : 'added';
		
		$txtsinglechoice  = $_POST['txtsinglechoice'];
		$title = $_POST['txtQuestion'];
		$questionType = $_POST['cbotype'];
		$optionSortOrder = $_POST['txtsinglechoicemarks'];
		$isPosition = $_POST['isPosition'];
		$sortOrder = isset($_POST['sortOrder']) ? $_POST['sortOrder'] : NULL;
		
		//Save data
        $objPevaluation = new clsPEvaluation();
		$objPevaluation->schoolId = $currentSchoolId;
		$objPevaluation->sectionMasterId = $sectionMasterId;
		$objPevaluation->questionText = $title;			
		$objPevaluation->schoolPEvaluationQuestionType = $questionType;			
		$objPevaluation->isPosition = $isPosition;
		$objPevaluation->sortOrder = $sortOrder;
		if($isCurrentSchoolSuperAdmin == 1) // For superadmin			
			$retquestionId = $objPevaluation->SaveDefaultPevaluationQuestion($questionId);
		else
			$retquestionId = $objPevaluation->SavePevaluationQuestion($questionId);
	
		//Delete Site evaluation Options
		if($questionId)
		{
			if($isCurrentSchoolSuperAdmin == 1) // For superadmin
				$deleteSiteevaluationOptions = $objPevaluation->DeleteDefaultPEvaluationQuestionDetail($questionId);
			else
				$deleteSiteevaluationOptions = $objPevaluation->DeletePEvaluationQuestionDetail($questionId);
		}
			
		
		if($questionType == 2)
		{
			foreach($txtsinglechoice as $key=>$value)
			{
				$objPevaluation->schoolPEvaluationQuestionId = $retquestionId;	
				$objPevaluation->optionText = $value;		
				$objPevaluation->schoolOptionValue = $optionSortOrder[$key];

				if($isCurrentSchoolSuperAdmin == 1) // For superadmin
					$retQuestionOptionId = $objPevaluation->SaveDefaultPEvaluationQuestionOptions($questionId);
				else
					$retQuestionOptionId = $objPevaluation->SavePEvaluationQuestionOptions($questionId);
					
			}
		}


		unset($objPevaluation);
		if($retquestionId > 0)
		{
			//Audit Log Start
			// Instantiate the Logger class
			$objLog = new clsLogger();

			// Determine the action type (EDIT or ADD) based on the presence of a journal ID
			$action = ($questionId > 0) ? $objLog::EDIT : $objLog::ADD;
			$type = "step";
			$isSuperAdmin = ($isCurrentSchoolSuperAdmin == 1) ? 1 : 0;
			$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN; // User type is set to Admin

			$objPevaluation = new clsPEvaluation();
			$objPevaluation->savePEvalAuditLog($questionId,$retquestionId, $_SESSION["loggedUserId"], $userType, $action, $IsMobile = 0, $type,$isSuperAdmin);
			unset($objPevaluation);

			unset($objLog);
			//Audit Log End

			header('location:pEvaluationQuestionList.html?status='.$status.'&sectionMasterId='.EncodeQueryData($sectionMasterId));
		}
		else
		{
			header('location:addPEvaluationQuestions.html?sectionMasterId='.EncodeQueryData($sectionMasterId).'&status=error');
		}
	}
	else
	{
		header('location:pEvaluationQuestionList.html?sectionMasterId='.EncodeQueryData($sectionMasterId).'&status='.$status);
		//header('location:checkoffsection.html');
		exit();
	}

?>