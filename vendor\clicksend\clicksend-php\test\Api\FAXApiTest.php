<?php
/**
 * FAXApiTest
 * PHP version 5
 *
 * @category Class
 * @package  ClickSend
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * ClickSend v3 API
 *
 * This is an official SDK for [ClickSend](https://clicksend.com)  Below you will find a current list of the available methods for clicksend.  *NOTE: You will need to create a free account to use the API. You can register [here](https://dashboard.clicksend.com/#/signup/step1/)..*
 *
 * OpenAPI spec version: 3.1
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * ClickSend Codegen version: 3.0.51
 */
/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Please update the test case below to test the endpoint.
 */

namespace ClickSend;

use ClickSend\Configuration;
use Click<PERSON><PERSON>\ApiException;
use <PERSON>lickSend\ObjectSerializer;
use PHPUnit\Framework\TestCase;

/**
 * FAXApiTest Class Doc Comment
 *
 * @category Class
 * @package  ClickSend
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */
class FAXApiTest extends TestCase
{

    /**
     * Setup before running any test cases
     */
    public static function setUpBeforeClass(): void
    {
    }

    /**
     * Setup before running each test case
     */
    public function setUp(): void
    {
    }

    /**
     * Clean up after running each test case
     */
    public function tearDown(): void
    {
    }

    /**
     * Clean up after running all test cases
     */
    public static function tearDownAfterClass(): void
    {
    }

    /**
     * Test case for faxHistoryGet
     *
     * Get a list of Fax History..
     *
     */
    public function testFaxHistoryGet()
    {
    }

    /**
     * Test case for faxPricePost
     *
     * Calculate Total Price for Fax Messages sent.
     *
     */
    public function testFaxPricePost()
    {
    }

    /**
     * Test case for faxReceiptsByMessageIdGet
     *
     * Get a single fax receipt based on message id..
     *
     */
    public function testFaxReceiptsByMessageIdGet()
    {
    }

    /**
     * Test case for faxReceiptsGet
     *
     * Get all delivery receipts.
     *
     */
    public function testFaxReceiptsGet()
    {
    }

    /**
     * Test case for faxReceiptsPost
     *
     * Add a delivery receipt.
     *
     */
    public function testFaxReceiptsPost()
    {
    }

    /**
     * Test case for faxReceiptsReadPut
     *
     * Mark delivery receipts as read.
     *
     */
    public function testFaxReceiptsReadPut()
    {
    }

    /**
     * Test case for faxSendPost
     *
     * Send a fax using supplied supported file-types..
     *
     */
    public function testFaxSendPost()
    {
    }
}
