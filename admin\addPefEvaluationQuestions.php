<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsPEF.php');
include('../setRequest.php');

$isPEFType = isset($_GET['isPEFType']) ? DecodeQueryData($_GET['isPEFType']) : 0;


$title = "Add PEF Evaluation Questions - ";
$page_title = "Add PEF Evaluation Questions";
$title = '';
$sortOrder  = '';
$TopicTitleId = '';
$bedCrumTitle = 'Add';
$questionId = 0;
$sortOrder = '';
$isActiveCheckoff = $_SESSION['isActiveCheckoff'];
$optionText = '';
$questionType = '';
$description = '';
$view = '';
$sectionMasterId = 0;
$isPosition = 0;
if ($isPEFType == 0) {
    $activeType = 'pef1';
} else {
    $activeType = 'pef2';
}
if (isset($_GET['sectionMasterId'])) //Edit Mode
{
    $sectionMasterId = DecodeQueryData($_GET['sectionMasterId']);
}
$view = isset($_GET['view']) ? $_GET['view'] : '';

if (isset($_GET['editid'])) //Edit Mode
{
    $questionId = DecodeQueryData($_GET['editid']);
    if ($view == 1) {
        if ($isPEFType == 1)
            $page_title = "View PEF II ";
        else
            $page_title = "View PEF I ";
    } else {
        if ($isPEFType == 1)
            $page_title = "Edit PEF II ";
        else
            $page_title = "Edit PEF I ";
    }

    if ($view == '1') {
        $bedCrumTitle = 'view';
    } else {
        $bedCrumTitle = 'Edit';
    }

    $objPEF = new clsPEF();

    $row = $objPEF->GetPefEvaluationQuestionDetails($questionId, $isPEFType);
    $rowsquistionoption = $objPEF->GetPefEvaluationQuestionOptionDetails($questionId, $isPEFType);

    $optionText  = isset($row['optionText']) ? stripslashes($row['optionText']) : '';
    $questionType = isset($row['pefQuestionType']) ? $row['pefQuestionType'] : '';
    $isPosition = isset($row['isPosition']) ? $row['isPosition'] : '';
    $sortOrder = isset($row['sortOrder']) ? $row['sortOrder'] : '';
    $description = isset($row['description']) ? $row['description'] : '';

    unset($objPEF);
}

$optionDefaultArray = array("1" => "1", "2" => "2", "3" => "3", "4" => "4 ", "5" => "5");

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($page_title); ?></title>
    <?php include('includes/headercss.php'); ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <style>
        .select2-container {
            border-left: 3px solid red !important;
            border-radius: 12px !important;
        }
    </style>
</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="settings.html">Settings</a></li>
                    <li>
                        <a href="pefEvaluationSectionList.html?active=<?php echo ($isPEFType == 1 ? 'pef2' : 'pef1'); ?>&isPEFType=<?php echo EncodeQueryData($isPEFType); ?>">
                            <?php echo ($isPEFType == 1) ? "PEF II Evaluation Section" : "PEF I Evaluation Section"; ?>
                        </a>

                    </li>
                    <li><a href="pefEvaluationQuestionList.html?sectionMasterId=<?php echo EncodeQueryData($sectionMasterId); ?>&isPEFType=<?php echo EncodeQueryData($isPEFType); ?>">Steps</a></li>
                    <li class="active"><?php echo ($bedCrumTitle); ?></li>
                </ol>
            </div>
        </div>
    </div>

    <div class="container">

        <form id="frmcheckoff" data-parsley-validate class="form-horizontal" method="POST" action="pefEvaluationQuestionSubmit.html?editid=<?php echo (EncodeQueryData($questionId)); ?>&sectionMasterId=<?php echo (EncodeQueryData($sectionMasterId)); ?>&isPEFType=<?php echo (EncodeQueryData($isPEFType)); ?>">
            <input type="hidden" name="isPEFType" id="" value="<?php echo $isPEFType; ?>">

            <div class="row">
                <div class="col-md-6">
                    <!-- Text input-->
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="cbotype">Question Type</label>
                        <div class="col-md-12 flex-direction-reverse">
                            <select id="cbotype" name="cbotype" class="form-control input-md select2_single required-input" required data-parsley-errors-container="#error-cbotype" required>
                                <option <?php if ($questionType == '') { ?> selected<?php } ?> value="" >Select</option>
                                <option <?php if ($questionType == 2) { ?> selected<?php } ?> value="2" name="questionType">Single Choice</option>
                                <option <?php if ($questionType == 5) { ?> selected<?php } ?> value="5" name="questionType">Long Answer</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtQuestion"> Title</label>
                        <div class="col-md-12">
                            <input id="txtQuestion" name="txtQuestion" value="<?php echo ($optionText); ?>" type="text" class="form-control input-md required-input" required>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <?php ?>
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="sortOrder"> Sort Order</label>
                        <div class="col-md-12">
                            <input id="sortOrder" name="sortOrder" value="<?php echo ($sortOrder); ?>" type="text" class="form-control input-md ">
                        </div>
                    </div>
                    <?php ?>
                </div>

                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="sortOrder">Description</label>
                        <div class="col-md-12">
                            <textarea id="description" name="description" class="form-control input-md" value="" rows="2"><?php echo ($description); ?></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">


                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label mt-2" for="txtQuestion"> Option Position</label>
                        <div class="col-md-3">
                            <input id="horizontal" name="isPosition" style=" margin-top: 11px;" value="0" <?php echo ($isPosition == 0) ?  "checked" : "";  ?> type="radio" class="input-md required-input ml-3" required> Horizontal
                        </div>
                        <div class="col-md-3">
                            <input id="vertical" name="isPosition" style=" margin-top: 11px;" value="1" <?php echo ($isPosition == 1) ?  "checked" : "";  ?> type="radio" class=" input-md required-input" required> Vertical
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group clssingle" style="display: none;">
                        <div class="form-group m-0">
                            <div class="col-md-12 control-label"> </div>
                            <div class="col-md-12">
                                <div class="row">
                                    <?php if ($questionId) {
                                        $optionDefaultArray = array(); ?>
                                        <div class="col-md-12 textboxDiv p-0">
                                            <?php

                                            while ($row = mysqli_fetch_array($rowsquistionoption)) {
                                                $optionText = $row['optionText'];
                                                $pefOptionValue = $row['pefOptionValue'];
                                            ?>
                                                <div class="singlechoiceboxdiv row m-0" style="margin-bottom: 10px;">
                                                    <input type="hidden" id="hid_anser" class="hid_anser" name="answers[]" value="0">
                                                    <div class="col-md-6">
                                                        <label style=''>Answer</label>
                                                        <input type="text" id="txtsinglechoice" style='' value="<?php echo $optionText; ?>" class="form-control input-md required-input choicebox" name="txtsinglechoice[]" placeholder="Add an answer" required><br />
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label>Answer Number</label>
                                                        <div style="display: flex; align-items: flex-start;">
                                                            <div style="flex: 1;">
                                                                <input type="text"
                                                                    id="txtsinglechoicemarks"
                                                                    value="<?php echo $pefOptionValue; ?>"
                                                                    class="form-control input-md required-input choicebox"
                                                                    name="txtsinglechoicemarks[]"
                                                                    placeholder="Add an answer number"
                                                                    required
                                                                    style="width: 100%; margin-bottom: 5px;">
                                                            </div>
                                                            <?php if ($isCurrentSchoolSuperAdmin == 1): ?>
                                                                <span id="Remove"
                                                                    class="glyphicon glyphicon-trash Remove"
                                                                    style="color: red; margin-left: 10px; margin-top: 8px; cursor: pointer;"></span>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>

                                                </div>
                                            <?php } ?>
                                        </div>
                                    <?php } else {
                                    ?>
                                        <div class="col-md-12 px-0 textboxDiv">
                                            <?php
                                            foreach ($optionDefaultArray as $key => $value) {
                                                $optionText = $value;
                                                $pefOptionValue = $key;
                                            ?>
                                                <div class="singlechoiceboxdiv row m-0">
                                                    <input type="hidden" id="hid_anser" class="hid_anser" name="answers[]" value="0">
                                                    <div class="col-md-6">
                                                        <label style=''>Answer</label>
                                                        <input type="text" id="txtsinglechoice" style='' value="<?php echo $optionText; ?>" class="form-control input-md required-input choicebox" name="txtsinglechoice[]" placeholder="Add an answer" required><br />
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label style=" ">Answer Number</label>
                                                        <div style="display: flex;align-items: center;">
                                                        <div style="flex: 1;">
                                                            <input type="text" id="txtsinglechoicemarks" value="<?php echo $pefOptionValue; ?>" style="" class="form-control input-md required-input choicebox" name="txtsinglechoicemarks[]" placeholder="Add an answer number" required>
                                                        </div>
                                                            <span id="Remove" style="color:red;margin-left: 10px;" class="glyphicon glyphicon-trash Remove"></span><br />
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php
                                            }
                                            ?>
                                        </div>
                                    <?php } ?>
                                    <div class="col-md-12" style="display: flex; justify-content: end; margin-top: 10px;">
                                        <?php if ($isCurrentSchoolSuperAdmin == 1) { ?>
                                            <button type="button" id="Add" class="btn btn-success">Add</button>
                                        <?php
                                        }
                                        ?>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group clslongans" style="display: none;">
                        <div class="">
                            <label class="col-md-12 control-label" for="Answer"> Answer </label>
                            <div class="col-md-12">
                                <textarea id="longans" name="longans" class="form-control input-md" rows="3"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <!-- <label class="col-md-2 control-label"></label> -->
                <div class="col-md-12" style="display: flex; justify-content: center;margin: 20px 0;gap: 15px;">

                    <?php if ($view == '1') { ?>
                        <div class="form-group">
                            <div class="col-md-12" style="display: flex; justify-content: center;margin: 20px 0;gap: 15px;">
                                <a type="button" href="pefEvaluationQuestionList.html?sectionMasterId=<?php echo (EncodeQueryData($sectionMasterId)); ?>" class="btn btn-default">Cancel</a>
                            </div>
                        </div>
                    <?php } else { ?>
                        <div class="form-group">
                            <div class="col-md-12" style="display: flex; justify-content: center;margin: 20px 0;gap: 15px;">
                                <button type="submit" id="btnSubmit" name="btnSubmit" style="margin-left:63px;" class="btn btn-success">Save</button>
                                <a type="button" href="pefEvaluationQuestionList.html?sectionMasterId=<?php echo (EncodeQueryData($sectionMasterId)); ?>" class="btn btn-default">Cancel</a>
                            </div>
                        </div>
                    <?php } ?>

                </div>
            </div>
        </form>

    </div>

    <?php include('includes/footer.php'); ?>

    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>

    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script type="text/javascript">
        $(".select2_tags").select2({
            'placeholder': 'Select'
        }); //for multiple selection

        $(window).load(function() {



            $('#frmcheckoff').parsley().on('field:validated', function() {
                    var ok = $('.parsley-error').length === 0;
                })
                .on('form:submit', function() {
                    ShowProgressAnimation();
                    return true;
                });

            $(".select2_single").select2();

        });

        $(document).ready(function() {
            $("select").change(function() {

                $('.clssingle').hide();
                $('.clslongans').hide();

                if ($(this).val() == 2)
                    $('.clssingle').show();
                else if ($(this).val() == 5)
                    $('.clslongans').show();
            });

            $('#cbotype').trigger('change');

            $("#Add").on("click", function() {
                $(".textboxDiv").append("<div class='singlechoiceboxdiv row m-0'style='margin-bottom: 10px;''><div class='col-md-6'><label  style=''>Answer</label>	<input type='text' class='form-control input-md required-input choicebox' id='txtsinglechoice' style=''  name='txtsinglechoice[]' placeholder='Add an answer' required /><br /></div><div class='col-md-6'><label style=''>Answer Number</label><div style='display: flex;align-items: center;'><div class='w-full'><input type='text' class='form-control input-md required-input choicebox' id='txtsinglechoicemarks'  style='' name='txtsinglechoicemarks[]' placeholder='Add an answer number' required /><input type='hidden'  id='hid_anser' class='hid_anser' name='answers[]'  value='0'/></div><span id='Remove' style='color:red;margin-left: 10px;' class='glyphicon glyphicon-trash Remove'></span></div></div></div>");
            });

            // $(".Remove").on("click", function() {  

            $(document).on("click", ".Remove", function() {
                $(this).closest('.singlechoiceboxdiv').remove();
            });
        });
    </script>
    <?php if ($view == 1): ?>
        <script>
            document.addEventListener("DOMContentLoaded", function() {
                // Disable all form fields inside the form only
                document.querySelectorAll('#frmcheckoff input, #frmcheckoff select, #frmcheckoff textarea, #frmcheckoff button').forEach(function(el) {
                    el.setAttribute('disabled', true);
                });

                // Optional: enable Cancel button so user can go back
                document.querySelectorAll('.btn-default').forEach(function(el) {
                    el.removeAttribute('disabled');
                });
            });
        </script>
    <?php endif; ?>


</body>

</html>