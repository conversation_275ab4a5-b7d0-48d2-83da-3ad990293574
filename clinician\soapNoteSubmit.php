<?php
$IsMobile  = isset($_POST['IsMobile']) ? $_POST['IsMobile'] : 0;

include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsSchool.php');
include('../class/clsSystemUser.php');
include('../class/clsStudent.php');
include('../setRequest.php');
include('../class/clsSoapNote.php');
include('../class/clsPreceptorDetails.php');
include('../class/clsExternalPreceptors.php');

$objDB = new clsDB();
$objSoapNote = new clsSoapNote();
$soapNoteDate = '';

$studentId = isset($_GET['studentId']) ? ($_GET['studentId']) : 0;

//Get Country Code
$countryCode = $objDB->GetSingleColumnValueFromTable('schools', 'countryCode', 'schoolId', $currentSchoolId);

if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit'])) {
    $soapNoteId = isset($_GET['editsoapNoteId']) ? DecodeQueryData($_GET['editsoapNoteId']) : 0;
    $rotationId = isset($_POST['cborotation']) ? DecodeQueryData($_POST['cborotation']) : 0;
    $clinicianSignatureDate = isset($_POST['ClinicianDate']) ? ($_POST['ClinicianDate']) : '';

    if ($clinicianSignatureDate) {
        $clinicianSignatureDate  = GetDateStringInServerFormat($_POST['ClinicianDate']);
        $clinicianSignatureDate = str_replace('00:00:00', '12:00 PM', $clinicianSignatureDate);
        $clinicianSignatureDate = date('Y-m-d H:i', strtotime($clinicianSignatureDate));
    } else {
        $clinicianSignatureDate = '';
    }

    if ($soapNoteId > 0) {
        if (!empty($clinicianSignatureDate)) {
            $status = "SignOff";
        } else {
            $status = "Updated";
        }
    } else {
        $status = "Added";
    }

    $objSoapNote = new clsSoapNote();
    $objSoapNote->clinicianSignatureDate = $clinicianSignatureDate;
    $objSoapNote->updatedBy = $_SESSION["loggedClinicianId"];
    $retSoapNote = $objSoapNote->UpdateSoapNoteByClinician($soapNoteId);

    if ($soapNoteId > 0) {
        //Audit Log Start
        // Instantiate the Logger class
        $objLog = new clsLogger();

        // Determine the action type (EDIT or ADD) based on the presence of a journal ID
        $action = $objLog::SIGNOFF;
        $userType = $objLog::CLINICIAN; // User type is set to CLINICIAN

        $objSoapNote->saveSoapNoteAuditLog($retSoapNote, $_SESSION["loggedClinicianId"], $userType, $action, $IsMobile);

        unset($objLog);
        //Audit Log End

        if ($IsMobile) {
            header('location:' . BASE_PATH . '/webRedirect.html?status=Success&type=soapnote');
            exit;
        } else {
            header('location:soapNoteList.html?rotationId=' . EncodeQueryData($rotationId) . '&status=' . $status);
            exit();
        }
    } else {

        if ($IsMobile) {
            header('location:' . BASE_PATH . '/webRedirect.html?status=Success&type=soapnote');
            exit;
        } else {
            header('location:soapNoteList.html?rotationId=' . EncodeQueryData($rotationId) . '&status=' . $status);
            exit();
        }
    }
} else {
    if ($IsMobile) {
        header('location:' . BASE_PATH . '/webRedirect.html?status=Error&type=soapnote');
        exit();
    } else {
        header('location:soapNoteList.html');
        exit();
    }
}
