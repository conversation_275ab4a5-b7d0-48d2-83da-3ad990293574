<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');
include('../class/clsJournal.php');
include('../class/clsRotation.php');
include('../class/clsLocations.php');
include('../setRequest.php');
include('../class/clsActivitysheet.php');
include('../class/clsExternalPreceptors.php');
include('../class/clsStudent.php');


$selrotationId = 0;
$rotationId = 0;
$selectedStudentId = 0;
$studentId = 0;
$TimeZone =  $_SESSION["loggedUserSchoolTimeZone"];
$loggedAsBackUserId = isset($_SESSION["loggedAsBackUserId"]) ? $_SESSION["loggedAsBackUserId"] : 0;
$loggedUserId = isset($_SESSION["loggedUserId"]) ? $_SESSION["loggedUserId"] : 0;

$rotationId = isset($_GET['rotationId']) ? DecodeQueryData($_GET['rotationId']) : 0;

//For Student
if (isset($_GET['studentId'])) {
    $studentId = DecodeQueryData($_GET['studentId']);
    $selectedStudentId = DecodeQueryData($_GET['studentId']);
}
//Get All Activity Sheet List
$objJournal = new clsActivitysheet();
$rowsActivitySheetData = $objJournal->GetAllActivitySheet($currentSchoolId, $studentId, 0, $rotationId);
$totalActivityCount = 0;
if ($rowsActivitySheetData != '') {
    $totalActivityCount = mysqli_num_rows($rowsActivitySheetData);
}
unset($objJournal);


$status = isset($_GET["status"]) ? $_GET["status"] : '';
$rotationStatus = checkRotationStatus($rotationId);

$objStudent = new clsStudent();
$rowsStudents = $objStudent->GetSingleStudent($currentSchoolId, $studentId);
$studentfullname = isset($rowsStudents['firstName'], $rowsStudents['lastName']) ? $rowsStudents['firstName'] . ' ' . $rowsStudents['lastName'] : '';

$objrotation = new clsRotation();

//Get Rotation in Dropdown   
$rotation = $objrotation->GetActiveRotationBySchool($currentSchoolId, $selectedStudentId);

//For Student in Dropdown	
$Students = $objStudent->GetAllSchoolStudents($currentSchoolId);
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title>Activity Sheet</title>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <?php include('includes/headercss.php'); ?>
    <?php include("includes/datatablecss.php") ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">


    <style>
        .input-group-addon {
            position: absolute;
            right: 7px;
            /* width: 100%; */
            z-index: 99;
            width: 35px;
            margin: auto;
            top: 5px;
            border-left: 1px solid #ccc;
            border-radius: 50% !important;
            padding: 10px -2px;
            height: 35px;
            /* background: #01A750; */
            /* color: #fff; */
            color: #555;
            background: #f6f9f9;
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .panel-default>.panel-heading {
            background-color: transparent !important;
        }

        .btn-success,
        .btn-default {
            padding: 8px 25px;
            border-radius: 10px;
        }

        .panel {
            border-radius: 14px !important;
        }

        .input-group-addon {
            position: absolute;
            right: 7px;
            /* width: 100%; */
            z-index: 99;
            width: 35px;
            margin: auto;
            top: 5px;
            border-left: 1px solid #ccc;
            border-radius: 50% !important;
            padding: 10px -2px;
            height: 35px;
            /* background: #01A750; */
            /* color: #fff; */
            color: #555;
            background: #f6f9f9;
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .panel-default>.panel-heading {
            background-color: transparent !important;
        }

        .btn-success,
        .btn-default {
            padding: 8px 25px;
            border-radius: 10px;
        }

        .panel {
            border-radius: 14px !important;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 45px !important;
        }

        .required-select2 {
            border-left: solid 3px red !important;
            border-radius: 12px !important;
        }

        .select2-container--default .select2-selection--single {
            background-color: #f6f9f9 !important;
            cursor: default !important;
            height: 45px !important;
            border-radius: 10px !important;
        }

        .select2-container--default .select2-selection--single {
            border: none !important;
        }

        .panel,
        .form-group {
            margin-bottom: 10px;
        }

        .bootstrap-datetimepicker-widget {
            border-radius: 12px !important;
        }

        .form-control {
            height: 45px;
        }

        .input-group {
            width: 100%;
        }

        /* .form-horizontal .form-group {
                margin-right: 0;
                margin-left: 0;
            } */

        @media screen and (max-width: 500px) {
            .panel-body ol {
                padding-left: 20px;
            }

            .panel-default>.panel-heading {
                padding-left: 5px;
            }

            /* .container-zero{
                    padding: 0;
                } */

            div.dataTables_wrapper div.dataTables_length {
                text-align: left !important;
                margin-top: 10px;
            }
        }
    </style>
</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="studentListForAdmin.html?type=activitysheet">Student List</a></li>
                    <li class="active"><?php echo $studentfullname; ?> </li>
                    <li class="active">Activity Sheet</li>
                </ol>
            </div>
        </div>
    </div>

    <div class="container">
        <?php
        if (isset($_GET["status"])) {
            $message = '';
            $alertType = 'success';

            switch ($_GET["status"]) {
                case "added":
                    $message = "Activity Sheet added successfully.";
                    break;
                case "updated":
                    $message = "Activity Sheet updated successfully.";
                    break;
                case "Error":
                    $message = "Error occurred.";
                    $alertType = 'danger';
                    break;
            }

            if ($message) {
                echo '
            <div class="alert alert-' . $alertType . ' alert-dismissible fade in" role="alert">
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>' . $message . '
            </div>';
            }
        }
        ?>

        <div id="divTopLoading">Loading...</div>

        <div class="col-md-2 pull-right padding_right_zero">
            <select studentId="<?php echo EncodeQueryData($currentstudentId); ?>" id="cborotation" name="cborotation" class="form-control input-md select2_single redirectUrl" Type="<?php echo ($Type); ?>">
                <option value="" selected>Select</option>
                <?php
                if ($rotation != "") {
                    while ($row = mysqli_fetch_assoc($rotation)) {
                        $selrotationId  = $row['rotationId'];

                        $name  = stripslashes($row['title']);

                ?>
                        <option value="<?php echo (EncodeQueryData($selrotationId)); ?>" <?php if ($rotationId == $selrotationId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                <?php
                    }
                }
                ?>
            </select>
        </div>
        <label class=" control-label  pull-right mt1" for="cborotation" style="margin-top:8px;margin-bottom:20px">Rotation:</label>

        <div class="col-md-2 pull-right padding_right_zero" style="margin-right: 10px;">
            <select id="cboStudent" name="cboStudent" class="form-control input-md required-input select2_single redirectUrl" Type='<?php echo $Type; ?>'>
                <option value="" selected>Select</option>
                <?php
                if ($Students != "") {
                    while ($row = mysqli_fetch_assoc($Students)) {

                        $studentsId  = $row['studentId'];
                        $firstName = $row['firstName'];
                        $lastName = $row['lastName'];
                        $fullName  = $firstName . ' ' . $lastName;

                ?>
                        <option value="<?php echo EncodeQueryData($studentsId); ?>" <?php if ($selectedStudentId == $studentsId) { ?> selected="true" <?php } ?>><?php echo ($fullName); ?></option>
                <?php
                    }
                }
                ?>
            </select>
        </div>
        <label class=" control-label  pull-right mt1" for="cbostudent" style="margin-top:8px;margin-bottom:20px">Student:</label>
        <br /> <br><br />

        <table id="datatable-responsive" class="table table-bordered nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr>

                    <th>Date</th>
                    <th>Student </th>
                    <th>Rotation </th>
                    <th style="text-align: center">Clinician/Preceptor </th>
                    <th style="text-align: center">Amount of<br>Time Spent </th>
                    <th style="text-align: center">Points <br> Awarded</th>
                    <th style="text-align: center">Procedure <br> Count</th>
                    <th style="text-align: center">Clinician/Preceptor <br> Response</th>
                    <th>Clinician/Preceptor <br> Sign Date </th>
                    <th>School Sign Date </th>
                    <th style="text-align: center">Action</th>
                </tr>
            </thead>
            <tbody>
                <?php
                if ($totalActivityCount > 0) {
                    while ($row = mysqli_fetch_array($rowsActivitySheetData)) {

                        $activityId = ($row['activityId']);
                        $activityDate = stripslashes($row['activityDate']);
                        $rotationName = stripslashes($row['rotationName']);
                        $isSendToPreceptor = stripslashes($row['isSendToPreceptor']);
                        $externalPreceptorId = stripslashes($row['preceptorId']);
                        $clinicianName = stripslashes($row['clinicianName']);
                        $studentName = stripslashes($row['studentName']);

                        $rotationId = $row['rotationId'];
                        $courselocationId = $row['locationId'];
                        $parentRotationId = stripslashes($row['parentRotationId']);
                        $rotationLocationId = stripslashes($row['rotationLocationId']);
                        $interactionId = stripslashes($row['interactionId']);

                        $locationId = 0;
                        if ($rotationLocationId != $courselocationId && $parentRotationId > 0) {
                            if ($parentRotationId > 0) {
                                if (!$rotationLocationId)
                                    $locationId = $objrotation->GetLocationByRotation($rotationId);
                                else
                                    $locationId  = $rotationLocationId;
                            }
                        } else {
                            $locationId  = $courselocationId;
                        }

                        // Get Time Zone By Rotation 
                        $objLocation = new clsLocations();
                        $TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
                        unset($objLocation);
                        if ($TimeZone == '')
                            $TimeZone = $_SESSION["loggedUserSchoolTimeZone"];

                        // $activityDate = converFromServerTimeZone($activityDate, $TimeZone);
                        $activityDate = date("m/d/Y", strtotime($activityDate));
                        $schoolComment = stripslashes($row['schoolComment']);
                        $schoolSignatureDate = stripslashes($row['schoolSignatureDate']);

                        if ($schoolSignatureDate != '12/31/1969' && $schoolSignatureDate != '' && $schoolSignatureDate != '11/30/-0001' && $schoolSignatureDate != '0000-00-00') {

                            $schoolSignatureDate = date("m/d/Y", strtotime($schoolSignatureDate));
                        } else
                            $schoolSignatureDate = '-';

                        $clinicianComment = stripslashes($row['clinicianComment']);
                        $clinicianSignatureDate = stripslashes($row['clinicianSignatureDate']);

                        if ($clinicianSignatureDate != '12/31/1969' && $clinicianSignatureDate != '' && $clinicianSignatureDate != '11/30/-0001' && $clinicianSignatureDate != '0000-00-00') {

                            // $clinicianSignatureDate = converFromServerTimeZone($clinicianSignatureDate, $TimeZone);
                            $clinicianSignatureDate = date("m/d/Y", strtotime($clinicianSignatureDate));
                        } else
                            $clinicianSignatureDate = '-';


                        if ($clinicianComment != '') {
                            $Response = 'Yes';
                        } else {
                            $Response = 'No';
                        }
                        $isCompletedStatus = '';
                        $objExternalPreceptors = new clsExternalPreceptors();

                        $isPreceptorCompletedStatus  = stripslashes($row['isPreceptorCompletedStatus']);

                        // if ($isPreceptorCompletedStatus) {
                        $externalPreceptorDetail = $objExternalPreceptors->GetExternalPreceptorDetail($externalPreceptorId);
                        $preceptorId = isset($externalPreceptorDetail['id']) ? $externalPreceptorDetail['id'] : 0;
                        $preceptorFirstName = isset($externalPreceptorDetail['firstName']) ? $externalPreceptorDetail['firstName'] : '';
                        $preceptorLastName = isset($externalPreceptorDetail['lastName']) ? $externalPreceptorDetail['lastName'] : '';
                        $preceptorFullName = $preceptorFirstName . ' ' . $preceptorLastName;
                        $preceptorMobileNum = isset($externalPreceptorDetail['mobile_num']) ? $externalPreceptorDetail['mobile_num'] : '';
                        $formatted_number = '';
                        if ($preceptorMobileNum != '') {
                            $mobileNum = str_replace("-", "", $preceptorMobileNum);

                            $masked_number = substr_replace($mobileNum, "XXXXXX", 0, 6);
                            $formatted_number = substr($masked_number, 0, 3) . "-" . substr($masked_number, 3, 3) . "-" . substr($mobileNum, 6);
                        }
                        $isCompletedStatus = $isPreceptorCompletedStatus ? 'Completed' : 'Pending';
                        // }

                        // Get Interaction Time spent and points awarded
                        $objDB = new clsDB();
                        $interactionDetails = $objDB->GetSelectedColumnsFromTable('interaction', ["timeSpent", "pointsAwarded"], 'interactionId', $interactionId);
                        unset($objDB);
                        $pointsAwarded = isset($interactionDetails['pointsAwarded']) ? $interactionDetails['pointsAwarded'] : '';
                        $timeSpent = isset($interactionDetails['timeSpent']) ? $interactionDetails['timeSpent'] : '';

                        $procedurecountIds = isset($row['procedurecountId']) ? $row['procedurecountId'] : '';

                ?>
                        <tr>

                            <td>
                                <?php echo ($activityDate);
                                ?>
                            </td>

                            <td>
                                <?php echo ($studentName);
                                ?>
                            </td>
                            <td>
                                <?php echo ($rotationName);
                                ?>
                            </td>
                            <td class="<?php if ($preceptorMobileNum == 0) {
                                            echo 'text-center';
                                        }  ?>">
                                <?php if ($isSendToPreceptor) { ?>
                                    Name: <?php echo $preceptorFullName; ?> <br>
                                    Phone: <?php echo $formatted_number; ?> <br>
                                    Status: <?php echo $isCompletedStatus; ?> <br>
                                <?php
                                } else { ?>
                                    <?php echo $clinicianName; ?>
                                <?php } ?>
                            </td>
                            <td align="center">
                                <?php echo $timeSpent; ?>
                            </td>
                            <td align="center">
                                <?php echo $pointsAwarded; ?>
                            </td>
                            <td align="center">
                                <?php if ($procedurecountIds != '') { ?>
                                    <a href="javascript:void(0);" class="getProcedureCount" activityId="<?php echo EncodeQueryData($activityId) ?>">Yes</a>
                                <?php } else { ?>
                                    No
                                <?php } ?>

                            </td>
                            <td align="center">
                                <?php echo ($Response); ?>
                            </td>
                            <td align="center">
                                <?php echo ($clinicianSignatureDate); ?>
                            </td>

                            <td align="center">
                                <?php echo ($schoolSignatureDate); ?>
                            </td>
                            <td style="text-align: center">
                                <?php
                                $rotationStatus = checkRotationStatus($rotationId);
                                if ($schoolSignatureDate != '-' ||  $rotationStatus) { ?>
                                    <a href="activitySheet.html?editactivityid=<?php echo (EncodeQueryData($activityId)); ?>&studentId=<?php echo (EncodeQueryData($selectedStudentId)); ?>&view=V">View</a>
                                <?php } else { ?>
                                    <a href="activitySheet.html?editactivityid=<?php echo (EncodeQueryData($activityId)); ?>&studentId=<?php echo (EncodeQueryData($selectedStudentId)); ?>">Edit</a>
                                <?php } ?>
                                <?php if ($loggedAsBackUserId) {  ?>
                                    | <a href="javascript:void(0);" class="deleteAjaxRow" activityId="<?php echo EncodeQueryData($activityId); ?>" studentId="<?php echo ($selectedStudentId); ?>">Delete</a>

                                <?php }  ?>
                            </td>
                        </tr>
                <?php
                    }
                }
                ?>
            </tbody>

        </table>
    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <?php include('../includes/activitysheetProcedureCount.php'); ?>

    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>

    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        $(window).load(function() {
            $("#divTopLoading").addClass('hide');
            $(".select2_single").select2();

            $(".redirectUrl").change(function() {

                var selectedStudentId = $('#cboStudent').val();
                var rotationId = $('#cborotation').val();
                Url = "activitysheetlist.html?";
                if (selectedStudentId != '') {
                    Url = Url + "studentId=" + selectedStudentId;
                }

                if (rotationId != '') {
                    Url = Url + "&rotationId=" + rotationId;
                }
                window.location.href = Url;

            });
        });

        var current_datatable = $("#datatable-responsive").DataTable({
            "scrollX": true,
            "responsive": false,
            "ordering": true,
            "order": [
                [0, "desc"]
            ],
            "columns": [{
                    "width": "10%"
                },
                {
                    "width": "10%",
                    "orderable": false
                },
                {
                    "width": "10%",
                    "className": "alignCenter"
                },
                {
                    "width": "15%",
                    "className": "alignCenter"
                },
                {
                    "width": "10%",
                    "className": "alignCenter",
                    "orderable": false
                },
                {
                    "width": "10%",
                    "className": "alignCenter",
                    "orderable": false
                },
                {
                    "width": "10%",
                    "className": "alignCenter",
                    "orderable": false
                },
                {
                    "width": "15%",
                    "className": "alignCenter"
                },
                {
                    "sWidth": "15%",
                    "sClass": "alignCenter"

                }, {
                    "sWidth": "15%",
                    "sClass": "alignCenter"

                },
                {
                    "width": "10%",
                    "className": "alignCenter",
                    "orderable": false
                }
            ]
        });

        // ajax call for deleteAjaxRow
        $(document).on('click', '.deleteAjaxRow', function() {
            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var activityId = $(this).attr('activityId');
            // var title = $(this).attr('studentName');
            var userId = '<?php echo EncodeQueryData($loggedUserId); ?>';
            var isUser = 1; //for Admin
            alertify.confirm('Activity Sheet: ', 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: activityId,
                        userId: userId,
                        isUser: isUser,
                        type: 'ActivitySheet'
                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        alertify.success('Deleted');
                    }
                });
            }, function() {});
        });
    </script>
</body>

</html>