{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "1ebf14b52203ea594cd6c9d577978f79", "packages": [{"name": "clicksend/clicksend-php", "version": "v5.0.81", "source": {"type": "git", "url": "https://github.com/ClickSend/clicksend-php.git", "reference": "d642c1cbde5e7e9e247fb65adc2eeec4019a4f82"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ClickSend/clicksend-php/zipball/d642c1cbde5e7e9e247fb65adc2eeec4019a4f82", "reference": "d642c1cbde5e7e9e247fb65adc2eeec4019a4f82", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "guzzlehttp/guzzle": "^6.2|^7.3", "php": ">=5.5"}, "require-dev": {"friendsofphp/php-cs-fixer": "~2.12", "phpunit/phpunit": "^4.8", "squizlabs/php_codesniffer": "~2.6"}, "type": "library", "autoload": {"psr-4": {"ClickSend\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["proprietary"], "authors": [{"name": "Swagger and contributors", "homepage": "https://github.com/swagger-api/swagger-codegen"}], "homepage": "http://swagger.io", "keywords": ["api", "php", "sdk", "swagger"], "support": {"issues": "https://github.com/ClickSend/clicksend-php/issues", "source": "https://github.com/ClickSend/clicksend-php/tree/v5.0.81"}, "time": "2025-03-19T01:23:20+00:00"}, {"name": "clue/stream-filter", "version": "v1.7.0", "source": {"type": "git", "url": "https://github.com/clue/stream-filter.git", "reference": "049509fef80032cb3f051595029ab75b49a3c2f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/clue/stream-filter/zipball/049509fef80032cb3f051595029ab75b49a3c2f7", "reference": "049509fef80032cb3f051595029ab75b49a3c2f7", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"Clue\\StreamFilter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A simple and modern approach to stream filtering in PHP", "homepage": "https://github.com/clue/stream-filter", "keywords": ["bucket brigade", "callback", "filter", "php_user_filter", "stream", "stream_filter_append", "stream_filter_register"], "support": {"issues": "https://github.com/clue/stream-filter/issues", "source": "https://github.com/clue/stream-filter/tree/v1.7.0"}, "funding": [{"url": "https://clue.engineering/support", "type": "custom"}, {"url": "https://github.com/clue", "type": "github"}], "time": "2023-12-20T15:40:13+00:00"}, {"name": "graham-campbell/result-type", "version": "v1.0.4", "source": {"type": "git", "url": "https://github.com/GrahamCampbell/Result-Type.git", "reference": "0690bde05318336c7221785f2a932467f98b64ca"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/GrahamCampbell/Result-Type/zipball/0690bde05318336c7221785f2a932467f98b64ca", "reference": "0690bde05318336c7221785f2a932467f98b64ca", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "phpoption/phpoption": "^1.8"}, "require-dev": {"phpunit/phpunit": "^6.5.14 || ^7.5.20 || ^8.5.19 || ^9.5.8"}, "type": "library", "autoload": {"psr-4": {"GrahamCampbell\\ResultType\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "An Implementation Of The Result Type", "keywords": ["<PERSON>", "Graham<PERSON><PERSON><PERSON>", "Result Type", "Result-Type", "result"], "support": {"issues": "https://github.com/GrahamCampbell/Result-Type/issues", "source": "https://github.com/GrahamCampbell/Result-Type/tree/v1.0.4"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/graham-campbell/result-type", "type": "tidelift"}], "time": "2021-11-21T21:41:47+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.9.3", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "7b2f29fe81dc4da0ca0ea7d42107a0845946ea77"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/7b2f29fe81dc4da0ca0ea7d42107a0845946ea77", "reference": "7b2f29fe81dc4da0ca0ea7d42107a0845946ea77", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.3", "guzzlehttp/psr7": "^2.7.0", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "guzzle/client-integration-tests": "3.0.2", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.39 || ^9.6.20", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.9.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2025-03-27T13:37:11+00:00"}, {"name": "guzzlehttp/promises", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "7c69f28996b0a6920945dd20b3857e499d9ca96c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/7c69f28996b0a6920945dd20b3857e499d9ca96c", "reference": "7c69f28996b0a6920945dd20b3857e499d9ca96c", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.2.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2025-03-27T13:27:01+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.7.1", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/c2270caaabe631b3b44c85f99e5a04bbb8060d16", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "0.9.0", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.7.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2025-03-27T12:30:47+00:00"}, {"name": "mailgun/mailgun-php", "version": "v4.3.5", "source": {"type": "git", "url": "https://github.com/mailgun/mailgun-php.git", "reference": "****************************************"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mailgun/mailgun-php/zipball/****************************************", "reference": "****************************************", "shasum": ""}, "require": {"php": "^7.4 || ^8.0", "php-http/client-common": "^2.2.1", "php-http/discovery": "^1.19", "php-http/multipart-stream-builder": "^1.1.2", "psr/http-client": "^1.0", "webmozart/assert": "^1.9.1"}, "require-dev": {"ergebnis/composer-normalize": "^2.43", "nyholm/nsa": "^1.2.1", "nyholm/psr7": "^1.3.1", "phpcompatibility/php-compatibility": "^9.3", "phpunit/phpunit": "^9.3", "squizlabs/php_codesniffer": "^3.7", "symfony/http-client": "^5.4 || ^6.3", "vimeo/psalm": "^4.0 || ^5.25"}, "suggest": {"nyholm/psr7": "PSR-7 message implementation", "symfony/http-client": "HTTP client"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"Mailgun\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Mailgun SDK provides methods for all API functions.", "support": {"issues": "https://github.com/mailgun/mailgun-php/issues", "source": "https://github.com/mailgun/mailgun-php/tree/v4.3.5"}, "time": "2025-04-06T12:36:45+00:00"}, {"name": "markbaker/complex", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPComplex.git", "reference": "c3131244e29c08d44fefb49e0dd35021e9e39dd2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPComplex/zipball/c3131244e29c08d44fefb49e0dd35021e9e39dd2", "reference": "c3131244e29c08d44fefb49e0dd35021e9e39dd2", "shasum": ""}, "require": {"php": "^5.6.0|^7.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.5.0", "phpcompatibility/php-compatibility": "^9.0", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "^4.0|^5.0|^6.0|^7.0", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^4.8.35|^5.0|^6.0|^7.0", "sebastian/phpcpd": "2.*", "squizlabs/php_codesniffer": "^3.4.0"}, "type": "library", "autoload": {"files": ["classes/src/functions/abs.php", "classes/src/functions/acos.php", "classes/src/functions/acosh.php", "classes/src/functions/acot.php", "classes/src/functions/acoth.php", "classes/src/functions/acsc.php", "classes/src/functions/acsch.php", "classes/src/functions/argument.php", "classes/src/functions/asec.php", "classes/src/functions/asech.php", "classes/src/functions/asin.php", "classes/src/functions/asinh.php", "classes/src/functions/atan.php", "classes/src/functions/atanh.php", "classes/src/functions/conjugate.php", "classes/src/functions/cos.php", "classes/src/functions/cosh.php", "classes/src/functions/cot.php", "classes/src/functions/coth.php", "classes/src/functions/csc.php", "classes/src/functions/csch.php", "classes/src/functions/exp.php", "classes/src/functions/inverse.php", "classes/src/functions/ln.php", "classes/src/functions/log2.php", "classes/src/functions/log10.php", "classes/src/functions/negative.php", "classes/src/functions/pow.php", "classes/src/functions/rho.php", "classes/src/functions/sec.php", "classes/src/functions/sech.php", "classes/src/functions/sin.php", "classes/src/functions/sinh.php", "classes/src/functions/sqrt.php", "classes/src/functions/tan.php", "classes/src/functions/tanh.php", "classes/src/functions/theta.php", "classes/src/operations/add.php", "classes/src/operations/subtract.php", "classes/src/operations/multiply.php", "classes/src/operations/divideby.php", "classes/src/operations/divideinto.php"], "psr-4": {"Complex\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with complex numbers", "homepage": "https://github.com/MarkBaker/PHPComplex", "keywords": ["complex", "mathematics"], "support": {"issues": "https://github.com/MarkBaker/PHPComplex/issues", "source": "https://github.com/MarkBaker/PHPComplex/tree/1.5.0"}, "time": "2020-08-26T19:47:57+00:00"}, {"name": "markbaker/matrix", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPMatrix.git", "reference": "44bb1ab01811116f01fe216ab37d921dccc6c10d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPMatrix/zipball/44bb1ab01811116f01fe216ab37d921dccc6c10d", "reference": "44bb1ab01811116f01fe216ab37d921dccc6c10d", "shasum": ""}, "require": {"php": "^5.6.0|^7.0.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "dev-master", "phploc/phploc": "^4", "phpmd/phpmd": "dev-master", "phpunit/phpunit": "^5.7|^6.0|7.0", "sebastian/phpcpd": "^3.0", "squizlabs/php_codesniffer": "^3.0@dev"}, "type": "library", "autoload": {"files": ["classes/src/Functions/adjoint.php", "classes/src/Functions/antidiagonal.php", "classes/src/Functions/cofactors.php", "classes/src/Functions/determinant.php", "classes/src/Functions/diagonal.php", "classes/src/Functions/identity.php", "classes/src/Functions/inverse.php", "classes/src/Functions/minors.php", "classes/src/Functions/trace.php", "classes/src/Functions/transpose.php", "classes/src/Operations/add.php", "classes/src/Operations/directsum.php", "classes/src/Operations/subtract.php", "classes/src/Operations/multiply.php", "classes/src/Operations/divideby.php", "classes/src/Operations/divideinto.php"], "psr-4": {"Matrix\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with matrices", "homepage": "https://github.com/MarkBaker/PHPMatrix", "keywords": ["mathematics", "matrix", "vector"], "support": {"issues": "https://github.com/MarkBaker/PHPMatrix/issues", "source": "https://github.com/MarkBaker/PHPMatrix/tree/1.2.3"}, "time": "2021-01-26T14:36:01+00:00"}, {"name": "nyholm/psr7", "version": "1.8.2", "source": {"type": "git", "url": "https://github.com/Nyholm/psr7.git", "reference": "a71f2b11690f4b24d099d6b16690a90ae14fc6f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Nyholm/psr7/zipball/a71f2b11690f4b24d099d6b16690a90ae14fc6f3", "reference": "a71f2b11690f4b24d099d6b16690a90ae14fc6f3", "shasum": ""}, "require": {"php": ">=7.2", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0"}, "provide": {"php-http/message-factory-implementation": "1.0", "psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"http-interop/http-factory-tests": "^0.9", "php-http/message-factory": "^1.0", "php-http/psr7-integration-tests": "^1.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.4", "symfony/error-handler": "^4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.8-dev"}}, "autoload": {"psr-4": {"Nyholm\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "marti<PERSON>@vanderven.se"}], "description": "A fast PHP7 implementation of PSR-7", "homepage": "https://tnyholm.se", "keywords": ["psr-17", "psr-7"], "support": {"issues": "https://github.com/Nyholm/psr7/issues", "source": "https://github.com/Nyholm/psr7/tree/1.8.2"}, "funding": [{"url": "https://github.com/Zegnat", "type": "github"}, {"url": "https://github.com/nyholm", "type": "github"}], "time": "2024-09-09T07:06:30+00:00"}, {"name": "php-http/client-common", "version": "2.7.2", "source": {"type": "git", "url": "https://github.com/php-http/client-common.git", "reference": "0cfe9858ab9d3b213041b947c881d5b19ceeca46"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/client-common/zipball/0cfe9858ab9d3b213041b947c881d5b19ceeca46", "reference": "0cfe9858ab9d3b213041b947c881d5b19ceeca46", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "php-http/httplug": "^2.0", "php-http/message": "^1.6", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0 || ^2.0", "symfony/options-resolver": "~4.0.15 || ~4.1.9 || ^4.2.1 || ^5.0 || ^6.0 || ^7.0", "symfony/polyfill-php80": "^1.17"}, "require-dev": {"doctrine/instantiator": "^1.1", "guzzlehttp/psr7": "^1.4", "nyholm/psr7": "^1.2", "phpspec/phpspec": "^5.1 || ^6.3 || ^7.1", "phpspec/prophecy": "^1.10.2", "phpunit/phpunit": "^7.5.20 || ^8.5.33 || ^9.6.7"}, "suggest": {"ext-json": "To detect JSON responses with the ContentTypePlugin", "ext-libxml": "To detect XML responses with the ContentTypePlugin", "php-http/cache-plugin": "PSR-6 Cache plugin", "php-http/logger-plugin": "PSR-3 Logger plugin", "php-http/stopwatch-plugin": "Symfony Stopwatch plugin"}, "type": "library", "autoload": {"psr-4": {"Http\\Client\\Common\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common HTTP Client implementations and tools for HTTPlug", "homepage": "http://httplug.io", "keywords": ["client", "common", "http", "httplug"], "support": {"issues": "https://github.com/php-http/client-common/issues", "source": "https://github.com/php-http/client-common/tree/2.7.2"}, "time": "2024-09-24T06:21:48+00:00"}, {"name": "php-http/curl-client", "version": "2.3.3", "source": {"type": "git", "url": "https://github.com/php-http/curl-client.git", "reference": "f3eb48d266341afec0229a7a37a03521d3646b81"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/curl-client/zipball/f3eb48d266341afec0229a7a37a03521d3646b81", "reference": "f3eb48d266341afec0229a7a37a03521d3646b81", "shasum": ""}, "require": {"ext-curl": "*", "php": "^7.4 || ^8.0", "php-http/discovery": "^1.6", "php-http/httplug": "^2.0", "php-http/message": "^1.2", "psr/http-client": "^1.0", "psr/http-factory-implementation": "^1.0", "symfony/options-resolver": "^3.4 || ^4.0 || ^5.0 || ^6.0 || ^7.0"}, "provide": {"php-http/async-client-implementation": "1.0", "php-http/client-implementation": "1.0", "psr/http-client-implementation": "1.0"}, "require-dev": {"guzzlehttp/psr7": "^2.0", "laminas/laminas-diactoros": "^2.0 || ^3.0", "php-http/client-integration-tests": "^3.0", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^7.5 || ^9.4"}, "type": "library", "autoload": {"psr-4": {"Http\\Client\\Curl\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "m.<PERSON><PERSON><PERSON>@yandex.ru"}], "description": "PSR-18 and HTTPlug Async client with cURL", "homepage": "http://php-http.org", "keywords": ["curl", "http", "psr-18"], "support": {"issues": "https://github.com/php-http/curl-client/issues", "source": "https://github.com/php-http/curl-client/tree/2.3.3"}, "time": "2024-10-31T07:36:58+00:00"}, {"name": "php-http/discovery", "version": "1.20.0", "source": {"type": "git", "url": "https://github.com/php-http/discovery.git", "reference": "82fe4c73ef3363caed49ff8dd1539ba06044910d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/discovery/zipball/82fe4c73ef3363caed49ff8dd1539ba06044910d", "reference": "82fe4c73ef3363caed49ff8dd1539ba06044910d", "shasum": ""}, "require": {"composer-plugin-api": "^1.0|^2.0", "php": "^7.1 || ^8.0"}, "conflict": {"nyholm/psr7": "<1.0", "zendframework/zend-diactoros": "*"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "*", "psr/http-factory-implementation": "*", "psr/http-message-implementation": "*"}, "require-dev": {"composer/composer": "^1.0.2|^2.0", "graham-campbell/phpspec-skip-example-extension": "^5.0", "php-http/httplug": "^1.0 || ^2.0", "php-http/message-factory": "^1.0", "phpspec/phpspec": "^5.1 || ^6.1 || ^7.3", "sebastian/comparator": "^3.0.5 || ^4.0.8", "symfony/phpunit-bridge": "^6.4.4 || ^7.0.1"}, "type": "composer-plugin", "extra": {"class": "Http\\Discovery\\Composer\\Plugin", "plugin-optional": true}, "autoload": {"psr-4": {"Http\\Discovery\\": "src/"}, "exclude-from-classmap": ["src/Composer/Plugin.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Finds and installs PSR-7, PSR-17, PSR-18 and HTTPlug implementations", "homepage": "http://php-http.org", "keywords": ["adapter", "client", "discovery", "factory", "http", "message", "psr17", "psr7"], "support": {"issues": "https://github.com/php-http/discovery/issues", "source": "https://github.com/php-http/discovery/tree/1.20.0"}, "time": "2024-10-02T11:20:13+00:00"}, {"name": "php-http/guzzle7-adapter", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-http/guzzle7-adapter.git", "reference": "03a415fde709c2f25539790fecf4d9a31bc3d0eb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/guzzle7-adapter/zipball/03a415fde709c2f25539790fecf4d9a31bc3d0eb", "reference": "03a415fde709c2f25539790fecf4d9a31bc3d0eb", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^7.0", "php": "^7.3 | ^8.0", "php-http/httplug": "^2.0", "psr/http-client": "^1.0"}, "provide": {"php-http/async-client-implementation": "1.0", "php-http/client-implementation": "1.0", "psr/http-client-implementation": "1.0"}, "require-dev": {"php-http/client-integration-tests": "^3.0", "php-http/message-factory": "^1.1", "phpspec/prophecy-phpunit": "^2.0", "phpunit/phpunit": "^8.0|^9.3"}, "type": "library", "autoload": {"psr-4": {"Http\\Adapter\\Guzzle7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Guzzle 7 HTTP Adapter", "homepage": "http://httplug.io", "keywords": ["Guzzle", "http"], "support": {"issues": "https://github.com/php-http/guzzle7-adapter/issues", "source": "https://github.com/php-http/guzzle7-adapter/tree/1.1.0"}, "time": "2024-11-26T11:14:36+00:00"}, {"name": "php-http/httplug", "version": "2.4.1", "source": {"type": "git", "url": "https://github.com/php-http/httplug.git", "reference": "5cad731844891a4c282f3f3e1b582c46839d22f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/httplug/zipball/5cad731844891a4c282f3f3e1b582c46839d22f4", "reference": "5cad731844891a4c282f3f3e1b582c46839d22f4", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "php-http/promise": "^1.1", "psr/http-client": "^1.0", "psr/http-message": "^1.0 || ^2.0"}, "require-dev": {"friends-of-phpspec/phpspec-code-coverage": "^4.1 || ^5.0 || ^6.0", "phpspec/phpspec": "^5.1 || ^6.0 || ^7.0"}, "type": "library", "autoload": {"psr-4": {"Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Eric <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "HTTPlug, the HTTP client abstraction for PHP", "homepage": "http://httplug.io", "keywords": ["client", "http"], "support": {"issues": "https://github.com/php-http/httplug/issues", "source": "https://github.com/php-http/httplug/tree/2.4.1"}, "time": "2024-09-23T11:39:58+00:00"}, {"name": "php-http/message", "version": "1.16.2", "source": {"type": "git", "url": "https://github.com/php-http/message.git", "reference": "06dd5e8562f84e641bf929bfe699ee0f5ce8080a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message/zipball/06dd5e8562f84e641bf929bfe699ee0f5ce8080a", "reference": "06dd5e8562f84e641bf929bfe699ee0f5ce8080a", "shasum": ""}, "require": {"clue/stream-filter": "^1.5", "php": "^7.2 || ^8.0", "psr/http-message": "^1.1 || ^2.0"}, "provide": {"php-http/message-factory-implementation": "1.0"}, "require-dev": {"ergebnis/composer-normalize": "^2.6", "ext-zlib": "*", "guzzlehttp/psr7": "^1.0 || ^2.0", "laminas/laminas-diactoros": "^2.0 || ^3.0", "php-http/message-factory": "^1.0.2", "phpspec/phpspec": "^5.1 || ^6.3 || ^7.1", "slim/slim": "^3.0"}, "suggest": {"ext-zlib": "Used with compressor/decompressor streams", "guzzlehttp/psr7": "Used with Guzzle PSR-7 Factories", "laminas/laminas-diactoros": "Used with Diactoros Factories", "slim/slim": "Used with Slim Framework PSR-7 implementation"}, "type": "library", "autoload": {"files": ["src/filters.php"], "psr-4": {"Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "HTTP Message related tools", "homepage": "http://php-http.org", "keywords": ["http", "message", "psr-7"], "support": {"issues": "https://github.com/php-http/message/issues", "source": "https://github.com/php-http/message/tree/1.16.2"}, "time": "2024-10-02T11:34:13+00:00"}, {"name": "php-http/multipart-stream-builder", "version": "1.4.2", "source": {"type": "git", "url": "https://github.com/php-http/multipart-stream-builder.git", "reference": "10086e6de6f53489cca5ecc45b6f468604d3460e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/multipart-stream-builder/zipball/10086e6de6f53489cca5ecc45b6f468604d3460e", "reference": "10086e6de6f53489cca5ecc45b6f468604d3460e", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "php-http/discovery": "^1.15", "psr/http-factory-implementation": "^1.0"}, "require-dev": {"nyholm/psr7": "^1.0", "php-http/message": "^1.5", "php-http/message-factory": "^1.0.2", "phpunit/phpunit": "^7.5.15 || ^8.5 || ^9.3"}, "type": "library", "autoload": {"psr-4": {"Http\\Message\\MultipartStream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A builder class that help you create a multipart stream", "homepage": "http://php-http.org", "keywords": ["factory", "http", "message", "multipart stream", "stream"], "support": {"issues": "https://github.com/php-http/multipart-stream-builder/issues", "source": "https://github.com/php-http/multipart-stream-builder/tree/1.4.2"}, "time": "2024-09-04T13:22:54+00:00"}, {"name": "php-http/promise", "version": "1.3.1", "source": {"type": "git", "url": "https://github.com/php-http/promise.git", "reference": "fc85b1fba37c169a69a07ef0d5a8075770cc1f83"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/promise/zipball/fc85b1fba37c169a69a07ef0d5a8075770cc1f83", "reference": "fc85b1fba37c169a69a07ef0d5a8075770cc1f83", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"friends-of-phpspec/phpspec-code-coverage": "^4.3.2 || ^6.3", "phpspec/phpspec": "^5.1.2 || ^6.2 || ^7.4"}, "type": "library", "autoload": {"psr-4": {"Http\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Promise used for asynchronous HTTP requests", "homepage": "http://httplug.io", "keywords": ["promise"], "support": {"issues": "https://github.com/php-http/promise/issues", "source": "https://github.com/php-http/promise/tree/1.3.1"}, "time": "2024-03-15T13:55:21+00:00"}, {"name": "phpoffice/phpspreadsheet", "version": "1.12.0", "source": {"type": "git", "url": "https://github.com/PHPOffice/PhpSpreadsheet.git", "reference": "f79611d6dc1f6b7e8e30b738fc371b392001dbfd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PhpSpreadsheet/zipball/f79611d6dc1f6b7e8e30b738fc371b392001dbfd", "reference": "f79611d6dc1f6b7e8e30b738fc371b392001dbfd", "shasum": ""}, "require": {"ext-ctype": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-zlib": "*", "markbaker/complex": "^1.4", "markbaker/matrix": "^1.2", "php": "^7.1", "psr/simple-cache": "^1.0"}, "require-dev": {"dompdf/dompdf": "^0.8.3", "friendsofphp/php-cs-fixer": "^2.16", "jpgraph/jpgraph": "^4.0", "mpdf/mpdf": "^8.0", "phpcompatibility/php-compatibility": "^9.3", "phpunit/phpunit": "^7.5", "squizlabs/php_codesniffer": "^3.5", "tecnickcom/tcpdf": "^6.3"}, "suggest": {"dompdf/dompdf": "Option for rendering PDF with PDF Writer", "jpgraph/jpgraph": "Option for rendering charts, or including charts with PDF or HTML Writers", "mpdf/mpdf": "Option for rendering PDF with PDF Writer", "tecnickcom/tcpdf": "Option for rendering PDF with PDF Writer"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\PhpSpreadsheet\\": "src/PhpSpreadsheet"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://blog.maartenballiauw.be"}, {"name": "<PERSON>", "homepage": "https://markbakeruk.net"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "description": "PHPSpreadsheet - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PhpSpreadsheet", "keywords": ["OpenXML", "excel", "gnumeric", "ods", "php", "spreadsheet", "xls", "xlsx"], "support": {"issues": "https://github.com/PHPOffice/PhpSpreadsheet/issues", "source": "https://github.com/PHPOffice/PhpSpreadsheet/tree/1.12.0"}, "time": "2020-04-27T08:12:48+00:00"}, {"name": "phpoption/phpoption", "version": "1.8.1", "source": {"type": "git", "url": "https://github.com/schmittjoh/php-option.git", "reference": "eab7a0df01fe2344d172bff4cd6dbd3f8b84ad15"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/php-option/zipball/eab7a0df01fe2344d172bff4cd6dbd3f8b84ad15", "reference": "eab7a0df01fe2344d172bff4cd6dbd3f8b84ad15", "shasum": ""}, "require": {"php": "^7.0 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "phpunit/phpunit": "^6.5.14 || ^7.5.20 || ^8.5.19 || ^9.5.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.8-dev"}}, "autoload": {"psr-4": {"PhpOption\\": "src/PhpOption/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>", "homepage": "https://github.com/schmitt<PERSON>h"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "Option Type for PHP", "keywords": ["language", "option", "php", "type"], "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/1.8.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpoption/phpoption", "type": "tidelift"}], "time": "2021-12-04T23:24:31+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "2.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/402d35bcb92c70c026d1a6a9883f06b2ead23d71", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/2.0"}, "time": "2023-04-04T09:54:51+00:00"}, {"name": "psr/simple-cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/master"}, "time": "2017-10-23T01:57:42+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "sendgrid/php-http-client", "version": "3.14.0", "source": {"type": "git", "url": "https://github.com/sendgrid/php-http-client.git", "reference": "7880d5aecc53856802130ba83af1dfcf942e9767"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sendgrid/php-http-client/zipball/7880d5aecc53856802130ba83af1dfcf942e9767", "reference": "7880d5aecc53856802130ba83af1dfcf942e9767", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "php": ">=5.6"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.16", "phpunit/phpunit": "^5.7 || ^6.5", "sebastian/version": "^1.0.6", "squizlabs/php_codesniffer": "~2.0"}, "suggest": {"composer/ca-bundle": "Including this library will ensure that a valid CA bundle is available for secure connections"}, "type": "library", "autoload": {"psr-4": {"SendGrid\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "HTTP REST client, simplified for PHP", "homepage": "http://github.com/sendgrid/php-http-client", "keywords": ["api", "fluent", "http", "rest", "sendgrid"], "support": {"issues": "https://github.com/sendgrid/php-http-client/issues", "source": "https://github.com/sendgrid/php-http-client/tree/3.14.0"}, "time": "2021-03-24T20:45:06+00:00"}, {"name": "sendgrid/sendgrid", "version": "7.11.1", "source": {"type": "git", "url": "https://github.com/sendgrid/sendgrid-php.git", "reference": "071eac6de115e8c0ead18a7ebdad7c19043f07e4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sendgrid/sendgrid-php/zipball/071eac6de115e8c0ead18a7ebdad7c19043f07e4", "reference": "071eac6de115e8c0ead18a7ebdad7c19043f07e4", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "ext-openssl": "*", "php": ">=5.6", "sendgrid/php-http-client": "~3.10", "starkbank/ecdsa": "0.*"}, "replace": {"sendgrid/sendgrid-php": "*"}, "require-dev": {"phpunit/phpunit": "^5.7.9 || ^6.4.3", "squizlabs/php_codesniffer": "3.*", "swaggest/json-diff": "^3.4"}, "type": "library", "autoload": {"psr-4": {"SendGrid\\Contacts\\": "lib/contacts/", "SendGrid\\EventWebhook\\": "lib/eventwebhook/", "SendGrid\\Helper\\": "lib/helper/", "SendGrid\\Mail\\": "lib/mail/", "SendGrid\\Stats\\": "lib/stats/"}, "classmap": ["lib/BaseSendGridClientInterface.php", "lib/SendGrid.php", "lib/TwilioEmail.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "This library allows you to quickly and easily send emails through Twilio SendGrid using PHP.", "homepage": "http://github.com/sendgrid/sendgrid-php", "keywords": ["email", "grid", "send", "sendgrid", "twi<PERSON>"], "support": {"issues": "https://github.com/sendgrid/sendgrid-php/issues", "source": "https://github.com/sendgrid/sendgrid-php/tree/7.11.1"}, "time": "2021-12-15T21:06:20+00:00"}, {"name": "starkbank/ecdsa", "version": "0.0.5", "source": {"type": "git", "url": "https://github.com/starkbank/ecdsa-php.git", "reference": "484bedac47bac4012dc73df91da221f0a66845cb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/starkbank/ecdsa-php/zipball/484bedac47bac4012dc73df91da221f0a66845cb", "reference": "484bedac47bac4012dc73df91da221f0a66845cb", "shasum": ""}, "require": {"php": ">=5.5"}, "type": "library", "autoload": {"files": ["src/ellipticcurve.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "StarkBank", "email": "<EMAIL>", "homepage": "https://starkbank.com", "role": "Developer"}], "description": "fast openSSL-compatible implementation of the Elliptic Curve Digital Signature Algorithm (ECDSA)", "homepage": "https://github.com/starkbank/ecdsa-php", "support": {"issues": "https://github.com/starkbank/ecdsa-php/issues", "source": "https://github.com/starkbank/ecdsa-php/tree/v0.0.5"}, "time": "2021-06-06T22:24:49+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v2.5.4", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "605389f2a7e5625f273b53960dc46aeaf9c62918"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/605389f2a7e5625f273b53960dc46aeaf9c62918", "reference": "605389f2a7e5625f273b53960dc46aeaf9c62918", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "2.5-dev"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v2.5.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/options-resolver", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/options-resolver.git", "reference": "74e5b6f0db3e8589e6cfd5efb317a1fc2bb52fb6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/options-resolver/zipball/74e5b6f0db3e8589e6cfd5efb317a1fc2bb52fb6", "reference": "74e5b6f0db3e8589e6cfd5efb317a1fc2bb52fb6", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php73": "~1.0", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an improved replacement for the array_replace PHP function", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "support": {"source": "https://github.com/symfony/options-resolver/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "0424dff1c58f028c451efff2045f5d92410bd540"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/0424dff1c58f028c451efff2045f5d92410bd540", "reference": "0424dff1c58f028c451efff2045f5d92410bd540", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "fd22ab50000ef01661e2a31d850ebaa297f8e03c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/fd22ab50000ef01661e2a31d850ebaa297f8e03c", "reference": "fd22ab50000ef01661e2a31d850ebaa297f8e03c", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-06-19T12:30:46+00:00"}, {"name": "symfony/polyfill-php73", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "0f68c03565dcaaf25a890667542e8bd75fe7e5bb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/0f68c03565dcaaf25a890667542e8bd75fe7e5bb", "reference": "0f68c03565dcaaf25a890667542e8bd75fe7e5bb", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "77fa7995ac1b21ab60769b7323d600a991a90433"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/77fa7995ac1b21ab60769b7323d600a991a90433", "reference": "77fa7995ac1b21ab60769b7323d600a991a90433", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T15:07:36+00:00"}, {"name": "vlucas/phpdotenv", "version": "v5.5.0", "source": {"type": "git", "url": "https://github.com/vlucas/phpdotenv.git", "reference": "1a7ea2afc49c3ee6d87061f5a233e3a035d0eae7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/1a7ea2afc49c3ee6d87061f5a233e3a035d0eae7", "reference": "1a7ea2afc49c3ee6d87061f5a233e3a035d0eae7", "shasum": ""}, "require": {"ext-pcre": "*", "graham-campbell/result-type": "^1.0.2", "php": "^7.1.3 || ^8.0", "phpoption/phpoption": "^1.8", "symfony/polyfill-ctype": "^1.23", "symfony/polyfill-mbstring": "^1.23.1", "symfony/polyfill-php80": "^1.23.1"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "ext-filter": "*", "phpunit/phpunit": "^7.5.20 || ^8.5.30 || ^9.5.25"}, "suggest": {"ext-filter": "Required to use the boolean validator."}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": true}, "branch-alias": {"dev-master": "5.5-dev"}}, "autoload": {"psr-4": {"Dotenv\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/vlucas"}], "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["dotenv", "env", "environment"], "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v5.5.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/vlucas/phpdotenv", "type": "tidelift"}], "time": "2022-10-16T01:01:54+00:00"}, {"name": "webmozart/assert", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": ""}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.11.0"}, "time": "2022-06-03T18:03:27+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": [], "platform-dev": [], "plugin-api-version": "2.2.0"}