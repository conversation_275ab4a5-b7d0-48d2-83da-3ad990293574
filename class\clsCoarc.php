<?php
class clsCoarc
{
	var $studentCoarcMasterId = 0;
	var $clinicanId = '';
	var $studentId = '';
	var $rotationId = '';
	var $createdBy = '';
	var $createdDate = '';
	var $updatedBy = '';
	var $updatedDate = '';
	var $schoolCoarcQuestionId = '';
	var $equipmentQuestionType = '';
	var $optionText = '';
	var $sectionMasterId = '';
	var $schoolCoarcQuestionType = '';
	var $schoolCoarcOptionAnswerText  = '';
	var $status  = '';
	var $schoolCoarcOptionValue = '';
	var $coarcSurveyMasterId = '';
	var $coarcId = '';
	var $evaluationDate = '';
	var $coARCEntryBaseProgramId = '';
	var $coARCSatelliteOptionProgramId = '';
	var $sponsoringInstitution = '';


	function SaveCoarcSurvey($studentCoarcMasterId = 0)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($studentCoarcMasterId > 0) {

			$sql = "UPDATE studentsitecoarcmaster SET 						
						 clinicanId = '" . addslashes($this->clinicanId) . "',					 
						 evaluationDate='" . addslashes($this->evaluationDate) . "',
						 sponsoringInstitution='" . addslashes($this->sponsoringInstitution) . "',
						 coARCSatelliteOptionProgramId='" . addslashes($this->coARCSatelliteOptionProgramId) . "',
						 coARCEntryBaseProgramId='" . addslashes($this->coARCEntryBaseProgramId) . "',
						 updatedBy = '" . addslashes($this->createdBy) . "',
						 updatedDate = '" . (date("Y-m-d h:i:s")) . "' 
						 Where studentCoarcMasterId= " . $studentCoarcMasterId;
			//echo 'update->'.$sql;exit;
			$objDB->ExecuteQuery($sql);
		} else {
			$sql = "INSERT INTO studentsitecoarcmaster (clinicanId, studentId,evaluationDate,
								sponsoringInstitution,coARCSatelliteOptionProgramId,
								coARCEntryBaseProgramId,createdBy, createdDate) 
				VALUES ('" . addslashes($this->clinicanId) . "',
						'" . addslashes($this->studentId) . "',						
						'" . addslashes($this->evaluationDate) . "',						 
						'" . addslashes($this->sponsoringInstitution) . "',						 
						'" . addslashes($this->coARCSatelliteOptionProgramId) . "',						 
						'" . addslashes($this->coARCEntryBaseProgramId) . "',						 
						'" . addslashes($this->createdBy) . "',
						'" . (date("Y-m-d h:i:s")) . "'						
						)";
			//echo '<hr>Insert->'.$sql;exit;
			$studentCoarcMasterId = $objDB->ExecuteInsertQuery($sql);
		}

		unset($objDB);
		return $studentCoarcMasterId;
	}

	function SaveStudentSiteCoarcSurvey($studentCoarcMasterId)
	{
		$objDB = new clsDB();
		$sql = '';
		// echo 'studentCoarcMasterId->'.$studentCoarcMasterId.'<br>'; exit;
		if ($studentCoarcMasterId > 0) {

			$sql = "UPDATE studentsitecoarcmaster SET 						
						 clinicanId = '" . addslashes($this->clinicanId) . "',					 
						 evaluationDate='" . addslashes($this->evaluationDate) . "',
						 sponsoringInstitution='" . addslashes($this->sponsoringInstitution) . "',
						 coARCSatelliteOptionProgramId='" . addslashes($this->coARCSatelliteOptionProgramId) . "',
						 coARCEntryBaseProgramId='" . addslashes($this->coARCEntryBaseProgramId) . "',
						 updatedBy = '" . addslashes($this->createdBy) . "',
						 updatedDate = '" . (date("Y-m-d h:i:s")) . "' 
						 Where studentCoarcMasterId= " . $studentCoarcMasterId;
			//echo 'update->'.$sql;exit;
			$objDB->ExecuteQuery($sql);
		} else {
			$sql = "INSERT INTO studentsitecoarcmaster (clinicanId, studentId,evaluationDate,
								sponsoringInstitution,coARCSatelliteOptionProgramId,
								coARCEntryBaseProgramId,coarcId,coarcSurveyMasterId,createdBy, createdDate) 
				VALUES ('" . addslashes($this->clinicanId) . "',
						'" . addslashes($this->studentId) . "',						
						'" . addslashes($this->evaluationDate) . "',						 
						'" . addslashes($this->sponsoringInstitution) . "',						 
						'" . addslashes($this->coARCSatelliteOptionProgramId) . "',						 
						'" . addslashes($this->coARCEntryBaseProgramId) . "',						 
						'" . addslashes($this->coarcId) . "',
						'" . addslashes($this->coarcSurveyMasterId) . "',						 
						'" . addslashes($this->createdBy) . "',
						'" . (date("Y-m-d h:i:s")) . "'						
						)";
			//echo '<hr>Insert->'.$sql;exit;
			$studentCoarcMasterId = $objDB->ExecuteInsertQuery($sql);
		}

		unset($objDB);
		return $studentCoarcMasterId;
	}
	function UpdateCoarcStatus($coarcSurveyMasterId, $studentId)
	{
		$objDB = new clsDB();
		$sql = '';
		$sql = "UPDATE studentcoarcmaster SET 
					status = '1'
					Where coarcSurveyMasterId= " . $coarcSurveyMasterId . " AND studentId=" . $studentId;
		//echo 'update->'.$sql;exit;
		$objDB->ExecuteQuery($sql);
		unset($objDB);
		return $coarcSurveyMasterId;
	}
	function DeleteStudentCoarcSurveyDetails($studentCoarcMasterId)
	{
		$objDB = new clsDB();

		$sql = "DELETE  FROM studentsitecoarcdetail WHERE studentCoarcMasterId=" . $studentCoarcMasterId;
		//ECHO 'DELETE->'.$sql;EXIT;
		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}
	function SaveStudentCoarcSurveyDetail()
	{
		$objDB = new clsDB();

		$sql = "INSERT INTO studentsitecoarcdetail (studentCoarcMasterId,schoolCoarcQuestionId,
								schoolCoarcOptionValue,schoolCoarcOptionAnswerText) 
					 VALUES ('" . ($this->studentCoarcMasterId) . "',
							 '" . ($this->schoolCoarcQuestionId) . "',
							 '" . ($this->schoolCoarcOptionValue) . "',
							 '" . addslashes($this->schoolCoarcOptionAnswerText) . "'
							 
							)";
		// echo 'INSERT2->'.$sql;exit;
		$studentFormativeDetailId = $objDB->ExecuteInsertQuery($sql);
		unset($objDB);
		return $studentFormativeDetailId;
	}

	function CopyAllEquipmentQuestionMaster($currentSchoolId)
	{
		$this->schoolId = $currentSchoolId;
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM  equipmentquestionmaster";
		//echo $sql.'<hr>';
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		if ($rows != "") {
			while ($row = mysqli_fetch_array($rows)) {
				$this->optionText = $row['optionText'];
				$this->equipmentQuestionType = $row['equipmentQuestionType'];
				$this->sectionMasterId = $row['sectionMasterId'];
				$this->SaveSchoolEquipmentQuestionMaster();
			}
		}
	}

	function SaveSchoolEquipmentQuestionMaster()
	{
		$objDB = new clsDB();
		$sql = "INSERT INTO schoolcoarcquestionmaster 
						(questionText,schoolCoarcQuestionType,schoolId,sectionMasterId) 
						 VALUES ('" . addslashes($this->questionText) . "',
								 '" . addslashes($this->equipmentQuestionType) . "',
								 '" . addslashes($this->schoolId) . "',
								 '" . addslashes($this->sectionMasterId) . "'
								 
								)";
		//echo $sql;exit;
		$schoolCoarcQuestionId = $objDB->ExecuteInsertQuery($sql);
	}

	function GetAllCoarcSurveyQuestionMaster($currentSchoolId, $sectionMasterId)
	{


		$objDB = new clsDB();
		$rows = "";
		$sql = "select schoolcoarcquestionmaster.* from 
						schoolcoarcquestionmaster
						LEFT JOIN schoolcoarcquestiondetail ON  schoolcoarcquestionmaster.schoolCoarcQuestionId
						=schoolcoarcquestiondetail .schoolCoarcQuestionId 																
						where schoolcoarcquestionmaster.sectionMasterId=" . $sectionMasterId;

		$sql .= " GROUP BY schoolcoarcquestionmaster.schoolCoarcQuestionId";
		// echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	// function GetAllCoarcSurveyQuestionMaster($currentSchoolId,$sectionMasterId)
	// {


	// 	$objDB = new clsDB();
	// 	$rows = "";				
	// 	$sql = "select schoolcoarcquestionmaster.* from 
	// 			schoolcoarcquestionmaster
	// 			LEFT JOIN schoolcoarcquestiondetail ON  schoolcoarcquestionmaster.schoolCoarcQuestionId
	// 			=schoolcoarcquestiondetail .schoolCoarcQuestionId
	// 			LEFT JOIN coarcsectionmaster ON coarcsectionmaster.sectionMasterId = schoolcoarcquestionmaster.sectionMasterId
	// 			where schoolcoarcquestionmaster.sectionMasterId=". $sectionMasterId;

	// 	$sql.=" GROUP BY schoolcoarcquestionmaster.schoolCoarcQuestionId 
	// 	ORDER BY 
	// 		CASE 
	// 			WHEN coarcsectionmaster.title = '3. LABORATORY RESOURCES' AND ASCII(questionText) BETWEEN ASCII('A') AND ASCII('Z') AND SUBSTRING(questionText, 2, 1) = '.' THEN 1
	// 			WHEN ASCII(questionText) BETWEEN ASCII('A') AND ASCII('Z') AND SUBSTRING(questionText, 2, 1) = '.' THEN 1
	// 			ELSE 2 
	// 		END, 
	// 		questionText
	// 						";
	// 	echo $sql;		
	// 	$rows = $objDB->GetResultset($sql);		
	// 	return $rows;
	// 	unset($objDB);


	// }


	function GetSections($currentSchoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT coarcsectionmaster.* ,schools.schoolId
						FROM coarcsectionmaster 
						LEFT JOIN schools ON coarcsectionmaster.schoolId=schools.schoolId
						WHERE coarcsectionmaster.schoolId=" . $currentSchoolId;
		$sql .= " ORDER BY coarcsectionmaster.sortOrder";
		// echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetSubSections($currentSchoolId, $sectionMasterId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT coarcsectionmaster.sectionMasterId,coarcsubsectionmaster.coarcSubSectionMasterId,
							coarcsubsectionmaster.subTitle
							FROM coarcsectionmaster
							LEFT JOIN coarcsubsectionmaster ON coarcsectionmaster.sectionMasterId=
									  coarcsubsectionmaster.sectionMasterId																
							WHERE coarcsectionmaster.sectionMasterId=" . $sectionMasterId;
		$sql .= " ORDER BY coarcsubsectionmaster.sortOrder";
		//echo '<hr>subsection->'.$sql;	
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetAllCoarcSurvey($studentId)
	{
		$objDB = new clsDB();
		$sql = "SELECT studentsitecoarcmaster.*,studentsitecoarcdetail.*,student.studentId,
						student.firstName,student.lastName
						FROM studentsitecoarcmaster
						LEFT JOIN studentsitecoarcdetail ON studentsitecoarcmaster.studentCoarcMasterId=
															studentsitecoarcdetail.studentCoarcMasterId
						
						LEFT JOIN student ON studentsitecoarcmaster.studentId=student.studentId
						WHERE studentsitecoarcmaster.studentId=" . $studentId . "  GROUP BY 
						studentsitecoarcdetail.studentCoarcMasterId";

		//ECHO $sql;
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}
	function DeleteCoarcSurvey($studentCoarcMasterId)
	{
		$objDB = new clsDB();
		$result = "";
		if ($studentCoarcMasterId > 0) {
			$objDB = new clsDB();
			$sql = "DELETE studentsitecoarcmaster,studentsitecoarcdetail FROM studentsitecoarcmaster
									INNER JOIN studentsitecoarcdetail ON studentsitecoarcmaster.studentCoarcMasterId=
																			studentsitecoarcdetail.studentCoarcMasterId
									WHERE studentsitecoarcmaster.studentCoarcMasterId = " . $studentCoarcMasterId;

			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
		return $result;
	}

	function GetStudentCoarcSurveyDetails($studentCoarcMasterId, $studentId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT studentsitecoarcmaster.*,studentsitecoarcdetail.*
							FROM  studentsitecoarcmaster 
							LEFT JOIN studentsitecoarcdetail ON studentsitecoarcdetail.studentCoarcMasterId= 
							studentsitecoarcmaster.`studentCoarcMasterId`
							WHERE studentsitecoarcmaster.coarcSurveyMasterId=" . $studentCoarcMasterId . " AND studentsitecoarcmaster.studentId=" . $studentId;
		//   echo $sql;exit;		
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}
	function GetStudentStatus($studentId, $coarcSurveyMasterId)
	{

		$studentStatus = 0;
		$objDB = new clsDB();
		$sql = "SELECT * FROM  studentcoarcmaster WHERE studentId = " . $studentId . " AND  coarcSurveyMasterId = " . $coarcSurveyMasterId;
		//echo $sql;exit;
		$studentStatus = $objDB->GetDataRow($sql);

		unset($objDB);
		return $studentStatus;
	}
	function CopyDefaultCoarcTopicMaster($currentSchoolId)
	{
		$currentschoolSectionId = '';
		$currentschoolTopicId = '';
		$schoolTopicId = '';

		$this->schoolId = $currentSchoolId;
		$objDB = new clsDB();
		$rowsSectionMaster = "";
		$sql = "SELECT * FROM defaultcoarcsectionmaster";
		$rowsSectionMaster = $objDB->GetResultset($sql);

		$savedQuestionIds = array();
		$savedSectionIds = array();

		if ($rowsSectionMaster != "") {

			while ($row = mysqli_fetch_array($rowsSectionMaster)) {
				$this->schoolId = $currentSchoolId;
				$this->title = $row['title'];
				$this->sortOrder = $row['sortOrder'];
				$defaultsectionMasterId = $row['sectionMasterId'];

				$defaultTopicIdArray = array($defaultsectionMasterId);
				$currentschoolTopicId = $this->SaveSchoolSectionMaster($schoolTopicId); {
					$sql = "select defaultcoarcquestionmaster.* from 
													defaultcoarcquestionmaster
													LEFT JOIN defaultcoarcquestiondetail ON  defaultcoarcquestionmaster.coarcQuestionId
													=defaultcoarcquestiondetail .coarcQuestionId 																
													where defaultcoarcquestionmaster.sectionMasterId=" . $defaultsectionMasterId;
					$sql .= " GROUP BY defaultcoarcquestionmaster.coarcQuestionId";
					//echo '<hr>Question->'.$sql;
					$rowsQuestionMaster = $objDB->GetResultset($sql);

					if ($rowsQuestionMaster != "") {
						while ($row = mysqli_fetch_array($rowsQuestionMaster)) {
							$masterQuestionId = $row['coarcQuestionId'];
							// If already used then skipp
							if (array_key_exists($masterQuestionId, $savedQuestionIds)) {
								$currentschoolQuestionId[] = $savedQuestionIds[$masterQuestionId];
								continue;
							} else {
								$this->questionText = $row['questionText'];
								$this->schoolCoarcQuestionType = $row['coarcQuestionType'];
								$this->schoolId = $currentSchoolId;
								$this->sectionMasterId = $currentschoolTopicId;
								$this->schoolCoarcQuestionId = $masterQuestionId;

								//$schoolQuestionMasterId = $this->SaveSchoolQuestionMaster($questionId);
								$schoolQuestionId = $this->CopyMasterQuestionToSchool($currentSchoolId, $masterQuestionId, $currentschoolTopicId);
								//Bind in array
								$savedQuestionIds[$masterQuestionId] = $schoolQuestionId;
								$currentschoolQuestionId[] = $schoolQuestionId;
								//-----------------------------------------------------
								//Copy Question Choices
								//-----------------------------------------------------
								$this->CopyMasterQuestionChoicesToSchool($masterQuestionId, $schoolQuestionId);
								//-----------------------------------------------------
							}
						} //End while defaultcoarcquestionmaster


					} //End if defaultcoarcquestionmaster                                               

					     

				}
			}
		}
	}

	function SaveSchoolSectionMaster($schoolTopicId)
	{
		$objDB = new clsDB();
		$sql = "INSERT INTO coarcsectionmaster (schoolId,title,sortOrder) 
                VALUES ('" . addslashes($this->schoolId) . "',
                        '" . addslashes($this->title) . "',
                        '" . addslashes($this->sortOrder) . "'                        
                        )";
		$schoolTopicId = $objDB->ExecuteInsertQuery($sql);
		$this->schoolTopicId = $schoolTopicId;
		unset($objDB);
		return $schoolTopicId;
	}
	//Copy default question data
	function CopyMasterQuestionToSchool($schoolId, $questionId, $currentschoolTopicId)
	{
		$objDB = new clsDB();
		$sql = "INSERT INTO schoolcoarcquestionmaster(questionText,schoolCoarcQuestionType,schoolId,
																  sectionMasterId) 
					
					SELECT questionText,coarcQuestionType," . $schoolId . "," . $currentschoolTopicId . "  
					FROM defaultcoarcquestionmaster
					WHERE coarcQuestionId=" . $questionId;

		//ECHO '<HR>QUESTION INSERT->'.$sql.'<HR>';
		$schoolQuestionId = $objDB->ExecuteInsertQuery($sql);
		unset($objDB);
		return  $schoolQuestionId;;
	}
	function CopyMasterQuestionChoicesToSchool($questionMasterId, $schoolQuestionId)
	{
		$sql = "INSERT INTO schoolcoarcquestiondetail(schoolCoarcQuestionId,optionText,schoolOptionValue) 
							SELECT  " . $schoolQuestionId . " ,optionText,schoolOptionValue
							FROM defaultcoarcquestiondetail  WHERE coarcQuestionId=" . $questionMasterId;

		$objDB = new clsDB();
		$schoolQuestionDId = $objDB->ExecuteInsertQuery($sql);
		unset($objDB);
	}
	function DeleteSchoolCoARC($schoolId)
	{
		$result = "";
		$objDB = new clsDB();
		$sql = "DELETE 	coarcsectionmaster.*  ,										
										schoolcoarcquestionmaster.*,
										schoolcoarcquestiondetail.*
										FROM coarcsectionmaster										
										LEFT JOIN schoolcoarcquestionmaster ON coarcsectionmaster.sectionMasterId=
																		schoolcoarcquestionmaster.sectionMasterId
										LEFT JOIN schoolcoarcquestiondetail ON schoolcoarcquestionmaster.schoolCoarcQuestionId=
																		schoolcoarcquestiondetail.schoolCoarcQuestionId
								WHERE coarcsectionmaster.schoolId = " . $schoolId;
		//echo $sql;exit;
		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}

	function GetCoarcSurveyType()
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM coarcsurveytype order by title asc";
		//echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function SaveCoARCSurveyMaster($coarcSurveyMasterId)
	{
		$objDB = new clsDB();
		$sql = '';

		if ($coarcSurveyMasterId > 0) {
			$sql = "UPDATE coarcsurveymaster SET 
													title = '" . addslashes($this->title) . "',	
													coarctype = '" . addslashes($this->coarctype) . "',	
													isDelivery = '" . addslashes($this->isDelivery) . "',
													startDate = '" . addslashes($this->startDate) . "',
													endDate = '" . addslashes($this->endDate) . "'										
													Where coarcSurveyMasterId= " . $coarcSurveyMasterId;

			//echo $sql;exit;
			$objDB->ExecuteQuery($sql);
		} else {
			$sql = "INSERT INTO coarcsurveymaster (schoolId,title,coarctype,isDelivery,year,startDate,endDate,createdBy, createdDate) 
							VALUES (
									'" . addslashes($this->schoolId) . "',
									'" . addslashes($this->title) . "',						
									'" . ($this->coarctype) . "',
									'" . ($this->isDelivery) . "',	
									'" . addslashes($this->year) . "',
									'" . addslashes($this->startDate) . "',
									'" . addslashes($this->endDate) . "',
									'" . addslashes($this->createdBy) . "',
									'" . (date("Y-m-d h:i:s")) . "'						
									)";
			$coarcSurveyMasterId = $objDB->ExecuteInsertQuery($sql);
		}
		unset($objDB);
		return $coarcSurveyMasterId;
	}

	function DeleteCoarcSurveyAssignedStudent($coarcSurveyMasterId)
	{
		$objDB = new clsDB();
		$result = "";
		if ($coarcSurveyMasterId > 0) {
			$objDB = new clsDB();
			$sql = "DELETE FROM coarcsurveydetails WHERE coarcSurveyMasterId=" . $coarcSurveyMasterId;
			// echo '$sql1->'.$sql;exit;
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
		return $result;
	}

	function SavecoarcSurveyDetail($retcoarcSurveyMasterId)
	{
		$objDB = new clsDB();
		$sql = '';
		$sql = "INSERT INTO  coarcsurveydetails (coarcSurveyMasterId,studentId,clinicianId) 
							VALUES (
									'" . addslashes($this->coarcSurveyMasterId) . "',
									'" . addslashes($this->studentId) . "',	
									'" . addslashes($this->clinicianId) . "'										
									)";
		// echo '<hr>Insert->'.$sql;
		$retcoarcSurveyMasterId = $objDB->ExecuteInsertQuery($sql);

		unset($objDB);
		return $retcoarcSurveyMasterId;
	}

	function GetAllIRRAssignmentsDetails($schoolId)
	{
		$objDB = new clsDB();
		$sql = "SELECT coarcsurveymaster.*,
						coarcsurveydetails.coarcSurveyMasterId AS DetailcoarcSurveyMasterId,
						student.firstName,student.lastName,clinician.firstName,clinician.lastName
						FROM coarcsurveymaster				
						LEFT JOIN coarcsurveydetails ON coarcsurveymaster.coarcSurveyMasterId=coarcsurveydetails.coarcSurveyMasterId	
						LEFT JOIN student ON coarcsurveydetails.studentId=student.studentId
						LEFT JOIN clinician ON coarcsurveydetails.clinicianId=clinician.clinicianId 
						WHERE coarcsurveymaster.schoolId=" . $schoolId . " GROUP BY coarcsurveymaster.coarcSurveyMasterId
						order by coarcsurveymaster.createdDate desc";
		// ECHO $sql ;
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}

	function GetAllGraduateDetails($schoolId)
	{
		$objDB = new clsDB();
		$sql = "SELECT coarcsurveymaster.*,
					coarcsurveydetails.coarcSurveyMasterId AS DetailcoarcSurveyMasterId,
					student.firstName,student.lastName
					FROM coarcsurveymaster				
					INNER JOIN coarcsurveydetails ON coarcsurveymaster.coarcSurveyMasterId=coarcsurveydetails.coarcSurveyMasterId	
					INNER JOIN student ON coarcsurveydetails.studentId=student.studentId
					WHERE coarcsurveymaster.schoolId=" . $schoolId . " AND coarcsurveymaster.coarctype =3 GROUP BY coarcsurveymaster.title";
		// ECHO $sql ;
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}
	function GetStudentCount($coarcSurveyMasterId, $studentId)
	{
		$objDB = new clsDB();
		$sql = "SELECT COUNT(DISTINCT studentId) studentId FROM coarcsurveydetails 
					WHERE coarcSurveyMasterId=" . $coarcSurveyMasterId;

		if ($studentId > 0) {
			$sql .= " AND studentId=" . $studentId;
		}
		// echo $sql;EXIT;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}
	function GetClinicianCount($coarcSurveyMasterId, $clinicianId)
	{
		$objDB = new clsDB();
		$sql = "SELECT COUNT(DISTINCT clinicianId) clinicianId  FROM coarcsurveydetails 
					WHERE coarcSurveyMasterId=" . $coarcSurveyMasterId;

		if ($clinicianId > 0) {
			$sql .= " AND clinicianId =" . $clinicianId;
		}
		//echo $sql;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}
	function GetStudentDetailsforCoarcSurvey($coarcSurveyMasterId, $schoolId)
	{
		$objDB = new clsDB();
		$sql = "SELECT coarcsurveymaster.*,coarcsurveydetails.* ,studentadditationlcontactinformation.emailAddress
					FROM coarcsurveymaster 
					LEFT JOIN coarcsurveydetails ON coarcsurveymaster.coarcSurveyMasterId=coarcsurveydetails.coarcSurveyMasterId
					LEFT JOIN studentadditationlcontactinformation ON studentadditationlcontactinformation.studentId = coarcsurveydetails.studentId
					WHERE coarcsurveymaster.coarcSurveyMasterId=" . $coarcSurveyMasterId .
			" AND coarcsurveymaster.schoolId=" . $schoolId;
		// ECHO $sql;exit;
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}
	function GetCoarcSurveyDetails($coarcSurveyMasterId, $schoolId)
	{
		$objDB = new clsDB();
		$sql = "SELECT * FROM coarcsurveymaster 
					WHERE coarcSurveyMasterId=" . $coarcSurveyMasterId .
			" AND schoolId=" . $schoolId;
		// ECHO $sql;exit;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}
	function GetStudentDetailsByCoarcSurvey($coarcSurveyMasterId, $schoolId)
	{
		$objDB = new clsDB();
		$sql = "SELECT coarcsurveymaster.*,IFNULL(graduatecoarcrequestmaster.status, 0) as status,
						IFNULL(studentcoarcmaster.status, 0) as studentStatus,
						coarcsurveydetails.coarcSurveyMasterId AS DetailcoarcSurveyMasterId,
						student.studentId,student.firstName,student.lastName,rankmaster.title AS Ranktitle
						FROM coarcsurveymaster				
						INNER JOIN coarcsurveydetails ON coarcsurveymaster.coarcSurveyMasterId=coarcsurveydetails.coarcSurveyMasterId	
						INNER JOIN student ON coarcsurveydetails.studentId=student.studentId
						LEFT JOIN graduatecoarcrequestmaster ON graduatecoarcrequestmaster.studentId=student.studentId
						LEFT JOIN studentcoarcmaster ON studentcoarcmaster.studentId=student.studentId
						LEFT JOIN rankmaster ON student.rankId=rankmaster.rankId
						WHERE coarcsurveymaster.schoolId=" . $schoolId .
			" AND coarcsurveymaster.coarcSurveyMasterId=" . $coarcSurveyMasterId;
		$sql .= " group by studentcoarcmaster.studentId ORDER BY student.firstName ASC";
		// ECHO $sql;
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}

	function GetStudentListByCoarcSurvey($coarcSurveyMasterId, $schoolId)
	{
		$objDB = new clsDB();
		$sql = "SELECT coarcsurveymaster.*, 
					coarcsurveydetails.coarcSurveyMasterId AS DetailcoarcSurveyMasterId, student.studentId,student.firstName,student.lastName,rankmaster.title AS Ranktitle  
					FROM coarcsurveymaster 
					INNER JOIN coarcsurveydetails ON coarcsurveymaster.coarcSurveyMasterId=coarcsurveydetails.coarcSurveyMasterId 
					INNER JOIN student ON coarcsurveydetails.studentId=student.studentId
					INNER JOIN rankmaster ON student.rankId=rankmaster.rankId 
					WHERE coarcsurveymaster.schoolId=" . $schoolId .
			" AND coarcsurveymaster.coarcSurveyMasterId=" . $coarcSurveyMasterId;
		$sql .= " GROUP BY coarcsurveymaster.coarcSurveyMasterId,coarcsurveydetails.studentId ORDER BY student.firstName ASC";
		// ECHO $sql;
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}

	function GetStudentByCoarcSurvey($coarcSurveyMasterId, $schoolId)
	{
		$objDB = new clsDB();
		$sql = "SELECT coarcsurveydetails.studentId  
					FROM coarcsurveymaster 
					INNER JOIN coarcsurveydetails ON coarcsurveymaster.coarcSurveyMasterId=coarcsurveydetails.coarcSurveyMasterId 
					INNER JOIN student ON coarcsurveydetails.studentId=student.studentId
					INNER JOIN rankmaster ON student.rankId=rankmaster.rankId 
					WHERE coarcsurveymaster.schoolId=" . $schoolId .
			" AND coarcsurveymaster.coarcSurveyMasterId=" . $coarcSurveyMasterId;
		$sql .= " ORDER BY student.firstName ASC";
		// ECHO $sql;
		$row = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $row;
	}

	function GetGraduateStudentCoarcSurveyStatus($coarcSurveyMasterId, $studentId)
	{
		$objDB = new clsDB();
		$sql = "SELECT IFNULL(graduatecoarcrequestmaster.status, 0) as status
						FROM coarcsurveymaster				
						LEFT JOIN graduatecoarcrequestmaster ON graduatecoarcrequestmaster.coarcSurveyMasterId=coarcsurveymaster.coarcSurveyMasterId
						WHERE graduatecoarcrequestmaster.studentId=" . $studentId .
			" AND coarcsurveymaster.coarcSurveyMasterId=" . $coarcSurveyMasterId;
		// echo $sql;exit;		
		$row = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $row;
	}
	function GetStudentCoarcSurveyStatus($coarcSurveyMasterId, $studentId)
	{
		$objDB = new clsDB();
		$sql = "SELECT IFNULL(studentcoarcmaster.status, 0) as studentStatus
						FROM coarcsurveymaster				
						LEFT JOIN studentcoarcmaster ON studentcoarcmaster.coarcSurveyMasterId=coarcsurveymaster.coarcSurveyMasterId
						WHERE studentcoarcmaster.studentId=" . $studentId .
			" AND coarcsurveymaster.coarcSurveyMasterId=" . $coarcSurveyMasterId;
		// echo $sql;exit;		
		$row = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $row;
	}

	function GetPersonnelCoarcSurveyStatus($coarcSurveyMasterId, $clinicianId)
	{
		$objDB = new clsDB();
		$sql = "SELECT status
						FROM personnelcoarcrequestmaster				
						WHERE clinicianId=" . $clinicianId .
			" AND coarcSurveyMasterId=" . $coarcSurveyMasterId;
		//echo $sql;exit;		
		$row = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $row;
	}

	function GetClinicianDetailsByCoarcSurvey($coarcSurveyMasterId, $schoolId)
	{
		$objDB = new clsDB();
		$sql = "SELECT coarcsurveymaster.*,coarcsurveydetails.coarcSurveyMasterId AS DetailcoarcSurveyMasterId,
					clinician.clinicianId,clinician.firstName,clinician.lastName
					FROM coarcsurveymaster				
					INNER JOIN coarcsurveydetails ON coarcsurveymaster.coarcSurveyMasterId=coarcsurveydetails.coarcSurveyMasterId	
					INNER JOIN clinician ON coarcsurveydetails.clinicianId=clinician.clinicianId
					WHERE coarcsurveymaster.schoolId=" . $schoolId .
			" AND coarcsurveymaster.coarcSurveyMasterId=" . $coarcSurveyMasterId;
		$sql .= " ORDER BY clinician.firstName ASC";

		// echo $sql;exit;
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}

	function GetCoarcTitle($coarcSurveyMasterId)
	{
		$objDB = new clsDB();
		$sql = "SELECT coarcSurveyMasterId,title FROM  coarcsurveymaster WHERE coarcSurveyMasterId=" . $coarcSurveyMasterId;
		//echo $sql;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}
	function GetOtherRole()
	{
		$objDB = new clsDB();
		$sql = "SELECT * FROM otherrole ";
		//echo $sql;
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}

	function GetCoarcIdForStudent($studentId)
	{
		$objDB = new clsDB();
		$sql = "SELECT coarcId FROM studentcoarcmaster
					WHERE studentId=" . $studentId . "  GROUP BY studentId";


		$row = $objDB->GetSingleFieldValue($sql);
		//ECHO $sql;
		unset($objDB);
		return $row;
	}

	function GetCoarcSurveyTitle($coarcSurveyMasterId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT title FROM coarcsurveymaster where coarcSurveyMasterId = " . $coarcSurveyMasterId;
		// echo $sql;
		$rows = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $rows;
	}


	function GetStudentCoarcSurveyByStudent($studentId, $coarcSurveyMasterId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT IFNULL(studentcoarcmaster.status, 0) as status, coarcId FROM studentcoarcmaster where coarcSurveyMasterId = " . $coarcSurveyMasterId . " AND studentId=" . $studentId;
		// 			echo $sql; exit;
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}

	function GetGraduateCoarcSurveyByStudent($studentId, $coarcSurveyMasterId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT IFNULL(graduatecoarcrequestmaster.status, 0) as status, graduatecoarcId FROM graduatecoarcrequestmaster where coarcSurveyMasterId = " . $coarcSurveyMasterId . " AND studentId=" . $studentId;
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}

	function GetPersonnelCoarcSurveyByStudent($clinicianId, $coarcSurveyMasterId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT IFNULL(personnelcoarcrequestmaster.status, 0) as status, personnelCoarcId FROM personnelcoarcrequestmaster where coarcSurveyMasterId = " . $coarcSurveyMasterId . " AND clinicianId=" . $clinicianId;
		// 			echo $sql;
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}


	function GetCoarcSurveyDelivery($coarcSurveyMasterId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT isDelivery FROM coarcsurveymaster where coarcSurveyMasterId = " . $coarcSurveyMasterId;
		$rows = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $rows;
	}

	function SaveCoarcQuestionToSchool($questionText, $schoolId, $sectionMasterId)
	{
		$objDB = new clsDB();
		$sql = "INSERT INTO `schoolcoarcquestionmaster` (`questionText`, `schoolCoarcQuestionType`, `schoolId`, `sectionMasterId`) VALUES ('" . addslashes($questionText) . "', '2', '" . addslashes($schoolId) . "', '" . addslashes($sectionMasterId) . "')";

		// ECHO '<HR>QUESTION INSERT->'.$sql.'<HR>';exit;
		$schoolQuestionId = $objDB->ExecuteInsertQuery($sql);
		unset($objDB);
		return  $schoolQuestionId;
	}

	function SaveCoarcQuestionOptionToSchool($schoolCoarcQuestionId = 0)
	{
		$objDB = new clsDB();
		$sql = "INSERT INTO `schoolcoarcquestiondetail` (`schoolCoarcQuestionDetailId`, `schoolCoarcQuestionId`, `optionText`, `schoolOptionValue`) VALUES (NULL, '" . addslashes($schoolCoarcQuestionId) . "', '5', '1'), (NULL, '" . addslashes($schoolCoarcQuestionId) . "', '4', '2'), (NULL, '" . addslashes($schoolCoarcQuestionId) . "', '3', '3'), (NULL, '" . addslashes($schoolCoarcQuestionId) . "', '2', '4'), (NULL, '" . addslashes($schoolCoarcQuestionId) . "', '1', '5'), (NULL, '" . addslashes($schoolCoarcQuestionId) . "', 'N/A', '6')
				";

		// ECHO '<HR>QUESTION INSERT->'.$sql.'<HR>';exit;
		$schoolQuestionId = $objDB->ExecuteInsertQuery($sql);
		unset($objDB);
		return  $schoolQuestionId;
	}
	function GetCoarcSurveyTitleIdByTitle($title, $schoolId, $cbosurveyType)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT coarcSurveyMasterId FROM coarcsurveymaster where title = '" . $title . "' AND coarctype  = " . $cbosurveyType . " AND schoolId = " . $schoolId;
		// echo $sql; exit;
		$rows = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $rows;
	}

	function GetGraduateStudentDetailsByCoarcSurvey($coarcSurveyMasterId, $schoolId)
	{
		$objDB = new clsDB();
		$sql = "SELECT coarcsurveymaster.*,IFNULL(graduatecoarcrequestmaster.status, 0) as status,
						0 as studentStatus,
						coarcsurveydetails.coarcSurveyMasterId AS DetailcoarcSurveyMasterId,
						student.studentId,student.firstName,student.lastName,rankmaster.title AS Ranktitle
						FROM coarcsurveymaster				
						INNER JOIN coarcsurveydetails ON coarcsurveymaster.coarcSurveyMasterId=coarcsurveydetails.coarcSurveyMasterId	
						INNER JOIN student ON coarcsurveydetails.studentId=student.studentId
						LEFT JOIN graduatecoarcrequestmaster ON graduatecoarcrequestmaster.studentId=student.studentId
						LEFT JOIN graduatecoarcmaster ON graduatecoarcmaster.studentId=student.studentId
						LEFT JOIN rankmaster ON student.rankId=rankmaster.rankId
						WHERE coarcsurveymaster.schoolId=" . $schoolId .
			" AND coarcsurveymaster.coarcSurveyMasterId=" . $coarcSurveyMasterId;
		$sql .= " group by graduatecoarcmaster.studentId ORDER BY student.firstName ASC";
		// ECHO $sql;
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}

	/**
	 * Retrieves details of a specific CoARC survey, including survey type and school information.
	 *
	 * @param int $id The CoARC survey master ID.
	 * @param int $schoolId Optional. The school ID to filter the results. Default is 0.
	 * @return array The details of the CoARC survey including survey type title and school name.
	 */

	function GetAllCoARCSurveyDetailsForLogs($id, $schoolId = 0)
	{
		$objDB = new clsDB();

		$sql = "SELECT coarcsurveymaster.*,coarcsurveytype.title as coarcSurveyTypeTitle,schools.schoolId, schools.displayName as schoolName FROM `coarcsurveymaster` 
        INNER JOIN coarcsurveytype ON coarcsurveytype.coarcSurveyTypeId = coarcsurveymaster.coarctype
        INNER JOIN schools ON schools.schoolId = coarcsurveymaster.schoolId
		WHERE coarcSurveyMasterId =" . $id;

		if ($schoolId) {
			$sql .= " AND coarcsurveymaster.schoolId=" . $schoolId;
		}
		// echo $sql;exit;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}

	/**
	 * Create log for CoARC survey.
	 *
	 * @param int $id The CoARC survey master id.
	 * @param string $action The action (Add, Edit, Delete) that was performed.
	 * @param int $userId The user id.
	 * @param string $userType The user type (Student, School, District, State, Admin).
	 * @param string $type The type of CoARC survey (Student, Personnel, Graduate, Employer).
	 * @param int $isSuperAdmin Whether the user is a super admin or not.
	 *
	 * @return array The log data, CoARC survey data, and additional data.
	 */
	function createCoARCSurveyLog($id, $action, $userId, $userType, $type = '', $isSuperAdmin = 0)
	{
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$obj = new clsCoarc(); // Assuming `Attendance` class is used for `prepareAttendanceLogData`

		// Get user details based on user type
		$userDetails = getUserDetails($userId, $userType);

		$rowData = $obj->GetAllCoARCSurveyDetailsForLogs($id);
		if ($rowData != '')
			$logData = $objLog->generateLogData($rowData, $userType);

		$additionalData = '';

		// Populate log data with user details
		$logData['userId'] = $userDetails['userId'];
		$logData['userName'] = $userDetails['userName'];
		$logMessage = '';

		if ($action == 'Add') {
			$logMessage = $logData['userName'] . ' added new ' . $rowData['coarcSurveyTypeTitle'];
		} else if ($action == 'Edit') {
			$logMessage = $logData['userName'] . ' updated ' . $rowData['coarcSurveyTypeTitle'];
		} else if ($action == 'Delete') {
			$logMessage = $logData['userName'] . ' deleted ' . $rowData['title'] . ' ' . $rowData['coarcSurveyTypeTitle'];
		}
		// Construct log message
		$logData['message'] = $logMessage;

		// Return the data
		return [$logData, $rowData, $additionalData];
	}

	/**
	 * Saves CoARC survey audit logs to the database.
	 *
	 * @param int $id The ID of the CoARC survey.
	 * @param int $userId The ID of the user performing the action.
	 * @param string $userType The user type (Student, School, District, State, Admin).
	 * @param string $action The action performed (Add, Edit, Delete).
	 * @param int $isMobile Optional; specifies if the action was performed from a mobile device (0 = No, 1 = Yes).
	 * @param string $type Optional; specifies the type of CoARC survey (if applicable).
	 * @param int $isSuperAdmin Optional; specifies if the action was performed by a super admin (0 = No, 1 = Yes).
	 * @return bool True if the log was successfully saved.
	 */
	function saveCoARCSurveyAuditLog($id, $userId, $userType, $action, $isMobile = 0, $type = '', $isSuperAdmin = 0)
	{
		// Instantiate the Logger and Checkoff classes
		$objLog = new clsLogger();
		$obj = new clsCoarc();

		// Prepare log data
		[$logData, $rowData, $additionalData] = $obj->createCoARCSurveyLog($id, $action, $userId, $userType, $type, $isSuperAdmin);
		$logData['isMobile'] = $isMobile;

		
		// Save the log details
		$objLog->saveLogs($logData, $action, $id, $rowData['coarcSurveyTypeTitle'], $rowData, $additionalData);

		// Clean up
		unset($objLog);
		unset($obj);

		return true; // Return success or handle further actions as needed
	}
}
