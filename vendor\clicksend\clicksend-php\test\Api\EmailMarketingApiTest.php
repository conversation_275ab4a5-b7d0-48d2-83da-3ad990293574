<?php
/**
 * EmailMarketingApiTest
 * PHP version 5
 *
 * @category Class
 * @package  ClickSend
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * ClickSend v3 API
 *
 * This is an official SDK for [ClickSend](https://clicksend.com)  Below you will find a current list of the available methods for clicksend.  *NOTE: You will need to create a free account to use the API. You can register [here](https://dashboard.clicksend.com/#/signup/step1/)..*
 *
 * OpenAPI spec version: 3.1
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * ClickSend Codegen version: 3.0.51
 */
/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Please update the test case below to test the endpoint.
 */

namespace ClickSend;

use ClickSend\Configuration;
use <PERSON>lickS<PERSON>\ApiException;
use <PERSON>lickSend\ObjectSerializer;
use PHPUnit\Framework\TestCase;

/**
 * EmailMarketingApiTest Class Doc Comment
 *
 * @category Class
 * @package  ClickSend
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */
class EmailMarketingApiTest extends TestCase
{

    /**
     * Setup before running any test cases
     */
    public static function setUpBeforeClass(): void
    {
    }

    /**
     * Setup before running each test case
     */
    public function setUp(): void
    {
    }

    /**
     * Clean up after running each test case
     */
    public function tearDown(): void
    {
    }

    /**
     * Clean up after running all test cases
     */
    public static function tearDownAfterClass(): void
    {
    }

    /**
     * Test case for allowedEmailAddressGet
     *
     * Get all email addresses.
     *
     */
    public function testAllowedEmailAddressGet()
    {
    }

    /**
     * Test case for allowedEmailAddressPost
     *
     * Create allowed Email Address.
     *
     */
    public function testAllowedEmailAddressPost()
    {
    }

    /**
     * Test case for cancelEmailCampaignPut
     *
     * Cancel email campaign.
     *
     */
    public function testCancelEmailCampaignPut()
    {
    }

    /**
     * Test case for emailCampaignGet
     *
     * Get specific email campaign.
     *
     */
    public function testEmailCampaignGet()
    {
    }

    /**
     * Test case for emailCampaignHistoryExportGet
     *
     * Export specific email campaign history.
     *
     */
    public function testEmailCampaignHistoryExportGet()
    {
    }

    /**
     * Test case for emailCampaignHistoryGet
     *
     * Get specific email campaign history.
     *
     */
    public function testEmailCampaignHistoryGet()
    {
    }

    /**
     * Test case for emailCampaignPost
     *
     * Send email campaign.
     *
     */
    public function testEmailCampaignPost()
    {
    }

    /**
     * Test case for emailCampaignPricePost
     *
     * Calculate email campaign price.
     *
     */
    public function testEmailCampaignPricePost()
    {
    }

    /**
     * Test case for emailCampaignPut
     *
     * Edit email campaign.
     *
     */
    public function testEmailCampaignPut()
    {
    }

    /**
     * Test case for emailCampaignsGet
     *
     * Get all email campaigns.
     *
     */
    public function testEmailCampaignsGet()
    {
    }

    /**
     * Test case for sendVerificationTokenGet
     *
     * Send verification token.
     *
     */
    public function testSendVerificationTokenGet()
    {
    }

    /**
     * Test case for specificAllowedEmailAddressDelete
     *
     * Delete specific email address.
     *
     */
    public function testSpecificAllowedEmailAddressDelete()
    {
    }

    /**
     * Test case for specificAllowedEmailAddressGet
     *
     * Get specific email address.
     *
     */
    public function testSpecificAllowedEmailAddressGet()
    {
    }

    /**
     * Test case for verifyAllowedEmailAddressGet
     *
     * Verify email address using verification token.
     *
     */
    public function testVerifyAllowedEmailAddressGet()
    {
    }
}
