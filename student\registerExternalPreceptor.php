<?php

include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsRotation.php');
include('../class/clscheckoff.php');
include('../class/clsExternalPreceptors.php');
$externalPreceptorId = isset($_GET['externalPreceptorId']) ? $_GET['externalPreceptorId'] : 0;
$isHours = isset($_GET['isHours']) ? $_GET['isHours'] : '';


?>
<style>
  .button {
    border: 1px solid #5cb85c !important;
    background-color: #5cb85c !important;
    color: #fff !important;
    cursor: pointer !important;
    padding: 10px 30px !important;
    border-radius: 10px !important;
    float: right;
}

.modal-body{
  padding-bottom: 5px !important;
  padding-top: 5px !important;
}

.mfp-bg{
  height: 100vh !important;
  position: fixed !important;
}

.mfp-wrap{
  top: 0 !important;
  position: fixed !important;
  height: 100% !important; 
}

</style>

<div class="" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close hide" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h4 class="modal-title">Preceptor Registration Form</h4>
      </div>
      <div class="modal-body">
        <div class="modal-body">

          <form id="frmRegistration" data-parsley-validate class="form-horizontal" method="POST" action="">
            <div class="form-group">
              <label for="firstName" class="control-label"> First Name:</label>
              <input type="text" id="firstName" name="firstName" class="form-control required-input" placeholder="Enter First name" required data-parsley-errors-container="#error-firstName-container">
              <div id="error-firstName-container"></div>
            </div>
            <div class="form-group">
              <label for="lastName" class="control-label"> Last Name:</label>
              <input type="text" id="lastName" name="lastName" class="form-control required-input" placeholder="Enter Last Name" required>
              <div id="error-lastName"></div>
            </div>
            <?php if($isHours != 'false') {?>
            <div class="form-group date">
              <label for="totalHours" class="control-label"> Preceptor Total Hours:</label>
              <input type="text" id="totalHours" name="totalHours" class="datetimepicker form-control" placeholder="Select Hours">
              <div id="error-totalHours"></div>
            </div>
            <?php } ?>
            <div class="form-group">
              <button type="button" id="btnSend" name="btnSend" class="btn button btnSend">Submit</button>
            </div>
          </form>


        </div>

        <!-- <div class="modal-footer">

        </div> -->
        <!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
    <!-- <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script> -->
    <script>
      // $('form').parsley();
      $('.mfp-close').addClass('hide');
      $('#btnSend').click(function() {
        var firstName = $("input[name=firstName]").val();
        var lastName = $("input[name=lastName]").val();
        var totalHours = $("input[name=totalHours]").val();
    
        if (firstName && lastName) {
          // $("#frmRegsitration").on("submit", function(event) {
          $.ajax({
            type: "POST",
            url: "registerExternalPreceptorSubmit.html?externalPreceptorId=<?php echo $externalPreceptorId; ?>",
            data: $("#frmRegistration").serialize(),
            success: function(responseData) {
              var obj = jQuery.parseJSON(responseData);
              // console.log(obj);
              if (obj.status == 'success') {
                var url = window.location.href;
                url = url + '&isRegister=1';
                window.location.href = url;
                // location.reload();
              }
            }
          });
        }
        // });
      });

      $('.close').click(function() {
        $.magnificPopup.close();
      });

      $(document).ready(function() {
        $('#form-control step2 input-md select2_single').addClass('required-select2');

        $('#totalHours').datetimepicker({
          format: 'HH:mm'
        });
      });
    </script>