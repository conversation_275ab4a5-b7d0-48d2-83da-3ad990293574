<?php
	include('includes/validateUserLogin.php'); 
    include('../includes/config.php');
	include('../includes/commonfun.php');	
    include('../class/clsDB.php');
    include('../class/clsRotation.php');
	include('../class/clsClinician.php'); 
	include('../class/clsFormative.php');
	include('../class/clsRotationDetails.php');
	
	
if(!empty($_POST["Rotation_Id"])) {

	$Rotation_Id = EncodeQueryData($_POST["Rotation_Id"]);

	$objDB = new clsDB();
	$query = '';
	$query ="SELECT  clinician.clinicianId,clinician.firstName,clinician.lastName,rotation.hospitalSiteId,rotation.rotationId,
			clinicianhospitalsite.clinicianId,clinicianhospitalsite.hospitalSiteId
			FROM  clinician
			LEFT JOIN clinicianhospitalsite ON clinician.clinicianId =clinicianhospitalsite.clinicianId
			LEFT JOIN rotation ON clinicianhospitalsite.hospitalSiteId = rotation.hospitalSiteId	
			
			WHERE rotation.rotationId = '" . DecodeQueryData($_POST["Rotation_Id"])  . "' AND clinician.isActive=1 ORDER BY clinician.firstName ASC";	
	// echo $query;exit;
	$Clinician = $objDB->GetResultSet($query);	
	$seletedClinicianId = (isset($_POST["Clinician_Id"])) ? $_POST["Clinician_Id"] : 0;
?>
	<option value="">Select</option>
<?php
	while($rows = mysqli_fetch_assoc($Clinician))
    {				
    	$seletedString = ($seletedClinicianId == $rows['clinicianId']) ? "selected"  : "";
		//echo $rows['clinicianId'];
		$firstName=$rows["firstName"];
		$lastName=$rows["lastName"];
		$fullName=$firstName.' '.$lastName;
		
		echo "<option value='{$rows['clinicianId']}' {$seletedString} >{$fullName}</option>";
	}
}
?>