<!-- Modal -->
<div class="modal fade" id="resendEmailModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">Send URL to Email</h4>
            </div>
            <div class="modal-body">
                <div class="modal-body pt-0">

                    <form id="resendForm" data-parsley-validate class="form-horizontal" method="POST">

                        <div class="form-group isPreceptorDiv">
                            <table id="preceptorTable" width="100%">
                                <tr>
                                    <td>
                                        <input type="hidden" id="email_preceptorId" name="email_preceptorId" value="0">
                                        <input type="hidden" id="email_rotationId" name="email_rotationId" value="0">
                                        <input type="hidden" id="email_activityId" name="email_activityId" value="0">
                                        <input type="hidden" id="email_soapNoteId" name="email_soapNoteId" value="0">
                                        <input type="hidden" id="email_evaluationId" name="email_evaluationId" value="0">
                                        <input type="hidden" id="email_evaluationType" name="email_evaluationType" value="">
                                        <input type="hidden" id="email_checkoffId" name="email_checkoffId" value="0">
                                        <input type="hidden" id="email_schoolTopicId" name="email_schoolTopicId" value="0">
                                        <label for="email_emailId"> Email: </label>
                                        <input type="email" id="email_emailId" name="email_emailId" value="" placeholder="Enter email" class=" form-control margin_right_five" required>
                                        <input type="hidden" name="email_preceptorNum" id="email_preceptorNum" maxlength="12" data-inputmask-alias="************" class=" form-control margin_right_five margin_top_fifteen validateOnlynumbers" placeholder="Enter Preceptor Phone Number" required />
                                    </td>
                                </tr>
                            </table>

                            <small style="color: #201f1fdb;">
                                Kindly input the email address to which you intend to send the URL.
                                <!-- <input class="margin_top_ten btn btn-primary" type="button" value="Insert row"> -->
                            </small>
                        </div>
                        <div class="flex-end">
                            <button id="btnSendEmail" name="btnSendEmail" type="button" class="btn button btnSendEmail ">Send</button>
                        </div>
                    </form>

                </div>

                <!-- <div class="modal-footer">

                </div> -->
                <!-- /.modal-content -->
            </div>
            <!-- /.modal-dialog -->
        </div>
    </div>
</div>

<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/jquery.min.js" type="text/javascript"></script>
<script src="<?php echo ($dynamicOrgUrl); ?>/assets/inputmask/jquery.inputmask.bundle.js"></script>
<script src="<?php echo ($dynamicOrgUrl); ?>/assets/inputmask/inputmask.js"></script>
<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
<script>
    function ShowEvaluationDetailsForEmail(eleObj) {

        // console.log(eleObj);
        var preceptorId = eleObj.hasAttribute('preceptorId') ? $(eleObj).attr('preceptorId') : 0;
        var preceptorNum = eleObj.hasAttribute('preceptorMobileNum') ? $(eleObj).attr('preceptorMobileNum') : '';
        var checkoffId = eleObj.hasAttribute('checkoffId') ? $(eleObj).attr('checkoffId') : 0;
        var schoolTopicId = eleObj.hasAttribute('schoolTopicId') ? $(eleObj).attr('schoolTopicId') : 0;
        var rotationId = eleObj.hasAttribute('rotationId') ? $(eleObj).attr('rotationId') : 0;
        var evaluationId = eleObj.hasAttribute('evaluationId') ? $(eleObj).attr('evaluationId') : 0;
        var email = eleObj.hasAttribute('email') ? $(eleObj).attr('email') : '';
        var evaluationType = eleObj.hasAttribute('evaluationType') ? $(eleObj).attr('evaluationType') : '';
        var preceptorNo = eleObj.hasAttribute('preceptorNum') ? $(eleObj).attr('preceptorNum') : '';
        var activityId = eleObj.hasAttribute('activityId') ? $(eleObj).attr('activityId') : 0;
        var soapNoteId = eleObj.hasAttribute('soapNoteId') ? $(eleObj).attr('soapNoteId') : 0;
    //   console.log(preceptorNum);

        preceptorNum = (evaluationType == 'checkoff' || evaluationType == 'activitySheet' || evaluationType == 'soapnote') ? preceptorNum : preceptorNo;

        $('#email_preceptorId').val(preceptorId);
        $('#email_preceptorNum').val(preceptorNum); 
        $('#email_checkoffId').val(checkoffId);
        $('#email_schoolTopicId').val(schoolTopicId);
        $('#email_emailId').val(email);
        $('#email_evaluationType').val(evaluationType);
        $('#email_rotationId').val(rotationId);
        $('#email_evaluationId').val(evaluationId);
        $('#email_activityId').val(activityId);
        $('#email_soapNoteId').val(soapNoteId);

        // $('#email_evaluationId').val(email_preceptorNum);
    }

    $(document).on('click', '.btnSendEmail', function() {
        var preceptorId = $('#email_preceptorId').val();
        var preceptorNum = $('#email_preceptorNum').val();
        var checkoffId = $('#email_checkoffId').val();
        var schoolTopicId = $('#email_schoolTopicId').val();
        var evaluationType = $('#email_evaluationType').val();
        var email = $('#email_emailId').val();
        var rotationId = $('#email_rotationId').val();
        var evaluationId = $('#email_evaluationId').val();
        var activityId = $('#email_activityId').val();
        var soapNoteId = $('#email_soapNoteId').val();

        // console.log(preceptorNum);


        var emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/;
        if (email == '') {
            alertify.error('Please enter Email');
            return false;
        }

        if (emailRegex.test(email)) {

        } else {
            alertify.error('Invalid Email');
            return false;
        }

        $('.btnSendEmail').html(loadingText());
        $('.btnSendEmail').prop('disabled', true);
        $.ajax({
            type: "POST",
            url: "../ajax/ajax_copy_link.html",
            data: {
                preceptorId: preceptorId,
                preceptorNum: preceptorNum,
                checkoffId: checkoffId,
                schoolTopicId: schoolTopicId,
                rotationId: rotationId,
                evaluationId: evaluationId,
                evaluationType: evaluationType,
                email: email,
                isSendEmail: 1,
                activityId: activityId,
                soapNoteId:soapNoteId

            },
            success: function(data) {
                alertify.success('Sent');
                $('.btnSendEmail').html('Send');
                $('.btnSendEmail').prop('disabled', false);
                // window.location.reload();
            }
        });
    });
</script>