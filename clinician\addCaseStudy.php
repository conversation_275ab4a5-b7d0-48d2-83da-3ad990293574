<?php
$IsMobile = isset($_GET['IsMobile']) ? ($_GET['IsMobile']) : 0;

include('../includes/config.php');
include('includes/validateUserLogin.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsRotation.php');
include('../class/clsInteraction.php');
include('../class/clsCaseStudy.php');
include('../class/clsLocations.php');
include('../setRequest.php');

$schoolId = 0;
$schoolId = $currentSchoolId;
$page_title = "Add Case Study";
$bedCrumTitle = 'Add';
$studentId = 0;
$caseStudyId = 0;
$rotationId = 0;
$floorCohort = '';
$floorAdmissionDate = '';
$floorPtAge = '';
$floorSex = '';
$floorHt = '';
$floorSmoking = '';
$floorChiefComplaint = '';
$floorRespiratory = '';
$floorOtherPulmonaryProblems = '';
$floorHR = '';
$floorSpontRR = '';
$floorBP = '';
$floorTemp = '';
$floorSpO2 = '';
$floorIO = '';
$floorBreathSounds = '';
$floorLevelofActivity = '';
$floorNa = '';
$floorK = '';
$floorCl = '';
$floorWBC = '';
$floorHgb = '';
$floorHct = '';
$floorCO2 = '';
$floorBUN = '';
$floorGlucose = '';
$floorPlatelets = '';
$floorINR = '';
$floorSputumCult = '';
$floorCreatinine = '';
$floorLabInterpretation = '';
$floorXRayInterpretation = '';
$floorEKG = '';
$floorTrachSize = '';
$floorTrachType = '';
$floorCuffPressure = '';
$floorpH = '';
$floorPaCO2 = '';
$floorHCO3 = '';
$floorFVC = '';
$floorFEF25 = '';
$floorFEF1 = '';
$floorPaO2 = '';
$floorSaO2 = '';
$floorFiO2 = '';
$floorPEFR = '';
$floorFEV1 = '';
$floorDateBloodGas = '';
$floorLungValues = '';
$floorInterpretationABG = '';
$floorInterpretationPFT = '';
$floorInterpretationPAO2 = '';
$floorInterpretationAO2 = '';
$floorInterpretationCaO2 = '';
$floorInterpretationPFRatio = '';
$floorIPAP = '';
$floorEPAP = '';
$floorRate = '';
$floorFiO2Setting = '';
$studentcomments = '';
/* 21042021 */
$clinician_comments = '';
$school_comments = '';
// $school_comments = 'No';
/* 21042021 */

$floorItime = '';
$floorRise = '';
$floorRamp = '';
$floorHumidityTemp = '';
$floorSuction = '';
$floorCough = '';
$floorSputumAmount = '';
$flooMedicationsUse1 = '';
$floorModificationCarePlan1 = '';
$flooMedicationsUse2 = '';
$floorModificationCarePlan2 = '';
$flooMedicationsUse3  = '';
$floorModificationCarePlan3  = '';
$flooMedicationsUse4  = '';
$floorModificationCarePlan4  = '';
$flooMedicationsUse5  = '';
$floorModificationCarePlan5  = '';
$flooMedicationsUse6  = '';
$floorModificationCarePlan6  = '';
$flooMedicationsUse7  = '';
$floorModificationCarePlan7  = '';
$flooMedicationsUse8  = '';
$floorModificationCarePlan8  = '';
$flooMedicationsUse9  = '';
$floorModificationCarePlan9  = '';
$flooMedicationsUse10  = '';
$floorModificationCarePlan10  = '';
$flooMedicationsUse11  = '';
$floorModificationCarePlan11  = '';
$flooMedicationsUse12  = '';
$floorModificationCarePlan12  = '';
$flooMedicationsUse13  = '';
$floorModificationCarePlan13  = '';
$flooMedicationsUse14  = '';
$floorModificationCarePlan14  = '';
$flooMedicationsUse15  = '';
$floorModificationCarePlan15  = '';
$flooMedicationsUseList  = '';
$floorModificationCarePlanList  = '';
$caseStudydate = '';
$clinicianDate = '';
$isClinicianSignoff = 0;
$schoolDate = '';
$floorPastMedicalHx = '';
$firstName = '';
$lastName = '';
$fullName = '';
$clinicianId = $_SESSION['loggedClinicianId'];
$loggedClinicianType = $_SESSION['loggedClinicianType'];
$TimeZone = $_SESSION["loggedClinicianSchoolTimeZone"];
$view = '';

//object
$objRotation = new clsRotation();

if (isset($_GET['rotationId'])) {
    $DefaultrotationId = $_GET['rotationId'];
    $rotationId = DecodeQueryData($DefaultrotationId);
}

if (isset($_GET['view'])) {
    $view = $_GET['view'];
}

if (isset($_GET['caseStudyId'])) //Edit Mode
{
    $caseStudyId = $_GET['caseStudyId'];
    $caseStudyId = DecodeQueryData($caseStudyId);
    $title = "Edit Case Study";
    $bedCrumTitle = 'Edit';

    $objCaseStudy = new clsCaseStudy();
    $row = $objCaseStudy->GetFloorCaseStudyForStudent($caseStudyId);
    unset($objCaseStudy);
    if ($row == '') {
        header('location:caseStudyList.html');
    }

    $rotationId  = stripslashes($row['rotationId']);
    $schoolDate  = ($row['schoolDate']);
    $courselocationId = $row['locationId'];
    $parentRotationId = stripslashes($row['parentRotationId']);
    $rotationLocationId = stripslashes($row['rotationLocationId']);
    $locationId = 0;

    if ($rotationLocationId != $courselocationId && $parentRotationId > 0) {
        if (!$rotationLocationId)
            $locationId = $objRotation->GetLocationByRotation($rotationId);
        else
            $locationId  = $rotationLocationId;
    } else {
        $locationId  = $courselocationId;
    }

    //Get Time Zone By Rotation 
    $objLocation = new clsLocations();
    $TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
    unset($objLocation);
    if ($TimeZone == '')
        $TimeZone = $_SESSION["loggedClinicianSchoolTimeZone"];

    if ($schoolDate) {
        $schoolDate = converFromServerTimeZone($schoolDate, $TimeZone);
        $schoolDate = date('m/d/Y', strtotime($schoolDate));
    }
    $caseStudydate  = ($row['caseStudydate']);
    if ($caseStudydate) {
        $caseStudydate = converFromServerTimeZone($caseStudydate, $TimeZone);
        $caseStudydate = date('m/d/Y', strtotime($caseStudydate));
    }

    $clinicianDate  = stripslashes($row['ClinicianDate']);
    $studentcomments  = stripslashes($row['studentcomments']);
    // if ($studentcomments == '')
    //     $studentcomments = 'No';
    /* 21042021 */
    $clinician_comments = stripslashes($row['clinician_comments']);
    $clinician_comments = trim($clinician_comments);

    $school_comments  = stripslashes($row['school_comments']);
    // if ($school_comments != '') {
    //     $school_comments = 'Yes';
    // } else {
    //     $school_comments = 'No';
    // }
    /* 21042021 */

    if ($clinicianDate) {
        $isClinicianSignoff = 1;
        $clinicianDate = converFromServerTimeZone($clinicianDate, $TimeZone);
        $clinicianDate = date('m/d/Y', strtotime($clinicianDate));
    }
    $firstName  = ($row['firstName']);
    $lastName  = ($row['lastName']);
    $fullName = $firstName . ' ' . $lastName;
    $floorCohort  = ($row['floorCohort']);
    $floorAdmissionDate  = ($row['floorAdmissionDate']);
    $floorPtAge  = ($row['floorPtAge']);
    $floorSex  = ($row['floorSex']);
    $floorHt  = ($row['floorHt']);

    $floorSmoking  = ($row['floorSmoking']);
    $floorChiefComplaint  = ($row['floorChiefComplaint']);
    $floorRespiratory  = ($row['floorRespiratory']);
    $floorPastMedicalHx  = ($row['floorPastMedicalHx']);
    $floorOtherPulmonaryProblems  = ($row['floorOtherPulmonaryProblems']);
    $floorHR  = ($row['floorHR']);
    $floorSpontRR  = ($row['floorSpontRR']);
    $floorBP  = ($row['floorBP']);
    $floorTemp  = ($row['floorTemp']);
    $floorSpO2  = ($row['floorSpO2']);
    $floorIO  = ($row['floorIO']);
    $floorBreathSounds  = ($row['floorBreathSounds']);
    $floorLevelofActivity  = ($row['floorLevelofActivity']);
    $floorNa  = ($row['floorNa']);
    $floorK  = ($row['floorK']);

    $floorCl  = ($row['floorCl']);
    $floorWBC  = ($row['floorWBC']);
    $floorHgb  = ($row['floorHgb']);
    $floorHct  = ($row['floorHct']);
    $floorCO2  = ($row['floorCO2']);
    $floorBUN  = ($row['floorBUN']);
    $floorGlucose  = ($row['floorGlucose']);
    $floorPlatelets  = ($row['floorPlatelets']);
    $floorINR  = ($row['floorINR']);
    $floorSputumCult  = ($row['floorSputumCult']);
    $floorCreatinine  = ($row['floorCreatinine']);
    $floorLabInterpretation  = ($row['floorLabInterpretation']);
    $floorXRayInterpretation  = ($row['floorXRayInterpretation']);
    $floorEKG  = ($row['floorEKG']);
    $floorTrachSize  = ($row['floorTrachSize']);
    $floorTrachType  = ($row['floorTrachType']);
    $floorCuffPressure  = ($row['floorCuffPressure']);
    $floorpH  = ($row['floorpH']);
    $floorPaCO2  = ($row['floorPaCO2']);
    $floorHCO3  = ($row['floorHCO3']);
    $floorFVC  = ($row['floorFVC']);
    $floorFEF25  = ($row['floorFEF25']);
    $floorFEF1  = ($row['floorFEF1']);
    $floorPaO2  = ($row['floorPaO2']);
    $floorSaO2  = ($row['floorSaO2']);
    $floorFiO2  = ($row['floorFiO2']);
    $floorPEFR  = ($row['floorPEFR']);
    $floorFEV1  = ($row['floorFEV1']);
    $floorDateBloodGas  = ($row['floorDateBloodGas']);
    $floorLungValues  = ($row['floorLungValues']);
    $floorInterpretationABG  = ($row['floorInterpretationABG']);

    $floorInterpretationPFT  = ($row['floorInterpretationPFT']);
    $floorInterpretationPAO2  = ($row['floorInterpretationPAO2']);
    $floorInterpretationAO2 = ($row['floorInterpretationAO2']);
    $floorInterpretationCaO2  = ($row['floorInterpretationCaO2']);
    $floorInterpretationPFRatio  = ($row['floorInterpretationPFRatio']);
    $floorIPAP  = ($row['floorIPAP']);
    $floorEPAP  = ($row['floorEPAP']);

    $floorRate  = ($row['floorRate']);
    $floorFiO2Setting  = ($row['floorFiO2Setting']);

    $floorItime  = ($row['floorItime']);
    $floorRise  = ($row['floorRise']);
    $floorRamp  = ($row['floorRamp']);
    $floorHumidityTemp  = ($row['floorHumidityTemp']);
    $floorSuction  = ($row['floorSuction']);
    $floorCough  = ($row['floorCough']);

    $floorSputumAmount  = ($row['floorSputumAmount']);
    $flooMedicationsUse1  = ($row['flooMedicationsUse1']);
    $floorModificationCarePlan1  = ($row['floorModificationCarePlan1']);
    $flooMedicationsUse2  = ($row['flooMedicationsUse2']);
    $floorModificationCarePlan2  = ($row['floorModificationCarePlan2']);
    $flooMedicationsUse3  = ($row['flooMedicationsUse3']);
    $floorModificationCarePlan3  = ($row['floorModificationCarePlan3']);
    $flooMedicationsUse4  = ($row['flooMedicationsUse4']);
    $floorModificationCarePlan4  = ($row['floorModificationCarePlan4']);
    $flooMedicationsUse5  = ($row['flooMedicationsUse5']);
    $floorModificationCarePlan5  = ($row['floorModificationCarePlan5']);
    $flooMedicationsUse6  = ($row['flooMedicationsUse6']);
    $floorModificationCarePlan6  = ($row['floorModificationCarePlan6']);
    $flooMedicationsUse7  = ($row['flooMedicationsUse7']);
    $floorModificationCarePlan7  = ($row['floorModificationCarePlan7']);
    $flooMedicationsUse8  = ($row['flooMedicationsUse8']);
    $floorModificationCarePlan8  = ($row['floorModificationCarePlan8']);
    $flooMedicationsUse9  = ($row['flooMedicationsUse9']);
    $floorModificationCarePlan9  = ($row['floorModificationCarePlan9']);
    $flooMedicationsUse10  = ($row['flooMedicationsUse10']);
    $floorModificationCarePlan10  = ($row['floorModificationCarePlan10']);
    $flooMedicationsUse11  = ($row['flooMedicationsUse11']);
    $floorModificationCarePlan11  = ($row['floorModificationCarePlan11']);
    $flooMedicationsUse12  = ($row['flooMedicationsUse12']);
    $floorModificationCarePlan12  = ($row['floorModificationCarePlan12']);
    $flooMedicationsUse13  = ($row['flooMedicationsUse13']);
    $floorModificationCarePlan13  = ($row['floorModificationCarePlan13']);
    $flooMedicationsUse14  = ($row['flooMedicationsUse14']);
    $floorModificationCarePlan14  = ($row['floorModificationCarePlan14']);
    $flooMedicationsUse15  = ($row['flooMedicationsUse15']);
    $floorModificationCarePlan15  = ($row['floorModificationCarePlan15']);
    $flooMedicationsUseList  = ($row['flooMedicationsUseList']);
    $floorModificationCarePlanList  = ($row['floorModificationCarePlanList']);
}

//$rotation=$objRotation->GetRotationByStudent($schoolId,$studentId);
$rotation = $objRotation->GetAllActiveRotation($schoolId, $studentId);
unset($objRotation);
$readonly = ($caseStudydate != '') ? 'readonly' : '';
$readonly = ($caseStudydate != '') ? 'readonly' : '';

$clinicianDate = ($clinicianDate != '') ? $clinicianDate : date('m/d/Y');

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title>
        <?php echo ($page_title); ?>
    </title>
    <?php include('includes/headercss.php'); ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/stepper.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">


    <style>
        .d-flex {
            display: flex;
        }

        .margin-right {
            margin-right: 7px;
        }

        .checkboxStyle {
            margin: 0 !important;
            width: 50%;
            border: 1px solid #ccc;
        }

        .alignCenter {
            align-items: center !important;
        }

        .abnormal {
            color: red !important;
        }

        .input-group-addon {
            position: absolute;
            right: 7px;
            /* width: 100%; */
            z-index: 99;
            width: 35px;
            margin: auto;
            top: 5px;
            border-left: 1px solid #ccc;
            border-radius: 50% !important;
            padding: 10px -2px;
            height: 35px;
            /* background: #01A750; */
            /* color: #fff; */
            color: #555;
            /* background: #f6f9f9; */
            background: transparent;
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .panel-default>.panel-heading {
            background-color: transparent !important;
        }

        .btn-success,
        .btn-default {
            padding: 8px 25px;
            border-radius: 10px;
        }

        .panel {
            border-radius: 14px !important;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 45px !important;
        }

        .required-select2 {
            border-left: solid 3px red !important;
            border-radius: 12px !important;
        }

        .select2-container--default .select2-selection--single {
            background-color: #f6f9f9 !important;
            cursor: default !important;
            height: 45px !important;
            border-radius: 10px !important;
        }

        .select2-container--default .select2-selection--single {
            border: none !important;
        }

        .panel,
        .form-group {
            margin-bottom: 10px;
        }

        .bootstrap-datetimepicker-widget {
            border-radius: 12px !important;
        }

        .form-control {
            height: 45px;
        }

        textarea.form-control {
            height: auto;
        }

        .pl-0 {
            padding-left: 0;
        }

        .blood-pressure-input {
            width: 80px;
            display: inline-block;
            margin: 5px 10px;
        }

        .abnormal-value-checkbox {
            padding: 0;
            width: 37px;
            height: 37px;
            margin-left: 10px;
        }

        .row-delete-icon {
            font-size: 18px;
            padding-left: 6px;
        }

        .mobile-block {
            display: flex;
            align-items: center;
        }

        /* .form-horizontal .form-group {
            margin-right: 0;
            margin-left: 0;
        } */

        .select2-container {
            width: 100% !important;
        }

        ion-icon {
            pointer-events: none;
        }

        .fa-chevron-down {
            font-size: 13px;
            font-weight: 500;
            color: #000000bd;
        }

        .icon-inner,
        .ionicon,
        svg {
            color: #01A750;
        }

        @media screen and (max-width: 500px) {
            .panel-body ol {
                padding-left: 20px;
            }

            .panel-default>.panel-heading {
                padding-left: 5px;
            }

            .blood-pressure-input {
                width: 100%;
                margin: 5px 0;
            }

            /* .container-zero{
            padding: 0;
         } */

            .mobile-gray-border {
                border: 1px solid #ececec;
            }

            .collapsible {
                cursor: pointer;
                padding: 15px;
                /* border: 1px solid #181818; */
                /* background-color: #f9f9f9; */
                display: flex;
                justify-content: space-between;
                align-items: center;
                /* border-radius: 14px; */
            }

            .collapsible p {
                margin: 0;
            }

            .collapsible-arrow {
                font-size: 18px;
                transition: transform 0.3s ease;
            }

            .content {
                display: none;
                padding: 10px 0;
                /* border-top: 1px solid #ececec; */
            }

            .content.active {
                display: block;
            }

            .active.collapsible-arrow {
                transform: rotate(180deg);
            }

            .row-delete-icon {
                position: absolute;
                top: -82px;
                right: 20px;
            }

            .mobile-block {
                display: block;
            }

            .form-stepper-horizontal li {
                min-width: 90px;
            }
        }
    </style>
</head>

<body>
    <?php if ($IsMobile == 0) { ?>

        <?php include('includes/header.php'); ?>

        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
                        <li><a href="dashboard.html">Home</a></li>
                        <li><a href="caseStudyList.html?active=floor">Case Study</a></li>
                        <li><a href="caseStudyList.html?active=floor">Floor Therapy</a></li>
                        <li class="active">
                            <?php echo ($bedCrumTitle); ?>
                        </li>
                    </ol>
                </div>

            </div>
        </div>
    <?php  }   ?>


    <div class="container mb-15 mobile-padding-4">

        <!-- stepper start -->
        <div>
            <div id="multi-step-form-container">
                <!-- Form Steps / Progress Bar -->
                <ul class="form-stepper form-stepper-horizontal text-center mx-auto">
                    <!-- Step 1 -->
                    <li class="form-stepper-active text-center form-stepper-list" style="" step="1">
                        <a class="mx-2">
                            <span class="form-stepper-circle">
                                <span>1</span>
                            </span>
                            <div class="label stepper-label" style="">Details</div>
                        </a>
                    </li>
                    <!-- Step 2 -->
                    <li class="form-stepper-unfinished text-center form-stepper-list" step="2">
                        <a class="mx-2">
                            <span class="form-stepper-circle text-muted">
                                <span>2</span>
                            </span>
                            <div class="label text-muted stepper-label">Vitals</div>
                        </a>
                    </li>
                    <!-- Step 3 -->
                    <li class="form-stepper-unfinished text-center form-stepper-list" step="3">
                        <a class="mx-2">
                            <span class="form-stepper-circle text-muted">
                                <span>3</span>
                            </span>
                            <div class="label text-muted stepper-label">Readings</div>
                        </a>
                    </li>
                    <!-- Step 4 -->
                    <li class="form-stepper-unfinished text-center form-stepper-list" step="4">
                        <a class="mx-2">
                            <span class="form-stepper-circle text-muted">
                                <span>4</span>
                            </span>
                            <div class="label text-muted stepper-label">Medications</div>
                        </a>
                    </li>
                    <!-- Step 5 -->
                    <li class="form-stepper-unfinished text-center form-stepper-list" step="5">
                        <a class="mx-2">
                            <span class="form-stepper-circle text-muted">
                                <span>5</span>
                            </span>
                            <div class="label text-muted stepper-label">Comments</div>
                        </a>
                    </li>
                    <!-- Step 6 -->
                    <!-- <li class="form-stepper-unfinished text-center form-stepper-list" step="6">
                        <a class="mx-2">
                            <span class="form-stepper-circle text-muted">
                                <span>6</span>
                            </span>
                            <div class="label text-muted stepper-label">Comment</div>
                        </a>
                    </li> -->
                </ul>
            </div>
        </div>

        <!-- Step Wise Form Content -->
        <form id="formCaseStudy" data-parsley-validate class="form-horizontal" method="POST" action="addCaseStudySubmit.html?caseStudyId=<?php echo EncodeQueryData($caseStudyId); ?>">
            <input type="hidden" name="IsMobile" id="IsMobile" value="<?php echo $IsMobile; ?>">

            <!-- Step 1 Content -->
            <section id="step-1" class="form-step" data-parsley-validate>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="caseStudydate"> Date</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <div class='input-group date w-full' id='caseStudydate' style="position: relative;">
                                    <input type='text' name="caseStudydate" id="caseStudydate" class="form-control  input-md required-input dateInputFormat" value="<?php echo $caseStudydate; ?>" readonly required data-parsley-errors-container="#error-caseStudydate" placeholder="MM-DD-YYYY" />
                                    <span class="input-group-addon calender-icon">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>
                                </div>
                                <div id="error-caseStudydate"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="cborotation">Rotation</label>
                            <div class="col-md-12">
                                <select id="cborotation" name="cborotation" class="form-control input-md  select2_single required-input" required data-parsley-errors-container="#error-cborotation" disabled>
                                    <option value="" selected>Select</option>
                                    <?php
                                    if ($rotation != "") {
                                        while ($row = mysqli_fetch_assoc($rotation)) {
                                            $selrotationId  = $row['rotationId'];
                                            $name  = stripslashes($row['title']);
                                    ?>
                                            <option value="<?php echo EncodeQueryData($selrotationId); ?>" <?php if ($rotationId == $selrotationId) { ?> selected="true" <?php } ?>>
                                                <?php echo ($name); ?>
                                            </option>
                                    <?php
                                        }
                                    }
                                    ?>
                                </select>
                                <div id="error-cborotation"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="studentName">Student Name</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <input type='text' name="studentName" class="form-control input-md " value="<?php echo $fullName; ?>" readonly />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="ClinicianDate">Clinician Signature</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <div class='input-group date w-full' id='ClinicianDate' style="position: relative;">
                                    <input type='text' name="ClinicianDate" id="ClinicianDate" class="form-control input-md required-input dateInputFormat" value="<?php echo $clinicianDate; ?>" data-parsley-errors-container="#error-txtClinicianDate" placeholder="MM-DD-YYYY" />
                                    <span class="input-group-addon calender-icon">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>
                                </div>
                                <div id="error-txtClinicianDate"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="schoolDate">School Signature</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <div class='input-group date w-full' id='schoolDate' style="position: relative;">
                                    <input type='text' name="schoolDate" id="schoolDate" class="form-control input-md dateInputFormat" value="<?php echo $schoolDate; ?>" placeholder="MM-DD-YYYY" readonly />
                                    <span class="input-group-addon calender-icon">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="floorCohort">Cohort</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <input type="text" class="form-control input-md" value="<?php echo $floorCohort; ?>" name="floorCohort" id="floorCohort">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="floorAdmissionDate">Admission Date</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <div class='input-group date w-full' id='floorAdmissionDate' style="position: relative;">
                                    <input type='text' name="floorAdmissionDate" id="floorAdmissionDate" class="form-control input-md dateInputFormat" value="<?php echo $floorAdmissionDate; ?>" placeholder="MM-DD-YYYY" />
                                    <span class="input-group-addon calender-icon">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 p-0">
                        <div class=" col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="floorPtAge">Pt Age</label>
                                <div class="col-md-12 col-sm-12 col-xs-12">
                                    <input type="text" class="form-control input-md" value="<?php echo $floorPtAge; ?>" name="floorPtAge" id="floorPtAge">
                                </div>
                            </div>
                        </div>
                        <div class=" col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="Sex">Sex</label>
                                <div class="col-md-12 col-sm-12 col-xs-12 margin_right_zero">
                                    <input type="text" class="form-control input-md" value="<?php echo $floorSex; ?>" name="floorSex" id="floorSex">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 p-0">
                        <div class=" col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="floorHt">Ht</label>
                                <div class="col-md-12 col-sm-12 col-xs-12">
                                    <input type="text" class="form-control input-md" value="<?php echo $floorHt; ?>" name="floorHt" id="floorHt">
                                </div>
                            </div>
                        </div>
                        <div class=" col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="floorSmoking">Smoking: (Pack/Yrs)</label>
                                <div class="col-md-12 col-sm-12 col-xs-12 margin_right_zero">
                                    <input type="text" class="form-control input-md" value="<?php echo $floorSmoking; ?>" name="floorSmoking" id="floorSmoking">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="floorChiefComplaint">Chief Complaint / Admitting Diagnosis:</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <input type="text" class="form-control input-md" value="<?php echo $floorChiefComplaint; ?>" name="floorChiefComplaint" id="floorChiefComplaint">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="floorRespiratory">Respiratory Rx(s)</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <input type="text" class="form-control input-md" value="<?php echo $floorRespiratory; ?>" name="floorRespiratory" id="floorRespiratory">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="floorPastMedicalHx">Past Medical Hx</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <input type="text" class="form-control input-md" value="<?php echo $floorPastMedicalHx; ?>" name="floorPastMedicalHx" id="floorPastMedicalHx">
                            </div>
                        </div>
                    </div>

                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="floorOtherPulmonaryProblems">Other Pulmonary Problems:</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <input type="text" class="form-control input-md" value="<?php echo $floorOtherPulmonaryProblems; ?>" name="floorOtherPulmonaryProblems" id="floorOtherPulmonaryProblems">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-3 flex-end">
                    <button class="button next btn-navigate-form-step" type="button" step_number="2">Next</button>
                </div>
            </section>
            <!-- Step 2 Content, default hidden on page load. -->
            <section id="step-2" class="form-step d-none" data-parsley-validate>
                <div class="row padding_top_ten margin_bottom_ten">
                    <!-- <div class="row text-center margin_bottom_five">
                        <label><b>Vital Signs</b></label>
                    </div> -->
                    <div class="row m-0">
                        <div class="col-md-6 p-0">
                            <div class=" col-md-4">
                                <div class="form-group">
                                    <label class="col-md-12 control-label" for="floorHR">HR</label>
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <input type="text" class="form-control input-md" value="<?php echo $floorHR; ?>" name="floorHR" id="floorHR" readonly>
                                    </div>
                                </div>
                            </div>
                            <div class=" col-md-4">
                                <div class="form-group">
                                    <label class="col-md-12 control-label" for="floorSpontRR">Spont. RR</label>
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <input type="text" class="form-control input-md" value="<?php echo $floorSpontRR; ?>" name="floorSpontRR" id="floorSpontRR" readonly>
                                    </div>
                                </div>
                            </div>
                            <div class=" col-md-4">
                                <div class="form-group">
                                    <label class="col-md-12 control-label" for="floorBP">BP</label>
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <input type="text" class="form-control input-md" value="<?php echo $floorBP; ?>" name="floorBP" id="floorBP" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 p-0">
                            <div class=" col-md-4">
                                <div class="form-group">
                                    <label class="col-md-12 control-label" for="">Temp</label>
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <input type="text" class="form-control input-md" value="<?php echo $floorTemp; ?>" name="floorTemp" id="floorTemp" readonly>
                                    </div>
                                </div>
                            </div>
                            <div class=" col-md-4">
                                <div class="form-group">
                                    <label class="col-md-12 control-label" for="floorSpO2">SpO2</label>
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <input type="text" class="form-control input-md" value="<?php echo $floorSpO2; ?>" name="floorSpO2" id="floorSpO2" readonly>
                                    </div>
                                </div>
                            </div>
                            <div class=" col-md-4">
                                <div class="form-group">
                                    <label class="col-md-12 control-label" for="floorIO">I/O</label>
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <input type="text" class="form-control input-md" value="<?php echo $floorIO; ?>" name="floorIO" id="floorIO" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row m-0">
                        <div class="col-md-12 p-0">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-md-12 control-label" for="floorBreathSounds">Breath Sounds</label>
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <input type="text" class="form-control input-md" value="<?php echo $floorBreathSounds; ?>" name="floorBreathSounds" id="floorBreathSounds" readonly>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-md-12 control-label" for="floorLevelofActivity">Level of Activity/Cooperation</label>
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <input type="text" class="form-control input-md" value="<?php echo $floorLevelofActivity; ?>" name="floorLevelofActivity" id="floorLevelofActivity" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-3 flex-end">
                    <button class="button btn-navigate-form-step button-prev" type="button" step_number="1">Prev</button>
                    <button class="button next btn-navigate-form-step" type="button" step_number="3">Next</button>
                </div>
            </section>
            <!-- Step 3 Content, default hidden on page load. -->
            <section id="step-3" class="form-step d-none " data-parsley-validate>
                <div class="row padding_top_ten margin_bottom_ten w-full" style="margin: 10px auto;">
                    <!-- <div class="row">
                            <div class="col-md-6 text-center margin_bottom_five">
                                <label><b>Chemistry Panel</b></label>
                            </div>
                            <div class="col-md-6 text-center margin_bottom_five">
                                <label><b>Hematology Panel</b></label>
                            </div>
                        </div> -->
                    <div class="row">
                        <div class="col-md-12 desktop-px-zero">
                            <div class="col-md-6 p-0 mb-10 mobile-padding-zero mobile-collaps-view">
                                <div class="collapsible">
                                    <p style="text-align: center;"> <b> Chemistry Panel </b></p>
                                    <i class="fa fa-chevron-down desktop-hide" aria-hidden="true"></i>
                                </div>
                                <div class="content">
                                    <div class="col-md-12 p-0">
                                        <div class=" col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="floorNa">Na+ </label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $floorNa; ?>" name="floorNa" id="floorNa" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class=" col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="floorK">K+</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $floorK; ?>" name="floorK" id="floorK" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class=" col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="floorCl">Cl+</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $floorCl; ?>" name="floorCl" id="floorCl" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class=" col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="floorCO2">CO2 </label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $floorCO2; ?>" name="floorCO2" id="floorCO2" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class=" col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="floorBUN">BUN</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $floorBUN; ?>" name="floorBUN" id="floorBUN" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class=" col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="floorGlucose">Glucose</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $floorGlucose; ?>" name="floorGlucose" id="floorGlucose" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="">Creatinine </label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $floorCreatinine; ?>" name="floorCreatinine" id="floorCreatinine" readonly>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 p-0 mb-10 mobile-padding-zero mobile-collaps-view">
                                <div class="collapsible">
                                    <p style="text-align: center;"><b> Hematology Panel </b></p>
                                    <i class="fa fa-chevron-down desktop-hide" aria-hidden="true"></i>
                                </div>
                                <div class="content">
                                    <div class="col-md-12 p-0">

                                        <div class=" col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="floorWBC">WBC</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $floorWBC; ?>" name="floorWBC" id="floorWBC" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class=" col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="floorHgb">Hgb</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $floorHgb; ?>" name="floorHgb" id="floorHgb" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class=" col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="floorHct">Hct</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $floorHct; ?>" name="floorHct" id="floorHct" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class=" col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="floorPlatelets">Platelets</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $floorPlatelets; ?>" name="floorPlatelets" id="floorPlatelets" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class=" col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="floorINR">INR</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $floorINR; ?>" name="floorINR" id="floorINR" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class=" col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="floorSputumCult">Sputum Cult</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $floorSputumCult; ?>" name="floorSputumCult" id="floorSputumCult" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="floorLabInterpretation">Lab Interpretation </label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <textarea type="text" class="form-control input-md w-full" name="floorLabInterpretation" id="floorLabInterpretation" rows="3" readonly><?php echo $floorLabInterpretation; ?></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- X-ray section start -->
                            <div class="col-md-12 p-0 mb-10 mobile-padding-zero mt-10 mobile-collaps-view">
                                <div class="collapsible">
                                    <p style="text-align: center;"><b> X-Ray </b></p>
                                    <i class="fa fa-chevron-down desktop-hide" aria-hidden="true"></i>
                                </div>
                                <div class="content">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="floorX-RayInterpretation">X-Ray Interpretation </label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <textarea type="text" class="form-control input-md" name="floorXRayInterpretation" id="floorXRayInterpretation" rows="3" readonly><?php echo $floorXRayInterpretation; ?></textarea>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label class="col-md-12 control-label" for="floorEKG">EKG</label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                                        <input type="text" class="form-control input-md" value="<?php echo $floorEKG; ?>" name="floorEKG" id="floorEKG" readonly>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label class="col-md-12 control-label" for="floorTrachSize">Trach size</label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                                        <input type="text" class="form-control input-md" value="<?php echo $floorTrachSize; ?>" name="floorTrachSize" id="floorTrachSize" readonly>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label class="col-md-12 control-label" for="floorTrachType">Trach Type</label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                                        <input type="text" class="form-control input-md" value="<?php echo $floorTrachType; ?>" name="floorTrachType" id="floorTrachType" readonly>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label class="col-md-12 control-label" for="">Cuff Pressure</label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                                        <input type="text" class="form-control input-md" value="<?php echo $floorCuffPressure; ?>" name="floorCuffPressure" id="floorCuffPressure" readonly>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- X-ray section end -->
                            <div class="col-md-6 p-0 mb-10 mobile-padding-zero mt-10 mobile-collaps-view">
                                <!-- <div class="row">
                                    <div class="col-md-6 text-center margin_bottom_five">
                                        <label><b>ABG</b></label>
                                    </div>
                                    <div class="col-md-6 text-center margin_bottom_five">
                                        <label><b>PFT</b></label>
                                    </div>
                                </div> -->
                                <div class="collapsible">
                                    <p style="text-align: center;"> <b> ABG </b></p>
                                    <i class="fa fa-chevron-down desktop-hide" aria-hidden="true"></i>
                                </div>
                                <div class="content">
                                    <div class="col-md-12 p-0">
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="floorpH">pH</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $floorpH; ?>" name="floorpH" id="floorpH" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="floorPaCO2">PaCO2</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $floorPaCO2; ?>" name="floorPaCO2" id="floorPaCO2" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="floorHCO3">HCO3</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $floorHCO3; ?>" name="floorHCO3" id="floorHCO3" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="floorPaO2">PaO2</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $floorPaO2; ?>" name="floorPaO2" id="floorPaO2" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="floorSaO2">SaO2</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $floorSaO2; ?>" name="floorSaO2" id="floorSaO2" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="floorFiO2">FiO2 / Lpm</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $floorFiO2; ?>" name="floorFiO2" id="floorFiO2" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="floorDateBloodGas">Date / Time Blood Gas Obtained</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $floorDateBloodGas; ?>" name="floorDateBloodGas" id="floorDateBloodGas" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="floorInterpretationABG">Interpretation</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <textarea type="text" class="form-control input-md" name="floorInterpretationABG" id="floorInterpretationABG" rows="3" readonly><?php echo $floorInterpretationABG; ?></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 p-0 mb-10 mobile-padding-zero mt-10 mobile-collaps-view">
                                <div class="collapsible">
                                    <p style="text-align: center;"> <b> PFT </b></p>
                                    <i class="fa fa-chevron-down desktop-hide" aria-hidden="true"></i>
                                </div>
                                <div class="content">
                                    <div class="col-md-12 p-0">
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="floorFVC">FVC</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $floorFVC; ?>" name="floorFVC" id="floorFVC" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="floorFEF25">FEF 25-75</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $floorFEF25; ?>" name="floorFEF25" id="floorFEF25" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="floorFEF1">FEV1</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $floorFEF1; ?>" name="floorFEF1" id="floorFEF1" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="floorPEFR">PEFR</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $floorPEFR; ?>" name="floorPEFR" id="floorPEFR" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="floorFEV1">FEV1/FVC</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $floorFEV1; ?>" name="floorFEV1" id="floorFEV1" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="floorLungValues">Lung Values and % Predicted:</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <textarea type="text" class="form-control input-md" name="floorLungValues" id="floorLungValues" rows="3" readonly><?php echo $floorLungValues; ?></textarea>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="floorInterpretationPFT">Interpretation:</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <textarea type="text" class="form-control input-md" name="floorInterpretationPFT" id="floorInterpretationPFT" rows="3" readonly><?php echo $floorInterpretationPFT; ?></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>


                            <div class="col-md-12 p-0 mb-10 mobile-padding-zero mt-10 mobile-collaps-view">
                                <div class="collapsible">
                                    <p style="text-align: center;"><b> Interpretation </b></p>
                                    <i class="fa fa-chevron-down desktop-hide" aria-hidden="true"></i>
                                </div>
                                <div class="content">
                                    <div class="col-md-12">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label class="col-md-12 control-label" for="floorInterpretationPAO2">PAO2</label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                                        <textarea type="text" class="form-control input-md" name="floorInterpretationPAO2" id="floorInterpretationPAO2" rows="2" readonly><?php echo $floorInterpretationPAO2; ?></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label class="col-md-12 control-label" for="floorInterpretationAO2">(A-a)O2 </label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                                        <textarea type="text" class="form-control input-md" name="floorInterpretationAO2" id="floorInterpretationAO2" rows="2" readonly><?php echo $floorInterpretationAO2; ?></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label class="col-md-12 control-label" for="floorInterpretationCaO2">CaO2</label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                                        <textarea type="text" class="form-control input-md" name="floorInterpretationCaO2" id="floorInterpretationCaO2" rows="2" readonly><?php echo $floorInterpretationCaO2; ?></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label class="col-md-12 control-label" for="floorInterpretationP/FRatio">P/F Ratio</label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                                        <textarea type="text" class="form-control input-md" name="floorInterpretationPFRatio" id="floorInterpretationPFRatio" rows="2" readonly><?php echo $floorInterpretationPFRatio; ?></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-12 p-0 mb-10 mobile-padding-zero mt-10 mobile-collaps-view">
                                <!-- <div class="row text-center ">
                                <label class="control-label" for=""><b>BiPAP / O2 via NC settings</b></label>
                                </div> -->

                                <div class="collapsible">
                                    <p style="text-align: center;"><b> BiPAP/O2 via NC settings </b></p>
                                    <i class="fa fa-chevron-down desktop-hide" aria-hidden="true"></i>
                                </div>
                                <div class="content">
                                    <div class="col-md-12">
                                        <div class="row">
                                            <div class="col-md-2">
                                                <div class="form-group">
                                                    <label class="col-md-12 control-label" for="floorIPAP">IPAP</label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                                        <input type="text" class="form-control input-md" value="<?php echo $floorIPAP; ?>" name="floorIPAP" id="floorIPAP" readonly>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-group">
                                                    <label class="col-md-12 control-label" for="floorEPAP">EPAP</label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                                        <input type="text" class="form-control input-md" value="<?php echo $floorEPAP; ?>" name="floorEPAP" id="floorEPAP" readonly>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-group">
                                                    <label class="col-md-12 control-label" for="floorRate">Rate</label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                                        <input type="text" class="form-control input-md" value="<?php echo $floorRate; ?>" name="floorRate" id="floorRate" readonly>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-group">
                                                    <label class="col-md-12 control-label" for="floorFiO2Setting">FiO2</label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                                        <input type="text" class="form-control input-md" value="<?php echo $floorFiO2Setting; ?>" name="floorFiO2Setting" id="floorFiO2Setting" readonly>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-group">
                                                    <label class="col-md-12 control-label" for="floorItime">I-time</label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                                        <input type="text" class="form-control input-md" value="<?php echo $floorItime; ?>" name="floorItime" id="floorItime" readonly>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-group">
                                                    <label class="col-md-12 control-label" for="floorRise">Rise</label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                                        <input type="text" class="form-control input-md" value="<?php echo $floorRise; ?>" name="floorRise" id="floorRise" readonly>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label class="col-md-12 control-label" for="floorRamp">Ramp</label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                                        <select id="floorRamp" name="floorRamp" class="form-control input-md  select2_single " disabled>
                                                            <option value="">Select</option>
                                                            <option value="Yes" <?php if ($floorRamp == "Yes") { ?> selected="true" <?php } ?>>Yes</option>
                                                            <option value="No" <?php if ($floorRamp == "No") { ?> selected="true" <?php } ?>>No</option>

                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label class="col-md-12 control-label" for="floorHumidityTemp">Humidity Temp</label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                                        <input type="text" class="form-control input-md" value="<?php echo $floorHumidityTemp; ?>" name="floorHumidityTemp" id="floorHumidityTemp" readonly>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12 p-0 mb-10 mobile-padding-zero mt-10 mobile-collaps-view">
                                <div class="collapsible">
                                    <p style="text-align: center;"><b> Cough </b></p>
                                    <i class="fa fa-chevron-down desktop-hide" aria-hidden="true"></i>
                                </div>
                                <div class="content">
                                    <div class="col-md-12">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label class="col-md-12 control-label" for="">Suction</label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                                        <input type="text" class="form-control input-md" value="<?php echo $floorSuction; ?>" name="floorSuction" id="floorSuction" readonly>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label class="col-md-12 control-label" for="floorCough">Cough</label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                                        <input type="text" class="form-control input-md" value="<?php echo $floorCough; ?>" name="floorCough" id="floorCough" readonly>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label class="col-md-12 control-label" for="floorSputumAmount">Sputum Amount / Color</label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                                        <input type="text" class="form-control input-md" value="<?php echo $floorSputumAmount; ?>" name="floorSputumAmount" id="floorSputumAmount" readonly>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-3 flex-end">
                        <button class="button btn-navigate-form-step button-prev" type="button" step_number="2">Prev</button>
                        <button class="button next btn-navigate-form-step" type="button" step_number="4">Next</button>
                    </div>
            </section>
            <!-- Step 4 Content, default hidden on page load. -->
            <section id="step-4" class="form-step d-none" data-parsley-validate>
                <div class="row padding_top_ten margin_bottom_ten w-full" style="margin: 10px auto;">
                    <div class="mobile-padding-zero">
                        <div class="mobile-hide">
                            <div class="col-md-6 text-center">
                                <div class="form-group">
                                    <label class="control-label" for=""><b>List ALL Medicationsw/ Indications for use </b></label>
                                </div>
                            </div>
                            <div class="col-md-6 text-center">
                                <div class="form-group">
                                    <label class="control-label" for=""><b>Recommended Modifications to Care Plan:(if required)</b></label>
                                </div>
                            </div>
                        </div>
                        <div id="MedicationDiv" style="margin-right: 5px;">

                            <?php
                            if ($caseStudyId) {
                                // DECODE JSON FOR PRINT DATA
                                $flooMedicationsUseList = json_decode($flooMedicationsUseList, true);
                                $floorModificationCarePlanList = json_decode($floorModificationCarePlanList, true);

                                $result = '';
                                //MEARGE ARRAY 
                                if ($flooMedicationsUseList && $floorModificationCarePlanList)
                                    $result = array_map('array_merge', $flooMedicationsUseList, $floorModificationCarePlanList);
                                $listId = 0;
                                if ($result) {
                                    foreach ($result as $key => $value) {
                                        $listId = $key + 1;
                                        $value = ($value); // convert object to array
                                        // PRINT HTML
                                        $divHtml = '<div class="row" id="row_' . $listId . '" isrow="' . $listId . '"> <div class="col-md-6"> <div class="form-group"><div class="col-md-12 col-sm-12 col-xs-12  mobile-block"> <label class="control-label labelcount margin-right" for="flooMedicationsUse' . $listId . '">' . $listId . '</label><input type="text" class="form-control input-md" value="' . $value[0] . '" name="flooMedicationsUseList[' . $listId . '][]" id="flooMedicationsUse' . $listId . '" placeholder="Medications w/Indications for use" readonly> </div> </div> </div> <div class="col-md-6"> <div class="form-group"> <div class="col-md-12 col-sm-12 col-xs-12"> <textarea type="text" class="form-control input-md" rows="2" cols="5" readonly name="floorModificationCarePlanList[' . $listId . '][]" id="floorModificationCarePlan' . $listId . '" placeholder="Modifications to Care Plan">' . $value[1] . '</textarea> </div> </div> </div> </div>';
                                        echo $divHtml;
                                    }
                                } else {
                                    echo '<div class="row" >';
                                }

                            ?>

                            <?php }  ?>
                        </div>
                        <!-- <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-6 control-label" for="flooMedicationsUse1">1</label>
                            <div class="col-md-6 col-sm-4 col-xs-12">
                                <input type="text" class="form-control input-md" value="<?php echo $flooMedicationsUse1; ?>" readonly name="flooMedicationsUse1" id="flooMedicationsUse1">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <div class="col-md-12 col-sm-4 col-xs-12">
                                <textarea type="text" class="form-control input-md" rows="1" readonly cols="5" name="floorModificationCarePlan1" id="floorModificationCarePlan1"><?php echo $floorModificationCarePlan1; ?></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-6 control-label" for="flooMedicationsUse2">2</label>
                            <div class="col-md-6 col-sm-4 col-xs-12">
                                <input type="text" class="form-control input-md" value="<?php echo $flooMedicationsUse2; ?>" readonly name="flooMedicationsUse2" id="flooMedicationsUse2">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <div class="col-md-12 col-sm-4 col-xs-12">
                                <textarea type="text" class="form-control input-md" rows="1" readonly cols="5" name="floorModificationCarePlan2" id="floorModificationCarePlan2"><?php echo $floorModificationCarePlan2; ?></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-6 control-label labelcount" for="flooMedicationsUse3">3</label>
                            <div class="col-md-6 col-sm-4 col-xs-12">
                                <input type="text" class="form-control input-md" value="<?php echo $flooMedicationsUse3; ?>" readonly name="flooMedicationsUse3" id="flooMedicationsUse3">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <div class="col-md-12 col-sm-4 col-xs-12">
                                <textarea type="text" class="form-control input-md" rows="1" readonly cols="5" name="floorModificationCarePlan3" id="floorModificationCarePlan3"><?php echo $floorModificationCarePlan3; ?></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-6 control-label labelcount" for="flooMedicationsUse4">4</label>
                            <div class="col-md-6 col-sm-4 col-xs-12">
                                <input type="text" class="form-control input-md" value="<?php echo $flooMedicationsUse4; ?>" readonly name="flooMedicationsUse4" id="flooMedicationsUse4">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <div class="col-md-12 col-sm-4 col-xs-12">
                                <textarea type="text" class="form-control input-md" rows="1" readonly cols="5" name="floorModificationCarePlan4" id="floorModificationCarePlan4"><?php echo $floorModificationCarePlan4; ?></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-6 control-label labelcount" for="flooMedicationsUse5">5</label>
                            <div class="col-md-6 col-sm-4 col-xs-12">
                                <input type="text" class="form-control input-md" value="<?php echo $flooMedicationsUse5; ?>" readonly name="flooMedicationsUse5" id="flooMedicationsUse5">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <div class="col-md-12 col-sm-4 col-xs-12">
                                <textarea type="text" class="form-control input-md" rows="1" readonly cols="5" name="floorModificationCarePlan5" id="floorModificationCarePlan5"><?php echo $floorModificationCarePlan5; ?></textarea>
                            </div>
                        </div>
                    </div>
                </div> -->
                        <!-- ----------------------------Show More-------------------------------------- -->
                        <!-- <div class="row">
                    <div class="col-md-12 form-group justify-content-center" style="width: 173px;margin: 7px;padding-left: 23px;margin-left: 504px;margin-top: -6px;">
                        <input type="button" class="form-control input-md showMore_6_10" value="Show More Rows">
                    </div>
                </div> -->
                        <!-- ------------------------------------------------------------------ -->
                        <div class="row hidecol_6 hide">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-md-6 control-label labelcount" for="flooMedicationsUse6">6</label>
                                    <div class="col-md-6 col-sm-4 col-xs-12">
                                        <input type="text" class="form-control input-md" value="<?php echo $flooMedicationsUse6; ?>" readonly name="flooMedicationsUse6" id="flooMedicationsUse6">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="col-md-12 col-sm-4 col-xs-12">
                                        <textarea type="text" class="form-control input-md" rows="1" readonly cols="5" name="floorModificationCarePlan6" id="floorModificationCarePlan6"><?php echo $floorModificationCarePlan6; ?></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row hidecol_6 hide">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-md-6 control-label labelcount" for="flooMedicationsUse7">7</label>
                                    <div class="col-md-6 col-sm-4 col-xs-12">
                                        <input type="text" class="form-control input-md" value="<?php echo $flooMedicationsUse7; ?>" readonly name="flooMedicationsUse7" id="flooMedicationsUse7">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="col-md-12 col-sm-4 col-xs-12">
                                        <textarea type="text" class="form-control input-md" rows="1" readonly cols="5" name="floorModificationCarePlan7" id="floorModificationCarePlan7"><?php echo $floorModificationCarePlan7; ?></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row hidecol_6 hide">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-md-6 control-label labelcount" for="flooMedicationsUse8">8</label>
                                    <div class="col-md-6 col-sm-4 col-xs-12">
                                        <input type="text" class="form-control input-md" value="<?php echo $flooMedicationsUse8; ?>" readonly name="flooMedicationsUse8" id="flooMedicationsUse8">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="col-md-12 col-sm-4 col-xs-12">
                                        <textarea type="text" class="form-control input-md" rows="1" readonly cols="5" name="floorModificationCarePlan8" id="floorModificationCarePlan8"><?php echo $floorModificationCarePlan8; ?></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row hidecol_6 hide">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-md-6 control-label labelcount" for="flooMedicationsUse9">9</label>
                                    <div class="col-md-6 col-sm-4 col-xs-12">
                                        <input type="text" class="form-control input-md" value="<?php echo $flooMedicationsUse9; ?>" readonly name="flooMedicationsUse9" id="flooMedicationsUse9">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="col-md-12 col-sm-4 col-xs-12">
                                        <textarea type="text" class="form-control input-md" rows="1" readonly cols="5" name="floorModificationCarePlan9" id="floorModificationCarePlan9"><?php echo $floorModificationCarePlan9; ?></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row hidecol_6 hide">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-md-6 control-label labelcount" for="flooMedicationsUse10">10</label>
                                    <div class="col-md-6 col-sm-4 col-xs-12">
                                        <input type="text" class="form-control input-md" value="<?php echo $flooMedicationsUse10; ?>" readonly name="flooMedicationsUse10" id="flooMedicationsUse10">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="col-md-12 col-sm-4 col-xs-12">
                                        <textarea type="text" class="form-control input-md" rows="1" readonly cols="5" name="floorModificationCarePlan10" id="floorModificationCarePlan10"><?php echo $floorModificationCarePlan10; ?></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- ----------------------------Show More-------------------------------------- -->
                        <div class="row showLess_1 hide">
                            <div class="col-md-6 form-group justify-content-center" style="width: 164px;margin-left: 446px;">
                                <input type="button" class="form-control input-md showMore_10_15" value="Show More Rows">
                            </div>
                            <div class="col-md-6 form-group justify-content-center" style="width: 160px;margin-left: -4px;">
                                <input type="button" class="form-control input-md showLess_10_6" value="Show Less Rows">
                            </div>
                        </div>
                        <!-- ------------------------------------------------------------------ -->
                        <div class="row hidecol_10 hide">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-md-6 control-label labelcount" for="flooMedicationsUse10">11</label>
                                    <div class="col-md-6 col-sm-4 col-xs-12">
                                        <input type="text" class="form-control input-md" value="<?php echo $flooMedicationsUse11; ?>" readonly name="flooMedicationsUse11" id="flooMedicationsUse11">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="col-md-12 col-sm-4 col-xs-12">
                                        <textarea type="text" class="form-control input-md" rows="1" readonly cols="5" name="floorModificationCarePlan11" id="floorModificationCarePlan11"><?php echo $floorModificationCarePlan11; ?></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row hidecol_10 hide">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-md-6 control-label labelcount" for="flooMedicationsUse10">12</label>
                                    <div class="col-md-6 col-sm-4 col-xs-12">
                                        <input type="text" class="form-control input-md" value="<?php echo $flooMedicationsUse12; ?>" readonly name="flooMedicationsUse12" id="flooMedicationsUse12">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="col-md-12 col-sm-4 col-xs-12">
                                        <textarea type="text" class="form-control input-md" rows="1" readonly cols="5" name="floorModificationCarePlan12" id="floorModificationCarePlan12"><?php echo $floorModificationCarePlan12; ?></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row  hidecol_10 hide">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-md-6 control-label labelcount" for="flooMedicationsUse13">13</label>
                                    <div class="col-md-6 col-sm-4 col-xs-12">
                                        <input type="text" class="form-control input-md" value="<?php echo $flooMedicationsUse13; ?>" readonly name="flooMedicationsUse13" id="flooMedicationsUse13">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="col-md-12 col-sm-4 col-xs-12">
                                        <textarea type="text" class="form-control input-md" rows="1" readonly cols="5" name="floorModificationCarePlan13" id="floorModificationCarePlan13"><?php echo $floorModificationCarePlan13; ?></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row  hidecol_10 hide">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-md-6 control-label labelcount" for="flooMedicationsUse14">14</label>
                                    <div class="col-md-6 col-sm-4 col-xs-12">
                                        <input type="text" class="form-control input-md" value="<?php echo $flooMedicationsUse14; ?>" readonly name="flooMedicationsUse14" id="flooMedicationsUse14">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="col-md-12 col-sm-4 col-xs-12">
                                        <textarea type="text" class="form-control input-md" rows="1" readonly cols="5" name="floorModificationCarePlan14" id="floorModificationCarePlan14"><?php echo $floorModificationCarePlan14; ?></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row  hidecol_10 hide">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-md-6 control-label labelcount" for="flooMedicationsUse15">15</label>
                                    <div class="col-md-6 col-sm-4 col-xs-12">
                                        <input type="text" class="form-control input-md" value="<?php echo $flooMedicationsUse15; ?>" readonly name="flooMedicationsUse15" id="flooMedicationsUse15">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="col-md-12 col-sm-4 col-xs-12">
                                        <textarea type="text" class="form-control input-md" rows="1" readonly cols="5" name="floorModificationCarePlan15" id="floorModificationCarePlan15"><?php echo $floorModificationCarePlan15; ?></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- ----------------------------Show Less-------------------------------------- -->
                        <div class="row showLess_2 hide">
                            <div class="col-md-12 form-group justify-content-center" style="width: 168px;margin: 7px;padding-left: 23px;margin-left: 503px;margin-top: -6px;">
                                <input type="button" class="form-control input-md showLess_15_10" value="Show Less Rows">
                            </div>

                        </div>
                        <!-- ------------------------------------------------------------------ -->
                    </div>

                    <div class="mt-3 flex-end">
                        <button class="button btn-navigate-form-step button-prev" type="button" step_number="3">Prev</button>
                        <button class="button next btn-navigate-form-step" type="button" step_number="5">Next</button>
                    </div>
            </section>
            <!-- Step 5 Content, default hidden on page load. -->
            <section id="step-5" class="form-step d-none" data-parsley-validate>
                <div class="row padding_top_ten margin_bottom_ten">
                    <div class="col-md-12">
                        <div class="form-group m-0">
                            <label class="control-label" for=""><b>Student Comment </b></label>
                        </div>
                    </div>
                    <div class="col-md-12 text-left" style="margin-top: 5px;">
                        <div class="form-group">
                            <?php //if ($studentcomments != '') { 
                            ?>
                            <div class="col-md-12 col-sm-4 col-xs-12">
                                <textarea type="text" class="form-control input-md" name="studentcomments" id="studentcomments" rows="5" readonly><?php echo $studentcomments; ?></textarea>

                            </div>
                            <?php //} else {
                            ?>
                            <!-- <div class="col-md-12 col-sm-4 col-xs-12">
                                    <textarea type="text" class="form-control input-md" name="studentcomments" id="studentcomments" rows="5" readonly><?php echo $studentcomments; ?></textarea>

                                </div> -->
                            <?php //} 
                            ?>
                        </div>
                    </div>
                    <!-- school comments 21042021-->
                    <div class="col-md-12">
                        <div class="form-group m-0">
                            <label class="control-label" for=""><b>School Comment </b></label>
                        </div>
                    </div>
                    <div class="col-md-12 text-left" style="margin-top: 5px;">
                        <div class="form-group">
                            <?php //if ($school_comments != '') { 
                            ?>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <textarea type="text" class="form-control input-md" name="school_comments" id="school_comments" rows="5" readonly><?php echo $school_comments; ?></textarea>

                            </div>
                            <?php //} else {
                            ?>
                            <!-- <div class="col-md-12 col-sm-12 col-xs-12">
                                    <textarea type="text" class="form-control input-md" name="school_comments" id="school_comments" rows="5" readonly><?php echo $school_comments; ?></textarea>

                                </div> -->
                            <?php //} 
                            ?>
                        </div>
                    </div>
                    <!-- school comments 21042021-->
                    <!-- clinician comments 21042021-->
                    <div class="col-md-12">
                        <div class="form-group m-0">
                            <label class="control-label" for=""><b>Clinician Comment </b></label>
                        </div>
                    </div>
                    <div class="col-md-12 text-left">
                        <div class="form-group">
                            <div class="col-md-12">
                                <textarea name="clinician_comments" rows="5" id="clinician_comments" class="form-control"><?php if ($clinician_comments) echo $clinician_comments; ?></textarea>

                            </div>
                        </div>
                    </div>
                    <!-- clinician comments 21042021-->
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group" style="display: flex;margin-left: 0;">
                            <button class="button btn-navigate-form-step button-prev mt-10" type="button" step_number="4">Prev</button>
                            <!-- <label class="col-md-2 control-label"></label> -->
                            <!-- <div class="col-md-12 col-sm-12 col-xs-12 mt-10 flex-end" style="gap: 15px;">
                                <?php if (!$isClinicianSignoff) { ?>
                                    <button id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
                                <?php } ?>
                                <a href="caseStudyList.html" class="btn btn-default">Cancel</a>
                            </div> -->
                            <div class="col-md-12 col-sm-12 col-xs-12 mt-10 flex-end">
                                <?php if ($view != 'V') { ?>
                                    <button id="btnSubmit" name="btnSubmit" class="btn btn-success mr-15">Save</button>
                                <?php } ?>
                                <?php if ($IsMobile) { ?>
                                    <a type="button" href="<?php echo BASE_PATH ?>/webRedirect.html?status=Cancel&type=floorCaseStudy" class="btn btn-default">Cancel</a>
                                <?php } else { ?>
                                    <a href="caseStudyList.html" class="btn btn-default">Cancel</a>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="mt-3 flex-end" style="display: none;">

                    <button class="button btn-navigate-form-step button-prev" type="button" step_number="4">Prev</button>
                    <button class="button next submit-btn" type="submit">Save</button>
                </div>
            </section>
        </form>

    </div>

    <?php include('includes/footer.php'); ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script src="https://cdn.ckeditor.com/ckeditor5/35.0.1/classic/ckeditor.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>


    <script>
        $('.next').on('click', function() {
            event.preventDefault();

            var isValid = $('#step-1').parsley().validate();

            if (!isValid) {
                return false;
            }
        });
        /**
         * Define a function to navigate betweens form steps.
         * It accepts one parameter. That is - step number.
         */
        const navigateToFormStep = (stepNumber) => {
            /**
             * Hide all form steps.
             */
            document.querySelectorAll(".form-step").forEach((formStepElement) => {
                formStepElement.classList.add("d-none");
            });
            /**
             * Mark all form steps as unfinished.
             */
            document.querySelectorAll(".form-stepper-list").forEach((formStepHeader) => {
                formStepHeader.classList.add("form-stepper-unfinished");
                formStepHeader.classList.remove("form-stepper-active", "form-stepper-completed");
            });
            /**
             * Show the current form step (as passed to the function).
             */
            document.querySelector("#step-" + stepNumber).classList.remove("d-none");
            /**
             * Select the form step circle (progress bar).
             */
            const formStepCircle = document.querySelector('li[step="' + stepNumber + '"]');
            /**
             * Mark the current form step as active.
             */
            formStepCircle.classList.remove("form-stepper-unfinished", "form-stepper-completed");
            formStepCircle.classList.add("form-stepper-active");
            /**
             * Loop through each form step circles.
             * This loop will continue up to the current step number.
             * Example: If the current step is 3,
             * then the loop will perform operations for step 1 and 2.
             */
            for (let index = 0; index < stepNumber; index++) {
                /**
                 * Select the form step circle (progress bar).
                 */
                const formStepCircle = document.querySelector('li[step="' + index + '"]');
                /**
                 * Check if the element exist. If yes, then proceed.
                 */
                if (formStepCircle) {
                    /**
                     * Mark the form step as completed.
                     */
                    formStepCircle.classList.remove("form-stepper-unfinished", "form-stepper-active");
                    formStepCircle.classList.add("form-stepper-completed");
                }
            }
        };
        /**
         * Select all form navigation buttons, and loop through them.
         */
        // document.querySelectorAll(".btn-navigate-form-step").forEach((formNavigationBtn) => {
        //     /**
        //      * Add a click event listener to the button.
        //      */
        //     formNavigationBtn.addEventListener("click", () => {
        //         /**
        //          * Get the value of the step.
        //          */
        //         const stepNumber = parseInt(formNavigationBtn.getAttribute("step_number"));
        //         /**
        //          * Call the function to navigate to the target form step.
        //          */
        //         navigateToFormStep(stepNumber);
        //     });
        // });

        document.querySelectorAll(".btn-navigate-form-step").forEach((formNavigationBtn) => {
            formNavigationBtn.addEventListener("click", () => {
                const stepNumber = parseInt(formNavigationBtn.getAttribute("step_number"));

                if (stepNumber === 2) { // Check if navigating to the second step
                    const isValidStep1 = $('#step-1').parsley().validate(); // Trigger validation for step 1

                    if (!isValidStep1) {
                        return false;
                    }
                }

                if (stepNumber === 3) { // Check if navigating to the second step
                    const isValidStep3 = $('#step-3').parsley().validate(); // Trigger validation for step 1

                    if (!isValidStep3) {
                        return false;
                    }
                }

                if (stepNumber === 4) { // Check if navigating to the second step
                    const isValidStep4 = $('#step-4').parsley().validate(); // Trigger validation for step 1

                    if (!isValidStep4) {
                        return false;
                    }
                }

                if (stepNumber === 5) { // Check if navigating to the second step
                    const isValidStep5 = $('#step-5').parsley().validate(); // Trigger validation for step 1

                    if (!isValidStep5) {
                        return false;
                    }
                }

                // Proceed to navigate to the desired step if validation passes or for other steps
                navigateToFormStep(stepNumber);
            });
        });

        $('.form-stepper-list').click(function() {
            stepId = $(this).attr('step');
            stepNextId = $('.form-stepper-active').attr('step');
            // if (stepId < stepNextId)

            const isValidSteps = $('#step-' + stepNextId).parsley().validate(); // Trigger validation for step 1

            if (!isValidSteps) {
                return false;
            }
            // console.log(stepId);
            // console.log(stepNextId);
            navigateToFormStep(stepId);
        });
    </script>

    <!-- collapsible start -->
    <script>
        const collapsibles = document.querySelectorAll('.collapsible');

        collapsibles.forEach(collapsible => {
            collapsible.addEventListener('click', () => {
                const content = collapsible.nextElementSibling;
                const icon = collapsible.querySelector('i.fa-chevron-down');

                // Check if the content is currently visible
                const isActive = content.classList.contains('active');

                // Close all content sections and reset arrows
                document.querySelectorAll('.content').forEach(item => {
                    item.classList.remove('active');
                });

                document.querySelectorAll('i.fa-chevron-down').forEach(item => {
                    item.classList.remove('rotate');
                });

                // Toggle the content's active state and arrow rotation
                content.classList.toggle('active', !isActive);
                icon.classList.toggle('rotate', !isActive);
            });
        });
    </script>
    <!-- collapsible end -->

    <script type="text/javascript">
        $(".select2_single").select2();
        $('#select2-cborotation-container').addClass('required-select2');

        $(window).load(function() {

            $('#formCaseStudy').parsley().on('field:validated', function() {
                    var ok = $('.parsley-error').length === 0;
                })
                .on('form:submit', function() {
                    ShowProgressAnimation();
                    return true; // Don't submit form for this demo
                });

            $('#caseStudydate').datetimepicker({
                format: 'MM/DD/YYYY'
            });
            $('#ClinicianDate').datetimepicker({
                format: 'MM/DD/YYYY'
            });
            $('#schoolDate').datetimepicker({
                format: 'MM/DD/YYYY'
            });
            $('#floorAdmissionDate').datetimepicker({
                format: 'MM/DD/YYYY'
            });





        });

        $('.showMore_6_10').click(function() {

            $('.hidecol_6').removeClass('hide');
            $(this).addClass('hide');
            $('.showLess_1').removeClass('hide');
        });
        $('.showLess_10_6').click(function() {
            $('.hidecol_6').addClass('hide');
            $('.showMore_6_10').removeClass('hide');
            $('.showLess_1').addClass('hide');
        });

        $('.showMore_10_15').click(function() {
            $('.hidecol_6, .hidecol_10').removeClass('hide');
            $('.showLess_1').addClass('hide');
            $('.showLess_2').removeClass('hide');
            $('.showLess_15_10').removeClass('hide');
        });
        $('.showLess_15_10').click(function() {

            $('.hidecol_10').addClass('hide');
            $('.showLess_1').removeClass('hide');
            $('.showLess_10').removeClass('hide');
            $(this).addClass('hide');
        });
    </script>
</body>

</html>