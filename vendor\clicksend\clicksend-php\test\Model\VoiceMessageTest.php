<?php
/**
 * VoiceMessageTest
 *
 * PHP version 5
 *
 * @category Class
 * @package  ClickSend
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * ClickSend v3 API
 *
 * This is an official SDK for [ClickSend](https://clicksend.com)  Below you will find a current list of the available methods for clicksend.  *NOTE: You will need to create a free account to use the API. You can register [here](https://dashboard.clicksend.com/#/signup/step1/)..*
 *
 * OpenAPI spec version: 3.1
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * ClickSend Codegen version: 3.0.51
 */
/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Please update the test case below to test the model.
 */

namespace ClickSend;

use PHPUnit\Framework\TestCase;

/**
 * VoiceMessageTest Class Doc Comment
 *
 * @category    Class
 * @description VoiceMessage fields: source, to, list_id, body, lang, voice, schedule, custom_string, country
 * @package     ClickSend
 * <AUTHOR> Codegen team
 * @link        https://github.com/swagger-api/swagger-codegen
 */
class VoiceMessageTest extends TestCase
{

    /**
     * Setup before running any test case
     */
    public static function setUpBeforeClass(): void
    {
    }

    /**
     * Setup before running each test case
     */
    public function setUp(): void
    {
    }

    /**
     * Clean up after running each test case
     */
    public function tearDown(): void
    {
    }

    /**
     * Clean up after running all test cases
     */
    public static function tearDownAfterClass(): void
    {
    }

    /**
     * Test "VoiceMessage"
     */
    public function testVoiceMessage()
    {
    }

    /**
     * Test attribute "to"
     */
    public function testPropertyTo()
    {
    }

    /**
     * Test attribute "body"
     */
    public function testPropertyBody()
    {
    }

    /**
     * Test attribute "voice"
     */
    public function testPropertyVoice()
    {
    }

    /**
     * Test attribute "custom_string"
     */
    public function testPropertyCustomString()
    {
    }

    /**
     * Test attribute "country"
     */
    public function testPropertyCountry()
    {
    }

    /**
     * Test attribute "source"
     */
    public function testPropertySource()
    {
    }

    /**
     * Test attribute "list_id"
     */
    public function testPropertyListId()
    {
    }

    /**
     * Test attribute "lang"
     */
    public function testPropertyLang()
    {
    }

    /**
     * Test attribute "schedule"
     */
    public function testPropertySchedule()
    {
    }

    /**
     * Test attribute "require_input"
     */
    public function testPropertyRequireInput()
    {
    }

    /**
     * Test attribute "machine_detection"
     */
    public function testPropertyMachineDetection()
    {
    }
}
