<?php
class clsSiteevaluation
{
	var $csEvaluationMasterId = '';
	var $studentId = '';
	var $schoolId = '';
	var $rotationId = '';
	var $schoolClinicalSiteUnitId = '';
	var $createdBy = '';
	var $createdDate = '';
	var $updatedBy = '';
	var $updatedDate = '';
	var $schoolCSEvaluationQuestionId = '';
	var $formativeQuestionType = '';
	var $optionText = '';
	var $hospitalSiteId = '';
	var $sectionMasterId = '';
	var $schoolFormativeQuestionType = '';
	var $schoolCSEvaluationOptionAnswerText  = '';

	function SaveCSevaluation($csEvaluationMasterId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($csEvaluationMasterId > 0) {

			$sql = "UPDATE siteevaluationmaster SET	  
						  
						 rotationId = '" . addslashes($this->rotationId) . "',
						 schoolClinicalSiteUnitId = '" . addslashes($this->schoolClinicalSiteUnitId) . "',
						 hospitalSiteId='" . addslashes($this->hospitalSiteId) . "',						
						 evaluationDate='" . addslashes($this->evaluationDate) . "',
						 patientCareAdultAreaId = '" . addslashes($this->patientCareAdultAreaId) . "',
						 patientCarePediatricAreaId = '" . addslashes($this->patientCarePediatricAreaId) . "',
						 patientCareNeonatalaAreaId = '" . addslashes($this->patientCareNeonatalaAreaId) . "',
						 updatedBy = '" . addslashes($this->createdBy) . "',
						 updatedDate = '" . (date("Y-m-d h:i:s")) . "' 
						 Where csEvaluationMasterId= " . $csEvaluationMasterId;
			//echo 'update->'.$sql;exit;
			$objDB->ExecuteQuery($sql);
		} else {
			$sql = "INSERT INTO siteevaluationmaster (schoolId,studentId, rotationId,schoolClinicalSiteUnitId,hospitalSiteId,
					evaluationDate,patientCareAdultAreaId,patientCarePediatricAreaId,patientCareNeonatalaAreaId,
								createdBy, createdDate) 
				VALUES ('" . addslashes($this->schoolId) . "',
						'" . addslashes($this->studentId) . "',
						'" . addslashes($this->rotationId) . "',
						'" . ($this->schoolClinicalSiteUnitId) . "',
						'" . addslashes($this->hospitalSiteId) . "',						
						'" . addslashes($this->evaluationDate) . "',
						'" . addslashes($this->patientCareAdultAreaId) . "',
						'" . addslashes($this->patientCarePediatricAreaId) . "',
						'" . addslashes($this->patientCareNeonatalaAreaId) . "',
						'" . addslashes($this->createdBy) . "',
						'" . (date("Y-m-d h:i:s")) . "'						
						)";
			//	echo 'Insert->'.$sql;exit;
			$csEvaluationMasterId = $objDB->ExecuteInsertQuery($sql);
		}

		unset($objDB);
		return $csEvaluationMasterId;
	}

	function SaveAdminCSevaluation($csEvaluationMasterId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($csEvaluationMasterId > 0) {

			$sql = "UPDATE siteevaluationmaster SET	  
						  
						 rotationId = '" . addslashes($this->rotationId) . "',
						 schoolClinicalSiteUnitId = '" . addslashes($this->schoolClinicalSiteUnitId) . "',						
						 hospitalSiteId = '" . addslashes($this->hospitalSiteId) . "',						
						 evaluationDate='" . addslashes($this->evaluationDate) . "',
						 patientCareAdultAreaId = '" . addslashes($this->patientCareAdultAreaId) . "',
						 patientCarePediatricAreaId = '" . addslashes($this->patientCarePediatricAreaId) . "',
						 patientCareNeonatalaAreaId = '" . addslashes($this->patientCareNeonatalaAreaId) . "',
						 updatedBy = '" . addslashes($this->createdBy) . "',
						 updatedDate = '" . (date("Y-m-d h:i:s")) . "' 
						 Where csEvaluationMasterId= " . $csEvaluationMasterId;
			//echo 'update->'.$sql;exit;
			$objDB->ExecuteQuery($sql);
		} else {
			$sql = "INSERT INTO siteevaluationmaster (schoolId,studentId, rotationId,schoolClinicalSiteUnitId,
					hospitalSiteId,evaluationDate,patientCareAdultAreaId,patientCarePediatricAreaId,patientCareNeonatalaAreaId,
								createdBy, createdDate) 
				VALUES ('" . addslashes($this->schoolId) . "',
						'" . addslashes($this->studentId) . "',
						'" . addslashes($this->rotationId) . "',
						" . ($this->schoolClinicalSiteUnitId) . ",						
						" . ($this->hospitalSiteId) . ",						
						'" . addslashes($this->evaluationDate) . "',
						'" . addslashes($this->patientCareAdultAreaId) . "',
						'" . addslashes($this->patientCarePediatricAreaId) . "',
						'" . addslashes($this->patientCareNeonatalaAreaId) . "',
						'" . addslashes($this->createdBy) . "',
						'" . (date("Y-m-d h:i:s")) . "'						
						)";
			//echo 'Insert->'.$sql;exit;
			$csEvaluationMasterId = $objDB->ExecuteInsertQuery($sql);
		}

		unset($objDB);
		return $csEvaluationMasterId;
	}


	function SaveSiteevaluationQuestion($questionId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($questionId > 0) {

			$sql = "UPDATE schoolsiteevaluationquestionmaster SET 						
						 questionText = '" . addslashes($this->questionText) . "',
						 schoolCSEvaluationQuestionType = '" . addslashes($this->schoolCSEvaluationQuestionType) . "',
						 isPosition = '" . addslashes($this->isPosition) . "',
						 sortOrder = '" . addslashes($this->sortOrder) . "'
						 Where schoolCSEvaluationQuestionId= " . $questionId;
			//echo 'Insert->'.$sql;exit;
			$objDB->ExecuteQuery($sql);
		} else {
			$sql = "INSERT INTO schoolsiteevaluationquestionmaster (questionText,schoolId,schoolCSEvaluationQuestionType,sectionMasterId,isPosition,sortOrder) 
				VALUES ('" . addslashes($this->questionText) . "',
						'" . addslashes($this->schoolId) . "',					
						'" . addslashes($this->schoolCSEvaluationQuestionType) . "',					
						'" . addslashes($this->sectionMasterId) . "'	,			
						'" . addslashes($this->isPosition) . "',
						'" . addslashes($this->sortOrder) . "'
						)";
			// echo 'Insert->'.$sql;exit;
			$questionId = $objDB->ExecuteInsertQuery($sql);
		}

		unset($objDB);
		return $questionId;
	}
	function SaveDefaultSiteevaluationQuestion($questionId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($questionId > 0) {

			$sql = "UPDATE defaultsiteevaluationquestionmaster SET 						
						questionText = '" . addslashes($this->questionText) . "',
						defaultCSEvaluationQuestionType = '" . addslashes($this->schoolCSEvaluationQuestionType) . "',
						isPosition = '" . addslashes($this->isPosition) . "'

						Where defaultCSEvaluationQuestionId= " . $questionId;

			$objDB->ExecuteQuery($sql);
		} else {
			$sql = "INSERT INTO defaultsiteevaluationquestionmaster (questionText,defaultCSEvaluationQuestionType,sectionMasterId,isPosition) 
				VALUES ('" . addslashes($this->questionText) . "',				
						'" . addslashes($this->schoolCSEvaluationQuestionType) . "',					
						'" . addslashes($this->sectionMasterId) . "'	,			
						'" . addslashes($this->isPosition) . "'			
						)";

			$questionId = $objDB->ExecuteInsertQuery($sql);
		}

		unset($objDB);
		return $questionId;
	}



	function DeleteCSevaluationDetails($csEvaluationMasterId)
	{
		$objDB = new clsDB();

		$sql = "DELETE  FROM siteevaluationdetail WHERE csEvaluationMasterId=" . $csEvaluationMasterId;
		//ECHO 'DELETE->'.$sql;EXIT;
		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}
	function SaveCSevaluationDetails()
	{
		$objDB = new clsDB();

		$sql = "INSERT INTO siteevaluationdetail (csEvaluationMasterId,schoolCSEvaluationQuestionId,
								schoolCSEvaluationOptionValue,schoolCSEvaluationOptionAnswerText) 
					 VALUES ('" . ($this->csEvaluationMasterId) . "',
							 '" . ($this->schoolCSEvaluationQuestionId) . "',
							 '" . ($this->schoolCSEvaluationOptionValue) . "',
							 '" . addslashes($this->schoolCSEvaluationOptionAnswerText) . "'
							 
							)";
		//echo 'INSERT->'.$sql;exit;
		$csEvaluationDetaild = $objDB->ExecuteInsertQuery($sql);
		unset($objDB);
		return $csEvaluationDetaild;
	}

	function CopyAllCIevaluationDetailsQuestionMaster($currentSchoolId)
	{
		$this->schoolId = $currentSchoolId;
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM  formativequestionmaster";
		//echo $sql.'<hr>';
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		if ($rows != "") {
			while ($row = mysqli_fetch_array($rows)) {
				$this->optionText = $row['optionText'];
				$this->formativeQuestionType = $row['formativeQuestionType'];
				$this->sectionMasterId = $row['sectionMasterId'];
				$this->SaveSchoolformativequestionmaster();
			}
		}
	}

	function SaveSchoolFormativeQuestionMaster()
	{
		$objDB = new clsDB();
		$sql = "INSERT INTO schoolformativequestionmaster 
						(optionText,schoolFormativeQuestionType,schoolId,sectionMasterId) 
						 VALUES ('" . addslashes($this->optionText) . "',
								 '" . addslashes($this->formativeQuestionType) . "',
								 '" . addslashes($this->schoolId) . "',
								 '" . addslashes($this->sectionMasterId) . "'
								 
								)";
		//echo $sql;exit;
		$schoolCSEvaluationQuestionId = $objDB->ExecuteInsertQuery($sql);
	}

	function GetAllCSEvaluationQuestionMaster($currentSchoolId, $sectionMasterId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM  schoolsiteevaluationquestionmaster
						WHERE schoolId=" . $currentSchoolId . " AND sectionMasterId=" . $sectionMasterId;

		// if($currentSchoolId == '118')	
		$sql .= " ORDER BY sortOrder ASC ";
		//echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}


	function GetSections($currentSchoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT sitesectionmaster.* ,schools.schoolId
						FROM sitesectionmaster 
						LEFT JOIN schools ON sitesectionmaster.schoolId=schools.schoolId
						WHERE sitesectionmaster.schoolId=" . $currentSchoolId . " and sitesectionmaster.isActive=1";
		//echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetAllSections($currentSchoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT sitesectionmaster.* ,schools.schoolId
					FROM sitesectionmaster 
					LEFT JOIN schools ON sitesectionmaster.schoolId=schools.schoolId
					WHERE sitesectionmaster.schoolId=" . $currentSchoolId;
		//echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetAllDefaultSections()
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM defaultsitesectionmaster ";
		//echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetAllCSEvaluation($rotationId, $studentId, $canvasStatus = '', $schoolId = 0)
	{
		$objDB = new clsDB();
		$sql = "SELECT siteevaluationmaster.*,
						siteevaluationmaster.csEvaluationMasterId AS StudentCsEvaluationMasterId,
						siteevaluationdetail.*,student.studentId,
						rotation.rotationId,rotation.title,student.firstName,student.lastName,
						rotation.parentRotationId,rotation.locationId as rotationLocationId,courses.locationId
						FROM siteevaluationmaster
						LEFT JOIN siteevaluationdetail ON siteevaluationmaster.csEvaluationMasterId=
															siteevaluationdetail.csEvaluationMasterId
						LEFT JOIN rotation ON siteevaluationmaster.rotationId=rotation.rotationId
						LEFT JOIN courses ON rotation.courseId=courses.courseId
						LEFT JOIN student ON siteevaluationmaster.studentId=student.studentId";
		$sql .= " WHERE siteevaluationmaster.csEvaluationMasterId !=0 ";
		if ($rotationId > 0) {
			$sql .= " AND siteevaluationmaster.rotationId=" . $rotationId;
		}
		if ($studentId > 0) {
			$sql .= "  AND siteevaluationmaster.studentId=" . $studentId;
		}
		if ($canvasStatus != '')
			$sql .= "  AND siteevaluationmaster.isSendToCanvas=" . $canvasStatus . " AND siteevaluationmaster.schoolId=" . $schoolId;

		$sql .= " GROUP BY siteevaluationmaster.csEvaluationMasterId";
		$sql .= " order BY siteevaluationmaster.evaluationDate";

		// ECHO $sql;
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}
	function DeleteCSEvaluation($csEvaluationMasterId)
	{
		$objDB = new clsDB();
		$result = "";
		if ($csEvaluationMasterId > 0) {
			$objDB = new clsDB();
			$sql = "DELETE siteevaluationmaster,siteevaluationdetail FROM siteevaluationmaster
									INNER JOIN siteevaluationdetail ON siteevaluationmaster.csEvaluationMasterId=
																			siteevaluationdetail.csEvaluationMasterId
									WHERE siteevaluationmaster.csEvaluationMasterId = " . $csEvaluationMasterId;
			//ECHO $sql;EXIT;
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
		return $result;
	}

	function GetCSEvaluationDetails($csEvaluationMasterId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT siteevaluationmaster.*,siteevaluationdetail.*,rotation.parentRotationId,rotation.locationId as rotationLocationId,courses.locationId
						FROM  siteevaluationmaster 
						LEFT JOIN siteevaluationdetail ON siteevaluationdetail.csEvaluationMasterId= 
						siteevaluationmaster.`csEvaluationMasterId`
						LEFT JOIN hospitalsites ON siteevaluationmaster.hospitalSiteId=hospitalsites.hospitalSiteId
						LEFT JOIN rotation ON siteevaluationmaster.rotationId=rotation.rotationId
						LEFT JOIN courses ON rotation.courseId=courses.courseId
							
						WHERE siteevaluationmaster.csEvaluationMasterId=" . $csEvaluationMasterId;
		//    echo $sql;	exit;	
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}
	function GetStudentSiteEvaluationDetailsForreport(
		$schoolId,
		$rotationId = 0,
		$studentId,
		$rankId = 0,
		$evaluator = 0,
		$locationId = 0,
		$hospitalSiteId = 0,
		$startdate = '',
		$endtdate = '',
		$ascdesc,
		$sordorder,
		$cbosemester,
		$subcborotation,
		$selTopicId
	) {

		if ($evaluator)
			$evaluator = str_replace(" ", ",", $evaluator);

		$hospitalSiteId = $hospitalSiteId ? str_replace(" ", ",", $hospitalSiteId) : '';

		$selTopicId = $selTopicId ? str_replace(" ", ",", $selTopicId) : '';

		$cbosemester = str_replace(" ", ",", $cbosemester);
		$subcborotation = str_replace(" ", ",", $subcborotation);

		$studentIds = is_array($studentId) ? implode(",", $studentId) : 0;
		// $studentIds =  $studentId ? implode(',',$studentId) : '';

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT siteevaluationmaster.*,schools.schoolId, rotation.rotationId,
							rotation.title AS Rotationname,rankmaster.rankId,rankmaster.title AS Rankname,
							student.studentId,student.rankId,student.firstName,student.lastName
							
							FROM siteevaluationmaster
							INNER JOIN schools ON siteevaluationmaster.schoolId=schools.schoolId
							LEFT JOIN rotation ON siteevaluationmaster.rotationId=rotation.rotationId
							LEFT JOIN courses ON courses.`courseId` = rotation.`courseId`
							LEFT JOIN semestermaster ON courses.`semesterId` = semestermaster.`semesterId`
							INNER JOIN student ON siteevaluationmaster.studentId=student.studentId
							INNER JOIN rankmaster ON student.rankId=rankmaster.rankId";

		if ($evaluator)
			$sql .= " INNER JOIN clinicianhospitalsite ON siteevaluationmaster.hospitalSiteId=clinicianhospitalsite.hospitalSiteId
		                     INNER JOIN clinician ON clinicianhospitalsite.clinicianId=clinician.clinicianId";

		$sql .= " WHERE schools.schoolId= " . $schoolId;

		if ($cbosemester > 0)
			$sql .= " AND semestermaster.semesterId IN ($cbosemester)";
		if ($rotationId > 0)
			$sql .= " AND rotation.rotationId=" . $rotationId . " OR  rotation.parentRotationId = " . $rotationId;
		if ($subcborotation > 0)
			$sql .= " AND rotation.rotationId IN ($subcborotation)";
		if ($studentIds > 0)
			$sql .= " AND student.studentId IN ($studentIds)";
		if ($rankId > 0)
			$sql .= " AND rankmaster.rankId=" . $rankId;
		if ($hospitalSiteId > 0)
			$sql .= " AND siteevaluationmaster.hospitalSiteId IN ($hospitalSiteId)";
		if ($startdate != '' || $endtdate != '')
			$sql .= " AND date(siteevaluationmaster.evaluationDate) >= '" . $startdate . "'
									AND date(siteevaluationmaster.evaluationDate) <= '" . $endtdate . "' ";
		if ($evaluator)
			$sql .= " AND clinician.clinicianId IN ($evaluator) ";
		if ($selTopicId)
			$sql .= " AND siteevaluationmaster.schoolClinicalSiteUnitId IN ($selTopicId)";

		if ($ascdesc && $sordorder == 10)
			$sql .= "  ORDER BY rotation.`title` " . $ascdesc;

		else if ($ascdesc && $sordorder == 14)
			$sql .= "  ORDER BY student.firstName " . $ascdesc;

		else if ($ascdesc && $sordorder == 15)
			$sql .= "  ORDER BY student.lastName " . $ascdesc;
		//echo $sql;			
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	function CopyAllSiteEvalQuestionMaster($currentSchoolId, $isNewSection = 0)
	{
		$currentschoolSectionId = '';
		$this->schoolId = $currentSchoolId;
		$objDB = new clsDB();
		$savedQuestionIds = array();
		$savedSectionIds = array();
		$sql = "select defaultsitesectionmaster.* from 
                                        defaultsiteevaluationquestionmaster 
                                        RIGHT JOIN defaultsitesectionmaster ON  defaultsitesectionmaster.sectionMasterId = defaultsiteevaluationquestionmaster.sectionMasterId";

		if ($isNewSection == 1)
			$sql .= " where defaultsitesectionmaster.isSectionAdded=1";

		$sql .= " GROUP BY defaultsitesectionmaster.sectionMasterId";
		$rowsSectionMaster = $objDB->GetResultset($sql);
		// echo $count=mysqli_num_rows($rowsSectionMaster);exit;
		if ($rowsSectionMaster != "") {
			while ($sectionMaster = mysqli_fetch_array($rowsSectionMaster)) {
				$currentschoolQuestionId = array();
				//default assignment

				$currentschoolQuestionId[] = 0;
				$sectionId = $sectionMaster['sectionMasterId'];

				//Skipp
				if (array_key_exists($sectionId, $savedSectionIds)) {
					$currentschoolSectionId = $savedSectionIds[$sectionId];
				} else {
					$this->title = $sectionMaster['title'];
					$this->sortOrder = $sectionMaster['sortOrder'];
					$this->schoolId = $currentSchoolId;
					$currentschoolSectionId = $this->SaveSchoolSectionMaster($sectionId);
					$savedSectionIds[$sectionId] = $currentschoolSectionId;
				}
				$sql = "select defaultsiteevaluationquestionmaster.* from 
															defaultsiteevaluationquestiondetail 
															RIGHT JOIN defaultsiteevaluationquestionmaster ON  defaultsiteevaluationquestionmaster.defaultCSEvaluationQuestionId
																								=defaultsiteevaluationquestiondetail .defaultCSEvaluationQuestionId
																								WHERE defaultsiteevaluationquestionmaster.sectionMasterId=" . $sectionId;
				$rowsQuestionMaster = $objDB->GetResultset($sql);
				if ($rowsQuestionMaster != "") {
					while ($row = mysqli_fetch_array($rowsQuestionMaster)) {
						$masterQuestionId = $row['defaultCSEvaluationQuestionId'];
						// If already used then skipp
						if (array_key_exists($masterQuestionId, $savedQuestionIds)) {
							$currentschoolQuestionId[] = $savedQuestionIds[$masterQuestionId];
							continue;
						} else {
							$this->questionText = $row['questionText'];
							$this->schoolCSEvaluationQuestionType = $row['defaultCSEvaluationQuestionType'];
							$this->schoolCSEvaluationQuestionId = $masterQuestionId;
							$this->sectionMasterId = $currentschoolSectionId;
							$schoolQuestionId = $this->CopyMasterQuestionToSchool($currentSchoolId, $masterQuestionId, $currentschoolSectionId);
							//Bind in array
							$savedQuestionIds[$masterQuestionId] = $schoolQuestionId;
							$currentschoolQuestionId[] = $schoolQuestionId;
							//-----------------------------------------------------
							//Copy Question Choices
							//-----------------------------------------------------
							$this->CopyMasterQuestionChoicesToSchool($masterQuestionId, $schoolQuestionId);
							//-----------------------------------------------------
						}
					} //while end


				} //if end
			} //1st while end
		} //1st if end


	}
	function SaveSchoolSectionMaster($schoolSectionId)
	{
		$objDB = new clsDB();
		$sql = "INSERT INTO sitesectionmaster (schoolId,title,sortOrder) 
								 VALUES (
											" . addslashes($this->schoolId) . ",
											'" . addslashes($this->title) . "',
											" . addslashes($this->sortOrder) . "							 
										)";
		//echo 'section->'. $sql;exit;
		$schoolSectionId = $objDB->ExecuteInsertQuery($sql);
		$this->schoolSectionId = $schoolSectionId;
		unset($objDB);
		return $schoolSectionId;
	}
	function CopyMasterQuestionToSchool($schoolId, $questionId, $currentschoolSectionId)
	{
		$objDB = new clsDB();
		$sql = "INSERT INTO schoolsiteevaluationquestionmaster (questionText,schoolCSEvaluationQuestionType,schoolId,sectionMasterId) 
							
									SELECT questionText,defaultCSEvaluationQuestionType," . $schoolId . ",
									" . $currentschoolSectionId . "									
									FROM defaultsiteevaluationquestionmaster
									WHERE defaultCSEvaluationQuestionId=" . $questionId;
		//echo $sql;exit;
		$schoolQuestionId = $objDB->ExecuteInsertQuery($sql);
		unset($objDB);
		return  $schoolQuestionId;;
	}
	function CopyMasterQuestionChoicesToSchool($questionMasterId, $schoolQuestionId)
	{
		$sql = "INSERT INTO schoolsiteevaluationquestiondetail (schoolCSEvaluationQuestionId,optionText,schoolOptionValue) 
								SELECT " . $schoolQuestionId . ",optionText,defaultOptionValue
								FROM defaultsiteevaluationquestiondetail  WHERE defaultCSEvaluationQuestionId=" . $questionMasterId;

		$objDB = new clsDB();
		$schoolQuestionDId = $objDB->ExecuteInsertQuery($sql);
		unset($objDB);
	}
	function DeleteSchoolSiteEvaluation($schoolId)
	{
		$result = "";
		$objDB = new clsDB();
		$sql = "DELETE schoolsiteevaluationquestionmaster.*  ,schoolsiteevaluationquestiondetail.*
								FROM schoolsiteevaluationquestionmaster
								LEFT JOIN schoolsiteevaluationquestiondetail ON schoolsiteevaluationquestionmaster.schoolCSEvaluationQuestionId=
																		 schoolsiteevaluationquestiondetail.schoolCSEvaluationQuestionId
								WHERE schoolsiteevaluationquestionmaster.schoolId = " . $schoolId;
		//echo $sql;exit;
		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}
	function GetSiteEvaluationScore($csEvaluationMasterId)
	{
		$objDB = new clsDB();
		$SiteEvaluation = "";
		$sql = "SELECT
								AVG(schoolsiteevaluationquestiondetail.schoolOptionValue) AS EvaluationScore, 
								siteevaluationdetail.csEvaluationDetaild,
								schoolsiteevaluationquestiondetail.schoolCSEvaluationQuestionId 						
								FROM siteevaluationdetail
								INNER JOIN siteevaluationmaster ON siteevaluationdetail.csEvaluationMasterId=siteevaluationmaster.csEvaluationMasterId 	
								INNER JOIN schoolsiteevaluationquestiondetail  ON 
								siteevaluationdetail.schoolCSEvaluationOptionValue=schoolsiteevaluationquestiondetail.schoolCSEvaluationQuestionDetailId
								AND schoolsiteevaluationquestiondetail.schoolCSEvaluationQuestionId=siteevaluationdetail.schoolCSEvaluationQuestionId";
		$sql .= " WHERE  siteevaluationmaster.csEvaluationMasterId=" . $csEvaluationMasterId;
		//echo $sql;
		$SiteEvaluation = $objDB->GetDataRow($sql);
		return $SiteEvaluation;
		unset($objDB);
	}

	function GetRecommendClinicalAffilate($csEvaluationMasterId)
	{
		$objDB = new clsDB();
		$siteEvaluation = "";

		$sql = "SELECT
						siteevaluationdetail.schoolCSEvaluationOptionAnswerText 				
						FROM siteevaluationdetail
						INNER JOIN siteevaluationmaster ON siteevaluationdetail.csEvaluationMasterId=
						siteevaluationmaster.csEvaluationMasterId
						INNER JOIN schoolsiteevaluationquestionmaster ON schoolsiteevaluationquestionmaster.schoolCSEvaluationQuestionId=
						siteevaluationdetail.schoolCSEvaluationQuestionId
						WHERE  siteevaluationmaster.csEvaluationMasterId = " . $csEvaluationMasterId . " AND schoolsiteevaluationquestionmaster.questionText  like 'I would recommend this clinical affiliate for future rotations' AND siteevaluationdetail.schoolCSEvaluationOptionAnswerText !='' ";

		$siteEvaluation = $objDB->GetSingleFieldValue($sql);
		return $siteEvaluation;
		unset($objDB);
	}

	function GetClinicalLocationStrengths($csEvaluationMasterId)
	{
		$objDB = new clsDB();
		$siteEvaluation = "";

		$sql = "SELECT
						siteevaluationdetail.schoolCSEvaluationOptionAnswerText 				
						FROM siteevaluationdetail
						INNER JOIN siteevaluationmaster ON siteevaluationdetail.csEvaluationMasterId=
						siteevaluationmaster.csEvaluationMasterId
						INNER JOIN schoolsiteevaluationquestionmaster ON schoolsiteevaluationquestionmaster.schoolCSEvaluationQuestionId=
						siteevaluationdetail.schoolCSEvaluationQuestionId
						WHERE  siteevaluationmaster.csEvaluationMasterId = " . $csEvaluationMasterId . " AND schoolsiteevaluationquestionmaster.questionText  like 'What are this clinical location''s strengths?' AND siteevaluationdetail.schoolCSEvaluationOptionAnswerText !='' ";

		$siteEvaluation = $objDB->GetSingleFieldValue($sql);
		return $siteEvaluation;
		unset($objDB);
	}

	function GetClinicalLocationNeedToImprove($csEvaluationMasterId)
	{
		$objDB = new clsDB();
		$siteEvaluation = "";

		$sql = "SELECT
						siteevaluationdetail.schoolCSEvaluationOptionAnswerText 				
						FROM siteevaluationdetail
						INNER JOIN siteevaluationmaster ON siteevaluationdetail.csEvaluationMasterId=
						siteevaluationmaster.csEvaluationMasterId
						INNER JOIN schoolsiteevaluationquestionmaster ON schoolsiteevaluationquestionmaster.schoolCSEvaluationQuestionId=
						siteevaluationdetail.schoolCSEvaluationQuestionId
						WHERE  siteevaluationmaster.csEvaluationMasterId = " . $csEvaluationMasterId . " AND schoolsiteevaluationquestionmaster.questionText  like 'In what areas does this clinical location need to improve?' AND siteevaluationdetail.schoolCSEvaluationOptionAnswerText !='' ";

		$siteEvaluation = $objDB->GetSingleFieldValue($sql);
		return $siteEvaluation;
		unset($objDB);
	}

	function GetSiteQuestionCountbySections($currentSchoolId, $sectionMasterId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT count(sectionMasterId) as count
                                FROM schoolsiteevaluationquestionmaster 
                                WHERE schoolId=" . $currentSchoolId . " AND sectionMasterId=" . $sectionMasterId;
		//echo $sql;		
		$rows = $objDB->GetSingleFieldValue($sql);
		return $rows;
		unset($objDB);
	}

	function GetDefaultSiteQuestionCountbySections($sectionMasterId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT count(sectionMasterId) as count
                                FROM defaultsiteevaluationquestionmaster 
                                WHERE  sectionMasterId=" . $sectionMasterId;
		//echo $sql;		
		$rows = $objDB->GetSingleFieldValue($sql);
		return $rows;
		unset($objDB);
	}

	function DeleteSiteEvaluationSection($sectionMasterId)
	{
		$objDB = new clsDB();

		$sql = "DELETE  FROM sitesectionmaster WHERE sectionMasterId=" . $sectionMasterId;
		//ECHO 'DELETE->'.$sql;EXIT;
		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}

	function DeleteDefaultSiteEvaluationSection($sectionMasterId)
	{
		$objDB = new clsDB();

		$sql = "DELETE  FROM defaultsitesectionmaster WHERE sectionMasterId=" . $sectionMasterId;
		//ECHO 'DELETE->'.$sql;EXIT;
		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}

	function GetSiteEvaluationSectionDetail($sectionMasterId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM  sitesectionmaster
                                WHERE sectionMasterId=" . $sectionMasterId;
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}
	function GetDefaultSiteEvaluationSectionDetail($sectionMasterId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM  defaultsitesectionmaster WHERE sectionMasterId=" . $sectionMasterId;
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}

	function SaveSiteevaluationSection($sectionId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($sectionId > 0) {

			$sql = "UPDATE sitesectionmaster SET 						
                                         title = '" . addslashes($this->title) . "',						
                                         sortOrder = '" . addslashes($this->sortOrder) . "'
                                         Where sectionMasterId= " . $sectionId;
			//echo 'update->'.$sql;exit;
			$objDB->ExecuteQuery($sql);
		} else {
			$sql = "INSERT INTO sitesectionmaster (schoolId,title,sortOrder) 
                                VALUES ('" . addslashes($this->schoolId) . "',
                                        '" . addslashes($this->title) . "',
                                        '" . addslashes($this->sortOrder) . "'						
                                        )";
			//echo 'Insert->'.$sql;exit;
			$sectionId = $objDB->ExecuteInsertQuery($sql);
		}

		unset($objDB);
		return $sectionId;
	}

	function SaveDefaultSiteevaluationSection($sectionId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($sectionId > 0) {

			$sql = "UPDATE defaultsitesectionmaster SET 						
										title = '" . addslashes($this->title) . "',						
										sortOrder = '" . addslashes($this->sortOrder) . "'
										Where sectionMasterId= " . $sectionId;
			//echo 'update->'.$sql;exit;
			$objDB->ExecuteQuery($sql);
		} else {
			$sql = "INSERT INTO defaultsitesectionmaster (title,sortOrder) 
                                VALUES ('" . addslashes($this->title) . "',
                                        '" . addslashes($this->sortOrder) . "'						
                                        )";
			//echo 'Insert->'.$sql;exit;
			$sectionId = $objDB->ExecuteInsertQuery($sql);
		}

		unset($objDB);
		return $sectionId;
	}

	function GetAllSiteEvaluationQuestionToSetting($currentSchoolId, $sectionMasterId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT schoolsiteevaluationquestionmaster.*,questiontypemaster.title as questionType FROM  schoolsiteevaluationquestionmaster
                                LEFT JOIN questiontypemaster ON schoolsiteevaluationquestionmaster.schoolCSEvaluationQuestionType=questiontypemaster.questionType
                                WHERE schoolId=" . $currentSchoolId . " AND schoolsiteevaluationquestionmaster.sectionMasterId=" . $sectionMasterId;
		// if($currentSchoolId == '118')
		$sql .= " ORDER BY sortOrder ASC";
		//echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetAllDefaultSiteEvaluationQuestionToSetting($sectionMasterId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT defaultsiteevaluationquestionmaster.*,questiontypemaster.title as questionType FROM  defaultsiteevaluationquestionmaster
                                LEFT JOIN questiontypemaster ON defaultsiteevaluationquestionmaster.defaultCSEvaluationQuestionType=questiontypemaster.questionType
                                WHERE defaultsiteevaluationquestionmaster.sectionMasterId=" . $sectionMasterId;
		//echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetAllSiteEvaluationAssignQuestionToSection($currentSchoolId, $sectionMasterId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT schoolCSEvaluationQuestionId FROM  schoolsiteevaluationquestionmaster
                                WHERE schoolId=" . $currentSchoolId . " AND sectionMasterId=" . $sectionMasterId;
		//echo $sql;		
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}

	function GetSiteEvaluationQuestionDetail($currentSchoolId, $questionId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM  schoolsiteevaluationquestionmaster
                                WHERE schoolId=" . $currentSchoolId . " AND schoolCSEvaluationQuestionId=" . $questionId;
		//echo $sql;		
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}

	function GetDefaultSiteEvaluationQuestionDetail($questionId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM  defaultsiteevaluationquestionmaster
                                WHERE defaultCSEvaluationQuestionId=" . $questionId;
		//echo $sql;		
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}

	function DeleteSiteEvaluationQuestion($questionId)
	{
		$objDB = new clsDB();

		$sql = "DELETE  FROM schoolsiteevaluationquestionmaster WHERE schoolCSEvaluationQuestionId=" . $questionId;
		//ECHO 'DELETE->'.$sql;EXIT;
		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}
	function DeleteDefaultSiteEvaluationQuestion($questionId)
	{
		$objDB = new clsDB();

		$sql = "DELETE  FROM defaultsiteevaluationquestionmaster WHERE defaultCSEvaluationQuestionId=" . $questionId;
		//ECHO 'DELETE->'.$sql;EXIT;
		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}

	function DeleteSiteEvaluationQuestionDetail($questionId)
	{
		$objDB = new clsDB();

		$sql = "DELETE  FROM schoolsiteevaluationquestiondetail WHERE schoolCSEvaluationQuestionId=" . $questionId;
		//ECHO 'DELETE->'.$sql;EXIT;
		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}

	function DeleteDefaultSiteEvaluationQuestionDetail($questionId)
	{
		$objDB = new clsDB();

		$sql = "DELETE  FROM defaultsiteevaluationquestiondetail WHERE defaultCSEvaluationQuestionId=" . $questionId;

		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}

	function SaveSiteEvaluationQuestionOptions()
	{
		$objDB = new clsDB();
		$sql = '';
		$sql = "INSERT INTO schoolsiteevaluationquestiondetail(schoolCSEvaluationQuestionId,optionText,schoolOptionValue) 
						VALUES ('" . addslashes($this->schoolCSEvaluationQuestionId) . "',
								'" . addslashes($this->optionText) . "',					
								'" . addslashes($this->schoolOptionValue) . "'					
								)";
		///echo 'Insert->'.$sql;exit;
		$objDB->ExecuteInsertQuery($sql);
		unset($objDB);
	}
	function SaveDefaultSiteEvaluationQuestionOptions()
	{
		$objDB = new clsDB();
		$sql = '';
		$sql = "INSERT INTO defaultsiteevaluationquestiondetail(defaultCSEvaluationQuestionId,optionText,defaultOptionValue) 
						VALUES ('" . addslashes($this->schoolCSEvaluationQuestionId) . "',
								'" . addslashes($this->optionText) . "',					
								'" . addslashes($this->schoolOptionValue) . "'					
								)";
		///echo 'Insert->'.$sql;exit;
		$objDB->ExecuteInsertQuery($sql);
		unset($objDB);
	}

		function GetSiteevaluationQuestionOptionDetails($questionId)
		{

			$objDB = new clsDB();
			$rows = "";
			$sql = "SELECT schoolsiteevaluationquestionmaster. *,schoolsiteevaluationquestiondetail.*
							FROM schoolsiteevaluationquestionmaster 
							INNER JOIN schoolsiteevaluationquestiondetail ON schoolsiteevaluationquestionmaster.schoolCSEvaluationQuestionId=
							schoolsiteevaluationquestiondetail.schoolCSEvaluationQuestionId
							WHERE schoolsiteevaluationquestionmaster.schoolCSEvaluationQuestionId=" . $questionId .
				" ORDER BY schoolsiteevaluationquestiondetail.schoolOptionValue";
			//echo $sql;exit;
			$rows = $objDB->GetResultset($sql);
			return $rows;
			unset($objDB);
		}
		function GetDefaultSiteevaluationQuestionOptionDetails($questionId)
		{

			$objDB = new clsDB();
			$rows = "";
			$sql = "SELECT defaultsiteevaluationquestionmaster.*,defaultsiteevaluationquestiondetail.*,defaultsiteevaluationquestiondetail.defaultOptionValue as schoolOptionValue
							FROM defaultsiteevaluationquestionmaster 
							INNER JOIN defaultsiteevaluationquestiondetail ON defaultsiteevaluationquestionmaster.defaultCSEvaluationQuestionId=
							defaultsiteevaluationquestiondetail.defaultCSEvaluationQuestionId
							WHERE defaultsiteevaluationquestionmaster.defaultCSEvaluationQuestionId=" . $questionId .
				" ORDER BY defaultsiteevaluationquestiondetail.defaultOptionValue";
			//echo $sql;exit;
			$rows = $objDB->GetResultset($sql);
			return $rows;
			unset($objDB);
		}
	function SetSiteEvaluationStatus($sectionMasterId, $status)
	{
		if ($sectionMasterId > 0) {
			$objDB = new clsDB();
			$sql = "Update sitesectionmaster set isActive = " . $status . " Where sectionMasterId = " . $sectionMasterId;
			//echo $sql;exit;
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
	}
	function GetSiteQuestionPosition($questionId)
	{
		$objDB = new clsDB();
		$sql = "select  isPosition from schoolsiteevaluationquestionmaster   Where schoolCSEvaluationQuestionId = " . $questionId;
		//echo $sql;
		$result = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $result;
	}

	function GetAllCSEvaluationForApp($rotationId, $studentId, $limitString = "", $searchText = '')
	{
		$objDB = new clsDB();
		$sql = "SELECT siteevaluationmaster.*,
						siteevaluationmaster.csEvaluationMasterId AS StudentCsEvaluationMasterId,
						siteevaluationdetail.*,student.studentId,
						rotation.rotationId,rotation.title,student.firstName,student.lastName,
						rotation.parentRotationId,rotation.locationId as rotationLocationId,courses.locationId, 
						schoolclinicalsiteunit.title AS schoolClinicalSiteUnitName, hospitalsites.title AS hospitalSiteName
						FROM siteevaluationmaster
						LEFT JOIN siteevaluationdetail ON siteevaluationmaster.csEvaluationMasterId=
															siteevaluationdetail.csEvaluationMasterId
						LEFT JOIN rotation ON siteevaluationmaster.rotationId=rotation.rotationId
						LEFT JOIN courses ON rotation.courseId=courses.courseId
						LEFT JOIN student ON siteevaluationmaster.studentId=student.studentId
						LEFT JOIN hospitalsites on siteevaluationmaster.hospitalSiteId = hospitalsites.hospitalSiteId
                        LEFT JOIN schoolclinicalsiteunit ON siteevaluationmaster.schoolClinicalSiteUnitId = schoolclinicalsiteunit.schoolClinicalSiteUnitId
						";

		$sql .= " WHERE siteevaluationmaster.csEvaluationMasterId !=0 ";
		if ($rotationId > 0) {
			$sql .= " AND siteevaluationmaster.rotationId=" . $rotationId;
		}
		if ($studentId > 0) {
			$sql .= "  AND siteevaluationmaster.studentId=" . $studentId;
		}
		if ($searchText != "") {
			$sql .= " AND ( hospitalsites.title LIKE '%" . $searchText . "%' OR schoolclinicalsiteunit.title LIKE '%" . $searchText . "%') ";
		}
		$sql .= " GROUP BY siteevaluationmaster.csEvaluationMasterId";
		$sql .= " order BY siteevaluationmaster.evaluationDate Desc" . $limitString;

		// ECHO $sql;	
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}

	
	function saveSiteEvalAuditLog($siteEvalMasterId=0, $retSiteEvalId, $clinicianId, $userType, $action, $isMobile = 0,$type = '',$isSuperAdmin=0)
	{	
		// Instantiate the Logger and Site Evaluation classes
		$objLog = new clsLogger();
		$objSiteEval = new clsSiteevaluation();

		// Prepare log data
		[$logData, $rowData, $additionalData] = $objSiteEval->createSiteEvalLog($retSiteEvalId, $action, $clinicianId, $userType,$type,$isSuperAdmin);
		$logData['isMobile'] = $isMobile;

		if ($action == 'Delete') {

			if ($type == 'section') {
				if($isSuperAdmin){
					$objSiteEval->DeleteDefaultSiteEvaluationSection($retSiteEvalId);
				} else {
					$objSiteEval->DeleteSiteEvaluationSection($retSiteEvalId);
				}	
			} else if ($type == 'step') {

				if ($isSuperAdmin) {
					// Initialize database object
					$objDB = new clsDB();

					// Fetch data from `siteevaluationdetail` table for the given master ID
					$evaluationDetailsResult = $objDB->GetAllSelectedDataFromTable('defaultsiteevaluationquestiondetail', '', 'defaultCSEvaluationQuestionId', $retSiteEvalId);
					unset($objDB);

					if ($evaluationDetailsResult) {
						// Convert the result set into an array
						$evaluationDetailsArray = fetchResultAsArray($evaluationDetailsResult);

						// Generate the JSON array (if needed for logs or other purposes)
						$additionalData = $evaluationDetailsArray;

						$objSiteEval->DeleteDefaultSiteEvaluationQuestion($retSiteEvalId);

					}
				} else {
					// Initialize database object
					$objDB = new clsDB();

					// Fetch data from `siteevaluationdetail` table for the given master ID
					$evaluationDetailsResult = $objDB->GetAllSelectedDataFromTable('siteevaluationdetail', '', 'csEvaluationMasterId', $retSiteEvalId);
					unset($objDB);

					if ($evaluationDetailsResult) {
						// Convert the result set into an array
						$evaluationDetailsArray = fetchResultAsArray($evaluationDetailsResult);

						// Generate the JSON array (if needed for logs or other purposes)
						$additionalData = $evaluationDetailsArray;
						// print_r($additionalData);exit;

						$objSiteEval->DeleteSiteEvaluationQuestion($retSiteEvalId);

					}
				}


			} else {
				// Initialize database object
				$objDB = new clsDB();

				// Fetch data from `siteevaluationdetail` table for the given master ID
				$evaluationDetailsResult = $objDB->GetAllSelectedDataFromTable('siteevaluationdetail', '', 'csEvaluationMasterId', $retSiteEvalId);
				unset($objDB);

				if ($evaluationDetailsResult) {
					// Convert the result set into an array
					$evaluationDetailsArray = fetchResultAsArray($evaluationDetailsResult);

					// Generate the JSON array (if needed for logs or other purposes)
					$additionalData = $evaluationDetailsArray;

					$objSiteEval->DeleteCSEvaluation($retSiteEvalId);
				}
			}
		}
		// Save the log details
		$objLog->saveLogs($logData, $action, $retSiteEvalId, 'Site Evaluation', $rowData, $additionalData);

		// Clean up
		unset($objLog);
		unset($objSiteEval);

		return true; // Return success or handle further actions as needed
	}

	function createSiteEvalLog($id, $action, $userId, $userType,$type,$isSuperAdmin)
	{
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$objSiteEval = new clsSiteevaluation(); // Assuming `Site Evaluation` class is used for `preparesiteevalLogData`

		// Get user details based on user type
		$userDetails = getUserDetails($userId, $userType);

		// Retrieve  Site Evaluation details for the given siteeval ID and school ID
		if ($type == 'section') {
			$rowData = $objSiteEval->GetSiteSectionDetailsForlog($id,$isSuperAdmin);
		} else if ($type == 'step') {
			$rowData = $objSiteEval->GetSiteEvalStepDetailsForLogs($id,$isSuperAdmin);
		} else {
			$rowData = $objSiteEval->GetAllSiteEvalDetailsForLogs($id);
		}

		// echo '<pre>';
		// print_r($rowData);
		// exit;
		if ($rowData != '')
			$logData = $objLog->generateLogData($rowData, $userType);

		$additionalData = '';

		// Populate log data with user details
		$logData['userId'] = $userDetails['userId'];
		$logData['userName'] = $userDetails['userName'];
		$logMessage = '';

		if ($type == 'section') {
			if ($action == 'Add') {
				$logMessage = $logData['userName'] . ' added new section in Site Evaluation.';
			} else if ($action == 'Edit') {
				$logMessage = $logData['userName'] . ' updated the Section from Site Evaluation.';
			} else if ($action == 'Delete') {
				$logMessage = $logData['userName'] . ' deleted the section from Site Evaluation.';
			} else if ($action == 'Active') {
				$logMessage = $logData['userName'] . ' activate the section from Site Evaluation.';
			} else if ($action == 'Inactive') {
				$logMessage = $logData['userName'] . ' deactivate the section from Site Evaluation.';
			}
		} else if ($type == "step") {

			if ($action == 'Add') {
				$logMessage = $logData['userName'] . ' added new Step in Site Evaluation Section.';
			} else if ($action == 'Edit') {
				$logMessage = $logData['userName'] . ' updated Step from Site Evaluation Section.';
			} else if ($action == 'Delete') {
				$logMessage = $logData['userName'] . ' deleted Step from Site Evaluation Section.';
			} 
		} else {

			if ($action == 'Add') {
				$logMessage = $logData['userName'] . ' added Site Evaluation for ' . $logData['rotationName'] . ' rotation.';
			} else if ($action == 'Edit') {
				$logMessage = $logData['userName'] . ' updated Site Evaluation from ' . $logData['rotationName'] . ' rotation.';
			} else if ($action == 'Delete') {
				$logMessage = $logData['userName'] . ' deleted Site Evaluation from ' . $logData['rotationName'] . ' rotation.';
			} 
		}

		// Construct log message
		$logData['message'] = $logMessage;

		// Return the data
		return [$logData, $rowData, $additionalData];
	}



	function GetAllSiteEvalDetailsForLogs($SiteEvalMasterId, $schoolId = 0)
	{
		$objDB = new clsDB();

		$sql = "SELECT siteevaluationmaster.*, rotation.title as rotationName,siteevaluationmaster.studentId as userId,
				CONCAT(student.firstName, ' ', student.lastName) AS userName,hospitalsites.title AS hospitalSiteName, 
				schools.displayName as schoolName
		FROM `siteevaluationmaster` 
		INNER JOIN rotation ON siteevaluationmaster.`rotationId` = rotation.`rotationId` 
		INNER JOIN student ON student.studentId=siteevaluationmaster.studentId
		LEFT JOIN hospitalsites ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
		INNER JOIN schools ON schools.schoolId = siteevaluationmaster.schoolId
		WHERE csEvaluationMasterId =" . $SiteEvalMasterId;
		
		if ($schoolId) {
			$sql .= " AND rotation.schoolId=" . $schoolId;
		}
		// echo $sql;
		// exit;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}

	// Site Evaluation Section for Audit Log
	function GetSiteSectionDetailsForlog($sectionMasterId,$isSuperAdmin=0)
	{
		
		$objDB = new clsDB();
		$rows = "";
		if($isSuperAdmin){
			$sql = "SELECT * FROM  defaultsitesectionmaster WHERE sectionMasterId=" . $sectionMasterId;
		} else {
			$sql = "SELECT sitesectionmaster.*,schools.schoolId,schools.displayName as schoolName FROM  sitesectionmaster
				INNER JOIN schools ON sitesectionmaster.schoolId=schools.schoolId
				WHERE sectionMasterId=" . $sectionMasterId;
		}
		// echo $sql;exit;
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}

	// Site Evaulation Steps For Audit Log
	function GetSiteEvalStepDetailsForLogs($questionId, $isSuperAdmin = 0)
	{

		$objDB = new clsDB();
		$rows = "";
		if($isSuperAdmin){
			$sql = "SELECT * FROM  defaultsiteevaluationquestionmaster
					WHERE defaultCSEvaluationQuestionId=" . $questionId;
		} else {
			$sql = "SELECT schoolsiteevaluationquestionmaster. *,schoolsiteevaluationquestiondetail.*,schools.displayName as schoolName
					FROM schoolsiteevaluationquestionmaster 
					INNER JOIN schools ON schools.schoolId = schoolsiteevaluationquestionmaster.schoolId
					INNER JOIN schoolsiteevaluationquestiondetail ON schoolsiteevaluationquestionmaster.schoolCSEvaluationQuestionId=
					schoolsiteevaluationquestiondetail.schoolCSEvaluationQuestionId
					WHERE schoolsiteevaluationquestionmaster.schoolCSEvaluationQuestionId=" . $questionId;
		}
		//echo $sql;exit;
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}
}
