<?php

class clsChatApp
{
    var $userManagmentId = 0;
    var $userId = 0;
    var $firstName = '';
    var $lastName = '';
    var $email = '';
    var $phone_no = 0;
    var $address = '';
    var $profileImagePath = '';
    var $businessName = '';
    var $parent_id = 0;
    var $role_id = 0;
    var $created_at = 0;
    var $updated_at = 0;
    var $deleted_at = 0;
    var $realtions = '';

    //  table : userfcmtokens

    var $tokenId = 0;
    var $schoolId = 0;
    var $fcmToken = '';
    var $chatroleId = 0;
    var $device = 0;


    function handle_error($message)
    {
        // Log or display the error message
        echo 'Error: ' . $message;
        exit;
    }
    function SaveNewuserdataTochat($postData)
    {

        // Endpoint URL
        $endpoint = 'https://' . CHAT_USER_SERVICE . '/api/v1/imports/createUser';

        // Client Secret Key to be sent in the request body
        $API_KEY = CHAT_APIKEY_LOCAL; // CHAT_APIKEY_LOCAL CHAT_APIKEY

        // Initialize cURL session
        $ch = curl_init();
        // Set cURL options
        curl_setopt($ch, CURLOPT_URL, $endpoint);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'x-api-key: ' . $API_KEY,
            'Content-Type: application/json'
        ));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        // Execute cURL request
        $response = curl_exec($ch);


        // Check for errors
        if (curl_errno($ch)) {
            // If there's an error, return the error status and message
            $responseArray = array(
                'status' => 'Apierror',
                'message' => curl_error($ch)
            );
        } else {
            // If successful, return success status and response object
            $responseArray = array(
                'status' => 'Apisuccess',
                'response' => json_decode($response, true)
            );
        }

        // Close cURL session
        curl_close($ch);

        //  echo json_encode($responseArray);
        // Output response
        return $responseArray;

        // exit;
    }


    function UpdatechatSetting($postData)
    {

        // Endpoint URL
        $endpoint = 'https://' . CHAT_DOMAIN_BACKEND . '/api/v1/client/chat';

        // Client Secret Key to be sent in the request body
        $API_KEY = CHAT_APIKEY_LOCAL; // CHAT_APIKEY_LOCAL CHAT_APIKEY

        // Initialize cURL session
        $ch = curl_init();
        // Set cURL options
        curl_setopt($ch, CURLOPT_URL, $endpoint);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'x-api-key: ' . $API_KEY,
            'Content-Type: application/json'
        ));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        // Execute cURL request
        $response = curl_exec($ch);


        // Check for errors
        if (curl_errno($ch)) {
            // If there's an error, return the error status and message
            $responseArray = array(
                'status' => 'Apierror',
                'message' => curl_error($ch)
            );
        } else {
            // If successful, return success status and response object
            $responseArray = array(
                'status' => 'Apisuccess',
                'response' => json_decode($response, true)
            );
        }

        // Close cURL session
        curl_close($ch);

        //  echo json_encode($responseArray);exit;
        // Output response
        return $responseArray;

        // exit;
    }



    function UpdateUserProfilePicTochat($profileImagePath, $userId, $role)
    {

        $data = [
            'profilePic' => $profileImagePath,
            'role' => $role,
            'ref_userId' => $userId,
        ];

        // Convert data to JSON
        $postData = json_encode($data);
        // Endpoint URL
        $endpoint = 'https://' . CHAT_USER_SERVICE . '/api/v1/user/updateProfilePic';

        // Client Secret Key to be sent in the request body
        $API_KEY = CHAT_APIKEY_LOCAL;

        // Initialize cURL session
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $endpoint);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PUT");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'x-api-key: ' . $API_KEY,
            'Content-Type: application/json'
        ));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);


        // Execute cURL request
        $response = curl_exec($ch);
        // print_r($response);
        // exit;
        // Check for errors
        if (curl_errno($ch)) {
            // If there's an error, return the error status and message
            $responseArray = array(
                'status' => 'Apierror',
                'message' => curl_error($ch)
            );
        } else {
            // If successful, return success status and response object
            $responseArray = array(
                'status' => 'Apisuccess',
                'response' => json_decode($response, true)
            );
        }

        // Close cURL session
        curl_close($ch);

        // echo json_encode($responseArray);

        // Output response
        return $responseArray;


        // exit;
    }


    function UpdateUserDataTochat($postData)
    {
        // print_r($postData);
        // exit;

        // $localDomain = 'https://192.168.1.26:4000';
        // $liveDomaein = 'https://chatuserservice.probizca.net';

        // Endpoint URL
        $endpoint = 'https://' . CHAT_USER_SERVICE . '/api/v1/user/update';

        // Initialize cURL session
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $endpoint);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PUT");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'x-api-key: ' . CHAT_APIKEY_LOCAL,
            'Content-Type: application/json'
        ));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        // Set timeout to 60 seconds (1 minute)
        curl_setopt($ch, CURLOPT_TIMEOUT, 60); // maximum execution time for the request
        // Execute cURL request
        $response = curl_exec($ch);
        // print_r($response);
        // exit;

        // Check for errors
        if (curl_errno($ch)) {
            // If there's an error, return the error status and message
            $responseArray = array(
                'status' => 'Apierror',
                'message' => curl_error($ch)
            );
        } else {
            // If successful, return success status and response object
            $responseArray = array(
                'status' => 'Apisuccess',
                'response' => json_decode($response, true)
            );
        }

        // Close cURL session
        curl_close($ch);

        // echo json_encode($responseArray);
        // Output response
        return $responseArray;


        // exit;
    }

    function SendDeviceTokenToChatApp($postData)
    {
        // print_r($postData);
        // exit;

        $endpoint = 'https://' . CHAT_DOMAIN_BACKEND . '/api/v1/auth/updateFcm';

        // Initialize cURL session
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $endpoint);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PUT");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'x-api-key: ' . CHAT_APIKEY_LOCAL,
            'Content-Type: application/json'
        ));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);


        // Execute cURL request
        $response = curl_exec($ch);
        // print_r($response);
        // exit;

        // Check for errors
        if (curl_errno($ch)) {
            // If there's an error, return the error status and message
            $responseArray = array(
                'status' => 'Apierror',
                'message' => curl_error($ch)
            );
        } else {
            // If successful, return success status and response object
            $responseArray = array(
                'status' => 'Apisuccess',
                'response' => json_decode($response, true)
            );
        }

        // Close cURL session
        curl_close($ch);

        // echo json_encode($responseArray);exit;
        // Output response
        return $responseArray;
    }


    function DeleteUserDeviceTokenToChatApp($postData)
    {
        // print_r($postData);
        // exit;

        $endpoint = 'https://' . CHAT_DOMAIN_BACKEND . '/api/v1/auth/deleteFcm';

        // Initialize cURL session
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $endpoint);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PUT");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'x-api-key: ' . CHAT_APIKEY_LOCAL,
            'Content-Type: application/json'
        ));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);


        // Execute cURL request
        $response = curl_exec($ch);
        // print_r($response);
        // exit;

        // Check for errors
        if (curl_errno($ch)) {
            // If there's an error, return the error status and message
            $responseArray = array(
                'status' => 'Apierror',
                'message' => curl_error($ch)
            );
        } else {
            // If successful, return success status and response object
            $responseArray = array(
                'status' => 'Apisuccess',
                'response' => json_decode($response, true)
            );
        }

        // Close cURL session
        curl_close($ch);

        // echo json_encode($responseArray);exit;
        // Output response
        return $responseArray;
    }




    function DeleteUserDataTochat($userId, $role_id)
    {
        // print_r($postData);
        // exit;


        // curl --location --request DELETE 'https://localhost:4000/api/v1/user/delete?ref_userId=200&parent_id=60&role=3'
        // --header 'x-api-key: c3d2118b7bb3d73f'


        // Endpoint URL
        $endpoint = 'https://' . CHAT_USER_SERVICE . '/api/v1/user/delete?ref_userId=' . $userId . '&role=' . $role_id;

        // Client Secret Key to be sent in the request body
        $API_KEY = CHAT_APIKEY_LOCAL;

        // Initialize cURL session
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $endpoint);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "DELETE");
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'x-api-key: ' . $API_KEY,
            'Content-Type: application/json'
        ));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);


        // Execute cURL request
        $response = curl_exec($ch);
        // Check for errors
        if (curl_errno($ch)) {
            // If there's an error, return the error status and message
            $responseArray = array(
                'status' => 'Apierror',
                'message' => curl_error($ch)
            );
        } else {
            // If successful, return success status and response object
            $responseArray = array(
                'status' => 'Apisuccess',
                'response' => json_decode($response, true)
            );
        }

        // Close cURL session
        curl_close($ch);

        // echo json_encode($responseArray);
        // Output response
        return $responseArray;


        // exit;
    }


    function GetUserData($chatroleId)
    {
        $rows = "";
        $objDB = new clsDB();
        // Superadmin user roleid 1
        if ($chatroleId == 1) {
            $sql = "SELECT 
                    systemuser.systemUserId as userId,
                    systemuser.firstName,
                    systemuser.lastName,
                    systemuser.email,
                    systemuser.phoneNo as phone_no,
                    systemuser.address,
                    systemuser.imageName,
                    1 as role_id
                FROM 
                    systemuser";
        } else if ($chatroleId == 2) {
            // Business Owner roleid 2
            $sql = "SELECT 
                    e.employeeId as userId,
                    e.firstName,
                    e.lastName,
                    e.email,
                    e.phoneNo as phone_no,
                    e.address,
                    b.businessName,
                    b.businessId as parent_id,
                    e.imageName,
                    2 as role_id
                FROM 
                    business b
                INNER JOIN 
                    employee e ON b.businessId = e.businessId
                WHERE 
                    e.isPrimary = 1 or e.isPrimaryUser = 1";
        } else if ($chatroleId == 3) {
            // Employee roleid 3
            $sql = "SELECT 
                    e.employeeId as userId,
                    e.firstName,
                    e.lastName,
                    e.email,
                    e.phoneNo as phone_no,
                    e.address,
                    b.businessName,
                    b.businessId as parent_id,
                    e.imageName,
                    3 as role_id
                FROM 
                    business b
                INNER JOIN 
                    employee e ON b.businessId = e.businessId
                WHERE 
                    e.isPrimary = 0 AND e.isPrimaryUser = 0";
        } else if ($chatroleId == 4) {
            // Customer/Contact roleid 4
            $sql = "SELECT 
                    customer.customerId as userId,
                    customer.firstName,
                    customer.lastName,
                    customer.email,
                    customer.phoneNo as phone_no,
                    customer.address,
                    business.businessName,
                    business.businessId as parent_id,
                    businesscustomer.refId,
                    businesscustomer.refType,
                    businesscustomer.employeeId,
                    businesscustomer.affiliateId,
                    customer.imageName,
                    4 as role_id
                FROM 
                    businesscustomer
                INNER JOIN 
                    customer ON customer.customerId = businesscustomer.customerId
                INNER JOIN 
                    business ON business.businessId = businesscustomer.businessId
                WHERE 
                    business.businessId = businesscustomer.businessId AND customer.status = 1 
                ORDER BY 
                    businesscustomer.createdDate DESC";
        } else if ($chatroleId == 5) {
            // Affiliate roleid 5
            $sql = "SELECT 
                        affiliate.affiliateId as userId ,
                        affiliate.firstName,
                        affiliate.lastName,
                        affiliate.email,
                        affiliate.phoneNo as phone_no,
                        affiliate.address,
                        affiliate.imageName,
                        business.businessName, 
                        business.businessId as parent_id,
                        5 as role_id,
                        businessaffiliate.requestStatus 
                    FROM 
                        affiliate 
                    LEFT JOIN 
                        businessaffiliate ON affiliate.affiliateId=businessaffiliate.affiliateId
                    LEFT JOIN 
                        business ON businessaffiliate.businessId=business.businessId";
            // GROUP BY 
            //     businessaffiliate.businessId
            // echo $sql;exit;
        }


        // echo $sql;exit;		
        $rows = $objDB->GetResultset($sql);
        unset($objDB);
        return $rows;
    }


    function checkUserExists($userId, $role_id)
    {
        $rows = "";
        $objDB = new clsDB();

        $sql = "SELECT * FROM `userrolemanagement` WHERE userId = $userId AND role_id = $role_id ";
        // echo $sql;exit;
        $rows = $objDB->GetResultset($sql);
        unset($objDB);
        return $rows;
    }

    function SaveAllUsers($userManagmentId)
    {
        $objDB = new clsDB();
        $sql = '';
        if ($userManagmentId > 0) {
            $sql = "UPDATE userrolemanagement SET 													
							userId = '" . addslashes($this->userId) . "',																
							firstName = '" . addslashes($this->firstName) . "',																
							lastName = '" . addslashes($this->lastName) . "',
							email = '" . addslashes($this->email) . "',																
							phone_no = '" . addslashes($this->phone_no) . "',		
							businessName = '" . addslashes($this->businessName) . "',																
							parent_id = '" . addslashes($this->parent_id) . "',																
							role_id = '" . addslashes($this->role_id) . "'															
							WHERE id= " . $userManagmentId;
            //  echo $sql;exit;	
            // address = '" . addslashes($this->address) . "',																
            // 				profileImagePath = '" . addslashes($this->profileImagePath) . "',									
            $objDB->ExecuteQuery($sql);
        } else {
            $sql = "INSERT INTO userrolemanagement(userId,firstName,lastName,email,phone_no,address,profileImagePath,businessName,parent_id,role_id) 
								VALUES(								  				
									  '" . addslashes($this->userId) . "',								
									  '" . addslashes($this->firstName) . "',								
									  '" . addslashes($this->lastName) . "',																
									  '" . ($this->email) . "',								
									  '" . addslashes($this->phone_no) . "',								
									  '" . addslashes($this->address) . "',								
									  '" . addslashes($this->profileImagePath) . "',																
									  '" . addslashes($this->businessName) . "',																
									  '" . addslashes($this->parent_id) . "',																
									  " . $this->role_id . "				
									  )";
            // echo $sql;exit;  									  
            $userManagmentId = $objDB->ExecuteInsertQuery($sql);
        }

        unset($objDB);
        return $userManagmentId;
    }


    function SaveUserFCMToken($tokenId)
    {
        $objDB = new clsDB();
        $sql = '';
        if ($tokenId > 0) {
            $sql = "UPDATE userfcmtokens SET 													
							schoolId = '" . addslashes($this->schoolId) . "',																
							userId = '" . addslashes($this->userId) . "',																
							roleId = '" . addslashes($this->roleId) . "',																
							fcmToken	 = '" . ($this->fcmToken) . "',														
							device	 = '" . ($this->device) . "',														
							updated_at = '" . (date("Y-m-d h:i:s")) . "'														
							WHERE tokenId= " . $tokenId;
            //  echo $sql;exit;	
            // address = '" . addslashes($this->address) . "',																
            // 				profileImagePath = '" . addslashes($this->profileImagePath) . "',									
            $objDB->ExecuteQuery($sql);
        } else {
            $sql = "INSERT INTO userfcmtokens(schoolId,userId,roleId,fcmToken,device,created_at) 
								VALUES(								  				
									  '" . addslashes($this->schoolId) . "',								
									  '" . addslashes($this->userId) . "',								
									  '" . addslashes($this->roleId) . "',								
									  '" . addslashes($this->fcmToken) . "',																
									  '" . ($this->device) . "',								
									 '" . date("Y-m-d h:i:s") . "'				    
									  )";
            // echo $sql;exit;  									  
            $tokenId = $objDB->ExecuteInsertQuery($sql);
        }

        unset($objDB);
        return $tokenId;
    }

    function checkUserFCMToken($schoolId, $userId, $chatroleId, $device)
    {
        $row = "";
        $objDB = new clsDB();
        $sql = "SELECT * FROM userfcmtokens 
		WHERE schoolId = $schoolId  AND userId= $userId AND roleId = $chatroleId AND  device = $device";
        // echo $sql;exit;	
        $rows = $objDB->GetDataRow($sql);
        unset($objDB);
        return $rows;
    }


    function updateUserRelations($userId, $realtions, $role_id)
    {
        $row = "0";
        $objDB = new clsDB();
        $sql = "UPDATE userrolemanagement SET 																							
		realtions = '" . addslashes($realtions) . "'
		WHERE userId= '" . $userId . "' And role_id ='" . $role_id . "'";
        // echo $sql;exit;	
        $objDB->ExecuteQuery($sql);
        unset($objDB);
        return $row;
    }

    function DeleteUser($userId, $role_id)
    {
        $row = "0";
        $objDB = new clsDB();
        $sql = "UPDATE userrolemanagement SET 																							
		deleted_at = '" . date('Y-m-d H:i:s') . "'
		WHERE userId= '" . $userId . "' And role_id ='" . $role_id . "'";
        // echo $sql;exit;	
        $objDB->ExecuteQuery($sql);
        unset($objDB);
        return $row;
    }


    function GetUserDatat($userId, $role_id)
    {
        $row = "";
        $objDB = new clsDB();
        $sql = "SELECT * FROM userrolemanagement 
		WHERE userId= '" . $userId . "' And role_id ='" . $role_id . "'";
        // echo $sql;exit;	
        $rows = $objDB->GetDataRow($sql);
        unset($objDB);
        return $rows;
    }

    function getUserDetails($role_id, $userId)
    {
        $rows = "";
        $objDB = new clsDB();
        $sql = "SELECT * FROM `userrolemanagement` WHERE role_id = $role_id AND userId = $userId";
        // echo $sql;exit;	
        $rows = $objDB->GetDataRow($sql);
        unset($objDB);
        return $rows;
    }


    function getUserDetailsForCustomer($role_id, $userId)
    {
        $rows = "";
        $objDB = new clsDB();
        $sql = "SELECT * FROM `userrolemanagement` WHERE role_id = $role_id AND userId = $userId";
        // echo $sql;exit;	
        $rows = $objDB->GetResultset($sql);
        unset($objDB);
        return $rows;
    }

    // Define a common function to set ChatApp user properties and save the user data
    function setAndSaveChatAppUser($objChatApp, $userId, $firstName, $lastName, $email, $phone, $address, $profileImagePath, $businessName, $parentId, $chatroleId, $userRoleManagementId)
    {
        // Set properties
        $objChatApp->userId = $userId;
        $objChatApp->firstName = ucfirst($firstName);
        $objChatApp->lastName = ucfirst($lastName);
        $objChatApp->email = $email;
        $objChatApp->phone_no = $phone;
        // $objChatApp->address = $address;
        // $objChatApp->profileImagePath = $profileImagePath;
        $objChatApp->businessName = $businessName;
        $objChatApp->parent_id = $parentId;
        $objChatApp->role_id = $chatroleId;

        // Save user data to the chat module and return the result
        return $this->SaveAllUsers($userRoleManagementId);
    }

    // Define a common function to prepare data, send API request, and handle errors
    function prepareAndSendUserData($objChatApp, $userId, $firstName, $lastName, $email, $phone, $profileImagePath, $address, $chatroleId, $businessName, $parentId, $userRoleManagementId)
    {
        // Prepare data for the API call
        if ($userRoleManagementId) {
            $data = [
                'ref_userId' => $userId,
                'firstName' => ucfirst($firstName),
                'lastName' => ucfirst($lastName),
                'emailToUpdate' => $email,
                'phoneNumber' => $phone,
                'address' => $address,
                'role' => $chatroleId,
            ];
        } else {
            $data = [
                'ref_userId' => $userId,
                'firstName' => ucfirst($firstName),
                'lastName' => ucfirst($lastName),
                'email' => $email,
                'phoneNumber' => $phone,
                'profilePic' => $profileImagePath,
                'address' => $address,
                'role' => $chatroleId,
                'businessName' => $businessName,
                'parent_id' => $parentId
            ];
        }
        // print_r($data);
        // Convert data to JSON format for the API request
        $postData = json_encode($data);

        // Decide on which API call to use based on user role management ID
        $response = $userRoleManagementId ?
            $this->UpdateUserDataTochat($postData) :
            $this->SaveNewuserdataTochat($postData);

        // print_r($response);exit;
        // Check for API errors and display the error message if present
        if ($response['status'] === 'Apierror') {
            // echo $response['message'];
            // exit;
        }
    }

    function AuthorizeUser($data)
    {
        // print_r($data);
        // exit;
        $Domainbackend = CHAT_DOMAIN_BACKEND;
        // Construct endpoint URL
        $endpoint = 'https://' . CHAT_DOMAIN_BACKEND . '/api/v1/oauth/authorize';
        // $endpoint = 'https://' . CHAT_DOMAIN_BACKEND . '/api/v1/oauth/token?authorization_code=' . $authorizationCode . '&state=' . $state . '&user_id=' . $user_id;

        // echo $endpoint .'?'. $data;exit;
        // Initialize cURL session
        $ch = curl_init();

        // Set cURL options
        curl_setopt($ch, CURLOPT_URL, $endpoint . '?' . $data);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer 60fcc34dff52041d', // Replace with your API key or token
            'Content-Type: application/json' // Specify content type if required
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        // Execute cURL request
        $response = curl_exec($ch);

        // Check for errors
        if (curl_errno($ch)) {
            $this->handle_error(curl_error($ch));
        }

        // Close cURL session
        curl_close($ch);
        return $response;

        // Output response
        // $array = json_decode($response, true);
        // $redirectUrl = $array['data'] ?? '';


        // echo $redirectUrl;exit;

        // exit;
    }

    function AuthCodeExchange($authorizationCode, $user_id, $state, $conversationId = 0, $authorRole = 0)
    {

        $curl = curl_init();
        $endpoint = 'https://' . CHAT_DOMAIN_BACKEND . '/api/v1/oauth/token?authorization_code=' . $authorizationCode . '&state=' . $state . '&user_id=' . $user_id;
        // echo $endpoint;
        curl_setopt_array($curl, array(
            CURLOPT_URL => $endpoint,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
        ));

        $response = curl_exec($curl);

        curl_close($curl);
        return $response;
    }

    function UpdateFCMToken($data)
    {

        // Endpoint URL
        $endpoint = 'https://' . CHAT_USER_SERVICE . '/api/v1/user/update';

        // Client Secret Key to be sent in the request body
        $API_KEY = CHAT_APIKEY_LOCAL; // CHAT_APIKEY_LOCAL CHAT_APIKEY

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => $endpoint,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'PUT',
            CURLOPT_POSTFIELDS => $data,
            CURLOPT_HTTPHEADER => array(
                'x-api-key: ' . $API_KEY,
                'Content-Type: application/json'
            ),
        ));

        $response = curl_exec($curl);

        curl_close($curl);
        return $response;
    }
}
