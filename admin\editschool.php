<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsSchool.php');
include('../class/clsCountryStateMaster.php');
include('../setRequest.php');


$timeZoneId = '';
$contractPeriodId = '';
$billingId = '';
$schoolName = '';
$contactPerson = '';
$email = '';
$phone = '';
$address1 = '';
$address2 = '';
$image_name = '';
$defaultSchoolImagePath = '';
$city = '';
$zip = "";

$dbStateId = '';
$dbCountryId = '';
$title = "My";
$displayName = "";
$contactEmail = "";
$coarcId = "";
$isdefaultFloorAndIcuEval = 0;
$isActiveCheckoff = 0;
$checkoffType = 0;
$scheduleActive = 0;
$studentChangeHospitalSite = 0;
$selfUnlock = 0;
$countryCode = '';

//For Country List
$objLocation = new clsCountryStateMaster();
$countries = $objLocation->GetAllCountry();

if ($currentSchoolId > 0) //Edit Mode
{
    $title = "My";

    //For School Details
    $objSchool = new clsSchool();
    $row = $objSchool->GetSchoolDetails($currentSchoolId);
    $rowContarctPeriods = $objSchool->GetContarctPeriods();
    $rowBilling = $objSchool->GetBilling();
    $rowTimezone = $objSchool->GetTimezone();
    unset($objSchool);
    if ($row == '') {
        header('location:settings.html');
        exit;
    }

    $schoolName  = stripslashes($row['title']);
    $displayName  = stripslashes($row['displayName']);
    $contactPerson  = stripslashes($row['contactPerson']);
    $email  = stripslashes($row['email']);
    $phone  = stripslashes($row['phone']);
    $address1  = stripslashes($row['address1']);
    $address2  = stripslashes($row['address2']);
    $zip  = stripslashes($row['zip']);
    $countryCode  = ($row['countryCode'] != 0) ? $row['countryCode'] : '';
    $city  = stripslashes($row['city']);
    $dbStateId  = stripslashes($row['stateId']);
    $contactEmail  = $row['contactEmail'];
    $coarcId  = stripslashes($row['coarcId']);
    $billingId  = stripslashes($row['billingId']);
    $contractPeriodId  = stripslashes($row['contractPeriodId']);
    $timeZoneId  = stripslashes($row['timeZoneId']);
    $contractStartDate  = ($row['contractStartDate']);
    $contractStartDate = date('m/d/Y', strtotime($contractStartDate));
    $accountPayableContact  = stripslashes($row['accountPayableContact']);
    $accountPayableEmail  = stripslashes($row['accountPayableEmail']);
    $accountPayablePhone  = stripslashes($row['accountPayablePhone']);
    $isdefaultFloorAndIcuEval  = stripslashes($row['isdefaultFloorAndIcuEval']);
    $image_name = stripslashes($row['logoName']);
    $defaultSchoolImagePath = GetSchoolImagePath($currentSchoolId, $image_name);

    //For State
    $objCountryStateMaster = new clsCountryStateMaster();
    $dbCountryId = $objCountryStateMaster->GetParentIdFromChildId($dbStateId);
    unset($objLocation);

    $isActiveCheckoff = $isActiveCheckoffForStudent;
    $checkoffType = stripslashes($row['checkoffType']);
    $scheduleActive = stripslashes($row['scheduleActive']);
    $studentChangeHospitalSite = isset($row['studentChangeHospitalSite']) ? stripslashes($row['studentChangeHospitalSite']) : 0;
    $selfUnlock = isset($row['selfUnlock']) ? stripslashes($row['selfUnlock']) : 0;
}


?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($title); ?> School</title>
    <?php include('includes/headercss.php'); ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">

    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/css/cropper.css">


</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">

                    <li><a href="dashboard.html">Home</a></li>

                    <li><a href="settings.html">Settings</a></li>
                    <li class="active">My School</li>
                </ol>
            </div>

        </div>
    </div>

    <div class="container">

        <form id="frmSchool" data-parsley-validate class="form-horizontal" method="POST" action="editschoolsubmit.html" enctype="multipart/form-data">

            <div class="row">
                <div class="col-md-12">

                    <div class="formSubHeading">School Information</div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="coarcId">Coarc Id</label>
                                <div class="col-md-12">
                                    <input id="coarcId" name="coarcId" type="number" placeholder="" value="<?php echo ($coarcId); ?>" required class="form-control input-md required-input">

                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">

                            <!-- Text input-->
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtSchoolName">School Code</label>
                                <div class="col-md-12">
                                    <input id="txtSchoolName" name="txtSchoolName" type="text" value="<?php echo ($schoolName); ?>" placeholder="" class="form-control input-md required-input" required="">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <!-- Text input-->
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtSchoolName">Display Name</label>
                                <div class="col-md-12">
                                    <input id="txtDisplayName" name="txtDisplayName" type="text" value="<?php echo ($displayName); ?>" placeholder="" class="form-control input-md required-input" required="">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <!-- Text input-->
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtEmail">Email</label>
                                <div class="col-md-12">
                                    <input id="txtEmail" name="txtEmail" type="email" placeholder="" value="<?php echo ($email); ?>" required class="form-control input-md required-input">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtContactPerson">Contact Person</label>
                                <div class="col-md-12">
                                    <input id="txtContactPerson" name="txtContactPerson" data-parsley-pattern="^(?:[A-Za-z]+[ -])*[A-Za-z]+$" type="text" value="<?php echo ($contactPerson); ?>" placeholder="" class="form-control input-md required-input" required="">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtEmail">Contact Email</label>
                                <div class="col-md-12">
                                    <input id="txtContactEmail" name="txtContactEmail" type="email" placeholder="" value="<?php echo ($contactEmail); ?>" required class="form-control input-md required-input">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <!-- Text input-->
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtPhone">Phone</label>
                                <div class="col-md-12">
                                    <input id="txtPhone" name="txtPhone" type="text" data-inputmask-alias="************" placeholder="" value="<?php echo ($phone); ?>" required class="form-control input-md required-input">

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="formSubHeading">Address Information</div>

                    <div class="row">
                        <div class="col-md-6">
                            <!-- Text input-->
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtAddress1">Address 1</label>
                                <div class="col-md-12">
                                    <input id="txtAddress1" name="txtAddress1" type="text" placeholder="" value="<?php echo ($address1); ?>" required class="form-control input-md required-input">

                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <!-- Text input-->
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtAddress2">Address 2</label>
                                <div class="col-md-12">
                                    <input id="txtAddress2" name="txtAddress2" type="text" placeholder="" value="<?php echo ($address2); ?>" class="form-control input-md">

                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="cboCountry">Country</label>
                                <div class="col-md-12 flex-col-reverse">
                                    <select id="cboCountry" name="cboCountry" class="form-control step1 input-md  select2_single" required>
                                        <option value="" selected>Select</option>
                                        <?php
                                        if ($countries != "") {
                                            while ($row = mysqli_fetch_assoc($countries)) {
                                                $location_id  = $row['location_id'];
                                                $name  = stripslashes($row['name']);

                                        ?>
                                                <option value="<?php echo ($location_id); ?>" <?php if ($dbCountryId == $location_id) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                                        <?php

                                            }
                                        }
                                        ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtCity">City</label>
                                <div class="col-md-12">
                                    <input id="txtCity" name="txtCity" type="text" placeholder="" value="<?php echo ($city); ?>" required class="form-control input-md required-input">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="cboState">State</label>
                                <div class="col-md-12 flex-col-reverse">
                                    <select id="cboState" name="cboState" class="form-control step2 input-md select2_single" required>
                                        <option value="" selected>Select</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtZipCode">Zip Code</label>
                                <div class="col-md-12">
                                    <input id="txtZipCode" name="txtZipCode" type="text" placeholder="" value="<?php echo ($zip); ?>" required class="form-control input-md required-input">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtCountryCode">Country Code</label>
                                <div class="col-md-12">
                                    <input id="txtCountryCode" name="txtCountryCode" type="text" placeholder="" value="<?php //echo ($countryCode); 
                                                                                                                        ?>" class="form-control input-md" disabled>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <div class="formSubHeading">System Information</div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="cbotimeZone">Time Zone</label>
                                <div class="col-md-12 flex-col-reverse">
                                    <select id="cbotimeZone" name="cbotimeZone" class="form-control input-md required-input select2_single" required>
                                        <option value="" selected>Select</option>

                                        <?php
                                        if ($rowTimezone != "") {
                                            while ($row = mysqli_fetch_assoc($rowTimezone)) {
                                                $seltimeZoneId  = $row['timeZoneId'];
                                                $locations  = stripslashes($row['locations']);
                                        ?>

                                                <option value="<?php echo ($seltimeZoneId); ?>" <?php if ($timeZoneId == $seltimeZoneId) { ?> selected="true" <?php } ?>><?php echo ($locations); ?></option>
                                        <?php

                                            }
                                        }
                                        ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="cbobilling">Billing</label>
                                <div class="col-md-12 flex-col-reverse">
                                    <select id="cbobilling" name="cbobilling" class="form-control input-md required-input select2_single" required>
                                        <option value="" selected>Select</option>

                                        <?php
                                        if ($rowBilling != "") {
                                            while ($row = mysqli_fetch_assoc($rowBilling)) {
                                                $selbillingId  = $row['billingId'];
                                                $billingTitle  = stripslashes($row['billingTitle']);
                                        ?>

                                                <option value="<?php echo ($selbillingId); ?>" <?php if ($billingId == $selbillingId) { ?> selected="true" <?php } ?>><?php echo ($billingTitle); ?></option>
                                        <?php

                                            }
                                        }
                                        ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="ContractStartDate">Contract Start Date</label>
                                <div class="col-md-12">
                                    <div class='input-group date w-full relative' id='ContractStartDate'>

                                        <input type='text' name="ContractStartDate" id="ContractStartDate" class="form-control input-md required-input rotation_date dateInputFormat" value="<?php echo ($contractStartDate); ?>" required data-parsley-errors-container="#error-txtDate" placeholder="MM-DD-YYYY" />
                                        <span class="input-group-addon calender-icon">
                                            <span class="glyphicon glyphicon-calendar"></span>
                                        </span>
                                    </div>
                                    <div id="error-txtDate"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="cbocontractPeriod">Contract Period</label>
                                <div class="col-md-12 flex-col-reverse">
                                    <select id="cbocontractPeriod" name="cbocontractPeriod" class="form-control input-md required-input select2_single" required>
                                        <option value="" selected>Select</option>

                                        <?php
                                        if ($rowContarctPeriods != "") {
                                            while ($row = mysqli_fetch_assoc($rowContarctPeriods)) {
                                                $selcontractPeriodId  = $row['contractPeriodId'];
                                                $contractPeriod  = stripslashes($row['contractPeriod']);

                                        ?>
                                                <option value="<?php echo ($selcontractPeriodId); ?>" <?php if ($contractPeriodId == $selcontractPeriodId) { ?> selected="true" <?php } ?>><?php echo ($contractPeriod); ?></option>
                                        <?php

                                            }
                                        }
                                        ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="AccountContactPerson">Account Payable Contact</label>
                                <div class="col-md-12">
                                    <input id="AccountContactPerson" name="AccountContactPerson" data-parsley-pattern="^(?:[A-Za-z]+[ -])*[A-Za-z]+$" type="text" value="<?php echo ($accountPayableContact); ?>" placeholder="" class="form-control input-md">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="AccountEmail">Account Payable Email</label>
                                <div class="col-md-12">
                                    <input id="AccountEmail" name="AccountEmail" type="email" placeholder="" value="<?php echo ($accountPayableEmail); ?>" class="form-control input-md">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <!-- Text input-->
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="AccountPhone">Account Payable Phone</label>
                                <div class="col-md-12">
                                    <input id="AccountPhone" name="AccountPhone" type="text" placeholder="" value="<?php echo ($accountPayablePhone); ?>" class="form-control input-md">

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <div class="formSubHeading">School Images</div>
                    <div style="display: flex; gap: 40px;">
                        <section class="upload-section" id="uploadSection">
                            <div class="upload-area" id="uploadArea">
                                <input style="visibility: hidden;" type="file" id="fileInput" accept="image/*" hidden>
                                <div class="upload-content">
                                    <svg class="upload-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                        <polyline points="7,10 12,15 17,10"></polyline>
                                        <line x1="12" y1="15" x2="12" y2="3"></line>
                                    </svg>
                                    <p>Click to browse or drag and drop an image</p>
                                    <span class="file-types">Supports: JPG, PNG, GIF, WebP</span>
                                </div>
                            </div>
                        </section>

                        <!-- Cropper Section -->
                        <section class="cropper-section" id="cropperSection" style="display: none;">
                            <div class="controls">
                                <div class="control-group">
                                    <div class="mode-buttons">
                                        <button type="button" class="mode-btn active" data-mode="rectangle">Rectangle</button>
                                        <button type="button" class="mode-btn" data-mode="square">Square</button>
                                    </div>
                                </div>
                                <div class="control-group">
                                    <button type="button" class="action-btn" id="resetBtn">Reset</button>
                                    <button type="button" class="action-btn" id="backBtn">Back</button>
                                    <button type="button" class="action-btn" id="bgRemovelToggle" style="display: none;">Remove Background</button>
                                    <button type="button" class="action-btn primary" id="cropBtn" style="display: none;">Crop & Save</button>
                                </div>
                                <div class="control-group">
                                    <label>Zoom:</label>
                                    <div class="zoom-controls">
                                        <button type="button" class="zoom-btn" id="zoomOut">-</button>
                                        <span class="zoom-level" id="zoomLevel">100%</span>
                                        <button type="button" class="zoom-btn" id="zoomIn">+</button>
                                        <input type="range" id="zoomSlider" min="20" max="300" value="100" class="zoom-slider">
                                    </div>
                                </div>
                            </div>
                            <div style="display: flex; gap: 20px; align-items: flex-start;">
                                <div class="image-container" id="imageContainer">
                                    <img id="previewImage" src="" alt="Preview">
                                    <div class="crop-overlay" id="cropOverlay">
                                        <div class="crop-selection" id="cropSelection"></div>
                                    </div>
                                </div>

                                <div class="preview-section">
                                    <h4>Cropped Preview</h4>
                                    <canvas id="previewCanvas" name='previewCanvas'></canvas>
                                </div>
                            </div>
                        </section>
                    </div>
                </div>
            </div>

            <input type="hidden" id="fileLogo" name="fileLogo" value="">
            <input type="hidden" id="hasCroppedImage" name="hasCroppedImage" value="0">

            <?php if ($currentSchoolId == 127 || $currentSchoolId == 75) { ?>
                <div class="row">
                    <div class="col-md-12">
                        <div class="formSubHeading">Settings</div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group defaultCIEval">
                                    <label class="col-md-12 control-label">Auto Approved Floor Therapy Evaluation</label>
                                    <div class="col-md-12">
                                        <select id="isdefaultFloorAndIcuEval" name="isdefaultFloorAndIcuEval" class="form-control input-md required-input select2_single col-md-12" required>
                                            <option value="0" <?php if ($isdefaultFloorAndIcuEval == "0") echo 'selected="selected"'; ?>>Yes</option>
                                            <option value="1" <?php if ($isdefaultFloorAndIcuEval == "1") echo 'selected="selected"'; ?>>No</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>

                    <div class="form-group">
                        <!-- <label class="col-md-2 control-label"></label> -->
                        <div class="col-md-12" style="display: flex; justify-content: center; margin: 20px 0;gap: 15px;">
                            <?php //if ($isPrimaryUser) { 
                            ?>
                            <button id="btnSubmit" class="btn btn-success" name="btnSubmit">Save</button>
                            <?php //} 
                            ?>
                            <a type="button" href="settings.html" class="btn btn-default">Cancel</a>
                        </div>
                    </div>
                </div>
        </form>
    </div>

    <?php include('includes/footer.php'); ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/jquery.cascadingdropdown.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/inputmask/jquery.inputmask.bundle.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/inputmask/inputmask.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/cropper.js"></script>

    <script type="text/javascript">
        $(window).load(function() {

            $(".select2_single").select2();
            $('#select2-cboCountry-container').addClass('required-select2');
            $('#select2-cboState-container').addClass('required-select2');
            $('#select2-cbocontractPeriod-container').addClass('required-select2');
            $('#select2-cbobilling-container').addClass('required-select2');
            $('#select2-cbotimeZone-container').addClass('required-select2');


            $('.image-preview').magnificPopup({
                type: 'image'
            });

            $('#frmSchool').parsley().on('field:validated', function() {
                    var ok = $('.parsley-error').length === 0;
                })
                .on('form:submit', function() {
                    ShowProgressAnimation();
                    $('#txtCountryCode').prop('disabled', false);
                    
                    // Check if we have a cropped image and no regular file upload
                    var hasCroppedImage = $('#hasCroppedImage').val() === '1';
                    
                    // If we have a cropped image, we need to handle this
                    if (hasCroppedImage) {
                        // The cropped image data is already in the hidden field
                        console.log('Submitting form with cropped image data');
                    }
                    
                    return true;
                });

            $('#ContractStartDate').datetimepicker({

                format: 'MM/DD/YYYY',
                defaultDate: new Date(),

            });

            <?php
            if ($currentSchoolId == 0) {
            ?>
                $('#cboCountry').val('224').trigger('change');
            <?php
            }
            ?>

            $('#frmSchool').cascadingDropdown({
                selectBoxes: [{
                        selector: '.step1',
                        selected: '<?php echo ($dbCountryId); ?>'
                    },
                    {
                        selector: '.step2',
                        selected: '<?php echo ($dbStateId); ?>',
                        requires: ['.step1'],
                        requireAll: true,
                        source: function(request, response) {
                            $.getJSON('<?php echo ($dynamicOrgUrl); ?>/ajax/getStates.html', request, function(data) {
                                response($.map(data, function(item, index) {
                                    return {
                                        label: item['StateName'],
                                        value: item['StateId']
                                    };
                                }));
                            });
                        }
                    }
                ]
            });

            studentChangeHospitalSite();

        });

        $(document).on('click', '.scheduleActive', function() {
            studentChangeHospitalSite();
        });

        function studentChangeHospitalSite() {
            var radioBtnVal = $('input[name="scheduleActive"]:checked').val();
            // console.log('radioBtnVal' + radioBtnVal);
            if (radioBtnVal == 1) {
                $('.studentChangeHospitalSite').show();
            } else {
                $('.studentChangeHospitalSite').hide();
            }
        }

        $('#cboCountry').on('change', function() {

            var countryId = $(this).val();
            // console.log(countryId);
            var schoolId = '<?php echo $currentSchoolId; ?>';

            $.ajax({
                type: "POST",
                url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_get_country_code.html",
                data: {
                    countryId: countryId,
                    schoolId: schoolId
                },
                success: function(responseData) {
                    var countryCode = '+' + responseData;
                    $('#txtCountryCode').val(countryCode);
                }
            });

        });
    </script>

    <script>
        // Initialize the image cropper when the page loads
        $(document).ready(function() {
            new ImageCropper();
            
            // Check if default school image exists and display it
            const defaultImagePath = '<?php echo $defaultSchoolImagePath; ?>';
            if (defaultImagePath && defaultImagePath !== '') {
                const $uploadArea = $('#uploadArea');
                const $uploadContent = $('.upload-content');
                
                // Add cropped state class to indicate we have an image
                $uploadArea.addClass('cropped-state');
                
                // Update the content with the existing image
                $uploadContent.html(`
                    <div style="display: flex; flex-direction: column; align-items: center; gap: 10px;">
                        <img src="${defaultImagePath}?randId=<?php echo time(); ?>" 
                             style="max-width: 150px; max-height: 150px; border-radius: 8px; border: 2px solid #667eea; object-fit: contain; box-shadow: 0 4px 8px rgba(0,0,0,0.1);" 
                             alt="School Logo Preview">
                        <div style="text-align: center;">
                            <p style="color: green; font-weight: bold; margin: 5px 0; font-size: 14px;">✓ Current school logo</p>
                            <span class="file-types" style="color: #666; font-size: 12px;">Click to select a different image if needed</span>
                        </div>
                    </div>
                `);
            }
        });
    </script>

</body>

</html>
