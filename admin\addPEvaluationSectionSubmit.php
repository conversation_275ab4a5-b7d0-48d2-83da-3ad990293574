<?php 
	include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
    include('../class/clsSchool.php');      	
    include('../class/clsPEvaluation.php');       
	include('../setRequest.php'); 
	
		//print_r($_POST);
	if($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit']))
	{
		
		$sectionId = isset($_GET['editid']) ? DecodeQueryData($_GET['editid']) : 0;
		$status = ($sectionId > 0) ? 'updated' : 'added';
		$title = $_POST['txtsection'];
		$sortOrder = $_POST['txtsortorder'];

		//Save data
		$objPevaluation = new clsPEvaluation();
		$objPevaluation->schoolId = $currentSchoolId;
		$objPevaluation->title = $title;
		$objPevaluation->sortOrder = $sortOrder;	
		
		if($isCurrentSchoolSuperAdmin == 1) // For superadmin		
			$retcheckoffsectionmasterId = $objPevaluation->SaveDefaultPevaluationSection($sectionId);
		else
			$retcheckoffsectionmasterId = $objPevaluation->SavePevaluationSection($sectionId);
		unset($objCheckoffSection);

		if($retcheckoffsectionmasterId > 0){
			//Audit Log Start
			// Instantiate the Logger class
			$objLog = new clsLogger();

			// Determine the action type (EDIT or ADD) based on the presence of a journal ID
			$action = ($sectionId > 0) ? $objLog::EDIT : $objLog::ADD;
			$type ="section";
			$isSuperAdmin = ($isCurrentSchoolSuperAdmin == 1) ? 1 : 0;
			$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN; // User type is set to Admins
    
            $objPevaluation = new clsPEvaluation();
			$objPevaluation->savePEvalAuditLog(0,$retcheckoffsectionmasterId, $_SESSION["loggedUserId"], $userType, $action, 0,$type,$isSuperAdmin);
			unset($objPevaluation);

			unset($objLog);
			//Audit Log End
			header('location:PEvaluationSectionList.html?status='.$status);
		}
		else {

			header('location:PEvaluationSectionList.html?status=error');
		}
		
	}
	else
	{
		header('location:siteEvaluationSectionList.html?status='.$status);
		exit();
	}

?>