<?php
/**
 * AccountApiTest
 * PHP version 5
 *
 * @category Class
 * @package  ClickSend
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * ClickSend v3 API
 *
 * This is an official SDK for [ClickSend](https://clicksend.com)  Below you will find a current list of the available methods for clicksend.  *NOTE: You will need to create a free account to use the API. You can register [here](https://dashboard.clicksend.com/#/signup/step1/)..*
 *
 * OpenAPI spec version: 3.1
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * ClickSend Codegen version: 3.0.51
 */
/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Please update the test case below to test the endpoint.
 */

namespace ClickSend;

use ClickSend\Configuration;
use ClickSend\ApiException;
use <PERSON>lickSend\ObjectSerializer;
use PHPUnit\Framework\TestCase;

/**
 * AccountApiTest Class Doc Comment
 *
 * @category Class
 * @package  ClickSend
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */
class AccountApiTest extends TestCase
{

    /**
     * Setup before running any test cases
     */
    public static function setUpBeforeClass(): void
    {
    }

    /**
     * Setup before running each test case
     */
    public function setUp(): void
    {
    }

    /**
     * Clean up after running each test case
     */
    public function tearDown(): void
    {
    }

    /**
     * Clean up after running all test cases
     */
    public static function tearDownAfterClass(): void
    {
    }

    /**
     * Test case for accountGet
     *
     * Get account information.
     *
     */
    public function testAccountGet()
    {
    }

    /**
     * Test case for accountPost
     *
     * Create a new account.
     *
     */
    public function testAccountPost()
    {
    }

    /**
     * Test case for accountUseageBySubaccountGet
     *
     * Get account useage by subaccount.
     *
     */
    public function testAccountUseageBySubaccountGet()
    {
    }

    /**
     * Test case for accountVerifySendPut
     *
     * Send account activation token.
     *
     */
    public function testAccountVerifySendPut()
    {
    }

    /**
     * Test case for accountVerifyVerifyByActivationTokenPut
     *
     * Verify new account.
     *
     */
    public function testAccountVerifyVerifyByActivationTokenPut()
    {
    }

    /**
     * Test case for forgotPasswordPut
     *
     * Forgot password.
     *
     */
    public function testForgotPasswordPut()
    {
    }

    /**
     * Test case for forgotPasswordVerifyPut
     *
     * Verify forgot password.
     *
     */
    public function testForgotPasswordVerifyPut()
    {
    }

    /**
     * Test case for forgotUsernamePut
     *
     * Forgot username.
     *
     */
    public function testForgotUsernamePut()
    {
    }
}
