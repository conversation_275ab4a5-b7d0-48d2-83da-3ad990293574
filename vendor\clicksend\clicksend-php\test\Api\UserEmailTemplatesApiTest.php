<?php
/**
 * UserEmailTemplatesApiTest
 * PHP version 5
 *
 * @category Class
 * @package  ClickSend
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * ClickSend v3 API
 *
 * This is an official SDK for [ClickSend](https://clicksend.com)  Below you will find a current list of the available methods for clicksend.  *NOTE: You will need to create a free account to use the API. You can register [here](https://dashboard.clicksend.com/#/signup/step1/)..*
 *
 * OpenAPI spec version: 3.1
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * ClickSend Codegen version: 3.0.51
 */
/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Please update the test case below to test the endpoint.
 */

namespace ClickSend;

use ClickSend\Configuration;
use <PERSON>lick<PERSON>end\ApiException;
use <PERSON>lickSend\ObjectSerializer;
use PHPUnit\Framework\TestCase;

/**
 * UserEmailTemplatesApiTest Class Doc Comment
 *
 * @category Class
 * @package  ClickSend
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */
class UserEmailTemplatesApiTest extends TestCase
{

    /**
     * Setup before running any test cases
     */
    public static function setUpBeforeClass(): void
    {
    }

    /**
     * Setup before running each test case
     */
    public function setUp(): void
    {
    }

    /**
     * Clean up after running each test case
     */
    public function tearDown(): void
    {
    }

    /**
     * Clean up after running all test cases
     */
    public static function tearDownAfterClass(): void
    {
    }

    /**
     * Test case for emailTemplateDelete
     *
     * Delete user email template.
     *
     */
    public function testEmailTemplateDelete()
    {
    }

    /**
     * Test case for emailTemplateGet
     *
     * Get specific user email template.
     *
     */
    public function testEmailTemplateGet()
    {
    }

    /**
     * Test case for emailTemplatePost
     *
     * Create email template.
     *
     */
    public function testEmailTemplatePost()
    {
    }

    /**
     * Test case for emailTemplatePut
     *
     * Update email template.
     *
     */
    public function testEmailTemplatePut()
    {
    }

    /**
     * Test case for emailTemplatesGet
     *
     * Get all user email templates.
     *
     */
    public function testEmailTemplatesGet()
    {
    }
}
