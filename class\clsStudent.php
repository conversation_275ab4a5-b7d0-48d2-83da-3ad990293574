<?php
class clsStudent
{
	var $studentId = '';
	var $schoolId = '';
	var $profilePic = '';
	var $firstName = '';
	var $middleName = '';
	var $lastName = '';
	var $address1 = '';
	var $address2 = '';
	var $city = '';
	var $stateId = '';
	var $zip = '';
	var $phone = '';
	var $cellPhone = '';
	var $email = '';
	var $dob = '';
	var $username = '';
	var $passwordHash = '';
	var $createdBy = '';
	var $creadtedDate = '';
	var $updatedBy = '';
	var $updatedDate = '';
	var $locationId = '';
	var $rankId = '';
	var $isActive = '';




	function SaveStudent($studentId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($studentId > 0) {
			$sql = "UPDATE student SET schoolId = '" . ($this->schoolId) . "', 
										locationId = '" . ($this->locationId) . "', 
										recordIdNumber = '" . ($this->recordIdNumber) . "', 
										rankId = '" . ($this->rankId) . "', 
										profilePic = '" . ($this->profilePic) . "', 
										firstName = '" . addslashes($this->firstName) . "', 
										middleName = '" . addslashes($this->middleName) . "', 
										lastName = '" . addslashes($this->lastName) . "',
										address1 = '" . addslashes($this->address1) . "',
										address2 = '" . addslashes($this->address2) . "',										
										city = '" . addslashes($this->city) . "',
										stateId = '" . addslashes($this->stateId) . "',
										zip = '" . addslashes($this->zip) . "',
										phone = '" . addslashes($this->phone) . "',
										cellPhone = '" . addslashes($this->cellPhone) . "',
										email = '" . addslashes($this->email) . "',
										username = '" . addslashes($this->username) . "',
										updatedBy = '" . addslashes($this->createdBy) . "',										 
										isEmailPassword = '" . addslashes($this->isEmailPassword) . "',										 
										updatedDate = '" . (date("Y-m-d h:i:s")) . "'										
										WHERE studentId= " . $studentId;

			// echo 'Update=> ' . $sql;exit;
			$objDB->ExecuteQuery($sql);
		} else {
			$sql = "INSERT INTO student (schoolId,recordIdNumber, locationId,rankId,profilePic, firstName, middleName, lastName,
										 address1, address2, city,  stateId, zip, phone, cellPhone, 
										 email, username, passwordHash, isActive,
										 createdBy, createdDate,isEmailPassword) 
								VALUES  ('" . ($this->schoolId) . "',
											'" . ($this->recordIdNumber) . "',
											'" . ($this->locationId) . "',
											'" . ($this->rankId) . "',
											'" . ($this->profilePic) . "',
											'" . addslashes($this->firstName) . "',
											'" . addslashes($this->middleName) . "',
											'" . addslashes($this->lastName) . "',
											'" . addslashes($this->address1) . "',
											'" . addslashes($this->address2) . "',											
											'" . addslashes($this->city) . "',
											'" . addslashes($this->stateId) . "',
											'" . ($this->zip) . "',
											'" . ($this->phone) . "',
											'" . ($this->cellPhone) . "',
											'" . addslashes($this->email) . "',
											'" . addslashes($this->username) . "',
											'" . ($this->passwordHash) . "',
											'" . ($this->isActive) . "',
											'" . ($this->createdBy) . "',
											'" . (date("Y-m-d h:i:s")) . "',
											'" . ($this->isEmailPassword) . "'
											)";
			// echo 'Insert=> '. $sql;
			// exit;
			$studentId = $objDB->ExecuteInsertQuery($sql);
		}

		unset($objDB);
		return $studentId;
	}


	function UpdateStudentProfile($studentId = 0)
	{
		$objDB = new clsDB();
		$sql = '';


		if ($studentId > 0) {

			$sql = "UPDATE student SET 
							recordIdNumber = '" . ($this->recordIdNumber) . "',
							firstName = '" . addslashes($this->firstName) . "', 
							lastName = '" . addslashes($this->lastName) . "',
							email = '" . addslashes($this->email) . "',
							username = '" . addslashes($this->username) . "',	
							address1 = '" . addslashes($this->address1) . "',
							address2 = '" . addslashes($this->address2) . "',										
							city = '" . addslashes($this->city) . "',
							stateId = '" . addslashes($this->stateId) . "',
							zip = '" . addslashes($this->zip) . "',									
							updatedBy = '" . addslashes($this->updatedBy) . "',
							updatedDate = '" . addslashes($this->updatedDate) . "'
							Where studentId= " . $studentId;
			$objDB->ExecuteQuery($sql);
		}
		unset($objDB);
		return $studentId;
	}


	function DeleteStudent($studentId, $schoolId)
	{
		$result = "";
		if ($studentId > 0) {
			$schoolDir = ROOT_PATH . "/upload/schools/" . $schoolId . "/student/" . $studentId;
			delete_directory($schoolDir);
			$objDB = new clsDB();
			$sql = "DELETE FROM student WHERE studentId = " . $studentId;
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
		return $result;
	}

	function GetAllStudents($schoolId)
	{
		$objDB = new clsDB();
		$rows = "";

		$sql = "SELECT student.*, location.title as location , rankmaster.title as rank ,
				rankmaster.rankId,rankmaster.sordOrder FROM student 
				INNER JOIN location ON student.locationId = location.locationId
				INNER JOIN rankmaster ON student.rankId = rankmaster.rankId	
		        WHERE student.schoolId=" . $schoolId . "  GROUP BY studentId
				 ORDER BY firstName";
		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllStudentsByRotation($schoolId, $rotationId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT student.studentId,CONCAT(student.firstName,' ',student.lastName) as studentname FROM student 
				LEFT JOIN rotationdetails ON rotationdetails.studentId=student.studentId
				LEFT JOIN rotation ON rotation.rotationId = rotationdetails.rotationId
				WHERE student.schoolId=" . $schoolId;

		if ($rotationId > 0) {
			$sql .= "  AND rotation.rotationId=" . $rotationId;
		}

		$sql .= " Group by CONCAT(student.firstName,' ',student.lastName) ORDER BY student.firstName ";
		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}


	function GetAllStudentsByloggedTypeRotation($schoolId, $rotationId, $ClinicianId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT student.studentId,CONCAT(student.firstName,' ',student.lastName) as studentname FROM student 
				LEFT JOIN rotationdetails ON rotationdetails.studentId=student.studentId
				INNER JOIN rotation ON rotation.rotationId = rotationdetails.rotationId
				INNER JOIN rankmaster ON student.rankId = rankmaster.rankId
				LEFT JOIN clinicianhospitalsite ON clinicianhospitalsite.hospitalSiteId=rotation.hospitalSiteId
					
				WHERE student.schoolId=" . $schoolId . " AND (rankmaster.title !='Graduate' AND rankmaster.title NOT like '%Graduate%') and rankmaster.title !='Dropout' ";

		if ($rotationId > 0) {
			$sql .= "  AND rotation.rotationId=" . $rotationId;
		}

		if ($ClinicianId > 0) {
			$sql .= "  AND clinicianhospitalsite.clinicianId=" . $ClinicianId;
		}

		$sql .= " Group by CONCAT(student.firstName,' ',student.lastName) ORDER BY student.firstName ";
		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}



	function GetAllSchoolStudents($schoolId, $rankId = 0, $studentId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT student.*, location.title as location , rankmaster.title as rank ,
				rankmaster.rankId,rankmaster.sordOrder ,attendance.orignalhours,attendance.approvedhours,
				sum(attendance.approvedhours)  
						 AS ApprovedTotalHours,
				sum(attendance.orignalhours + attendance.approvedhours) AS TotalHours
				FROM student
				INNER JOIN location ON student.locationId = location.locationId
				INNER JOIN rankmaster ON student.rankId = rankmaster.rankId
				LEFT JOIN attendance ON student.studentId=attendance.studentId";

		if ($schoolId > 0 && $rankId == 0 && $studentId == 0) {
			$sql .= " WHERE student.schoolId=" . $schoolId . " and (rankmaster.title !='Graduate' and rankmaster.title NOT like '%Graduate%' ) and rankmaster.title !='Dropout' ";
		} else {
			$sql .= " WHERE student.schoolId=" . $schoolId;
		}
		if ($studentId > 0) {
			$sql .= " AND student.studentId=" . $studentId;
		}
		$sql .= " AND student.isActive = 1";
		if ($rankId > 0) {
			$sql .= " AND student.rankId=" . $rankId;
		}

		$sql .= " GROUP BY student.studentId";
		$sql .= " ORDER BY student.firstName asc";
		// echo $sql;exit;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllSchoolStudentList($schoolId, $rankId = 0, $studentId = 0, $fromDate = '', $toDate = '', $isCanvasUser = 0, $studentIds = '')
	{
		$studentIds = $studentIds ? implode(",", unserialize($studentIds)) : '';

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT student.*, location.title as location , rankmaster.title as rank ,
				rankmaster.rankId,rankmaster.sordOrder ,attendance.orignalhours,attendance.approvedhours,
				sum(attendance.approvedhours)  
						 AS ApprovedTotalHours,
				sum(attendance.orignalhours + attendance.approvedhours) AS TotalHours
				FROM student
				LEFT JOIN location ON student.locationId = location.locationId
				LEFT JOIN rankmaster ON student.rankId = rankmaster.rankId
				LEFT JOIN attendance ON student.studentId=attendance.studentId";
		if ($schoolId > 0 && $rankId == 0 && $studentId == 0)
			$sql .= " WHERE student.schoolId=" . $schoolId . " and (rankmaster.title !='Graduate' AND rankmaster.title !='Dropout' AND rankmaster.title NOT like '%Graduate%') and rankmaster.title !='Drop Out' ";
		else
			$sql .= " WHERE student.schoolId=" . $schoolId;

		if ($studentId > 0)
			$sql .= " AND student.studentId=" . $studentId;

		if ($rankId > 0)
			$sql .= " AND student.rankId=" . $rankId;

		if ($isCanvasUser)
			$sql .= " AND student.canvasUserId = 0";

		if ($studentIds != '')
			$sql .= " AND student.studentId IN ($studentIds) ";

		if ($fromDate != '' && $toDate != '')
			$sql .= " AND date(student.createdDate) >= '" . date("Y-m-d", strtotime($fromDate)) . "' AND date(student.createdDate) <= '" . date("Y-m-d", strtotime($toDate)) . "'";

		$sql .= " AND student.isActive=1 GROUP BY student.studentId";
		$sql .= " ORDER BY student.firstName asc";
		// echo $sql;exit;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllSchoolStudentsForAssignCoarc($schoolId, $rankId, $filterYear, $type)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT student.*, location.title as location , rankmaster.title as rank , accreditation.actualGraduationDate,
				rankmaster.rankId,rankmaster.sordOrder ,attendance.orignalhours,attendance.approvedhours,
				sum(attendance.approvedhours)  
						 AS ApprovedTotalHours,
				sum(attendance.orignalhours + attendance.approvedhours) AS TotalHours
				FROM student
				INNER JOIN location ON student.locationId = location.locationId
				INNER JOIN rankmaster ON student.rankId = rankmaster.rankId
				LEFT JOIN attendance ON student.studentId=attendance.studentId
				LEFT JOIN accreditation ON student.studentId=accreditation.studentId";
		$sql .= " WHERE student.schoolId=" . $schoolId;

		if ($filterYear > 0 && $type == 3) {
			$sql .= " AND EXTRACT(YEAR FROM accreditation.actualGraduationDate)=" . $filterYear;
			$sql .= " AND rankmaster.title = 'Graduate' ";
		}
		if ($type == 3 && $filterYear == '') {
			$sql .= " AND rankmaster.title = 'Graduate' ";
		}
		if ($type == 2) {
			$sql .= " AND rankmaster.title != 'Graduate' ";
		}
		if ($rankId > 0) {
			$sql .= " AND student.rankId=" . $rankId;
		}

		$sql .= " GROUP BY student.studentId";
		$sql .= " ORDER BY student.firstName asc";
		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllSchoolStudentsForClinical($schoolId, $rankId = 0, $studentId = 0, $start, $length, $searchExpression, $orderByColumn, $orderByColumnDir)
	{

		$totalFiltered = 0;
		if ($orderByColumn == '') {
			$orderByColumn = 'student.firstName';
			$orderByColumnDir = 'asc';
		}

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT student.*, location.title as location , rankmaster.title as rank ,
				rankmaster.rankId,rankmaster.sordOrder
				FROM student
				RIGHT JOIN location ON student.locationId = location.locationId
				INNER JOIN rankmaster ON student.rankId = rankmaster.rankId				
				";
		if ($schoolId > 0 && $rankId == 0 && $studentId == 0) {
			$sql .= " WHERE student.schoolId=" . $schoolId . " and (rankmaster.title !='Graduate' AND rankmaster.title NOT like '%Graduate%' )  and rankmaster.title !='Dropout' ";
		} else {
			$sql .= " WHERE student.schoolId=" . $schoolId;
		}

		if ($studentId > 0)
			$sql .= " AND student.studentId=" . $studentId;

		if ($rankId > 0)
			$sql .= " AND student.rankId=" . $rankId;


		if ($searchExpression != '') {
			$sql .= " AND ( student.firstName LIKE '%" . $searchExpression . "%' ";
			$sql .= " OR student.lastName LIKE '%" . $searchExpression . "%' ";

			$sql .= " OR rankmaster.title LIKE '%" . $searchExpression . "%' )";
		}

		$sql .= " GROUP BY student.studentId";
		$query = $objDB->GetResultset($sql);
		if ($query)
			$totalFiltered = mysqli_num_rows($query); // when there is a search parameter then we have to modify total number filtered rows as per search result. 

		if ($orderByColumn == '')
			$sql .= " ORDER BY student.firstName asc";
		else
			$sql .= " ORDER BY " . $orderByColumn . " " . $orderByColumnDir;

		if ($length > 0)
			$sql .= " LIMIT " . $start . " ," . $length . " ";

		// echo $sql;exit;	
		$query = $objDB->GetResultset($sql);

		unset($objDB);
		return $query;
	}

	//done by yogita
	function GetSchoolStudentsForIncident($schoolId, $rotationId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT student.*, location.title as location , rankmaster.title as rank ,rankmaster.rankId
		,rankmaster.sordOrder FROM student
		LEFT JOIN location ON student.locationId = location.locationId
		LEFT JOIN rankmaster ON student.rankId = rankmaster.rankId
	     WHERE student.schoolId=" . $schoolId . " AND student.rotationId=" . $rotationId;

		//ECHO $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetSchoolStudents($schoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT student.*, location.title as location , rankmaster.title as rank ,rankmaster.rankId
		,rankmaster.sordOrder FROM student 
		INNER JOIN location ON student.locationId = location.locationId
		INNER JOIN rankmaster ON student.rankId = rankmaster.rankId";
		$sql .= " WHERE student.schoolId=" . $schoolId . " ORDER BY student.firstName ASC";


		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}



	function GetAllSchoolStudentsForAssignToROtation($schoolId, $rankId, $locationId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT student.*, rankmaster.title as rank,
				hospitalsites.title as hospitalsite FROM student
				LEFT JOIN rotationdetails ON student.studentId = rotationdetails.studentId
				LEFT JOIN rotation ON rotationdetails.rotationId = rotation.rotationId
				LEFT JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
				LEFT JOIN rankmaster ON student.rankId = rankmaster.rankId ";


		if ($schoolId > 0 && $rankId == 0) {
			$sql .= " WHERE student.schoolId=" . $schoolId . " AND rankmaster.title NOT LIKE 'Graduate%' AND rankmaster.title !='Drop Out' AND rankmaster.title !='Dropout'";
		} else {
			$sql .= " WHERE student.schoolId=" . $schoolId;
		}

		$sql .= " AND student.isActive= 1";

		if ($locationId) {
			$sql .= " AND student.locationId=" . $locationId;
		}

		if ($rankId > 0) {
			$sql .= " AND student.rankId=" . $rankId;
		}

		$sql .= " GROUP BY student.studentId";
		$sql .= " ORDER BY `student`.`firstName` ASC";

		// echo $sql; 
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllSchoolStudentsForCoarc($schoolId, $surveyId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT student.studentId,student.firstName,student.lastName,
				student.rankId, location.title as location , rankmaster.title as rank , coarcsurveydetails.coarcSurveyMasterId, coarcsurveymaster.title as surveyTitle, coarcsurveymaster.isDelivery
				FROM student
				INNER JOIN location ON student.locationId = location.locationId
				INNER JOIN rankmaster ON student.rankId = rankmaster.rankId
				INNER JOIN coarcsurveydetails ON student.studentId=coarcsurveydetails.studentId
				INNER JOIN coarcsurveymaster ON coarcsurveydetails.coarcSurveyMasterId=coarcsurveymaster.coarcSurveyMasterId
				INNER JOIN coarcsurveytype ON coarcsurveymaster.coarctype=coarcsurveytype.coarcSurveyTypeId
				
				WHERE student.schoolId=" . $schoolId . " AND coarcsurveymaster.coarctype='1'";

		if ($surveyId > 0) {
			$sql .= " AND coarcsurveydetails.coarcSurveyMasterId=" . $surveyId;
		}

		$sql .= "  ORDER BY `student`.`firstName` ASC";
		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllSchoolStudentsForCoarcList($schoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT coarcsurveydetails.coarcSurveyMasterId, coarcsurveymaster.title as surveyTitle
				FROM student
				INNER JOIN location ON student.locationId = location.locationId
				INNER JOIN rankmaster ON student.rankId = rankmaster.rankId
				INNER JOIN coarcsurveydetails ON student.studentId=coarcsurveydetails.studentId
				INNER JOIN coarcsurveymaster ON coarcsurveydetails.coarcSurveyMasterId=coarcsurveymaster.coarcSurveyMasterId
				INNER JOIN coarcsurveytype ON coarcsurveymaster.coarctype=coarcsurveytype.coarcSurveyTypeId
				
				WHERE student.schoolId=" . $schoolId . " AND coarcsurveymaster.coarctype='1'";
		$sql .= "Group By coarcsurveydetails.coarcSurveyMasterId order by coarcsurveymaster.title";
		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllSchoolStudentsForGraduateCoarc($schoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT student.studentId,student.firstName,student.lastName,
				student.rankId,student.PromotionDate, location.title as location , rankmaster.title as rank ,
				IFNULL(graduatecoarcrequestmaster.status, 0) as status, graduatecoarcrequestmaster.graduatecoarcId
				FROM student
				INNER JOIN location ON student.locationId = location.locationId
				INNER JOIN rankmaster ON student.rankId = rankmaster.rankId
				INNER JOIN coarcsurveydetails ON student.studentId=coarcsurveydetails.studentId
				INNER JOIN coarcsurveymaster ON coarcsurveydetails.coarcSurveyMasterId=coarcsurveymaster.coarcSurveyMasterId
				INNER JOIN coarcsurveytype ON coarcsurveymaster.coarctype=coarcsurveytype.coarcSurveyTypeId
				LEFT JOIN graduatecoarcrequestmaster ON student.studentId=graduatecoarcrequestmaster.studentId
				WHERE student.schoolId=" . $schoolId . "  AND coarcsurveymaster.coarctype='3'";
		$sql .= "  ORDER BY `student`.`firstName` ASC";
		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAssignStudentsForGraduateCoarc($schoolId, $surveyId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT student.studentId,student.firstName,student.lastName,
				student.rankId,student.PromotionDate, location.title as location , rankmaster.title as rank ,
				coarcsurveydetails.coarcSurveyMasterId,accreditation.actualGraduationDate,coarcsurveymaster.title as surveyTitle,coarcsurveymaster.isDelivery
				FROM student
				INNER JOIN location ON student.locationId = location.locationId
				INNER JOIN coarcsurveydetails ON coarcsurveydetails.studentId = student.studentId
				INNER JOIN coarcsurveymaster ON coarcsurveydetails.coarcSurveyMasterId=coarcsurveymaster.coarcSurveyMasterId
				INNER JOIN rankmaster ON student.rankId = rankmaster.rankId
				LEFT JOIN accreditation ON student.studentId=accreditation.studentId
				WHERE student.schoolId=" . $schoolId . " AND rankmaster.title='Graduate'";

		if ($surveyId > 0) {
			$sql .= " AND coarcsurveydetails.coarcSurveyMasterId=" . $surveyId;
		}

		$sql .= "  ORDER BY `student`.`firstName` ASC";
		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAssignStudentsForGraduateCoarcList($schoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT coarcsurveydetails.coarcSurveyMasterId,coarcsurveymaster.title as surveyTitle
				FROM student
				INNER JOIN location ON student.locationId = location.locationId
				INNER JOIN coarcsurveydetails ON coarcsurveydetails.studentId = student.studentId
				INNER JOIN coarcsurveymaster ON coarcsurveydetails.coarcSurveyMasterId=coarcsurveymaster.coarcSurveyMasterId
				INNER JOIN rankmaster ON student.rankId = rankmaster.rankId
				LEFT JOIN accreditation ON student.studentId=accreditation.studentId
				WHERE student.schoolId=" . $schoolId . " AND rankmaster.title='Graduate'";
		$sql .= "Group By coarcsurveydetails.coarcSurveyMasterId order by coarcsurveymaster.title";
		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllSchoolStudentsForEmployerCoarc($schoolId, $surveyId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT student.studentId,student.firstName,student.lastName,studentadditationlcontactinformation.businessName,studentadditationlcontactinformation.ContactFirstName,
				studentadditationlcontactinformation.ContactLastName,studentadditationlcontactinformation.emailAddress,
				student.rankId, location.title as location , rankmaster.title as rank ,
				coarcsurveydetails.coarcSurveyMasterId,coarcsurveymaster.title as surveyTitle,coarcsurveymaster.isDelivery
				FROM student
				INNER JOIN location ON student.locationId = location.locationId
				INNER JOIN rankmaster ON student.rankId = rankmaster.rankId	
				INNER JOIN studentadditationlcontactinformation ON student.studentId = studentadditationlcontactinformation.studentId	
				INNER JOIN coarcsurveydetails ON student.studentId=coarcsurveydetails.studentId
				INNER JOIN coarcsurveymaster ON coarcsurveydetails.coarcSurveyMasterId=coarcsurveymaster.coarcSurveyMasterId
				INNER JOIN coarcsurveytype ON coarcsurveymaster.coarctype=coarcsurveytype.coarcSurveyTypeId
				WHERE student.schoolId=" . $schoolId . " AND coarcsurveymaster.coarctype='4'";

		if ($surveyId > 0) {
			$sql .= " AND coarcsurveydetails.coarcSurveyMasterId=" . $surveyId;
		}

		$sql .= " GROUP BY student.studentId,studentadditationlcontactinformation.additationlcontactinformationId,coarcsurveymaster.coarcSurveyMasterId";
		// echo $sql; 
		// exit;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllSchoolStudentsForEmployerCoarcList($schoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT coarcsurveydetails.coarcSurveyMasterId,coarcsurveymaster.title as surveyTitle
				FROM student
				INNER JOIN location ON student.locationId = location.locationId
				INNER JOIN rankmaster ON student.rankId = rankmaster.rankId	
				INNER JOIN studentadditationlcontactinformation ON student.studentId = studentadditationlcontactinformation.studentId	
				INNER JOIN coarcsurveydetails ON student.studentId=coarcsurveydetails.studentId
				INNER JOIN coarcsurveymaster ON coarcsurveydetails.coarcSurveyMasterId=coarcsurveymaster.coarcSurveyMasterId
				INNER JOIN coarcsurveytype ON coarcsurveymaster.coarctype=coarcsurveytype.coarcSurveyTypeId
				WHERE student.schoolId=" . $schoolId . " AND coarcsurveymaster.coarctype='4'";
		$sql .= "Group By coarcsurveydetails.coarcSurveyMasterId order by coarcsurveymaster.title";
		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllSchoolStudentsForPersonnelCoarc($schoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT student.studentId,student.firstName,student.lastName,
				student.rankId, location.title as location , rankmaster.title as rank ,
				IFNULL(personnelcoarcrequestmaster.status, 0) as status, personnelcoarcrequestmaster.personnelCoarcId
				FROM student
				INNER JOIN location ON student.locationId = location.locationId
				INNER JOIN rankmaster ON student.rankId = rankmaster.rankId
				INNER JOIN coarcsurveydetails ON student.studentId=coarcsurveydetails.studentId
				INNER JOIN coarcsurveymaster ON coarcsurveydetails.coarcSurveyMasterId=coarcsurveymaster.coarcSurveyMasterId
				INNER JOIN coarcsurveytype ON coarcsurveymaster.coarctype=coarcsurveytype.coarcSurveyTypeId
				LEFT JOIN personnelcoarcrequestmaster ON student.studentId=personnelcoarcrequestmaster.studentId
				WHERE student.schoolId=" . $schoolId . " AND coarcsurveymaster.coarctype='2'";
		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetStudentDetails($studentId)
	{
		$objDB = new clsDB();
		$sql = "SELECT student.* , IFNULL(rankmaster.title,'') as ranktitle , student.rankId,
				schools.isActiveCheckoffForStudent AS isActiveCheckoff,
				timezonemaster.timezone
				FROM  student
				INNER JOIN schools ON student.schoolId=schools.schoolId
				INNER JOIN timezonemaster ON schools.timeZoneId=timezonemaster.timeZoneId
				LEFT JOIN rankmaster ON student.rankId = `rankmaster`.`rankId` 
				WHERE student.studentId=" . $studentId;
		// echo $sql;exit;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}

	function GetStudentTimeZoneByStudentId($studentId)
	{
		$objDB = new clsDB();
		$sql = "SELECT timezonemaster.timezone
				FROM  student
				INNER JOIN location ON student.locationId = location.locationId
				INNER JOIN timezonemaster ON location.timeZoneId=timezonemaster.timeZoneId
				WHERE student.studentId=" . $studentId;
		$row = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $row;
	}



	function SetStudentStatus($studentId, $status, $schoolId)
	{


		if ($studentId > 0) {
			$objDB = new clsDB();
			$sql = "Update student set isActive = " . $status . " Where studentId = " . $studentId;
			// echo $sql;exit;
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
	}

	function UpdateStudentpassword($currentSchoolId, $studentId, $newpassword, $isPasswordUpdated = 1)
	{
		$objDB = new clsDB();
		$sql = "Update student 
				Set passwordHash ='" . addslashes($newpassword) . "',
				isPasswordUpdated =" . $isPasswordUpdated . "
				Where studentId=" . $studentId . " AND schoolId= " . $currentSchoolId;

		//echo $sql;exit;
		$objDB->ExecuteQuery($sql);
		unset($objDB);
	}

	function IsDuplicateUserName($studentId, $username, $schoolId)
	{
		$sql = '';
		$retUserId = 0;
		$objDB = new clsDB();
		$sql = "select studentId from student where studentId != '" . $studentId .
			"' and LOWER(username) = '" . strtolower($username) . "' and schoolId=" . $schoolId;
		$retUserId = $objDB->GetSingleFieldValue($sql);
		unset($objDB);

		if ($retUserId > 0) {
			return 1;
		} else {
			return 0;
		}
	}

	function IsDuplicateEmail($email, $schoolId = 0)
	{
		$sql = '';
		$objDB = new clsDB();
		$sql = "select studentId from student where email = '" . $email . "'";

		if ($schoolId > 0) {
			$sql .= " AND student.schoolId=" . $schoolId;
		}
		$retUserId = $objDB->GetSingleFieldValue($sql);
		return $retUserId;
		unset($objDB);
	}

	function CheckLogin($schoolId, $username)
	{

		$student = 0;
		$objDB = new clsDB();
		$sql = "SELECT student.* ,timezonemaster.timezone,schools.isActiveCheckoffForStudent
				FROM student 
				INNER JOIN schools ON student.schoolId=schools.schoolId
				INNER JOIN  timezonemaster ON  timezonemaster.timeZoneId=schools.timeZoneId
				WHERE student.schoolId = " . $schoolId . " AND student.username = '" . $username . "'";
		// echo $sql;exit;
		$student = $objDB->GetDataRow($sql);

		unset($objDB);
		return $student;
	}

	function CheckLoginForEmployer($schoolId, $username)
	{

		$student = 0;
		$objDB = new clsDB();
		$sql = "SELECT employercoarcrequestmaster.* ,timezonemaster.timezone,schools.isActiveCheckoffForStudent
				FROM employercoarcrequestmaster 
				INNER JOIN schools ON employercoarcrequestmaster.schoolId=schools.schoolId
				INNER JOIN  timezonemaster ON  timezonemaster.timeZoneId=schools.timeZoneId
				WHERE employercoarcrequestmaster.schoolId = " . $schoolId . " AND employercoarcrequestmaster.username = '" . $username . "'";
		//echo $sql;exit;
		$student = $objDB->GetDataRow($sql);

		unset($objDB);
		return $student;
	}


	function CheckLoginDetail($SchoolCode, $UserName)
	{
		$row = '';
		$objDB = new clsDB();
		$sql = "SELECT student.* ,schools.schoolId,schools.code,schools.displayName,schools.isActive AS schoolIsActive,rankmaster.rankId,rankmaster.title AS ranktitle, schools.isActiveCheckoffForStudent AS isActiveCheckoff
				FROM student 
				INNER JOIN schools ON student.schoolId=schools.schoolId
				INNER JOIN rankmaster ON student.rankId=rankmaster.rankId	
				WHERE username =" . "'$UserName'" . " AND schools.code=" . $SchoolCode;
		// echo $sql;exit;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}

	function UpdateStudentPhotosFileName($studentId, $smallProfilePic, $profilePic)
	{
		if ($studentId > 0) {
			$objDB = new clsDB();
			$sql = "Update student set smallProfilePic = '" . $smallProfilePic . "',
			profilePic = '" . $profilePic . "'  Where studentId = " . $studentId;
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}

		return $studentId;
	}

	function GetStudentsCounts($schoolId)
	{
		$returnCount = 0;
		$objDB = new clsDB();
		$sql = "SELECT A.TotalCount, B.ActiveCount  FROM (SELECT COUNT(*) As TotalCount FROM `student` WHERE schoolId=" . $schoolId . ") As A,
				(SELECT COUNT(*) As ActiveCount FROM `student` WHERE isActive=1 AND schoolId=" . $schoolId . ") As B";

		//echo $sql;
		$returnCount = $objDB->GetDataRow($sql);
		unset($objDB);
		return $returnCount;
	}

	function GetStudentsCountsByClinician($currentSchoolId, $clinicianId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT distinct student.* FROM student
				INNER JOIN rotationdetails ON student.studentId = rotationdetails.studentId
				INNER JOIN rotation ON rotationdetails.rotationId=rotation.rotationId
				INNER JOIN clinicianhospitalsite ON clinicianhospitalsite.hospitalSiteId=rotation.hospitalSiteId
		 		WHERE student.schoolId=" . $currentSchoolId . "
				AND clinicianhospitalsite.clinicianId=" . $clinicianId;

		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetActiveStudentsCountsByClinician($currentSchoolId, $clinicianId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT distinct student.* FROM student
				INNER JOIN rotationdetails ON student.studentId = rotationdetails.studentId
				INNER JOIN rotation ON rotationdetails.rotationId=rotation.rotationId
				INNER JOIN clinicianhospitalsite ON clinicianhospitalsite.hospitalSiteId=rotation.hospitalSiteId";
		$sql .= " WHERE student.isActive=1 AND student.schoolId=" . $currentSchoolId;
		$sql .= " AND clinicianhospitalsite.clinicianId=" . $clinicianId;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}


	function GetClinicianStudentsByHospital($clinicianId, $rankId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT student.* , rankmaster.title as rank FROM student
				INNER JOIN rotationdetails ON student.studentId = rotationdetails.studentId
				INNER JOIN rotation ON rotationdetails.rotationId=rotation.rotationId
				INNER JOIN clinicianhospitalsite ON clinicianhospitalsite.hospitalSiteId=rotation.hospitalSiteId
				INNER JOIN rankmaster ON student.rankId = rankmaster.rankId";


		if ($clinicianId > 0 && $rankId == 0) {
			$sql .= " WHERE clinicianhospitalsite.clinicianId=" . $clinicianId . " and (rankmaster.title !='Graduate' AND rankmaster.title NOT like '%Graduate%') and rankmaster.title !='Dropout' ";
		} else {
			$sql .= " WHERE clinicianhospitalsite.clinicianId=" . $clinicianId;
		}
		$sql .= " AND student.isActive = 1";

		if ($rankId > 0) {
			$sql .= " AND student.rankId=" . $rankId;
		}
		$sql .= " GROUP BY rotationdetails.studentId";
		$sql .= " ORDER BY firstName";
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetStudentsByPreceptorOrCoordinatorClinicians($clinicianId, $rankId = 0, $start, $length, $searchExpression, $orderByColumn, $orderByColumnDir)
	{
		$totalFiltered = 0;

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT student.* , rankmaster.title as rank FROM student
				INNER JOIN rotationdetails ON student.studentId = rotationdetails.studentId
				INNER JOIN rotation ON rotationdetails.rotationId=rotation.rotationId
				INNER JOIN clinicianhospitalsite ON clinicianhospitalsite.hospitalSiteId=rotation.hospitalSiteId
				INNER JOIN rankmaster ON student.rankId = rankmaster.rankId";


		if ($clinicianId > 0 && $rankId == 0) {
			$sql .= " WHERE clinicianhospitalsite.clinicianId=" . $clinicianId . " and (rankmaster.title !='Graduate' and rankmaster.title NOT like '%Graduate%' )  and rankmaster.title !='Dropout' ";
		} else {
			$sql .= " WHERE clinicianhospitalsite.clinicianId=" . $clinicianId;
		}
		if ($rankId > 0) {
			$sql .= " AND student.rankId=" . $rankId;
		}

		//To Search
		if ($searchExpression != '') {
			$sql .= " AND ( student.firstName LIKE '%" . $searchExpression . "%' ";
			$sql .= " OR student.lastName LIKE '%" . $searchExpression . "%' ";

			$sql .= " OR rankmaster.title LIKE '%" . $searchExpression . "%' )";
		}
		$sql .= " GROUP BY rotationdetails.studentId";

		$query = $objDB->GetResultset($sql);
		if ($query)
			$totalFiltered = mysqli_num_rows($query); // when there is a search parameter then we have to modify total number filtered rows as per search result. 

		if ($orderByColumn == 0)
			$sql .= " ORDER BY student.firstName asc";
		else
			$sql .= " ORDER BY " . $orderByColumn . " " . $orderByColumnDir;

		if ($length > 0)
			$sql .= " LIMIT " . $start . " ," . $length . " ";

		$query = $objDB->GetResultset($sql);

		unset($objDB);
		return $query;
	}

	// Clinical API functions

	function GetStudentsByPreceptorOrCoordinatorCliniciansForAPI1($clinicianId, $schoolId, $rankId = 0, $limitString = '', $searchText = "")
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT rotation.title, rotation.`rotationId`, student.`studentId`, student.`firstName`,student.lastName,
		SEC_TO_TIME( SUM( TIME_TO_SEC(orignalhours) ) ) AS AttendanceTotalHours,
					SEC_TO_TIME( SUM( TIME_TO_SEC(approvedhours) ) ) AS ApprovedTotalHours,rankmaster.title as rank

		 FROM attendance 
		 LEFT JOIN rotation ON attendance.`rotationId` = rotation.`rotationId` 
		 LEFT JOIN student ON attendance.studentId = student.`studentId`          
		 LEFT JOIN schools ON  attendance.`schoolId`=schools.`schoolId`
		 INNER JOIN rankmaster ON student.rankId = rankmaster.rankId
		 INNER JOIN clinicianhospitalsite ON rotation.hospitalSiteId=clinicianhospitalsite.hospitalSiteId
	     INNER JOIN clinician ON clinicianhospitalsite.clinicianId=clinician.clinicianId AND clinician.schoolId=" . $schoolId;

		if ($clinicianId > 0 && $rankId == 0) {
			$sql .= " WHERE clinicianhospitalsite.clinicianId=" . $clinicianId . " and (rankmaster.title !='Graduate' and rankmaster.title NOT like '%Graduate%' )  and rankmaster.title !='Dropout' ";
		} else {
			$sql .= " WHERE clinicianhospitalsite.clinicianId=" . $clinicianId;
		}
		if ($rankId > 0) {
			$sql .= " AND student.rankId=" . $rankId;
		}


		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetStudentsByPreceptorOrCoordinatorCliniciansForAPI($clinicianId = 0, $schoolId, $rankId = 0, $limitString = '', $searchText = "")
	{
		$totalFiltered = 0;

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT student.*, location.title as location , rankmaster.title as rank , rankmaster.rankId,rankmaster.sordOrder,rotation.rotationId,rotation.title 
		FROM student 
		LEFT JOIN rotationdetails ON student.studentId = rotationdetails.studentId
		INNER JOIN rotation ON rotationdetails.rotationId=rotation.rotationId
		LEFT JOIN clinicianhospitalsite ON clinicianhospitalsite.hospitalSiteId=rotation.hospitalSiteId
		INNER JOIN rankmaster ON student.rankId = rankmaster.rankId 
		RIGHT JOIN location ON student.locationId = location.locationId
		WHERE student.schoolId=$schoolId and (rankmaster.title !='Graduate' AND rankmaster.title NOT like '%Graduate%' ) and rankmaster.title !='Dropout'";

		if ($clinicianId > 0) {
			$sql .= " AND clinicianhospitalsite.clinicianId=" . $clinicianId . " ";
		}

		if ($rankId > 0) {
			$sql .= " AND student.rankId=" . $rankId;
		}

		//To Search
		if ($searchText != '') {
			$sql .= " AND (CONCAT(student.firstName,' ',student.lastName )LIKE'%" . $searchText . "%' OR rankmaster.title LIKE '%" . $searchText . "%' )";

			// $sql .= " OR rankmaster.title LIKE '%" . $searchText . "%' )";
		}
		$sql .= " GROUP BY student.studentId ORDER BY student.firstName asc" . $limitString;
		// echo $sql;exit;
		$query = $objDB->GetResultset($sql);
		unset($objDB);
		return $query;
	}



	function GetClinicianStudents($schoolId, $rankId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT student.* , rankmaster.title as rank
				FROM student
				INNER JOIN rankmaster ON student.rankId = rankmaster.rankId";

		if ($schoolId > 0 && $rankId == 0) {
			$sql .= " WHERE student.schoolId=" . $schoolId . " and (rankmaster.title !='Graduate' AND rankmaster.title NOT like '%Graduate%') and rankmaster.title !='Dropout' ";
		} else {
			$sql .= " WHERE student.schoolId=" . $schoolId;
		}

		$sql .= " AND student.isActive = 1";

		if ($rankId > 0) {
			$sql .= " AND student.rankId=" . $rankId;
		}


		$sql .= " GROUP BY student.studentId order by student.firstName, student.lastName ASC";
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function SaveFailedLogin($studentId, $currentSchoolId)
	{
		$lastLoginFailedTime = date('Y-m-d H:i:s');
		//Save Failed login to DB 		
		//------------------------------------------------------------ 	
		$objDB = new clsDB();
		$sql = "Insert Into studentloginlogs (studentId)
	Values('" . $studentId . "')";
		$objDB->ExecuteInsertQuery($sql);  		//Make Auto Lock Records 
		$sql = "Select COUNT(*) From studentloginlogs Where DATE_ADD(LastFailedLoginTime, INTERVAL 15 MINUTE)
	>= NOW() and studentId=" . $studentId;

		$totalFailedCount = $objDB->GetSingleFieldValue($sql);
		if ($totalFailedCount > 4) //lockout after 5 failed login attempts within 15 minutes 
		{ 			//Make Blocked 			
			$sql = "Update student SET isPasswordUpdated = 1, isBlocked=1 
	Where studentId = " . $studentId;
			$objDB->ExecuteQuery($sql);
			//Clear All Records 			
			$this->ClearFailedLogin($studentId);  			//Notify Email to Student and DCE  - For Reset the password 	
			$objSendEmails = new clsSendEmails($currentSchoolId);
			$objSendEmails->NotifyToStudentAndDCEStudentLockedOut($studentId);
			unset($objSendEmails);
		}
		unset($objDB);
	}

	function ClearFailedLogin($studentId)
	{
		$objDB = new clsDB();
		$sql = "Delete from studentloginlogs Where studentId = " . $studentId;
		$objDB->ExecuteQuery($sql);
		unset($objDB);
	}

	function SetStudentBlockUnblock($studentId, $block, $schoolId)
	{
		if ($studentId > 0) {
			$objDB = new clsDB();
			$sql = "Update student set isBlocked = " . $block . " Where studentId = " . $studentId;
			//echo 	$sql;exit;
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
			return $result;
		}
	}

	function GetStudentAttendanceDetails($rotationId, $studentId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT  rotation.`title`, student.`firstName`,rotation.courseId,
		 attendance.`clockInDateTime`,attendance.`clockOutDateTime`,attendance.`clockInIp`,
		 attendance.`orignalhours`,attendance.`approvedhours`,attendance.`clockOutIp`,
		 attendance.`attendanceId`,attendance.`status`,attendance.`isException`,attendance.`comment`,
         courses.title
		 FROM attendance 
		 INNER JOIN rotation ON attendance.`rotationId` = rotation.`rotationId` 
		 INNER JOIN student ON attendance.studentId = student.`studentId`
         INNER JOIN courses ON attendance.`schoolId` = courses.`schoolId` 
		 WHERE attendance.rotationId= " . $rotationId . " AND student.`studentId`=" . $studentId .
			" group by attendance.attendanceId";
		//echo $sql;exit;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetStudentAttendanceDetailsByAttendance($rotationId, $studentId, $attendanceId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT  rotation.`title`, student.`firstName`,rotation.courseId,
		 attendance.`clockInDateTime`,attendance.`clockOutDateTime`,attendance.`clockInIp`,
		 attendance.`orignalhours`,attendance.`approvedhours`,attendance.`clockOutIp`,
		 attendance.`attendanceId`,attendance.`status`,attendance.`isException`,attendance.`comment`,
         courses.title,attendance.exceptionDate
		 FROM attendance 
		 INNER JOIN rotation ON attendance.`rotationId` = rotation.`rotationId` 
		 INNER JOIN student ON attendance.studentId = student.`studentId`
         INNER JOIN courses ON attendance.`schoolId` = courses.`schoolId` 
		 WHERE attendance.rotationId= " . $rotationId . " AND student.`studentId`=" . $studentId .
			" AND attendance.attendanceId=" . $attendanceId .
			" group by attendance.attendanceId";
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetSubRotationStudents($schoolId, $rotationId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT *, location.title as location ,rankmaster.title as rank
				FROM student
				INNER JOIN rotationdetails ON student.studentId	=rotationdetails.studentId
				INNER JOIN location ON student.locationId = location.locationId
				INNER JOIN rankmaster ON student.rankId = rankmaster.rankId
				WHERE student.schoolId=" . $schoolId . " AND rotationdetails.rotationId=" . $rotationId;

		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	function UpdateStudentImageName($studentId, $profileName, $smallprofileName)
	{
		if ($studentId > 0) {
			$objDB = new clsDB();
			$sql = "Update student set profilePic = '" . $profileName . "', smallProfilePic='" . $smallprofileName . "' Where studentId=" . $studentId;
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
	}

	function GetAllSchoolStudentsForReport($schoolId, $rotationId = 0, $studentId = '', $rankId = 0, $locationId = 0, $hospitalId = 0, $ascdesc = '', $sordorder = 0)
	{
		$studentIds = is_array($studentId) ? implode(",", $studentId) : '';
		// $studentIds = $studentId ? implode(',', $studentId) : '';
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT student.studentId,firstName,lastName, location.title as location , rankmaster.title as rank, 
					schools.title as school ,
					rotation.title as rotationname,attendance.orignalhours,
					(SELECT  count(*) as ProcedureCount
					FROM studentprocedutecount
					WHERE  student.studentId = studentprocedutecount.studentId) AS ProcedureCountTotal ,
					
					(SELECT  count(*) as EquipmentsCounts
					FROM studentequipmentmaster
					WHERE  studentequipmentmaster.studentId = student.studentId) AS EquipmentsCounts,

					(SELECT  count(*) as InteractionCount
					FROM interaction
					WHERE interaction.studentId=student.studentId ) AS InteractionCount,

					(SELECT  count(*) as  JournalCount
					FROM  journal
					WHERE journal.studentId=student.studentId ) AS JournalCount,
					rotationdetails.rotationId 
					
					FROM student 
					INNER JOIN location ON student.locationId = location.locationId 
					INNER JOIN rankmaster ON student.rankId = rankmaster.rankId 
					left JOIN attendance ON attendance.studentId=student.studentId 
					INNER JOIN schools ON student.schoolId = schools.schoolId  
					INNER JOIN rotationdetails ON student.studentId = rotationdetails.studentId
					
					INNER JOIN rotation ON rotationdetails.rotationId = rotation.rotationId";

		$sql .= " WHERE student.schoolId= " . $schoolId;
		if ($rotationId > 0)
			$sql .= " AND rotation.rotationId=" . $rotationId;
		if ($studentIds > 0)
			$sql .= " AND student.studentId IN ($studentIds)";
		if ($rankId > 0)
			$sql .= " AND rankmaster.rankId=" . $rankId;
		if ($locationId > 0)
			$sql .= " AND location.locationId=" . $locationId;
		$sql .= " GROUP BY student.studentId";

		if ($ascdesc && $sordorder == 14)
			$sql .= "  ORDER BY student.firstName " . $ascdesc;

		else if ($ascdesc && $sordorder == 15)
			$sql .= "  ORDER BY student.lastName " . $ascdesc;

		else if ($ascdesc)
			$sql .= "  ORDER BY student.firstName " . $ascdesc;

		// echo $sql;
		// exit;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllRank($schoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM rankmaster where schoolId=" . $schoolId . " and  title!='Graduate' order by sordOrder";
		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllSchoolStudentsForPromotion($schoolId, $currentRankId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT student.*,student.firstName,student.lastName,student.createdDate,rankmaster.title as rank FROM student
		INNER JOIN location ON student.locationId = location.locationId
		INNER JOIN rankmaster ON student.rankId = rankmaster.rankId
		WHERE student.schoolId=" . $schoolId;
		if ($currentRankId) {
			$sql .= " AND  student.rankId=" . $currentRankId;
		}
		$sql .= " GROUP BY student.firstName,student.lastName";
		$sql .= " ORDER BY `student`.`firstName` ASC ";
		// echo $sql;exit;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	function GetAllRankForNewRank($schoolId, $currentRankId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * from  rankmaster where schoolId=" . $schoolId . " 
	           AND `sordOrder` >= (SELECT sordOrder FROM rankmaster WHERE rankId=" . $currentRankId . ") AND rankmaster.rankId != " . $currentRankId . " order by sordOrder";
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function UpdateStudentRank($studentId)
	{
		if ($studentId > 0) {
			$objDB = new clsDB();
			$sql = "Update student set rankId = '" . addslashes($this->rankId) . "',
			PromotionDate = '" . (date("Y-m-d h:i:s")) . "'
             		Where studentId  in  (" . $studentId . ")";
			//echo $sql; exit;
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
	}
	function GetSingleStudent($schoolId, $studentId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT student.firstName ,rankmaster.title ,
				student.lastName,student.PromotionDate,schools.schoolId,schools.displayName,
				student.email,student.phone,student.recordIdNumber
				FROM student
				INNER JOIN schools ON student.schoolId=schools.schoolId
				INNER JOIN rankmaster ON rankmaster.rankId=student.rankId
				WHERE student.studentId=" . $studentId;
		//echo '<hr>'.$sql.'<hr>';exit;
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}

	function GetEmpoyerStudent($schoolId, $studentId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT studentadditationlcontactinformation.ContactFirstName,studentadditationlcontactinformation.cellPhone,
				studentadditationlcontactinformation.ContactLastName, studentadditationlcontactinformation.emailAddress
				FROM student
				INNER JOIN schools ON student.schoolId=schools.schoolId
				INNER JOIN studentadditationlcontactinformation ON studentadditationlcontactinformation.studentId=student.studentId
				WHERE student.studentId=" . $studentId;
		//echo '<hr>'.$sql.'<hr>';exit;
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}
	function GetStudentsByRotation($schoolId, $rotationId)
	{
		$objDB = new clsDB();
		$sql = "SELECT  student.studentId, student.schoolId, student.rankId, student.firstName,
				student.lastName,schools.schoolId,
				rotationdetails.studentId, rotationdetails.rotationId
				FROM  student
				INNER JOIN schools ON student.schoolId=schools.schoolId
				LEFT JOIN rotationdetails ON  student.studentId=rotationdetails.studentId";

		$sql .= " WHERE student.schoolId =" . $schoolId;
		if ($rotationId > 0) {
			$sql .= " AND rotationdetails.rotationId =" . $rotationId;
		}
		$sql .= " GROUP BY student.studentId
				ORDER BY student.firstName";
		// echo $sql;		
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}
	function GetCurrentUserPasswdStoredHash($username)
	{
		$objDB = new clsDB();
		$sql = "SELECT student.*,schools.schoolId,rankmaster.rankId,rankmaster.title AS Ranktitle
				FROM student
				INNER JOIN schools ON student.schoolId=schools.schoolId
				INNER JOIN rankmaster ON student.rankId=rankmaster.rankId				
				WHERE student.username =" . $username;
		///echo $sql;exit;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}

	function CheckLoginDetails($username)
	{
		$objDB = new clsDB();
		$sql = "SELECT student.*,schools.schoolId,rankmaster.rankId,rankmaster.title AS Ranktitle
				FROM student
				INNER JOIN schools ON student.schoolId=schools.schoolId
				INNER JOIN rankmaster ON student.rankId=rankmaster.rankId				
				WHERE student.username =" . $username;
		///echo $sql;exit;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}

	function GetUserDetail($UserId)
	{
		$objDB = new clsDB();
		$sql = "SELECT student.*,schools.schoolId,schools.displayName,schools.logoSmallName
				FROM student
				INNER JOIN schools ON student.schoolId=schools.schoolId
				WHERE student.studentId =" . $UserId;
		// echo $sql;exit;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}

	function ValidatestudentEmail($email, $schoolId)
	{
		$objDB = new clsDB();
		$sql = "SELECT student.*,schools.schoolId
				FROM student
				INNER JOIN schools ON student.schoolId=schools.schoolId
				WHERE student.email =" . "'$email' AND student.schoolId='" . $schoolId . "'";
		// echo $sql;exit;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}

	function ValidateApiStudentEmail($email, $schoolCode)
	{
		$sql = "SELECT student.*,schools.displayName,schools.logoSmallName
				FROM student
				INNER JOIN schools ON student.schoolId=schools.schoolId
				WHERE student.email =" . "'$email' AND code='" . $schoolCode . "'";

		$objDB = new clsDB();
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}

	function UpdateApi_StudentProfile($studentId = 0)
	{
		$objDB = new clsDB();
		$sql = '';

		if ($studentId > 0) {
			$sql = "UPDATE student SET 
					firstName = '" . addslashes($this->firstName) . "', 
					middleName = '" . addslashes($this->middleName) . "',
					lastName = '" . addslashes($this->lastName) . "',
					email = '" . addslashes($this->email) . "',
					updatedBy = '" . $studentId . "',
					updatedDate = '" . date("Y-m-d h:i:s") . "'
					Where studentId= " . $studentId;

			$objDB->ExecuteQuery($sql);
		}
		unset($objDB);
		return $studentId;
	}

	function DeleteSchoolStudent($schoolId)
	{
		$schoolDir = ROOT_PATH . "/upload/schools/" . $schoolId . "/student";
		delete_directory($schoolDir);
		$objDB = new clsDB();
		$sql = "DELETE FROM student WHERE schoolId  = " . $schoolId;
		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}

	function GetStudentTimezone($studentId)
	{
		//Get User Time Zone
		$sql = "SELECT timezonemaster.timezone
				FROM student
				INNER JOIN schools ON student.schoolId=schools.schoolId
				INNER JOIN  timezonemaster ON  timezonemaster.timeZoneId=schools.timeZoneId
				WHERE student.studentId = " . $studentId;

		$objDB = new clsDB();
		$timezone = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		$timezone = $timezone ? $timezone : SERVER_TIMEZONE;
		return $timezone;
	}

	function savestudentDocumet()
	{	
		$objDB = new clsDB();
		$sql = '';
		$sql = "INSERT INTO studentdocuments (uploadedDate,StudentId,studentImmunizationId,fileTitle,fileName,uploadedBy,documetype,recordid) 
				VALUES  ('" . (date("Y-m-d H:i:s")) . "',
					'" . ($this->StudentId) . "',
					'" . ($this->studentImmunizationId) . "',
					'" . addslashes($this->fileTitle) . "',
					'" . addslashes($this->fileName) . "',
					'" . ($this->uploadedBy) . "',
					'" . addslashes($this->documetype) . "',
					'" . ($this->recordid) . "'
					)";
		// echo $sql;exit;
		$studentDocumentId = $objDB->ExecuteInsertQuery($sql);
		unset($objDB);
		return $studentDocumentId;
	}

	function studentImmulizationDocumentDetails($studentId, $Type, $studentImmunizationId = 0)
	{
		$objDB = new clsDB();
		$sql = '';
		$sql = "select studentdocuments.* from studentdocuments
		INNER JOIN studentimulizations ON studentdocuments.studentImmunizationId = studentimulizations.studentImmunizationId
		where studentdocuments.StudentId = " . $studentId;

		$sql .= " AND documetype = '" . $Type . "'";

		$sql .= " AND studentdocuments.studentImmunizationId =" . $studentImmunizationId;

		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	function studentDocumentDetails($studentId, $Type, $isNotImmulizationFile = 0)
	{
		$objDB = new clsDB();
		$sql = '';
		$sql = "select studentdocuments.* from studentdocuments
		where studentdocuments.StudentId = " . $studentId;

		if ($Type)
			$sql .= " AND documetype = '" . $Type . "'";

		if ($isNotImmulizationFile)
			$sql .= " AND documetype != 'I'";
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}


	function singleDocument($studentDocumentId)
	{
		$sql = '';
		$objDB = new clsDB();
		$sql = "select * from studentdocuments where studentDocumentId = " . $studentDocumentId;

		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}

	function getStudentFile($studentId, $Type, $fileTitle)
	{
		$sql = '';
		$objDB = new clsDB();
		$sql = "select * from studentdocuments where StudentId = " . $studentId . "
		 AND documetype = '" . $Type . "' AND fileTitle ='" . $fileTitle . "'";
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}

	function getStudentDocumentForApp($studentId, $Type, $fileTitle)
	{
		$sql = '';
		$objDB = new clsDB();
		$sql = "SELECT *  FROM `studentdocuments` WHERE `StudentId` = " . $studentId . " and fileName = '" . $fileTitle . "' ORDER BY `documetype` ASC";
		// echo $sql; exit;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}

	function DeleteDocuments($studentDocumentId)
	{
		$sql = '';
		$objDB = new clsDB();
		$sql = "delete from studentdocuments where studentDocumentId = " . $studentDocumentId;

		$row = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $row;
	}
	function getDocumentsCount($studentId, $Type = '', $studentImmunizationId = 0, $isNotImmulizationFile = '')
	{
		$sql = '';
		$objDB = new clsDB();
		$sql = "select count(*) as countDocument from studentdocuments where StudentId = " . $studentId;

		if ($Type)
			$sql .= " AND documetype ='" . $Type . "'";

		if ($studentImmunizationId)
			$sql .= " AND studentImmunizationId =" . $studentImmunizationId;

		if ($isNotImmulizationFile)
			$sql .= " AND documetype != 'I'";

		$row = $objDB->GetDataRow($sql);

		unset($objDB);
		return $row;
	}

	function GetStudentDetailForSuperAdmin($searchForFilter, $schoolIds = '')
	{
		$sql = '';
		$objDB = new clsDB();
		$sql = " SELECT student.*,rankmaster.title,schools.title as schoolName,schools.slug FROM student 
		inner join rankmaster on rankmaster.rankId = student.rankId
		inner join schools on schools.schoolId = student.schoolId
		WHERE (student.firstName like '%$searchForFilter%' 
		OR student.lastName like '%$searchForFilter%' 
		OR student.email like '%$searchForFilter%' 
		OR student.username like '%$searchForFilter%' 
		OR CONCAT(student.firstName,' ',student.lastName) like '%$searchForFilter%' 
		OR student.phone like '%$searchForFilter%' )";

		if ($schoolIds != '')
			$sql .= " AND schools.schoolId IN ( $schoolIds ) ";

		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}

	function GetStudentDetailForAdmin($schoolId, $searchForFilter, $ranks, $fromDate, $toDate)
	{
		$sql = '';
		$objDB = new clsDB();
		$sql = " SELECT student.email as email FROM student 
		inner join rankmaster on rankmaster.rankId = student.rankId
		inner join schools on schools.schoolId = student.schoolId";
		$sql .= " where student.schoolId =" . $schoolId;

		if ($searchForFilter) {
			$sql .= " AND (student.firstName like '%$searchForFilter%' 
			OR student.lastName like '%$searchForFilter%' 
			OR CONCAT(student.firstName,' ',student.lastName) like '%$searchForFilter%') ";
		} elseif ($ranks) {
			$sql .= " AND rankmaster.rankId IN (" . $ranks . ")";
		}
		if ($fromDate != '1969-12-31' && $fromDate != '1969-12-31' &&  $toDate != '' &&  $toDate != '') {
			$sql .= " AND (CAST(student.createdDate AS Date) BETWEEN '" . $fromDate . "' AND '" . $toDate . "')";
		}
		// 		echo $sql;exit;
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}

	function StudentCount($schoolId, $created_at)
	{
		$sql = '';
		$objDB = new clsDB();
		$sql = "select count(*) as StudentCount from notificationsettings INNER join student ON student.studentId = notificationsettings.referenceId where notificationsettings.schoolId = " . $schoolId . " AND created_at  = '" . $created_at . "' group by notificationsettings.schoolId ";
		// echo $sql;
		$row = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $row;
	}

	function GetDetail($schoolId, $created_at)
	{
		$row = "";
		$objDB = new clsDB();
		$sql = "SELECT student.* FROM  notificationsettings  
		inner join  student on notificationsettings.referenceId = student.studentId
		WHERE notificationsettings.created_at  = '" . $created_at . "' AND notificationsettings.schoolId  = " . $schoolId . " ORDER BY notificationsettings.referenceId DESC";
		// echo $sql;
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}

	function GetArrayStudent($schoolId, $studentId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * from student
				WHERE studentId=" . $studentId . " AND schoolId=" . $schoolId . " ";
		//echo '<hr>'.$sql.'<hr>';exit;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	function GetActiveStudent()
	{
		$row = '';
		$objDB = new clsDB();
		$sql = "SELECT CONCAT(student.firstName,' ',student.lastName) as Fullname ,createdDate,rankId,email,phone,cellPhone,schoolId  FROM `student` WHERE isActive=1 ORDER BY createdDate DESC";
		// echo $sql;exit;
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}
	function GetActiveStudentRegisterYear()
	{
		$row = '';
		$objDB = new clsDB();
		$sql = "SELECT CONCAT(student.firstName,' ',student.lastName) as Fullname ,createdDate,rankId,email,phone,cellPhone,schoolId  FROM `student` WHERE isActive=1 GROUP by STR_TO_DATE(createdDate , '%Y' ) ORDER by createdDate desc LIMIT 5";
		// echo $sql;exit;
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}
	function GetActivestudentYearCount($year)
	{
		$row = '';
		$objDB = new clsDB();
		$sql = "SELECT count(*) from `student` WHERE isActive=1 AND YEAR ( createdDate ) =  '" . $year . "' ORDER BY `studentId` ASC";
		// echo $sql;exit;
		$row = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $row;
	}
	function GetSingleStudentForPeerToPeer($schoolId, $studentId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT CONCAT(student.firstName,' ',student.lastName) as studentname
				FROM student
				INNER JOIN schools ON student.schoolId=schools.schoolId
				WHERE student.studentId=" . $studentId;
		//echo '<hr>'.$sql.'<hr>';
		$rows = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $rows;
	}

	function GetDocumentUploadedStudentList($schoolId, $rankId = 0)
	{
		$sql = '';
		$objDB = new clsDB();
		$sql = " SELECT student.*,rankmaster.title FROM student 
		inner join rankmaster on rankmaster.rankId = student.rankId
		inner join studentdocuments on studentdocuments.StudentId = student.studentId
		WHERE student.schoolId =" . $schoolId . " AND documetype='S'";

		if ($rankId > 0)
			$sql .= " AND rankmaster.rankId =" . $rankId;

		$sql .= " Group By studentdocuments.StudentId";

		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}

	function GetStudentDetailsByEmail($searchForFilter)
	{
		$sql = '';
		$objDB = new clsDB();
		$sql = " SELECT student.*,rankmaster.title,schools.title as schoolName,schools.slug FROM student 
		inner join rankmaster on rankmaster.rankId = student.rankId
		inner join schools on schools.schoolId = student.schoolId
		WHERE student.email like '%$searchForFilter%'";
		// echo $sql;
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}
	function GetAllSchoolStudentProgramDateList($schoolId, $rankId = 0, $studentId = 0, $fromDate = '', $toDate = '')
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT student.*, accreditation.programEnrollmentDate,ontimeGreduationDate,actualGraduationDate,
		accreditation.crtCompletionDate,accreditation.wrrtCompletionDate,accreditation.programDropDate, rankmaster.title as rank ,
				rankmaster.rankId,rankmaster.sordOrder ,attendance.orignalhours,attendance.approvedhours,
				sum(attendance.approvedhours)  
						 AS ApprovedTotalHours,
				sum(attendance.orignalhours + attendance.approvedhours) AS TotalHours
				FROM student
				LEFT JOIN accreditation ON student.studentId = accreditation.studentId 
				LEFT JOIN rankmaster ON student.rankId = rankmaster.rankId
				LEFT JOIN attendance ON student.studentId=attendance.studentId";

		if ($schoolId > 0 && $rankId == 0 && $studentId == 0)
			$sql .= " WHERE student.schoolId=" . $schoolId . " and (rankmaster.title !='Graduate' AND rankmaster.title !='Dropout' AND rankmaster.title NOT like '%Graduate%') and rankmaster.title !='Drop Out' ";
		else
			$sql .= " WHERE student.schoolId=" . $schoolId;

		if ($studentId > 0)
			$sql .= " AND student.studentId=" . $studentId;

		if ($rankId > 0)
			$sql .= " AND student.rankId=" . $rankId;

		if ($fromDate != '' && $toDate != '')
			$sql .= " AND date(student.createdDate) >= '" . date("Y-m-d", strtotime($fromDate)) . "' AND date(student.createdDate) <= '" . date("Y-m-d", strtotime($toDate)) . "'";

		$sql .= " AND student.isActive=1 GROUP BY student.studentId";
		$sql .= " ORDER BY student.firstName asc";
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function getStudentByEmail($email)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "select canvasUserId, canvasAcessToken from student where email =" . $email;
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}
	function GetStudentsMail($schoolIds)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT `email` FROM `student` ";
		if ($schoolIds > 0)
			$sql .= " WHERE `schoolId` IN ($schoolIds)";
		// ECHO $sql;EXIT;
		$rows = $objDB->GetResultset($sql);

		unset($objDB);
		//print_r($rows);exit;
		return $rows;
	}

	// function findStudentIdINalltables() 
	// {

	// 	$objDB = new clsDB();
	// 	$rows = "";

	// 	$sql = "SELECT DISTINCT TABLE_NAME 
	// 			FROM INFORMATION_SCHEMA.COLUMNS
	// 			WHERE COLUMN_NAME IN('studentId')
	// 			AND TABLE_SCHEMA = 'clinical_trac' ";

	// 	// echo $sql;exit;
	// 	$rows = $objDB -> GetResultset($sql);
	// 	// print_r($rows);
	// 	while ($row = mysqli_fetch_assoc($rows)) {
	// 		// echo $results = $row['TABLE_NAME'];

	// 		$sql2 = "select * from "
	// 			.$row['TABLE_NAME']
	// 			." where studentId = 2403";

	// 		$rows2 = $objDB -> GetResultset($sql2);
	// 		if ($rows2 -> num_rows > 0 && $row['TABLE_NAME'] != 'student') {
	// 			echo $sql2;
	// 			// echo '<br>'; echo  $row['TABLE_NAME'].'<br>'; print_r($rows2)	; echo '<pre>';
	// 			while ($row2 = mysqli_fetch_array($rows2)) {
	// 				// print_r($row2)	; $firstName = stripslashes($row2['firstName']);
	// 			}
	// 		}

	// 	}

	// 	unset($objDB);
	// 	return $rows;
	// }

	function UpdateApi_StudentProfileForApp($studentId = 0)
	{
		$objDB = new clsDB();
		$sql = '';

		if ($studentId > 0) {
			$sql = "UPDATE student SET 
					firstName = '" . addslashes($this->firstName) . "', 
					lastName = '" . addslashes($this->lastName) . "',
					email = '" . addslashes($this->email) . "',
					username = '" . addslashes($this->username) . "',
					address1 = '" . addslashes($this->address1) . "',
					address2 = '" . addslashes($this->address2) . "',
					stateId = '" . addslashes($this->stateId) . "',
					city = '" . addslashes($this->city) . "',
					zip = '" . addslashes($this->zip) . "',
					updatedBy = '" . $studentId . "',
					updatedDate = '" . date("Y-m-d h:i:s") . "'
					Where studentId= " . $studentId;
			// echo $sql;exit;
			$objDB->ExecuteQuery($sql);
		}
		unset($objDB);
		return $studentId;
	}

	function checkStudentPhoneExist($phone, $schoolId = 0, $studentId = 0)
	{
		$sql = '';
		$objDB = new clsDB();
		$sql = "select studentId from student where REPLACE(phone, '-', '') = '" . $phone . "'";
		if ($schoolId)
			$sql .= " AND schoolId = " . $schoolId;
		if ($studentId)
			$sql .= " AND studentId != " . $studentId;
		// echo $sql;
		$retUserId = $objDB->GetSingleFieldValue($sql);
		return $retUserId;
		unset($objDB);
	}

	function GetClinicianStudentsForApp($schoolId, $rankId = 0, $rotationId = 0, $limitString = '', $searchText = '')
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT student.* , rankmaster.title as rank, rotationdetails.rotationId  
				FROM student				
				INNER JOIN rankmaster ON student.rankId = rankmaster.rankId
				LEFT JOIN rotationdetails ON rotationdetails.studentId = student.studentId";

		if ($schoolId > 0 && $rankId == 0) {
			$sql .= " WHERE student.schoolId=" . $schoolId . " and (rankmaster.title !='Graduate' AND rankmaster.title NOT like '%Graduate%') and rankmaster.title !='Dropout' ";
		} else {
			$sql .= " WHERE student.schoolId=" . $schoolId;
		}

		if ($rankId > 0) {
			$sql .= " AND student.rankId=" . $rankId;
		}

		if ($rotationId > 0) {
			$sql .= " AND rotationdetails.rotationId=" . $rotationId;
		}

		$sql .= " AND student.isActive= 1";
		if ($searchText != "") {
			$sql .= " AND ( CONCAT(student.firstName, ' ', student.lastName) LIKE '%" . $searchText . "%' OR student.email LIKE '%" . $searchText . "%' OR rankmaster.title LIKE '%" . $searchText . "%')";
		}
		$sql .= " GROUP BY student.studentId order by student.firstName, student.lastName ASC" . $limitString;
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetClinicianStudentsByHospitalForApp($clinicianId, $rankId = 0,  $rotationId = 0, $limitString = '', $searchText = '')
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT student.* , rankmaster.title as rank,  rotationdetails.rotationId   FROM student
				INNER JOIN rotationdetails ON student.studentId = rotationdetails.studentId
				INNER JOIN rotation ON rotationdetails.rotationId=rotation.rotationId
				INNER JOIN clinicianhospitalsite ON clinicianhospitalsite.hospitalSiteId=rotation.hospitalSiteId
				INNER JOIN rankmaster ON student.rankId = rankmaster.rankId";


		if ($clinicianId > 0 && $rankId == 0) {
			$sql .= " WHERE clinicianhospitalsite.clinicianId=" . $clinicianId . " and (rankmaster.title !='Graduate' AND rankmaster.title NOT like '%Graduate%') and rankmaster.title !='Dropout' ";
		} else {
			$sql .= " WHERE clinicianhospitalsite.clinicianId=" . $clinicianId;
		}
		if ($rankId > 0) {
			$sql .= " AND student.rankId=" . $rankId;
		}
		if ($rotationId > 0) {
			$sql .= " AND rotationdetails.rotationId=" . $rotationId;
		}
		if ($searchText != "") {
			$sql .= " AND ( CONCAT(student.firstName, ' ', student.lastName) LIKE '%" . $searchText . "%' OR student.email LIKE '%" . $searchText . "%') ";
		}
		$sql .= " GROUP BY rotationdetails.studentId";
		$sql .= " ORDER BY firstName" . $limitString;
		// echo $sql;

		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetStudentDetailForSuperAdminBySchoolIds($searchForFilter, $schoolIds = '')
	{
		$sql = '';
		$objDB = new clsDB();
		$sql = " SELECT student.*,rankmaster.title,schools.title as schoolName,schools.slug FROM student 
		inner join rankmaster on rankmaster.rankId = student.rankId
		inner join schools on schools.schoolId = student.schoolId
		WHERE (student.firstName like '%$searchForFilter%' 
		OR student.lastName like '%$searchForFilter%' 
		OR student.email like '%$searchForFilter%' 
		OR student.username like '%$searchForFilter%' 
		OR CONCAT(student.firstName,' ',student.lastName) like '%$searchForFilter%' 
		OR student.phone like '%$searchForFilter%' )";

		// if ($schoolIds != '')
		$sql .= " AND schools.schoolId IN ( $schoolIds ) ";

		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}

	function GetStudentNameById($studentId)
	{
		$sql = '';
		$objDB = new clsDB();
		$sql = " SELECT CONCAT(student.firstName,' ',student.lastName) FROM student 
					WHERE student.studentId = " . $studentId;

		$row = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $row;
	}

	function UpdateisBlockedAndOTPFlag($studentId = 0, $schoolId = 0)
	{
		$objDB = new clsDB();
		$sql = '';

		if ($studentId > 0) {
			$sql = "UPDATE student SET isBlocked = 0, otp = 0
					Where studentId= " . $studentId . " AND schoolId = " . $schoolId;
		}
		// echo $sql; exit;
		$objDB->ExecuteQuery($sql);
		unset($objDB);
		return $studentId;
	}

	function SaveSelfUnlockStudentDetails($studentId)
	{
		$objDB = new clsDB();
		$sql = '';

		$sql = "INSERT INTO selfunlock (studentId, selfunlockdate, type, role) 
								VALUES  ('" . $studentId . "',
											'" . date("Y-m-d h:i:s") . "',
											'Self Unlock',
											'Student'
											)";
		// echo $sql; exit;
		$studentId = $objDB->ExecuteInsertQuery($sql);


		unset($objDB);
		return $studentId;
	}

	/**
	 * Retrieves CI evaluation details for a given CI evaluation ID and school ID.
	 *
	 * @param int $studentId The ID of the CI evaluation.
	 * @param int $schoolId The ID of the school. If not provided, all schools will be considered.
	 *
	 * @return array An array containing the log data, row data, and additional data.
	 */
	function GetAllStudentDetailsForLogs($studentId, $schoolId = 0)
	{
		$objDB = new clsDB();

		$sql = "SELECT student.*,CONCAT(student.firstName, ' ', student.lastName) AS userName,schools.displayName as schoolName, location.title as location , rankmaster.title as rank ,rankmaster.rankId
		,rankmaster.sordOrder FROM student
		INNER JOIN location ON student.locationId = location.locationId
		INNER JOIN rankmaster ON student.rankId = rankmaster.rankId 
		INNER JOIN schools ON schools.schoolId = student.schoolId
		WHERE student.studentId =" . $studentId;
		
		if ($schoolId) {
			$sql .= " AND student.schoolId=" . $schoolId;
		}
		// echo $sql;exit;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}


	
	/**
	 * Retrieves CI evaluation details for a given CI evaluation ID and school ID.
	 *
	 * @param int $studentDocumentId The ID of the CI evaluation.
	 * @param int $schoolId The ID of the school. If not provided, all schools will be considered.
	 *
	 * @return array An array containing the log data, row data, and additional data.
	 */
	function GetAllStudentDocumentDetailsForLogs($studentDocumentId, $schoolId = 0)
	{
		$objDB = new clsDB();
		$sql = "SELECT studentdocuments.*,CONCAT(student.firstName, ' ', student.lastName) AS userName,schools.displayName as schoolName,schools.displayName as schoolName,student.schoolId from studentdocuments
				INNER JOIN student ON student.studentId = studentdocuments.studentId
				INNER JOIN schools ON schools.schoolId = student.schoolId
		WHERE studentdocuments.studentDocumentId = " . $studentDocumentId;

		// echo $sql;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;

	}

	/**
	 * This function creates a log entry for a CI evaluation action.
	 *
	 * @param int $id The ID of the CI evaluation.
	 * @param string $action The action performed (Add, Edit, Delete, Signoff).
	 * @param int $userId The ID of the user performing the action.
	 * @param string $userType The type of user performing the action (Student, Preceptor, Admin).
	 *
	 * @return array An array containing the log data, row data, and additional data.
	 */
	function createStudentLog($id, $action, $userId, $userType,$type,$studentCnt,$backUserId)
	{
			// echo "type ".$type;exit;
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$objStudent = new clsStudent();		// Assuming `Attendance` class is used for `prepareAttendanceLogData`

		// Get user details based on user type
		$userDetails = getUserDetails($userId, $userType);

		if($type == 'Document'){

			$rowData = $objStudent->GetAllStudentDocumentDetailsForLogs($id);
		}
		else{
			
			// For Login AS User Details
			if($backUserId > 0){
				include('clsSystemUser.php');
				$objSystemUser = new clsSystemUser();
				$backUserData = $objSystemUser->GetSchoolAdminDetailsForLogs($backUserId);
			}
			
			$rowData = $objStudent->GetAllStudentDetailsForLogs($id);
		}

		// echo "<pre>";
		// print_r($userDetails);exit;

		if ($rowData != '')
			$logData = $objLog->generateLogData($rowData, $userType);

		$additionalData = '';

		// Populate log data with user details
		$logData['userId'] = $userDetails['userId'];
		$logData['userName'] = isset($backUserData) ? $backUserData['userName'] : $userDetails['userName'];
		$logMessage = '';


		if($type == 'Document'){
			if ($action == 'Add' && $userType == 'Student') {
				$logMessage = $logData['userName'] . ' uploaded Document.';
			}
			 else if ($action == 'Add') {
				$logMessage = $logData['userName'] . ' uploaded Documents for ' . $rowData['userName'] . ' Student';
			} else if ($action == 'Delete') {
				$logMessage = $logData['userName'] . ' deleted Documents of ' . $rowData['userName'] . ' Student.';
			} 

		} else if($type == 'Import'){
			if ($action == 'Add') {
				$logMessage = $logData['userName'] . ' imported '.$studentCnt .' new Students';
			}
		} else if($type == 'studentId'){
			if ($action == 'Edit') {
				$logMessage = $logData['userName'] . ' Updated Student Id';
			}
		} else if($type == 'studentId Import'){
			if ($action == 'Edit') {
				$msg = ($studentCnt >0) ? " Imported Student Id's of " . $studentCnt . " Students" : " Imported Student Id's";
				$logMessage = $logData['userName'] .$msg;
			}
		} else{



			if ($action == 'Add') {
				$logMessage = $logData['userName'] . ' added the new Student';
			} else if ($action == 'Edit') {
				$msg = ($userType == 'Admin') ? $logData['userName'] . ' updated the ' . $rowData['userName'] . ' Student Profile details.' : $logData['userName'] . ' updated his Profile.';
				$logMessage = $msg;
			}  else if ($action == 'Delete') {
				$logMessage = $logData['userName'] . ' deleted the ' . $rowData['userName'] . ' Student .';
			} else if ($action == 'Active') {
				$logMessage = $logData['userName'] . ' activate the ' . $rowData['userName'] . ' Student .';
			} else if ($action == 'Inactive') {
				$logMessage = $logData['userName'] . ' deactivate the ' . $rowData['userName'] . ' Student .';
			} else if ($action == 'Lock') {
				$logMessage = $logData['userName'] . ' Locked the ' . $rowData['userName'] . ' Student .';
			} else if ($action == 'Unlocked') {
				$logMessage = $logData['userName'] . ' Unlocked the ' . $rowData['userName'] . ' Student .';
			} else if ($action == 'ChangePassword') {
				$logMessage =  $logData['userName'] .' Changed Password.';
			} else if ($action == 'LogIn') {
				$logMessage = $logData['userName'] . ' logged in.';
			} else if ($action == 'Login As') {
				$logMessage = $logData['userName'] . ' logged in as ' . $rowData['userName'] . ' Student.';
			} else if ($action == 'LogOut' && $backUserId > 0) {
				$logMessage = $backUserData['userName'] . ' logged out As ' . $rowData['userName'];
			} else if ($action == 'LogOut') {
				$logMessage =  $logData['userName'] .' Logged Out.';
			}

		}


		

		// Construct log message
		$logData['message'] = $logMessage;

		// Return the data
		return [$logData, $rowData, $additionalData];
	}

	/**
	 * Saves audit log for CI evaluation actions
	 *
	 * @param int $id ID of the CI evaluation master record
	 * @param int $userId User ID of the user performing the action
	 * @param string $userType Type of user (e.g. student, clinician, etc.)
	 * @param string $action Action type (Add, Edit, Delete, Signoff)
	 * @param int $isMobile Indicates if the action was performed from a mobile device (0 = false, 1 = true)
	 *
	 * @return bool Returns true if successful
	 */
	function saveStudentAuditLog($id, $userId, $userType, $action, $isMobile = 0,$SchoolId =0,$type ='',$studentCnt = 0,$backUserId =0)
	{
		// echo "type ".$type;exit;
		// Instantiate the Logger and Checkoff classes
		$objLog = new clsLogger();
		$objStudent = new clsStudent();	

		// Prepare log data
		[$logData, $rowData, $additionalData] = $objStudent->createStudentLog($id, $action, $userId, $userType,$type,$studentCnt,$backUserId);
		$logData['isMobile'] = $isMobile;

		if ($action == 'Delete') {

			$additionalData = '';
			if($type == 'Document')
				$objStudent->DeleteDocuments($id);	
			else
				$objStudent->DeleteStudent($id,$SchoolId);
			
		}
		// $type =($action == 'LogOut') ? 'Log Out' : $type;
		// Save the log details
		$objLog->saveLogs($logData, $action, $id, 'Student '.$type, $rowData, $additionalData);

		// Clean up
		unset($objLog);
		unset($objCIevaluation);

		return true; // Return success or handle further actions as needed
	}
	
}
