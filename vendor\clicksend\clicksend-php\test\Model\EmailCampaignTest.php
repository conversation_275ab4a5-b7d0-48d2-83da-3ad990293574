<?php
/**
 * EmailCampaignTest
 *
 * PHP version 5
 *
 * @category Class
 * @package  ClickSend
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * ClickSend v3 API
 *
 * This is an official SDK for [ClickSend](https://clicksend.com)  Below you will find a current list of the available methods for clicksend.  *NOTE: You will need to create a free account to use the API. You can register [here](https://dashboard.clicksend.com/#/signup/step1/)..*
 *
 * OpenAPI spec version: 3.1
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * ClickSend Codegen version: 3.0.51
 */
/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Please update the test case below to test the model.
 */

namespace ClickSend;

use PHPUnit\Framework\TestCase;

/**
 * EmailCampaignTest Class Doc Comment
 *
 * @category    Class
 * @description Campaign Model for Email
 * @package     ClickSend
 * <AUTHOR> Codegen team
 * @link        https://github.com/swagger-api/swagger-codegen
 */
class EmailCampaignTest extends TestCase
{

    /**
     * Setup before running any test case
     */
    public static function setUpBeforeClass(): void
    {
    }

    /**
     * Setup before running each test case
     */
    public function setUp(): void
    {
    }

    /**
     * Clean up after running each test case
     */
    public function tearDown(): void
    {
    }

    /**
     * Clean up after running all test cases
     */
    public static function tearDownAfterClass(): void
    {
    }

    /**
     * Test "EmailCampaign"
     */
    public function testEmailCampaign()
    {
    }

    /**
     * Test attribute "name"
     */
    public function testPropertyName()
    {
    }

    /**
     * Test attribute "subject"
     */
    public function testPropertySubject()
    {
    }

    /**
     * Test attribute "body"
     */
    public function testPropertyBody()
    {
    }

    /**
     * Test attribute "from_email_address_id"
     */
    public function testPropertyFromEmailAddressId()
    {
    }

    /**
     * Test attribute "from_name"
     */
    public function testPropertyFromName()
    {
    }

    /**
     * Test attribute "template_id"
     */
    public function testPropertyTemplateId()
    {
    }

    /**
     * Test attribute "list_id"
     */
    public function testPropertyListId()
    {
    }

    /**
     * Test attribute "schedule"
     */
    public function testPropertySchedule()
    {
    }
}
