<?php
$IsMobile = isset($_GET['IsMobile']) ? ($_GET['IsMobile']) : 0;

include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsExternalPreceptors.php');

// echo '<pre>';
// print_r($_GET);
// exit;
//For External Preceptor
$preceptorNum = isset($_GET['preceptorNum']) ? DecodeQueryData($_GET['preceptorNum']) : '';
$preceptorId = isset($_GET['preceptorId']) ? DecodeQueryData($_GET['preceptorId']) : 0;

$externalPreceptorFirstName = '';
$externalPreceptorId = 0;
$standardPreceptors = '';
if ($preceptorNum) {
    $objExternalPreceptors = new clsExternalPreceptors();
    $isPreceptorExist = $objExternalPreceptors->GetExternalPreceptorDetail($preceptorId, $preceptorNum);
    $externalPreceptorFirstName = isset($isPreceptorExist['firstName']) ? $isPreceptorExist['firstName'] : '';
    $externalPreceptorLastName = isset($isPreceptorExist['lastName']) ? $isPreceptorExist['lastName'] : '';
    $externalPreceptorEmail = isset($isPreceptorExist['email']) ? $isPreceptorExist['email'] : '';
    $externalPreceptorId = isset($isPreceptorExist['id']) ? $isPreceptorExist['id'] : '';
    $standardPreceptors = serialize($preceptorNum);
}


if (!$preceptorNum)
    include('includes/validateUserLogin.php');

include('../class/clsRotation.php');
include('../class/clsLocations.php');
include('../class/clsStudent.php');
include('../class/clsFormative.php');
include('../setRequest.php');
include('../class/clsQuestionOption.php');
include('../class/clsSectionStudentName.php');
include('../class/clsClinician.php');
include('../class/clsFloorTherapyAndICUEvaluation.php');
$objDB = new clsDB();
$getTimezoneId = $objDB->GetSingleColumnValueFromTable('schools', 'timeZoneId', 'schoolId', $currentSchoolId);
$getTimezone = $objDB->GetSingleColumnValueFromTable('timezonemaster', 'timezone', 'timeZoneId', $getTimezoneId);
unset($objDB);

$isEvalType = isset($_GET['isEvalType']) ? DecodeQueryData($_GET['isEvalType']) : '';
if($isEvalType == 1){
    $activebtn = 'icu';
}else{
    $activebtn = 'floor';
}
$TimeZone = isset($_SESSION["loggedClinicianSchoolTimeZone"]) ? $_SESSION["loggedClinicianSchoolTimeZone"] : $getTimezone;

$schoolId = 0;
$rotationId = 0;
$studentId = 0;
$currentstudentId = 0;
$studentMasterId = 0;
$rotationId = 0;
$dateOfStudentSignature = '';
$evaluationDate = '';
$view = '';
$display_to_date = date('m/d/Y');
$schoolId = $currentSchoolId;
$DBRotationId = 0;
$objRotation = new clsRotation();
$objEvaluation = new clsFloorTherapyAndICUEvaluation();
$isDisableStudentDate = '';

//For Student	
$currentstudentId = isset($_GET['studentId']) ? DecodeQueryData($_GET['studentId']) : 0;
$rotationId = isset($_GET['rotationId']) ? DecodeQueryData($_GET['rotationId']) : 0;

$clinicianId = isset($_SESSION['loggedClinicianId']) ? $_SESSION['loggedClinicianId'] : 0;

$DBRotationId = DecodeQueryData($_GET['rotationId']);

if (isset($_GET['view'])) {
    $view = $_GET['view'];
}

//For Edit Formative
if (isset($_GET['studentMasterId']) && ($_GET['rotationId'])) {

    $schoolId = $currentSchoolId;

    if ($isEvalType == 0)
        $page_title = "Floor Therapy ";
    
    elseif ($isEvalType == 1)
        $page_title = "Edit ICU Evaluation ";
    else
        $page_title = "Edit Floor Evaluation ";

    $bedCrumTitle = 'Edit';

    //For Formative Details
    $objEvaluation = new clsFloorTherapyAndICUEvaluation();
    $studentMasterId = DecodeQueryData($_GET['studentMasterId']);
    $rowFormative = $objEvaluation->GetStudentPEFDetails($studentMasterId);
    $objDB = new clsDB();
    $isPreceptorCompletedStatus = $objDB->GetSingleColumnValueFromTable('studentfloortherapyandicuevalmaster', 'isPreceptorCompletedStatus', 'studentMasterId', $studentMasterId);
    $retPreceptorId = $objDB->GetSingleColumnValueFromTable('studentfloortherapyandicuevalmaster', 'preceptorId', 'studentMasterId', $studentMasterId);
    unset($objDB);
    if ($isPreceptorCompletedStatus) {
        header('location:thankyou.html?isFloorTherapyEval=1');
        exit;
    }

    if ($preceptorId > 0 && $retPreceptorId !== $preceptorId) {
        header('location:thankyou.html?isFloorTherapyEval=Expired');
        exit;
    }
    unset($objEvaluation);
    // if($rowFormative=='')
    // {
    //     header('location:floorTherapyAndICUEvaluationlist.html');
    //     exit;
    // }

    $DBRotationId = isset($rowFormative['rotationId']) ? $rowFormative['rotationId'] : DecodeQueryData($_GET['rotationId']);
    $clinicianId = isset($rowFormative['clinicianId']) ? $rowFormative['clinicianId'] : 0;
    $evaluationDate = isset($rowFormative['evaluationDate']) ? $rowFormative['evaluationDate'] : '';
    $courselocationId = isset($rowFormative['locationId']) ? $rowFormative['locationId'] : 0;
    $parentRotationId = isset($rowFormative['parentRotationId']) ? $rowFormative['parentRotationId'] : 0;
    $rotationLocationId = isset($rowFormative['rotationLocationId']) ? $rowFormative['rotationLocationId'] : 0;
    $studentComment = isset($rowFormative['studentComment']) ? strip_tags($rowFormative['studentComment']) : '';

    $locationId = 0;
    if ($rotationLocationId != $courselocationId && $parentRotationId > 0) {
        if ($parentRotationId > 0) {
            if (!$rotationLocationId)
                $locationId = $objRotation->GetLocationByRotation($DBRotationId);
            else
                $locationId  = $rotationLocationId;
        }
    } else {
        $locationId  = $courselocationId;
    }

    //Get Time Zone By Rotation 
    $objLocation = new clsLocations();
    $TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
    unset($objLocation);
    if ($TimeZone == '')
        $TimeZone = isset($_SESSION["loggedUserSchoolTimeZone"]) ? $_SESSION["loggedUserSchoolTimeZone"] : $getTimezone;
    $evaluationDate = converFromServerTimeZone($evaluationDate, $TimeZone);
    $evaluationDate = (date('m/d/Y', strtotime($evaluationDate)));
    $studentId = isset($rowFormative['studentId']) ? $rowFormative['studentId'] : 0;
    $studentSignature = isset($rowFormative['studentsigniture']) ? $rowFormative['studentsigniture'] : '';
    $dateOfStudentSignature = isset($rowFormative['dateOfStudentSignature']) ? $rowFormative['dateOfStudentSignature'] : '';
    if ($dateOfStudentSignature != '' && $dateOfStudentSignature != '0000-00-00 00:00:00') {
        $isDisableStudentDate = 'disabled';
        $bedCrumTitle = 'View';
        $dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $TimeZone);
        $dateOfStudentSignature = (date('m/d/Y', strtotime($dateOfStudentSignature)));
    }
    // if($dateOfStudentSignature != '' && $dateOfStudentSignature != '0000-00-00 00:00:00')
    //     {
    //         $isDisableStudentDate = 'disabled';
    //         // $dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature,$TimeZone);
    //         // $dateOfStudentSignature=(date('m/d/Y',strtotime($dateOfStudentSignature)));
    //     }
    // echo 'dateOfStudentSignature'.$dateOfStudentSignature;
    //For Rotation List
    if ($preceptorNum)
        $rotation = $objRotation->GetAllActiveRotation($schoolId, $studentId);
    else
        $rotation = $objRotation->GetCurrentrotationByClinician($schoolId, $clinicianId);
    $totalRotation = ($rotation != '') ? mysqli_num_rows($rotation) : 0;
} else {
    if ($isEvalType == 1)
        $page_title = "Add ICU";
    else
        $page_title = "Add Floor";

    $bedCrumTitle = 'Add';

    //For Rotation List
    if ($preceptorNum)
        $rotation = $objRotation->GetAllActiveRotation($schoolId, $studentId);
    else
        $rotation = $objRotation->GetCurrentrotationByClinician($schoolId, 0);
    $totalRotation = ($rotation != '') ? mysqli_num_rows($rotation) : 0;
}


//For Clinician Name
$objClinician = new clsClinician();
$Clinician = $objClinician->GetClinicianByRotation($currentSchoolId, 0);
unset($objClinician);

//For Student Name
$objStudent = new clsStudent();
$Student = $objStudent->GetStudentsByRotation($currentSchoolId, $rotationId);
unset($objStudent);

$totalSection = 0;
$objEvaluation = new clsFloorTherapyAndICUEvaluation();

if ($activebtn == 'floor')
    $evalSection = $objEvaluation->GetSections('floor');
elseif ($activebtn == 'icu')
    $evalSection = $objEvaluation->GetSections('icu');


$totalSection = ($evalSection != '') ? mysqli_num_rows($evalSection) : 0;

//For Rotation Name
$RotationName = $objRotation->GetrotationDetails($rotationId, $schoolId);
$rotationtitle = $RotationName['title'];
unset($objRotation);

//For Student Full Name
// $objStudent=new clsStudent();
// $Rowstudent=$objStudent->GetSingleStudent($schoolId,$currentstudentId);
// $studentfullname=$Rowstudent['firstName'] . ' ' . $Rowstudent['lastName'];
// unset($objStudent);

if ($isEvalType == 1)
    $breadCrubTitle = 'ICU / ABG Rotation Evaluation';
else
    $breadCrubTitle = 'Floor Therapy Evaluation';

?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($page_title); ?></title>
    <?php include('includes/headercss.php'); ?>
    <link rel="shortcut icon" href="<?php echo ($dynamicOrgUrl); ?>/assets/images/favicon.ico" type="image/x-icon">
    <link rel="icon" href="<?php echo ($dynamicOrgUrl); ?>/assets/images/favicon.ico" type="image/x-icon">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />

    <style type="text/css">
        .some-class {
            float: left;
            clear: none;
        }

        .modalPosition {
            /* position: fixed; */
            top: 20px;
            margin-left: 10px;
            width: auto;
            position: unset !important;
        }

        .input-group-addon {
            position: absolute;
            right: 7px;
            /* width: 100%; */
            z-index: 99;
            width: 35px;
            margin: auto;
            top: 5px;
            border-left: 1px solid #ccc;
            border-radius: 50% !important;
            padding: 10px -2px;
            height: 35px;
            /* background: #01A750; */
            /* color: #fff; */
            color: #555;
            background: transparent;
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .panel-default>.panel-heading {
            background-color: transparent !important;
        }

        .btn-success,
        .btn-default {
            padding: 8px 25px;
            border-radius: 10px;
        }

        .panel {
            border-radius: 14px !important;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 45px !important;
        }

        .required-select2 {
            border-left: solid 3px red !important;
            border-radius: 12px !important;
        }

        .select2-container--default .select2-selection--single {
            background-color: #f6f9f9 !important;
            cursor: default !important;
            height: 45px !important;
            border-radius: 10px !important;
        }

        .select2-container--default .select2-selection--single {
            border: none !important;
        }

        .panel,
        .form-group {
            margin-bottom: 10px;
        }

        .bootstrap-datetimepicker-widget {
            border-radius: 12px !important;
        }

        .form-control {
            height: 45px;
        }

        .panel-default>.panel-heading+.panel-collapse>.panel-body {
            border-top: none !important;
        }

        /* .form-horizontal .form-group {
         margin-right: 0;
         margin-left: 0;
      } */

        /* Style for the collapsible content */
        .panel-collapse {
            display: none;
            /* Hidden by default */
            /* padding: 10px;
            border: 1px solid #ddd;
            background-color: #f9f9f9; */
        }

        /* Style for the collapsible button */
        .collapsible {
            /* background-color: #3498db;
            color: white;
            padding: 10px;
            border: none; */
            width: 100%;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            /* Align content horizontally */
        }

        .panel-heading {
            width: 100%;
        }

        /* Style for the arrow icons */
        .arrow-icon {
            transition: transform 0.3s;
        }

        .collapsible.expanded .arrow-icon i {
            transform: rotate(180deg);
        }

        @media screen and (max-width: 500px) {
            .panel-body ol {
                padding-left: 20px;
            }

            .panel-default>.panel-heading {
                padding-left: 5px;
            }

            /* .container-zero{
            padding: 0;
         } */
        }
    </style>
</head>

<body>
    <a href="registerExternalPreceptor.html?externalPreceptorId=<?php echo $externalPreceptorId; ?>" id="registerPage" class="addCommentpopup hide" data-organizationid="">Add</a>

<?php if ($IsMobile == 0) { ?>
    <?php if (!$preceptorNum) {
        include('includes/header.php'); ?>

        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
                        <li><a href="dashboard.html">Home</a></li>
                        <li><a href="rotations.html">Rotations</a></li>
                        <li><a href="rotations.html"><?php echo ($rotationtitle); ?></a></li>
                        <li><a href="floorTherapyAndICUEvaluationlist.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>"><?php echo $breadCrubTitle; ?></a></li>
                        <li class="active"><?php echo ($bedCrumTitle); ?></li>
                    </ol>
                </div>

            </div>
        </div>
    <?php } ?>
    <?php } ?>

    <div class="container">
        <?php if ($preceptorNum) { ?>
            <div class="formSubHeading" style="margin-top: 5rem;"> <?php echo $breadCrubTitle; ?>
                <br><span>
                    <h5>
                        <p>School Name: <?php echo $currenschoolDisplayname; ?></p>
                    </h5>
                </span>
            </div>
        <?php } ?>
        <form id="frmformative" data-parsley-validate class="form-horizontal" method="POST" <?php if ($preceptorNum) { ?> action="addFloorTherapyAndICUEvaluationSubmit.html?studentMasterId=<?php echo (EncodeQueryData($studentMasterId)); ?>&rotationId=<?php echo (EncodeQueryData($rotationId)); ?>&isPreceptor=1" <?php } else { ?> action="addFloorTherapyAndICUEvaluationSubmit.html?studentMasterId=<?php echo (EncodeQueryData($studentMasterId)); ?>&rotationId=<?php echo (EncodeQueryData($rotationId)); ?>" <?php } ?>>
            <input type="hidden" name="isEvalType" id="" value="<?php echo $isEvalType; ?>">
			<input type="hidden" name="IsMobile" id="IsMobile" value="<?php echo $IsMobile; ?>">

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="evaluationDate">Evaluation Date</label>
                        <div class="col-md-12">
                            <div class='input-group date w-full' id='evaluationDate' style="position: relative;">

                                <input type='text' name="evaluationDate" id="evaluationDate" class="form-control input-md required-input rotation_date" value="<?php echo ($evaluationDate); ?>" <?php echo $isDisableStudentDate; ?> required data-parsley-errors-container="#error-txtDate" placeholder="MM-DD-YYYY" />
                                <span class="input-group-addon calender-icon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                            <div id="error-txtDate"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <?php if (!$preceptorNum) { ?>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="cboclinician">Clinician</label>
                            <div class="col-md-12">
                                <select id="cboclinician" name="cboclinician" class="form-control input-md required-input select2_single" disabled>
                                    <option value="" selected>Select</option>
                                    <?php
                                    if ($Clinician != "") {
                                        while ($row = mysqli_fetch_assoc($Clinician)) {
                                            $selClinicianId  = $row['clinicianId'];
                                            $name  = stripslashes($row['firstName']);
                                            $lastName  = stripslashes($row['lastName']);
                                            $fullName = $name . ' ' . $lastName;
                                    ?>
                                            <option value="<?php echo ($selClinicianId); ?>" <?php if ($clinicianId == $selClinicianId) { ?> selected="true" <?php } ?>><?php echo ($fullName); ?></option>

                                    <?php

                                        }
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>
                    </div>
                <?php } elseif ($preceptorNum) { ?>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="cboclinician">Preceptor Name</label>
                            <div class="col-md-12">
                                <input type="text" name="" id="" class="form-control" readonly value="<?php echo $externalPreceptorFirstName . ' ' . $externalPreceptorLastName; ?>">
                            </div>
                        </div>
                    </div>
                <?php } ?>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="cborotation">Rotation</label>
                        <div class="col-md-12">
                            <select id="cborotation" name="cborotation" class="form-control input-md required-input select2_single" disabled>
                                <option value="" selected>Select</option>
                                <?php
                                if ($totalRotation > 0) {
                                    while ($row = mysqli_fetch_assoc($rotation)) {
                                        $selrotationId  = $row['rotationId'];
                                        $title  = stripslashes($row['title']);
                                ?>
                                        <option value="<?php echo ($selrotationId); ?>" <?php if ($DBRotationId == $selrotationId) { ?> selected="true" <?php } ?>><?php echo ($title); ?></option>
                                <?php }
                                }
                                ?>
                            </select>

                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="cbostudent">Student</label>
                        <div class="col-md-12">
                            <select id="cbostudent" name="cbostudent" class="form-control input-md required-input select2_single" <?php if ($preceptorNum) { ?> disabled <?php } else { ?> required <?php } ?> data-parsley-errors-container="#error-cbostudent">
                                <option value="" selected>Select</option>
                                <?php
                                if ($Student != "") {
                                    while ($row = mysqli_fetch_assoc($Student)) {
                                        $selstudentId  = $row['studentId'];
                                        $firstName  = stripslashes($row['firstName']);
                                        $lastName  = stripslashes($row['lastName']);
                                        $name =    $firstName . ' ' . $lastName;
                                ?>
                                        <option value="<?php echo ($selstudentId); ?>" <?php if ($studentId == $selstudentId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>

                                <?php

                                    }
                                }
                                ?>
                            </select>
                            <div id="error-cbostudent"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="studentsignitureDate">Date of Student Signature</label>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <div class='input-group date w-full' id='studentsignitureDate' style="position: relative;">

                                <input type='text' name="studentsignitureDate" readonly id="studentsignitureDate" class="form-control input-md required-input rotation_date" value="<?php echo ($dateOfStudentSignature);  ?>" data-parsley-errors-container="#error-txtDate" placeholder="MM-DD-YYYY" />
                                <span class="input-group-addon calender-icon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                            <div id="error-txtDate"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="studentcomment">Student Comment</label>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <textarea name="studentcomment" id="studentcomment" disabled class="form-control input-md " rows="4" cols="100"><?php if (isset($_GET['studentMasterId']))  echo ($studentComment); ?></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row ">
                <div class="col-md-12">
                    <div class="form-group">
                        <!-- <label class="col-md-2 control-label" for="instructions:"></label> -->
                        <div class="col-md-12 col-sm-12 col-xs-12 mt-10">
                            <div class="panel panel-default">
                                <div class="panel-body">
                                    <ul class="list-unstyled text-center">
                                        <li>This evaluation is based upon a <b>SATISFACTORY / NEEDS IMPROVEMENT / UNSATISFACTORY </b> scale</li>
                                        <li>
                                            <h4 class="margin_top_five margin_bottom_five"><b>For each objective below, please mark “S” “NI” or “U”.</b></h4>
                                        </li>
                                        <li>If “NI” or “U” is marked, please give an explanation in the comment section.</li>
                                        <?php if ($isEvalType == 1) { ?>
                                            <li>If the objective was not covered during this rotation, please input “N/A”</li>
                                        <?php } ?>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row instructionclass">
                <div class="col-md-12">
                    <div class="form-group">
                        <!-- <label class="col-md-2 control-label" for="instructions:"></label> -->
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <div class="panel-group" id="posts">
                                <?php

                                if ($evalSection) {
                                    while ($row = mysqli_fetch_array($evalSection)) {
                                        $sectionMasterId = $row['sectionMasterId'];
                                        $sectionDescription = isset($row['description']) ? $row['description'] : '';
                                        $title = $row['title'];
                                        //$firstName = $row['firstName'];
                                ?>

                                        <div class="panel panel-default">
                                            <a class="collapsible" style="color: #000; text-decoration: none;" href="#<?php echo $sectionMasterId; ?>" data-toggle="collapse" data-parent="#posts" id="collapse-link">
                                                <div class="panel-heading" style="display: flex; justify-content: space-between; align-items: center;">
                                                    <h4 class="panel-title">
                                                        <?php echo  $title;
                                                        if ($sectionDescription) {
                                                            echo $sectionDescription;
                                                        } ?>
                                                    </h4>
                                                    <span class="arrow-icon"> <i class="fa fa-angle-down" aria-hidden="true" id="collapse-icon" style="font-size: 19px;"></i></span>
                                                </div>
                                            </a>
                                            <div id="<?php echo $sectionMasterId; ?>" class="panel-collapse collapse">
                                                <?php
                                                // for question
                                                $pefquestion = $objEvaluation->GetAllQuestionMaster($sectionMasterId);
                                                $totalPEF = ($pefquestion != '') ? mysqli_num_rows($pefquestion) : 0;

                                                if ($totalPEF > 0) {
                                                    while ($row = mysqli_fetch_array($pefquestion)) {
                                                        if (isset($_GET['studentMasterId']))
                                                            $studentMasterId = DecodeQueryData($_GET['studentMasterId']);
                                                        else
                                                            $studentMasterId = 0;

                                                        $questionId = $row['questionId'];
                                                        $schoolPEFQuestionTitle = $row['optionText'];
                                                        $questionType = $row['questionType'];
                                                        $description = $row['description'];
                                                        $qhtml = GetFloorTherapyAndICUEvaluationQuestionHtml($questionId, $questionType, $studentMasterId, $currentSchoolId);

                                                ?>
                                                        <div class="panel-body isAllRadioButton">
                                                            <b class="questionDiv"><?php echo ($schoolPEFQuestionTitle); ?> </b><br /><br />
                                                            <?php echo $qhtml; ?>
                                                            <?php if ($description != '') { ?>
                                                                <br>
                                                                <div class="text-left margin_top_five margin_bottom_five" style="left: 5%;position: absolute;"><?php echo $description; ?></div>
                                                                <br><br>
                                                            <?php } ?>
                                                        </div>
                                                <?php
                                                    }
                                                }
                                                ?>
                                            </div>
                                        </div>
                                <?php
                                    }
                                }

                                ?>
                            </div>
                        </div>
                    </div>
                </div>

                <?php //if($isEvalType == 1) { 
                ?>
                <div class="">
                    <div class="col-md-12">
                        <div class="form-group">
                            <!-- <label class="col-md-2 control-label" for="instructions:"></label> -->
                            <div class="col-md-12 col-sm-12 col-xs-12 mb-10">
                                <div style="border-collapse: collapse;border-color: #ddd;border: 2px solid black;padding: 7px;" class="border border-primary border-14">
                                    <div class=" padding_top_five padding_bottom_five border-14" style="border: 1px solid black;">

                                        <div class="text-center">
                                            <p><b><u> SATISFACTORY’ / ‘NEEDS IMPROVEMENT’ / ‘UNSATISFACTORY’ grading scale.</u></b></p>
                                        </div>
                                        <p class="margin_left_ten"><b>All “S” results in a grade of 100% for rotation. Each “NI” results in 10 points off the rotation’s overall grade/ Each “U” results in 25 points off the rotation’s overall grade. </b></p>
                                        <p class="margin_left_ten">Receipt of an “NI” or “U” for any objective will result in a mandatory counseling session with the DCE and/or Program Director. Repeated “NI” ratings will be reviewed by the DCE and/or Program Director. At their discretion, the grade may be changed to a “U” designation.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- <div class="container">
                    <div class="row">
                        <div class="col-md-10 padding_left_zero">
                            <div class="form-group">
                                <label class="col-md-2 control-label">Rotation Grade: </label>
                                <div class="col-md-2 padding_zero">
                                    <input type="text" name="rotationGrade" id="rotationGrade" class="form-control" readonly>
                                </div>
                            </div>

                        </div>
                    </div>
                </div> -->

                <?php //} 
                ?>
                <!-- <div class="row">
                    <div class="form-group">
                        <label class="col-md-2 control-label"></label>
                        <div class="col-md-10">
                            <?php
                            // echo 'dateOfStudentSignature'.$dateOfStudentSignature;exit;

                            if ($dateOfStudentSignature == '0000-00-00 00:00:00') { ?>
                                <button style="margin: 0 0 0 11px;" id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
                            <?php } elseif ($dateOfStudentSignature == '') {
                            ?>
                                <button style="margin: 0 0 0 11px;" id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
                            <?php }
                            ?>
                            <?php if (!$preceptorNum) { ?>
                                <?php if ($currentstudentId > 0) { ?>
                                    <a type="button" href="floorTherapyAndICUEvaluationlist.html?studentId=<?php echo EncodeQueryData($currentstudentId); ?>" class="btn btn-default margin_left_ten">Cancel</a>
                                <?php } else { ?>
                                    <a type="button" href="floorTherapyAndICUEvaluationlist.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>" class="btn btn-default margin_left_ten">Cancel</a>
                                <?php } ?>
                            <?php } ?>
                        </div>
                    </div>
                </div> -->
            </div>

            <div class="row">
                <!-- <label class="col-md-2 control-label"></label>
                    <div class="col-md-10 padding_left_zero">
                        <div class="form-group">
                            <label class="col-md-2 control-label">Rotation Grade: </label>
                            <div class="col-md-2 padding_zero" style="margin-left: 8px;">
                                <input type="text" name="rotationGrade" id="rotationGrade" class="form-control" readonly>
                            </div>
                        </div>

                    </div> -->
                <div class="col-md-12">
                    <div class="grid-layout">
                        <div class="item card-body">
                            <div style="display: flex; align-items: center;">
                                <div class="card-image-section" style="background: #EEF5FF;">
                                    <img src="<?php echo ($dynamicOrgUrl); ?>/student/assets/images/award.png" alt="award">
                                </div>
                                <p class="card-title">
                                    Rotation Grade:
                                </p>
                            </div>
                            <div>
                                <p class="card-count" style="font-size: 18px; font-family: 600;">
                                    <input type="hidden" name="rotationGrade" id="inputRotationGrade" value="">
                                    <span class="card-count-span" id="rotationGrade">
                                        <?php //echo $rotationGrade; 
                                        ?>
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row" style="margin: 0;">
                <div class="form-group" style="margin: 20px 0;">
                    <!-- <label class="col-md-2 control-label"></label> -->
                    <div class="col-md-12" style="display: flex; justify-content: center;gap:15px;">
                        <?php
                        // echo 'dateOfStudentSignature'.$dateOfStudentSignature;exit;
                        if ($view != 'V') {
                        if ($dateOfStudentSignature == '0000-00-00 00:00:00') { ?>
                            <button id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
                        <?php } elseif ($dateOfStudentSignature == '') {
                        ?>
                            <button id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
                        <?php } 
                        }
                        ?>
                        <?php if($IsMobile){ ?>
						    <a type="button" href="<?php echo BASE_PATH ?>/webRedirect.html?status=Cancel&type=floorAndIcu&usertype=clinician" class="btn btn-default">Cancel</a>
                        
                        <?php }elseif (!$preceptorNum) { ?>
                            <?php if ($currentstudentId > 0) { ?>
                                <a type="button" href="floorTherapyAndICUEvaluationlist.html?studentId=<?php echo EncodeQueryData($currentstudentId); ?>" class="btn btn-default">Cancel</a>
                            <?php } else { ?>
                                <a type="button" href="floorTherapyAndICUEvaluationlist.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>" class="btn btn-default">Cancel</a>
                            <?php } ?>
                        <?php } ?>
                    </div>
                </div>
            </div>
        </form>
        <?php include('includes/footer.php'); ?>
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
        <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
        <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
        <script src="https://cdn.ckeditor.com/ckeditor5/35.0.1/classic/ckeditor.js"></script>
        <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
        <script type="text/javascript">
            $('.addCommentpopup').magnificPopup({
                'type': 'ajax',
                'closeOnBgClick': false
            });
            // ClassicEditor
 			// .create(document.querySelector('#studentcomment'))
 			// .catch(error => {
 			// 	console.error(error);
 			// });	
            $(window).load(function() {

                $('.isAllRadioButton').trigger('click');

                $('#frmformative').parsley().on('field:validated', function() {
                        var ok = $('.parsley-error').length === 0;
                    })
                    .on('form:submit', function() {
                        ShowProgressAnimation();
                        return true; // Don't submit form for this demo
                    });

                $('#evaluationDate').datetimepicker({
                    format: 'MM/DD/YYYY'
                });
                $('#studentsignitureDate').datetimepicker({
                    format: 'MM/DD/YYYY'
                });
                $('#InstructorDate').datetimepicker({
                    format: 'MM/DD/YYYY'
                });


                //for searching dropdown
                $(".select2_single").select2();
                $('#select2-cborotation-container').addClass('required-select2');
                $('#select2-cboclinician-container').addClass('required-select2');
                $('#select2-cbostudent-container').addClass('required-select2');
                $("#cboclinician").select2("readonly", true);

                <?php if ($currentstudentId > 0) { ?>
                    $('#cbostudent').prop('disabled', true);
                <?php }
                if ($rotationId > 0) { ?>
                    document.getElementById("cborotation").required = false;
                <?php } ?>

            });

            $(document).ready(function() {

                var externalPreceptorFirstName = '<?php echo $externalPreceptorFirstName; ?>';
                var preceptorNum = '<?php echo $preceptorNum; ?>';
                if (externalPreceptorFirstName == '' && preceptorNum != '') {
                    $('#registerPage')[0].click();
                }

                $('#isReloadPage').click(function() {
                    location.reload();
                });

                $(".isAllRadioButton").click(function() {
                    var isEvalType = "<?php echo $isEvalType; ?>";

                    var sumCheckedButton = 0;
                    var isTotal = 0;
                    var isNiTotal = 0;
                    var isUTotal = 0;
                    var isSSeleted = 0;

                    $(".isAllRadioButton input[type=radio]:checked").each(function() {

                        var checkedRadio = $.trim($(this).parent().text());
                        if (checkedRadio == 'NI')
                            isNiTotal = 10 + parseInt(isNiTotal);
                        if (checkedRadio == 'U')
                            isUTotal = 25 + parseInt(isUTotal);
                        if (checkedRadio == 'S')
                            isSSeleted = 100;

                    });

                    // if(isEvalType == 1)
                    // {
                     isTotal = parseInt(isUTotal) + parseInt(isNiTotal);
                    // console.log($("#rotationGrade").val());
                    $("#inputRotationGrade").val('');
                    // console.log(isTotal);
                    if (isTotal > 0) {
                        $("#inputRotationGrade").val(100 - isTotal);
                        $("#rotationGrade").text(100 - isTotal);
                    } else if (isSSeleted > 0) {
                        $("#inputRotationGrade").val(100);
                        $("#rotationGrade").text(100);
                    }

                    // $("#rotationGrade").text(isTotal);
                    // $(".rotationGrade").val(isTotal);

                });

            });

        </script>

        <script>
            // Get all collapsible button elements
            var buttons = document.querySelectorAll(".collapsible");
            var contents = document.querySelectorAll(".panel-collapse");

            // Add click event listeners to all buttons
            buttons.forEach(function(button, index) {
                button.addEventListener("click", function() {
                    // Check if the content is currently expanded
                    var isExpanded = contents[index].style.display === "block";

                    // Close all sections
                    contents.forEach(function(content) {
                        content.style.display = "none";
                    });

                    // Reset the "expanded" class for all buttons
                    buttons.forEach(function(btn) {
                        btn.classList.remove("expanded");
                    });

                    // Toggle the content for the clicked section
                    if (!isExpanded) {
                        contents[index].style.display = "block";
                        button.classList.add("expanded");
                    }
                });
            });
        </script>
</body>

</html>