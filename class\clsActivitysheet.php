<?php
class clsActivitysheet
{
	var $studentId = 0;
	var $schoolId = 0;
	var $journalId = '';
	var $interactionId = '';
	var $procedurecountId = '';
	var $createdBy = '';
	var $isSendToPreceptor = 0;
	var $clinicianDate = '';
	var $schoolDate = '';
	var $clinicianComments = '';
	var $schoolComments = '';
	var $date = '';
	var $rotationId = 0;
	var $physicianId = 0;
	var $clinicianId = 0;
	var $updatedBy = '';
	var $schoolSignatureDate = '';
	var $schoolComment = '';


	function SaveActivityDetails($activityId)
	{

		$objDB = new clsDB();
		$sql = '';
		$retActivity = 0;

		if ($activityId > 0) {

			$sql = "UPDATE activitysheetdetails SET 
						 studentId = '" . addslashes($this->studentId) . "', 
						 schoolId = '" . addslashes($this->schoolId) . "', 
						 journalId = '" . addslashes($this->journalId) . "',
						 interactionId = '" . addslashes($this->interactionId) . "',  
						 procedurecountId = '" . addslashes($this->procedurecountId) . "',  
						 activityDate = '" . addslashes($this->date) . "',
						 clinicianSignatureDate = '" . addslashes($this->clinicianDate) . "',
						 clinicianComment = '" . addslashes($this->clinicianComments) . "',
						 rotationId = '" . addslashes($this->rotationId) . "',
						 physicianinteractionId = '" . addslashes($this->physicianId) . "',
						 updatedBy = '" . addslashes($this->createdBy) . "',
						 updatedDate = '" . (date("Y-m-d h:i:s")) . "'
						 Where activityId= " . $activityId;
			// echo 'update->'.$sql;
			// exit;
			$objDB->ExecuteQuery($sql);
			$retActivity = $activityId;
		} else {

			$sql = "INSERT INTO activitysheetdetails (studentId, schoolId, clinicianId, journalId, interactionId,procedurecountId,activityDate,rotationId,physicianinteractionId, schoolSignatureDate, clinicianSignatureDate, schoolComment, clinicianComment, isSendToPreceptor,  createdBy, createdDate) 
					VALUES ('" . addslashes($this->studentId) . "',
							'" . addslashes($this->schoolId) . "',
							'" . addslashes($this->clinicianId) . "',
							'" . addslashes($this->journalId) . "',
							'" . addslashes($this->interactionId) . "',
							'" . addslashes($this->procedurecountId) . "',
							'" . addslashes($this->date) . "',
							'" . addslashes($this->rotationId) . "',
							'" . addslashes($this->physicianId) . "',
							'" . addslashes($this->schoolDate) . "',
							'" . addslashes($this->clinicianDate) . "',
							'" . addslashes($this->schoolComments) . "',
							'" . addslashes($this->clinicianComments) . "',
							'" . addslashes($this->isSendToPreceptor) . "',
							'" . addslashes($this->createdBy) . "',
							'" . (date("Y-m-d h:i:s")) . "'
													
							)";
			// echo 'Insert->'.$sql;exit;
			$retActivity = $objDB->ExecuteInsertQuery($sql);
		}


		unset($objDB);
		return $retActivity;
	}

	function GetAllActivitySheet($SchoolId, $studentId = 0, $clinicianId = 0, $rotationId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT activitysheetdetails.*,rotation.parentRotationId,rotation.locationId as rotationLocationId,courses.locationId,
				rotation.title AS rotationName, preceptordetails.preceptorId, CONCAT( clinician.firstName, ' ' ,clinician.lastName) AS clinicianName,
				CONCAT( student.firstName, ' ' ,student.lastName) AS studentName
				FROM activitysheetdetails
				LEFT JOIN rotation ON activitysheetdetails.rotationId=rotation.rotationId
				LEFT JOIN courses ON rotation.courseId=courses.courseId
				LEFT JOIN preceptordetails ON preceptordetails.referenceId = activitysheetdetails.activityId AND preceptordetails.type='activitySheet'
				LEFT JOIN clinician ON clinician.clinicianId = activitysheetdetails.clinicianId
				LEFT JOIN student ON student.studentId = activitysheetdetails.studentId
				WHERE activitysheetdetails.schoolId=" . $SchoolId . " AND activitysheetdetails.isDelete = 0";

		if ($studentId) {
			$sql	.= " AND activitysheetdetails.studentId = " . $studentId;
		}

		if ($clinicianId) {
			$sql	.= " AND activitysheetdetails.clinicianId = " . $clinicianId;
		}

		if ($rotationId) {
			$sql	.= " AND activitysheetdetails.rotationId = " . $rotationId;
		}
		$sql .= " GROUP BY activitysheetdetails.activityId";
		$sql .= " ORDER BY activitysheetdetails.activityDate DESC";
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllActivitySheetForClinician($SchoolId, $studentId = 0, $clinicianId = 0, $rotationId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT activitysheetdetails.*,rotation.parentRotationId,rotation.locationId as rotationLocationId,courses.locationId,
				rotation.title AS rotationName, preceptordetails.preceptorId, CONCAT( clinician.firstName, ' ' ,clinician.lastName) AS clinicianName,
				CONCAT( student.firstName, ' ' ,student.lastName) AS studentName
				FROM activitysheetdetails
				LEFT JOIN rotation ON activitysheetdetails.rotationId=rotation.rotationId
				LEFT JOIN courses ON rotation.courseId=courses.courseId
				LEFT JOIN preceptordetails ON preceptordetails.referenceId = activitysheetdetails.activityId AND preceptordetails.type = 'activitySheet'
				LEFT JOIN clinician ON clinician.clinicianId = activitysheetdetails.clinicianId
				LEFT JOIN student ON student.studentId = activitysheetdetails.studentId
				WHERE activitysheetdetails.schoolId=" . $SchoolId . " AND activitysheetdetails.isDelete = 0";

		if ($studentId) {
			$sql	.= " AND activitysheetdetails.studentId = " . $studentId;
		}

		if ($clinicianId) {
			$sql	.= " AND activitysheetdetails.clinicianId = " . $clinicianId;
		}

		if ($rotationId) {
			$sql	.= " AND activitysheetdetails.rotationId = " . $rotationId;
		}

		$sql .= "  AND (
			(CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
			OR
			(rotation.parentRotationId > 0
				AND EXISTS (
					SELECT 1
					FROM rotation AS parent
					WHERE parent.rotationId = rotation.parentRotationId
					AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
					AND rotation.isSchedule = 1 
				)
			)
		) ";

		$sql .= " GROUP BY activitysheetdetails.activityId";
		$sql .= " ORDER BY activitysheetdetails.activityDate DESC";
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllActivitySheetDetails($activityId, $schoolId)
	{
		$objDB = new clsDB();
		$sql = "SELECT activitysheetdetails.*, journal.journalDate, journal.journalStudentEntry, journal.hospitalSiteUnitId AS journalHospitalSiteUnitId,
					  interaction.hospitalSiteUnitId AS interactionHospitalSiteUnitId, interaction.pointsAwarded, interaction.timeSpent, interaction.interactionSummary,
					  interaction.clinicianId, interaction.selectedInteraction
					 
				FROM activitysheetdetails
				LEFT JOIN rotation ON activitysheetdetails.rotationId=rotation.rotationId
				LEFT JOIN journal ON journal.journalId = activitysheetdetails.journalId
				LEFT JOIN interaction ON interaction.interactionId = activitysheetdetails.interactionId
				LEFT JOIN studentprocedutecount ON studentprocedutecount.procedureCountId = activitysheetdetails.procedurecountId
				WHERE activitysheetdetails.activityId = " . $activityId . " and activitysheetdetails.schoolId = " . $schoolId;

		// echo $sql;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}

	function UpdateActivitySheetByPreceptor($activityId = 0, $activityDate = '')
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "UPDATE activitysheetdetails SET												 
					isPreceptorCompletedStatus='1',
					clinicianSignatureDate = '" . addslashes($this->clinicianDate) . "', 
					clinicianComment = '" . addslashes($this->clinicianComments) . "', 
					updatedDate = '" . (date("Y-m-d h:i:s")) . "'";
		if ($activityDate != '')
			$sql .= ", activityDate='" . ($activityDate) . "'	";

		$sql .= " Where activityId= " . $activityId;
		// echo $sql;
		// exit;
		$activityId = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $activityId;
	}

	function UpdateActivitySheetByClinician($activityId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "UPDATE activitysheetdetails SET
					clinicianSignatureDate = '" . addslashes($this->clinicianDate) . "', 
					clinicianComment = '" . addslashes($this->clinicianComments) . "', 
					updatedBy = '" . ($this->updatedBy) . "', 
					updatedDate = '" . (date("Y-m-d h:i:s")) . "'";

		$sql .= " Where activityId= " . $activityId;
		// echo $sql;
		// exit;
		$retActivityId = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $retActivityId ? $activityId : 0;
	}

	function UpdateActivitySheetByAdmin($activityId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "UPDATE activitysheetdetails SET
					schoolSignatureDate = '" . addslashes($this->schoolSignatureDate) . "', 
					schoolComment = '" . addslashes($this->schoolComment) . "',
					updatedBy = '" . ($this->updatedBy) . "', 
					updatedDate = '" . (date("Y-m-d h:i:s")) . "'";

		$sql .= " Where activityId= " . $activityId;
		// echo $sql;
		// exit;
		$retActivityId = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $retActivityId ? $activityId : 0;
	}

	function GetAllPhysicians()
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM physicianinteraction";

		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllActivitySheetForReport($schoolId, $rotationId, $subcborotation, $studentId, $clinicianId, $startDate, $endDate, $ascdesc = '', $sortorder)
	{
		// echo $sortorder;
		$objDB = new clsDB();
		$subcborotation = str_replace(" ", ",", $subcborotation);
		$studentIdCount = ($studentId != '') ? count($studentId) : 0;
		$studentIds =  ($studentIdCount > 0) ? implode(',', $studentId) : '';

		$sql = "SELECT activitysheetdetails.*,rotation.parentRotationId,rotation.locationId as rotationLocationId,courses.locationId,
		rotation.title AS rotationName, preceptordetails.preceptorId, CONCAT( clinician.firstName, ' ' ,clinician.lastName) AS clinicianName,
		student.firstName AS studentFirstName, student.lastName AS studentLastName, preceptordetails.preceptorId
		FROM activitysheetdetails
		LEFT JOIN rotation ON activitysheetdetails.rotationId=rotation.rotationId
		LEFT JOIN courses ON rotation.courseId=courses.courseId
		LEFT JOIN preceptordetails ON preceptordetails.referenceId = activitysheetdetails.activityId AND preceptordetails.type = 'activitySheet'
		LEFT JOIN clinician ON clinician.clinicianId = activitysheetdetails.clinicianId
		LEFT JOIN student ON student.studentId = activitysheetdetails.studentId
		LEFT JOIN hospitalsites ON hospitalsites.hospitalSiteId=rotation.hospitalSiteId
		WHERE activitysheetdetails.schoolId = " . $schoolId . " AND activitysheetdetails.isDelete = 0";

		if ($rotationId > 0 && $subcborotation > 0)
			$sql .= " AND (rotation.rotationId=" . $rotationId . " OR rotation.rotationId IN ($subcborotation))";
		elseif ($rotationId > 0)
			$sql .= " AND rotation.rotationId=" . $rotationId;
		elseif ($subcborotation > 0)
			$sql .= " AND rotation.rotationId IN ($subcborotation)";

		if ($studentIdCount > 0)
			$sql .= " AND student.studentId IN ($studentIds)";

		if ($clinicianId > 0)
			$sql .= " AND clinician.clinicianId=" . $clinicianId;

		if ($startDate > 0 || $endDate > 0)
			$sql .= " AND activitysheetdetails.activityDate >= '" . $startDate . "' and activitysheetdetails.activityDate <= '" . $endDate . "'";


		if ($ascdesc && $sortorder == 1)
			$sql .= "  ORDER BY student.firstName " . $ascdesc;

		else if ($ascdesc && $sortorder == 2)
			$sql .= "  ORDER BY rankmaster.title " . $ascdesc;
		else if ($ascdesc && $sortorder == 5) //rotation
			$sql .= "  ORDER BY rotation.title " . $ascdesc;

		else if ($ascdesc && $sortorder == 10)  //hospital site
			$sql .= "  ORDER BY rotation.`title` " . $ascdesc;

		else if ($ascdesc && $sortorder == 14)
			$sql .= "  ORDER BY student.firstName " . $ascdesc;

		else if ($ascdesc && $sortorder == 15)
			$sql .= "  ORDER BY student.lastName " . $ascdesc;

		else if ($ascdesc && $sortorder == 7)
			$sql .= "  ORDER BY clinician.firstName " . $ascdesc;

		else if ($ascdesc && $sortorder == 8)
			$sql .= "  ORDER BY hospitalsites.title " . $ascdesc;


		else
			$sql .= " ORDER BY `activitysheetdetails`.`activityDate`" . $ascdesc;
		// echo $sql;
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}
	function GetAllActivitySheetForApp($SchoolId, $studentId = 0, $clinicianId = 0, $rotationId = 0, $userType = 0, $limitstring = '', $searchText = '')
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT activitysheetdetails.*,rotation.parentRotationId,rotation.locationId as rotationLocationId,courses.locationId,
				rotation.rotationId,rotation.title AS rotationName, preceptordetails.preceptorId, CONCAT( clinician.firstName, ' ' ,clinician.lastName) AS clinicianName,
				CONCAT( student.firstName, ' ' ,student.lastName) AS studentName
				FROM activitysheetdetails
				LEFT JOIN rotation ON activitysheetdetails.rotationId=rotation.rotationId
				LEFT JOIN courses ON rotation.courseId=courses.courseId
				LEFT JOIN preceptordetails ON preceptordetails.referenceId = activitysheetdetails.activityId AND preceptordetails.type = 'activitySheet'
				LEFT JOIN clinician ON clinician.clinicianId = activitysheetdetails.clinicianId
				LEFT JOIN student ON student.studentId = activitysheetdetails.studentId
				LEFT JOIN extenal_preceptors ON extenal_preceptors.id = preceptordetails.preceptorId
				WHERE activitysheetdetails.schoolId=" . $SchoolId . " AND activitysheetdetails.isDelete = 0";

		if ($studentId) {
			$sql	.= " AND activitysheetdetails.studentId = " . $studentId;
		}

		if ($clinicianId) {
			$sql	.= " AND activitysheetdetails.clinicianId = " . $clinicianId;
		}

		if ($rotationId) {
			$sql	.= " AND activitysheetdetails.rotationId = " . $rotationId;
		}
		if ($searchText != "") {
			if ($userType) {
				$sql .= " AND (rotation.title LIKE '%" . $searchText . "%') ";
			} else {

				$sql .= " AND (CONCAT(clinician.firstName, ' ', clinician.lastName) LIKE '%" . $searchText . "%' OR CONCAT(extenal_preceptors.firstName, ' ', extenal_preceptors.lastName) LIKE '%" . $searchText . "%' OR rotation.title LIKE '%" . $searchText . "%') ";
			}
		}

		$sql .= " GROUP BY activitysheetdetails.activityId";
		$sql .= " ORDER BY activitysheetdetails.activityDate DESC" . $limitstring;
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}


	/**
	 * Retrieves detailed information for a specific activity sheet log.
	 *
	 * This function fetches detailed information for an activity sheet log entry
	 * by its activity ID, optionally filtered by school ID. It returns data such as
	 * the rotation name, user information, clinician name, hospital site name, 
	 * school name, and preceptor name associated with the activity sheet.
	 *
	 * @param int $activityId The ID of the activity sheet to retrieve details for.
	 * @param int $schoolId Optional. The school ID for filtering the results. Default is 0.
	 *
	 * @return array An associative array with the activity sheet log details.
	 */

	function GetAllActivitySheetDetailsForLogs($activityId, $schoolId = 0)
	{
		$objDB = new clsDB();

		$sql = "SELECT activitysheetdetails.*,rotation.title as rotationName,activitysheetdetails.studentId as userId,CONCAT(student.firstName, ' ', student.lastName) AS userName,CONCAT(clinician.firstName, ' ', clinician.lastName) AS clinicianName,hospitalsites.title AS hospitalSiteName, schools.displayName as schoolName,CONCAT(extenal_preceptors.firstName, ' ', extenal_preceptors.lastName) AS preceptorName FROM `activitysheetdetails` 
		INNER JOIN rotation ON activitysheetdetails.`rotationId` = rotation.`rotationId` 
		INNER JOIN student ON student.studentId=activitysheetdetails.studentId
        INNER JOIN hospitalsites ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
        INNER JOIN schools ON schools.schoolId = activitysheetdetails.schoolId
		LEFT JOIN clinician ON activitysheetdetails.clinicianId = clinician.clinicianId
		LEFT JOIN preceptordetails ON activitysheetdetails.activityId = preceptordetails.referenceId AND preceptordetails.type = 'activitySheet'
		LEFT JOIN extenal_preceptors ON extenal_preceptors.id = preceptordetails.preceptorId
		WHERE activityId =" . $activityId;

		if ($schoolId) {
			$sql .= " AND activitysheetdetails.schoolId=" . $schoolId;
		}
		// echo $sql;
		// exit;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}


	/**
	 * Creates a log entry for an activity sheet action
	 *
	 * @param int $id ID of the activity sheet
	 * @param string $action Action type (Add, Edit, Delete, Signoff)
	 * @param int $userId User ID of the user performing the action
	 * @param string $userType Type of user (e.g. student, clinician, etc.)
	 *
	 * @return array An array containing the log data, the original activity sheet data, and any additional data
	 */
	function createActivitySheetLog($id, $action, $userId, $userType)
	{
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$objActivitySheet = new clsActivitySheet(); // Assuming `Attendance` class is used for `prepareAttendanceLogData`

		// Get user details based on user type
		$userDetails = getUserDetails($userId, $userType);


		$rowData = $objActivitySheet->GetAllActivitySheetDetailsForLogs($id);

		if ($rowData != '')
			$logData = $objLog->generateLogData($rowData, $userType);

		$additionalData = '';

		// Populate log data with user details
		$logData['userId'] = $userDetails['userId'];
		$logData['userName'] = $userDetails['userName'];
		$logMessage = '';

		if ($action == 'Add') {
			$logMessage = $logData['userName'] . ' added activity sheet from ' . $logData['rotationName'] . ' rotation.';
		} else if ($action == 'Edit') {
			$logMessage = $logData['userName'] . ' updated activity sheet from ' . $logData['rotationName'] . ' rotation.';
		} else if ($action == 'Delete' && $userType != 'Student') {
			$logMessage = $logData['userName'] . ' deleted ' . $rowData['userName'] . ' activity sheet from ' . $logData['rotationName'] . ' rotation.';
		} else if ($action == 'Delete' && $userType == 'Student') {
			$logMessage = $logData['userName'] . ' deleted activity sheet from ' . $logData['rotationName'] . ' rotation.';
		} else if ($action == 'Signoff' && $userType == 'Preceptor') {
			$logMessage = $logData['userName'] . ' signoff activity sheet from ' . $logData['rotationName'] . ' rotation.';
		}

		// Construct log message
		$logData['message'] = $logMessage;

		// Return the data
		return [$logData, $rowData, $additionalData];
	}

	/**
	 * Saves activity sheet audit log
	 *
	 * @param int $id Activity sheet ID
	 * @param int $userId User ID that is performing the action
	 * @param string $userType User type (Student, Preceptor, Admin)
	 * @param string $action Action type (Add, Edit, Delete, Signoff)
	 * @param int $isMobile Whether the action was performed on a mobile device (0 or 1)
	 *
	 * @return bool Returns true if the log was successfully saved
	 */
	function saveActivitySheetAuditLog($id, $userId, $userType, $action, $isMobile = 0)
	{
		// Instantiate the Logger and Checkoff classes
		$objLog = new clsLogger();
		$objActivitySheet = new clsActivitySheet();

		// Prepare log data
		[$logData, $rowData, $additionalData] = $objActivitySheet->createActivitySheetLog($id, $action, $userId, $userType);
		$logData['isMobile'] = $isMobile;

		if ($action == 'Delete') {
			$objActivitySheet->DeleteActivitySheet($id);
		}
		// Save the log details
		$objLog->saveLogs($logData, $action, $id, 'Activity Sheet', $rowData, $additionalData);

		// Clean up
		unset($objLog);
		unset($objActivitySheet);

		return true; // Return success or handle further actions as needed
	}

	function DeleteActivitySheet($activitysheetId)
	{
		$objDB = new clsDB();
		$result = "";

		if ($activitysheetId > 0) {
			$objDB = new clsDB();
			$sql = " UPDATE activitysheetdetails 
					SET isDelete = 1, deletedDate ='" . (date("Y-m-d")) . "' WHERE activityId = " . $activitysheetId;
			// echo $sql;
			// exit;
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
		return $result;
	}
	function GetAllActivitySheetForClinicianApp($SchoolId, $studentId = 0, $clinicianId = 0, $rotationId = 0, $userType = 0, $limitstring = '', $searchText = '')
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT activitysheetdetails.*,rotation.parentRotationId,rotation.locationId as rotationLocationId,courses.locationId,
				rotation.rotationId,rotation.title AS rotationName, preceptordetails.preceptorId, CONCAT( clinician.firstName, ' ' ,clinician.lastName) AS clinicianName,
				CONCAT( student.firstName, ' ' ,student.lastName) AS studentName
				FROM activitysheetdetails
				LEFT JOIN rotation ON activitysheetdetails.rotationId=rotation.rotationId
				LEFT JOIN courses ON rotation.courseId=courses.courseId
				LEFT JOIN preceptordetails ON preceptordetails.referenceId = activitysheetdetails.activityId AND preceptordetails.type = 'activitySheet'
				LEFT JOIN clinician ON clinician.clinicianId = activitysheetdetails.clinicianId
				LEFT JOIN student ON student.studentId = activitysheetdetails.studentId
				LEFT JOIN extenal_preceptors ON extenal_preceptors.id = preceptordetails.preceptorId
				WHERE activitysheetdetails.schoolId=" . $SchoolId;

		if ($studentId) {
			$sql	.= " AND activitysheetdetails.studentId = " . $studentId;
		}

		if ($clinicianId) {
			$sql	.= " AND activitysheetdetails.clinicianId = " . $clinicianId;
		}

		if ($rotationId) {
			$sql	.= " AND activitysheetdetails.rotationId = " . $rotationId;
		}
		if ($searchText != "") {
			if ($userType) {
				$sql .= " AND (rotation.title LIKE '%" . $searchText . "%') ";
			} else {

				$sql .= " AND (CONCAT(clinician.firstName, ' ', clinician.lastName) LIKE '%" . $searchText . "%' OR CONCAT(extenal_preceptors.firstName, ' ', extenal_preceptors.lastName) LIKE '%" . $searchText . "%' OR rotation.title LIKE '%" . $searchText . "%') ";
			}
		}

		$sql .= "  AND (
			(CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
			OR
			(rotation.parentRotationId > 0
				AND EXISTS (
					SELECT 1
					FROM rotation AS parent
					WHERE parent.rotationId = rotation.parentRotationId
					AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
					AND rotation.isSchedule = 1 
				)
			)
		) ";

		$sql .= " GROUP BY activitysheetdetails.activityId";
		$sql .= " ORDER BY activitysheetdetails.activityDate DESC" . $limitstring;
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetStandardActivitysheetProcedureCount($procedureCountIds)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT studentprocedutecount.studentId,studentprocedutecount.procedurePointsAssist,studentprocedutecount.procedurePointsObserve,studentprocedutecount.procedurePointsPerform,studentprocedutecount.procedurePointsPerformTotal,studentprocedutecount.procedureTotal, procedurecountmaster.proceduteCountName,procedurecountmaster.procedureCountsCode,procedurecategory.categoryName FROM `studentprocedutecount` 
				INNER JOIN procedurecountmaster ON procedurecountmaster.proceduteCountId = studentprocedutecount.proceduteCountTopicId 
				INNER JOIN procedurecategory ON procedurecategory.procedureCategoryId = procedurecountmaster.procedureCategoryId
				WHERE procedureCountId IN (" . $procedureCountIds . ") ORDER BY procedurecategory.sortOrder,procedurecountmaster.procedureCountsCode ASC";
		// echo $sql;
		$rows = $objDB->GetResultSet($sql);
		return $rows;
		unset($objDB);
	}

	function GetAdvanceActivitysheetProcedureCount($procedureCountIds)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT studentprocedutecount.studentId,studentprocedutecount.procedurePointsAssist,studentprocedutecount.procedurePointsObserve,studentprocedutecount.procedurePointsPerform,studentprocedutecount.procedurePointsPerformTotal,studentprocedutecount.procedureTotal, advanceprocedurecountmaster.advanceProceduteCountName AS proceduteCountName,advanceprocedurecountmaster.advanceProcedureCountsCode AS procedureCountsCode,advanceprocedurecategory.categoryName FROM studentprocedutecount 
				INNER JOIN advanceprocedurecountmaster ON advanceprocedurecountmaster.advanceProceduteCountId = studentprocedutecount.proceduteCountTopicId
				INNER JOIN advanceprocedurecategory ON advanceprocedurecategory.procedureCategoryId = advanceprocedurecountmaster.advanceProcedureCategoryId
				WHERE procedureCountId IN (" . $procedureCountIds . ") ORDER BY advanceprocedurecategory.sortOrder,advanceprocedurecountmaster.advanceProcedureCountsCode ASC";
		// echo $sql;
		$rows = $objDB->GetResultSet($sql);
		return $rows;
		unset($objDB);
	}

	function GetUsafActivitysheetProcedureCount($procedureCountIds)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT studentprocedutecount.studentId,studentprocedutecount.procedurePointsAssist,studentprocedutecount.procedurePointsObserve,studentprocedutecount.procedurePointsPerform,studentprocedutecount.procedurePointsPerformTotal,studentprocedutecount.procedureTotal, usafprocedurecountmaster.proceduteCountName,usafprocedurecountmaster.procedureCountsCode,usafprocedurecategory.categoryName FROM studentprocedutecount 
INNER JOIN usafprocedurecountmaster ON usafprocedurecountmaster.proceduteCountId = studentprocedutecount.proceduteCountTopicId
INNER JOIN usafprocedurecategory ON usafprocedurecategory.procedureCategoryId = usafprocedurecountmaster.procedureCategoryId
				WHERE procedureCountId IN (" . $procedureCountIds . ") ORDER BY usafprocedurecategory.sortOrder,usafprocedurecountmaster.procedureCountsCode ASC";
		// echo $sql;
		$rows = $objDB->GetResultSet($sql);
		return $rows;
		unset($objDB);
	}
}
