<?php
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsExternalPreceptors.php');
$dynamicOrgUrl = BASE_PATH;
$data = urldecode($_POST['data']); // decode the serialized form data
parse_str($data, $form_data); // parse the form data into an array
// print_r($form_data);exit;
$rotationId = ($form_data['rotationId']);
$evaluationId = ($form_data['evaluationId']);
$preceptorNum = ($form_data['preceptorNum']);
$evaluationType = ($form_data['evaluationType']);
$checkoffId = $form_data['checkoffId'];
$schoolTopicId = $form_data['schoolTopicId'];
$activityId = ($form_data['activityId']);
$soapNoteId = ($form_data['soapNoteId']) ?? '';


//For Save Preceptor
$objExternalPreceptors = new clsExternalPreceptors();
$objExternalPreceptors->mobile_num = $preceptorNum;
$preceptorId = $objExternalPreceptors->SavePreceptors();

//Get schoolType
$objDB = new clsDB();
$isActiveCheckoff = $objDB->GetSingleColumnValueFromTable('schools', 'isActiveCheckoffForStudent', 'schoolId', $currentSchoolId);

$countryCode = $objDB->GetSingleColumnValueFromTable('schools', 'countryCode', 'schoolId', $currentSchoolId);

unset($objDB);

if ($evaluationType == 'dailyEval') {
    $objDB = new clsDB();
    $objDB->UpdateSingleColumnValueToTable('studentdailymaster', 'preceptorId', $preceptorId, 'studentDailyMasterId', $evaluationId);
    unset($objDB);

    $isEvalType = 1;
    $isEvalTypeLabel = 'Daily/Weekly Evaluation';

    $URL = $dynamicOrgUrl . '/school/' . $schoolSlug . '/clinician/dailyEval.html?studentDailyMasterId=' . EncodeQueryData($evaluationId) . '&rotationId=' . EncodeQueryData($rotationId) . '&preceptorNum=' . EncodeQueryData($preceptorNum) . '&preceptorId=' . EncodeQueryData($preceptorId) . '&isEvalType=' . EncodeQueryData($isEvalType);

    // Create TinyUrl
    $randomUrl = getTinyUrl($URL);
    $redirectUrl = $dynamicOrgUrl . '/redirect/' . EncodeQueryData($randomUrl);

    // Get Student Name
    $studentNameForSms = getStudentName('studentdailymaster', 'studentId', 'studentDailyMasterId', $evaluationId);

    // Send SMS to Preceptor
    $body = 'Hello, You received a ' . $isEvalTypeLabel . ' Message request from student ' . $studentNameForSms . '. Please Click below link and submit your Evaluation. ' . $redirectUrl;


    sendSMS('+' . $countryCode . $preceptorNum, $body);
} else if ($evaluationType == 'formative') {
    $objDB = new clsDB();
    $objDB->UpdateSingleColumnValueToTable('studentformativemaster', 'preceptorId', $preceptorId, 'studentFormativeMasterId', $evaluationId);
    unset($objDB);

    $isEvalType = 2;
    $isEvalTypeLabel = 'Formative Evaluation';

    $URL = $dynamicOrgUrl . '/school/' . $schoolSlug . '/clinician/formative.html?studentFormativeMasterId=' . EncodeQueryData($evaluationId) . '&formativerotationid=' . EncodeQueryData($rotationId) . '&preceptorNum=' . EncodeQueryData($preceptorNum) . '&preceptorId=' . EncodeQueryData($preceptorId) . '&isEvalType=' . EncodeQueryData($isEvalType);
    $randomUrl = getTinyUrl($URL);
    $redirectUrl = $dynamicOrgUrl . '/redirect/' . EncodeQueryData($randomUrl);
    // Get Student Name
    $studentNameForSms = getStudentName('studentformativemaster', 'studentId', 'studentFormativeMasterId', $evaluationId);

    // Send SMS to Preceptor
    $body = 'Hello, You received a ' . $isEvalTypeLabel . ' Message request from student ' . $studentNameForSms . '. Please Click below link and submit your Evaluation. ' . $redirectUrl;


    sendSMS('+' . $countryCode . $preceptorNum, $body);
} else if ($evaluationType == 'summative') {
    $objDB = new clsDB();
    $objDB->UpdateSingleColumnValueToTable('studentsummativemaster', 'preceptorId', $preceptorId, 'studentSummativeMasterId', $evaluationId);
    unset($objDB);

    $isEvalType = 2;
    $isEvalTypeLabel = 'Summative Evaluation';

    $URL = $dynamicOrgUrl . '/school/' . $schoolSlug . '/clinician/summative.html?studentSummativeMasterId=' . EncodeQueryData($evaluationId) . '&summativerotationid=' . EncodeQueryData($rotationId) . '&preceptorNum=' . EncodeQueryData($preceptorNum) . '&preceptorId=' . EncodeQueryData($preceptorId) . '&isEvalType=' . EncodeQueryData($isEvalType);
    $randomUrl = getTinyUrl($URL);
    $redirectUrl = $dynamicOrgUrl . '/redirect/' . EncodeQueryData($randomUrl);
    // Get Student Name
    $studentNameForSms = getStudentName('studentsummativemaster', 'studentId', 'studentSummativeMasterId', $evaluationId);

    // Send SMS to Preceptor
    $body = 'Hello, You received a ' . $isEvalTypeLabel . ' Message request from student ' . $studentNameForSms . '. Please Click below link and submit your Evaluation. ' . $redirectUrl;


    sendSMS('+' . $countryCode . $preceptorNum, $body);
} else if ($evaluationType == 'midterm') {
    $objDB = new clsDB();
    $objDB->UpdateSingleColumnValueToTable('studentmidtermmaster', 'preceptorId', $preceptorId, 'studentMidtermMasterId', $evaluationId);
    unset($objDB);

    $isEvalType = 2;
    $isEvalTypeLabel = 'Midterm Evaluation';

    $URL = $dynamicOrgUrl . '/school/' . $schoolSlug . '/clinician/midterm.html?studentMidtermMasterId=' . EncodeQueryData($evaluationId) . '&midtermrotationid=' . EncodeQueryData($rotationId) . '&preceptorNum=' . EncodeQueryData($preceptorNum) . '&preceptorId=' . EncodeQueryData($preceptorId) . '&isEvalType=' . EncodeQueryData($isEvalType);
    $randomUrl = getTinyUrl($URL);
    $redirectUrl = $dynamicOrgUrl . '/redirect/' . EncodeQueryData($randomUrl);
    // Get Student Name
    $studentNameForSms = getStudentName('studentmidtermmaster', 'studentId', 'studentMidtermMasterId', $evaluationId);

    // Send SMS to Preceptor
    $body = 'Hello, You received a ' . $isEvalTypeLabel . ' Message request from student ' . $studentNameForSms . '. Please Click below link and submit your Evaluation. ' . $redirectUrl;

    sendSMS('+'. $countryCode . $preceptorNum, $body);
} else if ($evaluationType == 'checkoff') {
    $objDB = new clsDB();
    $objDB->UpdateSingleColumnValueToTable('checkoff', 'completion1stPreceptorId', $preceptorId, 'checkoffId', $checkoffId);
    $studentId = $objDB->GetSingleColumnValueFromTable('checkoff', 'studentId', 'checkoffId', $checkoffId);
    unset($objDB);
    if ($isActiveCheckoff == 2)
        $URL = $dynamicOrgUrl . '/school/' . $schoolSlug . '/student/addusafcheckoff.html?schoolTopicId=' . EncodeQueryData($schoolTopicId) . '&checkoffId=' . EncodeQueryData($checkoffId) . '&preceptorNum=' . EncodeQueryData($preceptorNum) . '&preceptorId=' . EncodeQueryData($preceptorId);
    else
        $URL = $dynamicOrgUrl . '/school/' . $schoolSlug . '/student/addcheckoff.html?schoolTopicId=' . EncodeQueryData($schoolTopicId) . '&checkoffId=' . EncodeQueryData($checkoffId) . '&preceptorNum=' . EncodeQueryData($preceptorNum) . '&preceptorId=' . EncodeQueryData($preceptorId);

    // Create TinyUrl
    $randomUrl = getTinyUrl($URL);
    $redirectUrl = $dynamicOrgUrl . '/redirect/' . EncodeQueryData($randomUrl);
    // Get Student Name
    $studentNameForSms = getStudentName('checkoff', 'studentId', 'checkoffId', $checkoffId);

    // Send SMS to Preceptor
    $body = 'Hello, You received a Student ' . $studentNameForSms . ' Checkoff Message request. Click the link below to complete. ' . $redirectUrl;

    sendSMS('+'. $countryCode . $preceptorNum, $body);
} else if ($evaluationType == 'floorTherapy') {
    $objDB = new clsDB();
    $objDB->UpdateSingleColumnValueToTable('studentfloortherapyandicuevalmaster', 'preceptorId', $preceptorId, 'studentMasterId', $evaluationId);
    unset($objDB);

    $isEvalType = 0;

    $isEvalTypeLabel = 'Floor Therapy Evaluation';

    $URL = $dynamicOrgUrl . '/school/' . $schoolSlug . '/clinician/addFloorTherapyAndICUEvaluation.html?studentMasterId=' . EncodeQueryData($evaluationId) . '&rotationId=' . EncodeQueryData($rotationId) . '&preceptorNum=' . EncodeQueryData($preceptorNum) . '&isEvalType=' . EncodeQueryData($isEvalType) . '&preceptorId=' . EncodeQueryData($preceptorId);

    // Create TinyUrl
    $randomUrl = getTinyUrl($URL);
    $redirectUrl = $dynamicOrgUrl . '/redirect/' . EncodeQueryData($randomUrl);
    // Get Student Name
    $studentNameForSms = getStudentName('studentfloortherapyandicuevalmaster', 'studentId', 'studentMasterId', $evaluationId);

    // Send SMS to Preceptor
    $body = 'Hello, You received a ' . $isEvalTypeLabel . ' Message request from student ' . $studentNameForSms . '. Please Click below link and submit your Evaluation. ' . $redirectUrl;

    sendSMS('+'. $countryCode . $preceptorNum, $body);
} else if ($evaluationType == 'activitySheet') {

    // echo 'hi';
    // exit;
    $objDB = new clsDB();
    $objDB->UpdateSingleColumnValueToTable('preceptordetails', 'preceptorId', $preceptorId, 'referenceId', $activityId);
    unset($objDB);

    $isEvalType = 0;

    $isEvalTypeLabel = 'Activity Sheet';

    $URL = $dynamicOrgUrl . '/school/' . $schoolSlug . '/student/activitySheet.html?editactivityid=' . EncodeQueryData($activityId) . '&rotationId=' . EncodeQueryData($rotationId) . '&preceptorNum=' . EncodeQueryData($preceptorNum) .  '&isEvalType=' . EncodeQueryData($isEvalType) . '&preceptorId=' . EncodeQueryData($preceptorId) . '&isSendToExternalPreceptor=1';

    // Create TinyUrl
    $randomUrl = getTinyUrl($URL);
    $redirectUrl = $dynamicOrgUrl . '/redirect/' . EncodeQueryData($randomUrl);
    // Get Student Name
    $studentNameForSms = getStudentName('activitysheetdetails', 'studentId', 'activityId', $activityId);

    // Send SMS to Preceptor
    $body = 'Hello, You received a ' . $isEvalTypeLabel . ' Message request from student ' . $studentNameForSms . '. Please Click below link and submit your Activity Sheet. ' . $redirectUrl;

    sendSMS('+' . $countryCode . $preceptorNum, $body);
}else if ($evaluationType == 'soapnote') {
//   echo $evaluationType; exit;
    // echo 'hi';
    // exit;
    $objDB = new clsDB();
    $objDB->UpdateSingleColumnValueToTable('preceptordetails', 'preceptorId', $preceptorId, 'referenceId', $soapNoteId);
    unset($objDB);

    $isEvalType = 0;

    $isEvalTypeLabel = 'Soap Note';

    $URL = $dynamicOrgUrl . '/school/' . $schoolSlug . '/student/addSoapNote.html?editsoapNoteId=' . EncodeQueryData($soapNoteId) . '&rotationId=' . EncodeQueryData($rotationId) . '&preceptorNum=' . EncodeQueryData($preceptorNum) .  '&isEvalType=' . EncodeQueryData($isEvalType) . '&preceptorId=' . EncodeQueryData($preceptorId) . '&isSendToExternalPreceptor=1';

    // Create TinyUrl
    $randomUrl = getTinyUrl($URL);
    $redirectUrl = $dynamicOrgUrl . '/redirect/' . EncodeQueryData($randomUrl);
    // echo $redirectUrl; exit;
    // Get Student Name
    $studentNameForSms = getStudentName('soapnotedetails', 'studentId', 'soapNoteId', $soapNoteId);

    // Send SMS to Preceptor
    $body = 'Hello, You received a ' . $isEvalTypeLabel . ' Message request from student ' . $studentNameForSms . '. Please Click below link and submit your Activity Sheet. ' . $redirectUrl;

    sendSMS('+' . $countryCode . $preceptorNum, $body);
}



