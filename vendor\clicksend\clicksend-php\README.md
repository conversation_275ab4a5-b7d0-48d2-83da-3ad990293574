# ClickSendClient-php
This is an official SDK for [ClickSend](https://clicksend.com)  Below you will find a current list of the available methods for clicksend.  *NOTE: You will need to create a free account to use the API. You can register [here](https://dashboard.clicksend.com/#/signup/step1/)..*

This PHP package is automatically generated by the [ClickSend Codegen](https://github.com/swagger-api/swagger-codegen) project:

- API version: 3.1
- Build package: io.swagger.codegen.v3.generators.php.PhpClientCodegen

## Requirements

PHP 5.5 and later

## Installation & Usage
### Composer

To install the bindings via [Composer](http://getcomposer.org/), add the following to `composer.json`:

```
{
  "repositories": [
    {
      "type": "git",
      "url": "https://github.com/git_user_id/git_repo_id.git"
    }
  ],
  "require": {
    "git_user_id/git_repo_id": "*@dev"
  }
}
```

Then run `composer install`

### Manual Installation

Download the files and include `autoload.php`:

```php
    require_once('/path/to/ClickSendClient-php/vendor/autoload.php');
```

## Tests

To run the unit tests:

```
composer install
./vendor/bin/phpunit
```

## Getting Started

Please follow the [installation procedure](#installation--usage) and then run the following:

```php
<?php
require_once(__DIR__ . '/vendor/autoload.php');
// Configure HTTP basic authorization: BasicAuth
$config = ClickSend\Configuration::getDefaultConfiguration()
    ->setUsername('YOUR_USERNAME')
    ->setPassword('YOUR_PASSWORD');

$apiInstance = new ClickSend\Api\AccountApi(
    // If you want use custom http client, pass your client which implements `GuzzleHttp\ClientInterface`.
    // This is optional, `GuzzleHttp\Client` will be used as default.
    new GuzzleHttp\Client(),
    $config
);

try {
    $result = $apiInstance->accountGet();
    print_r($result);
} catch (Exception $e) {
    echo 'Exception when calling AccountApi->accountGet: ', $e->getMessage(), PHP_EOL;
}
// Configure HTTP basic authorization: BasicAuth
$config = ClickSend\Configuration::getDefaultConfiguration()
    ->setUsername('YOUR_USERNAME')
    ->setPassword('YOUR_PASSWORD');

$apiInstance = new ClickSend\Api\AccountApi(
    // If you want use custom http client, pass your client which implements `GuzzleHttp\ClientInterface`.
    // This is optional, `GuzzleHttp\Client` will be used as default.
    new GuzzleHttp\Client(),
    $config
);
$body = new \ClickSend\Model\Account(); // \ClickSend\Model\Account | Account model

try {
    $result = $apiInstance->accountPost($body);
    print_r($result);
} catch (Exception $e) {
    echo 'Exception when calling AccountApi->accountPost: ', $e->getMessage(), PHP_EOL;
}
// Configure HTTP basic authorization: BasicAuth
$config = ClickSend\Configuration::getDefaultConfiguration()
    ->setUsername('YOUR_USERNAME')
    ->setPassword('YOUR_PASSWORD');

$apiInstance = new ClickSend\Api\AccountApi(
    // If you want use custom http client, pass your client which implements `GuzzleHttp\ClientInterface`.
    // This is optional, `GuzzleHttp\Client` will be used as default.
    new GuzzleHttp\Client(),
    $config
);
$year = 56; // int | Year to filter by (yyyy)
$month = 56; // int | Month to filter by (mm)

try {
    $result = $apiInstance->accountUseageBySubaccountGet($year, $month);
    print_r($result);
} catch (Exception $e) {
    echo 'Exception when calling AccountApi->accountUseageBySubaccountGet: ', $e->getMessage(), PHP_EOL;
}
// Configure HTTP basic authorization: BasicAuth
$config = ClickSend\Configuration::getDefaultConfiguration()
    ->setUsername('YOUR_USERNAME')
    ->setPassword('YOUR_PASSWORD');

$apiInstance = new ClickSend\Api\AccountApi(
    // If you want use custom http client, pass your client which implements `GuzzleHttp\ClientInterface`.
    // This is optional, `GuzzleHttp\Client` will be used as default.
    new GuzzleHttp\Client(),
    $config
);
$body = new \ClickSend\Model\AccountVerify(); // \ClickSend\Model\AccountVerify | Account details

try {
    $result = $apiInstance->accountVerifySendPut($body);
    print_r($result);
} catch (Exception $e) {
    echo 'Exception when calling AccountApi->accountVerifySendPut: ', $e->getMessage(), PHP_EOL;
}
// Configure HTTP basic authorization: BasicAuth
$config = ClickSend\Configuration::getDefaultConfiguration()
    ->setUsername('YOUR_USERNAME')
    ->setPassword('YOUR_PASSWORD');

$apiInstance = new ClickSend\Api\AccountApi(
    // If you want use custom http client, pass your client which implements `GuzzleHttp\ClientInterface`.
    // This is optional, `GuzzleHttp\Client` will be used as default.
    new GuzzleHttp\Client(),
    $config
);
$activation_token = 56; // int | 

try {
    $result = $apiInstance->accountVerifyVerifyByActivationTokenPut($activation_token);
    print_r($result);
} catch (Exception $e) {
    echo 'Exception when calling AccountApi->accountVerifyVerifyByActivationTokenPut: ', $e->getMessage(), PHP_EOL;
}
// Configure HTTP basic authorization: BasicAuth
$config = ClickSend\Configuration::getDefaultConfiguration()
    ->setUsername('YOUR_USERNAME')
    ->setPassword('YOUR_PASSWORD');

$apiInstance = new ClickSend\Api\AccountApi(
    // If you want use custom http client, pass your client which implements `GuzzleHttp\ClientInterface`.
    // This is optional, `GuzzleHttp\Client` will be used as default.
    new GuzzleHttp\Client(),
    $config
);
$body = new \ClickSend\Model\ForgotPassword(); // \ClickSend\Model\ForgotPassword | 

try {
    $result = $apiInstance->forgotPasswordPut($body);
    print_r($result);
} catch (Exception $e) {
    echo 'Exception when calling AccountApi->forgotPasswordPut: ', $e->getMessage(), PHP_EOL;
}
// Configure HTTP basic authorization: BasicAuth
$config = ClickSend\Configuration::getDefaultConfiguration()
    ->setUsername('YOUR_USERNAME')
    ->setPassword('YOUR_PASSWORD');

$apiInstance = new ClickSend\Api\AccountApi(
    // If you want use custom http client, pass your client which implements `GuzzleHttp\ClientInterface`.
    // This is optional, `GuzzleHttp\Client` will be used as default.
    new GuzzleHttp\Client(),
    $config
);
$body = new \ClickSend\Model\AccountForgotPasswordVerify(); // \ClickSend\Model\AccountForgotPasswordVerify | verifyPassword data

try {
    $result = $apiInstance->forgotPasswordVerifyPut($body);
    print_r($result);
} catch (Exception $e) {
    echo 'Exception when calling AccountApi->forgotPasswordVerifyPut: ', $e->getMessage(), PHP_EOL;
}

$apiInstance = new ClickSend\Api\AccountApi(
    // If you want use custom http client, pass your client which implements `GuzzleHttp\ClientInterface`.
    // This is optional, `GuzzleHttp\Client` will be used as default.
    new GuzzleHttp\Client()
);
$body = new \ClickSend\Model\ForgotUsername(); // \ClickSend\Model\ForgotUsername | 

try {
    $result = $apiInstance->forgotUsernamePut($body);
    print_r($result);
} catch (Exception $e) {
    echo 'Exception when calling AccountApi->forgotUsernamePut: ', $e->getMessage(), PHP_EOL;
}
?>
```

## Documentation for API Endpoints

All URIs are relative to *https://rest.clicksend.com/v3*

Class | Method | HTTP request | Description
------------ | ------------- | ------------- | -------------
*AccountApi* | [**accountGet**](docs/Api/AccountApi.md#accountget) | **GET** /account | Get account information
*AccountApi* | [**accountPost**](docs/Api/AccountApi.md#accountpost) | **POST** /account | Create a new account
*AccountApi* | [**accountUseageBySubaccountGet**](docs/Api/AccountApi.md#accountuseagebysubaccountget) | **GET** /account/usage/{year}/{month}/subaccount | Get account useage by subaccount
*AccountApi* | [**accountVerifySendPut**](docs/Api/AccountApi.md#accountverifysendput) | **PUT** /account-verify/send | Send account activation token
*AccountApi* | [**accountVerifyVerifyByActivationTokenPut**](docs/Api/AccountApi.md#accountverifyverifybyactivationtokenput) | **PUT** /account-verify/verify/{activation_token} | Verify new account
*AccountApi* | [**forgotPasswordPut**](docs/Api/AccountApi.md#forgotpasswordput) | **PUT** /forgot-password | Forgot password
*AccountApi* | [**forgotPasswordVerifyPut**](docs/Api/AccountApi.md#forgotpasswordverifyput) | **PUT** /forgot-password/verify | Verify forgot password
*AccountApi* | [**forgotUsernamePut**](docs/Api/AccountApi.md#forgotusernameput) | **PUT** /forgot-username | Forgot username
*AccountRechargeApi* | [**rechargeCreditCardGet**](docs/Api/AccountRechargeApi.md#rechargecreditcardget) | **GET** /recharge/credit-card | Get Credit Card info
*AccountRechargeApi* | [**rechargeCreditCardPut**](docs/Api/AccountRechargeApi.md#rechargecreditcardput) | **PUT** /recharge/credit-card | Update credit card info
*AccountRechargeApi* | [**rechargePackagesGet**](docs/Api/AccountRechargeApi.md#rechargepackagesget) | **GET** /recharge/packages | Get list of all packages
*AccountRechargeApi* | [**rechargePurchaseByPackageIdPut**](docs/Api/AccountRechargeApi.md#rechargepurchasebypackageidput) | **PUT** /recharge/purchase/{package_id} | Purchase a package
*AccountRechargeApi* | [**rechargeTransactionsByTransactionIdGet**](docs/Api/AccountRechargeApi.md#rechargetransactionsbytransactionidget) | **GET** /recharge/transactions/{transaction_id} | Get specific Transaction
*AccountRechargeApi* | [**rechargeTransactionsGet**](docs/Api/AccountRechargeApi.md#rechargetransactionsget) | **GET** /recharge/transactions | Purchase a package
*ContactApi* | [**listsContactsByListIdAndContactIdDelete**](docs/Api/ContactApi.md#listscontactsbylistidandcontactiddelete) | **DELETE** /lists/{list_id}/contacts/{contact_id} | Delete a contact
*ContactApi* | [**listsContactsByListIdAndContactIdGet**](docs/Api/ContactApi.md#listscontactsbylistidandcontactidget) | **GET** /lists/{list_id}/contacts/{contact_id} | Get a specific contact
*ContactApi* | [**listsContactsByListIdAndContactIdPut**](docs/Api/ContactApi.md#listscontactsbylistidandcontactidput) | **PUT** /lists/{list_id}/contacts/{contact_id} | Update specific contact
*ContactApi* | [**listsContactsByListIdGet**](docs/Api/ContactApi.md#listscontactsbylistidget) | **GET** /lists/{list_id}/contacts | Get all contacts in a list
*ContactApi* | [**listsContactsByListIdPost**](docs/Api/ContactApi.md#listscontactsbylistidpost) | **POST** /lists/{list_id}/contacts | Create new contact
*ContactApi* | [**listsCopyContactPut**](docs/Api/ContactApi.md#listscopycontactput) | **PUT** /lists/{from_list_id}/contacts/{contact_id}/copy/{to_list_id} | Copy contact to another list
*ContactApi* | [**listsRemoveOptedOutContactsByListIdAndOptOutListIdPut**](docs/Api/ContactApi.md#listsremoveoptedoutcontactsbylistidandoptoutlistidput) | **PUT** /lists/{list_id}/remove-opted-out-contacts/{opt_out_list_id} | Remove all opted out contacts
*ContactApi* | [**listsTransferContactPut**](docs/Api/ContactApi.md#liststransfercontactput) | **PUT** /lists/{from_list_id}/contacts/{contact_id}/transfer/{to_list_id} | Transfer contact to another list
*ContactListApi* | [**listsByListIdDelete**](docs/Api/ContactListApi.md#listsbylistiddelete) | **DELETE** /lists/{list_id} | ListsByListIdDelete
*ContactListApi* | [**listsByListIdGet**](docs/Api/ContactListApi.md#listsbylistidget) | **GET** /lists/{list_id} | Get specific contact list
*ContactListApi* | [**listsByListIdPut**](docs/Api/ContactListApi.md#listsbylistidput) | **PUT** /lists/{list_id} | Update specific contact list
*ContactListApi* | [**listsGet**](docs/Api/ContactListApi.md#listsget) | **GET** /lists | Get all contact lists
*ContactListApi* | [**listsImportByListIdPost**](docs/Api/ContactListApi.md#listsimportbylistidpost) | **POST** /lists/{list_id}/import | Import contacts to list
*ContactListApi* | [**listsPost**](docs/Api/ContactListApi.md#listspost) | **POST** /lists | Create new contact list
*ContactListApi* | [**listsRemoveDuplicatesByListIdPut**](docs/Api/ContactListApi.md#listsremoveduplicatesbylistidput) | **PUT** /lists/{list_id}/remove-duplicates | Remove duplicate contacts
*CountriesApi* | [**countriesGet**](docs/Api/CountriesApi.md#countriesget) | **GET** /countries | Get all country codes
*DeliveryIssuesApi* | [**deliveryIssuesGet**](docs/Api/DeliveryIssuesApi.md#deliveryissuesget) | **GET** /delivery-issues | Get all delivery issues
*DeliveryIssuesApi* | [**deliveryIssuesPost**](docs/Api/DeliveryIssuesApi.md#deliveryissuespost) | **POST** /delivery-issues | Create delivery Issue
*DetectAddressApi* | [**detectAddressPost**](docs/Api/DetectAddressApi.md#detectaddresspost) | **POST** /post/letters/detect-address | Detects address in uploaded file.
*EmailDeliveryReceiptRulesApi* | [**emailDeliveryReceiptAutomationDelete**](docs/Api/EmailDeliveryReceiptRulesApi.md#emaildeliveryreceiptautomationdelete) | **DELETE** /automations/email/receipts/{receipt_rule_id} | Delete email delivery receipt automation
*EmailDeliveryReceiptRulesApi* | [**emailDeliveryReceiptAutomationGet**](docs/Api/EmailDeliveryReceiptRulesApi.md#emaildeliveryreceiptautomationget) | **GET** /automations/email/receipts/{receipt_rule_id} | Get specific email delivery receipt automation
*EmailDeliveryReceiptRulesApi* | [**emailDeliveryReceiptAutomationPost**](docs/Api/EmailDeliveryReceiptRulesApi.md#emaildeliveryreceiptautomationpost) | **POST** /automations/email/receipts | Create email delivery receipt automations
*EmailDeliveryReceiptRulesApi* | [**emailDeliveryReceiptAutomationPut**](docs/Api/EmailDeliveryReceiptRulesApi.md#emaildeliveryreceiptautomationput) | **PUT** /automations/email/receipts/{receipt_rule_id} | Update email delivery receipt automation
*EmailDeliveryReceiptRulesApi* | [**emailDeliveryReceiptAutomationsGet**](docs/Api/EmailDeliveryReceiptRulesApi.md#emaildeliveryreceiptautomationsget) | **GET** /automations/email/receipts | Get all email delivery receipt automations
*EmailMarketingApi* | [**allowedEmailAddressGet**](docs/Api/EmailMarketingApi.md#allowedemailaddressget) | **GET** /email/addresses | Get all email addresses
*EmailMarketingApi* | [**allowedEmailAddressPost**](docs/Api/EmailMarketingApi.md#allowedemailaddresspost) | **POST** /email/addresses | Create allowed Email Address
*EmailMarketingApi* | [**cancelEmailCampaignPut**](docs/Api/EmailMarketingApi.md#cancelemailcampaignput) | **PUT** /email-campaigns/{email_campaign_id}/cancel | Cancel email campaign
*EmailMarketingApi* | [**emailCampaignGet**](docs/Api/EmailMarketingApi.md#emailcampaignget) | **GET** /email-campaigns/{email_campaign_id} | Get specific email campaign
*EmailMarketingApi* | [**emailCampaignHistoryExportGet**](docs/Api/EmailMarketingApi.md#emailcampaignhistoryexportget) | **GET** /email-campaigns/{email_campaign_id}/history/export | Export specific email campaign history
*EmailMarketingApi* | [**emailCampaignHistoryGet**](docs/Api/EmailMarketingApi.md#emailcampaignhistoryget) | **GET** /email-campaigns/{email_campaign_id}/history | Get specific email campaign history
*EmailMarketingApi* | [**emailCampaignPost**](docs/Api/EmailMarketingApi.md#emailcampaignpost) | **POST** /email-campaigns/send | Send email campaign
*EmailMarketingApi* | [**emailCampaignPricePost**](docs/Api/EmailMarketingApi.md#emailcampaignpricepost) | **POST** /email-campaigns/price | Calculate email campaign price
*EmailMarketingApi* | [**emailCampaignPut**](docs/Api/EmailMarketingApi.md#emailcampaignput) | **PUT** /email-campaigns/{email_campaign_id} | Edit email campaign
*EmailMarketingApi* | [**emailCampaignsGet**](docs/Api/EmailMarketingApi.md#emailcampaignsget) | **GET** /email-campaigns | Get all email campaigns
*EmailMarketingApi* | [**sendVerificationTokenGet**](docs/Api/EmailMarketingApi.md#sendverificationtokenget) | **PUT** /email/address-verify/{email_address_id}/send | Send verification token
*EmailMarketingApi* | [**specificAllowedEmailAddressDelete**](docs/Api/EmailMarketingApi.md#specificallowedemailaddressdelete) | **DELETE** /email/addresses/{email_address_id} | Delete specific email address
*EmailMarketingApi* | [**specificAllowedEmailAddressGet**](docs/Api/EmailMarketingApi.md#specificallowedemailaddressget) | **GET** /email/addresses/{email_address_id} | Get specific email address
*EmailMarketingApi* | [**verifyAllowedEmailAddressGet**](docs/Api/EmailMarketingApi.md#verifyallowedemailaddressget) | **PUT** /email/address-verify/{email_address_id}/verify/{activation_token} | Verify email address using verification token
*EmailToSmsApi* | [**smsEmailSmsGet**](docs/Api/EmailToSmsApi.md#smsemailsmsget) | **GET** /sms/email-sms | Get list of email to sms allowed addresses
*EmailToSmsApi* | [**smsEmailSmsPost**](docs/Api/EmailToSmsApi.md#smsemailsmspost) | **POST** /sms/email-sms | Create email to sms allowed address
*EmailToSmsApi* | [**smsEmailSmsStrippedStringDelete**](docs/Api/EmailToSmsApi.md#smsemailsmsstrippedstringdelete) | **DELETE** /sms/email-sms-stripped-strings/{rule_id} | Delete email to sms stripped string rule
*EmailToSmsApi* | [**smsEmailSmsStrippedStringGet**](docs/Api/EmailToSmsApi.md#smsemailsmsstrippedstringget) | **GET** /sms/email-sms-stripped-strings/{rule_id} | Get email to sms stripped string rule
*EmailToSmsApi* | [**smsEmailSmsStrippedStringPost**](docs/Api/EmailToSmsApi.md#smsemailsmsstrippedstringpost) | **POST** /sms/email-sms-stripped-strings | Create email to sms stripped string rule
*EmailToSmsApi* | [**smsEmailSmsStrippedStringPut**](docs/Api/EmailToSmsApi.md#smsemailsmsstrippedstringput) | **PUT** /sms/email-sms-stripped-strings/{rule_id} | Update email to sms stripped string rule
*EmailToSmsApi* | [**smsEmailSmsStrippedStringsGet**](docs/Api/EmailToSmsApi.md#smsemailsmsstrippedstringsget) | **GET** /sms/email-sms-stripped-strings | Get list of email to sms stripped string rules
*FAXApi* | [**faxHistoryGet**](docs/Api/FAXApi.md#faxhistoryget) | **GET** /fax/history | Get a list of Fax History.
*FAXApi* | [**faxPricePost**](docs/Api/FAXApi.md#faxpricepost) | **POST** /fax/price | Calculate Total Price for Fax Messages sent
*FAXApi* | [**faxReceiptsByMessageIdGet**](docs/Api/FAXApi.md#faxreceiptsbymessageidget) | **GET** /fax/receipts/{message_id} | Get a single fax receipt based on message id.
*FAXApi* | [**faxReceiptsGet**](docs/Api/FAXApi.md#faxreceiptsget) | **GET** /fax/receipts | Get all delivery receipts
*FAXApi* | [**faxReceiptsPost**](docs/Api/FAXApi.md#faxreceiptspost) | **POST** /fax/receipts | Add a delivery receipt
*FAXApi* | [**faxReceiptsReadPut**](docs/Api/FAXApi.md#faxreceiptsreadput) | **PUT** /fax/receipts-read | Mark delivery receipts as read
*FAXApi* | [**faxSendPost**](docs/Api/FAXApi.md#faxsendpost) | **POST** /fax/send | Send a fax using supplied supported file-types.
*FAXDeliveryReceiptRulesApi* | [**faxDeliveryReceiptAutomationDelete**](docs/Api/FAXDeliveryReceiptRulesApi.md#faxdeliveryreceiptautomationdelete) | **DELETE** /automations/fax/receipts/{receipt_rule_id} | Delete fax delivery receipt automation
*FAXDeliveryReceiptRulesApi* | [**faxDeliveryReceiptAutomationGet**](docs/Api/FAXDeliveryReceiptRulesApi.md#faxdeliveryreceiptautomationget) | **GET** /automations/fax/receipts/{receipt_rule_id} | Get specific fax delivery receipt automation
*FAXDeliveryReceiptRulesApi* | [**faxDeliveryReceiptAutomationPost**](docs/Api/FAXDeliveryReceiptRulesApi.md#faxdeliveryreceiptautomationpost) | **POST** /automations/fax/receipts | Create fax delivery receipt automations
*FAXDeliveryReceiptRulesApi* | [**faxDeliveryReceiptAutomationPut**](docs/Api/FAXDeliveryReceiptRulesApi.md#faxdeliveryreceiptautomationput) | **PUT** /automations/fax/receipts/{receipt_rule_id} | Update fax delivery receipt automation
*FAXDeliveryReceiptRulesApi* | [**faxDeliveryReceiptAutomationsGet**](docs/Api/FAXDeliveryReceiptRulesApi.md#faxdeliveryreceiptautomationsget) | **GET** /automations/fax/receipts | Get all fax delivery receipt automations
*GlobalSendingApi* | [**listCountriesGet**](docs/Api/GlobalSendingApi.md#listcountriesget) | **GET** /country-list | List of countries
*GlobalSendingApi* | [**userCountriesAgreePost**](docs/Api/GlobalSendingApi.md#usercountriesagreepost) | **POST** /user-countries/agree | Agree to rules and regulation
*GlobalSendingApi* | [**userCountriesGet**](docs/Api/GlobalSendingApi.md#usercountriesget) | **GET** /user-countries | Get Countries for Global Sending
*GlobalSendingApi* | [**userCountriesPost**](docs/Api/GlobalSendingApi.md#usercountriespost) | **POST** /user-countries | Select Countries for Global Sending
*InboundFAXRulesApi* | [**faxInboundAutomationDelete**](docs/Api/InboundFAXRulesApi.md#faxinboundautomationdelete) | **DELETE** /automations/fax/inbound/{inbound_rule_id} | Delete inbound fax automation
*InboundFAXRulesApi* | [**faxInboundAutomationGet**](docs/Api/InboundFAXRulesApi.md#faxinboundautomationget) | **GET** /automations/fax/inbound/{inbound_rule_id} | Get specific inbound fax automation
*InboundFAXRulesApi* | [**faxInboundAutomationPost**](docs/Api/InboundFAXRulesApi.md#faxinboundautomationpost) | **POST** /automations/fax/inbound | Create new inbound fax automation
*InboundFAXRulesApi* | [**faxInboundAutomationPut**](docs/Api/InboundFAXRulesApi.md#faxinboundautomationput) | **PUT** /automations/fax/inbound/{inbound_rule_id} | Update inbound fax automation
*InboundFAXRulesApi* | [**faxInboundAutomationsGet**](docs/Api/InboundFAXRulesApi.md#faxinboundautomationsget) | **GET** /automations/fax/inbound | Get all inbound fax automations
*InboundSMSRulesApi* | [**smsInboundAutomationDelete**](docs/Api/InboundSMSRulesApi.md#smsinboundautomationdelete) | **DELETE** /automations/sms/inbound/{inbound_rule_id} | Delete inbound sms automation
*InboundSMSRulesApi* | [**smsInboundAutomationGet**](docs/Api/InboundSMSRulesApi.md#smsinboundautomationget) | **GET** /automations/sms/inbound/{inbound_rule_id} | Get specific inbound sms automation
*InboundSMSRulesApi* | [**smsInboundAutomationPost**](docs/Api/InboundSMSRulesApi.md#smsinboundautomationpost) | **POST** /automations/sms/inbound | Create new inbound sms automation
*InboundSMSRulesApi* | [**smsInboundAutomationPut**](docs/Api/InboundSMSRulesApi.md#smsinboundautomationput) | **PUT** /automations/sms/inbound/{inbound_rule_id} | Update inbound sms automation
*InboundSMSRulesApi* | [**smsInboundAutomationsGet**](docs/Api/InboundSMSRulesApi.md#smsinboundautomationsget) | **GET** /automations/sms/inbound | Get all inbound sms automations
*MMSApi* | [**mmsHistoryExportGet**](docs/Api/MMSApi.md#mmshistoryexportget) | **GET** /mms/history/export | Export all mms history
*MMSApi* | [**mmsHistoryGet**](docs/Api/MMSApi.md#mmshistoryget) | **GET** /mms/history | Get all mms history
*MMSApi* | [**mmsPricePost**](docs/Api/MMSApi.md#mmspricepost) | **POST** /mms/price | Get Price for MMS sent
*MMSApi* | [**mmsReceiptsGet**](docs/Api/MMSApi.md#mmsreceiptsget) | **GET** /mms/receipts | Get all delivery receipts
*MMSApi* | [**mmsReceiptsReadPut**](docs/Api/MMSApi.md#mmsreceiptsreadput) | **PUT** /mms/receipts-read | Mark delivery receipts as read
*MMSApi* | [**mmsSendPost**](docs/Api/MMSApi.md#mmssendpost) | **POST** /mms/send | Send MMS
*MasterEmailTemplatesApi* | [**masterEmailTemplateCategoriesGet**](docs/Api/MasterEmailTemplatesApi.md#masteremailtemplatecategoriesget) | **GET** /email/master-templates-categories | Get all master email template categories
*MasterEmailTemplatesApi* | [**masterEmailTemplateCategoryGet**](docs/Api/MasterEmailTemplatesApi.md#masteremailtemplatecategoryget) | **GET** /email/master-templates-categories/{category_id} | Get specific master email template category
*MasterEmailTemplatesApi* | [**masterEmailTemplateGet**](docs/Api/MasterEmailTemplatesApi.md#masteremailtemplateget) | **GET** /email/master-templates/{template_id} | Get specific master email template
*MasterEmailTemplatesApi* | [**masterEmailTemplatesGet**](docs/Api/MasterEmailTemplatesApi.md#masteremailtemplatesget) | **GET** /email/master-templates | Get all master email templates
*MasterEmailTemplatesApi* | [**masterEmailTemplatesInCategoryGet**](docs/Api/MasterEmailTemplatesApi.md#masteremailtemplatesincategoryget) | **GET** /email/master-templates-categories/{category_id}/master-templates | Get all master email templates in a category
*MmsCampaignApi* | [**mmsCampaignByMmsCampaignIdGet**](docs/Api/MmsCampaignApi.md#mmscampaignbymmscampaignidget) | **GET** /mms-campaigns/{mms_campaign_id} | Get specific mms campaign
*MmsCampaignApi* | [**mmsCampaignsByMmsCampaignIdPut**](docs/Api/MmsCampaignApi.md#mmscampaignsbymmscampaignidput) | **PUT** /mms-campaigns/{mms_campaign_id} | Update mms campaign
*MmsCampaignApi* | [**mmsCampaignsCancelByMmsCampaignIdPut**](docs/Api/MmsCampaignApi.md#mmscampaignscancelbymmscampaignidput) | **PUT** /mms-campaigns/{mms_campaign_id}/cancel | Cancel mms campaign
*MmsCampaignApi* | [**mmsCampaignsGet**](docs/Api/MmsCampaignApi.md#mmscampaignsget) | **GET** /mms-campaigns | Get list of mms campaigns
*MmsCampaignApi* | [**mmsCampaignsPricePost**](docs/Api/MmsCampaignApi.md#mmscampaignspricepost) | **POST** /mms-campaigns/price | Calculate price for mms campaign
*MmsCampaignApi* | [**mmsCampaignsSendPost**](docs/Api/MmsCampaignApi.md#mmscampaignssendpost) | **POST** /mms-campaigns/send | Create mms campaign
*NumberApi* | [**numbersBuyByDedicatedNumberPost**](docs/Api/NumberApi.md#numbersbuybydedicatednumberpost) | **POST** /numbers/buy/{dedicated_number} | Buy dedicated number
*NumberApi* | [**numbersGet**](docs/Api/NumberApi.md#numbersget) | **GET** /numbers | Get all availible dedicated numbers
*NumberApi* | [**numbersSearchByCountryGet**](docs/Api/NumberApi.md#numberssearchbycountryget) | **GET** /numbers/search/{country} | Get all dedicated numbers by country
*PostLetterApi* | [**postLettersExportGet**](docs/Api/PostLetterApi.md#postlettersexportget) | **GET** /post/letters/history/export | export post letter history
*PostLetterApi* | [**postLettersHistoryGet**](docs/Api/PostLetterApi.md#postlettershistoryget) | **GET** /post/letters/history | Get all post letter history
*PostLetterApi* | [**postLettersPricePost**](docs/Api/PostLetterApi.md#postletterspricepost) | **POST** /post/letters/price | Calculate post letter price
*PostLetterApi* | [**postLettersSendPost**](docs/Api/PostLetterApi.md#postletterssendpost) | **POST** /post/letters/send | Send post letter
*PostPostcardApi* | [**postPostcardsHistoryExportGet**](docs/Api/PostPostcardApi.md#postpostcardshistoryexportget) | **GET** /post/postcards/history/export | Export postcard history to a CSV file
*PostPostcardApi* | [**postPostcardsHistoryGet**](docs/Api/PostPostcardApi.md#postpostcardshistoryget) | **GET** /post/postcards/history | Retrieve the history of postcards sent or scheduled
*PostPostcardApi* | [**postPostcardsPricePost**](docs/Api/PostPostcardApi.md#postpostcardspricepost) | **POST** /post/postcards/price | Calculate price for sending one or more postcards
*PostPostcardApi* | [**postPostcardsSendPost**](docs/Api/PostPostcardApi.md#postpostcardssendpost) | **POST** /post/postcards/send | Send one or more postcards
*PostReturnAddressApi* | [**postReturnAddressesByReturnAddressIdDelete**](docs/Api/PostReturnAddressApi.md#postreturnaddressesbyreturnaddressiddelete) | **DELETE** /post/return-addresses/{return_address_id} | Delete specific post return address
*PostReturnAddressApi* | [**postReturnAddressesByReturnAddressIdGet**](docs/Api/PostReturnAddressApi.md#postreturnaddressesbyreturnaddressidget) | **GET** /post/return-addresses/{return_address_id} | Get specific post return address
*PostReturnAddressApi* | [**postReturnAddressesByReturnAddressIdPut**](docs/Api/PostReturnAddressApi.md#postreturnaddressesbyreturnaddressidput) | **PUT** /post/return-addresses/{return_address_id} | Update post return address
*PostReturnAddressApi* | [**postReturnAddressesGet**](docs/Api/PostReturnAddressApi.md#postreturnaddressesget) | **GET** /post/return-addresses | Get list of post return addresses
*PostReturnAddressApi* | [**postReturnAddressesPost**](docs/Api/PostReturnAddressApi.md#postreturnaddressespost) | **POST** /post/return-addresses | Create post return address
*ReferralAccountApi* | [**referralAccountsGet**](docs/Api/ReferralAccountApi.md#referralaccountsget) | **GET** /referral/accounts | Get all referral accounts
*ResellerAccountApi* | [**resellerAccountsByClientUserIdGet**](docs/Api/ResellerAccountApi.md#reselleraccountsbyclientuseridget) | **GET** /reseller/accounts/{client_user_id} | Get Reseller clients Account
*ResellerAccountApi* | [**resellerAccountsByClientUserIdPut**](docs/Api/ResellerAccountApi.md#reselleraccountsbyclientuseridput) | **PUT** /reseller/accounts/{client_user_id} | Update Reseller clients Account
*ResellerAccountApi* | [**resellerAccountsGet**](docs/Api/ResellerAccountApi.md#reselleraccountsget) | **GET** /reseller/accounts | Get list of reseller accounts
*ResellerAccountApi* | [**resellerAccountsPost**](docs/Api/ResellerAccountApi.md#reselleraccountspost) | **POST** /reseller/accounts | Create reseller account
*SMSApi* | [**smsCancelAllPut**](docs/Api/SMSApi.md#smscancelallput) | **PUT** /sms/cancel-all | Update all scheduled message as cancelled
*SMSApi* | [**smsCancelByMessageIdPut**](docs/Api/SMSApi.md#smscancelbymessageidput) | **PUT** /sms/{message_id}/cancel | Update scheduled message as cancelled
*SMSApi* | [**smsHistoryExportGet**](docs/Api/SMSApi.md#smshistoryexportget) | **GET** /sms/history/export | Export all sms history
*SMSApi* | [**smsHistoryGet**](docs/Api/SMSApi.md#smshistoryget) | **GET** /sms/history | Get all sms history
*SMSApi* | [**smsInboundGet**](docs/Api/SMSApi.md#smsinboundget) | **GET** /sms/inbound | Get all inbound sms
*SMSApi* | [**smsInboundPost**](docs/Api/SMSApi.md#smsinboundpost) | **POST** /sms/inbound | Create inbound sms
*SMSApi* | [**smsInboundReadByMessageIdPut**](docs/Api/SMSApi.md#smsinboundreadbymessageidput) | **PUT** /sms/inbound-read/{message_id} | Mark inbound SMS as read
*SMSApi* | [**smsInboundReadPut**](docs/Api/SMSApi.md#smsinboundreadput) | **PUT** /sms/inbound-read | Mark inbound SMS as read
*SMSApi* | [**smsPricePost**](docs/Api/SMSApi.md#smspricepost) | **POST** /sms/price | Calculate sms price
*SMSApi* | [**smsReceiptsByMessageIdGet**](docs/Api/SMSApi.md#smsreceiptsbymessageidget) | **GET** /sms/receipts/{message_id} | Get a Specific Delivery Receipt
*SMSApi* | [**smsReceiptsGet**](docs/Api/SMSApi.md#smsreceiptsget) | **GET** /sms/receipts | Get all delivery receipts
*SMSApi* | [**smsReceiptsPost**](docs/Api/SMSApi.md#smsreceiptspost) | **POST** /sms/receipts | Add a delivery receipt
*SMSApi* | [**smsReceiptsReadPut**](docs/Api/SMSApi.md#smsreceiptsreadput) | **PUT** /sms/receipts-read | Mark delivery receipts as read
*SMSApi* | [**smsSendPost**](docs/Api/SMSApi.md#smssendpost) | **POST** /sms/send | Send sms message(s)
*SMSApi* | [**smsTemplatesByTemplateIdDelete**](docs/Api/SMSApi.md#smstemplatesbytemplateiddelete) | **DELETE** /sms/templates/{template_id} | Delete sms template
*SMSApi* | [**smsTemplatesByTemplateIdPut**](docs/Api/SMSApi.md#smstemplatesbytemplateidput) | **PUT** /sms/templates/{template_id} | Update sms template
*SMSApi* | [**smsTemplatesGet**](docs/Api/SMSApi.md#smstemplatesget) | **GET** /sms/templates | Get lists of all sms templates
*SMSApi* | [**smsTemplatesPost**](docs/Api/SMSApi.md#smstemplatespost) | **POST** /sms/templates | Create sms template
*SMSDeliveryReceiptRulesApi* | [**smsDeliveryReceiptAutomationDelete**](docs/Api/SMSDeliveryReceiptRulesApi.md#smsdeliveryreceiptautomationdelete) | **DELETE** /automations/sms/receipts/{receipt_rule_id} | Delete sms delivery receipt automation
*SMSDeliveryReceiptRulesApi* | [**smsDeliveryReceiptAutomationGet**](docs/Api/SMSDeliveryReceiptRulesApi.md#smsdeliveryreceiptautomationget) | **GET** /automations/sms/receipts/{receipt_rule_id} | Get specific sms delivery receipt automation
*SMSDeliveryReceiptRulesApi* | [**smsDeliveryReceiptAutomationPost**](docs/Api/SMSDeliveryReceiptRulesApi.md#smsdeliveryreceiptautomationpost) | **POST** /automations/sms/receipts | Create sms delivery receipt automations
*SMSDeliveryReceiptRulesApi* | [**smsDeliveryReceiptAutomationPut**](docs/Api/SMSDeliveryReceiptRulesApi.md#smsdeliveryreceiptautomationput) | **PUT** /automations/sms/receipts/{receipt_rule_id} | Update sms delivery receipt automation
*SMSDeliveryReceiptRulesApi* | [**smsDeliveryReceiptAutomationsGet**](docs/Api/SMSDeliveryReceiptRulesApi.md#smsdeliveryreceiptautomationsget) | **GET** /automations/sms/receipts | Get all sms delivery receipt automations
*SearchApi* | [**searchContactsListsGet**](docs/Api/SearchApi.md#searchcontactslistsget) | **GET** /search/contacts-lists | Get list of searched contact list
*SmsCampaignApi* | [**smsCampaignBySmsCampaignIdGet**](docs/Api/SmsCampaignApi.md#smscampaignbysmscampaignidget) | **GET** /sms-campaigns/{sms_campaign_id} | Get specific sms campaign
*SmsCampaignApi* | [**smsCampaignsBySmsCampaignIdPut**](docs/Api/SmsCampaignApi.md#smscampaignsbysmscampaignidput) | **PUT** /sms-campaigns/{sms_campaign_id} | Update sms campaign
*SmsCampaignApi* | [**smsCampaignsCancelBySmsCampaignIdPut**](docs/Api/SmsCampaignApi.md#smscampaignscancelbysmscampaignidput) | **PUT** /sms-campaigns/{sms_campaign_id}/cancel | Cancel sms campaign
*SmsCampaignApi* | [**smsCampaignsGet**](docs/Api/SmsCampaignApi.md#smscampaignsget) | **GET** /sms-campaigns | Get list of sms campaigns
*SmsCampaignApi* | [**smsCampaignsPricePost**](docs/Api/SmsCampaignApi.md#smscampaignspricepost) | **POST** /sms-campaigns/price | Calculate price for sms campaign
*SmsCampaignApi* | [**smsCampaignsSendPost**](docs/Api/SmsCampaignApi.md#smscampaignssendpost) | **POST** /sms-campaigns/send | Create sms campaign
*StatisticsApi* | [**statisticsSmsGet**](docs/Api/StatisticsApi.md#statisticssmsget) | **GET** /statistics/sms | Get sms statistics
*StatisticsApi* | [**statisticsVoiceGet**](docs/Api/StatisticsApi.md#statisticsvoiceget) | **GET** /statistics/voice | Get voice statistics
*SubaccountApi* | [**subaccountsBySubaccountIdDelete**](docs/Api/SubaccountApi.md#subaccountsbysubaccountiddelete) | **DELETE** /subaccounts/{subaccount_id} | Delete a subaccount
*SubaccountApi* | [**subaccountsBySubaccountIdGet**](docs/Api/SubaccountApi.md#subaccountsbysubaccountidget) | **GET** /subaccounts/{subaccount_id} | Get specific subaccount
*SubaccountApi* | [**subaccountsBySubaccountIdPut**](docs/Api/SubaccountApi.md#subaccountsbysubaccountidput) | **PUT** /subaccounts/{subaccount_id} | Update subaccount
*SubaccountApi* | [**subaccountsGet**](docs/Api/SubaccountApi.md#subaccountsget) | **GET** /subaccounts | Get all subaccounts
*SubaccountApi* | [**subaccountsPost**](docs/Api/SubaccountApi.md#subaccountspost) | **POST** /subaccounts | Create new subaccount
*SubaccountApi* | [**subaccountsRegenApiKeyBySubaccountIdPut**](docs/Api/SubaccountApi.md#subaccountsregenapikeybysubaccountidput) | **PUT** /subaccounts/{subaccount_id}/regen-api-key | Regenerate an API Key
*TimezonesApi* | [**timezonesGet**](docs/Api/TimezonesApi.md#timezonesget) | **GET** /timezones | Get supported list of timezones.
*TransactionalEmailApi* | [**emailHistoryExportGet**](docs/Api/TransactionalEmailApi.md#emailhistoryexportget) | **GET** /email/history/export | Export all Transactional Email history
*TransactionalEmailApi* | [**emailHistoryGet**](docs/Api/TransactionalEmailApi.md#emailhistoryget) | **GET** /email/history | Get all transactional email history
*TransactionalEmailApi* | [**emailPricePost**](docs/Api/TransactionalEmailApi.md#emailpricepost) | **POST** /email/price | Get transactional email price
*TransactionalEmailApi* | [**emailSendPost**](docs/Api/TransactionalEmailApi.md#emailsendpost) | **POST** /email/send | Send transactional email
*TransferCreditApi* | [**resellerTransferCreditPut**](docs/Api/TransferCreditApi.md#resellertransfercreditput) | **PUT** /reseller/transfer-credit | Transfer Credit
*UploadApi* | [**uploadsPost**](docs/Api/UploadApi.md#uploadspost) | **POST** /uploads | Upload File
*UserEmailTemplatesApi* | [**emailTemplateDelete**](docs/Api/UserEmailTemplatesApi.md#emailtemplatedelete) | **DELETE** /email/templates/{template_id} | Delete user email template
*UserEmailTemplatesApi* | [**emailTemplateGet**](docs/Api/UserEmailTemplatesApi.md#emailtemplateget) | **GET** /email/templates/{template_id} | Get specific user email template
*UserEmailTemplatesApi* | [**emailTemplatePost**](docs/Api/UserEmailTemplatesApi.md#emailtemplatepost) | **POST** /email/templates | Create email template
*UserEmailTemplatesApi* | [**emailTemplatePut**](docs/Api/UserEmailTemplatesApi.md#emailtemplateput) | **PUT** /email/templates/{template_id} | Update email template
*UserEmailTemplatesApi* | [**emailTemplatesGet**](docs/Api/UserEmailTemplatesApi.md#emailtemplatesget) | **GET** /email/templates | Get all user email templates
*VoiceApi* | [**voiceCancelAllPut**](docs/Api/VoiceApi.md#voicecancelallput) | **PUT** /voice/cancel-all | Update all voice messages as cancelled
*VoiceApi* | [**voiceCancelByMessageIdPut**](docs/Api/VoiceApi.md#voicecancelbymessageidput) | **PUT** /voice/{message_id}/cancel | Update voice message status as cancelled
*VoiceApi* | [**voiceHistoryExportGet**](docs/Api/VoiceApi.md#voicehistoryexportget) | **GET** /voice/history/export | Export voice history
*VoiceApi* | [**voiceHistoryGet**](docs/Api/VoiceApi.md#voicehistoryget) | **GET** /voice/history | Get all voice history
*VoiceApi* | [**voiceLangGet**](docs/Api/VoiceApi.md#voicelangget) | **GET** /voice/lang | Get all voice languages
*VoiceApi* | [**voicePricePost**](docs/Api/VoiceApi.md#voicepricepost) | **POST** /voice/price | Calculate voice price
*VoiceApi* | [**voiceReceiptsGet**](docs/Api/VoiceApi.md#voicereceiptsget) | **GET** /voice/receipts | Get all delivery receipts
*VoiceApi* | [**voiceReceiptsPost**](docs/Api/VoiceApi.md#voicereceiptspost) | **POST** /voice/receipts | Add a delivery receipt
*VoiceApi* | [**voiceReceiptsReadPut**](docs/Api/VoiceApi.md#voicereceiptsreadput) | **PUT** /voice/receipts-read | Mark delivery receipts as read
*VoiceApi* | [**voiceSendPost**](docs/Api/VoiceApi.md#voicesendpost) | **POST** /voice/send | Send voice message(s)
*VoiceDeliveryReceiptRulesApi* | [**voiceDeliveryReceiptAutomationDelete**](docs/Api/VoiceDeliveryReceiptRulesApi.md#voicedeliveryreceiptautomationdelete) | **DELETE** /automations/voice/receipts/{receipt_rule_id} | Delete voice delivery receipt automation
*VoiceDeliveryReceiptRulesApi* | [**voiceDeliveryReceiptAutomationGet**](docs/Api/VoiceDeliveryReceiptRulesApi.md#voicedeliveryreceiptautomationget) | **GET** /automations/voice/receipts/{receipt_rule_id} | Get specific voice delivery receipt automation
*VoiceDeliveryReceiptRulesApi* | [**voiceDeliveryReceiptAutomationPost**](docs/Api/VoiceDeliveryReceiptRulesApi.md#voicedeliveryreceiptautomationpost) | **POST** /automations/voice/receipts | Create voice delivery receipt automations
*VoiceDeliveryReceiptRulesApi* | [**voiceDeliveryReceiptAutomationPut**](docs/Api/VoiceDeliveryReceiptRulesApi.md#voicedeliveryreceiptautomationput) | **PUT** /automations/voice/receipts/{receipt_rule_id} | Update voice delivery receipt automation
*VoiceDeliveryReceiptRulesApi* | [**voiceDeliveryReceiptAutomationsGet**](docs/Api/VoiceDeliveryReceiptRulesApi.md#voicedeliveryreceiptautomationsget) | **GET** /automations/voice/receipts | Get all voice delivery receipt automations

## Documentation For Models

 - [Account](docs/Model/Account.md)
 - [AccountForgotPasswordVerify](docs/Model/AccountForgotPasswordVerify.md)
 - [AccountVerify](docs/Model/AccountVerify.md)
 - [Address](docs/Model/Address.md)
 - [Attachment](docs/Model/Attachment.md)
 - [Contact](docs/Model/Contact.md)
 - [ContactList](docs/Model/ContactList.md)
 - [ContactListImport](docs/Model/ContactListImport.md)
 - [CountryListIds](docs/Model/CountryListIds.md)
 - [CreditCard](docs/Model/CreditCard.md)
 - [DateBefore](docs/Model/DateBefore.md)
 - [DeliveryIssue](docs/Model/DeliveryIssue.md)
 - [DeliveryReceiptRule](docs/Model/DeliveryReceiptRule.md)
 - [Email](docs/Model/Email.md)
 - [EmailAddress](docs/Model/EmailAddress.md)
 - [EmailCampaign](docs/Model/EmailCampaign.md)
 - [EmailFrom](docs/Model/EmailFrom.md)
 - [EmailRecipient](docs/Model/EmailRecipient.md)
 - [EmailSMSAddress](docs/Model/EmailSMSAddress.md)
 - [EmailTemplateNew](docs/Model/EmailTemplateNew.md)
 - [EmailTemplateUpdate](docs/Model/EmailTemplateUpdate.md)
 - [FaxMessage](docs/Model/FaxMessage.md)
 - [FaxMessageCollection](docs/Model/FaxMessageCollection.md)
 - [Fields](docs/Model/Fields.md)
 - [FieldsFields](docs/Model/FieldsFields.md)
 - [ForgotPassword](docs/Model/ForgotPassword.md)
 - [ForgotUsername](docs/Model/ForgotUsername.md)
 - [InboundFaxRule](docs/Model/InboundFaxRule.md)
 - [InboundSMSRule](docs/Model/InboundSMSRule.md)
 - [MmsCampaign](docs/Model/MmsCampaign.md)
 - [MmsMessage](docs/Model/MmsMessage.md)
 - [MmsMessageCollection](docs/Model/MmsMessageCollection.md)
 - [PostLetter](docs/Model/PostLetter.md)
 - [PostPostcard](docs/Model/PostPostcard.md)
 - [PostRecipient](docs/Model/PostRecipient.md)
 - [ResellerAccount](docs/Model/ResellerAccount.md)
 - [ResellerAccountTransferCredit](docs/Model/ResellerAccountTransferCredit.md)
 - [SmsCampaign](docs/Model/SmsCampaign.md)
 - [SmsMessage](docs/Model/SmsMessage.md)
 - [SmsMessageCollection](docs/Model/SmsMessageCollection.md)
 - [SmsTemplate](docs/Model/SmsTemplate.md)
 - [StrippedString](docs/Model/StrippedString.md)
 - [Subaccount](docs/Model/Subaccount.md)
 - [UploadFile](docs/Model/UploadFile.md)
 - [Url](docs/Model/Url.md)
 - [VoiceMessage](docs/Model/VoiceMessage.md)
 - [VoiceMessageCollection](docs/Model/VoiceMessageCollection.md)

## Documentation For Authorization


## BasicAuth

- **Type**: HTTP basic authentication


## Author

<EMAIL>

