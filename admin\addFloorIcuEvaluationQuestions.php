<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsFloorTherapyAndICUEvaluation.php');
include('../setRequest.php');

$isFloorIcuType = isset($_GET['isFloorIcuType']) ? DecodeQueryData($_GET['isFloorIcuType']) : 0;

$title = "Add Floor Therapy and ICU/ABG Evaluation Questions";
$page_title = "Add Floor Therapy and ICU/ABG Evaluation Questions";
$schoolSectionId = 0;
$title = '';
$sortOrder  = '';
$TopicTitleId = '';
$bedCrumTitle = 'Add';
$questionId = 0;
$sortOrder = '';
$isActiveCheckoff = $_SESSION['isActiveCheckoff'];
$optionText = '';
$questionType = '';
$description = '';
$view = '';
$sectionMasterId = 0;
$isPosition = 0;
if ($isFloorIcuType == 0) {
    $activeType = 'floor';
} else {
    $activeType = 'icu';
}
if (isset($_GET['sectionMasterId'])) //Edit Mode
{
    $sectionMasterId = DecodeQueryData($_GET['sectionMasterId']);
}
$view = isset($_GET['view']) ? $_GET['view'] : '';

if (isset($_GET['editid'])) //Edit Mode
{
    $questionId = DecodeQueryData($_GET['editid']);
    if ($view == 1) {
        if ($isFloorIcuType == 1)
            $page_title = "View ICU/ABG Evaluation";
        else
            $page_title = "View Floor Therapy ";
    } else {
        if ($isFloorIcuType == 1)
            $page_title = "Edit ICU/ABG Evaluation";
        else
            $page_title = "Edit Floor Therapy ";
    }

    if ($view == '1') {
        $bedCrumTitle = 'view';
    } else {
        $bedCrumTitle = 'Edit';
    }

    $objFloorTherapy = new clsFloorTherapyAndICUEvaluation();

    $row = $objFloorTherapy->GetFloorIcuEvaluationQuestionDetails($questionId, $isFloorIcuType);
    $rowsquistionoption = $objFloorTherapy->GetFloorIcuEvaluationQuestionOptionDetails($questionId, $isFloorIcuType);

    $optionText  = isset($row['optionText']) ? stripslashes($row['optionText']) : '';
    $questionType = isset($row['questionType']) ? $row['questionType'] : '';
    $isPosition = isset($row['isPosition']) ? $row['isPosition'] : '';
    $sortOrder = isset($row['sortOrder']) ? $row['sortOrder'] : '';
    $description = isset($row['description']) ? $row['description'] : '';

    unset($objFloorTherapy);
}

$optionDefaultArray = array("1" => "1", "2" => "2", "3" => "3", "4" => "4 ", "5" => "5");

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($page_title); ?></title>
    <?php include('includes/headercss.php'); ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <style>
        .select2-container {
            border-left: 3px solid red !important;
            border-radius: 12px !important;
        }
    </style>
</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="settings.html">Settings</a></li>
                    <li>
                        <a href="floorIcuEvaluationSectionList.html?active=<?php echo ($isFloorIcuType == 1 ? 'icu' : 'floor'); ?>&isFloorIcuType=<?php echo EncodeQueryData($isFloorIcuType); ?>">
                            <?php echo ($isFloorIcuType == 1) ? "ICU/ABG Evaluation Section" : "Floor Therapy Evaluation Section"; ?>
                        </a>
                    <li><a href="floorIcuEvaluationQuestionList.html?sectionMasterId=<?php echo EncodeQueryData($sectionMasterId); ?>&isFloorIcuType=<?php echo EncodeQueryData($isFloorIcuType); ?>">Steps</a></li>
                    <li class="active"><?php echo ($bedCrumTitle); ?></li>
                </ol>
            </div>

        </div>
    </div>

    <div class="container">

        <form id="frmcheckoff" data-parsley-validate class="form-horizontal" method="POST" action="floorIcuEvaluationQuestionSubmit.html?editid=<?php echo (EncodeQueryData($questionId)); ?>&sectionMasterId=<?php echo (EncodeQueryData($sectionMasterId)); ?>&isFloorIcuType=<?php echo (EncodeQueryData($isFloorIcuType)); ?>">
            <input type="hidden" name="isFloorIcuType" id="" value="<?php echo $isFloorIcuType; ?>">

            <div class="row">
                <div class="col-md-6">

                    <!-- Text input-->
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="cbotype">Question Type</label>
                        <div class="col-md-12 flex-direction-reverse">
                            <select id="cbotype" name="cbotype" class="form-control input-md required-input select2_single input-md required-input" required>
                                <?php if ($questionId == 0) { ?>
                                    <option value="<?php echo ($questionType) ?>" selected>Select</option>
                                <?php } ?>
                                <option <?php if ($questionType == 2) { ?> selected<?php } ?> value="2" name="questionType">Single Choice</option>
                                <option <?php if ($questionType == 5) { ?> selected<?php } ?> value="5" name="questionType">Long Answer</option>
                                <option <?php if ($questionType == 3) { ?> selected<?php } ?> value="3" name="questionType">Check Box</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtQuestion"> Title</label>
                        <div class="col-md-12">
                            <input id="txtQuestion" name="txtQuestion" value="<?php echo ($optionText); ?>" type="text" class="form-control input-md required-input" required>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <?php ?>
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="sortOrder"> Sort Order</label>
                        <div class="col-md-12">
                            <input id="sortOrder" name="sortOrder" value="<?php echo ($sortOrder); ?>" type="text" class="form-control input-md ">
                        </div>
                    </div>
                    <?php ?>
                </div>

                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="sortOrder">Description</label>
                        <div class="col-md-12">
                            <textarea id="description" name="description" class="form-control input-md" value="" rows="2"><?php echo ($description); ?></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">


                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label mt-2" for="txtQuestion"> Option Position</label>
                        <div class="col-md-3">
                            <input id="horizontal" name="isPosition" style=" margin-top: 11px;" value="0" <?php echo ($isPosition == 0) ?  "checked" : "";  ?> type="radio" class="input-md required-input ml-3" required> Horizontal
                        </div>
                        <div class="col-md-3">
                            <input id="vertical" name="isPosition" style=" margin-top: 11px;" value="1" <?php echo ($isPosition == 1) ?  "checked" : "";  ?> type="radio" class=" input-md required-input" required> Vertical
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group clssingle" style="display:none;">
                        <div class="form-group m-0">
                            <div class="col-md-12 control-label"> </div>
                            <div class="col-md-12">
                                <div class="row">
                                    <?php if ($questionId && ($questionType == 2 || $questionType == 3)) {
                                        $optionDefaultArray = array();
                                    ?>
                                        <div class="col-md-12 textboxDiv p-0">
                                            <?php

                                            while ($row = mysqli_fetch_array($rowsquistionoption)) {
                                                $optionText = $row['optionText'];
                                                $optionValue = $row['optionValue'];
                                            ?>

                                                <div class="singlechoiceboxdiv row m-0" style="margin-bottom: 10px;">
                                                    <input type="hidden" id="hid_anser" class="hid_anser" name="answers[]" value="0">
                                                    <div class="col-md-6">
                                                        <label style=''>Answer</label>
                                                        <input type="text" id="txtsinglechoice" style='' value="<?php echo $optionText; ?>" class="form-control input-md required-input choicebox" name="txtsinglechoice[]" placeholder="Add an answer" required><br />
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label style="">Answer Number</label>
                                                        <div style="display: flex;align-items: flex-start;">
                                                            <div style="flex: 1;">
                                                                <input type="text" id="txtsinglechoicemarks" value="<?php echo $optionValue; ?>" class="form-control input-md required-input choicebox" name="txtsinglechoicemarks[]" placeholder="Add an answer number" required style="width: 100%; margin-bottom: 5px;">
                                                            </div>
                                                            <?php if ($isCurrentSchoolSuperAdmin == 1) { ?>
                                                                <span id="Remove" style="color: red; margin-left: 10px; margin-top: 8px; cursor: pointer;" class="glyphicon glyphicon-trash Remove"></span>
                                                            <?php } ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php } ?>
                                        </div>
                                    <?php } else {
                                    ?>
                                        <div class="col-md-12 px-0 textboxDiv">
                                            <?php

                                            foreach ($optionDefaultArray as $key => $value) {
                                                $optionText = $value;
                                                $optionValue = $key;

                                            ?>
                                                <div class="singlechoiceboxdiv row m-0">
                                                    <input type="hidden" id="hid_anser" class="hid_anser" name="answers[]" value="0">
                                                    <div class="col-md-6">
                                                        <label style=''>Answer</label>
                                                        <input type="text" id="txtsinglechoice" style='' value="<?php echo $optionText; ?>" class="form-control input-md required-input choicebox" name="txtsinglechoice[]" placeholder="Add an answer" required><br />
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label style=" ">Answer Number</label>
                                                        <div style="display: flex;align-items: center;">
                                                            <div style="flex: 1;">
                                                                <input type="text" id="txtsinglechoicemarks" value="<?php echo $optionValue; ?>" style="" class="form-control input-md required-input choicebox" name="txtsinglechoicemarks[]" placeholder="Add an answer number" required  style="width: 100%; margin-bottom: 5px;">
                                                            </div>
                                                                <?php if ($isCurrentSchoolSuperAdmin == 1) { ?>
                                                                    <span id="Remove" style="color:red; margin-left: 10px;" class="glyphicon glyphicon-trash Remove"></span>
                                                                <?php } ?>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php
                                            }
                                                ?>
                                                </div>
                                            <?php } ?>
                                            <div class="col-md-12" style="display: flex; justify-content: end; margin-top: 10px;">
                                                <?php if ($isCurrentSchoolSuperAdmin == 1) { ?>
                                                    <button type="button" id="Add" class="btn btn-success">Add</button>
                                                <?php
                                                }
                                                ?>
                                            </div>

                                        </div>
                                </div>
                            </div>
                        </div>
                    </div>
               
                    <div class="col-md-6">
                        <div class="form-group clslongans" style="display: none;">
                            <div class="">
                                <label class="col-md-12 control-label" for="Answer"> Answer </label>
                                <div class="col-md-12">
                                    <textarea id="longans" name="longans" class="form-control input-md" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <!-- <label class="col-md-2 control-label"></label> -->
                    <div class="col-md-12" style="display: flex; justify-content: center;margin: 20px 0;gap: 15px;">

                        <?php if ($view == '1') { ?>
                            <div class="form-group">
                                <div class="col-md-12" style="display: flex; justify-content: center;margin: 20px 0;gap: 15px;">
                                    <a type="button" href="floorIcuEvaluationQuestionList.html?sectionMasterId=<?php echo (EncodeQueryData($sectionMasterId)); ?>" class="btn btn-default">Cancel</a>
                                </div>
                            </div>
                        <?php } else { ?>
                            <div class="form-group">
                                <div class="col-md-12" style="display: flex; justify-content: center;margin: 20px 0;gap: 15px;">
                                    <button type="submit" id="btnSubmit" name="btnSubmit" style="margin-left:63px;" class="btn btn-success">Save</button>
                                    <a type="button" href="floorIcuEvaluationQuestionList.html?sectionMasterId=<?php echo (EncodeQueryData($sectionMasterId)); ?>" class="btn btn-default">Cancel</a>
                                </div>
                            </div>
                        <?php } ?>

                    </div>
                </div>
        </form>

    </div>

    <?php include('includes/footer.php'); ?>

    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script type="text/javascript">
        $(".select2_tags").select2({
            'placeholder': 'Select'
        }); //for multiple selection

        $(window).load(function() {

            $(".select2_single").select2();

            $('#frmcheckoff').parsley().on('field:validated', function() {
                    var ok = $('.parsley-error').length === 0;
                })
                .on('form:submit', function(e) {
                    ShowProgressAnimation();
                    return true;
                });

        });

        $(document).ready(function() {
            try {
                $("select").change(function() {
                    //    console.log($(this).val());
                    $('.clssingle').hide();
                    $('.clslongans').hide();
                    if ($(this).val() == 2 || $(this).val() == 3 )
                        $('.clssingle').show();
                    else if ($(this).val() == 5)
                        $('.clslongans').show();

                });

                $('#cbotype').trigger('change');

                $("#Add").on("click", function() {
                    $(".textboxDiv").append("<div class='singlechoiceboxdiv row m-0'style='margin-bottom: 10px;''><div class='col-md-6'><label style=''>Answer</label>	<input type='text' class='form-control input-md required-input choicebox' id='txtsinglechoice' style=''  name='txtsinglechoice[]' placeholder='Add an answer' required /><br /></div><div class='col-md-6'><label style=''>Answer Number</label><div style='display: flex;align-items: center;'><div class='w-full'><input type='text' class='form-control input-md required-input choicebox' id='txtsinglechoicemarks'  style='' name='txtsinglechoicemarks[]' placeholder='Add an answer number' required /><input type='hidden'  id='hid_anser' class='hid_anser' name='answers[]'  value='0'/></div><span id='Remove' style='color:red;margin-left: 10px;' class='glyphicon glyphicon-trash Remove'></span></div></div></div>");
                    $('#frmcheckoff').parsley().reset();
                });

                // $(".Remove").on("click", function() {  

                $(document).on("click", ".Remove", function() {
                    $(this).closest('.singlechoiceboxdiv').remove();
                });
               
            } catch (e) {
                console.error("Error in document ready:", e);
            }


        });
    </script>
    <?php if ($view == 1): ?>
        <script>
            document.addEventListener("DOMContentLoaded", function() {
                // Disable all form fields inside the form only
                document.querySelectorAll('#frmcheckoff input, #frmcheckoff select, #frmcheckoff textarea, #frmcheckoff button').forEach(function(el) {
                    el.setAttribute('disabled', true);
                });

                // Optional: enable Cancel button so user can go back
                document.querySelectorAll('.btn-default').forEach(function(el) {
                    el.removeAttribute('disabled');
                });
            });
        </script>
    <?php endif; ?>
</body>

</html>