<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Compact Site Cards</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .site-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 16px;
            position: relative;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .site-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            border-color: #3b82f6;
        }

        .site-card.selected {
            border-color: #3b82f6;
            background: linear-gradient(135deg, #dbeafe 0%, #f0f9ff 100%);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.15);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }

        .site-info {
            flex: 1;
        }

        .site-name {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .site-id {
            font-size: 12px;
            color: #64748b;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .checkbox {
            width: 18px;
            height: 18px;
            border: 2px solid #d1d5db;
            border-radius: 4px;
            position: relative;
            background: white;
            cursor: pointer;
        }

        .checkbox.checked {
            background: #3b82f6;
            border-color: #3b82f6;
        }

        .checkbox.checked::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .details-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .detail-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .detail-icon {
            width: 24px;
            height: 24px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .icon-time {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
        }

        .icon-code {
            background: rgba(99, 102, 241, 0.1);
            color: #6366f1;
        }

        .icon-capacity {
            background: rgba(16, 185, 129, 0.1);
            color: #10b981;
        }

        .icon-days {
            background: rgba(245, 158, 11, 0.1);
            color: #f59e0b;
        }

        .detail-content {
            flex: 1;
            min-width: 0;
        }

        .detail-label {
            font-size: 10px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            color: #64748b;
            margin-bottom: 2px;
        }

        .detail-value {
            font-size: 13px;
            font-weight: 500;
            color: #1e293b;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .days-full {
            grid-column: 1 / -1;
        }

        .days-full .detail-value {
            white-space: normal;
            font-size: 12px;
        }

        .selection-indicator {
            position: absolute;
            top: -1px;
            left: -1px;
            right: -1px;
            bottom: -1px;
            border: 2px solid #3b82f6;
            border-radius: 12px;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.2s;
        }

        .selected .selection-indicator {
            opacity: 1;
        }

        .pulse-dot {
            position: absolute;
            top: 8px;
            left: 8px;
            width: 8px;
            height: 8px;
            background: #3b82f6;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Icons using CSS */
        .icon::before {
            font-size: 14px;
        }

        .icon-clock::before { content: '🕐'; }
        .icon-hash::before { content: '#'; }
        .icon-users::before { content: '👥'; }
        .icon-calendar::before { content: '📅'; }
        .icon-map::before { content: '📍'; }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="margin-bottom: 24px; color: #1e293b; font-size: 24px; font-weight: 700;">Compact Site Cards</h1>
        
        <div class="cards-grid">
            <!-- Card 1 - Regular -->
            <div class="site-card" onclick="toggleCard(this)">
                <div class="card-header">
                    <div class="site-info">
                        <div class="site-name">AG Hospital</div>
                        <div class="site-id">
                            <span class="icon icon-map"></span>
                            Site ID: 01
                        </div>
                    </div>
                    <div class="checkbox"></div>
                </div>
                
                <div class="details-grid">
                    <div class="detail-item">
                        <div class="detail-icon icon-time">
                            <span class="icon icon-clock"></span>
                        </div>
                        <div class="detail-content">
                            <div class="detail-label">Time</div>
                            <div class="detail-value">08:00 AM-08:00 PM</div>
                        </div>
                    </div>
                    
                    <div class="detail-item">
                        <div class="detail-icon icon-code">
                            <span class="icon icon-hash"></span>
                        </div>
                        <div class="detail-content">
                            <div class="detail-label">Code</div>
                            <div class="detail-value">N/A</div>
                        </div>
                    </div>
                    
                    <div class="detail-item">
                        <div class="detail-icon icon-capacity">
                            <span class="icon icon-users"></span>
                        </div>
                        <div class="detail-content">
                            <div class="detail-label">Capacity</div>
                            <div class="detail-value">3 participants</div>
                        </div>
                    </div>
                    
                    <div class="detail-item days-full">
                        <div class="detail-icon icon-days">
                            <span class="icon icon-calendar"></span>
                        </div>
                        <div class="detail-content">
                            <div class="detail-label">Days</div>
                            <div class="detail-value">Monday, Tuesday, Wednesday, Thursday, Friday</div>
                        </div>
                    </div>
                </div>
                
                <div class="selection-indicator"></div>
            </div>

            <!-- Card 2 - Selected -->
            <div class="site-card selected" onclick="toggleCard(this)">
                <div class="card-header">
                    <div class="site-info">
                        <div class="site-name">Baylor SWM</div>
                        <div class="site-id">
                            <span class="icon icon-map"></span>
                            Site ID: 02
                        </div>
                    </div>
                    <div class="checkbox checked"></div>
                </div>
                
                <div class="details-grid">
                    <div class="detail-item">
                        <div class="detail-icon icon-time">
                            <span class="icon icon-clock"></span>
                        </div>
                        <div class="detail-content">
                            <div class="detail-label">Time</div>
                            <div class="detail-value">08:00 AM-08:00 PM</div>
                        </div>
                    </div>
                    
                    <div class="detail-item">
                        <div class="detail-icon icon-code">
                            <span class="icon icon-hash"></span>
                        </div>
                        <div class="detail-content">
                            <div class="detail-label">Code</div>
                            <div class="detail-value">N/A</div>
                        </div>
                    </div>
                    
                    <div class="detail-item">
                        <div class="detail-icon icon-capacity">
                            <span class="icon icon-users"></span>
                        </div>
                        <div class="detail-content">
                            <div class="detail-label">Capacity</div>
                            <div class="detail-value">3 participants</div>
                        </div>
                    </div>
                    
                    <div class="detail-item days-full">
                        <div class="detail-icon icon-days">
                            <span class="icon icon-calendar"></span>
                        </div>
                        <div class="detail-content">
                            <div class="detail-label">Days</div>
                            <div class="detail-value">Monday, Tuesday, Wednesday, Thursday, Friday</div>
                        </div>
                    </div>
                </div>
                
                <div class="selection-indicator"></div>
                <div class="pulse-dot"></div>
            </div>

            <!-- Card 3 - Regular -->
            <div class="site-card" onclick="toggleCard(this)">
                <div class="card-header">
                    <div class="site-info">
                        <div class="site-name">FW Hospital - AM</div>
                        <div class="site-id">
                            <span class="icon icon-map"></span>
                            Site ID: 03
                        </div>
                    </div>
                    <div class="checkbox"></div>
                </div>
                
                <div class="details-grid">
                    <div class="detail-item">
                        <div class="detail-icon icon-time">
                            <span class="icon icon-clock"></span>
                        </div>
                        <div class="detail-content">
                            <div class="detail-label">Time</div>
                            <div class="detail-value">08:00 AM-05:00 PM</div>
                        </div>
                    </div>
                    
                    <div class="detail-item">
                        <div class="detail-icon icon-code">
                            <span class="icon icon-hash"></span>
                        </div>
                        <div class="detail-content">
                            <div class="detail-label">Code</div>
                            <div class="detail-value">N/A</div>
                        </div>
                    </div>
                    
                    <div class="detail-item">
                        <div class="detail-icon icon-capacity">
                            <span class="icon icon-users"></span>
                        </div>
                        <div class="detail-content">
                            <div class="detail-label">Capacity</div>
                            <div class="detail-value">3 participants</div>
                        </div>
                    </div>
                    
                    <div class="detail-item days-full">
                        <div class="detail-icon icon-days">
                            <span class="icon icon-calendar"></span>
                        </div>
                        <div class="detail-content">
                            <div class="detail-label">Days</div>
                            <div class="detail-value">Monday, Tuesday, Wednesday, Thursday, Friday</div>
                        </div>
                    </div>
                </div>
                
                <div class="selection-indicator"></div>
            </div>

            <!-- Card 4 - Regular -->
            <div class="site-card" onclick="toggleCard(this)">
                <div class="card-header">
                    <div class="site-info">
                        <div class="site-name">FW Hospital - PM</div>
                        <div class="site-id">
                            <span class="icon icon-map"></span>
                            Site ID: 04
                        </div>
                    </div>
                    <div class="checkbox"></div>
                </div>
                
                <div class="details-grid">
                    <div class="detail-item">
                        <div class="detail-icon icon-time">
                            <span class="icon icon-clock"></span>
                        </div>
                        <div class="detail-content">
                            <div class="detail-label">Time</div>
                            <div class="detail-value">08:00 PM-07:00 AM</div>
                        </div>
                    </div>
                    
                    <div class="detail-item">
                        <div class="detail-icon icon-code">
                            <span class="icon icon-hash"></span>
                        </div>
                        <div class="detail-content">
                            <div class="detail-label">Code</div>
                            <div class="detail-value">N/A</div>
                        </div>
                    </div>
                    
                    <div class="detail-item">
                        <div class="detail-icon icon-capacity">
                            <span class="icon icon-users"></span>
                        </div>
                        <div class="detail-content">
                            <div class="detail-label">Capacity</div>
                            <div class="detail-value">3 participants</div>
                        </div>
                    </div>
                    
                    <div class="detail-item days-full">
                        <div class="detail-icon icon-days">
                            <span class="icon icon-calendar"></span>
                        </div>
                        <div class="detail-content">
                            <div class="detail-label">Days</div>
                            <div class="detail-value">Monday, Tuesday, Wednesday, Thursday, Friday</div>
                        </div>
                    </div>
                </div>
                
                <div class="selection-indicator"></div>
            </div>
        </div>
    </div>

    <script>
        function toggleCard(card) {
            const checkbox = card.querySelector('.checkbox');
            const isSelected = card.classList.contains('selected');
            
            if (isSelected) {
                card.classList.remove('selected');
                checkbox.classList.remove('checked');
                
                // Remove pulse dot if exists
                const pulseDot = card.querySelector('.pulse-dot');
                if (pulseDot) {
                    pulseDot.remove();
                }
            } else {
                card.classList.add('selected');
                checkbox.classList.add('checked');
                
                // Add pulse dot
                const pulseDot = document.createElement('div');
                pulseDot.className = 'pulse-dot';
                card.appendChild(pulseDot);
            }
        }
    </script>
</body>
</html>