<?php
class clsFloorTherapyAndICUEvaluation
{

	function SaveClinicianEval($studentMasterId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($studentMasterId > 0) {

			$sql = "UPDATE studentfloortherapyandicuevalmaster SET 						
						 clinicianId = '" . addslashes($this->clinicianId) . "', 						  
						 rotationId = '" . addslashes($this->rotationId) . "',
						 rotationGrade = '" . addslashes($this->rotationGrade) . "',
						 evaluationDate='" . addslashes($this->evaluationDate) . "'
						 Where studentMasterId= " . $studentMasterId;
			//  echo 'update->'.$sql;exit;
			$objDB->ExecuteQuery($sql);
		} else {
			$sql = "INSERT INTO studentfloortherapyandicuevalmaster (schoolId,clinicianId, studentId, rotationId,evaluationDate,rotationGrade,isType,
								createdBy, createdDate) 
				VALUES ('" . addslashes($this->schoolId) . "',
						'" . addslashes($this->clinicianId) . "',
						'" . addslashes($this->studentId) . "',
						'" . addslashes($this->rotationId) . "',
						'" . addslashes($this->evaluationDate) . "',
						'" . addslashes($this->rotationGrade) . "',
						'" . addslashes($this->isType) . "',
						'" . addslashes($this->createdBy) . "',
						'" . (date("Y-m-d h:i:s")) . "'						
						)";

			$studentMasterId = $objDB->ExecuteInsertQuery($sql);
		}

		// echo '<hr>Insert->'.$sql;exit;
		unset($objDB);
		return $studentMasterId;
	}

	function SaveStudentEval($studentMasterId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($studentMasterId > 0) {

			$sql = "UPDATE studentfloortherapyandicuevalmaster SET 		
						dateOfStudentSignature='" . addslashes($this->dateOfStudentSignature) . "',
						studentcomment='" . addslashes($this->studentcomment) . "'
						Where studentMasterId= " . $studentMasterId;
			//  echo 'update->'.$sql;exit;
			$objDB->ExecuteQuery($sql);
		}

		unset($objDB);
		return $studentMasterId;
	}
	function SaveAprovalEval($studentMasterId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($studentMasterId > 0) {

			$sql = "UPDATE studentfloortherapyandicuevalmaster SET 		
						aprovedDate='" . addslashes($this->aprovalDate) . "',
						aprovedBy='" . addslashes($this->aprovalBy) . "'
						Where studentMasterId= " . $studentMasterId;
			//  echo 'update->'.$sql;exit;
			$objDB->ExecuteQuery($sql);
		}

		unset($objDB);
		return $studentMasterId;
	}
	function SaveClinicianPreceptorEval($studentMasterId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($studentMasterId > 0) {

			$sql = "UPDATE studentfloortherapyandicuevalmaster SET 		
						evaluationDate='" . addslashes($this->evaluationDate) . "',
						rotationGrade='" . addslashes($this->rotationGrade) . "',
						isPreceptorCompletedStatus='" . addslashes($this->isPreceptorCompletedStatus) . "'
						Where studentMasterId= " . $studentMasterId;
			//  echo 'update->'.$sql;exit;
			$objDB->ExecuteQuery($sql);
		}

		unset($objDB);
		return $studentMasterId;
	}

	function SaveStudentPEF($studentMasterId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($studentMasterId > 0) {

			$sql = "UPDATE studentfloortherapyandicuevalmaster SET 												  
						dateOfStudentSignature = '" . addslashes($this->dateOfStudentSignature) . "',
						studentComment = '" . addslashes($this->studentComment) . "',
						studentsigniture='" . addslashes($this->studentsigniture) . "',						 
						score='" . addslashes($this->score) . "',						 
						totalPoints='" . addslashes($this->totalPoints) . "' 
						Where studentMasterId= " . $studentMasterId;
			// echo 'update->'.$sql;exit;
			$objDB->ExecuteQuery($sql);
		}

		// echo '<hr>Insert->'.$sql;exit;
		unset($objDB);
		return $studentMasterId;
	}

	function DeleteStudentEvalDetails($studentMasterId)
	{
		$objDB = new clsDB();
		$sql = "DELETE  FROM studentfloortherapyandicuevaldetails WHERE studentMasterId=" . $studentMasterId;
		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}

	function SaveStudentEvalDetail()
	{
		$objDB = new clsDB();

		$sql = "INSERT INTO studentfloortherapyandicuevaldetails (studentMasterId,schoolQuestionId,
						schoolOptionvalue,schoolOptionAnswerText) 
				VALUES ('" . ($this->studentMasterId) . "',
						'" . ($this->schoolQuestionId) . "',
						'" . ($this->schoolOptionvalue) . "',
						'" . addslashes($this->schoolOptionAnswerText) . "'							 
					)";
		// echo 'INSERT->'.$sql;exit;
		$studentPEFDetailId = $objDB->ExecuteInsertQuery($sql);
		unset($objDB);
		return $studentPEFDetailId;
	}

	function SaveFloorIcuEvaluationSection($sectionId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($sectionId > 0) {

			$sql = "UPDATE schoolfloortherapyandicuevalsectionmaster SET 						
						title = '" . addslashes($this->title) . "',						
						sortOrder = '" . addslashes($this->sortOrder) . "',
						isType = '" . addslashes($this->isFloorIcuType) . "'
						Where sectionMasterId= " . $sectionId;
			//echo 'update->'.$sql;exit;
			$objDB->ExecuteQuery($sql);
		} else {
			$sql = "INSERT INTO schoolfloortherapyandicuevalsectionmaster (title,sortOrder,isType) 
				VALUES (
						'" . addslashes($this->title) . "',
						'" . addslashes($this->sortOrder) . "',
						'" . addslashes($this->isFloorIcuType) . "'						
						)";

			$sectionId = $objDB->ExecuteInsertQuery($sql);
		}

		unset($objDB);
		return $sectionId;
	}

	function SaveFloorIcuEvaluationQuestion($questionId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($questionId > 0) {
			$sql = "UPDATE schoolfloortherapyandicuevalmaster SET  
                optionText = '" . addslashes($this->optionText) . "',
                questionType = '" . addslashes($this->questionType) . "',
                sectionMasterId = '" . addslashes($this->sectionMasterId) . "',
                isPosition = '" . addslashes($this->isPosition) . "',
                sortOrder = '" . addslashes($this->sortOrder) . "',
                isType  = '" . addslashes($this->isFloorIcuType) . "',
                description  = '" . addslashes($this->description) . "'
            WHERE questionId = " . $questionId;
			//   echo $sql; exit;
			$objDB->ExecuteQuery($sql);
		} else {
			$sql = "INSERT INTO schoolfloortherapyandicuevalmaster (optionText,questionType,sectionMasterId,sortOrder,isPosition,isType,description) 
				VALUES ('" . addslashes($this->optionText) . "',
						'" . addslashes($this->questionType) . "',					
						'" . addslashes($this->sectionMasterId) . "',
						'" . addslashes($this->sortOrder) . "',
						'" . addslashes($this->isPosition) . "',
						'" . addslashes($this->isFloorIcuType) . "',
						'" . addslashes($this->description) . "'	
						)";

			$questionId = $objDB->ExecuteInsertQuery($sql);
			// echo $sql;
			// exit;
		}

		unset($objDB);
		return $questionId;
	}

	function SaveFloorIcuEvaluationQuestionOptions()
	{
		$objDB = new clsDB();
		$sql = '';
		$sql = "INSERT INTO schoolfloortherapyandicuevaldetails(questionId,optionText,optionValue) 
					VALUES ('" . addslashes($this->questionId) . "',
							'" . addslashes($this->optionText) . "',					
							'" . addslashes($this->optionValue) . "'					
							)";
		$objDB->ExecuteInsertQuery($sql);
		unset($objDB);
	}

	function GetFloorIcuEvaluationSectionDetail($sectionMasterId, $isType = '')
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM  schoolfloortherapyandicuevalsectionmaster
				WHERE sectionMasterId=" . $sectionMasterId;
		if ($isType == '0')
			$sql .= " AND schoolfloortherapyandicuevalsectionmaster.isType=0";

		else if ($isType == '1')
			$sql .= " AND schoolfloortherapyandicuevalsectionmaster.isType=1";
		// echo $sql;exit;
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}

	function DeleteFloorIcuEvaluationSection($sectionMasterId)
	{
		$objDB = new clsDB();
		$sql = "DELETE FROM schoolfloortherapyandicuevalsectionmaster WHERE sectionMasterId=" . $sectionMasterId;
		$result = $objDB->ExecuteQuery($sql);
		// echo $sql; exit;
		unset($objDB);
		return $result;
	}
	function DeleteFloorIcuEvaluationOptions($questionId)
	{
		$objDB = new clsDB();
		$sql = "DELETE  FROM schoolfloortherapyandicuevaldetails WHERE questionId=" . $questionId;
		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}

	function DeleteFloorIcuQuestion($questionId)
	{
		$objDB = new clsDB();
		$sql = "DELETE m, d
            FROM schoolfloortherapyandicuevalmaster m
            LEFT JOIN schoolfloortherapyandicuevaldetails d ON m.questionId = d.questionId
            WHERE m.questionId =" . $questionId;

		$result = $objDB->ExecuteQuery($sql);
		//    echo $sql; exit;
		unset($objDB);

		return $result;
	}
	function GetAllEval($currentSchoolId, $rotationId = 0, $studentId = 0, $isType = '', $startDate = '', $endDate = '', $clinicianId = 0, $isdefaultFloorAndIcuEval = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT studentfloortherapyandicuevalmaster.* ,schools.schoolId, student.firstName as studentfirstName, 
                student.lastName as studentlastName, student.studentId, rotation.title as rotationName
                from studentfloortherapyandicuevalmaster
                inner join schools on schools.schoolId = studentfloortherapyandicuevalmaster.schoolId
                inner join rotation on rotation.rotationId = studentfloortherapyandicuevalmaster.rotationId
                inner join student on student.studentId =  studentfloortherapyandicuevalmaster.studentId
                WHERE studentfloortherapyandicuevalmaster.schoolId=" . $currentSchoolId;

		if ($rotationId > 0)
			$sql .= " AND studentfloortherapyandicuevalmaster.rotationId=" . $rotationId;
		if ($clinicianId > 0)
			$sql .= " AND studentfloortherapyandicuevalmaster.clinicianId=" . $clinicianId;


		if ($studentId > 0)
			$sql .= " AND studentfloortherapyandicuevalmaster.studentId=" . $studentId;

		if ($isType == 'floor')
			$sql .= " AND studentfloortherapyandicuevalmaster.isType=0";

		elseif ($isType == 'icu')
			$sql .= " AND studentfloortherapyandicuevalmaster.isType=1";

		if ($startDate != '')
			$sql .= " AND date(studentfloortherapyandicuevalmaster.evaluationDate) >= '" . $startDate . "'";

		if ($endDate != '')
			$sql .= " AND date(studentfloortherapyandicuevalmaster.evaluationDate) <= '" . $endDate . "' ";

		// if($isdefaultFloorAndIcuEval > 0)
		// 	$sql .=" AND studentfloortherapyandicuevalmaster.clinicianId !=0";

		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetStudentPEFDetails($studentMasterId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT studentfloortherapyandicuevalmaster.*,studentfloortherapyandicuevaldetails.*,rotation.parentRotationId,rotation.locationId as rotationLocationId,courses.locationId
				FROM  studentfloortherapyandicuevalmaster 
				LEFT JOIN studentfloortherapyandicuevaldetails ON studentfloortherapyandicuevaldetails.studentMasterId= 
				studentfloortherapyandicuevalmaster.`studentMasterId`
				LEFT JOIN rotation ON studentfloortherapyandicuevalmaster.rotationId=rotation.rotationId
				LEFT JOIN courses ON rotation.courseId=courses.courseId
				WHERE studentfloortherapyandicuevalmaster.studentMasterId=" . $studentMasterId;
		// echo $sql;exit;
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}

	function GetSections($isType = '')
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT schoolfloortherapyandicuevalsectionmaster.*
				FROM schoolfloortherapyandicuevalsectionmaster WHERE isActive = 1";
		if ($isType == 'floor')
			$sql .= " AND schoolfloortherapyandicuevalsectionmaster.isType=0";
		elseif ($isType == 'icu')
			$sql .= " AND schoolfloortherapyandicuevalsectionmaster.isType=1";

		$sql .= " ORDER BY schoolfloortherapyandicuevalsectionmaster.sortOrder,title ASC";
		// echo $sql;	exit;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}
	function GetSectionForSettings($isType = '')
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT schoolfloortherapyandicuevalsectionmaster.*
				FROM schoolfloortherapyandicuevalsectionmaster";
		if ($isType == 'floor')
			$sql .= " WHERE schoolfloortherapyandicuevalsectionmaster.isType=0";
		elseif ($isType == 'icu')
			$sql .= " WHERE schoolfloortherapyandicuevalsectionmaster.isType=1";

		$sql .= " ORDER BY schoolfloortherapyandicuevalsectionmaster.sortOrder,title ASC";
		// echo $sql;	exit;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}
	function SetFloorIcuEvaluationStatus($sectionMasterId, $status)
	{
		if ($sectionMasterId > 0) {
			$objDB = new clsDB();
			$sql = "Update schoolfloortherapyandicuevalsectionmaster set isActive = " . $status . " Where sectionMasterId = " . $sectionMasterId;
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
	}
	function GetFloorIcuQuestionCountbySections($sectionMasterId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT count(sectionMasterId) as count
					FROM schoolfloortherapyandicuevalmaster 
					WHERE sectionMasterId=" . $sectionMasterId;
		$rows = $objDB->GetSingleFieldValue($sql);
		return $rows;
		unset($objDB);
	}

	function GetFloorIcuEvaluationQuestionDetails($questionId, $isType = '')
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM  schoolfloortherapyandicuevalmaster
					WHERE questionId=" . $questionId;
		if ($isType == '0')
			$sql .= " AND schoolfloortherapyandicuevalmaster.isType=0";

		else if ($isType == '1')
			$sql .= " AND schoolfloortherapyandicuevalmaster.isType=1";
		$rows = $objDB->GetDataRow($sql);
		// echo $sql; exit;
		return $rows;
		unset($objDB);
	}

	function GetFloorIcuEvaluationQuestionOptionDetails($questionId, $isType = '')
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT schoolfloortherapyandicuevalmaster. *,schoolfloortherapyandicuevaldetails.*
						FROM schoolfloortherapyandicuevalmaster 
						INNER JOIN schoolfloortherapyandicuevaldetails ON schoolfloortherapyandicuevalmaster.questionId=
						schoolfloortherapyandicuevaldetails.questionId
						WHERE schoolfloortherapyandicuevalmaster.questionId=" . $questionId .
			" ORDER BY schoolfloortherapyandicuevaldetails.optionValue";

		if ($isType == '0')
			$sql .= " AND schoolfloortherapyandicuevalmaster.isType=0";

		else if ($isType == '1')
			$sql .= " AND schoolfloortherapyandicuevalmaster.isType=1";
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}
	function GetAllQuestionMaster($sectionMasterId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM  schoolfloortherapyandicuevalmaster
				WHERE sectionMasterId=" . $sectionMasterId . " Order By sortOrder,optionText";
		// echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetAllFloorIcuEvaluationQuestionToSetting($sectionMasterId, $isType = '')
	{
		$objDB = new clsDB();
		$rows = "";

		$sql = "SELECT schoolfloortherapyandicuevalmaster.*, questiontypemaster.title as questionType FROM schoolfloortherapyandicuevalmaster
            LEFT JOIN questiontypemaster ON schoolfloortherapyandicuevalmaster.questionType = questiontypemaster.questionType
            WHERE schoolfloortherapyandicuevalmaster.sectionMasterId = " . intval($sectionMasterId);

		if ($isType === '0')
			$sql .= " AND schoolfloortherapyandicuevalmaster.isType = 0";
		elseif ($isType === '1')
			$sql .= " AND schoolfloortherapyandicuevalmaster.isType = 1";

		$sql .= " ORDER BY schoolfloortherapyandicuevalmaster.sortOrder,optionText ASC";

		// Debug - remove or comment this out in production
		// echo $sql; 
		// exit;

		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function DeleteFloorEvaluation($studentMasterId)
	{
		$objDB = new clsDB();
		$sql = "DELETE  FROM studentfloortherapyandicuevalmaster WHERE studentMasterId=" . $studentMasterId;
		// echo $sql;exit;
		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}

	function SaveStudentPreceptorEval()
	{
		$objDB = new clsDB();
		$sql = "INSERT INTO studentfloortherapyandicuevalmaster (schoolId,preceptorId, studentId, rotationId,evaluationDate,isType, createdDate) 
			VALUES ('" . addslashes($this->schoolId) . "',
					'" . addslashes($this->preceptorId) . "',
					'" . addslashes($this->studentId) . "',
					'" . addslashes($this->rotationId) . "',
					'" . addslashes($this->evaluationDate) . "',
					'" . addslashes($this->isType) . "',
					'" . (date("Y-m-d h:i:s")) . "'						
					)";

		$studentMasterId = $objDB->ExecuteInsertQuery($sql);
		// echo '<hr>Insert->'.$sql;exit;
		unset($objDB);
		return $studentMasterId;
	}

	function GetAllFloorForApp($currentSchoolId, $rotationId = 0, $studentId = 0, $clinicianId = 0, $isType = '', $limitString = '', $searchText = '')
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT studentfloortherapyandicuevalmaster.* ,schools.schoolId, student.firstName as studentfirstName, 
                student.lastName as studentlastName, student.studentId, rotation.title as rotationName,
				courses.title as course, hospitalsites.hospitalSiteId, hospitalsites.title AS Hospitalsite
                from studentfloortherapyandicuevalmaster
                inner join schools on schools.schoolId = studentfloortherapyandicuevalmaster.schoolId
                inner join rotation on rotation.rotationId = studentfloortherapyandicuevalmaster.rotationId
                inner join student on student.studentId =  studentfloortherapyandicuevalmaster.studentId
				 LEFT JOIN hospitalsites on hospitalsites.hospitalSiteId = rotation.hospitalSiteId
				 LEFT JOIN extenal_preceptors ON extenal_preceptors.id =studentfloortherapyandicuevalmaster.preceptorId
				LEFT JOIN courses ON rotation.courseId = courses.courseId
                WHERE studentfloortherapyandicuevalmaster.schoolId=" . $currentSchoolId;

		if ($rotationId > 0)
			$sql .= " AND studentfloortherapyandicuevalmaster.rotationId=" . $rotationId;

		if ($clinicianId > 0)
			$sql .= " AND studentfloortherapyandicuevalmaster.clinicianId=" . $clinicianId;

		if ($studentId > 0)
			$sql .= " AND studentfloortherapyandicuevalmaster.studentId=" . $studentId;

		if ($isType == 'floor')
			$sql .= " AND studentfloortherapyandicuevalmaster.isType=0";

		elseif ($isType == 'icu')
			$sql .= " AND studentfloortherapyandicuevalmaster.isType=1";

		elseif ($isType == 'icu')
			$sql .= " AND studentfloortherapyandicuevalmaster.isType=1";
		if ($searchText != "") {
			$sql .= " AND (CONCAT(extenal_preceptors.firstName, ' ', extenal_preceptors.lastName) LIKE '%" . $searchText . "%' OR  CONCAT(student.firstName, ' ', student.lastName) LIKE '%" . $searchText . "%' OR  rotation.title LIKE '%" . $searchText . "%' )";
		}

		$sql .= " ORDER BY `studentfloortherapyandicuevalmaster`.`evaluationDate` DESC" . $limitString;

		// echo $sql;exit;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	/**
	 * Retrieves details about a Floor Therapy or ICU evaluation for logging purposes.
	 * 
	 * @param int $studentMasterId The ID of the student master record.
	 * @param int $schoolId Optional. The ID of the school. If provided, only records from this school will be returned.
	 * 
	 * @return array Details about the evaluation, including the student's name, rotation name, hospital site name, school name, clinician name, and preceptor name.
	 */
	function GetAllFloorAndIcuEvaluationDetailsForLogs($studentMasterId, $schoolId = 0)
	{
		$objDB = new clsDB();

		$sql = "SELECT studentfloortherapyandicuevalmaster.*,rotation.title as rotationName,studentfloortherapyandicuevalmaster.studentId as userId,CONCAT(student.firstName, ' ', student.lastName) AS userName,hospitalsites.title AS hospitalSiteName, schools.displayName as schoolName,CONCAT(clinician.firstName, ' ', clinician.lastName) AS clinicianName, CONCAT(extenal_preceptors.firstName, ' ', extenal_preceptors.lastName) AS preceptorName FROM `studentfloortherapyandicuevalmaster` 
		INNER JOIN rotation ON studentfloortherapyandicuevalmaster.`rotationId` = rotation.`rotationId` 
		INNER JOIN student ON student.studentId=studentfloortherapyandicuevalmaster.studentId
        INNER JOIN hospitalsites ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
        INNER JOIN schools ON schools.schoolId = studentfloortherapyandicuevalmaster.schoolId
		LEFT JOIN clinician ON studentfloortherapyandicuevalmaster.clinicianId = clinician.clinicianId
		LEFT JOIN extenal_preceptors ON extenal_preceptors.id = studentfloortherapyandicuevalmaster.preceptorId

		WHERE studentMasterId =" . $studentMasterId;

		if ($schoolId) {
			$sql .= " AND studentfloortherapyandicuevalmaster.schoolId=" . $schoolId;
		}
		// echo $sql;exit;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}

	/**
	 * Retrieves CI evaluation details for a given CI evaluation ID and school ID.
	 *
	 * @param int $sectionMasterId The ID of the CI evaluation.
	 * @param int $schoolId The ID of the school. If not provided, all schools will be considered.
	 *
	 * @return array An array containing the log data, row data, and additional data.
	 */
	function GetFloorAndIcuSectionDetailsForLogs($sectionMasterId, $isSuperAdmin = 0)
	{
		$objDB = new clsDB();
		$rows = "";

		$sql = "SELECT schoolfloortherapyandicuevalsectionmaster.* FROM  schoolfloortherapyandicuevalsectionmaster
				WHERE sectionMasterId=" . $sectionMasterId;

		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}


	/**
	 * Retrieves CI evaluation details for a given CI evaluation ID and school ID.
	 *
	 * @param int $pefQuestionId The ID of the CI evaluation.
	 * @param int $schoolId The ID of the school. If not provided, all schools will be considered.
	 *
	 * @return array An array containing the log data, row data, and additional data.
	 */
	function GetFloorAndIcuStepDetailsForLogs($questionId, $isSuperAdmin = 0)
	{
		$objDB = new clsDB();
		$row = "";

		$sql = "SELECT schoolfloortherapyandicuevalmaster.*,questiontypemaster.title as questionType,schoolfloortherapyandicuevalsectionmaster.title AS SectionTitle FROM schoolfloortherapyandicuevalmaster
				LEFT JOIN questiontypemaster ON schoolfloortherapyandicuevalmaster.questionType=questiontypemaster.questionType
				INNER JOIN schoolfloortherapyandicuevalsectionmaster ON schoolfloortherapyandicuevalsectionmaster.sectionMasterId = schoolfloortherapyandicuevalsectionmaster.sectionMasterId
				WHERE  schoolfloortherapyandicuevalmaster.questionId=" . $questionId;

		// if($currentSchoolId == '118')
		$sql .= " ORDER BY sortOrder ASC";
		// echo $sql;exit;

		$row = $objDB->GetDataRow($sql);
		return $row;
		unset($objDB);
	}

	/**
	 * This function creates a log entry for a Floor Therapy or ICU evaluation action.
	 *
	 * @param int $id The ID of the Floor Therapy or ICU evaluation.
	 * @param string $action The action performed (Add, Edit, Delete, Signoff).
	 * @param int $userId The ID of the user performing the action.
	 * @param string $userType The type of user performing the action (Student, Preceptor, Admin).
	 * @param int $type 0 for Floor Therapy, 1 for ICU evaluation.
	 *
	 * @return array An array containing the log data, row data, and additional data.
	 */
	function createFloorAndIcuEvaluationLog($id, $action, $userId, $userType, $type)
	{
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$objEval = new clsFloorTherapyAndICUEvaluation(); // Assuming `Attendance` class is used for `prepareAttendanceLogData`

		// Get user details based on user type
		$userDetails = getUserDetails($userId, $userType);

		// print_r($userDetails);exit;
		if ($type == 'section') {
			$rowData = $objEval->GetFloorAndIcuSectionDetailsForLogs($id);
		} else if ($type == 'step') {
			$rowData = $objEval->GetFloorAndIcuStepDetailsForLogs($id);
		} else {
			$rowData = $objEval->GetAllFloorAndIcuEvaluationDetailsForLogs($id);
		}

		if ($rowData != '')
			$logData = $objLog->generateLogData($rowData, $userType);

		$additionalData = '';

		// Populate log data with user details
		$logData['userId'] = $userDetails['userId'];
		$logData['userName'] = $userDetails['userName'];
		$logMessage = '';
        	if ($type == 'section') {
			$logData['isTypeSection'] = isset($rowData['isType']) ? (($rowData['isType'] == 0) ? 'Floor Therapy' : 'ICU/ABG') : '';
		}
        if ($type == 'step') {
			$logData['isTypeStep'] = isset($rowData['isType']) ? (($rowData['isType'] == 0) ? 'Floor Therapy' : 'ICU/ABG') : '';
		}

		if ($type == 'section') {
			if ($action == 'Add') {
				$logMessage = $logData['userName'] . ' added new section in ' . $logData['isTypeSection'] . ' Evaluation.';
			} else if ($action == 'Edit') {
				$logMessage = $logData['userName'] . ' updated the section from ' . $logData['isTypeSection'] . ' Evaluation.';
			} else if ($action == 'Delete') {
				$logMessage = $logData['userName'] . ' deleted the section from ' . $logData['isTypeSection'] . ' Evaluation.';
			} else if ($action == 'Active') {
				$logMessage = $logData['userName'] . ' activate the section from ' . $logData['isTypeSection'] . ' Evaluation.';
			} else if ($action == 'Inactive') {
				$logMessage = $logData['userName'] . ' deactivate the section from ' . $logData['isTypeSection'] . ' Evaluation.';
			}
		} else if ($type == 'step') {

			if ($action == 'Add') {
				$logMessage = $logData['userName'] . ' added new Step in ' . $logData['isTypeStep'] . ' Evaluation Section.';
			} else if ($action == 'Edit') {
				$logMessage = $logData['userName'] . ' updated Step from ' . $logData['isTypeStep'] . ' Evaluation Section.';
			} else if ($action == 'Delete') {
				$logMessage = $logData['userName'] . ' deleted Step from ' . $logData['isTypeStep'] . ' Evaluation Section.';
			} else if ($action == 'Assign') {
				$logMessage = $logData['userName'] . ' Assign Steps in ' . $logData['isTypeStep'] . ' Evaluation Section.';
			} else if ($action == 'Unassign') {
				$logMessage = $logData['userName'] . ' Unassign Steps from ' . $logData['isTypeStep'] . ' Evaluation Section.';
			}
		} else {
					$resource = isset($rowData['isType']) ? (($rowData['isType'] == 0) ? 'Floor Therapy Evaluation' : 'ICU/ABG Evaluation') : '';

			if ($action == 'Add') {
				$logMessage = $logData['userName'] . ' added ' . $resource . ' for ' . $logData['rotationName'] . ' rotation.';
			} else if ($action == 'Edit') {
				$logMessage = $logData['userName'] . ' updated ' . $resource . ' from ' . $logData['rotationName'] . ' rotation.';
			} else if ($action == 'Delete' && $userType != 'Student') {
				$logMessage = $logData['userName'] . ' deleted ' . $rowData['userName'] . ' ' . $resource . ' from ' . $logData['rotationName'] . ' rotation.';
			} else if ($action == 'Delete' && $userType == 'Student') {
				$logMessage = $logData['userName'] . ' deleted ' . $resource . ' from ' . $logData['rotationName'] . ' rotation.';
			} else if ($action == 'Signoff' && $userType == 'Preceptor') {
				$logMessage = $logData['userName'] . ' signoff ' . $resource . ' from ' . $logData['rotationName'] . ' rotation.';
			} else if ($action == 'Signoff') {
				$logMessage = $logData['userName'] . ' signoff ' . $resource . ' from ' . $logData['rotationName'] . ' rotation.';
			}
		}

		// Construct log message
		$logData['message'] = $logMessage;

		// Return the data
		return [$logData, $rowData, $additionalData];
	}

	/**
	 * Saves audit log for floor and icu evaluation actions
	 *
	 * @param int $id the floor and icu evaluation master ID
	 * @param int $userId the user ID of the user performing the action
	 * @param string $userType the user type (e.g. student, clinician, etc.)
	 * @param string $action the action to be logged (Add, Edit, Delete, Signoff)
	 * @param int $type the type of evaluation (0 for floor therapy evaluation, 1 for icu/abg evaluation)
	 * @param int $isMobile optional, indicates if the action was performed from a mobile device (0 = false, 1 = true)
	 *
	 * @return bool Returns true if successful
	 */
	function saveFloorAndIcuEvaluationAuditLog($id, $userId, $userType, $action, $type, $isMobile = 0)
	{
		// Super admin For floor & icu evaluations -> the type is pass for section and step 
		// Clinician and Student for Floor & icu form -> the type is pass for isType means it is 0 or 1
		// Instantiate the Logger and Checkoff classes
		$objLog = new clsLogger();
		$objEval = new clsFloorTherapyAndICUEvaluation();

		// Prepare log data
		[$logData, $rowData, $additionalData] = $objEval->createFloorAndIcuEvaluationLog($id, $action, $userId, $userType, $type);
		$logData['isMobile'] = $isMobile;

		if ($action == 'Delete') {
			if ($type == 'section') {
				$objEval->DeleteFloorIcuEvaluationSection($id);
			} else if ($type == 'step') {
				$objEval->DeleteFloorIcuQuestion($id);
			} else {
				// // Initialize database object
				$objDB = new clsDB();

				// Fetch data from `studentdailyevaldetails` table for the given master ID
				$evaluationDetailsResult = $objDB->GetAllSelectedDataFromTable('studentfloortherapyandicuevaldetails', '', 'studentMasterId', $id);
				unset($objDB);

				if ($evaluationDetailsResult) {
					// Convert the result set into an array
					$evaluationDetailsArray = fetchResultAsArray($evaluationDetailsResult);

					// Generate the JSON array (if needed for logs or other purposes)
					$additionalData = $evaluationDetailsArray;
					$objEval->DeleteFloorEvaluation($id);
				}
			}
		}
		$moduleName = '';
		if ($type == 'section') {
			$moduleName = " Section";
		} else if ($type == 'step') {
			$moduleName = " Step";
		}
		
		$resource = isset($rowData['isType']) ? (($rowData['isType'] == 0) ? 'Floor Therapy Evaluation' : 'ICU/ABG Evaluation') : '';
		// Save the log details
		$objLog->saveLogs($logData, $action, $id, $resource, $rowData, $additionalData);

		// Clean up
		unset($objLog);
		unset($objEval);

		return true; // Return success or handle further actions as needed
	}
}
