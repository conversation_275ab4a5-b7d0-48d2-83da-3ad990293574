<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsSchool.php');
include('../class/clsFloorTherapyAndICUEvaluation.php');
include('../setRequest.php');

// echo '<pre>';
// print_r($_POST);
// exit;
if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit'])) {

	$questionId = isset($_GET['editid']) ? DecodeQueryData($_GET['editid']) : 0;
	$isFloorIcuType = isset($_POST['isFloorIcuType']) ? ($_POST['isFloorIcuType']) : 0;
	$sectionMasterId = isset($_GET['sectionMasterId']) ? DecodeQueryData($_GET['sectionMasterId']) : 0;

	$status = ($questionId > 0) ? 'updated' : 'added';

	$txtsinglechoice  = ($_POST['txtsinglechoice']) ?? '';
	$title = $_POST['txtQuestion'];
	$questionType = $_POST['cbotype'];
	$optionSortOrder = $_POST['txtsinglechoicemarks'] ?? '';
	$isPosition = $_POST['isPosition'];
	$sortOrder = isset($_POST['sortOrder']) ? $_POST['sortOrder'] : NULL;
	$description = $_POST['description'];

	//Save data
	$objFloorTherapy = new clsFloorTherapyAndICUEvaluation();
	$objFloorTherapy->optionText = $title;
	$objFloorTherapy->questionType = $questionType;
	$objFloorTherapy->sectionMasterId = $sectionMasterId;
	$objFloorTherapy->isPosition = $isPosition;
	$objFloorTherapy->sortOrder = $sortOrder;
	$objFloorTherapy->isFloorIcuType = $isFloorIcuType;
	$objFloorTherapy->description = $description;

	$retquestionId = $objFloorTherapy->SaveFloorIcuEvaluationQuestion($questionId, $isFloorIcuType);
    
	if ($questionId) {
		$deleteCiEvalutionOptions = $objFloorTherapy->DeleteFloorIcuEvaluationOptions($questionId);
	}

	if ($questionType == 2 || $questionType == 3) {
		foreach ($txtsinglechoice as $key => $value) {
           
			$objFloorTherapy->questionId = $retquestionId;
			$objFloorTherapy->optionText = $value;
			$objFloorTherapy->optionValue = $optionSortOrder[$key];
			$retQuestionOptionId = $objFloorTherapy->SaveFloorIcuEvaluationQuestionOptions($questionId);
		}
	}

	unset($objFloorTherapy);

	if ($retquestionId > 0) {
		//Audit Log Start
		// Instantiate the Logger class
		$objLog = new clsLogger();

		// Determine the action type (EDIT or ADD) based on the presence of a journal ID
		$action = ($questionId > 0) ? $objLog::EDIT : $objLog::ADD;
		$type = "step";

		$userType = $objLog::SUPERADMIN; // User type is set to Admin

		$objFloorTherapy = new clsFloorTherapyAndICUEvaluation();
		$objFloorTherapy->saveFloorAndIcuEvaluationAuditLog($retquestionId, $_SESSION["loggedUserId"], $userType, $action, $type, $IsMobile = 0);
		unset($objFloorTherapy);

		unset($objLog);
		//Audit Log End

		header('location:floorIcuEvaluationQuestionList.html?status=' . $status . '&sectionMasterId=' . EncodeQueryData($sectionMasterId) . '&isFloorIcuType=' . EncodeQueryData($isFloorIcuType));
	} else {
		header('location:addFloorIcuEvaluationQuestions.html?sectionMasterId=' . EncodeQueryData($sectionMasterId) . '&isFloorIcuType=' . EncodeQueryData($isFloorIcuType) . '&status=error');
	}
} else {
	header('location:floorIcuEvaluationQuestionList.html?sectionMasterId=' . EncodeQueryData($sectionMasterId) . '&isFloorIcuType=' . EncodeQueryData($isFloorIcuType) . '&status=' . $status);
	//header('location:checkoffsection.html');
	exit();
}
