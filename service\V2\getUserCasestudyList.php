<?php
include("../../includes/config.php");
include("../../includes/commonfun.php");
include("../../class/clsDB.php");
include("../../class/clsStudent.php");
include("../../class/clsCaseStudy.php");
include("../../class/clsRotation.php");
include("../../class/clsLocations.php");
include('../../class/clsExternalPreceptors.php');
include("../../class/clsStatusCodes.php");
include("../../class/clsClinician.php");
include('validateParameters.php');

$isTrue = true;
$isFalse = false;
$objStatusCode = new clsStatusCodes();

$UserId = "";
$TimeZone = "";
$AccessToken = '';
$errroMessage = "";
$studentTimezone = "";
$rotationId = 0;
$searchText = "";
$Type = 'All';

// Check if the request method 
if ($_SERVER['REQUEST_METHOD'] === 'GET') {

    $userType = isset($_GET['UserType']) ? $_GET['UserType'] : 0;

    $loggedClinicianType = isset($_GET['RoleType']) ? $_GET['RoleType'] : '';

    $LoggedUserType = ($userType) ? 'Clinician' : 'Student';

    $requestParameters = array(
        'UserId' => $_GET['UserId'],
        'AccessToken' => $_GET['AccessToken'],
        'PageNo' => $_GET['PageNo'],
    );


    // Passarry for Vaildation
    $validatedParameters = validateParameters($requestParameters, $objStatusCode, $isFalse, $isTrue);

    // Access the validated parameters
    if (isset($validatedParameters['UserId'])  || isset($validatedParameters['AccessToken']) || isset($validatedParameters['PageNo'])) {
        $UserId = $validatedParameters['UserId'];
        $accesstoken = $validatedParameters['AccessToken'];
        $PageNo = $validatedParameters['PageNo'];
    }

    //-----------------------------------
    //Validate AccessToken
    //-----------------------------------
    if (ValidateUserAccessToken($UserId, $accesstoken) == false) {
        CreateResponce($objStatusCode::HTTP_UNAUTHORIZED, $isFalse, 'You are not authorized user to use this service');
        exit();
    }


    //Validate type
    //-----------------------------------	
    if (isset($_GET['Type'])) {
        $Type = trim($_GET['Type']);
    }

    //-----------------------------------
    // SearchText
    if (isset($_GET['SearchText'])) {
        $searchText = $_GET['SearchText'];
    }
    // Records per page
    //-----------------------------------	
    $recordsPerPage = 10;
    if (isset($_GET['RecordsPerPage'])) {
        $recordsPerPage = $_GET['RecordsPerPage'];
    }
    //Validate rotationId
    //-----------------------------------	
    if (isset($_GET['RotationId']) && $_GET['RotationId'] != '') {
        $rotationId = ($_GET['RotationId']);
    }

    //-----------------------------------
    // SearchText
    if (isset($_GET['SearchText'])) {
        $searchText = $_GET['SearchText'];
    }


    //-----------------------------------
    //Validate User

    $objUser = $userType ? new clsClinician() : new clsStudent();

    $getUserDetailsMethod = $userType ? 'GetClinicianDetails' : 'GetStudentDetails';
    $rowUser = $objUser->$getUserDetailsMethod($UserId);

    if ($rowUser == '') {
        CreateResponce($objStatusCode::HTTP_UNAUTHORIZED, $isFalse, 'Invalid User');
        exit();
    }
    $SchoolId = $rowUser['schoolId'];
    $isActiveCheckoff = $rowUser['isActiveCheckoff'];
    // echo $isActiveCheckoff;exit;

    $getUserDetailsMethod = $userType ? 'GetClinicianTimeZoneByClinicianId' : 'GetStudentTimezone';
    $UserTimezone = $objUser->$getUserDetailsMethod($UserId);
    unset($objUser);

    $clinicianId = 0;
    if ($loggedClinicianType == 'p' || $loggedClinicianType == 'P' || $loggedClinicianType == 'C' || $loggedClinicianType == 'c') {

        $clinicianId = $UserId;
    }

    $UserId = $userType ? 0 : $UserId;

    // $studentId = $userType ? $studentId : $UserId;

    // $objStudent = new clsStudent();
    // $rowUser = $objStudent->GetUserDetail($UserId);
    // unset($objStudent);

    // if ($rowUser == '') {
    //     CreateResponce($objStatusCode::HTTP_UNAUTHORIZED, $isFalse, 'Invalid User');
    //     exit();
    // }

    // $SchoolId = $rowUser['schoolId'];

    // $objStudent = new clsStudent();
    // $studentTimezone = $objStudent->GetStudentTimezone($UserId);
    // unset($objStudent);



    $objRotation = new clsRotation();



    $objDB = new clsDB();
    $casestudySettings = '';
    $casestudySettings = $objDB->GetSingleColumnValueFromTable('schools', 'casestudySettings', 'schoolId', $SchoolId);
    unset($objDB);


    //Get All Case Study List
    $objCaseStudy = new clsCaseStudy();
    $generateLimitString = GenerateOrderLimitString($PageNo, $recordsPerPage);

    $casestudySettingValues = explode(",", $casestudySettings);
    $isSelectedTab = 'All';
    $getCasestudydetails = '';
    $totalRecordsList = '';
    $totalCaseStudyCount = 0;

    if ($userType) {
        if ($Type == 'Floor' && in_array("2", $casestudySettingValues)) {
            $isSelectedTab = $Type;
            $getCasestudydetails = $objCaseStudy->GetAllFloorCaseStudyForClinician($SchoolId, $rotationId, $UserId, 0, $generateLimitString, $searchText, $clinicianId);
            $totalRecordsList = $objCaseStudy->GetAllFloorCaseStudyForClinician($SchoolId, $rotationId, $UserId, 0, '', $searchText, $clinicianId);
        } elseif ($Type == 'Adult' && in_array("3", $casestudySettingValues)) {
            $isSelectedTab = $Type;
            $getCasestudydetails = $objCaseStudy->GetAllAdultCaseStudyForClinician($SchoolId, $rotationId, $UserId, 0, $generateLimitString, $searchText, $clinicianId);
            $totalRecordsList = $objCaseStudy->GetAllAdultCaseStudyForClinician($SchoolId, $rotationId, $UserId, 0, '', $searchText, $clinicianId);
        } elseif ($Type == 'PACR' && in_array("1", $casestudySettingValues)) {
            $isSelectedTab = $Type;
            $getCasestudydetails = $objCaseStudy->GetAllPACRCaseStudyForClinician($SchoolId, $rotationId, $UserId, 0, $generateLimitString, $searchText, $clinicianId);
            $totalRecordsList = $objCaseStudy->GetAllPACRCaseStudyForClinician($SchoolId, $rotationId, $UserId,  0, '', $searchText, $clinicianId);
        } elseif ($Type == 'All') {
            $isSelectedTab = 'All';
            $getCasestudydetails = $objCaseStudy->GetAllCaseStudyForClinician($SchoolId, $rotationId, $UserId, $casestudySettings, $generateLimitString, $searchText, $clinicianId);
            $totalRecordsList = $objCaseStudy->GetAllCaseStudyForClinician($SchoolId, $rotationId, $UserId,  $casestudySettings, '', $searchText, $clinicianId);
        }
    } else {
        if ($Type == 'Floor' && in_array("2", $casestudySettingValues)) {
            $isSelectedTab = $Type;
            $getCasestudydetails = $objCaseStudy->GetAllFloorCaseStudy($SchoolId, $rotationId, $UserId, 0, $generateLimitString, $searchText);
            $totalRecordsList = $objCaseStudy->GetAllFloorCaseStudy($SchoolId, $rotationId, $UserId, 0, '', $searchText);
        } elseif ($Type == 'Adult' && in_array("3", $casestudySettingValues)) {
            $isSelectedTab = $Type;
            $getCasestudydetails = $objCaseStudy->GetAllAdultCaseStudy($SchoolId, $rotationId, $UserId, 0, $generateLimitString, $searchText);
            $totalRecordsList = $objCaseStudy->GetAllAdultCaseStudy($SchoolId, $rotationId, $UserId, 0, '', $searchText);
        } elseif ($Type == 'PACR' && in_array("1", $casestudySettingValues)) {
            $isSelectedTab = $Type;
            $getCasestudydetails = $objCaseStudy->GetAllPACRCaseStudy($SchoolId, $rotationId, $UserId, 0, $generateLimitString, $searchText);
            $totalRecordsList = $objCaseStudy->GetAllPACRCaseStudy($SchoolId, $rotationId, $UserId,  0, '', $searchText);
        } elseif ($Type == 'All') {
            $isSelectedTab = 'All';
            $getCasestudydetails = $objCaseStudy->GetAllCaseStudyForApp($SchoolId, $rotationId, $UserId, $casestudySettings, $generateLimitString, $searchText);
            $totalRecordsList = $objCaseStudy->GetAllCaseStudyForApp($SchoolId, $rotationId, $UserId,  $casestudySettings, '', $searchText);
        }
    }
    if ($getCasestudydetails != '') {
        $totalCaseStudyCount = mysqli_num_rows($getCasestudydetails);
    }
    $totalRecords = ($totalRecordsList != '') ? mysqli_num_rows($totalRecordsList) : 0;
    $pager = array();
    $pager = array(
        "PageNumber" => strval($PageNo),
        "RecordsPerPage" => strval($recordsPerPage),
        "TotalRecords" => strval($totalRecords)
    );

    $CasestudyListArray = array();
    $data = array();

    $TimeZone = "UTC";
    if ($totalCaseStudyCount) {
        while ($row = mysqli_fetch_assoc($getCasestudydetails)) {
            $casestudyId = $row['caseStudyId'];
            $rotationId = $row['rotationId'];

            $studentId = $row['studentId'];
            $firstName = $row['studentfirstName'];
            $lastName = $row['studentlastName'];
            $studentFullName = $firstName . ' ' . $lastName;

            $rotationName = $row['rotationname'];
            $chiefComplaint = $row['ChiefComplaint'];
            $chiefComplaint = str_replace(array("\r", "\n"), '', $chiefComplaint);
            $type = ucwords($row['type']);
            $clinicianDate = $row['ClinicianDate'];
            $schoolDate = $row['schoolDate'];
            $cliniciancomment = $row['clinician_comments'];
            $schoolcomment = $row['school_comments'];
            $schoolcomment = ($schoolcomment != '') ? 'Yes' : 'No';
            $cliniciancomment = ($cliniciancomment != '') ? 'Yes' : 'No';

            if ($type  == 'PACR') {
                $typeName = "PACR";
            }
            if ($type  == 'Adult') {
                $typeName = "Adult ICU & NICU/PICU";
            }
            if ($type  == 'Floor') {
                $typeName = "Floor Therapy";
            }

            // Case Study Status
            $caseStudyStatus = $row['status'];
            if ($caseStudyStatus == '0') {
                $caseStudyStatus = 'Pending';
            } else if ($caseStudyStatus == '1') {
                $caseStudyStatus = 'Completed';
            }
            $rsClinicianReviewsCount = 0;
            $rsClinicianReviews = $objCaseStudy->GetClinicianReviews($casestudyId);
            $rsClinicianReviewsCount = ($rsClinicianReviews != '') ? mysqli_num_rows($rsClinicianReviews) : 0;
            $clinicianDate = $row['ClinicianDate'];
            if ($clinicianDate) {
                $clinicianDate = converFromServerTimeZone($clinicianDate, $TimeZone);
                $clinicianDate = date("Y-m-d H:i:s", strtotime($clinicianDate));
            } else if ($clinicianDate == '' && $rsClinicianReviewsCount) {
                $cliniciancomment = 'Under Review';
                $isActionBtnName = 'Edit';
                $clinicianDate = '-';
            } else {
                $clinicianDate = '-';
            }
            $schoolDate = $row['schoolDate'];
            if ($schoolDate) {
                $schoolDate = converFromServerTimeZone($schoolDate, $TimeZone);
                $schoolDate = date("Y-m-d H:i:s", strtotime($schoolDate));
                $isSchoolAproved = 1;
            } else {
                $schoolDate = '-';
                $isSchoolAproved = 0;
            }

            $caseStudydate = stripslashes($row['caseStudydate']);
            if ($caseStudydate != '' && $caseStudydate != '0000-00-00 00:00:00') {
                $caseStudydate = converFromServerTimeZone($caseStudydate, $TimeZone);
                $caseStudydate = date("Y-m-d H:i:s", strtotime($caseStudydate));
            } else {
                $caseStudydate = "";
            }

            // 0 = pending , 1= signoff, 2= view, 3= edit
            if ($userType) {
                // For userType true: check caseStudyStatus or clinicianDate
                if ($caseStudyStatus === 'Pending' || $clinicianDate !== '-') {
                    $status = '2';
                } else {
                    $status = '3';
                }
            } else {
                // For userType false: check clinicianDate or schoolDate
                if ($clinicianDate !== '-' || $schoolDate !== '-') {
                    $status = '2';
                } else {
                    $status = '3';
                }
            }



            $rotationStatus = checkRotationStatus($rotationId, $isMobile = 1);
            // $status = ($rotationStatus) ? '2' : $status;
            $isExpire = ($rotationStatus) ? $isTrue : $isFalse;

            $CasestudyListArray[] = array(

                "CaseStudyId" => $casestudyId,
                "StudentId" => strval($studentId),
                "StudentFullName" => strval($studentFullName),
                "RotationId" => $rotationId,
                "RotationName" => $rotationName,
                "ChiefComplaint/Admitting Diagnosis" => $chiefComplaint,
                "CaseStudyDate" => $caseStudydate,
                "Type" => $type,
                "TypeName" => $typeName,
                "SchoolSignoffDate" => $schoolDate,
                "ClinicianSignoffDate" => $clinicianDate,
                "SchoolComment" => $schoolcomment,
                "ClinicianComment" => $cliniciancomment,
                "Status" => $status,
                "CaseStudyStatus" => strval($caseStudyStatus),
                "IsExpire" => $isExpire

            );
        }
    }
    if (!empty($CasestudyListArray)) {
        // Custom sorting function based on CaseStudyDate in descending order
        usort($CasestudyListArray, function ($a, $b) {
            return strtotime($b['CaseStudyDate']) - strtotime($a['CaseStudyDate']);
        });
        $data[] = array(
            "isSelectedTab" => $isSelectedTab,
            "caseStudyList" => $CasestudyListArray
        );
    }
    unset($objCaseStudy);

    unset($objRotation);
    unset($objLocation);


    CreateResponce($objStatusCode::HTTP_OK, $isTrue, 'Action successful', $data, $pager);
    exit;
} else {
    CreateResponce($objStatusCode::HTTP_METHOD_NOT_ALLOWED, $isFalse, 'Method not allowed');
    exit();
}
