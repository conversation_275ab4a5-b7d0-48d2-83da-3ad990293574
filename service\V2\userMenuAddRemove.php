<?php
include("../../includes/config.php");
include("../../class/clsLocations.php");
include("../../class/clsDB.php");
include("../../includes/commonfun.php");
include("../../class/clsStudent.php");
include("../../class/clsStatusCodes.php");
include('validateParameters.php');

$isTrue = true;
$isFalse = false;

$objStatusCode = new clsStatusCodes();

if ($_SERVER['REQUEST_METHOD'] === 'GET') {

    $userType = isset($_GET['UserType']) ? $_GET['UserType'] : 0;

    $requestParameters = array(
        'UserId' => isset($_GET['UserId']) ? $_GET['UserId'] : 0,
        'AccessToken' => isset($_GET['AccessToken']) ? $_GET['AccessToken'] : ''
    );

    // Passarry for Vaildation
    $validatedParameters = validateParameters($requestParameters, $objStatusCode, $isFalse, $isTrue);

    // Access the validated parameters
    if (isset($validatedParameters['UserId']) || isset($validatedParameters['AccessToken'])) {
        $UserId = $validatedParameters['UserId'];
        $accesstoken = $validatedParameters['AccessToken'];
    }

    $objDB = new clsDB();
    if ($userType) {
        $currentSchoolId = $objDB->GetSingleColumnValueFromTable('clinician', 'schoolId', 'clinicianId', $UserId);
    } else {
        $currentSchoolId = $objDB->GetSingleColumnValueFromTable('student', 'schoolId', 'studentId', $UserId);
    }

    $isActivitySheet = $objDB->GetSingleColumnValueFromTable('schools', 'activitySheet', 'schoolId', $currentSchoolId);
    $isActiveCheckoff = $objDB->GetSingleColumnValueFromTable('schools', 'isActiveCheckoffForStudent', 'schoolId', $currentSchoolId);

    if ($isActivitySheet)
        $activitySheet = $isTrue;
    else
        $activitySheet = $isFalse;

    $isChat = $objDB->GetSingleColumnValueFromTable('schoolsettings', 'status', 'schoolId', $currentSchoolId, 'type', 'chat');


    if ($isChat)
        $chat = $isTrue;
    else
        $chat = $isFalse;

    if ($userType) {
        //clinician
        $MenuVisibility = array(
            "Student Attendance" => $isTrue,
            "Case Study" => $isTrue,
            "Incident" => $isTrue,
            "Briefcase" => $isTrue,
            "Exception" => $isTrue,
            "Medical Terminology" => $isFalse,
            "Dr. Interaction" => $isTrue,
            "Daily Journal" => $isTrue,
            "Formative" => $isTrue,
            "Daily/Weekly" => $isTrue,
            "Midterm" => $isTrue,
            "Summative" => $isTrue,
            // "Mastery Evaluation" => $isFalse,
            "CI Evaluation" => $isTrue,
            "P Evaluation" => $isFalse,
            "Site Evaluation" => $isFalse,
            // "Volunter Evaluation" => $isFalse,
            // "Floor Therapy And ICU" => $isFalse,
            // "PEF Evaluation" => $isFalse,
            "Equipment List" => $isTrue,
            "Checkoff" => $isTrue,
            "Procedure Count" => $isTrue,
            "Chat" => $chat,
            "Help" => $isFalse,
            "My Attendance" => $isTrue,
            "IRR" => $isTrue,
            "Activity Sheet" => $activitySheet,
            "DCE CI Evaluation" => $isTrue

        );
    } else {
        //student
        $MenuVisibility = array(
            "Attendance" => $isTrue,
            "Case Study" => $isTrue,
            "Incident" => $isTrue,
            "Briefcase" => $isTrue,
            "Exception" => $isTrue,
            "Medical Terminology" => $isTrue,
            "Dr. Interaction" => $isTrue,
            "Daily Journal" => $isTrue,
            "Formative" => $isTrue,
            "Daily/Weekly" => $isTrue,
            "Midterm" => $isTrue,
            "Summative" => $isTrue,
            "CI Evaluation" => $isTrue,
            "P Evaluation" => $isTrue,
            "Site Evaluation" => $isTrue,
            "Equipment List" => $isTrue,
            "Activity Sheet" => $activitySheet,
            "Checkoff" => $isTrue,
            "Chat" => $chat,
            "Help" => $isFalse
        );
    }
    // Add conditional entries
    if ($isActiveCheckoff == 0) {
        $MenuVisibility["Mastery Evaluation"] = $isTrue;
    } else if ($isActiveCheckoff == 1) {
        $MenuVisibility["Volunter Evaluation"] = $isTrue;
        $MenuVisibility["Floor Therapy And ICU"] = $isTrue;
        $MenuVisibility["PEF Evaluation"] = $isTrue;
    }

    CreateResponce($objStatusCode::HTTP_OK, $isTrue, 'Action successful', $MenuVisibility);
} else {
    CreateResponce($objStatusCode::HTTP_METHOD_NOT_ALLOWED, $isFalse, 'Method not allowed');
    exit();
}
