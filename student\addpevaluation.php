<?php
// Redirect from mobile
$IsMobile = isset($_GET['IsMobile']) ? ($_GET['IsMobile']) : 0;
include('../includes/config.php');
include('includes/validateUserLogin.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsRotation.php');
include('../class/clsLocations.php');
include('../class/clsHospitalSite.php');
include('../class/clsStudent.php');
include('../class/clsFormative.php');
include('../setRequest.php');
include('../class/clsQuestionOption.php');
include('../class/clsSectionStudentName.php');
include('../class/clsClinician.php');
include('../class/clsPEvaluation.php');

$objDB = new clsDB();

$schoolId = 0;
$rotationId = 0;
$studentId = 0;
$pEvaluationMasterId = 0;
$clinicianId = 0;
$pevaluationrotationid = 0;
$patientCareAdultAreaId = 0;
$patientCarePediatricAreaId = 0;
$patientCareNeonatalaAreaId = 0;
$hospitalSiteId = 0;
$courselocationId = 0;
$totalSection = 0;
$parentRotationId = 0;
$rotationLocationId = 0;
$display_to_date = date('m/d/Y');
$evaluationDate = '';
$currentDate = date('m/d/Y');
$TimeZone = $_SESSION["loggedStudentSchoolTimeZone"];
$preceptorName = '';
//object
$objRotation = new clsRotation();

if (isset($_GET['pevaluationrotationid'])) {
	$pevaluationrotationid = DecodeQueryData($_GET['pevaluationrotationid']);
}

//For Edit P Evaluation
if (isset($_GET['pEvaluationMasterId']) && ($_GET['pevaluationrotationid'])) {
	$pEvaluationMasterId = DecodeQueryData($_GET['pEvaluationMasterId']);
	$schoolId = $currentSchoolId;
	$page_title = "Edit P Evaluation ";
	$bedCrumTitle = 'Edit';

	//Get CI Evalution Details
	$objPevaluation = new clsPEvaluation();
	$rowPevaluation = $objPevaluation->GetEvaluationDetails($pEvaluationMasterId);

	if ($rowPevaluation == '') {
		header('location:pevaluationlist.html');
		exit;
	}
	$rotationId = ($rowPevaluation['rotationId']);
	$clinicianId = ($rowPevaluation['clinicianId']);
	$hospitalSiteId = ($rowPevaluation['schoolClinicalSiteUnitId']);
	$patientCareAdultAreaId = ($rowPevaluation['patientCareAdultAreaId']);
	$patientCarePediatricAreaId = ($rowPevaluation['patientCarePediatricAreaId']);
	$patientCareNeonatalaAreaId = ($rowPevaluation['patientCareNeonatalaAreaId']);
	$evaluationDate = ($rowPevaluation['evaluationDate']);


	$courselocationId = $rowPevaluation['locationId'];
	$parentRotationId = stripslashes($rowPevaluation['parentRotationId']);
	$rotationLocationId = stripslashes($rowPevaluation['rotationLocationId']);

	$locationId = 0;
	if ($rotationLocationId != $courselocationId && $parentRotationId > 0) {
		if ($parentRotationId > 0) {
			if (!$rotationLocationId)
				$locationId = $objRotation->GetLocationByRotation($rotationId);
			else
				$locationId  = $rotationLocationId;
		}
	} else {
		$locationId  = $courselocationId;
	}

	//Get Time Zone By Rotation 
	$objLocation = new clsLocations();
	$TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
	unset($objLocation);
	if ($TimeZone == '')
		$TimeZone = $_SESSION["loggedStudentSchoolTimeZone"];

	$evaluationDate = converFromServerTimeZone($evaluationDate, $TimeZone);
	$evaluationDate = date('m/d/Y', strtotime($evaluationDate));
	$studentId = ($rowPevaluation['studentId']);
	$preceptorName = isset($rowPevaluation['preceptorName']) ? $rowPevaluation['preceptorName'] : '';
} else {
	$schoolId = $currentSchoolId;
	$page_title = "Add P Evaluation";
	$bedCrumTitle = 'Add';
}

//----------------------------//
//Get Clinician Names
$objClinician = new clsClinician();
$Clinician = $objClinician->GetClinicianByRotation($currentSchoolId, $pevaluationrotationid);
unset($objClinician);
//--------------------------//
$objStudent = new clsStudent();
$Student = $objStudent->GetStudentsByRotation($currentSchoolId, $pevaluationrotationid);
unset($objStudent);

///----------------------//
$objPevaluation = new clsPEvaluation();

$CIevaluationSection = $objPevaluation->GetSections($schoolId,1);
if ($CIevaluationSection != '') {
	$totalSection = mysqli_num_rows($CIevaluationSection);
}

//Get all patient area names in dropdown
$PatientCareAdultArea = $objPevaluation->GetPatientCareAdultArea($currentSchoolId);
$PatientCarePediatricArea = $objPevaluation->GetPatientCarePediatricArea($currentSchoolId);
$PatientCareNeonatalArea = $objPevaluation->GetPatientCareNeonatalArea($currentSchoolId);

//Get Hospital Site
$objHospitalSite = new clsHospitalSite();
$hospitalSite = $objHospitalSite->GetAllHospitalSite($currentSchoolId);
unset($objHospitalSite);

//Get Rotation Name

$RotationName = $objRotation->GetrotationDetails($pevaluationrotationid, $schoolId);

$rotationtitle = $RotationName['title'];
$endDate = $RotationName['endDate'];
//For Schedule
$isSchedule = $RotationName['isSchedule'];
$parentsRotationId = $RotationName['parentRotationId'];
if ($isSchedule)
	$endDate = $objDB->GetSingleColumnValueFromTable('rotation', 'endDate', 'rotationId', $parentsRotationId);
//-----------------
$endDate = date('m/d/Y', strtotime($endDate));

$view = isset($_GET['view']) ? $_GET['view'] : 0;
$bedCrumTitle = ($view) ? 'View' : $bedCrumTitle;
?>
<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
	<!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
	<title>
		<?php echo ($page_title); ?>
	</title>
	<?php include('includes/headercss.php'); ?>
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />

	<style type="text/css">
		.some-class {
			float: left;
			clear: none;
		}

		.input-group-addon {
			position: absolute;
			right: 7px;
			/* width: 100%; */
			z-index: 99;
			width: 35px;
			margin: auto;
			top: 5px;
			border-left: 1px solid #ccc;
			border-radius: 50% !important;
			padding: 10px -2px;
			height: 35px;
			/* background: #01A750; */
			/* color: #fff; */
			color: #555;
			background: #f6f9f9;
			border: none;
			display: flex;
			justify-content: center;
			align-items: center;
		}

		.panel-default>.panel-heading {
			background-color: transparent !important;
		}

		.btn-success,
		.btn-default {
			padding: 8px 25px;
			border-radius: 10px;
		}

		.panel {
			border-radius: 14px !important;
		}

		.input-group-addon {
			position: absolute;
			right: 7px;
			/* width: 100%; */
			z-index: 99;
			width: 35px;
			margin: auto;
			top: 5px;
			border-left: 1px solid #ccc;
			border-radius: 50% !important;
			padding: 10px -2px;
			height: 35px;
			/* background: #01A750; */
			/* color: #fff; */
			color: #555;
			background: #f6f9f9;
			border: none;
			display: flex;
			justify-content: center;
			align-items: center;
		}

		.panel-default>.panel-heading {
			background-color: transparent !important;
		}

		.btn-success,
		.btn-default {
			padding: 8px 25px;
			border-radius: 10px;
		}

		.panel {
			border-radius: 14px !important;
		}

		.select2-container--default .select2-selection--single .select2-selection__rendered {
			line-height: 45px !important;
		}

		.required-select2 {
			border-left: solid 3px red !important;
			border-radius: 12px !important;
		}

		.select2-container--default .select2-selection--single {
			background-color: #f6f9f9 !important;
			cursor: default !important;
			height: 45px !important;
			border-radius: 10px !important;
		}

		.select2-container--default .select2-selection--single {
			border: none !important;
		}

		.panel,
		.form-group {
			margin-bottom: 10px;
		}

		.bootstrap-datetimepicker-widget {
			border-radius: 12px !important;
		}

		.form-control {
			height: 45px;
		}

		/* Style for the collapsible content */
		.panel-collapse {
			display: none;
			/* Hidden by default */
			/* padding: 10px;
            border: 1px solid #ddd;
            background-color: #f9f9f9; */
		}

		/* Style for the collapsible button */
		.collapsible {
			/* background-color: #3498db;
            color: white;
            padding: 10px;
            border: none; */
			width: 100%;
			cursor: pointer;
			display: flex;
			justify-content: space-between;
			/* Align content horizontally */
		}

		.panel-heading {
			width: 100%;
		}

		/* Style for the arrow icons */
		.arrow-icon {
			transition: transform 0.3s;
		}

		.collapsible.expanded .arrow-icon i {
			transform: rotate(180deg);
		}


		@media screen and (max-width: 500px) {
			.panel-body ol {
				padding-left: 20px;
			}

			.panel-default>.panel-heading {
				padding-left: 5px;
			}

			#CIevaluationSection p {
				padding-left: 70px !important;
			}

			.mobile-px-0 {
				padding-left: 0;
				padding-right: 0;
			}
		}
	</style>

</head>

<body>
	<?php if ($IsMobile == 0) { ?>

		<?php include('includes/header.php'); ?>

		<div class="row margin_zero breadcrumb-bg">
			<div class="container">
				<div class="pull-left">
					<ol class="breadcrumb">
						<li><a href="dashboard.html">Home</a></li>
						<li><a href="rotations.html">Rotation</a></li>
						<li><a href="rotations.html">
								<?php echo ($rotationtitle); ?>
							</a></li>
						<li><a href="pevaluationlist.html?pevaluationrotationid=<?php echo EncodeQueryData($pevaluationrotationid); ?>">P
								Evaluation</a></li>
						<li class="active">
							<?php echo ($bedCrumTitle); ?>
						</li>
					</ol>
				</div>

			</div>
		</div>
	<?php  } else  ?>

	<div class="container">

		<form id="frmcievaluation" data-parsley-validate class="form-horizontal" method="POST" action="addpevaluationsubmit.html?pEvaluationMasterId=<?php echo (EncodeQueryData($pEvaluationMasterId)); ?>
																									&pevaluationrotationid=<?php echo (EncodeQueryData($pevaluationrotationid)); ?>">

			<div class="row">
				<!-- Mobile redirect -->
				<input type="hidden" name="IsMobile" id="IsMobile" value="<?php echo $IsMobile; ?>">

				<!-- <div class="col-md-6">
				
				<div class="form-group">
                        <label class="col-md-4 control-label" for="cboclinician">Clinician</label>
                        <div class="col-md-8">
                            <select id="cboclinician" name="cboclinician"  
									class="form-control input-md required-input select2_single" onChange="getHospital(this.value);"  required>
                            <option value="" selected>Select</option>
								<?php
								if ($Clinician != "") {
									while ($row = mysqli_fetch_assoc($Clinician)) {
										$selClinicianId  = $row['clinicianId'];
										$firstname  = stripslashes($row['firstName']);
										$lastname  = stripslashes($row['lastName']);

										$name = $firstname . ' ' . $lastname;


								?>
										<option value="<?php echo ($selClinicianId); ?>" <?php if ($clinicianId == $selClinicianId) { ?>  selected="true" <?php } ?>><?php echo ($name); ?></option>
                                        
										<?php

									}
								}

										?>
                            </select>
                        </div>
                    </div>
					
				</div> -->



			</div>
			<div class="row">
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-12 control-label" for="evaluationDate">Evaluation Date</label>
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class='input-group date w-full' id='evaluationDate' style="position: relative;">

								<input type='text' name="evaluationDate" id="evaluationDate" class="form-control input-md required-input rotation_date" value="<?php echo ($evaluationDate); ?>" required data-parsley-errors-container="#error-txtDate" placeholder="MM-DD-YYYY" />
								<span class="input-group-addon calender-icon">
									<span class="glyphicon glyphicon-calendar"></span>
								</span>
							</div>
							<div id="error-txtDate"></div>
						</div>
					</div>

				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-12 control-label" for="cbohospitalsites">Hospital Site</label>
						<div class="col-md-12">
							<select id="cbohospitalsites" name="cbohospitalsites" class="form-control input-md required-input select2_single" required data-parsley-errors-container="#cbohospitalsitese-error">
								<option value="" selected>Select</option>
								<?php
								if ($hospitalSite != "") {
									while ($row = mysqli_fetch_assoc($hospitalSite)) {
										$schoolClinicalSiteUnitId  = $row['hospitalSiteId'];

										$name  = stripslashes($row['title']);

								?>
										<option value="<?php echo ($schoolClinicalSiteUnitId); ?>" <?php
																									if ($schoolClinicalSiteUnitId == $hospitalSiteId) { ?> selected="true" <?php } ?>>
											<?php echo ($name); ?>
										</option>
								<?php

									}
								}
								?>


							</select>
							<div id="cbohospitalsitese-error"></div>
							<!-- <p id="hospitalerror" style="color:#E74C3C; border: 1px solid #E8544;"></p> -->
						</div>
					</div>
				</div>

				<!-- <div class="col-md-6">						
					<div class="form-group">
						<label class="col-md-4 control-label" for="Adult">Patient Care Adult Area</label>
								<div class="col-md-8">
									<select id="Adult"   name="Adult"   class="form-control input-md  select2_single">
									<option value="" selected>select</option>
										<?php
										if ($PatientCareAdultArea != "") {
											while ($row = mysqli_fetch_assoc($PatientCareAdultArea)) {
												$selpatientCareAdultAreaId  = $row['patientCareAdultAreaId'];
												$name  = stripslashes($row['patientCareAdultAreaName']);

										?>
												  <option value="<?php echo ($selpatientCareAdultAreaId); ?>" <?php if ($patientCareAdultAreaId == $selpatientCareAdultAreaId) { ?> selected="true" <?php } ?> ><?php echo ($name); ?></option>
												 <?php

												}
											}
													?>
									</select>
								</div>
					</div>
							
				</div> -->
			</div>
			<div class="row">
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-12 control-label" for="preceptorName">Preceptor Name</label>
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class='input-group date w-full' id='preceptorName' style="position: relative;">

								<input type='text' name="preceptorName" id="preceptorName" class="form-control input-md" value="<?php echo ($preceptorName); ?>" data-parsley-errors-container="#error-preceptorName" />

							</div>
							<div id="error-preceptorName"></div>
						</div>
					</div>

				</div>
			</div>
			<!-- <div class="row">
						<div class="col-md-6">
							<div class="form-group">
								<label class="col-md-4 control-label" for="Pediatric">Patient Care Pediatric Area</label>
								<div class="col-md-8">
									<select id="Pediatric"   name="Pediatric"   class="form-control input-md  select2_single" >
									<option value="" selected>select</option>
										<?php
										if ($PatientCarePediatricArea != "") {
											while ($row = mysqli_fetch_assoc($PatientCarePediatricArea)) {
												$selpatientCarePediatricAreaId  = $row['patientCarePediatricAreaId'];
												$name  = stripslashes($row['patientCarePediatricAreaName']);

										?>
												  <option value="<?php echo ($selpatientCarePediatricAreaId); ?>" <?php if ($patientCarePediatricAreaId == $selpatientCarePediatricAreaId) { ?> selected="true" <?php } ?> ><?php echo ($name); ?></option>
												 <?php

												}
											}
													?>
									</select>
								</div>
							</div>
						</div>
						<div class="col-md-6">
							<div class="form-group">
								<label class="col-md-4 control-label" for="Neonatal">Patient Care Neonatal Area</label>
								<div class="col-md-8">
										<select id="Neonatal"   name="Neonatal"   class="form-control input-md  select2_single" >
									<option value="" selected>select</option>
										<?php
										if ($PatientCareNeonatalArea != "") {
											while ($row = mysqli_fetch_assoc($PatientCareNeonatalArea)) {
												$selpatientCareNeonatalaAreaId  = $row['patientCareNeonatalAreaId'];
												$name  = stripslashes($row['patientCareNeonatalAreaName']);

										?>
												  <option value="<?php echo ($selpatientCareNeonatalaAreaId); ?>" <?php if ($patientCareNeonatalaAreaId == $selpatientCareNeonatalaAreaId) { ?> selected="true" <?php } ?> ><?php echo ($name); ?></option>
												 <?php

												}
											}
													?>
									</select>
								</div>
							</div>
						</div>
				</div> -->
			<!-- 1st SECTION div start -->
			<div class="row">
				<div class="col-md-12">
					<div class="form-group">
						<label class="col-md-12 control-label" for="instructions:"></label>
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class="panel panel-default">
								<div class="panel-body">
									<p>The preceptor's ratings are based on the following categories. The rating scale
										for each category is<br>
										<b>1-Strongly Disagree, 2-Disagree, 3-Neutral/Acceptable, 4-Agree, 5-Strongly
											Agree.</b>
									</p>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-md-12">
					<div class="form-group">
						<!-- <label class="col-md-12 control-label" for="instructions:"></label> -->
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class="panel-group" id="posts">
								<div class="panel panel-default">
									<a class="collapsible" style="color: #000; text-decoration: none;" href="#CIevaluationSection" data-toggle="collapse" data-parent="#posts" id="collapse-link">
										<div class="panel-heading" style="display: flex; justify-content: space-between; align-items: center;">
											<h4 class="panel-title">
												<b>Student Preceptor Evaluation</b>
											</h4>
											<span class="arrow-icon"> <i class="fa fa-angle-down" aria-hidden="true" id="collapse-icon" style="font-size: 19px;"></i></span>
										</div>
									</a>
									<div id="CIevaluationSection" class="panel-collapse collapse">
										<?php while ($row = mysqli_fetch_array($CIevaluationSection)) {
											$sectionMasterId = $row['sectionMasterId'];
											$title = $row['title'];
										?>
											<div class="panel-body mobile-px-0">
												<?php echo '<b>' . $title . '</b>'; ?>
											</div>
											<?php   // for question
											$totalCIevaluation = 0;
											$CIevaluationquestion = $objPevaluation->GetAllEvaluationQuestionMaster($schoolId, $sectionMasterId);

											if ($CIevaluationquestion != '') {
												$totalCIevaluation = mysqli_num_rows($CIevaluationquestion);
											}

											if ($totalCIevaluation > 0) {
												while ($row = mysqli_fetch_array($CIevaluationquestion)) {
													if (isset($_GET['pEvaluationMasterId']))
														$pEvaluationMasterId = DecodeQueryData($_GET['pEvaluationMasterId']);
													else
														$pEvaluationMasterId = 0;

													$schoolPEvaluationQuestionId = $row['schoolPEvaluationQuestionId'];
													$questionText = $row['questionText'];
													$schoolPEvaluationQuestionType = $row['schoolPEvaluationQuestionType'];
													$qhtml = GetPEvaluationQuestionHtml($schoolPEvaluationQuestionId, $schoolPEvaluationQuestionType, $pEvaluationMasterId, $currentSchoolId);

													//Get Question Comment
													$questionComment = '';
													if ($pEvaluationMasterId > 0)
														$questionComment = $objDB->GetSingleColumnValueFromTable('pevaluationdetail', 'comment', 'pEvaluationMasterId', $pEvaluationMasterId, 'schoolPEvaluationQuestionId', $schoolPEvaluationQuestionId);

											?>
													<div class="panel-body isAllRadioButton mobile-px-0">
														<b>
															<?php echo ($questionText); ?>
														</b><br /><br />
														<?php echo $qhtml; ?>
														<?php if ($schoolPEvaluationQuestionType == 2) { ?>
															<textarea name="textarea_<?php echo $schoolPEvaluationQuestionId; ?>" id="textarea_<?php echo $schoolPEvaluationQuestionId; ?>" rows="4" class="form-control"><?php echo $questionComment; ?></textarea>
														<?php } ?>
													</div>
										<?php
												}
											}
										}
										unset($objPevaluation);
										?>
									</div>

								</div>
							</div>

						</div>
					</div>
				</div>
			</div>

			<div class="row" style="margin: 0;">
				<div class="form-group">
					<!-- <label class="col-md-2 control-label"></label> -->
					<div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; justify-content: center; margin: 20px 0;">
						<?php $rotationStatus = checkRotationStatus($pevaluationrotationid);
						if ($rotationStatus == 0) {
							if ($view == 0) {
						?>
								<button style="margin-right: 10px;" id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
						<?php }
						} ?>
						<?php if ($IsMobile) { ?>
							<a type="button" href="<?php echo BASE_PATH ?>/webRedirect.html?status=Cancel&type=pEvaluation" class="btn btn-default">Cancel</a>
						<?php } else { ?>
							<a type="button" href="pevaluationlist.html?pevaluationrotationid=<?php echo EncodeQueryData($pevaluationrotationid); ?>" class="btn btn-default">Cancel</a>
						<?php }
						?>
					</div>
				</div>
			</div>
		</form>


	</div>

	<?php include('includes/footer.php'); ?>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>

	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/jquery.cascadingdropdown.js"></script>




	<script type="text/javascript">
		$(window).load(function() {

			$(".isAllRadioButton").trigger('click');

			$('#frmcievaluation').parsley().on('field:validated', function() {
					var ok = $('.parsley-error').length === 0;
				})
				.on('form:submit', function() {
					ShowProgressAnimation();
					return true; // Don't submit form for this demo
				});

			$('#evaluationDate').datetimepicker({
				format: 'MM/DD/YYYY'
			});


			//for searching dropdown
			$(".select2_single").select2();
			$('#select2-cbohospitalsites-container').addClass('required-select2');
			$('#select2-cboclinician-container').addClass('required-select2');



		});

		$(document).ready(function() {
			$(".isAllRadioButton").click(function() {
				$(".isAllRadioButton input[type=radio]:checked").each(function() {
					var checkboxName = $.trim($(this).attr('name'));
					var checkboxValue = $.trim($(this).parent().text());
					const questionArray = checkboxName.split("_");
					var questionId = questionArray[1];
					questionId = questionId.replace('[]', '');

					//Hide textarea if options are not numeric
					if ($.isNumeric(checkboxValue) == false)
						$('#textarea_' + questionId).addClass('hide');

					//Add Required to textarea
					$('#textarea_' + questionId).prop('required', false);
					$('#textarea_' + questionId).removeClass('required-input');
					if (checkboxValue <= 3) {
						$('#textarea_' + questionId).addClass('required-input');
						$('#textarea_' + questionId).prop('required', true);
					}

				});
			});


			var pEvaluationMasterId = '<?php echo $pEvaluationMasterId; ?>';
			if (pEvaluationMasterId == 0) {
				$('p').each(function() {
					// Check if the text inside the paragraph contains only "3" (after trimming extra spaces)
					if ($(this).text().trim() === '3') {
						// Find the radio button within the same paragraph and check it
						$(this).find('input[type="radio"]').prop('checked', true);
					}
				});
			}

		});

		function getHospital(val, selectedval) {
			selectedval = selectedval == undefined ? 0 : selectedval;

			$.ajax({
				type: "POST",
				url: "<?php echo ($dynamicOrgUrl); ?>/student/get_hospital.html",

				data: 'Clinician_Id=' + val + "&HospitalSite_Id=" + selectedval,
				success: function(data) {
					$("#cbohospitalsites").html(data);
				}
			});
		}
	</script>
	<!-- collapsible script -->
	<script>
		// Get all collapsible button elements
		var buttons = document.querySelectorAll(".collapsible");
		var contents = document.querySelectorAll(".panel-collapse");

		// Add click event listeners to all buttons
		buttons.forEach(function(button, index) {
			button.addEventListener("click", function() {
				// Check if the content is currently expanded
				var isExpanded = contents[index].style.display === "block";

				// Close all sections
				contents.forEach(function(content) {
					content.style.display = "none";
				});

				// Reset the "expanded" class for all buttons
				buttons.forEach(function(btn) {
					btn.classList.remove("expanded");
				});

				// Toggle the content for the clicked section
				if (!isExpanded) {
					contents[index].style.display = "block";
					button.classList.add("expanded");
				}
			});
		});
	</script>
</body>

</html>