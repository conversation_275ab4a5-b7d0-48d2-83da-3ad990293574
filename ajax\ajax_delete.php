<?php
include('../class/clsDB.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsLogger.php');
include('../class/clsChatApp.php');
@session_start();
// print_r($_GET);


if (isset($_GET['id']) && isset($_GET['type'])) {

	$deleteId = DecodeQueryData($_GET['id']);
	if ($_GET['type'] == 'school') {
		include('../class/clsSchool.php');
		$objSchool = new clsSchool();
		$objSchool->DeleteSchool($deleteId);
		unset($objSchool);
	} else if ($_GET['type'] == 'clinicianrole') {

		include('../class/clsClinicianRoleMaster.php');

		$objClinicianRankMaster = new clsClinicianRoleMaster();
		// $objClinicianRankMaster->DeleteClinicianRole($deleteId);

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

		// $isUser 1 for Admin and 2 for Clinician and 3 for Student
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$logAction = $objLog::DELETE;
		if ($isUser == 1) {
			$userType = $objLog::ADMIN;
		} else if ($isUser == 3) {
			$userType = $objLog::STUDENT;
		} else if ($isUser == 2) {
			$userType = $objLog::CLINICIAN;
		}

		$objClinicianRankMaster->saveSchoolClinicianRoleAuditLog($deleteId, $userId, $userType, $logAction);


		unset($objClinicianRankMaster);
	} else if ($_GET['type'] == 'studentRank') {

		include('../class/clsStudentRankMaster.php');

		$objStudentRankMaster = new clsStudentRankMaster();

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$logAction = $objLog::DELETE;
		$userType = $objLog::ADMIN;
		$objStudentRankMaster->saveRankAuditLog($deleteId, $userId, $userType, $logAction);
		unset($objStudentRankMaster);
		unset($objLog);
	} else if ($_GET['type'] == 'systemuser') {
		$schoolId = isset($_GET['schoolId']) ? $_GET['schoolId'] : '';
		include('../class/clsSystemUser.php');

		$objSystemUser = new clsSystemUser();
		// $returnId = MoveDeletedRecordsToArchive($deleteId, $_GET['type']);
		// if ($returnId)
		// $objSystemUser->DeleteSystemUser($deleteId, $schoolId);


		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

		// $isUser 1 for Admin and 2 for Clinician and 3 for Student
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$logAction = $objLog::DELETE;
		$isSuperAdmin = isset($_GET['isCurrentSchoolSuperAdmin']) ? ($_GET['isCurrentSchoolSuperAdmin']) : 0;
		$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN; // User type is set to Admins

		$objSystemUser->saveSchoolAdminAuditLog($deleteId, $userId, $userType, $logAction, 0, $schoolId);

		//Delete User Data from chat app
		$objDB = new clsDB();
		$chatroleId = ($isSuperAdmin) ? 1 : 2;

		$objDB->UpdateSingleColumnValueToTable('userrolemanagement', 'deleted_at', date('Y-m-d H:i:s'), 'userId', $deleteId, 'role_id', $chatroleId);
		$objChatApp = new clsChatApp();
		$objChatApp->DeleteUserDataTochat($deleteId, $chatroleId);

		unset($objSystemUser);
	} else if ($_GET['type'] == 'floorICUEvalution') {
		include('../class/clsFloorTherapyAndICUEvaluation.php');

		$objFloorTherapyAndICUEvaluation = new clsFloorTherapyAndICUEvaluation();

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;
		$isType = isset($_GET['isType']) ? ($_GET['isType']) : 0;

		// $isUser 1 for Admin and 2 for Clinician and 3 for Student
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$logAction = $objLog::DELETE;
		if ($isUser == 1) {
			$userType = $objLog::ADMIN;
		} else if ($isUser == 3) {
			$userType = $objLog::STUDENT;
		} else if ($isUser == 2) {
			$userType = $objLog::CLINICIAN;
		}

		$objFloorTherapyAndICUEvaluation->saveFloorAndIcuEvaluationAuditLog($deleteId, $userId, $userType, $logAction, $isType);
		unset($objFloorTherapyAndICUEvaluation);
		unset($objSystemUser);
	} else if ($_GET['type'] == 'systemuserrole') {
		include('../class/clsSystemUserRoleMaster.php');

		$objSystemUserRoleMaster = new clsSystemUserRoleMaster();
		// $objSystemUserRoleMaster->DeleteSystemUserRole($deleteId);

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

		// $isUser 1 for Admin and 2 for Clinician and 3 for Student
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$logAction = $objLog::DELETE;
		if ($isUser == 1) {
			$userType = $objLog::ADMIN;
		} else if ($isUser == 3) {
			$userType = $objLog::STUDENT;
		} else if ($isUser == 2) {
			$userType = $objLog::CLINICIAN;
		}

		$objSystemUserRoleMaster->saveSchoolAdminRoleAuditLog($deleteId, $userId, $userType, $logAction);


		unset($objSystemUserRoleMaster);
	} else if ($_GET['type'] == 'clinician') {
		include('../class/clsClinician.php');
		$SchoolId = $_GET['SchoolId'];
		$objClinician = new clsClinician();

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

		// $isUser 1 for Admin and 2 for Clinician and 3 for Student
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$logAction = $objLog::DELETE;
		if ($isUser == 1) {
			$userType = $objLog::ADMIN;
		} else if ($isUser == 3) {
			$userType = $objLog::STUDENT;
		} else if ($isUser == 2) {
			$userType = $objLog::CLINICIAN;
		}

		$objClinician->saveClinicianAuditLog($deleteId, $userId, $userType, $logAction, 0, $SchoolId);

		//Delete User Data from chat app
		$objDB = new clsDB();
		$objDB->UpdateSingleColumnValueToTable('userrolemanagement', 'deleted_at', date('Y-m-d H:i:s'), 'userId', $deleteId, 'role_id', 3);
		$objChatApp = new clsChatApp();
		$chatroleId = 3;
		$objChatApp->DeleteUserDataTochat($deleteId, $chatroleId);

		// $returnId = MoveDeletedRecordsToArchive($deleteId, $_GET['type']);
		// if ($returnId)
		// $objClinician->DeleteClinician($deleteId, $SchoolId);
		unset($objClinician);
	} else if ($_GET['type'] == 'location') {
		include('../class/clsLocations.php');

		$objLocations = new clsLocations();

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$logAction = $objLog::DELETE;
		$userType = $objLog::ADMIN;
		$objLocations->saveLocationAuditLog($deleteId, $userId, $userType, $logAction);
		unset($objLocations);
		unset($objLog);
	} else if ($_GET['type'] == 'rotation') {
		include('../class/clsRotation.php');

		$objRotation = new clsRotation();
		$loggedDeletedUserId = $_GET['loggedDeletedUserId'];
		$title = $_GET['title'];

		// Retrieve journal details for the given delete ID
		$rowData = $objRotation->GetAllRotationDetailsForLogs($deleteId);

		$fieldsForLogData = [
			'schoolId' => 0,
			'schoolName' => '',
			'rotationName' => ''
		];
		$objLog = new clsLogger();

		$logData = extractFieldsForLogData($rowData, $fieldsForLogData);


		// Initialize additional data (if any)
		$additionalData = '';

		// Define the fields to extract from journal data for logging
		$fieldsForLogData = [
			'schoolId' => 0,
			'schoolName' => '',
			'rotationName' => ''
		];

		// Extract the required fields for logging
		$logData = extractFieldsForLogData($rowData, $fieldsForLogData);
		$userDetails = getLoggedInUserDetails();
		$logData['userId'] = $userDetails['userId'];
		$logData['userName'] = $userDetails['userName'];
		$logData['userType'] = clsLogger::ADMIN;

		if ($rowData['parentRotationId'] == 0 && $rowData['isSchedule'] == 0) {
			$logMessage = $logData['userName'] . ' deleted the ' . $logData['rotationName'] . ' Rotation.';
		} elseif ($rowData['parentRotationId'] > 0 && $rowData['isSchedule'] == 0) {
			$logMessage = $logData['userName'] . ' deleted the ' . $logData['rotationName'] . ' Hospital Site from ' . $rowData['parentRotationName'] . ' Rotation.';
		} else {
			$logMessage = $logData['userName'] . ' deleted the ' . $logData['rotationName'] . ' Schedule from ' . $rowData['parentRotationName'] . ' Rotation.';
		}

		// Add extra details to the log data
		$logData['message'] = $logMessage;

		$objRotation->Deleterotation($deleteId, $loggedDeletedUserId, $title);
		$objRotation->DeleteParentrotation($deleteId, $loggedDeletedUserId, $title);

		// Save the delete operation details in logs
		$objLog->saveLogs($logData, $objLog::DELETE, $deleteId, 'rotation', $rowData, $additionalData);

		unset($objLog);
		unset($objRotation);
	} else if ($_GET['type'] == 'hospitalSite') {
		include('../class/clsHospitalSite.php');

		$objHospitalSite = new clsHospitalSite();
		// $objHospitalSite->DeleteHospitalSite($deleteId);
		// unset($objHospitalSite);


		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$logAction = $objLog::DELETE;
		$userType = $objLog::ADMIN;
		$objHospitalSite->saveHospitalSiteAuditLog($deleteId, $userId, $userType, $logAction);
		unset($objHospitalSite);
		unset($objLog);
	} else if ($_GET['type'] == 'course') {
		include('../class/clsCourses.php');

		$objCourses = new clsCourses();
		// $objCourses->DeleteCourses($deleteId);

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

		// $isUser 1 for Admin and 2 for Clinician and 3 for Student
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$logAction = $objLog::DELETE;
		if ($isUser == 1) {
			$userType = $objLog::ADMIN;
		} else if ($isUser == 3) {
			$userType = $objLog::STUDENT;
		} else if ($isUser == 2) {
			$userType = $objLog::CLINICIAN;
		}

		$objCourses->saveCourseAuditLog($deleteId, $userId, $userType, $logAction);



		unset($objCourses);
	} else if ($_GET['type'] == 'Document') {
		include('../class/clsStudent.php');

		$objStudent = new clsStudent();

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

		// $isUser 1 for Admin and 2 for Clinician and 3 for Student
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$type = $_GET['type']; // delete type for document
		$logAction = $objLog::DELETE;
		if ($isUser == 1) {
			$userType = $objLog::ADMIN;
		} else if ($isUser == 3) {
			$userType = $objLog::STUDENT;
		} else if ($isUser == 2) {
			$userType = $objLog::CLINICIAN;
		}

		$objStudent->saveStudentAuditLog($deleteId, $userId, $userType, $logAction, 0, 0, $type);

		// $objStudent->DeleteDocuments($deleteId);

		unset($objStudent);
	} else if ($_GET['type'] == 'ClinicianDocument') {
		include('../class/clsClinician.php');

		$objclinician = new clsClinician();

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

		// $isUser 1 for Admin and 2 for Clinician and 3 for Student
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$type = "Document";
		$logAction = $objLog::DELETE;
		if ($isUser == 1) {
			$userType = $objLog::ADMIN;
		} else if ($isUser == 3) {
			$userType = $objLog::STUDENT;
		} else if ($isUser == 2) {
			$userType = $objLog::CLINICIAN;
		}

		$objclinician->saveClinicianAuditLog($deleteId, $userId, $userType, $logAction, 0, 0, $type);

		// $objclinician->DeleteClinicianDocuments($deleteId);

		unset($objclinician);
	} else if ($_GET['type'] == 'student') {
		include('../class/clsStudent.php');
		$SchoolId = $_GET['SchoolId'];
		$objStudent = new clsStudent();

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

		// $isUser 1 for Admin and 2 for Clinician and 3 for Student
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$logAction = $objLog::DELETE;
		if ($isUser == 1) {
			$userType = $objLog::ADMIN;
		} else if ($isUser == 3) {
			$userType = $objLog::STUDENT;
		} else if ($isUser == 2) {
			$userType = $objLog::CLINICIAN;
		}

		$objStudent->saveStudentAuditLog($deleteId, $userId, $userType, $logAction, 0, $SchoolId);
		unset($objStudent);
		// $returnId = MoveDeletedRecordsToArchive($deleteId, $_GET['type']);
		// if ($returnId)
		// $objStudent->DeleteStudent($deleteId, $SchoolId);
		unset($objStudent);

		//Delete User Data from chat app
		$objDB = new clsDB();
		$objDB->UpdateSingleColumnValueToTable('userrolemanagement', 'deleted_at', date('Y-m-d H:i:s'), 'userId', $deleteId, 'role_id', 4);
		$objChatApp = new clsChatApp();
		$chatroleId = 4;
		$objChatApp->DeleteUserDataTochat($deleteId, $chatroleId);
		unset($objChatApp);
	} else if ($_GET['type'] == 'interaction') {
		include('../class/clsInteraction.php');

		$objInteraction = new clsInteraction();

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

		// $isUser 1 for Admin and 2 for Clinician and 3 for Student
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$logAction = $objLog::DELETE;
		if ($isUser == 1) {
			$userType = $objLog::ADMIN;
		} else if ($isUser == 3) {
			$userType = $objLog::STUDENT;
		}

		[$logData, $rowData, $additionalData] = $objInteraction->createInteractionLog($deleteId, $logAction, $userId, $userType);

		$objInteraction->DeleteInteraction($deleteId);

		$objLog->saveLogs($logData, $logAction, $deleteId, 'Interaction', $rowData, $additionalData);

		unset($objLog);
		unset($objInteraction);
	} else if ($_GET['type'] == 'volunteerEval') {
		include('../class/clsVolunteerEval.php');

		$objVolunteerEval = new clsVolunteerEval();

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

		// $isUser 1 for Admin and 2 for Clinician and 3 for Student
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$logAction = $objLog::DELETE;
		if ($isUser == 1) {
			$userType = $objLog::ADMIN;
		} else if ($isUser == 3) {
			$userType = $objLog::STUDENT;
		} else if ($isUser == 2) {
			$userType = $objLog::CLINICIAN;
		}

		$objVolunteerEval->saveVolunteerAuditLog($deleteId, $userId, $userType, $logAction);
		unset($objFloorTherapyAndICUEvaluation);
		unset($objVolunteerEval);
	} else if ($_GET['type'] == 'caseStusyFloor') {
		include('../class/clsCaseStudy.php');

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

		// $isUser 1 for Admin and 2 for Clinician and 3 for Student
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$logAction = $objLog::DELETE;
		if ($isUser == 1) {
			$userType = $objLog::ADMIN;
		} else if ($isUser == 3) {
			$userType = $objLog::STUDENT;
		} else if ($isUser == 2) {
			$userType = $objLog::CLINICIAN;
		}

		$objCaseStudy = new clsCaseStudy();
		$objCaseStudy->saveCaseStudyAuditLog($deleteId, 'floor', $userId, $userType, $logAction);
		unset($objCaseStudy);
	} else if ($_GET['type'] == 'caseStusyAdult') {
		include('../class/clsCaseStudy.php');

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

		// $isUser 1 for Admin and 2 for Clinician and 3 for Student
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$logAction = $objLog::DELETE;
		if ($isUser == 1) {
			$userType = $objLog::ADMIN;
		} else if ($isUser == 3) {
			$userType = $objLog::STUDENT;
		} else if ($isUser == 2) {
			$userType = $objLog::CLINICIAN;
		}
		$objCaseStudy = new clsCaseStudy();
		$objCaseStudy->saveCaseStudyAuditLog($deleteId, 'Adult', $userId, $userType, $logAction);
		unset($objCaseStudy);
	} else if ($_GET['type'] == 'caseStusyPACR') {
		include('../class/clsCaseStudy.php');

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

		// $isUser 1 for Admin and 2 for Clinician and 3 for Student
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$logAction = $objLog::DELETE;
		if ($isUser == 1) {
			$userType = $objLog::ADMIN;
		} else if ($isUser == 3) {
			$userType = $objLog::STUDENT;
		} else if ($isUser == 2) {
			$userType = $objLog::CLINICIAN;
		}
		$objCaseStudy = new clsCaseStudy();
		$objCaseStudy->saveCaseStudyAuditLog($deleteId, 'PACR', $userId, $userType, $logAction);
		unset($objCaseStudy);
	} else if ($_GET['type'] == 'Incident') {
		include('../class/clsStudentIncident.php');

		$objIncident = new clsStudentIncident();
		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

		// $isUser 1 for Admin and 2 for Clinician and 3 for Student
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$logAction = $objLog::DELETE;
		if ($isUser == 1) {
			$userType = $objLog::ADMIN;
		} else if ($isUser == 3) {
			$userType = $objLog::STUDENT;
		} else if ($isUser == 2) {
			$userType = $objLog::CLINICIAN;
		}
		$objIncident->saveIncidentAuditLog($deleteId, $userId, $userType, $logAction);
		unset($objLog);
		unset($objIncident);
	} else if ($_GET['type'] == 'Immunization') {
		include('../class/clsImmunization.php');

		$objimmunization = new clsImmunization();
		// $objimmunization->DeleteImmunization($deleteId);

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

		// $isUser 1 for Admin and 2 for Clinician and 3 for Students
		// Instantiate the Logger class
		$objLog = new clsLogger();
		// $type = $_GET['type']; // delete type for document
		$logAction = $objLog::DELETE;
		if ($isUser == 1) {
			$userType = $objLog::ADMIN;
		} else if ($isUser == 3) {
			$userType = $objLog::STUDENT;
		} else if ($isUser == 2) {
			$userType = $objLog::CLINICIAN;
		}

		$objimmunization->saveStudentImmunizationAuditLog($deleteId, $userId, $userType, $logAction);



		unset($objimmunization);
	} else if ($_GET['type'] == 'ClinicianImmunization') {
		include('../class/clsClinicianImmunization.php');

		$objimmunization = new clsClinicianImmunization();
		// $objimmunization->DeleteClinicianImmunization($deleteId);

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

		// $isUser 1 for Admin and 2 for Clinician and 3 for Students
		// Instantiate the Logger class
		$objLog = new clsLogger();
		// $type = $_GET['type']; // delete type for document
		$logAction = $objLog::DELETE;
		if ($isUser == 1) {
			$userType = $objLog::ADMIN;
		} else if ($isUser == 3) {
			$userType = $objLog::STUDENT;
		} else if ($isUser == 2) {
			$userType = $objLog::CLINICIAN;
		}

		$objimmunization->saveClinicianImmunizationAuditLog($deleteId, $userId, $userType, $logAction);

		unset($objimmunization);
	} else if ($_GET['type'] == 'journal_student') {
		// Include the Journal class
		include('../class/clsJournal.php');

		// Instantiate the Journal class
		$objJournal = new clsJournal();
		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;

		// Instantiate the Logger class
		$objLog = new clsLogger();
		$action = $objLog::DELETE;
		$userType = $objLog::ADMIN;
		[$logData, $rowData, $additionalData] = $objJournal->createJournalLog($deleteId, $action, $userId, $userType);
		// Perform the delete operation on the journal
		$objJournal->DeleteJournal($deleteId);

		// Save the delete operation details in logs
		$objLog->saveLogs($logData, $objLog::DELETE, $deleteId, 'Journal', $rowData, $additionalData);

		unset($objLog);
		unset($objJournal);
	} else if ($_GET['type'] == 'studentFormative') {
		include('../class/clsFormative.php');
		$objFormative = new clsFormative();

		//Audit Log Start
		// Instantiate the Logger class
		$objLog = new clsLogger();

		$action = $objLog::DELETE;
		$userType = $objLog::ADMIN; // User type is set to ADMIN

		$objFormative->saveFormativeAuditLog($deleteId, $deleteId, $_SESSION['loggedUserId'], $userType, $action);
		unset($objLog);

		// $objFormative->DeleteFormative($deleteId);
		unset($objFormative);
	} else if ($_GET['type'] == 'Midterm') {
		include('../class/clsMidterm.php');
		$objMidterm = new clsMidterm();

		//Audit Log Start
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		// $isUser 1 for Admin and 2 for Clinician and 3 for Student
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

		$action = $objLog::DELETE;
		if ($isUser == 1) {
			$userType = $objLog::ADMIN;
		} elseif ($isUser == 2) {
			$userType = $objLog::CLINICIAN;
		}

		$objMidterm->saveMidtermEvalAuditLog($deleteId, $deleteId, $userId, $userType, $action);
		unset($objLog);


		// $objMidterm->DeleteMidterm($deleteId);
		unset($objMidterm);
	} else if ($_GET['type'] == 'Daily/Weekly') {
		include('../class/clsDaily.php');
		$objDaily = new clsDaily();
		//Audit Log Start
		// Instantiate the Logger class
		$objLog = new clsLogger();

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		// $isUser 1 for Admin and 2 for Clinician and 3 for Student
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

		// Instantiate the Logger class
		$objLog = new clsLogger();
		$action = $objLog::DELETE;
		if ($isUser == 1) {
			$userType = $objLog::ADMIN;
		} elseif ($isUser == 2) {
			$userType = $objLog::CLINICIAN;
		}
		$objDaily->saveDailyEvalAuditLog($deleteId, $deleteId, $userId, $userType, $action);

		// $objDaily->DeleteDailyEval($deleteId);

		unset($objLog);
		unset($objDaily);
	} else if ($_GET['type'] == 'immunizationMaster') {
		include('../class/clsImmunizationMaster.php');
		$objimmunizationmaster = new clsImmunizationMaster();

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

		// $isUser 1 for Admin and 2 for Clinician and 3 for Students
		// Instantiate the Logger class
		$objLog = new clsLogger();
		// $type = $_GET['type']; // delete type for document
		$logAction = $objLog::DELETE;
		if ($isUser == 1) {
			$userType = $objLog::ADMIN;
		} else if ($isUser == 3) {
			$userType = $objLog::STUDENT;
		} else if ($isUser == 2) {
			$userType = $objLog::CLINICIAN;
		}

		$objimmunizationmaster->saveImmunizationAuditLog($deleteId, $userId, $userType, $logAction);

		unset($objimmunizationmaster);
	} else if ($_GET['type'] == 'studentSummative') {
		include('../class/clsSummative.php');
		$objsummative = new clsSummative();

		//Audit Log Start
		// Instantiate the Logger class
		$objLog = new clsLogger();

		$action = $objLog::DELETE;
		$userType = $objLog::ADMIN; // User type is set to ADMIN

		$objsummative->saveSummativeEvalAuditLog($deleteId, $deleteId, $_SESSION['loggedUserId'], $userType, $action);
		unset($objLog);


		// $objsummative->DeleteSummative($deleteId);
		unset($objsummative);
	} else if ($_GET['type'] == 'clinicalsite') {
		include('../class/clsschoolclinicalsiteunit.php');
		$objschoolclinicalsiteunit = new clsschoolclinicalsiteunit();
		// $objschoolclinicalsiteunit->DeleteClinicalSiteUnit($deleteId);

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

		// $isUser 1 for Admin and 2 for Clinician and 3 for Student
		// Instantiate the Logger class
		$objLog = new clsLogger();
		// $type = $_GET['type']; // delete type for document
		$logAction = $objLog::DELETE;
		if ($isUser == 1) {
			$userType = $objLog::ADMIN;
		} else if ($isUser == 3) {
			$userType = $objLog::STUDENT;
		} else if ($isUser == 2) {
			$userType = $objLog::CLINICIAN;
		}

		$objschoolclinicalsiteunit->saveClinicalSiteUnitAuditLog($deleteId, $userId, $userType, $logAction);


		unset($objschoolclinicalsiteunit);
	} else if ($_GET['type'] == 'CIEvaluation') {
		include('../class/clsCIevaluation.php');
		$objCIevaluation = new clsCIevaluation();

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

		// $isUser 1 for Admin and 2 for Clinician and 3 for Student
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$logAction = $objLog::DELETE;
		if ($isUser == 1) {
			$userType = $objLog::ADMIN;
		} else if ($isUser == 3) {
			$userType = $objLog::STUDENT;
		} else if ($isUser == 2) {
			$userType = $objLog::CLINICIAN;
		}

		$objCIevaluation->saveCIEvaluationAuditLog($deleteId, $userId, $userType, $logAction);
		unset($objCIevaluation);
	} else if ($_GET['type'] == 'PEvaluation') {
		include('../class/clsPEvaluation.php');
		$objPevaluation = new clsPEvaluation();

		//Audit Log Start
		// Instantiate the Logger class
		$objLog = new clsLogger();

		// Determine the action type (EDIT or ADD) based on the presence of a journal ID
		$action = $objLog::DELETE;
		$userType = $objLog::ADMIN; // User type is set to ADMIN

		$objPevaluation->savePEvalAuditLog($deleteId, $deleteId, $_SESSION['loggedUserId'], $userType, $action);
		unset($objLog);

		// $objPevaluation->DeletePEvaluation($deleteId);
		unset($objPevaluation);
	} else if ($_GET['type'] == 'CSEvaluation') {
		include('../class/clsSiteevaluation.php');
		$objSiteevaluation = new clsSiteevaluation();

		//Audit Log Start
		// Instantiate the Logger class
		$objLog = new clsLogger();

		// Determine the action type (EDIT or ADD) 
		$action = $objLog::DELETE;
		$userType = $objLog::ADMIN; // User type is set to ADMIN

		$objSiteevaluation->saveSiteEvalAuditLog($deleteId, $deleteId, $_SESSION['loggedUserId'], $userType, $action);
		unset($objLog);

		// $objSiteevaluation->DeleteCSEvaluation($deleteId);
		unset($objSiteevaluation);
	} else if ($_GET['type'] == 'announcement') {

		include('../class/clsAnnouncement.php');

		$objAnnouncement = new clsAnnouncement();
		$objAnnouncement->DeleteAnnouncement($deleteId);
		unset($objAnnouncement);
	} else if ($_GET['type'] == 'CheckOff') {
		include('../class/clscheckoff.php');
		$objcheckoff = new clscheckoff();

		// Instantiate the Logger class
		$objLog = new clsLogger();

		// Determine the action type (EDIT or ADD) based on the presence of a journal ID
		$action = $objLog::DELETE;
		$userType = $objLog::ADMIN; // User type is set to Admin

		$objcheckoff->saveCheckoffAuditLog($deleteId, $deleteId, $_SESSION['loggedUserId'], $userType, $action);
		unset($objcheckoff);

		unset($objLog);
	} else if ($_GET['type'] == 'CIEvaluation_Section') {
		include('../class/clsCIevaluation.php');
		$objCIevaluation = new clsCIevaluation();
		// $objCIevaluation->DeleteCiEvaluationSection($deleteId);

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

		// $isUser 1 for Admin and 2 for Clinician and 3 for Student
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$type = "section";
		$logAction = $objLog::DELETE;
		if ($isUser == 1) {
			$userType = $objLog::ADMIN;
		} else if ($isUser == 3) {
			$userType = $objLog::STUDENT;
		} else if ($isUser == 2) {
			$userType = $objLog::CLINICIAN;
		}

		$objCIevaluation->saveCIEvaluationAuditLog($deleteId, $userId, $userType, $logAction, 0, $type);


		unset($objCIevaluation);
	} else if ($_GET['type'] == 'CIEvaluation_Question') {
		include('../class/clsCIevaluation.php');
		$objCIevaluation = new clsCIevaluation();
		// $objCIevaluation->DeleteCiEvaluationQuestion($deleteId);



		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

		// $isUser 1 for Admin and 2 for Clinician and 3 for Student
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$type = "step";
		$logAction = $objLog::DELETE;
		$isSuperAdmin = isset($_GET['isCurrentSchoolSuperAdmin']) ? ($_GET['isCurrentSchoolSuperAdmin']) : 0;
		$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN; // User type is set to Admins

		$objCIevaluation->saveCIEvaluationAuditLog($deleteId, $userId, $userType, $logAction, 0, $type, $isSuperAdmin);
		unset($objCIevaluation);
	} else if ($_GET['type'] == 'SummativeEvaluation_Question') {
		include('../class/clsSummative.php');
		$objSummative = new clsSummative();
		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$type = "step";
		$logAction = $objLog::DELETE;
		$isSuperAdmin = isset($_GET['isCurrentSchoolSuperAdmin']) ? ($_GET['isCurrentSchoolSuperAdmin']) : 0;
		$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN;
		$objSummative->saveSummativeEvalAuditLog($deleteId, $deleteId, $userId, $userType, $logAction, $IsMobile = 0, $type, $isSuperAdmin);
		unset($objSummative);
		unset($objLog);
	} else if ($_GET['type'] == 'SummativeEvaluation_Section') {
		include('../class/clsSummative.php');
		$objSummative = new clsSummative();
		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$type = "section";
		$logAction = $objLog::DELETE;
		$isSuperAdmin = isset($_GET['isCurrentSchoolSuperAdmin']) ? ($_GET['isCurrentSchoolSuperAdmin']) : 0;
		$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN;
		$objSummative->saveSummativeEvalAuditLog($deleteId, $deleteId, $userId, $userType, $logAction, $IsMobile = 0, $type, $isSuperAdmin);
		unset($objSummative);
		unset($objLog);
	} else if ($_GET['type'] == 'SiteEvaluation_Section') {
		include('../class/clsSiteevaluation.php');
		$objSiteevaluation = new clsSiteevaluation();

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

		// $isUser 1 for Admin and 2 for Clinician and 3 for Student
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$type = "section";
		$logAction = $objLog::DELETE;
		$isSuperAdmin = isset($_GET['isCurrentSchoolSuperAdmin']) ? ($_GET['isCurrentSchoolSuperAdmin']) : 0;
		$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN;

		$objSiteevaluation->saveSiteEvalAuditLog($deleteId, $deleteId, $userId, $userType, $logAction, 0, $type, $isSuperAdmin);

		unset($objSiteevaluation);
	} else if ($_GET['type'] == 'SiteEvaluation_Question') {
		include('../class/clsSiteevaluation.php');
		$objSiteevaluation = new clsSiteevaluation();

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

		// $isUser 1 for Admin and 2 for Clinician and 3 for Student
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$type = "step";
		$logAction = $objLog::DELETE;
		$isSuperAdmin = isset($_GET['isCurrentSchoolSuperAdmin']) ? ($_GET['isCurrentSchoolSuperAdmin']) : 0;
		$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN;

		$objSiteevaluation->saveSiteEvalAuditLog($deleteId, $deleteId, $userId, $userType, $logAction, 0, $type, $isSuperAdmin);

		unset($objSiteevaluation);
	} else if ($_GET['type'] == 'FormativeEvaluation_Section') {
		include('../class/clsFormative.php');
		$objFormative = new clsFormative();

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

		// $isUser 1 for Admin and 2 for Clinician and 3 for Student
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$type = "section";
		$logAction = $objLog::DELETE;
		$isSuperAdmin = isset($_GET['isCurrentSchoolSuperAdmin']) ? ($_GET['isCurrentSchoolSuperAdmin']) : 0;
		$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN;

		$objFormative->saveFormativeAuditLog($deleteId, $deleteId, $userId, $userType, $logAction, 0, $type, $isSuperAdmin);


		unset($objFormative);
	} else if ($_GET['type'] == 'Midterm_Section') {
		include('../class/clsMidterm.php');
		$objMidterm = new clsMidterm();

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$type = "section";
		$logAction = $objLog::DELETE;
		$isSuperAdmin = isset($_GET['isCurrentSchoolSuperAdmin']) ? ($_GET['isCurrentSchoolSuperAdmin']) : 0;
		$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN;
		$objMidterm->saveMidtermEvalAuditLog($deleteId, $deleteId, $userId, $userType, $logAction, $IsMobile = 0, $type, $isSuperAdmin);
		unset($objMidterm);
		unset($objLog);
	} else if ($_GET['type'] == 'FormativeEvaluation_Question') {
		include('../class/clsFormative.php');
		$objFormative = new clsFormative();

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

		// $isUser 1 for Admin and 2 for Clinician and 3 for Student
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$type = "step";
		$logAction = $objLog::DELETE;
		$isSuperAdmin = isset($_GET['isCurrentSchoolSuperAdmin']) ? ($_GET['isCurrentSchoolSuperAdmin']) : 0;
		$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN;

		$objFormative->saveFormativeAuditLog($deleteId, $deleteId, $userId, $userType, $logAction, 0, $type, $isSuperAdmin);
		unset($objFormative);
	} else if ($_GET['type'] == 'MidtermEvaluation_Question') {
		include('../class/clsMidterm.php');
		$objMidterm = new clsMidterm();

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$type = "step";
		$logAction = $objLog::DELETE;
		$isSuperAdmin = isset($_GET['isCurrentSchoolSuperAdmin']) ? ($_GET['isCurrentSchoolSuperAdmin']) : 0;
		$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN;
		$objMidterm->saveMidtermEvalAuditLog($deleteId, $deleteId, $userId, $userType, $logAction, $IsMobile = 0, $type, $isSuperAdmin);
		unset($objMidterm);
		unset($objLog);
	} else if ($_GET['type'] == 'DailyEvaluation_Section') {
		include('../class/clsDaily.php');
		$objDaily = new clsDaily();

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$type = "section";
		$logAction = $objLog::DELETE;
		$isSuperAdmin = 1;
		$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN;
		$objDaily->saveDailyEvalAuditLog($deleteId, $deleteId, $userId, $userType, $logAction, $IsMobile = 0, $type, $isSuperAdmin);
		unset($objDaily);
		unset($objLog);
	} else if ($_GET['type'] == 'DailySchoolEvaluation_Section') {
		include('../class/clsDaily.php');
		$objDaily = new clsDaily();

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$type = "section";
		$logAction = $objLog::DELETE;
		$isSuperAdmin = 0;
		$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN;
		$objDaily->saveDailyEvalAuditLog($deleteId, $deleteId, $userId, $userType, $logAction, $IsMobile = 0, $type, $isSuperAdmin);
		unset($objDaily);
		unset($objLog);
	} else if ($_GET['type'] == 'DailyEvaluation_Question') {
		include('../class/clsDaily.php');
		$objDaily = new clsDaily();

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$type = "step";
		$logAction = $objLog::DELETE;
		$isSuperAdmin = 1;
		$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN;
		$objDaily->saveDailyEvalAuditLog($deleteId, $deleteId, $userId, $userType, $logAction, $IsMobile = 0, $type, $isSuperAdmin);
		unset($objDaily);
		unset($objLog);
	} else if ($_GET['type'] == 'DailySchoolEvaluation_Question') {
		include('../class/clsDaily.php');
		$objDaily = new clsDaily();

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$type = "step";
		$logAction = $objLog::DELETE;
		$isSuperAdmin = 0;
		$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN;
		$objDaily->saveDailyEvalAuditLog($deleteId, $deleteId, $userId, $userType, $logAction, $IsMobile = 0, $type, $isSuperAdmin);
		unset($objDaily);
		unset($objLog);
	} else if ($_GET['type'] == 'CaseStudyEvaluation_Section') {
		include('../class/clsCaseStudy.php');
		$objCaseStudy = new clsCaseStudy();
		$objCaseStudy->DeleteCaseStudyEvaluationSection($deleteId);
		unset($objCaseStudy);
	} else if ($_GET['type'] == 'studentEquipment') {
		include('../class/clsEquipment.php');
		$objEquipment = new clsEquipment();

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

		// $isUser 1 for Admin and 2 for Clinician and 3 for Student
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$logAction = $objLog::DELETE;
		if ($isUser == 1) {
			$userType = $objLog::ADMIN;
		} else if ($isUser == 3) {
			$userType = $objLog::STUDENT;
		} else if ($isUser == 2) {
			$userType = $objLog::CLINICIAN;
		}

		$objEquipment->saveEquipmentAuditLog($deleteId, $userId, $userType, $logAction);
		unset($objEquipment);
	} else if ($_GET['type'] == 'coarc_request_student') {
		$studentId = isset($_GET['studentId']) ? DecodeQueryData($_GET['studentId']) : 0;
		$coarcSurveyMasterId = isset($_GET['coarcSurveyMasterId']) ? DecodeQueryData($_GET['coarcSurveyMasterId']) : 0;
		include('../class/clsStudentCoarcMaster.php');
		$objStudentCoarc = new clsStudentCoarcMaster();
		//Audit Log Start
		// Instantiate the Logger class
		$objLog = new clsLogger();

		$action = $objLog::DELETE;
		$userType = $objLog::ADMIN; // User type is set to ADMIN

		$objStudentCoarc->saveStudentCoarcSurveyAuditLog($coarcSurveyMasterId, $studentId, $_SESSION['loggedUserId'], $userType, $action);
		unset($objLog);
		unset($objStudentCoarc);
	} else if ($_GET['type'] == 'graduate_coarc_request_student') {
		$studentId = isset($_GET['studentId']) ? DecodeQueryData($_GET['studentId']) : 0;
		$coarcSurveyMasterId = isset($_GET['coarcSurveyMasterId']) ? DecodeQueryData($_GET['coarcSurveyMasterId']) : 0;
		include('../class/clsGraduateCoarcRequestMaster.php');
		$objGraduateStudentCoarc = new clsGraduateCoarcRequestMaster();
		//Audit Log Start
		// Instantiate the Logger class
		$objLog = new clsLogger();

		$action = $objLog::DELETE;
		$userType = $objLog::ADMIN; // User type is set to ADMIN

		$objGraduateStudentCoarc->saveGraduateCoarcSurveyAuditLog($coarcSurveyMasterId, $studentId, $_SESSION['loggedUserId'], $userType, $action);
		unset($objLog);
		unset($objGraduateStudentCoarc);
	} else if ($_GET['type'] == 'personnel_coarc_request_student') {
		$clinicianId = isset($_GET['clinicianId']) ? DecodeQueryData($_GET['clinicianId']) : 0;
		$coarcSurveyMasterId = isset($_GET['coarcSurveyMasterId']) ? DecodeQueryData($_GET['coarcSurveyMasterId']) : 0;
		include('../class/clsPersonnelCoarcRequestMaster.php');
		$objPersonnelCoarc = new clsPersonnelCoarcRequestMaster();
		//Audit Log Start
		// Instantiate the Logger class
		$objLog = new clsLogger();

		$action = $objLog::DELETE;
		$userType = $objLog::ADMIN; // User type is set to ADMIN

		$objPersonnelCoarc->savePersonnelCoarcSurveyAuditLog($coarcSurveyMasterId, $clinicianId, $_SESSION['loggedUserId'], $userType, $action);
		unset($objLog);
		unset($objPersonnelCoarc);
	} else if ($_GET['type'] == 'employer_coarc_request_student') {
		$studentId = isset($_GET['studentId']) ? $_GET['studentId'] : 0;
		$coarcSurveyMasterId = isset($_GET['coarcSurveyMasterId']) ? $_GET['coarcSurveyMasterId'] : 0;
		include('../class/clsEmployerCoarcRequestMaster.php');
		$objEmployerCoarc = new clsEmployerCoarcRequestMaster();
		//Audit Log Start
		// Instantiate the Logger class
		$objLog = new clsLogger();

		$action = $objLog::DELETE;
		$userType = $objLog::ADMIN; // User type is set to ADMIN

		$objEmployerCoarc->saveEmployerCoarcSurveyAuditLog($coarcSurveyMasterId, $studentId, $_SESSION['loggedUserId'], $userType, $action);
		unset($objLog);
		unset($objEmployerCoarc);
	} else if ($_GET['type'] == 'Irr_Assignment') {
		include('../class/clsIrr.php');
		$objIrr = new clsIrr();

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$logAction = $objLog::DELETE;
		$userType = $objLog::ADMIN;
		$objIrr->saveIRRAuditLog($deleteId, $userId, $userType, $logAction);
		unset($objIrr);
		unset($objLog);
	} else if ($_GET['type'] == 'Master_PEF_Checkoff_Topic') {
		include('../class/clsMasterCheckoffTopic.php');
		$objCheckoffTopic = new clsMasterCheckoffTopic();
		$objCheckoffTopic->DeleteCheckoffTopic($deleteId);
		unset($objCheckoffTopic);
	} else if ($_GET['type'] == 'Usaf_Master_PEF_Checkoff_Topic') {
		include('../class/clsUsafMasterCheckoffTopic.php');
		$objCheckoffTopic = new clsUsafMasterCheckoffTopic();
		$objCheckoffTopic->DeleteUsafCheckoffTopic($deleteId);
		unset($objCheckoffTopic);
	} else if ($_GET['type'] == 'Advance_Master_PEF_Checkoff_Topic') {
		include('../class/clsAdvanceMasterCheckoffTopic.php');
		$objAdvanceCheckoffTopic = new clsAdvanceMasterCheckoffTopic();
		$objAdvanceCheckoffTopic->DeleteCheckoffTopic($deleteId);
		unset($objAdvanceCheckoffTopic);
	} else if ($_GET['type'] == 'PEF_Checkoff_Topic') {
		include('../class/clsCheckoffTopicMaster.php');
		$objCheckoffTopicMaster = new clsCheckoffTopicMaster();

		//Audit Log Start
		// Instantiate the Logger class
		$objLog = new clsLogger();

		// Determine the action type (EDIT or ADD) based on the presence of a ID
		$action = $objLog::DELETE;
		$userType = $objLog::ADMIN; // User type is set to ADMIN

		$objCheckoffTopicMaster->saveCheckoffTopicAuditLog($deleteId, $_SESSION['loggedUserId'], $userType, $action);
		unset($objLog);

		// $objCheckoffTopicMaster->DeleteCheckoffTopic($deleteId);
		unset($objCheckoffTopicMaster);
	} else if ($_GET['type'] == 'Master_PEF_Checkoff_Section') {
		include('../class/clsMasterCheckoffSection.php');
		$objCheckoffSection = new clsMasterCheckoffSection();
		$objCheckoffSection->DeleteMasterCheckoffSection($deleteId);
		unset($objCheckoffSection);
	} else if ($_GET['type'] == 'Advance_Master_PEF_Checkoff_Section') {
		include('../class/clsAdvanceMasterCheckoffSection.php');
		$objAdvanceCheckoffSection = new clsAdvanceMasterCheckoffSection();
		$objAdvanceCheckoffSection->DeleteMasterCheckoffSection($deleteId);
		unset($objAdvanceCheckoffSection);
	} else if ($_GET['type'] == 'Usaf_Master_PEF_Checkoff_Section') {
		include('../class/clsUsafMasterCheckoffSection.php');
		$objAdvanceCheckoffSection = new clsUsafMasterCheckoffSection();
		$objAdvanceCheckoffSection->DeleteUsafMasterCheckoffSection($deleteId);
		unset($objAdvanceCheckoffSection);
	} else if ($_GET['type'] == 'PEF_Checkoff_Section') {
		include('../class/clsCheckoffSectionMaster.php');
		$objCheckoffSectionMaster = new clsCheckoffSectionMaster();

		//Audit Log Start
		// Instantiate the Logger class
		$objLog = new clsLogger();

		// Determine the action type (DELETE) based on the presence of a ID
		$action = $objLog::DELETE;
		$userType = $objLog::ADMIN; // User type is set to ADMIN

		$objCheckoffSectionMaster->saveCheckoffSectionAuditLog($deleteId, $_SESSION['loggedUserId'], $userType, $action);
		unset($objLog);


		// $objCheckoffSectionMaster->DeleteCheckoffSection($deleteId);
		unset($objCheckoffSectionMaster);
	} else if ($_GET['type'] == 'PEF_Question') {
		$objDB = new clsDB();
		$isPresentQuestionId = $objDB->GetSingleColumnValueFromTable('schooltopicdetails', 'schoolQuestionId', 'schoolQuestionId', $deleteId);
		unset($objDB);
		// $isPresentQuestionId=0;
		if ($isPresentQuestionId) {
			echo 'alreadyAssigned';
			exit;
		} else {
			include('../class/clsCheckoffQuestionMaster.php');
			$objCheckoffQuestionMaster = new clsCheckoffQuestionMaster();

			$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
			$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

			// $isUser 1 for Admin and 2 for Clinician and 3 for Student
			// Instantiate the Logger class
			$objLog = new clsLogger();
			// $type = $_GET['type']; // delete type for document
			$logAction = $objLog::DELETE;
			if ($isUser == 1) {
				$userType = $objLog::ADMIN;
			} else if ($isUser == 3) {
				$userType = $objLog::STUDENT;
			} else if ($isUser == 2) {
				$userType = $objLog::CLINICIAN;
			}

			$objCheckoffQuestionMaster->savePEFQuestionAuditLog($deleteId, $userId, $userType, $logAction);

			// $objCheckoffQuestionMaster->DeleteMasterCheckoffQuestion($deleteId);
			unset($objCheckoffQuestionMaster);
		}
	} else if ($_GET['type'] == 'Master_PEF_Question') {
		include('../class/clsMasterCheckoffQuestion.php');
		$objCheckoffQuestion = new clsMasterCheckoffQuestion();
		$objCheckoffQuestion->DeleteMasterCheckoffQuestions($deleteId);
		unset($objCheckoffQuestion);
	} else if ($_GET['type'] == 'AdvanceMasterPEFQuestion') {
		include('../class/clsAdvanceMasterCheckoffQuestion.php');
		$objCheckoffQuestion = new clsAdvanceMasterCheckoffQuestion();
		$objCheckoffQuestion->DeleteNewQuestionsMaster($deleteId);
		unset($objCheckoffQuestion);
	} else if ($_GET['type'] == 'UsafMasterPEFQuestion') {
		include('../class/clsUsafMasterCheckoffQuestion.php');
		$objCheckoffQuestion = new clsUsafMasterCheckoffQuestion();
		$objCheckoffQuestion->DeleteUsafQuestionsMaster($deleteId);
		unset($objCheckoffQuestion);
	} else if ($_GET['type'] == 'EquipmentMaster') {
		include('../class/clsusafEquiment.php');
		$objequipment = new clsusafEquiment();

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$type = "";
		$logAction = $objLog::DELETE;
		$isSuperAdmin = 1;
		$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN;
		$objequipment->saveEquipmentAuditLog($deleteId, $userId, $userType, $logAction, $IsMobile = 0, $type, $isSuperAdmin);
		unset($objequipment);
		unset($objLog);
	} else if ($_GET['type'] == 'EquipmentSchool') {
		include('../class/clsusafEquiment.php');
		$objequipment = new clsusafEquiment();

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$type = "";
		$logAction = $objLog::DELETE;
		$isSuperAdmin = 0;
		$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN;
		$objequipment->saveEquipmentAuditLog($deleteId, $userId, $userType, $logAction, $IsMobile = 0, $type, $isSuperAdmin);
		unset($objequipment);
		unset($objLog);
	} else if ($_GET['type'] == 'Procedure_Category') {

		include('../class/clsProcedureCategory.php');

		$objProcedureCategory = new clsProcedureCategory();
		$objProcedureCategory->DeleteProcedurecategory($deleteId);
		unset($objProcedureCategory);
	} else if ($_GET['type'] == 'UsafProcedureCategory') {

		include('../class/clsUsafProcedureCategory.php');

		$objProcedureCategory = new clsUsafProcedureCategory();
		$objProcedureCategory->DeleteUsafProcedurecategory($deleteId);
		unset($objProcedureCategory);
	} else if ($_GET['type'] == 'AdvanceProcedure_Category') {

		include('../class/clsProcedureCategory.php');

		$objProcedureCategory = new clsProcedureCategory();
		$objProcedureCategory->DeleteAdvacneProcedurecategory($deleteId);
		unset($objProcedureCategory);
	} else if ($_GET['type'] == 'ProcedureCount') {

		include('../class/clsProcedureCategory.php');

		$objProcedureCategory = new clsProcedureCategory();
		$objProcedureCategory->DeleteProcedureCount($deleteId);
		unset($objProcedureCategory);
	} else if ($_GET['type'] == 'AdvanceProcedureCount') {
		include('../class/clsProcedureCategory.php');
		$objProcedureCategory = new clsProcedureCategory();
		$objProcedureCategory->DeleteAdvanceProcedureCount($deleteId);
		unset($objProcedureCategory);
	} else if ($_GET['type'] == 'UsafProcedureCount') {
		include('../class/clsUsafProcedureCategory.php');
		$objProcedureCategory = new clsUsafProcedureCategory();
		$objProcedureCategory->DeleteUsafProcedureCount($deleteId);
		unset($objProcedureCategory);
	} else if ($_GET['type'] == 'semester') {

		include('../class/clsSemester.php');

		$objSemester = new clsSemester();
		// $objSemester->DeleteSemester($deleteId);

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

		// $isUser 1 for Admin and 2 for Clinician and 3 for Student
		// Instantiate the Logger class
		$objLog = new clsLogger();
		// $type = $_GET['type']; // delete type for document
		$logAction = $objLog::DELETE;
		if ($isUser == 1) {
			$userType = $objLog::ADMIN;
		} else if ($isUser == 3) {
			$userType = $objLog::STUDENT;
		} else if ($isUser == 2) {
			$userType = $objLog::CLINICIAN;
		}

		$objSemester->saveSemesterAuditLog($deleteId, $userId, $userType, $logAction);


		unset($objSemester);
	} else if ($_GET['type'] == 'Medical_Terminology') {
		include('../class/clsMedicalTerminology.php');
		$objMedicalTerminology = new clsMedicalTerminology();
		$objMedicalTerminology->DeleteMedicalTerminology($deleteId);
		unset($objMedicalTerminology);
	} else if ($_GET['type'] == 'Medical_Terminology_files') {
		include('../class/clsMedicalTerminology.php');
		$objMedicalTerminology = new clsMedicalTerminology();
		$objMedicalTerminology->DeleteMedicalTerminologyFiles($deleteId);
		unset($objMedicalTerminology);
	} else if ($_GET['type'] == 'Briefcase') {
		$fileName = $_GET['filePath'];
		$schoolId = $_GET['schoolId'];
		include('../class/clsBriefcase.php');
		$objBriefcase = new clsBriefcase();
		// $objBriefcase->DeleteBriefcase($deleteId);
		$pdfPath = DeleteBriefcaseDocument($fileName, $schoolId);

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

		// $isUser 1 for Admin and 2 for Clinician and 3 for Student
		// Instantiate the Logger class
		$objLog = new clsLogger();
		// $type = $_GET['type']; // delete type for document
		$logAction = $objLog::DELETE;
		if ($isUser == 1) {
			$userType = $objLog::ADMIN;
		} else if ($isUser == 3) {
			$userType = $objLog::STUDENT;
		} else if ($isUser == 2) {
			$userType = $objLog::CLINICIAN;
		}

		$objBriefcase->saveBriefcaseAuditLog($deleteId, $userId, $userType, $logAction);
		unset($objBriefcase);
	} else if ($_GET['type'] == 'Mastery') {
		include('../class/clsMasteryEval.php');

		$objMasteryEval = new clsMasteryEval();
		// $objMasteryEval->DeleteMasteryEval($deleteId);

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

		// $isUser 1 for Admin and 2 for Clinician and 3 for Student
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$logAction = $objLog::DELETE;
		if ($isUser == 1) {
			$userType = $objLog::ADMIN;
		} else if ($isUser == 3) {
			$userType = $objLog::STUDENT;
		} else if ($isUser == 2) {
			$userType = $objLog::CLINICIAN;
		}
		$objMasteryEval->saveMasteryAuditLog($deleteId, $userId, $userType, $logAction);
		unset($objLog);
		unset($objMasteryEval);
	} else if ($_GET['type'] == 'attendence') {

		include('../class/clsAttendance.php');
		$objAttendance = new clsAttendance();
		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isAdmin = isset($_GET['isAdmin']) ? ($_GET['isAdmin']) : 0;

		// Instantiate the Logger class
		$objLog = new clsLogger();
		$logAction = $objLog::DELETE;
		if ($isAdmin == 1) {
			$userType = $objLog::ADMIN;
		} else {
			$userType = $objLog::STUDENT;
		}

		[$logData, $rowData, $additionalData] = $objAttendance->createAttendanceLog($deleteId, $logAction, $userId, $userType);

		$objAttendance->DeleteAttendance($deleteId);

		$objLog->saveLogs($logData, $logAction, $deleteId, 'Attendance', $rowData, $additionalData);

		unset($objLog);
		unset($objAttendance);
	} else if ($_GET['type'] == 'clinicianattendence') {
		include('../class/clsClinicianAttendance.php');
		$objAttendance = new clsClinicianAttendance();
		$objAttendance->DeleteClinicianAttendance($deleteId);
		unset($objAttendance);
	} else if ($_GET['type'] == 'pef1Evalution') {
		include('../class/clsPEF.php');
		$objPEF = new clsPEF();
		$objLog = new clsLogger();

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

		$action = $objLog::DELETE;
		if ($isUser == 1) {
			$userType = $objLog::ADMIN;
		} else if ($isUser == 2) {
			$userType = $objLog::CLINICIAN;
		}

		$objPEF->savePEFEvalAuditLog($deleteId, $deleteId, $userId, $userType, $action);

		// $objPEF->DeleteStudentPEF($deleteId);
		unset($objPEF);
	} else if ($_GET['type'] == 'PefEvaluationSection') {
		include('../class/clsPEF.php');
		$objPEF = new clsPEF();
		$objLog = new clsLogger();
		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$type = "section";
		$action = $objLog::DELETE;
		$userType = $objLog::SUPERADMIN;
		$objPEF->savePEFEvalAuditLog($deleteId, $deleteId, $userId, $userType, $action, 0, $type);

		// $objPEF->DeletePefEvaluationSection($deleteId);
		unset($objPEF);
	} else if ($_GET['type'] == 'PefEvaluationStep') {
		include('../class/clsPEF.php');
		$objPEF = new clsPEF();
		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$objLog = new clsLogger();
		$type = "step";
		$action = $objLog::DELETE;
		$userType = $objLog::SUPERADMIN;
		$objPEF->savePEFEvalAuditLog($deleteId, $deleteId, $userId, $userType, $action, 0, $type);
		// $objPEF->DeletePefQuestion($deleteId);
		unset($objPEF);
	} else if ($_GET['type'] == 'FloorIcuEvaluationSection') {
		include('../class/clsFloorTherapyAndICUEvaluation.php');
		$objFloorTherapy = new clsFloorTherapyAndICUEvaluation();
		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$objLog = new clsLogger();
		$type = "section";
		$action = $objLog::DELETE;
		$userType = $objLog::SUPERADMIN;
		$objFloorTherapy->saveFloorAndIcuEvaluationAuditLog($deleteId, $userId, $userType, $action, $type, 0);

		// $objFloorTherapy->DeleteFloorIcuEvaluationSection($deleteId);
		unset($objFloorTherapy);
	} else if ($_GET['type'] == 'FloorIcuEvaluationStep') {
		include('../class/clsFloorTherapyAndICUEvaluation.php');
		$objFloorTherapy = new clsFloorTherapyAndICUEvaluation();
		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$objLog = new clsLogger();
		$type = "step";
		$action = $objLog::DELETE;
		$userType = $objLog::SUPERADMIN;
		$objFloorTherapy->saveFloorAndIcuEvaluationAuditLog($deleteId, $userId, $userType, $action, $type, 0);

		// $objFloorTherapy->DeleteFloorIcuQuestion($deleteId);
		unset($objFloorTherapy);
	} else if ($_GET['type'] == 'preceptorIrr') {
		include('../class/clsIrr.php');
		$objIrr = new clsIrr();
		$objIrr->DeletePreceptorIrr($deleteId);
		unset($objIrr);
	} else if ($_GET['type'] == 'adminCIEvaluation') {
		include('../class/clsAdminCIevaluation.php');
		$objCIevaluation = new clsAdminCIevaluation();

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

		// $isUser 1 for Admin and 2 for Clinician and 3 for Student
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$logAction = $objLog::DELETE;
		if ($isUser == 1) {
			$userType = $objLog::ADMIN;
		} else if ($isUser == 2) {
			$userType = $objLog::CLINICIAN;
		}
		$objCIevaluation->saveDCECIEvaluationAuditLog($deleteId, $userId, $userType, $logAction);
		unset($objLog);
		unset($objCIevaluation);
	} else if ($_GET['type'] == 'DCECIEvaluation_Section') {
		include('../class/clsAdminCIevaluation.php');
		$objCIevaluation = new clsAdminCIevaluation();
		$schoolId = isset($_GET['schoolId']) ? $_GET['schoolId'] : 0;

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$type = "section";
		$logAction = $objLog::DELETE;
		$isSuperAdmin = isset($_GET['isCurrentSchoolSuperAdmin']) ? ($_GET['isCurrentSchoolSuperAdmin']) : 0;
		$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN;
		$objCIevaluation->saveDCECIEvaluationAuditLog($deleteId, $userId, $userType, $logAction, $IsMobile = 0, $type, $isSuperAdmin);
		unset($objCIevaluation);
		unset($objLog);
	} else if ($_GET['type'] == 'DCECIEvaluation_Question') {
		include('../class/clsAdminCIevaluation.php');
		$objCIevaluation = new clsAdminCIevaluation();

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$type = "step";
		$logAction = $objLog::DELETE;
		$isSuperAdmin = isset($_GET['isCurrentSchoolSuperAdmin']) ? ($_GET['isCurrentSchoolSuperAdmin']) : 0;
		$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN;
		$objCIevaluation->saveDCECIEvaluationAuditLog($deleteId, $userId, $userType, $logAction, $IsMobile = 0, $type, $isSuperAdmin);
		unset($objCIevaluation);
		unset($objLog);
	} else if ($_GET['type'] == 'AdminReport') {
		include('../class/clsReport.php');
		$objReport = new clsReport();

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$logAction = $objLog::DELETE;
		$userType = $objLog::ADMIN;
		$objReport->saveReportAuditLog($deleteId, $userId, $userType, $logAction, $IsMobile = 0);
		unset($objReport);
		unset($objLog);
	} else if ($_GET['type'] == 'ActivitySheet') {
		include('../class/clsActivitysheet.php');
		$objActivitySheet = new clsActivitysheet();

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$logAction = $objLog::DELETE;
		$userType = $objLog::ADMIN;
		$objActivitySheet->saveActivitySheetAuditLog($deleteId, $userId, $userType, $logAction, $IsMobile = 0);
		unset($objActivitySheet);
		unset($objLog);
	}else if ($_GET['type'] == 'soapnote') {
		include('../class/clsSoapNote.php');
		$objSoapNote = new clsSoapNote();

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$logAction = $objLog::DELETE;
		$userType = $objLog::ADMIN;
		$objSoapNote->saveSoapNoteAuditLog($deleteId, $userId, $userType, $logAction, $IsMobile = 0);
		unset($objSoapNote);
		unset($objLog);
	} else if ($_GET['type'] == 'studentProcedureCount') {
		include('../class/clsProcedureCount.php');
		$objProcedureCount = new clsProcedureCount();
		// $objProcedureCount->DeleteStudentProcedureCount($deleteId);

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;
		$isActiveCheckoff = isset($_GET['isActiveCheckoff']) ? ($_GET['isActiveCheckoff']) : 0;
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$logAction = $objLog::DELETE;
		if ($isUser == 1) {
			$userType = $objLog::ADMIN;
		} else if ($isUser == 3) {
			$userType = $objLog::STUDENT;
		} else if ($isUser == 2) {
			$userType = $objLog::CLINICIAN;
		}
		[$logData, $rowData, $additionalData] = $objProcedureCount->createProcedureCountLog($deleteId, $logAction, $userId, $userType, $isActiveCheckoff);
		// Perform the delete operation on the journal
		$objProcedureCount->DeleteStudentProcedureCount($deleteId);

		// Save the delete operation details in logs
		$objLog->saveLogs($logData, $objLog::DELETE, $deleteId, 'Procedure Count', $rowData, $additionalData);

		unset($objLog);
		unset($objProcedureCount);
	} else if ($_GET['type'] == 'callOff') {
		include('../class/clsCallOff.php');
		$objCallOff = new clsCallOff();

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$logAction = $objLog::DELETE;
		$userType = $objLog::STUDENT;
		$objCallOff->saveCallOffAuditLog($deleteId, $logAction, $userId, $userType, $IsMobile = 0);
		unset($objCallOff);
		unset($objLog);
	} else if ($_GET['type'] == 'PEvaluation_Section') {
		include('../class/clsPEvaluation.php');
		$objPevaluation = new clsPEvaluation();

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

		// $isUser 1 for Admin and 2 for Clinician and 3 for Student
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$type = "section";
		$logAction = $objLog::DELETE;
		$isSuperAdmin = isset($_GET['isCurrentSchoolSuperAdmin']) ? ($_GET['isCurrentSchoolSuperAdmin']) : 0;
		$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN;

		$objPevaluation->savePEvalAuditLog($deleteId, $deleteId, $userId, $userType, $logAction, 0, $type, $isSuperAdmin);

		unset($objPevaluation);
	} else if ($_GET['type'] == 'PEvaluation_Question') {
		include('../class/clsPEvaluation.php');
		$objPevaluation = new clsPEvaluation();

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

		// $isUser 1 for Admin and 2 for Clinician and 3 for Student
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$type = "step";
		$logAction = $objLog::DELETE;
		$isSuperAdmin = isset($_GET['isCurrentSchoolSuperAdmin']) ? ($_GET['isCurrentSchoolSuperAdmin']) : 0;
		$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN;

		$objPevaluation->savePEvalAuditLog($deleteId, $deleteId, $userId, $userType, $logAction, 0, $type, $isSuperAdmin);

		unset($objPevaluation);
	} else if ($_GET['type'] == 'MasteryEvaluation_Section') {
		include('../class/clsMasteryEval.php');
		$objMasteryEval = new clsMasteryEval();

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

		// $isUser 1 for Admin and 2 for Clinician and 3 for Student
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$type = "section";
		$logAction = $objLog::DELETE;
		$isSuperAdmin = isset($_GET['isCurrentSchoolSuperAdmin']) ? ($_GET['isCurrentSchoolSuperAdmin']) : 0;
		$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN;
		

		$objMasteryEval->saveMasteryAuditLog( $deleteId, $userId, $userType, $logAction, 0, $type, $isSuperAdmin);

		unset($objMasteryEval);
	} else if ($_GET['type'] == 'MasteryEvaluation_Question') {
		include('../class/clsMasteryEval.php');
		$objMasteryEval = new clsMasteryEval();

		$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
		$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

		// $isUser 1 for Admin and 2 for Clinician and 3 for Student
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$type = "step";
		$logAction = $objLog::DELETE;
		$isSuperAdmin = isset($_GET['isCurrentSchoolSuperAdmin']) ? ($_GET['isCurrentSchoolSuperAdmin']) : 0;
		$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN;

		$objMasteryEval->saveMasteryAuditLog( $deleteId, $userId, $userType, $logAction, 0, $type, $isSuperAdmin);

		unset($objMasteryEval);
	}
}

exit;