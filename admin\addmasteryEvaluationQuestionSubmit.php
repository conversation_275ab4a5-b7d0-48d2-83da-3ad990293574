<?php 
	include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
    include('../class/clsSchool.php');      	
	include('../class/clsMasteryEval.php');	
	include('../setRequest.php'); 
	
	// echo "<pre>";
	// 	print_r($_POST);exit;
	if($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit']))
	{
		
		$questionId = isset($_GET['editid']) ? DecodeQueryData($_GET['editid']) : 0;

		$sectionMasterId = isset($_GET['sectionMasterId']) ? DecodeQueryData($_GET['sectionMasterId']) : 0;
		$status = ($questionId > 0) ? 'updated' : 'added';
		
		$txtsinglechoice  = $_POST['txtsinglechoice'];
		$title = $_POST['txtQuestion'];
		$questionType = $_POST['cbotype'];
		$optionSortOrder = $_POST['txtsinglechoicemarks'];
		// $isPosition = $_POST['isPosition'];
		$sortOrder = isset($_POST['sortOrder']) ? $_POST['sortOrder'] : NULL;
		
		//Save data
		$objMasteryEval = new clsMasteryEval();
		$objMasteryEval->optionText = $title;			
		$objMasteryEval->masteryQuestionType = $questionType;
		$objMasteryEval->sectionMasterId = $sectionMasterId;
		$objMasteryEval->sortOrder = $sortOrder;
		$objMasteryEval->schoolId = $currentSchoolId;
		if($isCurrentSchoolSuperAdmin == 1) // For superadmin			
			$retquestionId = $objMasteryEval->SaveDefaultMasteryevaluationQuestion($questionId);
		else
			$retquestionId = $objMasteryEval->SaveMasteryevaluationQuestion($questionId);
	
		//Delete Site evaluation Options
		if($questionId)
		{
			if($isCurrentSchoolSuperAdmin == 1) // For superadmin
				$deleteSiteevaluationOptions = $objMasteryEval->DeleteDefaultMasteryEvaluationQuestionDetail($questionId);
			else
				$deleteSiteevaluationOptions = $objMasteryEval->DeleteMasteryEvaluationQuestionDetail($questionId);
		}
			
		
		if($questionType == 1)
		{
			foreach($txtsinglechoice as $key=>$value)
			{
				$objMasteryEval->masteryQuestionId = $retquestionId;	
				$objMasteryEval->optionText = $value;		
				$objMasteryEval->description = $optionSortOrder[$key];

				if($isCurrentSchoolSuperAdmin == 1) // For superadmin
					$retQuestionOptionId = $objMasteryEval->SaveDefaultMasteryEvaluationQuestionOptions($questionId);
				else
					$retQuestionOptionId = $objMasteryEval->SaveMasteryEvaluationQuestionOptions($questionId);
					
			}
		}


		unset($objMasteryEval);
		if($retquestionId > 0)
		{
			//Audit Log Start
			// Instantiate the Logger class
			$objLog = new clsLogger();

			// Determine the action type (EDIT or ADD) based on the presence of a journal ID
			$action = ($questionId > 0) ? $objLog::EDIT : $objLog::ADD;
			$type = "step";
			$isSuperAdmin = ($isCurrentSchoolSuperAdmin == 1) ? 1 : 0;
			$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN; // User type is set to Admin

			$objMasteryEval = new clsMasteryEval();
			$objMasteryEval->saveMasteryAuditLog($retquestionId, $_SESSION["loggedUserId"], $userType, $action, $IsMobile = 0, $type,$isSuperAdmin);
			unset($objMidterm);

			unset($objLog);
			//Audit Log End

			header('location:masteryEvaluationQuestionList.html?status='.$status.'&sectionMasterId='.EncodeQueryData($sectionMasterId));
		}
		else
		{
			header('location:addmasteryEvaluationQuestions.html?sectionMasterId='.EncodeQueryData($sectionMasterId).'&status=error');
		}
	}
	else
	{
		header('location:masteryEvaluationQuestionList.html?sectionMasterId='.EncodeQueryData($sectionMasterId).'&status='.$status);
		//header('location:checkoffsection.html');
		exit();
	}

?>	