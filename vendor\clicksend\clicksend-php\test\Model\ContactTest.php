<?php
/**
 * ContactTest
 *
 * PHP version 5
 *
 * @category Class
 * @package  ClickSend
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * ClickSend v3 API
 *
 * This is an official SDK for [ClickSend](https://clicksend.com)  Below you will find a current list of the available methods for clicksend.  *NOTE: You will need to create a free account to use the API. You can register [here](https://dashboard.clicksend.com/#/signup/step1/)..*
 *
 * OpenAPI spec version: 3.1
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * ClickSend Codegen version: 3.0.51
 */
/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Please update the test case below to test the model.
 */

namespace ClickSend;

use PHPUnit\Framework\TestCase;

/**
 * ContactTest Class Doc Comment
 *
 * @category    Class
 * @description Contains all details for the main contact.
 * @package     ClickSend
 * <AUTHOR> Codegen team
 * @link        https://github.com/swagger-api/swagger-codegen
 */
class ContactTest extends TestCase
{

    /**
     * Setup before running any test case
     */
    public static function setUpBeforeClass(): void
    {
    }

    /**
     * Setup before running each test case
     */
    public function setUp(): void
    {
    }

    /**
     * Clean up after running each test case
     */
    public function tearDown(): void
    {
    }

    /**
     * Clean up after running all test cases
     */
    public static function tearDownAfterClass(): void
    {
    }

    /**
     * Test "Contact"
     */
    public function testContact()
    {
    }

    /**
     * Test attribute "phone_number"
     */
    public function testPropertyPhoneNumber()
    {
    }

    /**
     * Test attribute "custom_1"
     */
    public function testPropertyCustom1()
    {
    }

    /**
     * Test attribute "email"
     */
    public function testPropertyEmail()
    {
    }

    /**
     * Test attribute "fax_number"
     */
    public function testPropertyFaxNumber()
    {
    }

    /**
     * Test attribute "first_name"
     */
    public function testPropertyFirstName()
    {
    }

    /**
     * Test attribute "address_line_1"
     */
    public function testPropertyAddressLine1()
    {
    }

    /**
     * Test attribute "address_line_2"
     */
    public function testPropertyAddressLine2()
    {
    }

    /**
     * Test attribute "address_city"
     */
    public function testPropertyAddressCity()
    {
    }

    /**
     * Test attribute "address_state"
     */
    public function testPropertyAddressState()
    {
    }

    /**
     * Test attribute "address_postal_code"
     */
    public function testPropertyAddressPostalCode()
    {
    }

    /**
     * Test attribute "address_country"
     */
    public function testPropertyAddressCountry()
    {
    }

    /**
     * Test attribute "organization_name"
     */
    public function testPropertyOrganizationName()
    {
    }

    /**
     * Test attribute "custom_2"
     */
    public function testPropertyCustom2()
    {
    }

    /**
     * Test attribute "custom_3"
     */
    public function testPropertyCustom3()
    {
    }

    /**
     * Test attribute "custom_4"
     */
    public function testPropertyCustom4()
    {
    }

    /**
     * Test attribute "last_name"
     */
    public function testPropertyLastName()
    {
    }
}
