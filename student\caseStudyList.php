<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsStudent.php');
include('../class/clsCaseStudy.php');
include('../class/clsRotation.php');
include('../class/clsCourses.php');
include('../class/clsLocations.php');

$rotationId = 0;
$schoolId = 0;
$currentstudentId = 0;
$schoolId = $currentSchoolId;
$transchooldisplayName = '';
$rotationId = 0;
$currentstudentId = $_SESSION["loggedStudentId"];
$TimeZone = $_SESSION["loggedStudentSchoolTimeZone"];
$activebtn = '';
$clsBtnActiveFloor = '';
$clsBtnActivePACR = '';
$clsBtnActiveAdult = '';
$clsBtnActive = '';

//object
$objRotation = new clsRotation();

if (isset($_GET['active']))
    $activebtn = $_GET['active'];

if ($activebtn == 'floor')
    $clsBtnActiveFloor = "active";
elseif ($activebtn == 'adult')
    $clsBtnActiveAdult = "active";
elseif ($activebtn == 'PACR')
    $clsBtnActivePACR = "active";
else
    $clsBtnActive = "active";

$clinicianId = 0;
$courseId = 0;
//For Rotation Site
if (isset($_GET['rotationId'])) {
    $rotationId = $_GET['rotationId'];
    $rotationId = DecodeQueryData($rotationId);
} else {
    $schoolId = $currentSchoolId;
    $transchooldisplayName = $currenschoolDisplayname;
}


$title = "Case Study |" . $transchooldisplayName;

//For Case Study List
$objCaseStudy = new clsCaseStudy();

$objDB = new clsDB();
$casestudySettings = '';
$casestudySettings = $objDB->GetSingleColumnValueFromTable('schools', 'casestudySettings', 'schoolId', $schoolId);
unset($objDB);

if ($activebtn == 'floor')
    $getCaseStudydetails = $objCaseStudy->GetAllFloorCaseStudy($schoolId, $rotationId, $currentstudentId);
elseif ($activebtn == 'adult')
    $getCaseStudydetails = $objCaseStudy->GetAllAdultCaseStudy($schoolId, $rotationId, $currentstudentId);
elseif ($activebtn == 'PACR')
    $getCaseStudydetails = $objCaseStudy->GetAllPACRCaseStudy($schoolId, $rotationId, $currentstudentId);
else
    $getCaseStudydetails = $objCaseStudy->GetAllCaseStudy($schoolId, $rotationId, $currentstudentId, 0, $casestudySettings);

$totalCaseStudy = 0;
if ($getCaseStudydetails != '') {
    $totalCaseStudy = mysqli_num_rows($getCaseStudydetails);
}
unset($objCaseStudy);



//For Student Name
$objStudent = new clsStudent();
$Rowstudent = $objStudent->GetSingleStudent($schoolId, $currentstudentId);
$studentfullname = $Rowstudent['firstName'] . ' ' . $Rowstudent['lastName'];
unset($objStudent);

//For Rotation Name
$RotationName = $objRotation->GetrotationDetails($rotationId, $schoolId);
$rotationtitle = isset($RotationName['title']) ? $RotationName['title'] : '';


//Get Course List for dropdown
$objCourses = new clsCourses();
$courseList = $objCourses->GetAllCoursesByStudent($schoolId, $currentstudentId);
unset($objCourses);


?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($title); ?></title>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <?php include('includes/headercss.php'); ?>
    <?php include("includes/datatablecss.php") ?>

    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">

    <style>
        .two-line-ellipsis {
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
        }
    </style>
</head>

<body>
    <?php include('includes/header.php'); ?>
    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <?php
                    if ($rotationId != '') { ?>
                        <li><a href="rotations.html">Rotations</a></li>
                        <li class="active"><a href="rotations.html"><?php echo ($rotationtitle); ?></a></li>
                    <?php } ?>
                    <li class="active">Case Study</li>

                </ol>
            </div>
        </div>
    </div>

    <div class="container">

        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "Added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Case Study added successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Case Study updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Deleted") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Case Study deleted successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
        <?php
            }
        }
        ?>
        <div class="row margin_bottom_ten">

            <div class="btn-group pull-right" role="group" aria-label="First group">
                <a role="button" class="btn btn-primary <?php echo $clsBtnActive; ?>" href="caseStudyList.html?studentId=<?php echo EncodeQueryData($currentstudentId); ?>">All</a>
                <?php
                $casestudySettings = explode(",", $casestudySettings);
                if (in_array("1", $casestudySettings)) {
                ?>
                    <a role="button" class="btn btn-primary <?php echo $clsBtnActivePACR; ?>" href="caseStudyList.html?active=PACR&studentId=<?php echo EncodeQueryData($currentstudentId); ?>"> Patient Assessment/Chart Review </a>
                <?php }
                if (in_array("2", $casestudySettings)) {
                ?>
                    <a role="button" class="btn btn-primary <?php echo $clsBtnActiveFloor; ?>" href="caseStudyList.html?active=floor&studentId=<?php echo EncodeQueryData($currentstudentId); ?>"> Floor Therapy</a>
                <?php }
                if (in_array("3", $casestudySettings)) {
                ?>
                    <a role="button" class="btn btn-primary  <?php echo $clsBtnActiveAdult; ?>" href="caseStudyList.html?active=adult&studentId=<?php echo EncodeQueryData($currentstudentId); ?>">Adult ICU & NICU/PICU</a>
                <?php } ?>
            </div>
        </div>

        <div class="row margin_bottom_ten">
            <?php if ($activebtn == 'adult') { ?>
                <a class="showall btn btn-success pull-right margin_right_fifteen" href="addCaseStudyAdult.html"> Add </a>
            <?php } else if ($activebtn == 'floor') { ?>
                <a class="showall btn btn-success pull-right margin_right_fifteen" href="addCaseStudy.html"> Add </a>
            <?php } else if ($activebtn == 'PACR') { ?>
                <a class="showall btn btn-success pull-right margin_right_fifteen" href="addCaseStudyPACR.html"> Add </a>
            <?php } ?>
        </div>

        <div id="divTopLoading">Loading...</div>
        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Rotation</th>
                    <th>Case<br>Study</th>
                    <th>Chief Complaint/<br>Admitting Diagnosis</th>
                    <th style="text-align: center">Clinician<br>Signoff Date</th>
                    <th style="text-align: center">School<br>Signoff Date</th>
                    <th>Comments</th>
                    <th>Status</th>
                    <th style="text-align: center">Action</th>
                </tr>
            </thead>
            <tbody>
                <?php
                if ($totalCaseStudy > 0) {
                    while ($row = mysqli_fetch_array($getCaseStudydetails)) {
                        $type = $row['type'];
                        if ($type == 'floor')
                            $caseStudyName = 'Floor Therapy';
                        else if ($type == 'PACR')
                            $caseStudyName = 'PACR';
                        else
                            $caseStudyName = 'Adult ICU & NICU/PICU';

                        $courselocationId = $row['locationId'];
                        $parentRotationId = stripslashes($row['parentRotationId']);
                        $caseStudyRotationId = stripslashes($row['rotationId']);
                        $rotationLocationId = stripslashes($row['rotationLocationId']);

                        $locationId = 0;
                        if ($rotationLocationId != $courselocationId && $parentRotationId > 0) {
                            if ($parentRotationId > 0) {
                                if (!$rotationLocationId)
                                    $locationId = $objRotation->GetLocationByRotation($rotationId);
                                else
                                    $locationId  = $rotationLocationId;
                            }
                        } else {
                            $locationId  = $courselocationId;
                        }

                        //Get Time Zone By Rotation 
                        $objLocation = new clsLocations();
                        $TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
                        unset($objLocation);
                        if ($TimeZone == '')
                            $TimeZone = $_SESSION["loggedStudentSchoolTimeZone"];

                        $caseStudyId = $row['caseStudyId'];
                        $caseStudydate = $row['caseStudydate'];
                        $caseStudydate = converFromServerTimeZone($caseStudydate, $TimeZone);
                        $caseStudydate = date("m/d/Y", strtotime($caseStudydate));
                        $rotationname = $row['rotationname'];
                        $ChiefComplaint = $row['ChiefComplaint'];
                        $isClinicianDate = $clinicianDate = $row['ClinicianDate'];
                        if ($clinicianDate != '') {
                            $clinicianDate = converFromServerTimeZone($clinicianDate, $TimeZone);
                            $clinicianDate = date("m/d/Y", strtotime($clinicianDate));
                        }
                        $rsClinicianReviewsCount = 0;
                        if ($type == 'PACR') {
                            //For Clinician Reviews
                            $objCaseStudy = new clsCaseStudy();
                            $rsClinicianReviews = $objCaseStudy->GetClinicianReviews($caseStudyId);
                            $rsClinicianReviewsCount = ($rsClinicianReviews != '') ? mysqli_num_rows($rsClinicianReviews) : 0;
                            unset($objCaseStudy);
                        }
                        /* 21042021 */
                        $astrisk = '';
                        if ($row['clinician_comments'] != '' || $row['school_comments'] != '') {
                            $astrisk = '<span class="text-danger"><b>*</b></span>';
                        }
                        /* 21042021 */
                        if ($clinicianDate) {
                            $clinicianDate = $clinicianDate;
                        } else if ($clinicianDate == '' && $rsClinicianReviewsCount) {
                            $clinicianDate = '<p style="color: red;">Under Review</p>';
                            $isActionBtnName = 'Edit';
                        } else {
                            $clinicianDate = '-';
                        }
                        $schoolDate = $row['schoolDate'];
                        if ($schoolDate != '') {
                            $schoolDate = converFromServerTimeZone($schoolDate, $TimeZone);
                            $schoolDate = date("m/d/Y", strtotime($schoolDate));
                        }
                        if ($schoolDate) {
                            $isSchoolAproved = 1;
                        } else {
                            $schoolDate = '-';
                            $isSchoolAproved = 0;
                        }

                        $shortTitlelen = strlen($ChiefComplaint);

                        // if($shortTitlelen > 40)
                        // {

                        //     $caseChiefComplaint=substr($ChiefComplaint,0,40);
                        //     $caseChiefComplaint .= '...';

                        // }else{
                        //     $caseChiefComplaint=$ChiefComplaint;
                        // }
                        $caseChiefComplaint = wordwrap($ChiefComplaint, 40, "<br>\n", true);
                        $school_comments = $row['school_comments'];
                        $clinician_comments = $row['clinician_comments'];
                        $school_comments = ($school_comments != '') ? 'Yes' : 'No';
                        $clinician_comments = ($clinician_comments != '') ? 'Yes' : 'No';

                        // Case Study Status
                        $caseStudyStatus = $row['status'];
                        if ($caseStudyStatus == '0') {
                            $caseStudyStatus = 'Pending';
                        } else if ($caseStudyStatus == '1') {
                            $caseStudyStatus = 'Completed';
                        }
                        
                        $caseStudyStatusColor = '';
                        $caseStudyStatusColor = ($caseStudyStatus == 'Pending') ? 'red' : 'green';
                ?>
                        <tr>
                            <td><?php echo ($caseStudydate); ?></td>
                            <td><?php echo ($rotationname); ?></td>
                            <td title="<?php echo ($ChiefComplaint); ?>"><?php echo $caseStudyName; ?></td>
                            <td><?php echo ($caseChiefComplaint); ?></td>
                            <td class="text-center"><?php echo ($clinicianDate); ?></td>
                            <td class="text-center"><?php echo ($schoolDate); ?></td>
                            <td><?php
                                echo 'Clinician: ' . $clinician_comments . '</br>School: ' . $school_comments;
                                ?></td>
                            <td class="text-center" style="color: <?php echo $caseStudyStatusColor; ?>"><?php echo $caseStudyStatus; ?></td>
                            <td>
                                <?php
                                $rotationStatus = checkRotationStatus($caseStudyRotationId);

                                if ($type == 'floor') {
                                    if ($isClinicianDate != '' || $schoolDate != '-' || $rotationStatus) { ?>
                                        <a href="addCaseStudy.html?caseStudyId=<?php echo EncodeQueryData($caseStudyId); ?>&view=V">View</a>
                                    <?php  } else {  ?>
                                        <a href="addCaseStudy.html?caseStudyId=<?php echo EncodeQueryData($caseStudyId); ?>">Edit</a>
                                    <?php } ?>
                                    <?php if ($rotationStatus == 0) {
                                        if ($isSchoolAproved == 0) { ?>
                                            | <a href="javascript:void(0);" class="deleteAjaxRowFloor" caseStudyId="<?php echo EncodeQueryData($caseStudyId); ?>">Delete</a>
                                    <?php }
                                    } ?>
                                    <?php } else if ($type == 'PACR') {
                                    if ($isClinicianDate != '' || $schoolDate != '-' || $rotationStatus) { ?>
                                        <a href="addCaseStudyPACR.html?caseStudyId=<?php echo EncodeQueryData($caseStudyId); ?>&view=V">View</a>
                                    <?php  } else {  ?>
                                        <a href="addCaseStudyPACR.html?caseStudyId=<?php echo EncodeQueryData($caseStudyId); ?>">Edit</a>
                                    <?php } ?>
                                    <?php if ($rotationStatus == 0) {
                                        if ($isSchoolAproved == 0) { ?>
                                            | <a href="javascript:void(0);" class="deleteAjaxRowPACR" caseStudyId="<?php echo EncodeQueryData($caseStudyId); ?>">Delete</a>
                                    <?php }
                                    } ?>
                                    <?php } else {
                                    if ($isClinicianDate != '' || $schoolDate != '-' || $rotationStatus) { ?>
                                        <a href="addCaseStudyAdult.html?caseStudyId=<?php echo EncodeQueryData($caseStudyId); ?>&view=V">View</a>
                                    <?php  } else {  ?>
                                        <a href="addCaseStudyAdult.html?caseStudyId=<?php echo EncodeQueryData($caseStudyId); ?>">Edit</a>
                                    <?php } ?>
                                    <?php if ($rotationStatus == 0) {
                                        if ($isSchoolAproved == 0) { ?>
                                            | <a href="javascript:void(0);" class="deleteAjaxRowAdult" caseStudyId="<?php echo EncodeQueryData($caseStudyId); ?>">Delete</a>
                                        <?php } ?>
                                <?php }
                                }
                                // ($type == 'floor' || $type == 'Adult') && && ($isClinicianDate != '' || $schoolDate != '-')
                                ?>
                                <?php if ($exportToPdf && ($isClinicianDate != '' || $schoolDate != '-')) { ?>
                                    | <a target="_blank" href="pdfViewForAllModules.html?id=<?php echo (EncodeQueryData($caseStudyId)); ?>&studentId=<?php echo (EncodeQueryData($currentstudentId)); ?>&rotationId=<?php echo EncodeQueryData($rotationId); ?>&type=<?php echo $type; ?>">Export to PDF</a>
                                <?php } ?>

                            </td>
                        </tr>
                <?php

                    }
                }
                unset($objRotation);
                ?>
            </tbody>
        </table>
    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";


        $(window).load(function() {
            $("#divTopLoading").addClass('hide');
        });

        $(document).on('click', '.deleteAjaxRowFloor', function() {

            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var caseStudyId = $(this).attr('caseStudyId');
            var userId = '<?php echo EncodeQueryData($currentstudentId); ?>';
            var isUser = 3; //for student

            alertify.confirm('Case Study: ', 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: caseStudyId,
                        userId: userId,
                        isUser: isUser,
                        type: 'caseStusyFloor'
                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        alertify.success('Deleted');
                    }
                });
            }, function() {});

        });

        $(document).on('click', '.deleteAjaxRowAdult', function() {

            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var caseStudyId = $(this).attr('caseStudyId');
            var userId = '<?php echo EncodeQueryData($currentstudentId); ?>';
            var isUser = 3; //for student


            alertify.confirm('Case Study: ', 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: caseStudyId,
                        userId: userId,
                        isUser: isUser,
                        type: 'caseStusyAdult'
                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        alertify.success('Deleted');
                    }
                });
            }, function() {});

        });

        $(document).on('click', '.deleteAjaxRowPACR', function() {

            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var caseStudyId = $(this).attr('caseStudyId');
            var userId = '<?php echo EncodeQueryData($currentstudentId); ?>';
            var isUser = 3; //for student


            alertify.confirm('Case Study: ', 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: caseStudyId,
                        userId: userId,
                        isUser: isUser,
                        type: 'caseStusyPACR'
                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        alertify.success('Deleted');
                    }
                });
            }, function() {});

        });

        $(".select2_single").select2();

        var current_datatable = $("#datatable-responsive").DataTable({
            "scrollX": true,
            responsive: false,
            "ordering": true,
            "order": [
                [0, "desc"]
            ],
            "aoColumns": [{
                "sWidth": "10%"
            }, {
                "sWidth": "20%"
            }, {
                "sWidth": "20%"
            }, {
                "sWidth": "20%"
            }, {
                "sWidth": "10%"
            }, {
                "sWidth": "10%"
            }, {
                "sWidth": "10%"
            }, {
                "sWidth": "10%"
            }, {
                "sWidth": "10%",
                "bSortable": false
            }]
        });
    </script>


</body>

</html>