<?php
class clsSchool
{
	var $schoolId = '';
	var $slug = '';
	var $code = '';
	var $title = '';
	var $displayName = '';
	var $contactPerson = '';
	var $contactEmail = '';
	var $phone = '';
	var $cellPhone = '';
	var $stateId = '';
	var $city = '';
	var $zip = '';
	var $address1 = '';
	var $address2 = '';
	var $domainName = '';
	var $isDomainActive = '';
	var $mapAddress = '';
	var $email = '';
	var $description = '';
	var $googleTrackingId = '';
	var $googlePlus = '';
	var $facebook = '';
	var $twitter = '';
	var $linkedin = '';
	var $youtube = '';
	var $instagram = '';
	var $googlePlayAppURL = '';
	var $itunesAppURL = '';
	var $logoName = '';
	var $logoSmallName = '';
	var $isSuperAdmin = 0;
	var $coarcId = '';
	var $billingId = '';
	var $contractPeriodId = '';
	var $timeZoneId = '';
	var $createdBy = '';
	var $isActive = 1;
	var $schoolDir = '';
	var $schoolSectionTitle = '';
	var $currentSchoolId = '';
	var $schoolSectionId = '';
	var $schoolOptionText = '';
	var $schoolOptionValue = '';
	var $schoolIsCorrectAnswer = '';
	var $schooltitle = '';
	var $schoolQuestionTitle = '';
	var $schoolQuestionType = '';
	var $schoolQuestionId = '';
	var $schoolTopicId = '';
	var $schoolIncidentQuestionTitle = '';
	var $schoolIncidentQuestionType = '';
	var $formativeQuestionType = '';
	var $schoolFormativeQuestionTitle = '';
	var $sectionMasterId = '';
	var $schoolFormativeQuestionType = '';
	var $optionText = '';
	var $contractStartDate = '';
	var $accountPayableContact = '';
	var $accountPayableEmail = '';
	var $accountPayablePhone = '';
	var $currentOrganizationLogoImagePath = '';
	var $isdefaultFloorAndIcuEval = 0;
	var $currentOrganizationDisplayname = '';
	var $scheduleActive = '';
	var $checkoffType = '';
	var $isCustomSms = 0;
	var $adminSms = '';
	var $clinicianSms = '';
	var $studentSms = '';
	var $studentChangeHospitalSite = 0;
	var $selfunlock = 0;
	var $countryCode = 0;

	function SaveSchool($schoolId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($schoolId > 0) {
			$sql = "UPDATE schools SET 
										
										title = '" . addslashes($this->title) . "', 
										code = '" . addslashes($this->code) . "', 
										displayName = '" . addslashes($this->displayName) . "', 
										contactPerson = '" . addslashes($this->contactPerson) . "',
										contactEmail = '" . addslashes($this->contactEmail) . "',
										phone = '" . ($this->phone) . "',
										cellPhone = '" . ($this->cellPhone) . "',
										stateId = '" . ($this->stateId) . "',
										city = '" . ($this->city) . "',
										zip = '" . ($this->zip) . "',
										countryCode = '" . ($this->countryCode) . "',
										address1 = '" . addslashes($this->address1) . "',
										address2 = '" . addslashes($this->address2) . "',
										domainName = '" . ($this->domainName) . "',
										isDomainActive = '" . ($this->isDomainActive) . "',
										mapAddress = '" . addslashes($this->mapAddress) . "',
										email = '" . ($this->email) . "',
										description = '" . addslashes($this->description) . "',
										coarcId = '" . ($this->coarcId) . "',
										billingId = '" . ($this->billingId) . "',
										contractPeriodId = '" . ($this->contractPeriodId) . "',
										timeZoneId = '" . ($this->timeZoneId) . "',
										contractStartDate = '" . ($this->contractStartDate) . "',
										accountPayableContact = '" . ($this->accountPayableContact) . "',
										accountPayableEmail = '" . ($this->accountPayableEmail) . "',
										accountPayablePhone = '" . ($this->accountPayablePhone) . "',
										updatedBy = '" . ($this->createdBy) . "',
										updatedDate = '" . (date("Y-m-d h:i:s")) . "',
										isActiveCheckoffForStudent='" . ($this->isActiveCheckoffForStudent) . "',
										isDefaultCiEval='" . ($this->isDefaultCiEval) . "',
										isClinicianReports='" . ($this->isClinicianReports) . "'
										Where schoolId= " . $schoolId;

			// echo $sql;exit;
			$objDB->ExecuteQuery($sql);
		} else {
			$sql = "INSERT INTO schools (slug, code, title, displayName, contactPerson,
										 contactEmail, phone, cellPhone, stateId, city, zip, countryCode, address1,
										 address2, domainName, isDomainActive, mapAddress, email, description,
										 googleTrackingId, googlePlus, facebook, twitter, linkedin,
										 youtube, instagram, googlePlayAppURL, itunesAppURL, logoName,
										 logoSmallName, isSuperAdmin,isActive, coarcId,billingId,contractPeriodId,
										 timeZoneId,contractStartDate,accountPayableContact,accountPayableEmail,
											accountPayablePhone,createdBy, createdDate,isActiveCheckoffForStudent,isDefaultCiEval,isClinicianReports
										 ) 
								VALUES  ('" . addslashes($this->slug) . "',
											'" . addslashes($this->code) . "',
											'" . ($this->title) . "',
											'" . ($this->displayName) . "',
											'" . ($this->contactPerson) . "',
											'" . ($this->contactEmail) . "',
											'" . ($this->phone) . "',
											'" . ($this->cellPhone) . "',
											'" . ($this->stateId) . "',
											'" . ($this->city) . "',
											'" . ($this->zip) . "',
											'" . ($this->countryCode) . "',
											'" . addslashes($this->address1) . "',
											'" . addslashes($this->address2) . "',
											'" . addslashes($this->domainName) . "',
											'" . ($this->isDomainActive) . "',
											'" . addslashes($this->mapAddress) . "',
											'" . ($this->email) . "',
											'" . addslashes($this->description) . "',
											'" . ($this->googleTrackingId) . "',
											'" . ($this->googlePlus) . "',
											'" . ($this->facebook) . "',
											'" . ($this->twitter) . "',
											'" . ($this->linkedin) . "',
											'" . ($this->youtube) . "',
											'" . ($this->instagram) . "',
											'" . ($this->googlePlayAppURL) . "',
											'" . ($this->itunesAppURL) . "',
											'" . ($this->logoName) . "',
											'" . ($this->logoSmallName) . "',
											'" . ($this->isSuperAdmin) . "',
											'" . ($this->isActive) . "',
											'" . ($this->coarcId) . "',
											'" . ($this->billingId) . "',
											'" . ($this->contractPeriodId) . "',
											'" . ($this->timeZoneId) . "',
											'" . ($this->contractStartDate) . "',
											'" . ($this->accountPayableContact) . "',
											'" . ($this->accountPayableEmail) . "',
											'" . ($this->accountPayablePhone) . "',
											'" . ($this->createdBy) . "',
											'" . date("Y-m-d h:i:s") . "',
											'" . ($this->isActiveCheckoffForStudent) . "',
											'" . ($this->isDefaultCiEval) . "',
											'" . ($this->isClinicianReports) . "'

											)";
			// echo $sql; exit;
			$schoolId = $objDB->ExecuteInsertQuery($sql);
		}

		unset($objDB);
		return $schoolId;
	}

	function UpdateSchoolInfo($schoolId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($schoolId > 0) {
			$sql = "UPDATE schools SET 
					title = '" . addslashes($this->title) . "', 
					displayName = '" . addslashes($this->displayName) . "', 
					email = '" . addslashes($this->email) . "',
					contactPerson = '" . addslashes($this->contactPerson) . "',
					contactEmail = '" . addslashes($this->contactEmail) . "',
					phone = '" . ($this->phone) . "',
					address1 = '" . addslashes($this->address1) . "',
					address2 = '" . addslashes($this->address2) . "',
					stateId = '" . ($this->stateId) . "',
					city = '" . ($this->city) . "',
					zip = '" . ($this->zip) . "',
					countryCode = '" . ($this->countryCode) . "',
					coarcId = '" . ($this->coarcId) . "',
					billingId = '" . ($this->billingId) . "',
					contractPeriodId = '" . ($this->contractPeriodId) . "',
					timeZoneId = '" . ($this->timeZoneId) . "',
					contractStartDate = '" . ($this->contractStartDate) . "',
					accountPayableContact = '" . ($this->accountPayableContact) . "',
					accountPayableEmail = '" . ($this->accountPayableEmail) . "',
					accountPayablePhone = '" . ($this->accountPayablePhone) . "',
					isdefaultFloorAndIcuEval = '" . ($this->isdefaultFloorAndIcuEval) . "',
					updatedBy = '" . ($this->createdBy) . "',
					updatedDate = '" . (date("Y-m-d h:i:s")) . "'
					Where schoolId= " . $schoolId;
			// echo $sql;exit;
			$objDB->ExecuteQuery($sql);
		}

		return $schoolId;
	}

	function DeleteSchool($schoolId)
	{
		$objDB = new clsDB();
		$sql = '';
		$sql = "UPDATE schools SET 
					isDeleted=1	Where schoolId= " . $schoolId;
		//echo $sql;exit;
		$objDB->ExecuteQuery($sql);
	}
	/*function DeleteSchool($schoolId)
	{
		
		$result="";
		if($schoolId > 0)
		{
			//Delete all school files
			$schoolDir = ROOT_PATH."/upload/schools/".$schoolId;
			delete_directory($schoolDir);
			//Delete from all tables
			$objDB = new clsDB();
			$sql = 'DELETE schools,clinician,courses,location,hospitalsites,student,systemusermaster
					,systemuserrolemaster,rankmaster,schoolclinicalsiteunit,semestermaster,clinicianrolemaster,
					schooldefaultquestionmaster,schoolsectionmaster,schooltopicmaster
					FROM schools 
					LEFT JOIN clinician on schools.schoolId=clinician.schoolId
					LEFT JOIN student on schools.schoolId=student.schoolId
					LEFT JOIN courses on schools.schoolId=courses.schoolId
					LEFT JOIN location on schools.schoolId=location.schoolId
					LEFT JOIN hospitalsites on schools.schoolId=hospitalsites.schoolId
					LEFT JOIN systemusermaster on schools.schoolId=systemusermaster.schoolId
					LEFT JOIN systemuserrolemaster on schools.schoolId=systemuserrolemaster.schoolId
					LEFT JOIN schooldefaultquestionmaster on schools.schoolId=schooldefaultquestionmaster.schoolId
					LEFT JOIN schoolsectionmaster on schools.schoolId=schoolsectionmaster.schoolId
					LEFT JOIN schooltopicmaster on schools.schoolId=schooltopicmaster.schoolId
					LEFT JOIN rankmaster ON schools.schoolId=rankmaster.schoolId
					LEFT JOIN clinicianrolemaster ON schools.schoolId=clinicianrolemaster.schoolId
					LEFT JOIN semestermaster ON schools.schoolId=semestermaster.schoolId
					LEFT JOIN schoolclinicalsiteunit ON schools.schoolId=schoolclinicalsiteunit.schoolId
					WHERE schools.schoolId='.$schoolId;
				//echo $sql;exit;
					//rotation
					//attendance
					//reports
					//checkoff
		
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
		return $result;
	} */

	function GetAllSchools($isactive = '', $schoolIds = '')
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM schools where isSuperAdmin=0 AND isDeleted=0 ";
		if ($isactive != '')
			$sql .= " AND isActive = $isactive";

		if ($schoolIds != '')
			$sql .= " AND schoolId IN ( $schoolIds ) ";

		$sql .= " ORDER BY title ASC";
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetSchoolsByPlatform($type = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM schools where isSuperAdmin=0 AND isDeleted=0 AND isActiveCheckoffForStudent = $type  ORDER BY `schools`.`title` ASC";
		// if($type)
		// 	$sql .= " AND isActiveCheckoffForStudent = $type";
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetSchoolDetails($schoolId)
	{
		$objDB = new clsDB();
		$sql = "SELECT * FROM  schools WHERE schoolId=" . $schoolId;
		//echo $sql;exit;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}


	function GetSchoolByDomainName($domainName)
	{
		$objDB = new clsDB();
		$sql = "SELECT * FROM schools WHERE LOWER(domainName) = LOWER('" . $domainName . "')";
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}

	function GetSchoolBySlug($slug)
	{
		$objDB = new clsDB();
		$sql = "SELECT * FROM schools WHERE LOWER(slug) = LOWER('" . $slug . "')";
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}

	function GetDefaultStudentCheckoff()
	{
		$objDB = new clsDB();
		$sql = "SELECT isActiveCheckoffForStudent FROM schools ";
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}


	function GetSchoolName($schoolId)
	{
		$returnSchoolName = '';
		$objDB = new clsDB();
		$sql = "SELECT displayName FROM schools WHERE schoolId = " . $schoolId;
		$returnSchoolName = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $returnSchoolName;
	}

	function GetSchoolCounts($schoolId = 0)
	{
		$returnCounts = '';
		$objDB = new clsDB();
		$sql = "SELECT A.TotalCount, B.ActiveCount  FROM (SELECT COUNT(*) As TotalCount FROM `schools` WHERE  isDeleted=0 AND isSuperAdmin =0) As A,
				(SELECT COUNT(*) As ActiveCount FROM `schools` WHERE isActive=1 AND isDeleted=0 AND  isSuperAdmin =0) As B";
		$returnCounts = $objDB->GetDataRow($sql);
		unset($objDB);
		return $returnCounts;
	}

	function GetAllSchoolUsersCount($schoolId)
	{
		$returnCount = 0;
		$objDB = new clsDB();
		$sql = "SELECT M.totalSchoolUserCount, C.ClinicianCount, S.StudentCount FROM
				(SELECT COUNT(*) As totalSchoolUserCount FROM `systemusermaster` where schoolId=" . $schoolId . ") As M,
				(SELECT COUNT(*) As ClinicianCount FROM `clinician` where schoolId=" . $schoolId . ") As C,
				(SELECT COUNT(*) As StudentCount FROM `student` where schoolId=" . $schoolId . ") As S";

		$returnCount = $objDB->GetDataRow($sql);
		unset($objDB);
		return $returnCount;
	}

	function SetSchoolStatus($schoolId, $status)
	{
		if ($schoolId > 0) {
			$objDB = new clsDB();
			$sql = "Update schools set isActive = " . $status . " Where schoolId = " . $schoolId;
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
	}

	//tejas

	function UpdateSchoolCode($schoolId, $code)
	{
		if ($schoolId > 0) {
			$objDB = new clsDB();
			$sql = "Update schools set code = '" . $code . "' Where schoolId = " . $schoolId;
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
	}

	function UpdateSchoolSlug($schoolId, $slug)
	{
		if ($schoolId > 0) {
			$objDB = new clsDB();
			$sql = "Update schools set slug = '" . $slug . "' Where schoolId = " . $schoolId;
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
	}

	function UpdateSchoolImageFileName($schoolId, $logoName, $logoSmallName)
	{
		if ($schoolId > 0) {
			$objDB = new clsDB();
			$sql = "Update schools set logoName = '" . $logoName . "', logoSmallName='" . $logoSmallName . "' Where schoolId=" . $schoolId;
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
	}
	function GetSchoolNames($schoolId)
	{

		$objDB = new clsDB();
		$sql = "SELECT schoolId,displayName FROM schools WHERE schoolId=" . $schoolId;
		//echo $sql;exit;
		$schoolId = $objDB->GetDataRow($sql);
		unset($objDB);
		return $schoolId;
	}
	function GetContarctPeriods()
	{
		$objDB = new clsDB();
		$sql = "SELECT * FROM contractperiod";
		$result = $objDB->GetResultset($sql);
		unset($objDB);
		return $result;
	}

	function GetBilling()
	{
		$objDB = new clsDB();
		$sql = "SELECT * FROM billing";
		$result = $objDB->GetResultset($sql);
		unset($objDB);
		return $result;
	}
	function GetTimezone()
	{
		$objDB = new clsDB();
		$sql = "SELECT * FROM timezonemaster GROUP BY timeZoneId ORDER BY locations";
		//ECHO $sql;
		$result = $objDB->GetResultset($sql);
		unset($objDB);
		return $result;
	}
	function IsDuplicateSlug($slug)
	{
		$retschoolId = 0;
		$objDB = new clsDB();

		$sql = "select schoolId from schools where  LOWER(slug) = '" . strtolower($slug) . "'";

		//echo $sql;exit;
		$retschoolId = $objDB->GetSingleFieldValue($sql);
		unset($objDB);

		if ($retschoolId > 0) {
			return 1;
		} else {
			return 0;
		}
	}

	function GetDefaultCopySchools()
	{
		$objDB = '';
		$objDB = new clsDB();
		$sql = "SELECT * FROM schools WHERE ";
		$sql .= " isCopiedDefaultData='0' AND isDefaultCopyProcessing='0'  ";
		//for testing
		//$sql .="  schoolId=3";
		// echo $sql;exit;
		$rows = $objDB->GetResultset($sql);
		return $rows;
	}

	function GetRemainingDefaultCopySchools()
	{
		$objDB = '';
		$objDB = new clsDB();
		$sql = "SELECT * FROM schools WHERE ";
		$sql .= " isCopiedDefaultData='1' AND isDefaultCopyProcessing='1'  ";
		//for testing
		// $sql .="  and schoolId=75";
		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		return $rows;
	}

	function GetDefaultCopySchools_Custome()
	{
		$objDB = '';
		$objDB = new clsDB();
		$sql = "SELECT * FROM schools WHERE ";
		//for testing
		$sql .= "  schoolId=0";
		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		return $rows;
	}

	function GetDeletedSchools()
	{

		//echo 'here';exit;
		$objDB = '';
		$objDB = new clsDB();
		$sql = "SELECT * FROM schools WHERE isDeleted=1";

		//for testing
		//$sql .="  schoolId=27";
		//echo $sql;exit;
		$rows = $objDB->GetResultset($sql);
		return $rows;
	}

	function DeletePermonentSchool($schoolId)
	{

		$result = "";
		if ($schoolId > 0) {
			$schoolDir = ROOT_PATH . "/upload/schools/" . $schoolId;
			delete_directory($schoolDir);
			$objDB = new clsDB();
			$sql = "DELETE FROM schools WHERE  mapAddress='Delete Completed' AND isDeleted=1";
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
		return $result;
	}

	function GetSuperAdminDetails()
	{
		$objDB = '';
		$objDB = new clsDB();
		$sql = "SELECT * FROM schools WHERE isSuperAdmin='1' ";
		//echo $sql;
		$rows = $objDB->GetDataRow($sql);
		return $rows;
	}

	function CopyStudent($studentId, $schoolId)
	{
		$objDB = new clsDB();
		$sql = "INSERT INTO student (schoolId, locationId,rankId,profilePic, firstName, middleName, lastName,
							address1, address2, city,  stateId, zip, phone, cellPhone, 
							email, username, passwordHash, isActive,
							createdBy, createdDate) 
		
		SELECT " . $schoolId . ", locationId,rankId,profilePic, firstName, middleName, lastName,
							address1, address2, city,  stateId, zip, phone, cellPhone, 
							email, CONCAT(firstName,'_', " . $schoolId . "), passwordHash, isActive,
							createdBy, createdDate FROM student
		WHERE studentId=" . $studentId;

		$schoolStudentId = $objDB->ExecuteInsertQuery($sql);

		unset($objDB);
		return  $schoolStudentId;;
	}

	function GetAllSchoolsToCopyStudent($schoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT schools.* FROM schools   
				INNER JOIN student on student.schoolId = schools.schoolId
				where isSuperAdmin=0 AND isDeleted=0 AND schools.schoolId != " . $schoolId . "
				GROUP by schools.schoolId
				order by displayName";
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetSchoolNamesSingle($schoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT displayName FROM schools WHERE schoolId=" . $schoolId;
		//echo $sql;exit;
		$rows = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $rows;
	}

	function GetSlugName($schoolId)
	{
		$returnSchoolSlug = '';
		$objDB = new clsDB();
		$sql = "SELECT slug FROM schools WHERE schoolId = " . $schoolId;
		$returnSchoolSlug = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $returnSchoolSlug;
	}

	function GetRegistrationNotification()
	{
		$result = '';
		$objDB = new clsDB();
		$sql = "SELECT description FROM registrationnotification";
		$result = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $result;
	}

	function SaveRegistrationNotification($dbDescription)
	{
		$result = '';
		$objDB = new clsDB();
		if ($dbDescription)
			$sql = "UPDATE registrationnotification SET description ='" . addslashes($this->description) . "'";
		else
			$sql = "INSERT into registrationnotification (description) VALUES  ('" . addslashes($this->description) . "')";

		$result = $objDB->ExecuteInsertQuery($sql);
		unset($objDB);
		return $result;
	}

	function SaveCanvasGroups()
	{
		$result = '';
		$objDB = new clsDB();

		$sql = "INSERT into canvasgroups (schoolId,title,canvasGroupId) VALUES  
											('" . addslashes($this->schoolId) . "'
											,'" . addslashes($this->title) . "'
											,'" . addslashes($this->canvasGroupId) . "')";
		$result = $objDB->ExecuteInsertQuery($sql);
		unset($objDB);
		return $result;
	}

	function updateAbsenceInfo($schoolId)
	{
		$objDB = new clsDB();
		$sql = '';
		$sql = "UPDATE schools SET 
					absenceSMSTime = '" . addslashes($this->absenceSMSTime) . "', 
					absenceSMSStatus = '" . addslashes($this->absenceSMSStatus) . "', 
					absenceSMSSendTo = '" . addslashes($this->absenceSMSSendTo) . "' ,
					adminRolesAbsenceSMSSendTo = '" . addslashes($this->adminRolesAbsenceSMSSendTo) . "', 
					clinitionRoleAbsenceSMSSendTo = '" . addslashes($this->clinitionRoleAbsenceSMSSendTo) . "' 
					Where schoolId= " . $schoolId;
		// echo $sql;exit;
		$objDB->ExecuteQuery($sql);
	}

	function GetSchoolAbsenseSmsDetails($schoolId)
	{
		$objDB = new clsDB();
		$sql = "SELECT schools.schoolId,schools.absenceSMSTime,schools.absenceSMSStatus,schools.absenceSMSSendTo,schools.adminRolesAbsenceSMSSendTo,schools.clinitionRoleAbsenceSMSSendTo,absensesmsdetails.* FROM  schools 
		LEFT JOIN absensesmsdetails ON absensesmsdetails.schoolId = schools.schoolId WHERE schools.schoolId =" . $schoolId;
		//echo $sql;exit;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}

	function deleteAbsenceDetails($schoolId)
	{

		$result = "";
		if ($schoolId > 0) {

			$objDB = new clsDB();
			$sql = "DELETE FROM absensesmsdetails WHERE  schoolId=" . $schoolId;
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
		return $result;
	}

	function updateAbsenceDetails($schoolId)
	{
		$objDB = new clsDB();
		$sql = '';
		$sql = "INSERT INTO absensesmsdetails (schoolId, isCustomSms, adminSms, clinicianSms, studentSms)
				VALUES  (
							'" . addslashes($schoolId) . "',
							'" . addslashes($this->isCustomSms) . "',
							'" . addslashes($this->adminSms) . "',
							'" . addslashes($this->clinicianSms) . "',
							'" . addslashes($this->studentSms) . "'
		
		)";
		// echo $sql;exit;

		$attendanceDetailsId = $objDB->ExecuteInsertQuery($sql);
		return $attendanceDetailsId;
	}

	// Get all Active user Count
	function GetUserCounts()
	{
		$returnCounts = '';
		$objDB = new clsDB();

		$sql = "SELECT SUM(userId) AS total_users
		FROM (
			SELECT count(systemusermaster.systemUserMasterId ) AS userId 
			FROM `systemusermaster` 
			INNER JOIN schools ON schools.schoolId = systemusermaster.schoolId
			WHERE systemusermaster.isActive = 1 AND schools.isActive = 1 AND schools.isDeleted=0 AND  schools.isSuperAdmin=0
		
			UNION ALL
		
			SELECT COUNT(student.studentId) AS userId 
			FROM student 
			INNER JOIN schools ON schools.schoolId = student.schoolId
			WHERE schools.isActive = 1 AND student.isActive = 1 AND schools.isDeleted=0 AND  schools.isSuperAdmin=0
		
			UNION ALL
		
			SELECT COUNT(clinician.clinicianId) AS userId 
			FROM clinician 
			INNER JOIN schools ON schools.schoolId = clinician.schoolId
			WHERE schools.isActive = 1 AND clinician.isActive = 1 AND schools.isDeleted=0 AND  schools.isSuperAdmin=0
		) AS user_counts";

		$returnCounts = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $returnCounts;
	}

	// Get all Active blocked user Count
	function GetBlockedUserCounts()
	{
		$returnCounts = '';
		$objDB = new clsDB();

		$sql = "SELECT SUM(userId) AS total_users
		FROM (
			SELECT count(systemusermaster.systemUserMasterId ) AS userId 
			FROM `systemusermaster` 
			INNER JOIN schools ON schools.schoolId = systemusermaster.schoolId
			WHERE systemusermaster.isActive = 1 AND systemusermaster.isBlocked = 1 AND schools.isActive = 1 AND schools.isDeleted=0 AND  schools.isSuperAdmin=0
		
			UNION ALL
		
			SELECT COUNT(student.studentId) AS userId 
			FROM student 
			INNER JOIN schools ON schools.schoolId = student.schoolId
			INNER JOIN rankmaster on rankmaster.rankId = student.rankId

			WHERE schools.isActive = 1 AND student.isActive = 1 AND student.isBlocked = 1 AND schools.isDeleted=0 AND  schools.isSuperAdmin=0 
			AND rankmaster.title != 'Graduate' AND rankmaster.title != 'Dropout'

		
			UNION ALL
		
			SELECT COUNT(clinician.clinicianId) AS userId 
			FROM clinician 
			INNER JOIN schools ON schools.schoolId = clinician.schoolId
			WHERE schools.isActive = 1 AND clinician.isActive = 1 AND clinician.isBlocked = 1 AND schools.isDeleted=0 AND  schools.isSuperAdmin=0
		   
		) AS user_counts";

		$returnCounts = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $returnCounts;
	}

	// Get all Active blocked user Details
	function GetActiveBlockedUserdetails()
	{
		$returnCounts = '';
		$objDB = new clsDB();

		$sql = "SELECT COUNT(systemusermaster.systemUserMasterId) AS users, 
				COALESCE(
					systemuserrolemaster.title,
					'Student'
				)AS roleTitle
				FROM systemusermaster
				INNER JOIN schools ON schools.schoolId = systemusermaster.schoolId
				LEFT JOIN systemuserrolemaster ON systemuserrolemaster.systemUserRoleMasterId = systemusermaster.systemUserRoleMasterId
				WHERE systemusermaster.isActive = 1
				AND systemusermaster.isBlocked = 1
				AND schools.isActive = 1
				AND schools.isDeleted = 0
				AND schools.isSuperAdmin = 0
				GROUP BY COALESCE(systemuserrolemaster.title, 'Student')

				UNION ALL

				SELECT COUNT(student.studentId)AS users, 'Student' AS roleTitle
				FROM student
				INNER JOIN schools ON schools.schoolId = student.schoolId
				LEFT JOIN rankmaster on rankmaster.rankId = student.rankId
				WHERE schools.isActive = 1
				AND student.isActive = 1
				AND student.isBlocked = 1
				AND schools.isDeleted = 0
				AND schools.isSuperAdmin = 0
				AND rankmaster.title != 'Graduate' AND rankmaster.title != 'Dropout'
				UNION ALL

				SELECT COUNT(clinician.clinicianId)AS users, 'Clinician' AS roleTitle
				FROM clinician
				INNER JOIN schools ON schools.schoolId = clinician.schoolId
				WHERE schools.isActive = 1
				AND clinician.isActive = 1
				AND clinician.isBlocked = 1
				AND schools.isDeleted = 0
				AND schools.isSuperAdmin = 0 ";

		$returnCounts = $objDB->GetResultSet($sql);
		unset($objDB);
		return $returnCounts;
	}

	// Get all Active Admin Details
	function GetActiveAdminDetails()
	{
		$returnCounts = '';
		$objDB = new clsDB();

		$sql = "SELECT COUNT(systemusermaster.systemUserMasterId) AS users, 
				COALESCE(
					systemuserrolemaster.title,
					'Student'
				)AS roleTitle
				FROM systemusermaster
				INNER JOIN schools ON schools.schoolId = systemusermaster.schoolId
				LEFT JOIN systemuserrolemaster ON systemuserrolemaster.systemUserRoleMasterId = systemusermaster.systemUserRoleMasterId
				WHERE systemusermaster.isActive = 1
				AND systemusermaster.isBlocked = 0
				AND schools.isActive = 1
				AND schools.isDeleted = 0
				AND schools.isSuperAdmin = 0
				GROUP BY COALESCE(systemuserrolemaster.title, 'Student')";

		$returnCounts = $objDB->GetResultSet($sql);
		unset($objDB);
		return $returnCounts;
	}

	// Get all Active Clinician Count
	function GetActiveClinicianCount()
	{
		$returnCounts = '';
		$objDB = new clsDB();

		$sql = "SELECT A.totalClinicianCount, B.activeClinicianCount  FROM 
		(SELECT COUNT(clinician.clinicianId) AS totalClinicianCount 
					FROM clinician 
					INNER JOIN schools ON schools.schoolId = clinician.schoolId
					WHERE schools.isActive = 1 AND schools.isDeleted=0 AND  schools.isSuperAdmin=0
		) As A,
		(SELECT COUNT(clinician.clinicianId) AS activeClinicianCount 
					FROM clinician 
					INNER JOIN schools ON schools.schoolId = clinician.schoolId
					WHERE schools.isActive = 1 AND clinician.isActive = 1 AND schools.isDeleted=0 AND  schools.isSuperAdmin=0) As B";

		$returnCounts = $objDB->GetDataRow($sql);
		unset($objDB);
		return $returnCounts;
	}

	// Get all Active Admin Count
	function GetActiveAdminCount()
	{
		$returnCounts = '';
		$objDB = new clsDB();

		$sql = "SELECT A.totalAdminCount, B.activeAdminCount  FROM 
		(SELECT count(systemusermaster.systemUserMasterId ) AS totalAdminCount 
			FROM `systemusermaster` 
			INNER JOIN schools ON schools.schoolId = systemusermaster.schoolId
			WHERE schools.isActive = 1  AND schools.isDeleted=0 AND  schools.isSuperAdmin=0
		) As A,
		(SELECT count(systemusermaster.systemUserMasterId ) AS activeAdminCount 
			FROM `systemusermaster` 
			INNER JOIN schools ON schools.schoolId = systemusermaster.schoolId
			WHERE systemusermaster.isActive = 1 AND schools.isActive = 1 AND schools.isDeleted=0 AND  schools.isSuperAdmin=0) As B";

		$returnCounts = $objDB->GetDataRow($sql);
		unset($objDB);
		return $returnCounts;
	}

	// Get all Active Student Count
	function GetActiveStudentCount()
	{
		$returnCounts = '';
		$objDB = new clsDB();

		$sql = "SELECT A.totalStudentCount, B.activeStudentCount  FROM 
		(SELECT COUNT(student.studentId) AS totalStudentCount 
			FROM student 
			INNER JOIN schools ON schools.schoolId = student.schoolId
			WHERE schools.isActive = 1  AND schools.isDeleted=0 AND  schools.isSuperAdmin=0
		) As A,
		(SELECT COUNT(student.studentId) AS activeStudentCount 
			FROM student 
			INNER JOIN schools ON schools.schoolId = student.schoolId
			WHERE schools.isActive = 1 AND student.isActive = 1 AND schools.isDeleted=0 AND  schools.isSuperAdmin=0) As B";

		$returnCounts = $objDB->GetDataRow($sql);
		unset($objDB);
		return $returnCounts;
	}

	// Get checkofftype by school
	function GetSchoolsByCheckoffType()
	{
		$returnCounts = '';
		$objDB = new clsDB();

		$sql = "SELECT count(schools.schoolId) as schoolCount, schools.checkoffType FROM `schools` where schools.isActive = 1 AND schools.isDeleted=0 AND  schools.isSuperAdmin=0 AND schools.isActiveCheckoffForStudent=1 group by checkoffType ORDER BY checkoffType asc";
		// echo $sql;
		$returnCounts = $objDB->GetResultSet($sql);
		unset($objDB);
		return $returnCounts;
	}

	// Get schedule school Count
	function GetSchoolsCountBySchedules()
	{
		$returnCounts = '';
		$objDB = new clsDB();

		$sql = "SELECT A.hopitalsiteSchoolCount, B.scheduleSchoolCount FROM
		 (SELECT COUNT(DISTINCT(schools.schoolId)) AS hopitalsiteSchoolCount FROM schools LEFT JOIN rotation ON schools.schoolId = rotation.schoolId WHERE `parentRotationId` > 0 AND `isSchedule` = 0 AND schools.scheduleActive = 0 AND schools.isActive = 1 AND schools.isDeleted=0 AND schools.isSuperAdmin=0 ) AS A,
		 (SELECT COUNT(DISTINCT(schools.schoolId)) AS scheduleSchoolCount FROM schools LEFT JOIN rotation ON schools.schoolId = rotation.schoolId WHERE `parentRotationId` > 0 AND `isSchedule` = 1 AND schools.scheduleActive = 1 AND schools.isActive = 1 AND schools.isDeleted=0 AND schools.isSuperAdmin=0 ) AS B";

		$returnCounts = $objDB->GetDataRow($sql);
		// echo $sql;
		unset($objDB);
		return $returnCounts;
	}

	function GetSchoolsCountByRotation()
	{
		$rotationSchoolCount = 0;
		$objDB = new clsDB();

		$sql = "SELECT GROUP_CONCAT(DISTINCT schoolId) AS allSchoolIds
		FROM (
			SELECT schools.schoolId
			FROM schools
			LEFT JOIN rotation ON schools.schoolId = rotation.schoolId
			WHERE parentRotationId > 0 
			AND isSchedule = 1 
			AND schools.scheduleActive = 1 
			AND schools.isActive = 1 
			AND schools.isDeleted = 0 
			AND schools.isSuperAdmin = 0
		
			UNION
		
			SELECT schools.schoolId
			FROM schools
			LEFT JOIN rotation ON schools.schoolId = rotation.schoolId
			WHERE parentRotationId > 0 
			AND isSchedule = 0 
			AND schools.scheduleActive = 0 
			AND schools.isActive = 1 
			AND schools.isDeleted = 0 
			AND schools.isSuperAdmin = 0
		) AS combined_school_ids;
		";

		$schoolIds = $objDB->GetSingleFieldValue($sql);

		if ($schoolIds != '') {
			$sql = "SELECT count(DISTINCT(schools.schoolId)) AS rotationSchoolCount FROM `rotation`
			LEFT JOIN schools on schools.schoolId = rotation.schoolId
			WHERE rotation.schoolId NOT IN (" . $schoolIds . ") AND schools.isActive = 1 AND schools.isDeleted=0 AND  schools.isSuperAdmin=0";
		}
		$rotationSchoolCount = $objDB->GetSingleFieldValue($sql);
		// echo $sql;
		unset($objDB);
		return $rotationSchoolCount;
	}

	function GetAllSchoolsBySchoolIds($isactive = '', $schoolIds = '')
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM schools where isSuperAdmin=0 AND isDeleted=0 ";
		if ($isactive != '')
			$sql .= " AND isActive = $isactive";

		// if ($schoolIds != '')
		$sql .= " AND schoolId IN ( $schoolIds ) ";

		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllSchoolsByschoolId()
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM schools where schoolId > 127 ";

		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetpersonnalSchools()
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT schools.* FROM `schoolpersonnelcoarcquestionmaster` LEFT JOIN schoolpersonnelcoarcquestiondetail ON schoolpersonnelcoarcquestiondetail.schoolPersonnelCoarcQuestionId = schoolpersonnelcoarcquestionmaster.schoolPersonnelCoarcQuestionId INNER JOIN schools ON schools.schoolId = schoolpersonnelcoarcquestionmaster.schoolId WHERE questionText = 'D. Supplies are sufficient for student to perform the required laboratory exercises (2.01)' AND schoolPersonnelCoarcQuestionDetailId is NULL AND sectionMasterId != 3 GROUP BY schoolId	;
		";

		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	// Get all Active blocked user Details
	function GetAllActiveBlockedUserdetails($roleTitle = '', $schoolId = 0)
	{
		$returnCounts = '';
		$objDB = new clsDB();

		$sql = "SELECT *
		FROM (
			SELECT (systemusermaster.systemUserMasterId) AS userId, 
				   CONCAT(systemusermaster.firstName, ' ', systemusermaster.lastName) AS name, 
				   systemusermaster.creadtedDate,
				   schools.schoolId, 
				   schools.displayName AS schoolName, 
				   systemusermaster.email, 
				   systemusermaster.phone,  
				   (systemuserrolemaster.title) AS roleTitle, '' AS rankTitle
			FROM systemusermaster 
			INNER JOIN schools ON schools.schoolId = systemusermaster.schoolId 
			LEFT JOIN systemuserrolemaster ON systemuserrolemaster.systemUserRoleMasterId = systemusermaster.systemUserRoleMasterId 
			WHERE systemusermaster.isActive = 1 
				AND systemusermaster.isBlocked = 1 
				AND schools.isActive = 1 
				AND schools.isDeleted = 0 
				AND schools.isSuperAdmin = 0
		
			UNION ALL
		
			SELECT (student.studentId) AS userId, 
				   CONCAT(student.firstName, ' ', student.lastName) AS name,
				   student.createdDate, 
				   schools.schoolId, 
				   schools.displayName AS schoolName, 
				   student.email, 
				   student.phone,  
				   'Student' AS roleTitle, rankmaster.title AS rankTitle
			FROM student 
			INNER JOIN schools ON schools.schoolId = student.schoolId 
			LEFT JOIN rankmaster on rankmaster.rankId = student.rankId
			WHERE schools.isActive = 1 
				AND student.isActive = 1 
				AND student.isBlocked = 1 
				AND schools.isDeleted = 0 
				AND schools.isSuperAdmin = 0 
				AND rankmaster.title != 'Graduate' AND rankmaster.title != 'Dropout'
			GROUP BY student.studentId
		
			UNION ALL
		
			SELECT (clinician.clinicianId)AS userId, 
				   CONCAT(clinician.firstName, ' ', clinician.lastName) AS name, 
				   clinician.createdDate,
				   schools.schoolId, 
				   schools.displayName AS schoolName, 
				   clinician.email, 
				   clinician.phone,  
				   'Clinician' AS roleTitle, clinicianrolemaster.title AS rankTitle
			FROM clinician 
			INNER JOIN schools ON schools.schoolId = clinician.schoolId
			LEFT JOIN clinicianrolemaster ON clinicianrolemaster.clinicianRoleId = clinician.clinicianRoleId
			WHERE schools.isActive = 1 
				AND clinician.isActive = 1 
				AND clinician.isBlocked = 1 
				AND schools.isDeleted = 0 
				AND schools.isSuperAdmin = 0
		) AS combined_data ";

		if ($roleTitle != '') {
			if ($roleTitle == 'Others') {
				$sql .= " WHERE roleTitle != 'D.C.E.' AND roleTitle != 'Student' AND roleTitle != 'Clinician' AND roleTitle != 'Preceptor' AND roleTitle != 'P.D.' AND roleTitle != 'Coordinator' AND roleTitle != 'Administrator' ";
			} else
				$sql .= " WHERE roleTitle = '" . $roleTitle . "'";
		}

		if ($schoolId) {
			if ($roleTitle != '')
				$sql .= " AND schoolId = " . $schoolId;
			else
				$sql .= "  WHERE schoolId = " . $schoolId;
		}
		// echo $sql;exit;
		$returnCounts = $objDB->GetResultSet($sql);
		unset($objDB);
		return $returnCounts;
	}

	// Get all Active blocked user Details
	function GetAllActiveUserdetails($roleTitle = '', $schoolId = 0)
	{
		$returnCounts = '';
		$objDB = new clsDB();

		$sql = "SELECT *
		FROM (
			SELECT (systemusermaster.systemUserMasterId) AS userId, 
				   CONCAT(systemusermaster.firstName, ' ', systemusermaster.lastName) AS name, 
				   schools.schoolId, 
				   schools.displayName AS schoolName, 
				   systemusermaster.email, 
				   systemusermaster.phone,  
				   (systemuserrolemaster.title) AS roleTitle 
			FROM systemusermaster 
			INNER JOIN schools ON schools.schoolId = systemusermaster.schoolId 
			LEFT JOIN systemuserrolemaster ON systemuserrolemaster.systemUserRoleMasterId = systemusermaster.systemUserRoleMasterId 
			WHERE systemusermaster.isActive = 1 
				AND systemusermaster.isBlocked = 0 
				AND schools.isActive = 1 
				AND schools.isDeleted = 0 
				AND schools.isSuperAdmin = 0
		
			UNION ALL
		
			SELECT (student.studentId) AS userId, 
				   CONCAT(student.firstName, ' ', student.lastName) AS name, 
				   schools.schoolId, 
				   schools.displayName AS schoolName, 
				   student.email, 
				   student.phone,  
				   'Student' AS roleTitle 
			FROM student 
			INNER JOIN schools ON schools.schoolId = student.schoolId 
			WHERE schools.isActive = 1 
				AND student.isActive = 1 
				AND student.isBlocked = 0
				AND schools.isDeleted = 0 
				AND schools.isSuperAdmin = 0 
			GROUP BY student.studentId
		
			UNION ALL
		
			SELECT (clinician.clinicianId)AS userId, 
				   CONCAT(clinician.firstName, ' ', clinician.lastName) AS name, 
				   schools.schoolId, 
				   schools.displayName AS schoolName, 
				   clinician.email, 
				   clinician.phone,  
				   'Clinician' AS roleTitle  
			FROM clinician 
			INNER JOIN schools ON schools.schoolId = clinician.schoolId 
			WHERE schools.isActive = 1 
				AND clinician.isActive = 1 
				AND clinician.isBlocked = 0
				AND schools.isDeleted = 0 
				AND schools.isSuperAdmin = 0
		) AS combined_data ";

		if ($roleTitle != '') {
			if ($roleTitle == 'Others') {
				$sql .= " WHERE roleTitle != 'D.C.E.' AND roleTitle != 'Student' AND roleTitle != 'Clinician' AND roleTitle != 'Preceptor' AND roleTitle != 'P.D.' AND roleTitle != 'Coordinator' AND roleTitle != 'Administrator' ";
			} else
				$sql .= " WHERE roleTitle = '" . $roleTitle . "'";
		}

		if ($schoolId) {
			if ($roleTitle != '')
				$sql .= " AND schoolId = " . $schoolId;
			else
				$sql .= "  WHERE schoolId = " . $schoolId;
		}
		// echo $sql;exit;
		$returnCounts = $objDB->GetResultSet($sql);
		unset($objDB);
		return $returnCounts;
	}
	function GetAllCheckoffTypeWiseSchools($checkoffType = '')
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM `schools` where schools.isActive = 1 AND schools.isDeleted=0 AND schools.isSuperAdmin=0 AND schools.isActiveCheckoffForStudent=1 ";

		if ($checkoffType != '')
			$sql .= " AND checkoffType = " . $checkoffType;

		$sql .= " ORDER BY displayName asc ";

		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllRotationTypeWiseSchools()
	{
		$rotationSchoolCount = '';
		$objDB = new clsDB();

		$sql = "SELECT GROUP_CONCAT(DISTINCT schoolId) AS allSchoolIds
		FROM (
			SELECT schools.schoolId
			FROM schools
			LEFT JOIN rotation ON schools.schoolId = rotation.schoolId
			WHERE parentRotationId > 0 
			AND isSchedule = 1 
			AND schools.scheduleActive = 1 
			AND schools.isActive = 1 
			AND schools.isDeleted = 0 
			AND schools.isSuperAdmin = 0
		
			UNION
		
			SELECT schools.schoolId
			FROM schools
			LEFT JOIN rotation ON schools.schoolId = rotation.schoolId
			WHERE parentRotationId > 0 
			AND isSchedule = 0 
			AND schools.scheduleActive = 0 
			AND schools.isActive = 1 
			AND schools.isDeleted = 0 
			AND schools.isSuperAdmin = 0
		) AS combined_school_ids;
		";

		$schoolIds = $objDB->GetSingleFieldValue($sql);

		if ($schoolIds != '') {
			$sql = "SELECT schools.* FROM `rotation`
			LEFT JOIN schools on schools.schoolId = rotation.schoolId
			WHERE rotation.schoolId NOT IN (" . $schoolIds . ") AND schools.isActive = 1 AND schools.isDeleted=0 AND  schools.isSuperAdmin=0 GROUP BY schools.schoolId ORDER BY displayName asc ";
		}
		$rotationSchoolCount = $objDB->GetResultSet($sql);
		// echo $sql;
		unset($objDB);
		return $rotationSchoolCount;
	}

	function GetAllRotationTypeWiseScheduleSchools($isSchedule = 0)
	{
		$returnCounts = '';
		$objDB = new clsDB();

		$sql = " SELECT schools.* FROM schools LEFT JOIN rotation ON schools.schoolId = rotation.schoolId WHERE `parentRotationId` > 0 AND `isSchedule` = $isSchedule AND schools.scheduleActive = $isSchedule AND schools.isActive = 1 AND schools.isDeleted=0 AND schools.isSuperAdmin=0 GROUP BY schools.schoolId ORDER BY displayName asc";

		$returnCounts = $objDB->GetResultSet($sql);
		// echo $sql;
		unset($objDB);
		return $returnCounts;
	}

	//Get All Graduate Student Count
	function GetActiveGraduateStudentCount()
	{
		$returnCounts = '';
		$objDB = new clsDB();

		$sql = "SELECT A.totalGraduateStudentCount, B.activeGraduateStudentCount  FROM 
		(SELECT COUNT(student.studentId) AS totalGraduateStudentCount 
			FROM student 
			INNER JOIN schools ON schools.schoolId = student.schoolId
			INNER JOIN rankmaster ON rankmaster.rankId = student.rankId 
			WHERE schools.isActive = 1  AND schools.isDeleted=0 AND  schools.isSuperAdmin=0 AND rankmaster.title = 'Graduate'
		) As A,
		(SELECT COUNT(student.studentId) AS activeGraduateStudentCount 
			FROM student 
			INNER JOIN schools ON schools.schoolId = student.schoolId
			INNER JOIN rankmaster ON rankmaster.rankId = student.rankId 
			WHERE schools.isActive = 1 AND student.isActive = 1 AND schools.isDeleted=0 AND  schools.isSuperAdmin=0 AND rankmaster.title = 'Graduate') As B";

		$returnCounts = $objDB->GetDataRow($sql);
		unset($objDB);
		return $returnCounts;
	}

	function GetAllGraduateStudent($active = 0, $schoolId = 0)
	{
		$returnCounts = '';
		$objDB = new clsDB();

		$sql = " SELECT student.studentId, student.firstName, student.lastName, student.email, student.phone, schools.title AS schoolname
				 FROM student 
	             INNER JOIN schools ON schools.schoolId = student.schoolId
	            INNER JOIN rankmaster ON rankmaster.rankId = student.rankId 
		         WHERE schools.isActive = 1  AND rankmaster.title = 'Graduate'";

		if ($active == 1) {
			$sql .= " AND student.isActive = 1";
		}

		if ($schoolId) {
			$sql .= " AND schools.schoolId =" . $schoolId;
		}

		$sql .= " ORDER BY student.firstName, student.lastName ASC";

		$returnCounts = $objDB->GetResultSet($sql);
		// echo $sql;
		unset($objDB);
		return $returnCounts;
	}
	function GetAllSelfUnlockedStudents($rankId = '', $schoolId = 0)
	{
		$result = '';
		$objDB = new clsDB();

		$sql = " SELECT student.studentId, CONCAT(student.firstName,' ', student.lastName) AS studentName, student.email,
		 rankmaster.title AS rankName, schools.schoolId, schools.title as schoolName, selfunlock.selfunlockdate

		FROM selfunlock 
		INNER JOIN student ON selfunlock.studentId = student.studentId
		INNER JOIN rankmaster ON student.rankId = rankmaster.rankId
		INNER JOIN schools ON schools.schoolId = student.schoolId";

		// if ($rankId != '') {
		// 	$sql .= " WHERE rankmaster.rankId = '" . $rankId . "'";
		// }

		if ($schoolId) {
			$sql .= "  WHERE schools.schoolId = " . $schoolId;
		}

		$sql .= " ORDER BY selfunlock.selfunlockdate DESC";

		$result = $objDB->GetResultSet($sql);
		// echo $sql;
		unset($objDB);
		return $result;
	}
	function UpdateSchoolAdditionalSettings($schoolId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($schoolId > 0) {
			$sql = "UPDATE schools SET 
					activitySheet = '" . ($this->activitysheet) . "',
					updatedBy = '" . ($this->createdBy) . "',
					updatedDate = '" . (date("Y-m-d h:i:s")) . "'
					Where schoolId= " . $schoolId;
			// echo $sql;exit;
			$result = $objDB->ExecuteQuery($sql);
		}

		return $result;
	}

	function GetCountryCode($countryId)
	{
		$objDB = new clsDB();
		$sql = "SELECT internationalDiallingCode FROM countrystatemaster WHERE countrystatemaster.countryStateMasterId =" . $countryId;
		//ECHO $sql;
		$returnContryCode = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $returnContryCode;
	}


	/**
	 * Retrieves School details for a given School ID and school ID.
	 *
	 * @param int $schoolId The ID of the school. If not provided, all schools will be considered.
	 *
	 * @return array An array containing the log data, row data, and additional data.
	 */
	function GetSchoolDetailsForLogs($schoolId){
		$objDB = new clsDB();

		$sql = "SELECT 
                s.*, 
                s.displayName AS schoolName,
                MAX(CASE WHEN ss.type = 'exportToPdf' THEN ss.status ELSE NULL END) AS exportToPdf,
                MAX(CASE WHEN ss.type = 'procedureCount' THEN ss.status ELSE NULL END) AS procedureCount,
                MAX(CASE WHEN ss.type = 'chat' THEN ss.status ELSE NULL END) AS chat,
                MAX(CASE WHEN ss.type = 'callOff' THEN ss.status ELSE NULL END) AS callOff,
                MAX(CASE WHEN ss.type = 'soapNote' THEN ss.status ELSE NULL END) AS soapNote
            FROM schools s
            LEFT JOIN schoolsettings ss ON ss.schoolId = s.schoolId
            WHERE s.schoolId = " . $schoolId . "
            GROUP BY s.schoolId";
        //  echo $sql; exit;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}

	/**
	 * Retrieves School details for a given School ID and school ID.
	 *
	 * @param int $schoolId The ID of the school. If not provided, all schools will be considered.
	 *
	 * @return array An array containing the log data, row data, and additional data.
	 */
	function GetSchoolAbsensSMSDetailsForLogs($schoolId)
	{
		$objDB = new clsDB();
		$sql = "SELECT schools.schoolId,schools.absenceSMSTime,schools.absenceSMSStatus,schools.absenceSMSSendTo,schools.adminRolesAbsenceSMSSendTo,schools.clinitionRoleAbsenceSMSSendTo,schools.displayName as schoolName,absensesmsdetails.* FROM  schools 
				LEFT JOIN absensesmsdetails ON absensesmsdetails.schoolId = schools.schoolId WHERE schools.schoolId =" . $schoolId;
		//echo $sql;exit;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}

	/**
	 * This function creates a log entry for a School action.
	 *
	 * @param int $id The ID of the School.
	 * @param string $action The action performed (Add, Edit, Delete, Signoff).
	 * @param int $userId The ID of the user performing the action.
	 * @param string $userType The type of user performing the action (Student, Preceptor, Admin).
	 *
	 * @return array An array containing the log data, row data, and additional data.
	 */
	function createSchoolLog($id, $action, $userId, $userType, $type)
	{

		// Instantiate the Logger class
		$objLog = new clsLogger();
		$objSchool = new clsSchool(); // Assuming `Attendance` class is used for `prepareAttendanceLogData`

		// Get user details based on user type
		$userDetails = getUserDetails($userId, $userType);


		$rowData = $objSchool->GetSchoolDetailsForLogs($id, $type);

		if ($rowData != '')
			$logData = $objLog->generateLogData($rowData, $userType);

		$additionalData = '';

		// Populate log data with user details
		$logData['userId'] = $userDetails['userId'];
		$logData['userName'] = $userDetails['userName'];
		$logMessage = '';

		$message = ' updated School details';
		if ($type != '' && $type == 'activitySheet') {
			$message = ' updated Activity Sheet Settings';
		} else if ($type != '' && $type == 'checkofftype') {
			$message = ' updated Checkoff Settings';
		} else if ($type != '' && $type == 'schedule') {
			$message = ' updated Schedule Settings';
		} else if ($type != '' && $type == 'selfunlock') {
			$message = ' updated Self Unlock Settings';
		} else if ($type != '' && $type == 'hospitalsite') {
			$message = ' updated Change Hospital Site Settings';
		} else if ($type != '' && $type == 'procedureCount') {
			$message = ' updated Procedure Count Settings';
		} else if ($type != '' && $type == 'exportToPdf') {
			$message = ' updated Export to PDF Settings';
		} else if ($type != '' && $type == 'sendCaseStudyToPreceptor') {
			$message = ' updated send CaseStudy To Preceptor Settings';
		} else if ($type != '' && $type == 'chat') {
			$message = ' updated chat Settings';
		} else if ($type != '' && $type == 'callOff') {
			$message = ' updated Call Off Settings';
		} else if ($type != '' && $type == 'soapNote') {
			$message = ' updated Soap Note Settings';
		} else if ($type != '' && $type == 'casestudySettings') {
			$message = ' updated Case Study Settings';
		} else if ($type == 'sendabsencesms') {
			$rowData = $objSchool->GetSchoolAbsensSMSDetailsForLogs($id);

			$message = ' updated Send Absence SMS Settings';
		}

		if ($action == 'Edit') {
			$logMessage = $logData['userName'] . $message;
		}

		// Construct log message
		$logData['message'] = $logMessage;

		// Return the data
		return [$logData, $rowData, $additionalData];
	}

	/**
	 * Saves audit log for School actions
	 *	
	 * @param int $id ID of the School master record
	 * @param int $userId User ID of the user performing the action
	 * @param string $userType Type of user (e.g. student, clinician, etc.)
	 * @param string $action Action type (Add, Edit, Delete, Signoff)
	 * @param int $isMobile Indicates if the action was performed from a mobile device (0 = false, 1 = true)
	 *
	 * @return bool Returns true if successful
	 */
	function saveSchoolAuditLog($id, $userId, $userType, $action, $isMobile = 0, $type = '')
	{

		// Instantiate the Logger and Checkoff classes
		$objLog = new clsLogger();
		$objSchool = new clsSchool();

		// Prepare log data
		[$logData, $rowData, $additionalData] = $objSchool->createSchoolLog($id, $action, $userId, $userType, $type);
		$logData['isMobile'] = $isMobile;

		$resource = 'School';
		if ($type == 'casestudySettings') {
			$resource = 'Case Study Settings';
		} else if ($type == 'sendabsencesms') {
			$resource = 'Send Absence SMS';
		} else if ($type != '') {
			$resource = 'Addtional Settings';
		}
		// Save the log details
		$objLog->saveLogs($logData, $action, $id, $resource, $rowData, $additionalData);

		// Clean up
		unset($objLog);
		unset($objSchool);

		return true; // Return success or handle further actions as needed
	}
}
