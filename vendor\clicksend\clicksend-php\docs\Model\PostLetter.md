# PostLetter

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**file_url** | **string** | URL of file to send | 
**priority_post** | **int** | Whether letter is priority | [optional] [default to 0]
**recipients** | [**\ClickSend\Model\PostRecipient[]**](PostRecipient.md) | Array of PostRecipient models | 
**template_used** | **int** | Whether using our template | [optional] [default to 0]
**duplex** | **int** | Whether letter is duplex | [optional] [default to 0]
**colour** | **int** | Whether letter is in colour | [optional] [default to 0]
**source** | **string** | Source being sent from | [optional] [default to 'sdk']

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)

