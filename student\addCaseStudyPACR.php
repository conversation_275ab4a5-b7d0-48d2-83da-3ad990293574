<?php
// Redirect from mobile
$IsMobile = isset($_GET['IsMobile']) ? ($_GET['IsMobile']) : 0;
include('../includes/config.php');
include('includes/validateUserLogin.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsRotation.php');
include('../class/clsCaseStudy.php');
include('../class/clsLocations.php');
include('../setRequest.php');
include('../class/clsStudent.php');
include('../class/clsHospitalSite.php');
include('../class/clsschoolclinicalsiteunit.php');

$schoolId = 0;
$schoolId = $currentSchoolId;
$hospitalSiteId = 0;
$page_title = "Add Case Study";
$bedCrumTitle = 'Add';
$caseStudyId = '';
$rotationId = 0;
$pacrCohort = '';
$pacrWeek = '';
$pacrAdmissionDate = '';
$pacrPtAge = '';
$pacrSex = '';
$pacrCC = '';
$pacrDx = '';
$pacrHx = '';
$pacrPhysicalExam = '';
$pacrBreathSounds = '';

$pacrTime1 = '';
$pacrHR1 = '';
$pacrRR1 = '';
$pacrBP1 = '';
$pacrTemp1 = '';
$pacrSpO21 = '';
$pacrFIO21 = '';

$pacrTime2 = '';
$pacrHR2 = '';
$pacrRR2 = '';
$pacrBP2 = '';
$pacrTemp2 = '';
$pacrSpO22 = '';
$pacrFIO22 = '';

$pacrTime3 = '';
$pacrHR3 = '';
$pacrRR3 = '';
$pacrBP3 = '';
$pacrTemp3 = '';
$pacrSpO23 = '';
$pacrFIO23 = '';


$pacrInerpret = '';
$pacrMapFormula = '';
$pacrMap1BP = '';
$pacrMap2BP = '';
$pacrMap3BP = '';
$pacrMap1BPNormal = 0;
$pacrMap2BPNormal = 0;
$pacrMap3BPNormal = 0;
$pacrMapElevated = '';
$pacrMapLow = '';
$pacrCBGDate = '';
$pacrNa = '';
$pacrK = '';
$pacrCl = '';
$pacrCa = '';
$pacrBun = '';
$pacrSputumCult = '';
$pacrGlucose = '';
$pacrPtt = '';
$pacrAlbumin = '';
$pacrSensitivity  = '';
$pacrAfb  = '';
$pacrCreatine  = '';
$pacrRbc  = '';
$pacrHb  = '';
$pacrHct = '';
$pacrWbc = '';
$pacrBaso = '';
$pacrLymph = '';
$pacrEosin = '';
$pacrBnp = '';
$pacrLactate = '';
$pacrTropinin = '';
$pacrIntake  = '';
$pacrTotalCO2 = '';
$pacrPh = '';
$pacrPaCO2 = '';
$pacrPaO2 = '';
$pacrHCO3 = '';
$pacrBE = '';
$pacrCoHb = '';
$pacrSaO2 = '';
$pacrFIO2 = '';
$pacrInterpretPatient = '';
$pacrInterpretABGStatus = '';
$pacrAaFormula = '';
$pacrAaNormalvalue = '';
$pacrAaIsNormal = '';
$pacrAaAbnormal = '';
$pacrCaO2 = '';
$pacrNormalCaO2 = '';
$pacrCaO2Isnormal = '';
$pacrCaO2Abnormal = '';
$pacrProcedures = '';
$pacrPFTList = '';
$pacrCXRList = '';
$pacrMedications = '';
$pacrClassifications = '';
$pacrModeOfctions = '';
$pacrDosage = '';
$pacrRespiratoryOrders = '';
$pacrIndications = '';
$pacrGoals = '';
$pacrRTOrder = '';
$pacrVentilator = '';
$pacrABGResults = '';
$pacrSvO2 = '';
$pacrModeOfctions = '';
$pacrModeOfctions = '';
$pacrModeOfctions = '';
$pacrModeOfctions = '';
$pacrModeOfctions = '';
$pacrModeOfctions = '';
$clinicianDate = '';
$pacrPastMedicalHx = '';
$pacrpH  = '';
$pacrPaCO2  = '';
$pacrHCO3  = '';
$pacrSvO2  = '';
$pacrCO = '';
$pacrPAP = '';
$pacrCVP = '';
$pacrICP = '';
$pacrPCWP = '';
$pacrSummery = '';

$visi1Check = '';
$visi2Check = '';
$visi3Check = '';
// $studentcomments  = 'Assessment: &#013;&#010;&#010;Plan of care:&#013;&#010;&#010;Critical Thinking Questions:&#013;&#010;&#010;';
/* 21042021 */
$studentcomments = '';
// $school_comments = '';
$clinician_comments = '';
$school_comments = '';

$pacrCBCCheckValues = '';
$hospitalSiteUnitId = 0;

/* 21042021 */

$caseStudydate = '';
$view = '';
$studentId = $_SESSION["loggedStudentId"];
$TimeZone = $_SESSION["loggedStudentSchoolTimeZone"];
// $row = $objCaseStudy->GetAdultCaseStudyForStudent($caseStudyId);

if (isset($_GET['rotationId'])) {
    $DefaultrotationId = $_GET['rotationId'];
    $rotationId = DecodeQueryData($DefaultrotationId);
}

if (isset($_GET['view'])) {
    $view = $_GET['view'];
}
$objStudent = new clsStudent();
$rowStudent = $objStudent->GetStudentDetails($studentId);
if ($rowStudent != '') {
    $firstName  = ($rowStudent['firstName']);
    $lastName  = ($rowStudent['lastName']);
    $fullName = $firstName . ' ' . $lastName;
}
unset($objStudent);
$objRotation = new clsRotation();
//$rotation=$objRotation->GetRotationByStudent($schoolId,$studentId);
//Get Active Rotations
$rotation = $objRotation->GetAllActiveRotationForDropdown($schoolId, $studentId);
if (isset($_GET['caseStudyId']) && DecodeQueryData($_GET['caseStudyId']) > 0) //Edit Mode
{
    //Get All Rotations
    if ($view == 'V') {
        $rotation = $objRotation->GetAllRotationForDropdown($schoolId, $studentId);
    }

    $caseStudyId = $_GET['caseStudyId'];
    $caseStudyId = DecodeQueryData($caseStudyId);
    $page_title = "Edit Case Study";
    $bedCrumTitle = 'Edit';
    // echo 'hi';
    $objCaseStudy = new clsCaseStudy();
    $rowMedications = $objCaseStudy->GetAdultMedicationsUseForStudent($caseStudyId);
    if ($rowMedications != '') {
        $aa = 0;
        while ($row1 = mysqli_fetch_assoc($rowMedications)) {
            // print_r($row);
            $shedules = [];

            $caseStudyAdultId = stripslashes($row1['caseStudyAdultId']);
            $adultMedicationsUse1 = stripslashes($row1['adultMedicationsUse1']);
            $adultMedicationsUse2 = stripslashes($row1['adultMedicationsUse2']);
            $adultMedicationsUse3 = stripslashes($row1['adultMedicationsUse3']);
            $adultMedicationsUse4 = stripslashes($row1['adultMedicationsUse4']);
            $adultMedicationsUse5 = stripslashes($row1['adultMedicationsUse5']);
            $adultMedicationsUse6 = stripslashes($row1['adultMedicationsUse6']);
            $adultMedicationsUse7 = stripslashes($row1['adultMedicationsUse7']);
            $adultMedicationsUse8 = stripslashes($row1['adultMedicationsUse8']);
            $adultMedicationsUse9 = stripslashes($row1['adultMedicationsUse9']);
            $adultMedicationsUse10 = stripslashes($row1['adultMedicationsUse10']);
            $adultMedicationsUse11 = stripslashes($row1['adultMedicationsUse11']);
            $adultMedicationsUse12 = stripslashes($row1['adultMedicationsUse12']);
            $adultMedicationsUse13 = stripslashes($row1['adultMedicationsUse13']);
            $adultMedicationsUse14 = stripslashes($row1['adultMedicationsUse14']);
            $adultMedicationsUse15 = stripslashes($row1['adultMedicationsUse15']);

            $adultModificationCarePlan1 = stripslashes($row1['adultModificationCarePlan1']);
            $adultModificationCarePlan2 = stripslashes($row1['adultModificationCarePlan2']);
            $adultModificationCarePlan3 = stripslashes($row1['adultModificationCarePlan3']);
            $adultModificationCarePlan4 = stripslashes($row1['adultModificationCarePlan4']);
            $adultModificationCarePlan5 = stripslashes($row1['adultModificationCarePlan5']);
            $adultModificationCarePlan6 = stripslashes($row1['adultModificationCarePlan6']);
            $adultModificationCarePlan7 = stripslashes($row1['adultModificationCarePlan7']);
            $adultModificationCarePlan8 = stripslashes($row1['adultModificationCarePlan8']);
            $adultModificationCarePlan9 = stripslashes($row1['adultModificationCarePlan9']);
            $adultModificationCarePlan10 = stripslashes($row1['adultModificationCarePlan10']);
            $adultModificationCarePlan11 = stripslashes($row1['adultModificationCarePlan11']);
            $adultModificationCarePlan12 = stripslashes($row1['adultModificationCarePlan12']);
            $adultModificationCarePlan13 = stripslashes($row1['adultModificationCarePlan13']);
            $adultModificationCarePlan14 = stripslashes($row1['adultModificationCarePlan14']);
            $adultModificationCarePlan15 = stripslashes($row1['adultModificationCarePlan15']);
            $shedules = [];
            $j = 0;
            $adultMedicationsUseArray = [];
            $adultModificationCarePlanArray = [];
            $adultMedicationsUseArray[$j = $j + 1][] = $adultMedicationsUse1;
            $adultModificationCarePlanArray[$j][] = $adultModificationCarePlan1;
            $adultMedicationsUseArray[$j = $j + 1][] = $adultMedicationsUse2;
            $adultModificationCarePlanArray[$j][] = $adultModificationCarePlan2;
            $adultMedicationsUseArray[$j = $j + 1][] = $adultMedicationsUse3;
            $adultModificationCarePlanArray[$j][] = $adultModificationCarePlan3;
            $adultMedicationsUseArray[$j = $j + 1][] = $adultMedicationsUse4;
            $adultModificationCarePlanArray[$j][] = $adultModificationCarePlan4;
            $adultMedicationsUseArray[$j = $j + 1][] = $adultMedicationsUse5;
            $adultModificationCarePlanArray[$j][] = $adultModificationCarePlan5;


            if ($adultMedicationsUse6  || $adultModificationCarePlan6) {
                $adultMedicationsUseArray[$j = $j + 1] = $adultMedicationsUse6;
                $adultModificationCarePlanArray[$j] = $adultModificationCarePlan6;
            }
            if ($adultMedicationsUse7  || $adultModificationCarePlan7) {
                $adultMedicationsUseArray[$j = $j + 1][] = $adultMedicationsUse7;
                $adultModificationCarePlanArray[$j][] = $adultModificationCarePlan7;
            }
            if ($adultMedicationsUse8  || $adultModificationCarePlan8) {
                $adultMedicationsUseArray[$j = $j + 1][] = $adultMedicationsUse8;
                $adultModificationCarePlanArray[$j][] = $adultModificationCarePlan8;
            }
            if ($adultMedicationsUse9  || $adultModificationCarePlan9) {
                $adultMedicationsUseArray[$j = $j + 1][] = $adultMedicationsUse9;
                $adultModificationCarePlanArray[$j][] = $adultModificationCarePlan9;
            }
            if ($adultMedicationsUse10 || $adultModificationCarePlan10) {
                $adultMedicationsUseArray[$j = $j + 1][] = $adultMedicationsUse10;
                $adultModificationCarePlanArray[$j][] = $adultModificationCarePlan10;
            }
            if ($adultMedicationsUse11 || $adultModificationCarePlan11) {
                $adultMedicationsUseArray[$j = $j + 1][] = $adultMedicationsUse11;
                $adultModificationCarePlanArray[$j][] = $adultModificationCarePlan11;
            }
            if ($adultMedicationsUse12 || $adultModificationCarePlan12) {
                $adultMedicationsUseArray[$j = $j + 1][] = $adultMedicationsUse12;
                $adultModificationCarePlanArray[$j][] = $adultModificationCarePlan12;
            }
            if ($adultMedicationsUse13 || $adultModificationCarePlan13) {
                $adultMedicationsUseArray[$j = $j + 1][] = $adultMedicationsUse13;
                $adultModificationCarePlanArray[$j][] = $adultModificationCarePlan13;
            }
            if ($adultMedicationsUse14 || $adultModificationCarePlan14) {
                $adultMedicationsUseArray[$j = $j + 1][] = $adultMedicationsUse14;
                $adultModificationCarePlanArray[$j][] = $adultModificationCarePlan14;
            }
            if ($adultMedicationsUse15 || $adultModificationCarePlan15) {
                $adultMedicationsUseArray[$j = $j + 1][] = $adultMedicationsUse15;
                $adultModificationCarePlanArray[$j][] = $adultModificationCarePlan15;
            }



            // $adultMedicationsUses[] = $adultMedicationsUseArray;
            // $adultModificationCarePlan[] = $adultModificationCarePlanArray;
            // print_r($adultMedicationsUses);
            $adultMedicationsUseList = json_encode($adultMedicationsUseArray);
            $adultModificationCarePlanList = json_encode($adultModificationCarePlanArray);
            // 	$objDB = new clsDB();

            // $resultadultMedicationsUses = $objDB->UpdateSingleColumnValueToTable('casestudyadult', 'adultMedicationsUseList',$adultMedicationsUseList,'caseStudyAdultId',$caseStudyAdultId);
            // $resultadultModificationCarePlan = $objDB->UpdateSingleColumnValueToTable('casestudyadult', 'adultModificationCarePlanList',$adultModificationCarePlanList,'caseStudyAdultId',$caseStudyAdultId);
            // unset($objDB);
            $aa++;
            // echo '<pre>';
            // print_r($shedules);

            // exit;
        }
    }
    $row = $objCaseStudy->GetPACRCaseStudyForStudent($caseStudyId);
    //     echo '<pre>';
    //     print_r($row);
    // exit;
    unset($objCaseStudy);
    if ($row == '') {
        header('location:caseStudyList.html');
    }
    $courselocationId = $row['locationId'];
    $parentRotationId = stripslashes($row['parentRotationId']);
    $rotationLocationId = stripslashes($row['rotationLocationId']);

    $locationId = 0;
    if ($rotationLocationId > 0) {
        if ($rotationLocationId != $courselocationId && $parentRotationId > 0) {
            if ($parentRotationId > 0) {
                if (!$rotationLocationId)
                    $locationId = $objRotation->GetLocationByRotation($rotationId);
                else
                    $locationId  = $rotationLocationId;
            }
        } else {
            $locationId  = $courselocationId;
        }
    }
    //Get Time Zone By Rotation 
    $objLocation = new clsLocations();
    $TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
    unset($objLocation);
    if ($TimeZone == '')
        $TimeZone = $_SESSION["loggedStudentSchoolTimeZone"];

    $rotationId  = stripslashes($row['rotationId']);
    $schoolDate  = ($row['schoolDate']);
    if ($schoolDate) {
        $schoolDate = converFromServerTimeZone($schoolDate, $TimeZone);
        $schoolDate = date('m/d/Y', strtotime($schoolDate));
    }
    $caseStudydate  = ($row['caseStudydate']);
    if ($caseStudydate) {
        $caseStudydate = converFromServerTimeZone($caseStudydate, $TimeZone);
        $caseStudydate = date('m/d/Y', strtotime($caseStudydate));
    }
    $clinicianDate  = stripslashes($row['ClinicianDate']);
    if ($clinicianDate) {
        $clinicianDate = converFromServerTimeZone($clinicianDate, $TimeZone);
        $clinicianDate = date('m/d/Y', strtotime($clinicianDate));
    }

    // $schoolId  = stripslashes($row['currentSchoolId']);
    // $studentId = stripslashes($row['studentId']);
    // $rotationId = stripslashes($row['rotationId']);
    $hospitalSiteId = stripslashes($row['hospitalSiteId']);
    $hospitalSiteUnitId = stripslashes($row['hospitalSiteUnitId']);
    $type = stripslashes($row['type']);
    $pacrCohort = stripslashes($row['pacrCohort']);
    $pacrWeek = stripslashes($row['pacrWeek']);
    $pacrAdmissionDate = stripslashes($row['pacrAdmissionDate']);
    $pacrPtAge = stripslashes($row['pacrPtAge']);
    $pacrSex = stripslashes($row['pacrSex']);
    $pacrCC = stripslashes($row['pacrCC']);
    $pacrDx = stripslashes($row['pacrDx']);
    $pacrHx = stripslashes($row['pacrHx']);
    $pacrPhysicalExam = stripslashes($row['pacrPhysicalExam']);
    $pacrBreathSounds = stripslashes($row['pacrBreathSounds']);
    $pacrVitals = stripslashes($row['pacrVitals']);
    $pacrCBCDetails = stripslashes($row['pacrCBCDetails']);
    $pacrCBCCheckValues = stripslashes($row['pacrCBCCheckValues']);
    $pacrInterpretPatient = stripslashes($row['pacrInterpretPatient']);
    $pacrInterpretABGStatus = stripslashes($row['pacrInterpretABGStatus']);
    $pacrAaFormula = stripslashes($row['pacrAaFormula']);

    $pacrAaNormalvalue = stripslashes($row['pacrAaNormalvalue']);
    $pacrAaIsNormal = stripslashes($row['pacrAaIsNormal']);
    $pacrAaAbnormal = stripslashes($row['pacrAaAbnormal']);
    $pacrCaO2 = stripslashes($row['pacrCaO2']);
    $pacrNormalCaO2 = stripslashes($row['pacrNormalCaO2']);
    $pacrCaO2Isnormal = stripslashes($row['pacrCaO2Isnormal']);
    $pacrCaO2Abnormal = stripslashes($row['pacrCaO2Abnormal']);
    $pacrProcedures = stripslashes($row['pacrProcedures']);
    $pacrPFTList = stripslashes($row['pacrPFTList']);
    $pacrCXRList = stripslashes($row['pacrCXRList']);
    $pacrMedications = stripslashes($row['pacrMedications']);
    $pacrClassifications = stripslashes($row['pacrClassifications']);
    $pacrModeOfctions = stripslashes($row['pacrModeOfctions']);

    $pacrDosage = stripslashes($row['pacrDosage']);
    $pacrRespiratoryOrders = stripslashes($row['pacrRespiratoryOrders']);
    $pacrIndications = stripslashes($row['pacrIndications']);
    $pacrGoals = stripslashes($row['pacrGoals']);


    $pacrRTOrder = stripslashes($row['pacrRTOrder']);
    $pacrVentilator = stripslashes($row['pacrVentilator']);
    $pacrABGResults = stripslashes($row['pacrABGResults']);
    $pacrHemodynamicsList = stripslashes($row['pacrHemodynamicsList']);
    $pacrHemodynamicCheckvalues = stripslashes($row['pacrHemodynamicCheckvalues']);
    $pacrSummery = stripslashes($row['pacrSummery']);
    $studentcomments = stripslashes($row['studentcomments']);
    $createdBy = stripslashes($row['studentId']);

    //Vitals list

    $pacrVitalList = json_decode($pacrVitals, true);
    $pacrTime1 = isset($pacrVitalList['pacrTime1']) ? $pacrVitalList['pacrTime1'] : '';
    $pacrTime2 = isset($pacrVitalList['pacrTime2']) ? $pacrVitalList['pacrTime2'] : '';
    $pacrTime3 = isset($pacrVitalList['pacrTime3']) ? $pacrVitalList['pacrTime3'] : '';
    if ($pacrTime1) {
        $pacrTime1 = date('m/d/Y h:i A', strtotime($pacrTime1));
    }
    if ($pacrTime2) {
        $pacrTime2 = date('m/d/Y h:i A', strtotime($pacrTime2));
    }
    if ($pacrTime3) {
        $pacrTime3 = date('m/d/Y h:i A', strtotime($pacrTime3));
    }
    $pacrHR1 = isset($pacrVitalList['pacrHR1']) ? $pacrVitalList['pacrHR1'] : '';
    $pacrHR2 = isset($pacrVitalList['pacrHR2']) ? $pacrVitalList['pacrHR2'] : '';
    $pacrHR3 = isset($pacrVitalList['pacrHR3']) ? $pacrVitalList['pacrHR3'] : '';
    $pacrBP1 = isset($pacrVitalList['pacrBP1']) ? $pacrVitalList['pacrBP1'] : '';
    $pacrBP2 = isset($pacrVitalList['pacrBP2']) ? $pacrVitalList['pacrBP2'] : '';
    $pacrBP3 = isset($pacrVitalList['pacrBP3']) ? $pacrVitalList['pacrBP3'] : '';
    $pacrRR1 = isset($pacrVitalList['pacrRR1']) ? $pacrVitalList['pacrRR1'] : '';
    $pacrRR2 = isset($pacrVitalList['pacrRR2']) ? $pacrVitalList['pacrRR2'] : '';
    $pacrRR3 = isset($pacrVitalList['pacrRR3']) ? $pacrVitalList['pacrRR3'] : '';
    $pacrTemp1 = isset($pacrVitalList['pacrTemp1']) ? $pacrVitalList['pacrTemp1'] : '';
    $pacrTemp2 = isset($pacrVitalList['pacrTemp2']) ? $pacrVitalList['pacrTemp2'] : '';
    $pacrTemp3 = isset($pacrVitalList['pacrTemp3']) ? $pacrVitalList['pacrTemp3'] : '';
    $pacrSpO21 = isset($pacrVitalList['pacrSpO21']) ? $pacrVitalList['pacrSpO21'] : '';
    $pacrSpO22 = isset($pacrVitalList['pacrSpO22']) ? $pacrVitalList['pacrSpO22'] : '';
    $pacrSpO23 = isset($pacrVitalList['pacrSpO23']) ? $pacrVitalList['pacrSpO23'] : '';
    $pacrFIO21 = isset($pacrVitalList['pacrFIO21']) ? $pacrVitalList['pacrFIO21'] : '';
    $pacrFIO22 = isset($pacrVitalList['pacrFIO22']) ? $pacrVitalList['pacrFIO22'] : '';
    $pacrFIO23 = isset($pacrVitalList['pacrFIO23']) ? $pacrVitalList['pacrFIO23'] : '';
    $pacrInerpret = isset($pacrVitalList['pacrInerpret']) ? $pacrVitalList['pacrInerpret'] : '';
    $pacrMapFormula = isset($pacrVitalList['pacrMapFormula']) ? $pacrVitalList['pacrMapFormula'] : '';
    $pacrMap1BP = isset($pacrVitalList['pacrMap1BP']) ? $pacrVitalList['pacrMap1BP'] : '';
    $pacrMap2BP = isset($pacrVitalList['pacrMap2BP']) ? $pacrVitalList['pacrMap2BP'] : '';
    $pacrMap3BP = isset($pacrVitalList['pacrMap3BP']) ? $pacrVitalList['pacrMap3BP'] : '';
    $visi1Check = isset($pacrVitalList['visi1Check']) ? $pacrVitalList['visi1Check'] : '';
    $visi2Check = isset($pacrVitalList['visi2Check']) ? $pacrVitalList['visi2Check'] : '';
    $visi3Check = isset($pacrVitalList['visi3Check']) ? $pacrVitalList['visi3Check'] : '';
    $pacrMapElevated = isset($pacrVitalList['pacrMapElevated']) ? $pacrVitalList['pacrMapElevated'] : '';
    $pacrMapLow = isset($pacrVitalList['pacrMapLow']) ? $pacrVitalList['pacrMapLow'] : '';

    // CBG list

    $pacrCBCDetailsList = json_decode($pacrCBCDetails, true);
    $pacrCBGDate = isset($pacrCBCDetailsList['pacrCBGDate']) ? $pacrCBCDetailsList['pacrCBGDate'] : '';
    $pacrNa = isset($pacrCBCDetailsList['pacrNa']) ? $pacrCBCDetailsList['pacrNa'] : '';
    $pacrK = isset($pacrCBCDetailsList['pacrK']) ? $pacrCBCDetailsList['pacrK'] : '';
    $pacrCl = isset($pacrCBCDetailsList['pacrCl']) ? $pacrCBCDetailsList['pacrCl'] : '';
    $pacrCa = isset($pacrCBCDetailsList['pacrCa']) ? $pacrCBCDetailsList['pacrCa'] : '';
    $pacrBun = isset($pacrCBCDetailsList['pacrBun']) ? $pacrCBCDetailsList['pacrBun'] : '';
    $pacrGlucose = isset($pacrCBCDetailsList['pacrGlucose']) ? $pacrCBCDetailsList['pacrGlucose'] : '';
    $pacrPtt = isset($pacrCBCDetailsList['pacrPtt']) ? $pacrCBCDetailsList['pacrPtt'] : '';
    $pacrAlbumin = isset($pacrCBCDetailsList['pacrAlbumin']) ? $pacrCBCDetailsList['pacrAlbumin'] : '';
    $pacrSensitivity = isset($pacrCBCDetailsList['pacrSensitivity']) ? $pacrCBCDetailsList['pacrSensitivity'] : '';
    $pacrAfb = isset($pacrCBCDetailsList['pacrAfb']) ? $pacrCBCDetailsList['pacrAfb'] : '';
    $pacrCreatine = isset($pacrCBCDetailsList['pacrCreatine']) ? $pacrCBCDetailsList['pacrCreatine'] : '';

    $pacrRbc = isset($pacrCBCDetailsList['pacrRbc']) ? $pacrCBCDetailsList['pacrRbc'] : '';
    $pacrHb = isset($pacrCBCDetailsList['pacrHb']) ? $pacrCBCDetailsList['pacrHb'] : '';
    $pacrHct = isset($pacrCBCDetailsList['pacrHct']) ? $pacrCBCDetailsList['pacrHct'] : '';
    $pacrWbc = isset($pacrCBCDetailsList['pacrWbc']) ? $pacrCBCDetailsList['pacrWbc'] : '';
    $pacrBaso = isset($pacrCBCDetailsList['pacrBaso']) ? $pacrCBCDetailsList['pacrBaso'] : '';
    $pacrLymph = isset($pacrCBCDetailsList['pacrLymph']) ? $pacrCBCDetailsList['pacrLymph'] : '';
    $pacrEosin = isset($pacrCBCDetailsList['pacrEosin']) ? $pacrCBCDetailsList['pacrEosin'] : '';
    $pacrBnp = isset($pacrCBCDetailsList['pacrBnp']) ? $pacrCBCDetailsList['pacrBnp'] : '';
    $pacrTropinin = isset($pacrCBCDetailsList['pacrTropinin']) ? $pacrCBCDetailsList['pacrTropinin'] : '';
    $pacrLactate = isset($pacrCBCDetailsList['pacrLactate']) ? $pacrCBCDetailsList['pacrLactate'] : '';

    $pacrIntake = isset($pacrCBCDetailsList['pacrIntake']) ? $pacrCBCDetailsList['pacrIntake'] : '';
    $pacrTotalCO2 = isset($pacrCBCDetailsList['pacrTotalCO2']) ? $pacrCBCDetailsList['pacrTotalCO2'] : '';
    $pacrPh = isset($pacrCBCDetailsList['pacrPh']) ? $pacrCBCDetailsList['pacrPh'] : '';
    $pacrPaCO2 = isset($pacrCBCDetailsList['pacrPaCO2']) ? $pacrCBCDetailsList['pacrPaCO2'] : '';
    $pacrPaO2 = isset($pacrCBCDetailsList['pacrPaO2']) ? $pacrCBCDetailsList['pacrPaO2'] : '';
    $pacrHCO3 = isset($pacrCBCDetailsList['pacrHCO3']) ? $pacrCBCDetailsList['pacrHCO3'] : '';
    $pacrBE = isset($pacrCBCDetailsList['pacrBE']) ? $pacrCBCDetailsList['pacrBE'] : '';
    $pacrCoHb = isset($pacrCBCDetailsList['pacrCoHb']) ? $pacrCBCDetailsList['pacrCoHb'] : '';
    $pacrSaO2 = isset($pacrCBCDetailsList['pacrSaO2']) ? $pacrCBCDetailsList['pacrSaO2'] : '';
    $pacrFIO2 = isset($pacrCBCDetailsList['pacrFIO2']) ? $pacrCBCDetailsList['pacrFIO2'] : '';

    // Hemodynamics List
    $pacrHemodynamicsList = json_decode($pacrHemodynamicsList, true);
    $pacrSvO2 = isset($pacrHemodynamicsList['pacrSvO2']) ? $pacrHemodynamicsList['pacrSvO2'] : '';
    $pacrCVP = isset($pacrHemodynamicsList['pacrCVP']) ? $pacrHemodynamicsList['pacrCVP'] : '';
    $pacrCO = isset($pacrHemodynamicsList['pacrCO']) ? $pacrHemodynamicsList['pacrCO'] : '';
    $pacrPCWP = isset($pacrHemodynamicsList['pacrPCWP']) ? $pacrHemodynamicsList['pacrPCWP'] : '';
    $pacrPAP = isset($pacrHemodynamicsList['pacrPAP']) ? $pacrHemodynamicsList['pacrPAP'] : '';
    $pacrICP = isset($pacrHemodynamicsList['pacrICP']) ? $pacrHemodynamicsList['pacrICP'] : '';

    $studentcomments  = ($row['studentcomments']);

    /* 21042021 */
    $clinician_comments = stripslashes($row['clinician_comments']);
    // if ($clinician_comments != '') {
    //     $clinician_comments = 'Yes';
    // } else {
    //     $clinician_comments = 'No';
    // }
    $school_comments  = stripslashes($row['school_comments']);
    // if ($school_comments != '') {
    //     $school_comments = 'Yes';
    // } else {
    //     $school_comments = 'No';
    // }
    /* 21042021 */


    // if($gestationalAOB)
    // {
    // 	$gestationalAOB = converFromServerTimeZone($gestationalAOB,$TimeZone);		
    // 	$gestationalAOB = date('m/d/Y', strtotime($gestationalAOB));
    // }
    // $studentDOB  = ($row['studentDOB']);
    // if ($studentDOB) {
    //     $studentDOB = converFromServerTimeZone($studentDOB, $TimeZone);
    //     $studentDOB = date('m/d/Y', strtotime($studentDOB));
    // }

}

$readonly = ($caseStudydate != '') ? 'readonly' : '';

//For Hospital Site
$objHospitalSite = new clsHospitalSite();
$Hospitals = $objHospitalSite->GetAllHospitalSite($schoolId);
unset($objHospitalSite);

//For Clinician Reviews
$objCaseStudy = new clsCaseStudy();
$rsClinicianReviews = $objCaseStudy->GetClinicianReviews($caseStudyId);
$rsClinicianReviewsCount = ($rsClinicianReviews != '') ? mysqli_num_rows($rsClinicianReviews) : 0;
unset($objCaseStudy);

//Get hospital Site Unit
$objhospitalsiteunit = new clsschoolclinicalsiteunit;
$hospitalsiteunit = $objhospitalsiteunit->GetAllClinicalSiteUnit($schoolId);
unset($objhospitalsiteunit);
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content
        must come *after* these tags -->
    <title>
        <?php echo ($page_title); ?>
    </title>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/stepper.css">
    <?php include('includes/headercss.php'); ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
    <!-- <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css" integrity="sha512-MV7K8+y+gLIBoVD59lQIYicR65iaqukzvf/nwasF0nqhPay5w/9lJmVM2hMDcnK1OnMGCdVK+iQrJ7lzPJQd1w==" crossorigin="anonymous" referrerpolicy="no-referrer" /> -->
    <script type="module" src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.esm.js"></script>
    <script nomodule src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.js"></script>
    <style>
        .d-flex {
            display: flex;
        }

        .margin-right {
            margin-right: 7px;
        }

        .checkboxStyle {
            margin: 0 !important;
            width: 50%;
            border: 1px solid #ccc;
        }

        .alignCenter {
            align-items: center !important;
        }

        .abnormal {
            color: red !important;
        }

        .input-group-addon {
            position: absolute;
            right: 7px;
            /* width: 100%; */
            z-index: 99;
            width: 35px;
            margin: auto;
            top: 5px;
            border-left: 1px solid #ccc;
            border-radius: 50% !important;
            padding: 10px -2px;
            height: 35px;
            /* background: #01A750; */
            /* color: #fff; */
            color: #555;
            background: #f6f9f9;
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .panel-default>.panel-heading {
            background-color: transparent !important;
        }

        .btn-success,
        .btn-default {
            padding: 8px 25px;
            border-radius: 10px;
        }

        .panel {
            border-radius: 14px !important;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 45px !important;
        }

        .required-select2 {
            border-left: solid 3px red !important;
            border-radius: 12px !important;
        }

        .select2-container--default .select2-selection--single {
            background-color: #f6f9f9 !important;
            cursor: default !important;
            height: 45px !important;
            border-radius: 10px !important;
        }

        .select2-container--default .select2-selection--single {
            border: none !important;
        }

        .panel,
        .form-group {
            margin-bottom: 10px;
        }

        .bootstrap-datetimepicker-widget {
            border-radius: 12px !important;
        }

        .form-control {
            height: 45px;
        }

        textarea.form-control {
            height: auto;
        }

        .pl-0 {
            padding-left: 0;
        }

        .blood-pressure-input {
            width: 80px;
            display: inline-block;
            margin: 5px 10px;
        }

        .abnormal-value-checkbox {
            padding: 0;
            width: 37px;
            height: 37px;
            margin-left: 10px;
        }

        .row-delete-icon {
            font-size: 18px;
            padding-left: 6px;
        }

        .mobile-block {
            display: flex;
            align-items: center;
        }

        /* .form-horizontal .form-group {
            margin-right: 0;
            margin-left: 0;
        } */
        .fa-chevron-down {
            font-size: 13px;
            font-weight: 500;
            color: #000000bd;
        }

        .icon-inner,
        .ionicon,
        svg {
            color: #01A750;
        }

        @media screen and (max-width: 500px) {
            .panel-body ol {
                padding-left: 20px;
            }

            .panel-default>.panel-heading {
                padding-left: 5px;
            }

            .blood-pressure-input {
                width: 100%;
                margin: 5px 0;
            }

            /* .container-zero{
            padding: 0;
         } */

            .mobile-gray-border {
                border: 1px solid #ececec;
            }

            .collapsible {
                cursor: pointer;
                padding: 15px;
                /* border: 1px solid #181818; */
                /* background-color: #f9f9f9; */
                display: flex;
                justify-content: space-between;
                align-items: center;
                /* border-radius: 14px; */
            }

            .collapsible p {
                margin: 0;
            }

            .collapsible-arrow {
                font-size: 18px;
                transition: transform 0.3s ease;
            }

            .content {
                display: none;
                padding: 10px 15px;
                /* border-top: 1px solid #ececec; */
            }

            .content.active {
                display: block;
            }

            .active.collapsible-arrow {
                transform: rotate(180deg);
            }

            .row-delete-icon {
                position: absolute;
                top: -192px;
                right: 20px;
            }

            .mobile-block {
                display: block;
            }

            .desktop-hide {
                display: flex !important;
            }

            .mobile-pl-0 {
                padding-left: 0;
            }

            input[type="radio"],
            input[type="checkbox"] {
                margin-left: 15px;
                width: 18px !important;
            }

            .mobile-flex-col {
                flex-direction: column;
            }
        }
    </style>
</head>

<body>
    <?php if ($IsMobile == 0) { ?>

        <?php include('includes/header.php'); ?>
        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
                        <li>
                            <a href="dashboard.html">Home</a>
                        </li>
                        <li>
                            <a href="caseStudyList.html?active=PACR">Case Study</a>
                        </li>
                        <li>
                            <a href="caseStudyList.html?active=PACR">Patient Assessment/Chart Review </a>
                        </li>
                        <li class="active">
                            <?php echo ($bedCrumTitle); ?>
                        </li>
                    </ol>
                </div>
            </div>
        </div>
    <?php  } else  ?>


    <div class="container mb-15 mobile-padding-4">


        <!-- stepper start -->
        <div>
            <div id="multi-step-form-container">
                <!-- Form Steps / Progress Bar -->
                <ul class="form-stepper form-stepper-horizontal text-center mx-auto">
                    <!-- Step 1 -->
                    <li class="form-stepper-active text-center form-stepper-list" style="" step="1">
                        <a class="mx-2">
                            <span class="form-stepper-circle">
                                <span>1</span>
                            </span>
                            <div class="label stepper-label" style="">Details</div>
                        </a>
                    </li>
                    <!-- Step 2 -->
                    <li class="form-stepper-unfinished text-center form-stepper-list" step="2">
                        <a class="mx-2">
                            <span class="form-stepper-circle text-muted">
                                <span>2</span>
                            </span>
                            <div class="label text-muted stepper-label">Demographics</div>
                        </a>
                    </li>
                    <!-- Step 3 -->
                    <li class="form-stepper-unfinished text-center form-stepper-list" step="3">
                        <a class="mx-2">
                            <span class="form-stepper-circle text-muted">
                                <span>3</span>
                            </span>
                            <div class="label text-muted stepper-label">Vitals</div>
                        </a>
                    </li>
                    <!-- Step 4 -->
                    <li class="form-stepper-unfinished text-center form-stepper-list" step="4">
                        <a class="mx-2">
                            <span class="form-stepper-circle text-muted">
                                <span>4</span>
                            </span>
                            <div class="label text-muted stepper-label">CBC, Chemistry, & ABG</div>
                        </a>
                    </li>
                    <!-- Step 5 -->
                    <li class="form-stepper-unfinished text-center form-stepper-list" step="5">
                        <a class="mx-2">
                            <span class="form-stepper-circle text-muted">
                                <span>5</span>
                            </span>
                            <div class="label text-muted stepper-label">Medications</div>
                        </a>
                    </li>
                    <!-- Step 6 -->
                    <li class="form-stepper-unfinished text-center form-stepper-list" step="6">
                        <a class="mx-2">
                            <span class="form-stepper-circle text-muted">
                                <span>6</span>
                            </span>
                            <div class="label text-muted stepper-label">Comments</div>
                        </a>
                    </li>
                </ul>
                <!-- Step Wise Form Content -->
                <form id="formCaseStudy" data-parsley-validate="data-parsley-validate" class="form-horizontal">

                    <!-- Mobile redirect -->
                    <input type="hidden" name="IsMobile" id="IsMobile" value="<?php echo $IsMobile; ?>">
                    <input type="hidden" name="hiddenCaseStudyId" id="hiddenCaseStudyId" value="<?php echo $caseStudyId; ?>">
                    <input type="hidden" name="hiddenStudentId" id="hiddenStudentId" value="<?php echo $studentId; ?>">
                    <input type="hidden" name="hiddenType" id="hiddenType" value="PACR">
                    <input type="hidden" name="hiddenView" id="hiddenView" value="<?php echo $view; ?>">

                    <!-- Step 1 Content -->
                    <section id="step-1" class="form-step" data-parsley-validate>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">

                                    <label class="col-md-12 control-label" for="studentName">Student Name </label>
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <input type="text" class="form-control input-md required-input" value="<?php echo $fullName; ?>" name="studentName" id="studentName" readonly>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-md-12 control-label" for="caseStudydate"> Date</label>
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <div class='input-group date w-full' for='caseStudydate' style="position: relative;">
                                            <input type='text' name="caseStudydate" id="caseStudydate" class="form-control input-md dateInputFormat required-input" value="<?php echo $caseStudydate; ?>" data-parsley-errors-container="#error-txtDate" placeholder="MM-DD-YYYY" required />
                                            <span class="input-group-addon calender-icon">
                                                <span class="glyphicon glyphicon-calendar"></span>
                                            </span>
                                        </div>
                                        <div id="error-txtDate" bis_skin_checked="1"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-md-12 control-label" for="gestationalAOB">Rotation</label>
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <select id="cborotation" name="cborotation" class="form-control input-md  select2_single required-input" data-parsley-errors-container="#error-txtRotation" required>
                                            <option value="" selected="selected">Select</option>
                                            <?php
                                            if ($rotation != "") {
                                                while ($row = mysqli_fetch_assoc($rotation)) {
                                                    $selrotationId  = $row['rotationId'];
                                                    $name  = stripslashes($row['title']);
                                                    $isHospitalSite  = stripslashes($row['hospitalSiteId']);
                                            ?>
                                                    <option isHospitalSite="<?php echo $isHospitalSite; ?>" value="<?php echo EncodeQueryData($selrotationId); ?>" <?php if ($rotationId == $selrotationId) { ?> selected="true" <?php } ?>>
                                                        <?php echo ($name); ?>
                                                    </option>
                                            <?php
                                                }
                                            }
                                            unset($objRotation);
                                            ?>
                                        </select>
                                        <div id="error-txtRotation"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-md-12 control-label" for="cbohospitalsites">Hospital Site</label>
                                    <div class="col-md-12">
                                        <select id="cbohospitalsites" name="cbohospitalsites" class="form-control input-md required-input select2_single" placeholder="Select Hospital site" disabled>
                                            <option value="" selected>Select</option>
                                            <?php
                                            if ($Hospitals != "") {
                                                while ($row = mysqli_fetch_assoc($Hospitals)) {
                                                    // print_r($row);
                                                    $selectHospitalSiteId  = $row['hospitalSiteId'];
                                                    //  $hospitalSiteId  = 0;
                                                    $name  = stripslashes($row['title']);

                                            ?>
                                                    <option value="<?php echo ($selectHospitalSiteId); ?>" <?php if ($selectHospitalSiteId == $hospitalSiteId) { ?> selected="true" <?php } ?>>
                                                        <?php echo ($name); ?>
                                                    </option>
                                            <?php

                                                }
                                            }
                                            ?>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-md-12 control-label" for="ClinicianDate">Clinician Signature</label>
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <div class='input-group date w-full' id='ClinicianDate' style="position: relative;">
                                            <input type='text' name="ClinicianDate" id="ClinicianDate" class="form-control input-md dateInputFormat ClinicianDate" value="<?php echo $clinicianDate; ?>" data-parsley-errors-container="#error-txtClinicianDate" placeholder="MM-DD-YYYY" readonly />
                                            <span class="input-group-addon calender-icon">
                                                <span class="glyphicon glyphicon-calendar"></span>
                                            </span>
                                            <div id="error-txtClinicianDate"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-md-12 control-label" for="schoolDate">School Signature</label>
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <div class='input-group date w-full' id='schoolDate' style="position: relative;">
                                            <input type='text' name="schoolDate" id="schoolDate" class="dateInputFormat form-control input-md schoolDate" value="<?php echo $schoolDate; ?>" data-parsley-errors-container="#error-txtschoolDate" placeholder="MM-DD-YYYY" readonly />
                                            <span class="input-group-addon calender-icon">
                                                <span class="glyphicon glyphicon-calendar"></span>
                                            </span>
                                        </div>
                                        <div id="error-txtschoolDate"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-md-12 control-label" for="pacrCohort">RT Cohort</label>
                                    <div class="col-md-12">
                                        <input type='text' name="pacrCohort" id="pacrCohort" class="form-control input-md " value="<?php echo $pacrCohort; ?>" />

                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-md-12 control-label" for="pacrWeek">Week</label>
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <input type='text' name="pacrWeek" id="pacrWeek" class="form-control input-md " value="<?php echo $pacrWeek; ?>" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-md-12 control-label" for="cbohospitalsites">Hospital Site
                                        Units</label>
                                    <div class="col-md-12">
                                        <select id="cbohospitalsiteunits" name="cbohospitalsiteunits" class="form-control input-md required-input select2_single" placeholder="Select Hospital site Unit">
                                            <option value="" selected>Select</option>
                                            <?php
                                            if ($hospitalsiteunit != "") {
                                                while ($row = mysqli_fetch_assoc($hospitalsiteunit)) {
                                                    $selhospitalSiteUnitId  = $row['schoolClinicalSiteUnitId'];
                                                    $name  = stripslashes($row['title']);

                                            ?>
                                                    <option value="<?php echo ($selhospitalSiteUnitId); ?>" <?php if ($hospitalSiteUnitId == $selhospitalSiteUnitId) { ?> selected="true" <?php } ?>>
                                                        <?php echo ($name); ?>
                                                    </option>

                                            <?php

                                                }
                                            }
                                            ?>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3 flex-end">
                            <button class="button next btn-navigate-form-step" type="button" step_number="2">Save & Next</button>
                        </div>
                    </section>
                    <!-- Step 2 Content, default hidden on page load. -->
                    <section id="step-2" class="form-step d-none" data-parsley-validate>
                        <div class="row padding_top_ten margin_bottom_ten w-full" style="margin: 10px auto;">
                            <!-- <div class="row text-center margin_bottom_ten">
                                <label>
                                    <b>Patient Demographics</b>
                                </label>
                            </div> -->
                            <div class="row">
                                <div class="col-md-12 p-0">
                                    <div class=" col-md-4 col-6">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="pacrPtAge">Age</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $pacrPtAge; ?>" name="pacrPtAge" id="pacrPtAge">
                                            </div>
                                        </div>
                                    </div>
                                    <div class=" col-md-4 col-6">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="pacrSex">Gender</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $pacrSex; ?>" name="pacrSex" id="pacrSex">
                                            </div>
                                        </div>
                                    </div>
                                    <div class=" col-md-4 col-12">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="pacrAdmissionDate">Date of
                                                Admission</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <div class='input-group date w-full' id='pacrAdmissionDate' style="position: relative;">
                                                    <input type='text' name="pacrAdmissionDate" id="pacrAdmissionDate" class="form-control input-md dateInputFormat " value="<?php echo $pacrAdmissionDate; ?>" placeholder="MM-DD-YYYY" />
                                                    <span class="input-group-addon calender-icon">
                                                        <span class="glyphicon glyphicon-calendar"></span>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="col-md-12 control-label" for="pacrCC">Chief Complaint (CC)</label>
                                        <div class="col-md-12 col-sm-12 col-xs-12">
                                            <textarea type="text" class="form-control input-md" name="pacrCC" id="pacrCC" rows="3"><?php echo $pacrCC; ?></textarea>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="col-md-12 control-label" for="pacrDx">Admitting Diagnosis
                                            (Dx)</label>
                                        <div class="col-md-12 col-sm-12 col-xs-12">
                                            <textarea type="text" class="form-control input-md" name="pacrDx" id="pacrDx" rows="3"><?php echo $pacrDx; ?></textarea>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="col-md-12 control-label" for="pacrHx">Patient History
                                            (Hx)</label>
                                        <div class="col-md-12 col-sm-12 col-xs-12">
                                            <textarea type="text" class="form-control input-md" name="pacrHx" id="pacrHx" rows="3"><?php echo $pacrHx; ?></textarea>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="col-md-12 control-label" for="">
                                            Physical Exam

                                        </label>

                                        <div class="col-md-12 text-left">
                                            <div class="form-group">

                                                <div class="col-md-12 col-md-12">
                                                    <textarea type="text" class="form-control input-md" name="pacrPhysicalExam" id="pacrPhysicalExam" rows="3"><?php echo $pacrPhysicalExam; ?></textarea>
                                                </div>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="col-md-12 control-label" for="pacrBreathSounds">Bilateral Breath
                                            Sounds</label>
                                        <div class="col-md-12 col-sm-12 col-xs-12">
                                            <textarea type="text" class="form-control input-md" name="pacrBreathSounds" id="" rows="3"><?php echo $pacrBreathSounds; ?></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3 flex-end">
                            <button class="button btn-navigate-form-step button-prev" type="button" step_number="1">Prev</button>
                            <button class="button next btn-navigate-form-step" type="button" step_number="3">Save & Next</button>
                        </div>
                    </section>
                    <!-- Step 3 Content, default hidden on page load. -->
                    <section id="step-3" class="form-step d-none " data-parsley-validate>
                        <div class="row padding_top_ten margin_bottom_ten w-full" style="margin: 10px auto;">
                            <!-- <div class="row text-center margin_bottom_ten">
                                <label>
                                    <b>VITALS (Per Visit)</b>
                                </label>
                            </div> -->
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="col-md-4  mb-10 mobile-padding-zero mobile-collaps-view">
                                        <div class="collapsible">
                                            <p style="text-align: center;"><b> Visit 1 </b> </p>
                                            <i class="fa fa-chevron-down desktop-hide" aria-hidden="true"></i>
                                        </div>
                                        <div class="content">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="pacrTime1"> Time</label>
                                                <div class="col-md-12">
                                                    <div class='input-group date w-full' id='pacrTime1' style="position: relative;">
                                                        <input type='text' name="pacrTime1" id="pacrTime1" class="form-control input-md  " value="<?php echo $pacrTime1; ?>" />
                                                        <span class="input-group-addon calender-icon">
                                                            <span class="glyphicon glyphicon-calendar"></span>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="pacrHR1">HR</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $pacrHR1; ?>" name="pacrHR1" id="pacrHR1">
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="pacrBP1">BP</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $pacrBP1; ?>" name="pacrBP1" id="pacrBP1">
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="pacrRR1">RR</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $pacrRR1; ?>" name="pacrRR1" id="pacrRR1">
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="pacrTemp1">Temp</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $pacrTemp1; ?>" name="pacrTemp1" id="pacrTemp1">
                                                </div>
                                            </div>
                                            <div class="col-6 col-md-6 pl-0">
                                                <div class="form-group">
                                                    <label class="col-md-12 control-label" for="pacrSpO21">SpO2</label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12" style="padding-right: 0;">
                                                        <input type="text" class="form-control input-md" value="<?php echo $pacrSpO21; ?>" name="pacrSpO21" id="pacrSpO21">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-6 col-md-6 mobile-pl-0">
                                                <div class="form-group">
                                                    <label class="col-md-12 control-label" for="pacrFIO21">FIO2</label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12" style="padding-right: 0;">
                                                        <input type="text" class="form-control input-md" value="<?php echo $pacrFIO21; ?>" name="pacrFIO21" id="pacrFIO21">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4  mb-10 mobile-padding-zero mobile-collaps-view">
                                        <div class="collapsible">
                                            <p style="text-align: center;"><b> Visit 2 </b></p>
                                            <i class="fa fa-chevron-down desktop-hide" aria-hidden="true"></i>
                                        </div>
                                        <div class="content">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="pacrTime2"> Time</label>
                                                <div class="col-md-12">
                                                    <div class='input-group date w-full' id='pacrTime2' style="position: relative;">
                                                        <input type='text' name="pacrTime2" id="pacrTime2" class="form-control input-md " value="<?php echo $pacrTime2; ?>" />
                                                        <span class="input-group-addon calender-icon">
                                                            <span class="glyphicon glyphicon-calendar"></span>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="pacrHR2">HR</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $pacrHR2; ?>" name="pacrHR2" id="pacrHR2">
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="pacrBP2">BP</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $pacrBP2; ?>" name="pacrBP2" id="pacrBP2">
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="pacrRR2">RR</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $pacrRR2; ?>" name="pacrRR2" id="pacrRR2">
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="pacrTemp2">Temp</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $pacrTemp2; ?>" name="pacrTemp2" id="pacrTemp2">
                                                </div>
                                            </div>
                                            <div class="col-md-6 pl-0">
                                                <div class="form-group">
                                                    <label class="col-md-12 control-label" for="pacrSpO22">SpO2</label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12" style="padding-right: 0;">
                                                        <input type="text" class="form-control input-md" value="<?php echo $pacrSpO22; ?>" name="pacrSpO22" id="pacrSpO22">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6 mobile-pl-0">
                                                <div class="form-group">
                                                    <label class="col-md-12 control-label" for="pacrFIO22">FIO2</label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12" style="padding-right: 0;">
                                                        <input type="text" class="form-control input-md" value="<?php echo $pacrFIO22; ?>" name="pacrFIO22" id="pacrFIO22">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4  mb-10 mobile-padding-zero mobile-collaps-view">
                                        <div class="collapsible">
                                            <p style="text-align: center;"><b> Visit 3 </b></p>
                                            <i class="fa fa-chevron-down desktop-hide" aria-hidden="true"></i>
                                        </div>
                                        <div class="content">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="pacrTime3"> Time</label>
                                                <div class="col-md-12">
                                                    <div class='input-group date w-full' id='pacrTime3' style="position: relative;">
                                                        <input type='text' name="pacrTime3" id="pacrTime3" class="form-control input-md " value="<?php echo $pacrTime3; ?>" />
                                                        <span class="input-group-addon calender-icon">
                                                            <span class="glyphicon glyphicon-calendar"></span>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="pacrHR3">HR</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $pacrHR3; ?>" name="pacrHR3" id="pacrHR3">
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="pacrBP3">BP</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $pacrBP3; ?>" name="pacrBP3" id="pacrBP3">
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="pacrRR3">RR</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $pacrRR3; ?>" name="pacrRR3" id="pacrRR3">
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="pacrTemp3">Temp</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $pacrTemp3; ?>" name="pacrTemp3" id="pacrTemp3">
                                                </div>
                                            </div>
                                            <div class="col-md-6 pl-0">
                                                <div class="form-group">
                                                    <label class="col-md-12 control-label" for="pacrSpO23">SpO2</label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12" style="padding-right: 0;">
                                                        <input type="text" class="form-control input-md" value="<?php echo $pacrSpO23; ?>" name="pacrSpO23" id="pacrSpO23">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6 mobile-pl-0">
                                                <div class="form-group">
                                                    <label class="col-md-12 control-label" for="pacrFIO23">FIO2</label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12" style="padding-right: 0;">
                                                        <input type="text" class="form-control input-md" value="<?php echo $pacrFIO23; ?>" name="pacrFIO23" id="pacrFIO23">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="col-md-12 control-label" for="pacrInerpret">Interpret the Changes
                                            from
                                            Baseline</label>
                                        <div class="col-md-12 col-sm-12 col-xs-12">
                                            <textarea type="text" class="form-control input-md" name="pacrInerpret" id="pacrInerpret" rows="3"><?php echo $pacrInerpret; ?></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="col-md-12 control-label" for="pacrMapFormula">Write the Mean
                                            Arterial
                                            Pressure (MAP) Formula</label>
                                        <div class="col-md-12 col-sm-12 col-xs-12">
                                            <textarea type="text" class="form-control input-md" name="pacrMapFormula" id="pacrMapFormula" rows="3"><?php echo $pacrMapFormula; ?></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            For each visit, state whether MAP is normal or abnormal (Refer to Dana Oakes).
                            <div class="col-md-9 col-sm-4 col-xs-12">
                                <input type="text" class="form-control input-md"
                                    value="<?php echo $adultBreathSounds; ?>" name="adultBreathSounds"
                                    id="adultBreathSounds">
                            </div> 
                        </div>
                    </div>
                </div> -->
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="col-md-12 control-label" for="">Visit 1: What is the MAP for each
                                            blood
                                            pressure?
                                            <div class="col-md-12 p-0">
                                                <input style="" type="text" class="form-control input-md blood-pressure-input" value="<?php echo $pacrMap1BP; ?>" name="pacrMap1BP" id="pacrMap1BP">
                                                <input style="width: 27px; vertical-align: text-top;" type="radio" id="pacrMap1BPNormal" name="visi1Check" value="0" <?php if ($visi1Check == 0 && $visi1Check != '') echo 'checked'; ?>>
                                                normal<input style="width: 27px; vertical-align: text-top;" type="radio" id="pacrMap1BPAbnormal" name="visi1Check" value="1" <?php if ($visi1Check == 1) echo 'checked'; ?>>abnormal
                                            </div>
                                        </label>
                                        <!-- <div class="col-md-1">
                                
                               <input type="text" class="form-control input-md" value="" name="" id=""> 
                            </div> -->
                                        <!-- <div class="col-md-2 col-sm-4 col-xs-12">
                                <input type="text" class="form-control input-md" value="" name="" id="">
                            </div> -->
                                        <!-- <label class="col-md-2 control-label" for="">Is it abnormal or normal?</label> -->

                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="col-md-7 control-label" for="">Visit 2: What is the MAP for each
                                            blood
                                            pressure
                                            <div class="col-md-12 p-0">
                                                <input type="text" class="form-control input-md blood-pressure-input" value="<?php echo $pacrMap2BP; ?>" name="pacrMap2BP" id="pacrMap2BP">
                                                <!-- <div class="col-md-2 col-sm-4 col-xs-12">
                                <input type="text" class="form-control input-md" value="" name="" id="">
                            </div> -->
                                                <!-- <label class="col-md-2 control-label" for="">Is it abnormal or normal?</label> -->
                                                <!-- <div class="col-md-2 col-sm-4 col-xs-12">
                                <input type="text" class="form-control input-md" value="" name="" id="">
                            </div> -->
                                                <input style="width: 27px; vertical-align: text-top;" type="radio" id="pacrMap2BPNormal" name="visi2Check" value="0" <?php if ($visi2Check == 0 && $visi2Check != '') echo 'checked'; ?>>
                                                normal<input style="width: 27px; vertical-align: text-top;" type="radio" id="pacrMap2BPAbnormal" name="visi2Check" value="1" <?php if ($visi2Check == 1) echo 'checked'; ?>>abnormal
                                            </div>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="col-md-7 control-label" for="">Visit 3: What is the MAP for each
                                            blood
                                            pressure
                                            <div class="col-md-12 p-0">
                                                <input class="form-control input-md blood-pressure-input" value="<?php echo $pacrMap3BP; ?>" name="pacrMap3BP" id="pacrMap3BP">
                                                <!-- <div class="col-md-2 col-sm-4 col-xs-12">
                                                        <input type="text" class="form-control input-md" value="" name="" id="">
                                                    </div> -->
                                                <!-- <label class="col-md-2 control-label" for="">Is it abnormal or normal?</label>
                                                    <div class="col-md-2 col-sm-4 col-xs-12">
                                                        <input type="text" class="form-control input-md" value="" name="" id="">
                                                    </div> -->
                                                <input style="width: 27px; vertical-align: text-top;" type="radio" id="pacrMap3BPNormal" name="visi3Check" value="0" <?php if ($visi3Check == 0 && $visi3Check != '') echo 'checked'; ?>>
                                                normal<input style="width: 27px; vertical-align: text-top;" type="radio" id="pacrMap3BPAbnormal" name="visi3Check" value="1" <?php if ($visi3Check == 1) echo 'checked'; ?>>abnormal
                                            </div>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="col-md-12 control-label" for="adultBreathSounds">If the MAP is
                                            elevated, what
                                            does this do to the heart?</label>
                                        <div class="col-md-12 col-sm-12 col-xs-12">
                                            <textarea type="text" class="form-control input-md" name="pacrMapElevated" id="pacrMapElevated" rows="3"><?php echo $pacrMapElevated; ?></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="col-md-12 control-label" for="adultBreathSounds">If the MAP is
                                            low, what does
                                            it do to the vital organs?</label>
                                        <div class="col-md-12 col-sm-12 col-xs-12">
                                            <textarea type="text" class="form-control input-md" name="pacrMapLow" id="pacrMapLow" rows="3"><?php echo $pacrMapLow; ?></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3 flex-end">
                            <button class="button btn-navigate-form-step button-prev" type="button" step_number="2">Prev</button>
                            <button class="button next btn-navigate-form-step" type="button" step_number="4">Save & Next</button>
                        </div>
                    </section>
                    <!-- Step 4 Content, default hidden on page load. -->
                    <section id="step-4" class="form-step d-none" data-parsley-validate>
                        <div class="row padding_top_ten margin_bottom_ten w-full" style="margin: 10px auto;">
                            <!-- <div class="row">
                                        <div class="col-md-12 text-center">
                                            <label>
                                                <b>CBC, Chemistry, and ABG</b>
                                            </label>
                                        </div>

                                    </div> -->

                            <div class="mobile-padding-zero mobile-collaps-view mb-10">
                                <div class="collapsible desktop-hide">
                                    <p style="text-align: center;"> <b> Abnormal Values </b></p>
                                    <i class="fa fa-chevron-down desktop-hide" aria-hidden="true"></i>
                                </div>
                                <div class="content">
                                    <div class="col-md-12 mobile-padding-zero">
                                        <p><b>Check any abnormal values in the boxes below </b>
                                            (Refer to Dana Oakes).<br> Please make sure to only use Dana Oakes values.
                                        </p>
                                    </div>
                                    <div class="col-md-12 mobile-padding-zero">
                                        <div class="col-md-4 pl-0 mobile-padding-zero">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="pacrCBGDate"> Date</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <div class='input-group date w-full' id='pacrCBGDate' style="position: relative;">
                                                        <input type='text' name="pacrCBGDate" id="pacrCBGDate" class="form-control input-md dateInputFormat " value="<?php echo $pacrCBGDate; ?>" placeholder="MM-DD-YYYY" />
                                                        <span class="input-group-addon calender-icon">
                                                            <span class="glyphicon glyphicon-calendar"></span>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 first border-right-gray mobile-padding-zero">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="form-group alignCenter">
                                                    <label class="col-md-12 control-label" for="pacrNa">Na
                                                    </label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; align-items: center;">
                                                        <div class="w-full">
                                                            <input type="text" class="form-control input-md" value="<?php echo $pacrNa; ?>" name="pacrNa" id="pacrNa">
                                                        </div>
                                                        <div class="d-flex abnormal-value-checkbox">
                                                            <input type="checkbox" class="custom-control-input checkboxStyle" name="pacrCBCCheckValue[]" id="pacrIsCheck1" isParent="pacrNa" value="pacrIsCheck1">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="form-group alignCenter">
                                                    <label class="col-md-12 control-label" for="pacrK">K+
                                                    </label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; align-items: center;">
                                                        <div class="w-full">
                                                            <input type="text" class="form-control input-md" value="<?php echo $pacrK; ?>" name="pacrK" id="pacrK">
                                                        </div>
                                                        <div class="d-flex abnormal-value-checkbox">
                                                            <input type="checkbox" class="custom-control-input checkboxStyle" name="pacrCBCCheckValue[]" id="pacrIsCheck2" isParent="pacrK" value="pacrIsCheck2">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="form-group alignCenter">
                                                    <label class="col-md-12 control-label" for="pacrCl">Cl-
                                                    </label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; align-items: center;">
                                                        <div class="w-full">
                                                            <input type="text" class="form-control input-md" value="<?php echo $pacrCl; ?>" name="pacrCl" id="pacrCl">
                                                        </div>
                                                        <div class="d-flex abnormal-value-checkbox">
                                                            <input type="checkbox" class="custom-control-input checkboxStyle" name="pacrCBCCheckValue[]" id="pacrIsCheck3" isParent="pacrCl" value="pacrIsCheck3">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="form-group alignCenter">
                                                    <label class="col-md-12 control-label" for="pacrCa">Ca
                                                    </label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; align-items: center;">
                                                        <div class="w-full">
                                                            <input type="text" class="form-control input-md" value="<?php echo $pacrCa; ?>" name="pacrCa" id="pacrCa">
                                                        </div>
                                                        <div class="d-flex abnormal-value-checkbox">
                                                            <input type="checkbox" class="custom-control-input checkboxStyle" name="pacrCBCCheckValue[]" id="pacrIsCheck4" isParent="pacrCa" value="pacrIsCheck4">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="form-group alignCenter">
                                                    <label class="col-md-12 control-label" for="pacrBun">BUN
                                                    </label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; align-items: center;">
                                                        <div class="w-full">
                                                            <input type="text" class="form-control input-md" value="<?php echo $pacrBun; ?>" name="pacrBun" id="pacrBun">
                                                        </div>
                                                        <div class="d-flex abnormal-value-checkbox">
                                                            <input type="checkbox" class="custom-control-input checkboxStyle" name="pacrCBCCheckValue[]" id="pacrIsCheck5" isParent="pacrBun" value="pacrIsCheck5">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="form-group alignCenter">
                                                    <label class="col-md-12 control-label" for="pacrGlucose">Glucose
                                                    </label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; align-items: center;">
                                                        <div class="w-full">
                                                            <input type="text" class="form-control input-md" value="<?php echo $pacrGlucose; ?>" name="pacrGlucose" id="pacrGlucose">
                                                        </div>
                                                        <div class="d-flex abnormal-value-checkbox">
                                                            <input type="checkbox" class="custom-control-input checkboxStyle" name="pacrCBCCheckValue[]" id="pacrIsCheck6" isParent="pacrGlucose" value="pacrIsCheck6">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="form-group alignCenter">
                                                    <label class="col-md-12 control-label" for="pacrPtt">Pt/PTT
                                                    </label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; align-items: center;">
                                                        <div class="w-full">
                                                            <input type="text" class="form-control input-md" value="<?php echo $pacrPtt; ?>" name="pacrPtt" id="pacrPtt">
                                                        </div>
                                                        <div class="d-flex abnormal-value-checkbox">
                                                            <input type="checkbox" class="custom-control-input checkboxStyle" name="pacrCBCCheckValue[]" id="pacrIsCheck7" isParent="pacrPtt" value="pacrIsCheck7">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="form-group alignCenter">
                                                    <label class="col-md-12 control-label" for="pacrAlbumin">Albumin
                                                    </label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; align-items: center;">
                                                        <div class="w-full">
                                                            <input type="text" class="form-control input-md" value="<?php echo $pacrAlbumin; ?>" name="pacrAlbumin" id="pacrAlbumin">
                                                        </div>
                                                        <div class="d-flex abnormal-value-checkbox">
                                                            <input type="checkbox" class="custom-control-input checkboxStyle" name="pacrCBCCheckValue[]" id="pacrIsCheck8" isParent="pacrAlbumin" value="pacrIsCheck8">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="form-group alignCenter">
                                                    <label class="col-md-12 control-label" for="pacrSensitivity">C&S:culture and sensitivity
                                                    </label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; align-items: center;">
                                                        <div class="w-full">
                                                            <input type="text" class="form-control input-md" value="<?php echo $pacrSensitivity; ?>" name="pacrSensitivity" id="pacrSensitivity">
                                                        </div>
                                                        <div class="d-flex abnormal-value-checkbox">
                                                            <input type="checkbox" class="custom-control-input checkboxStyle" name="pacrCBCCheckValue[]" id="pacrIsCheck9" isParent="pacrSensitivity" value="pacrIsCheck9">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="form-group alignCenter">
                                                    <label class="col-md-12 control-label" for="pacrAfb">AFB:acid fast
                                                        bacilli
                                                    </label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; align-items: center;">
                                                        <div class="w-full">
                                                            <input type="text" class="form-control input-md" value="<?php echo $pacrAfb; ?>" name="pacrAfb" id="pacrAfb">
                                                        </div>
                                                        <div class="d-flex abnormal-value-checkbox">
                                                            <input type="checkbox" class="custom-control-input checkboxStyle" name="pacrCBCCheckValue[]" id="pacrIsCheck10" isParent="pacrAfb" value="pacrIsCheck10">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="form-group alignCenter">
                                                    <label class="col-md-12 control-label" for="pacrCreatine">Creatine
                                                    </label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; align-items: center;">
                                                        <div class="w-full">
                                                            <input type="text" class="form-control input-md" value="<?php echo $pacrCreatine; ?>" name="pacrCreatine" id="pacrCreatine">
                                                        </div>
                                                        <div class="d-flex abnormal-value-checkbox">
                                                            <input type="checkbox" class="custom-control-input checkboxStyle" name="pacrCBCCheckValue[]" id="pacrIsCheck11" isParent="pacrCreatine" value="pacrIsCheck11">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                    <div class="col-md-4 second border-right-gray mobile-padding-zero">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="form-group alignCenter">
                                                    <label class="col-md-12 control-label" for="pacrRbc">RBC
                                                    </label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; align-items: center;">
                                                        <div class="w-full">
                                                            <input type="text" class="form-control input-md" value="<?php echo $pacrRbc; ?>" name="pacrRbc" id="pacrRbc">
                                                        </div>
                                                        <div class="d-flex abnormal-value-checkbox">
                                                            <input type="checkbox" class="custom-control-input checkboxStyle" name="pacrCBCCheckValue[]" id="pacrIsCheck12" isParent="pacrRbc" value="pacrIsCheck12">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="form-group alignCenter">
                                                    <label class="col-md-12 control-label" for="pacrHb">Hb
                                                    </label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; align-items: center;">
                                                        <div class="w-full">
                                                            <input type="text" class="form-control input-md" value="<?php echo $pacrHb; ?>" name="pacrHb" id="pacrHb">
                                                        </div>
                                                        <div class="d-flex abnormal-value-checkbox">
                                                            <input type="checkbox" class="custom-control-input checkboxStyle" name="pacrCBCCheckValue[]" id="pacrIsCheck13" isParent="pacrHb" value="pacrIsCheck13">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="form-group alignCenter">
                                                    <label class="col-md-12 control-label" for="pacrHct">Hct
                                                    </label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; align-items: center;">
                                                        <div class="w-full">
                                                            <input type="text" class="form-control input-md" value="<?php echo $pacrHct; ?>" name="pacrHct" id="pacrHct">
                                                        </div>
                                                        <div class="d-flex abnormal-value-checkbox">
                                                            <input type="checkbox" class="custom-control-input checkboxStyle" name="pacrCBCCheckValue[]" id="pacrIsCheck14" isParent="pacrHct" value="pacrIsCheck14">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="form-group alignCenter">
                                                    <label class="col-md-12 control-label" for="pacrWbc">WBC
                                                    </label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; align-items: center;">
                                                        <div class="w-full">
                                                            <input type="text" class="form-control input-md" value="<?php echo $pacrWbc; ?>" name="pacrWbc" id="pacrWbc">
                                                        </div>
                                                        <div class="d-flex abnormal-value-checkbox">
                                                            <input type="checkbox" class="custom-control-input checkboxStyle" name="pacrCBCCheckValue[]" id="pacrIsCheck15" isParent="pacrWbc" value="pacrIsCheck15">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="form-group alignCenter">
                                                    <label class="col-md-12 control-label" for="pacrBaso">Baso:basophils
                                                    </label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; align-items: center;">
                                                        <div class="w-full">
                                                            <input type="text" class="form-control input-md" value="<?php echo $pacrBaso; ?>" name="pacrBaso" id="pacrBaso">
                                                        </div>
                                                        <div class="d-flex abnormal-value-checkbox">
                                                            <input type="checkbox" class="custom-control-input checkboxStyle" name="pacrCBCCheckValue[]" id="pacrIsCheck16" isParent="pacrBaso" value="pacrIsCheck16">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="form-group alignCenter">
                                                    <label class="col-md-12 control-label" for="pacrLymph">Lymph
                                                    </label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; align-items: center;">
                                                        <div class="w-full">
                                                            <input type="text" class="form-control input-md" value="<?php echo $pacrLymph; ?>" name="pacrLymph" id="pacrLymph">
                                                        </div>
                                                        <div class="d-flex abnormal-value-checkbox">
                                                            <input type="checkbox" class="custom-control-input checkboxStyle" name="pacrCBCCheckValue[]" id="pacrIsCheck17" isParent="pacrLymph" value="pacrIsCheck17">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="form-group alignCenter">
                                                    <label class="col-md-12 control-label" for="pacrEosin">Eosin
                                                    </label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; align-items: center;">
                                                        <div class="w-full">
                                                            <input type="text" class="form-control input-md" value="<?php echo $pacrEosin; ?>" name="pacrEosin" id="pacrEosin">
                                                        </div>
                                                        <div class="d-flex abnormal-value-checkbox">
                                                            <input type="checkbox" class="custom-control-input checkboxStyle" name="pacrCBCCheckValue[]" id="pacrIsCheck18" isParent="pacrEosin" value="pacrIsCheck18">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="form-group alignCenter">
                                                    <label class="col-md-12 control-label" for="pacrBnp">BNP
                                                    </label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; align-items: center;">
                                                        <div class="w-full">
                                                            <input type="text" class="form-control input-md" value="<?php echo $pacrBnp; ?>" name="pacrBnp" id="pacrBnp">
                                                        </div>
                                                        <div class="d-flex abnormal-value-checkbox">
                                                            <input type="checkbox" class="custom-control-input checkboxStyle" name="pacrCBCCheckValue[]" id="pacrIsCheck19" isParent="pacrBnp" value="pacrIsCheck19">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-md-12">
                                                <div class="form-group alignCenter">
                                                    <label class="col-md-12 control-label" for="pacrLactate">Lactate
                                                    </label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; align-items: center;">
                                                        <div class="w-full">
                                                            <input type="text" class="form-control input-md" value="<?php echo $pacrLactate; ?>" name="pacrLactate" id="pacrLactate">
                                                        </div>
                                                        <div class="d-flex abnormal-value-checkbox">
                                                            <input type="checkbox" class="custom-control-input checkboxStyle" name="pacrCBCCheckValue[]" id="pacrIsCheck20" isParent="pacrLactate" value="pacrIsCheck20">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-md-12">
                                                <div class="form-group alignCenter">
                                                    <label class="col-md-12 control-label" for="pacrTropinin">Tropinin
                                                    </label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; align-items: center;">
                                                        <div class="w-full">
                                                            <input type="text" class="form-control input-md" value="<?php echo $pacrTropinin; ?>" name="pacrTropinin" id="pacrTropinin">
                                                        </div>
                                                        <div class="d-flex abnormal-value-checkbox">
                                                            <input type="checkbox" class="custom-control-input checkboxStyle" name="pacrCBCCheckValue[]" id="pacrIsCheck21" isParent="pacrTropinin" value="pacrIsCheck21">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-md-12">
                                                <div class="form-group alignCenter">
                                                    <label style="visibility: hidden;" class="col-md-12 control-label" for="adultNa">
                                                        hello Tropinin Tropinin Tropinin</label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; align-items: center;">
                                                        <div class="w-full">
                                                            <input type="hidden" class="form-control input-md" value="" name="adultNa" id="adultNa">
                                                        </div>
                                                        <div class="d-flex abnormal-value-checkbox" style="display: none;">
                                                            <input type="checkbox" class="custom-control-input checkboxStyle" name="pacrCBCCheckValue[]" id="pacrIsCheck22" isParent="adultNa" value="pacrIsCheck22">
                                                        </div>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 third mobile-padding-zero">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="form-group alignCenter">
                                                    <label class="col-md-12 control-label" for="pacrIntake">I&O:intake
                                                        and output
                                                    </label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; align-items: center;">
                                                        <div class="w-full">
                                                            <input type="text" class="form-control input-md" value="<?php echo $pacrIntake; ?>" name="pacrIntake" id="pacrIntake">
                                                        </div>
                                                        <div class="d-flex abnormal-value-checkbox">
                                                            <input type="checkbox" class="custom-control-input checkboxStyle" name="pacrCBCCheckValue[]" id="pacrIsCheck23" isParent="pacrIntake" value="pacrIsCheck23">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="form-group alignCenter">
                                                    <label class="col-md-12 control-label" for="pacrTotalCO2">Total CO2
                                                    </label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; align-items: center;">
                                                        <div class="w-full">
                                                            <input type="text" class="form-control input-md" value="<?php echo $pacrTotalCO2; ?>" name="pacrTotalCO2" id="pacrTotalCO2">
                                                        </div>
                                                        <div class="d-flex abnormal-value-checkbox">
                                                            <input type="checkbox" class="custom-control-input checkboxStyle" name="pacrCBCCheckValue[]" id="pacrIsCheck24" isParent="pacrTotalCO2" value="pacrIsCheck24">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="form-group alignCenter">
                                                    <label class="col-md-12 control-label" for="pacrPh">pH
                                                    </label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; align-items: center;">
                                                        <div class="w-full">
                                                            <input type="text" class="form-control input-md" value="<?php echo $pacrPh; ?>" name="pacrPh" id="pacrPh">
                                                        </div>
                                                        <div class="d-flex abnormal-value-checkbox">
                                                            <input type="checkbox" class="custom-control-input checkboxStyle" name="pacrCBCCheckValue[]" id="pacrIsCheck25" isParent="pacrPh" value="pacrIsCheck25">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="form-group alignCenter">
                                                    <label class="col-md-12 control-label" for="pacrPaCO2">PaCO2
                                                    </label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; align-items: center;">
                                                        <div class="w-full">
                                                            <input type="text" class="form-control input-md" value="<?php echo $pacrPaCO2; ?>" name="pacrPaCO2" id="pacrPaCO2">
                                                        </div>
                                                        <div class="d-flex abnormal-value-checkbox">
                                                            <input type="checkbox" class="custom-control-input checkboxStyle" name="pacrCBCCheckValue[]" id="pacrIsCheck26" isParent="pacrPaCO2" value="pacrIsCheck26">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-md-12">
                                                <div class="form-group alignCenter">
                                                    <label class="col-md-12 control-label" for="pacrPaO2">PaO2
                                                    </label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; align-items: center;">
                                                        <div class="w-full">
                                                            <input type="text" class="form-control input-md" value="<?php echo $pacrPaO2; ?>" name="pacrPaO2" id="pacrPaO2">
                                                        </div>
                                                        <div class="d-flex abnormal-value-checkbox">
                                                            <input type="checkbox" class="custom-control-input checkboxStyle" name="pacrCBCCheckValue[]" id="pacrIsCheck27" isParent="pacrPaO2" value="pacrIsCheck27">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="form-group alignCenter">
                                                    <label class="col-md-12 control-label" for="pacrHCO3">HCO3-
                                                    </label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; align-items: center;">
                                                        <div class="w-full">
                                                            <input type="text" class="form-control input-md" value="<?php echo $pacrHCO3; ?>" name="pacrHCO3" id="pacrHCO3">
                                                        </div>
                                                        <div class="d-flex abnormal-value-checkbox">
                                                            <input type="checkbox" class="custom-control-input checkboxStyle" name="pacrCBCCheckValue[]" id="pacrIsCheck28" isParent="pacrHCO3" value="pacrIsCheck28">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="form-group alignCenter">
                                                    <label class="col-md-12 control-label" for="pacrBE">BE
                                                    </label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; align-items: center;">
                                                        <div class="w-full">
                                                            <input type="text" class="form-control input-md" value="<?php echo $pacrBE; ?>" name="pacrBE" id="pacrBE">
                                                        </div>
                                                        <div class="d-flex abnormal-value-checkbox">
                                                            <input type="checkbox" class="custom-control-input checkboxStyle" name="pacrCBCCheckValue[]" id="pacrIsCheck29" isParent="pacrBE" value="pacrIsCheck29">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-md-12">
                                                <div class="form-group alignCenter">
                                                    <label class="col-md-12 control-label" for="pacrCoHb">CoHb:carboxyhemoglobin
                                                    </label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; align-items: center;">
                                                        <div class="w-full">
                                                            <input type="text" class="form-control input-md" value="<?php echo $pacrCoHb; ?>" name="pacrCoHb" id="pacrCoHb">
                                                        </div>
                                                        <div class="d-flex abnormal-value-checkbox">
                                                            <input type="checkbox" class="custom-control-input checkboxStyle" name="pacrCBCCheckValue[]" id="pacrIsCheck30" isParent="pacrCoHb" value="pacrIsCheck30">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-md-12">
                                                <div class="form-group alignCenter">
                                                    <label class="col-md-12 control-label" for="pacrSaO2">SaO2
                                                    </label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; align-items: center;">
                                                        <div class="w-full">
                                                            <input type="text" class="form-control input-md" value="<?php echo $pacrSaO2; ?>" name="pacrSaO2" id="pacrSaO2">
                                                        </div>
                                                        <div class="d-flex abnormal-value-checkbox">
                                                            <input type="checkbox" class="custom-control-input checkboxStyle" name="pacrCBCCheckValue[]" id="pacrIsCheck31" isParent="pacrSaO2" value="pacrIsCheck31">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-md-12">
                                                <div class="form-group alignCenter">
                                                    <label class="col-md-12 control-label" for="pacrFIO2">FIO2
                                                    </label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; align-items: center;">
                                                        <div class="w-full">
                                                            <input type="text" class="form-control input-md" value="<?php echo $pacrFIO2; ?>" name="pacrFIO2" id="pacrFIO2">
                                                        </div>
                                                        <div class="d-flex abnormal-value-checkbox">
                                                            <input type="checkbox" class="custom-control-input checkboxStyle" name="pacrCBCCheckValue[]" id="pacrIsCheck32" isParent="pacrFIO2" value="pacrIsCheck32">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>



                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12 control-label">
                                    <div class="form-group">
                                        <label class="col-md-12 control-label" for="pacrInterpretPatient">
                                            Interpret your patient’s labs

                                        </label>

                                        <div class="col-md-12 text-left" style="margin-top: 5px;">
                                            <textarea type="text" class="form-control input-md" name="pacrInterpretPatient" id="pacrInterpretPatient" rows="3"><?php echo $pacrInterpretPatient; ?></textarea>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12 control-label">
                                    <div class="form-group">
                                        <label class="col-md-12 control-label" for="pacrInterpretABGStatus">
                                            Interpret ABG and Oxygenation Status

                                        </label>

                                        <div class="col-md-12 text-left" style="margin-top: 5px;">
                                            <textarea type="text" class="form-control input-md" name="pacrInterpretABGStatus" id="pacrInterpretABGStatus" rows="3"><?php echo $pacrInterpretABGStatus; ?></textarea>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-12 control-label">
                                    <div class="form-group">
                                        <label class="col-md-12 control-label" for="pacrAaFormula">
                                            <b> Write out the formula for the A-a Gradient</b> (Refer to Dana Oakes).
                                            How do
                                            you get the <b style="text-decoration: underline;"> A </b> gradient in the
                                            formula?

                                        </label>

                                        <div class="col-md-12 text-left" style="margin-top: 5px;">


                                            <textarea type="text" class="form-control input-md" name="pacrAaFormula" id="pacrAaFormula" rows="3"><?php echo $pacrAaFormula; ?></textarea>


                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-12 control-label">
                                    <div class="form-group">
                                        <label class="col-md-12 control-label" for="pacrAaNormalvalue">
                                            <b> What is the normal value for the A-a gradient </b> (Refer to Dana
                                            Oakes)? Write out
                                            both formulas for the PAO2 and the PaO2.

                                        </label>

                                        <div class="col-md-12 text-left" style="margin-top: 5px;">


                                            <textarea type="text" class="form-control input-md" name="pacrAaNormalvalue" id="pacrAaNormalvalue" rows="3"><?php echo $pacrAaNormalvalue; ?></textarea>


                                        </div>
                                    </div>
                                </div>



                                <div class="col-md-12 control-label">
                                    <div class="form-group">
                                        <label class="col-md-12 control-label mobile-flex-col" for="pacrAaIsNormal" style="display: flex;">
                                            <b> Is the A-a Gradient abnormal or normal? </b>
                                            <div>
                                                <input style="width: 27px; vertical-align: text-top;" type="radio" id="pacrAaIsNormal" name="pacrAaCheck" value="0" <?php if ($pacrAaIsNormal == 0 && $pacrAaIsNormal != '') echo 'checked' ?>>
                                                normal<input style="width: 27px; vertical-align: text-top;" type="radio" id="pacrAaIsNormal" name="pacrAaCheck" value="1" <?php if ($pacrAaIsNormal == 1) echo 'checked' ?>>abnormal
                                            </div>
                                        </label>

                                        <!-- <div class="col-md-7 text-left" style="margin-top: 5px;">


                                <textarea type="text" class="form-control input-md" name="" id="" rows="1"></textarea>


                            </div> -->
                                    </div>
                                </div>

                                <div class="col-md-12 control-label">
                                    <div class="form-group">
                                        <label class="col-md-12 control-label" for="pacrAaAbnormal">
                                            <b>If abnormal, why </b> (Refer to Dana Oakes)? Please Explain

                                        </label>

                                        <div class="col-md-12 text-left" style="margin-top: 5px;">


                                            <textarea type="text" class="form-control input-md" name="pacrAaAbnormal" id="pacrAaAbnormal" rows="3"><?php echo $pacrAaAbnormal; ?></textarea>


                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12 control-label">
                                    <div class="form-group">
                                        <label class="col-md-12 control-label" for="pacrCaO2">
                                            <b>Write out the formula for Oxygen content (CaO2) - </b> Refer to Dana
                                            Oakes

                                        </label>

                                        <div class="col-md-12 text-left" style="margin-top: 5px;">


                                            <textarea type="text" class="form-control input-md" name="pacrCaO2" id="pacrCaO2" rows="3"><?php echo $pacrCaO2; ?></textarea>


                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12 control-label">
                                    <div class="form-group">
                                        <label class="col-md-12 control-label" for="pacrNormalCaO2">
                                            <b>What is the normal value for the CaO2? </b>

                                        </label>

                                        <div class="col-md-12 text-left" style="margin-top: 5px;">


                                            <textarea type="text" class="form-control input-md" name="pacrNormalCaO2" id="pacrNormalCaO2" rows="3"><?php echo $pacrNormalCaO2; ?></textarea>


                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12 control-label">
                                    <div class="form-group">
                                        <label class="col-md-12 control-label  mobile-flex-col" for="" style="display: flex;">
                                            <b>Is CaO2 normal or abnormal? </b>
                                            <div>
                                                <input style="width: 27px; vertical-align: text-top;" type="radio" id="pacrCaO2Isnormal" name="pacrCaO2Check" value="0" <?php if ($pacrCaO2Isnormal == 0 && $pacrCaO2Isnormal != '') echo 'checked' ?>>
                                                normal <input style="width: 27px; vertical-align: text-top;" type="radio" id="pacrCaO2Isnormal" name="pacrCaO2Check" value="1" <?php if ($pacrCaO2Isnormal == 1) echo 'checked' ?>>abnormal
                                            </div>

                                        </label>

                                        <!-- <div class="col-md-7 text-left" style="margin-top: 5px;">


                                <textarea type="text" class="form-control input-md" name="" id="" rows="1"></textarea>


                            </div> -->
                                    </div>
                                </div>

                                <div class="col-md-12 control-label">
                                    <div class="form-group">
                                        <label class="col-md-12 control-label" for="pacrCaO2Abnormal">
                                            <b>If abnormal, why?</b>Explain pathology.

                                        </label>

                                        <div class="col-md-12 text-left" style="margin-top: 5px;">


                                            <textarea type="text" class="form-control input-md" name="pacrCaO2Abnormal" id="pacrCaO2Abnormal" rows="3"><?php echo $pacrCaO2Abnormal; ?></textarea>


                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-12 control-label">
                                    <div class="form-group">
                                        <label class="col-md-12 control-label" for="pacrProcedures">
                                            <b>List any other procedures/diagnostics performed on your patient
                                            </b>(Bronchoscopy,
                                            ECG, Echo, etc.)

                                        </label>

                                        <div class="col-md-12 text-left" style="margin-top: 5px;">


                                            <textarea type="text" class="form-control input-md" name="pacrProcedures" id="pacrProcedures" rows="3"><?php echo $pacrProcedures; ?></textarea>


                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-12 control-label">
                                    <div class="form-group">
                                        <label class="col-md-12 control-label" for="pacrPFTList">
                                            <b>List any PFT or bedside spirometry performed </b>(list dates, if any)

                                        </label>

                                        <div class="col-md-12 text-left" style="margin-top: 5px;">


                                            <textarea type="text" class="form-control input-md" name="pacrPFTList" id="pacrPFTList" rows="3"><?php echo $pacrPFTList; ?></textarea>


                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12 control-label">
                                    <div class="form-group">
                                        <label class="col-md-12 control-label" for="pacrCXRList">
                                            <b>List any CXR performed </b>(list dates, if any)

                                        </label>

                                        <div class="col-md-12 text-left" style="margin-top: 5px;">
                                            <textarea type="text" class="form-control input-md" name="pacrCXRList" id="pacrCXRList" rows="3"><?php echo $pacrCXRList; ?></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-3 flex-end">
                            <button class="button btn-navigate-form-step button-prev" type="button" step_number="3">Prev</button>
                            <button class="button next btn-navigate-form-step" type="button" step_number="5">Save & Next</button>
                        </div>
                    </section>
                    <!-- Step 5 Content, default hidden on page load. -->
                    <section id="step-5" class="form-step d-none" data-parsley-validate>

                        <div class="row padding_top_ten margin_bottom_ten w-full" style="margin: 10px auto;">
                            <!-- <div class="row">
                                <div class="col-md-12 text-center">
                                    <label>
                                        <b>Other</b>
                                    </label>
                                </div>
                            </div> -->
                            <div class="row mobile-hide">
                                <div class="col-md-3 text-center">
                                    <div class="form-group">
                                        <label class="control-label" for="">
                                            <b>Medication
                                            </b>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-3 text-center">
                                    <div class="form-group">
                                        <label class="control-label" for="">
                                            <b>Classification
                                            </b>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-3 text-center">
                                    <div class="form-group">
                                        <label class="control-label" for="">
                                            <b>Mode of Action
                                            </b>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-2 text-center">
                                    <div class="form-group">
                                        <label class="control-label" for="">
                                            <b>Dosage
                                            </b>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="row" id="MedicationDiv">
                                <?php
                                if ($caseStudyId && ($pacrMedications != '' || $pacrClassifications != '' || $pacrModeOfctions != '' || $pacrDosage != '')) {
                                    // DECODE JSON FOR PRINT DATA
                                    $pacrMedications = json_decode($pacrMedications, true);
                                    $pacrClassifications = json_decode($pacrClassifications, true);
                                    $pacrModeOfctions = json_decode($pacrModeOfctions, true);
                                    $pacrDosage = json_decode($pacrDosage, true);
                                    $result = '';
                                    //MEARGE ARRAY 
                                    if ($pacrMedications && $pacrClassifications && $pacrModeOfctions && $pacrDosage)
                                        $result = array_map('array_merge', $pacrMedications, $pacrClassifications, $pacrModeOfctions, $pacrDosage);
                                    $listId = 0;
                                    if ($result) {
                                        foreach ($result as $key => $value) {
                                            $listId = $key + 1;
                                            $value = ($value); // convert object to array
                                            // PRINT HTML
                                            $divHtml = '<div class="col-md-12 p-0" id="row_' . $listId . '" isrow="' . $listId . '"> <div class="col-md-3"> <div class="form-group"> <div class="col-md-12 col-sm-12 col-xs-12 mobile-block"> <label class=" control-label margin-right" for="pacrMedications' . $listId . '">' . $listId . '</label> <input placeholder="Enter Medication" type="text" class="form-control input-md" value="' . $value[0] . '" name="pacrMedications[' . $listId . '][]" id="pacrMedications' . $listId . '"> </div> </div> </div> <div class="col-md-3"> <div class="form-group"> <div class="col-md-12 col-sm-12 col-xs-12"> <input placeholder="Enter Classification" type="text" class="form-control input-md" value="' . $value[1] . '" name="pacrClassifications[' . $listId . '][]" id="pacrClassifications' . $listId . '"> </div> </div> </div> <div class="col-md-3"> <div class="form-group"> <div class="col-md-12 col-sm-12 col-xs-12"> <input placeholder="Enter Mode of Action" type="text" class="form-control input-md" value="' . $value[2] . '" name="pacrModeOfctions[' . $listId . '][]" id="pacrModeOfctions' . $listId . '"> </div> </div> </div> <div class="col-md-3"> <div class="form-group"style="display: flex;align-items: center;"> <div class="col-md-12 col-sm-12 col-xs-12"> <input placeholder="Enter Dosage" type="text" class="form-control input-md" value="' . $value[3] . '" name="pacrDosage[' . $listId . '][]" id="pacrDosage' . $listId . '"> </div> <div> <a  href="javascript:void(0);" class="deleteRow" deleteRowId="' . $listId . '" ><i class="fa fa-trash-o row-delete-icon" aria-hidden="true" style="font-size: 18px;"></i></a> </div></div> </div> </div>';
                                            echo $divHtml;
                                        }
                                    }

                                ?>

                                <?php } else {
                                    $listId = 5; ?>
                                    <div class="col-md-12 p-0" id="row_1" isrow="1">

                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <div class="col-md-12 col-sm-12 col-xs-12 mobile-block">
                                                    <label class=" control-label margin-right" for="pacrMedications">1</label>

                                                    <input placeholder="Enter Medication" type="text" class="form-control input-md" value="" name="pacrMedications[1][]" id="pacrMedications">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <!-- <label class="col-md-6 control-label" for="adultMedicationsUse1">1</label> -->
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input placeholder="Enter Classification" d type="text" class="form-control input-md" value="" name="pacrClassifications[1][]" id="pacrClassifications">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <!-- <label class="col-md-6 control-label" for="adultMedicationsUse1">1</label> -->
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input placeholder="Enter Mode of Action" type="text" class="form-control input-md" value="" name="pacrModeOfctions[1][]" id="pacrModeOfctions">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <!-- <label class="col-md-6 control-label" for="adultMedicationsUse1">1</label> -->
                                                <div class="col-md-11 col-sm-12 col-xs-12">
                                                    <input placeholder="Enter Dosage" type="text" class="form-control input-md" value="" name="pacrDosage[1][]" id="pacrDosage">
                                                </div>
                                                <!-- <div> <a  href="javascript:void(0);" class="deleteRow" deleteRowId="' + listId + '" ><i class="fa-solid fa-trash" style="font-size: 18px;margin-top: 6px;"></i></a> </div> -->
                                            </div>
                                        </div>

                                    </div>
                                    <div class="col-md-12 p-0" id="row_2" isrow="2">

                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <div class="col-md-12 col-sm-12 col-xs-12 mobile-block">
                                                    <label class=" control-label margin-right" for="pacrMedications">2</label>

                                                    <input placeholder="Enter Medication" type="text" class="form-control input-md" value="" name="pacrMedications[2][]" id="pacrMedications">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <!-- <label class="col-md-6 control-label" for="adultMedicationsUse1">1</label> -->
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input placeholder="Enter Classification" type="text" class="form-control input-md" value="" name="pacrClassifications[2][]" id="pacrClassifications">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <!-- <label class="col-md-6 control-label" for="adultMedicationsUse1">1</label> -->
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input placeholder="Enter Mode of Action" type="text" class="form-control input-md" value="" name="pacrModeOfctions[2][]" id="pacrModeOfctions">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <!-- <label class="col-md-6 control-label" for="adultMedicationsUse1">1</label> -->
                                                <div class="col-md-11 col-sm-12 col-xs-12">
                                                    <input placeholder="Enter Dosage" type="text" class="form-control input-md" value="" name="pacrDosage[2][]" id="pacrDosage">
                                                </div>
                                                <!-- <div> <a  href="javascript:void(0);" class="deleteRow" deleteRowId="' + listId + '" ><i class="fa-solid fa-trash" style="font-size: 18px;margin-top: 6px;"></i></a> </div> -->
                                            </div>
                                        </div>

                                    </div>
                                    <div class="col-md-12 p-0" id="row_3" isrow="3">

                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <div class="col-md-12 col-sm-12 col-xs-12 mobile-block">
                                                    <label class=" control-label margin-right" for="pacrMedications">3</label>

                                                    <input placeholder="Enter Medication" type="text" class="form-control input-md" value="" name="pacrMedications[3][]" id="pacrMedications">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <!-- <label class="col-md-6 control-label" for="adultMedicationsUse1">1</label> -->
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input placeholder="Enter Classification" type="text" class="form-control input-md" value="" name="pacrClassifications[3][]" id="pacrClassifications">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <!-- <label class="col-md-6 control-label" for="adultMedicationsUse1">1</label> -->
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input placeholder="Enter Mode of Action" type="text" class="form-control input-md" value="" name="pacrModeOfctions[3][]" id="pacrModeOfctions">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <!-- <label class="col-md-6 control-label" for="adultMedicationsUse1">1</label> -->
                                                <div class="col-md-11 col-sm-12 col-xs-12">
                                                    <input placeholder="Enter Dosage" type="text" class="form-control input-md" value="" name="pacrDosage[3][]" id="pacrDosage">
                                                </div>
                                                <!-- <div> <a  href="javascript:void(0);" class="deleteRow" deleteRowId="' + listId + '" ><i class="fa-solid fa-trash" style="font-size: 18px;margin-top: 6px;"></i></a> </div> -->
                                            </div>
                                        </div>

                                    </div>
                                    <div class="col-md-12 p-0" id="row_4" isrow="4">

                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <div class="col-md-12 col-sm-12 col-xs-12 mobile-block">
                                                    <label class=" control-label margin-right" for="pacrMedications">4</label>

                                                    <input placeholder="Enter Medication" type="text" class="form-control input-md" value="" name="pacrMedications[4][]" id="pacrMedications">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <!-- <label class="col-md-6 control-label" for="adultMedicationsUse1">1</label> -->
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input placeholder="Enter Classification" type="text" class="form-control input-md" value="" name="pacrClassifications[4][]" id="pacrClassifications">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <!-- <label class="col-md-6 control-label" for="adultMedicationsUse1">1</label> -->
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input placeholder="Enter Mode of Action" type="text" class="form-control input-md" value="" name="pacrModeOfctions[4][]" id="pacrModeOfctions">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <!-- <label class="col-md-6 control-label" for="adultMedicationsUse1">1</label> -->
                                                <div class="col-md-11 col-sm-12 col-xs-12">
                                                    <input placeholder="Enter Dosage" type="text" class="form-control input-md" value="" name="pacrDosage[4][]" id="pacrDosage">
                                                </div>
                                                <!-- <div> <a  href="javascript:void(0);" class="deleteRow" deleteRowId="' + listId + '" ><i class="fa-solid fa-trash" style="font-size: 18px;margin-top: 6px;"></i></a> </div> -->
                                            </div>
                                        </div>

                                    </div>
                                    <div class="col-md-12 p-0" id="row_5" isrow="5">

                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <div class="col-md-12 col-sm-12 col-xs-12 mobile-block">
                                                    <label class=" control-label margin-right" for="pacrMedications">5</label>

                                                    <input placeholder="Enter Medication" type="text" class="form-control input-md" value="" name="pacrMedications[5][]" id="pacrMedications">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <!-- <label class="col-md-6 control-label" for="adultMedicationsUse1">1</label> -->
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input placeholder="Enter Classification" type="text" class="form-control input-md" value="" name="pacrClassifications[5][]" id="pacrClassifications">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <!-- <label class="col-md-6 control-label" for="adultMedicationsUse1">1</label> -->
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input placeholder="Enter Mode of Action" type="text" class="form-control input-md" value="" name="pacrModeOfctions[5][]" id="pacrModeOfctions">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <!-- <label class="col-md-6 control-label" for="adultMedicationsUse1">1</label> -->
                                                <div class="col-md-11 col-sm-12 col-xs-12">
                                                    <input placeholder="Enter Dosage" type="text" class="form-control input-md" value="" name="pacrDosage[5][]" id="pacrDosage">
                                                </div>
                                                <!-- <div> <a  href="javascript:void(0);" class="deleteRow" deleteRowId="' + listId + '" ><i class="fa-solid fa-trash" style="font-size: 18px;margin-top: 6px;"></i></a> </div> -->
                                            </div>
                                        </div>

                                    </div>
                                <?php } ?>
                            </div>
                            <!-- ----------------------------Show More---------------------------------------->
                            <div class="row" style="display: flex; justify-content: center; margin: 15px 0;">
                                <div class="col-md-12 form-group justify-content-center" style="width: fit-content;">
                                    <input type="button" class="form-control input-md showMore_6_10" value="Add More Rows" islast='<?php echo $listId; ?>'>
                                </div>
                            </div>
                            <!-- ------------------------------------------------------------------ -->
                            <div class="row hidecol_6 hide">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="col-md-6 control-label labelcount" for="adultMedicationsUse6">6</label>
                                        <div class="col-md-6 col-sm-4 col-xs-12">
                                            <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse6; ?>" name="adultMedicationsUse6" id="adultMedicationsUse6">
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="col-md-12 col-sm-12 col-xs-12">
                                            <textarea type="text" class="form-control input-md" rows="1" cols="5" name="adultModificationCarePlan6" id="adultModificationCarePlan6"><?php echo $adultModificationCarePlan6; ?></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row hidecol_6 hide">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="col-md-6 control-label labelcount" for="adultMedicationsUse7">7</label>
                                        <div class="col-md-6 col-sm-4 col-xs-12">
                                            <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse7; ?>" name="adultMedicationsUse7" id="adultMedicationsUse7">
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="col-md-12 col-sm-12 col-xs-12">
                                            <textarea type="text" class="form-control input-md" rows="1" cols="5" name="adultModificationCarePlan7" id="adultModificationCarePlan7"><?php echo $adultModificationCarePlan7; ?></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row hidecol_6 hide">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="col-md-6 control-label labelcount" for="adultMedicationsUse8">8</label>
                                        <div class="col-md-6 col-sm-4 col-xs-12">
                                            <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse8; ?>" name="adultMedicationsUse8" id="adultMedicationsUse8">
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="col-md-12 col-sm-12 col-xs-12">
                                            <textarea type="text" class="form-control input-md" rows="1" cols="5" name="adultModificationCarePlan8" id="adultModificationCarePlan8"><?php echo $adultModificationCarePlan8; ?></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row hidecol_6 hide">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="col-md-6 control-label labelcount" for="adultMedicationsUse9">9</label>
                                        <div class="col-md-6 col-sm-4 col-xs-12">
                                            <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse9; ?>" name="adultMedicationsUse9" id="adultMedicationsUse9">
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="col-md-12 col-sm-12 col-xs-12">
                                            <textarea type="text" class="form-control input-md" rows="1" cols="5" name="adultModificationCarePlan9" id="adultModificationCarePlan9"><?php echo $adultModificationCarePlan9; ?></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row hidecol_6 hide">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="col-md-6 control-label labelcount" for="adultMedicationsUse10">10</label>
                                        <div class="col-md-6 col-sm-4 col-xs-12">
                                            <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse10; ?>" name="adultMedicationsUse10" id="adultMedicationsUse10">
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="col-md-12 col-sm-12 col-xs-12">
                                            <textarea type="text" class="form-control input-md" rows="1" cols="5" name="adultModificationCarePlan10" id="adultModificationCarePlan10"><?php echo $adultModificationCarePlan10; ?></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- ----------------------------Show More---------------------------------------->
                            <div class="row showLess_1 hide">
                                <div class="col-md-6 form-group justify-content-center" style="width: 158px;margin-left: 446px;">
                                    <input type="button" class="form-control input-md showMore_10_15" value="Add More Rows">
                                </div>
                                <div class="col-md-6 form-group justify-content-center" style="width: 160px;margin-left: -4px;">
                                    <input type="button" class="form-control input-md showLess_10_6" value="Show Less Rows">
                                </div>
                            </div>
                            <!-- ------------------------------------------------------------------ -->
                            <div class="row hidecol_10 hide">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="col-md-6 control-label labelcount" for="adultMedicationsUse10">11</label>
                                        <div class="col-md-6 col-sm-4 col-xs-12">
                                            <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse11; ?>" name="adultMedicationsUse11" id="adultMedicationsUse11">
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="col-md-12 col-sm-12 col-xs-12">
                                            <textarea type="text" class="form-control input-md" rows="1" cols="5" name="adultModificationCarePlan11" id="adultModificationCarePlan11"><?php echo $adultModificationCarePlan11; ?></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row hidecol_10 hide">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="col-md-6 control-label labelcount" for="adultMedicationsUse10">12</label>
                                        <div class="col-md-6 col-sm-4 col-xs-12">
                                            <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse12; ?>" name="adultMedicationsUse12" id="adultMedicationsUse12">
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="col-md-12 col-sm-12 col-xs-12">
                                            <textarea type="text" class="form-control input-md" rows="1" cols="5" name="adultModificationCarePlan12" id="adultModificationCarePlan12"><?php echo $adultModificationCarePlan12; ?></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row  hidecol_10 hide">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="col-md-6 control-label labelcount" for="adultMedicationsUse13">13</label>
                                        <div class="col-md-6 col-sm-4 col-xs-12">
                                            <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse13; ?>" name="adultMedicationsUse13" id="adultMedicationsUse13">
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="col-md-12 col-sm-12 col-xs-12">
                                            <textarea type="text" class="form-control input-md" rows="1" cols="5" name="adultModificationCarePlan13" id="adultModificationCarePlan13"><?php echo $adultModificationCarePlan13; ?></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row  hidecol_10 hide">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="col-md-6 control-label labelcount" for="adultMedicationsUse14">14</label>
                                        <div class="col-md-6 col-sm-4 col-xs-12">
                                            <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse14; ?>" name="adultMedicationsUse14" id="adultMedicationsUse14">
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="col-md-12 col-sm-12 col-xs-12">
                                            <textarea type="text" class="form-control input-md" rows="1" cols="5" name="adultModificationCarePlan14" id="adultModificationCarePlan14"><?php echo $adultModificationCarePlan14; ?></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row  hidecol_10 hide">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="col-md-6 control-label labelcount" for="adultMedicationsUse15">15</label>
                                        <div class="col-md-6 col-sm-4 col-xs-12">
                                            <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse15; ?>" name="adultMedicationsUse15" id="adultMedicationsUse15">
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="col-md-12 col-sm-12 col-xs-12">
                                            <textarea type="text" class="form-control input-md" rows="1" cols="5" name="adultModificationCarePlan15" id="adultModificationCarePlan15"><?php echo $adultModificationCarePlan15; ?></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- ----------------------------Show Less-------------------------------------- -->
                            <div class="row showLess_2 hide">
                                <div class="col-md-12 form-group justify-content-center" style="width: 168px;margin: 7px;padding-left: 23px;margin-left: 503px;margin-top: -6px;">
                                    <input type="button" class="form-control input-md showLess_15_10" value="Show Less Rows">
                                </div>

                            </div>
                            <!-- ------------------------------------------------------------------ -->



                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="col-md-12 control-label" for="pacrRespiratoryOrders"><b>
                                                Respiratory Orders.</b> Include the correct
                                            sequence on how a respiratory treatment order should be written in the chart
                                            – Modality,
                                            frequency, PD&P, CPT, SXN, oxygen orders, ventilator orders, meds, and
                                            therapeutic goal
                                            for each respiratory treatment or procedure ordered
                                        </label>
                                        <div class="col-md-12 col-sm-12 col-xs-12">
                                            <textarea type="text" class="form-control input-md" name="pacrRespiratoryOrders" id="pacrRespiratoryOrders" rows="3"><?php echo $pacrRespiratoryOrders; ?></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="col-md-12 control-label" for="pacrIndications"><b>What are the
                                                indications for each order?
                                            </b> And what is the rationale for the order?
                                        </label>
                                        <div class="col-md-12 col-sm-12 col-xs-12">
                                            <textarea type="text" class="form-control input-md" name="pacrIndications" id="pacrIndications" rows="3"><?php echo $pacrIndications; ?></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="col-md-12 control-label" for="pacrGoals"><b>What are the goals for
                                                each order </b> (For
                                            example, CPT order goal is to help mobilize secretions)?
                                        </label>
                                        <div class="col-md-12 col-sm-12 col-xs-12">
                                            <textarea type="text" class="form-control input-md" name="pacrGoals" id="pacrGoals" rows="3"><?php echo $pacrGoals; ?></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="col-md-12 control-label" for="pacrRTOrder"><b>Describe how you
                                                know each RT order is
                                                working. </b>(These are called <b>Measures –</b> for example,
                                            bronchodilators,
                                            auscultation, vent settings). How do you know these settings are adequate
                                            without an
                                            ABG?
                                        </label>
                                        <div class="col-md-12 col-sm-12 col-xs-12">
                                            <textarea type="text" class="form-control input-md" name="pacrRTOrder" id="pacrRTOrder" rows="3"><?php echo $pacrRTOrder; ?></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="col-md-12 control-label" for="pacrVentilator"><b>Ventilator
                                                Management </b>(include all
                                            current ventilator modes and settings).
                                        </label>
                                        <div class="col-md-12 col-sm-12 col-xs-12">
                                            <textarea type="text" class="form-control input-md" name="pacrVentilator" id="pacrVentilator" rows="3"><?php echo $pacrVentilator; ?></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="col-md-12 control-label" for="pacrABGResults"><b>Knowing what you
                                                know about ABG results, are
                                                there any changes that YOU as the RT would like to make?</b>If goals are
                                            not being
                                            met what would you do differently as an RT?
                                        </label>
                                        <div class="col-md-12 col-sm-12 col-xs-12">
                                            <textarea type="text" class="form-control input-md" name="pacrABGResults" id="pacrABGResults" rows="3"><?php echo $pacrABGResults; ?></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label style="margin-bottom: 10px;" class="col-md-10 control-label" for=""><b>List any
                                                Hemodynamics and Check if
                                                abnormal. </b> Provide a review of what the hemodynamics is telling you
                                            about your
                                            patient, if applicable.
                                        </label>
                                        <div class="col-md-4 first">
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <div class="form-group alignCenter">
                                                        <label class="col-md-12 control-label" for="pacrSvO2">SvO2
                                                        </label>
                                                        <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex;align-items: center;">
                                                            <div class="w-full">
                                                                <input type="text" class="form-control input-md" value="<?php echo $pacrSvO2; ?>" name="pacrSvO2" id="pacrSvO2">
                                                            </div>
                                                            <div class="d-flex abnormal-value-checkbox">
                                                                <input type="checkbox" class="custom-control-input checkboxStyle" name="pacrCBCCheckValue[]" id="pacrIsCheck33" isParent="pacrSvO2" value="pacrIsCheck33">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-12">
                                                    <div class="form-group alignCenter">
                                                        <label class="col-md-12 control-label" for="pacrCVP">CVP
                                                        </label>
                                                        <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex;align-items: center;">
                                                            <div class="w-full">
                                                                <input type="text" class="form-control input-md" value="<?php echo $pacrCVP; ?>" name="pacrCVP" id="pacrCVP">
                                                            </div>
                                                            <div class="d-flex abnormal-value-checkbox">
                                                                <input type="checkbox" class="custom-control-input checkboxStyle" name="pacrCBCCheckValue[]" id="pacrIsCheck34" isParent="pacrCVP" value="pacrIsCheck34">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>



                                            </div>
                                        </div>
                                        <div class="col-md-4 second" style="border-right: 2px solid #F2F2F2;">
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <div class="form-group alignCenter">
                                                        <label class="col-md-12 control-label" for="pacrCO">CO
                                                        </label>
                                                        <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex;align-items: center;">
                                                            <div class="w-full">
                                                                <input type="text" class="form-control input-md" value="<?php echo $pacrCO; ?>" name="pacrCO" id="pacrCO">
                                                            </div>
                                                            <div class="d-flex abnormal-value-checkbox">
                                                                <input type="checkbox" class="custom-control-input checkboxStyle" name="pacrCBCCheckValue[]" id="pacrIsCheck35" isParent="pacrCO" value="pacrIsCheck35">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-12">
                                                    <div class="form-group alignCenter">
                                                        <label class="col-md-12 control-label" for="pacrPCWP">PCWP
                                                        </label>
                                                        <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex;align-items: center;">
                                                            <div class="w-full">
                                                                <input type="text" class="form-control input-md" value="<?php echo $pacrPCWP; ?>" name="pacrPCWP" id="pacrPCWP">
                                                            </div>
                                                            <div class="d-flex abnormal-value-checkbox">
                                                                <input type="checkbox" class="custom-control-input checkboxStyle" name="pacrCBCCheckValue[]" id="pacrIsCheck36" isParent="pacrPCWP" value="pacrIsCheck36">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>


                                            </div>
                                        </div>
                                        <div class="col-md-4 third">
                                            <div class="row">

                                                <div class="col-md-12">
                                                    <div class="form-group alignCenter">
                                                        <label class="col-md-12 control-label" for="pacrPAP">PAP
                                                        </label>
                                                        <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex;align-items: center;">
                                                            <div class="w-full">
                                                                <input type="text" class="form-control input-md" value="<?php echo $pacrPAP; ?>" name="pacrPAP" id="pacrPAP">
                                                            </div>
                                                            <div class="d-flex abnormal-value-checkbox">
                                                                <input type="checkbox" class="custom-control-input checkboxStyle" name="pacrCBCCheckValue[]" id="pacrIsCheck37" isParent="pacrPAP" value="pacrIsCheck37">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-12">
                                                    <div class="form-group alignCenter">
                                                        <label class="col-md-12 control-label" for="pacrICP">ICP
                                                        </label>
                                                        <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex;align-items: center;">
                                                            <div class="w-full">
                                                                <input type="text" class="form-control input-md" value="<?php echo $pacrICP; ?>" name="pacrICP" id="pacrICP">
                                                            </div>
                                                            <div class="d-flex abnormal-value-checkbox">
                                                                <input type="checkbox" class="custom-control-input checkboxStyle" name="pacrCBCCheckValue[]" id="pacrIsCheck38" isParent="pacrICP" value="pacrIsCheck38">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>

                            <div class="col-md-12 p-0">
                                <div class="form-group">
                                    <label class="col-md-12 control-label" for="pacrSummery"><b>Provide a summary of
                                            your patient’s condition
                                            over the course of hospitalization and explain the plan of care</b>
                                    </label>
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <textarea type="text" class="form-control input-md" name="pacrSummery" id="pacrSummery" rows="9"><?php echo $pacrSummery ?></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3 flex-end">
                            <button class="button btn-navigate-form-step button-prev" type="button" step_number="4">Prev</button>
                            <button class="button btn-navigate-form-step next" type="button" step_number="6">Save & Next</button>
                            <!-- <button class="button btn-navigate-form-step" type="button" step_number="3">Prev</button>
                            <button class="button submit-btn" type="submit">Save</button> -->
                        </div>
                    </section>
                    <!-- Step 6 Content, default hidden on page load. -->
                    <section id="step-6" class="form-step d-none" data-parsley-validate>

                        <?php if ($rsClinicianReviewsCount) { ?>
                            <div class="row padding_top_ten margin_bottom_ten w-full" style="margin: 10px auto;">
                                <div class="row">
                                    <div class="col-md-12 text-center mb-10">
                                        <label>
                                            <b>Clinician Review Section</b>
                                        </label>
                                    </div>

                                </div>
                                <div class="row reviewDiv w-full m-0 mt-3">
                                    <?php
                                    if ($rsClinicianReviewsCount) {
                                        $rowId = 1;
                                        while ($rowClinicianReview = mysqli_fetch_assoc($rsClinicianReviews)) {
                                            // echo '<pre>';
                                            // print_r($rowClinicianReview);

                                            $clinicianReviewId = $rowClinicianReview['clinicianReviewId'];
                                            $clinicianReview = $rowClinicianReview['clinicianReview'];
                                            $reviewDate = $rowClinicianReview['reviewDate'];
                                            $reviewClinicianName = $rowClinicianReview['clinicianName'];
                                            $reviewDate = date('m/d/Y', strtotime($reviewDate));
                                            // $divHtml = '<div id="reviewDiv_' .$rowId . '" class="row w-full" isReviewRow = "' .$rowId . '"> <div class="col-md-1"> <div class="form-group m-0"> <label class="control-label" for="" style="margin-top: 12px;"> <b>' .$rowId . ' </b> </label> </div> </div> <div class="col-md-10 text-left" style="margin-top: 5px;"> <div class="form-group"> <div class="col-md-11 col-md-12 mb-10"> <textarea type="text" class="form-control input-md clinician_comments" name="clinicianReviews['.$rowId.'][]" id="clinicianReviews" rows="2" >'.$clinicianReviewValue.'</textarea> </div> <div> <a  href="javascript:void(0);" class="deleteRow" deleteRowId="' .$rowId . '" ><ion-icon name="trash" title="Delete" style="font-size: 18px;margin-top: 15px;"></ion-icon></a> </div> </div> </div> </div>';
                                            $divHtml = '<div id="reviewDiv_' . $rowId . '" class="row" isReviewRow = ' . $rowId . '> <div class="col-md-1"> <div class="form-group m-0"> <label class="control-label" for="" style="margin-top: 12px;"> <b>' . $rowId . ' </b> </label> </div> </div> <div class="col-md-11 text-left" style="margin-top: 5px;" id="addReviewRow_' . $rowId . '"> <div class="form-group"> <div class="col-md-4 col-md-12 mb-10"> <textarea type="text" class="form-control input-md clinician_comments clinicianReviewClass_' . $rowId . '" name="clinicianReviews" id="clinicianReviews" rows="2" readonly>' . $clinicianReview . '</textarea> </div> <div class="col-md-4 col-sm-12 col-xs-12 mb-10"> <div class="input-group date reviewDate1 w-full" id=""> <input type="text" name="reviewDate" id="" class="form-control input-md dateInputFormat  reviewDate reviewDate_' . $rowId . '" value="' . $reviewDate . '" readonly /> <span class="input-group-addon"> <span class="glyphicon glyphicon-calendar reviewDate"></span> </span> </div> </div><div class="col-md-4 col-sm-12 col-xs-12"> <div class="" id=""> <input type="text" name="reviewDate" id="" class="form-control input-md" value="' . $reviewClinicianName . '" readonly disabled /> </div> </div>  </div> </div> </div>';
                                            echo $divHtml;
                                            $rowId++;
                                        }
                                        // $clinicianReview = json_decode($clinicianReviews,true);

                                        // foreach ($clinicianReview as $key => $value) 
                                        // {
                                        //     $rowId=$key+1;
                                        //     $clinicianReviewValue = ($value[0]); // convert object to array
                                        //     // PRINT HTML
                                        //     $divHtml = '<div id="reviewDiv_' .$rowId . '" class="row w-full" isReviewRow = "' .$rowId . '"> <div class="col-md-1"> <div class="form-group"> <label class="control-label" for="" style="margin-top: 12px;"> <b>' .$rowId . ' </b> </label> </div> </div> <div class="col-md-10 text-left" style="margin-top: 5px;"> <div class="form-group"> <div class="col-md-11 col-md-12"> <textarea type="text" class="form-control input-md clinician_comments" name="clinicianReviews['.$rowId.'][]" id="clinicianReviews" rows="2" >'.$clinicianReviewValue.'</textarea> </div> <div> <a  href="javascript:void(0);" class="deleteRow" deleteRowId="' .$rowId . '" ><ion-icon name="trash" title="Delete" style="font-size: 18px;margin-top: 15px;"></ion-icon></a> </div> </div> </div> </div>';
                                        //     echo $divHtml;
                                        // }
                                        $rowId = $rowId - 1;
                                    } ?>
                                </div>

                            </div>
                        <?php  } ?>

                        <div class="row padding_top_ten margin_bottom_ten w-full" style="margin: 10px auto;">
                            <!-- student comments 21042021-->
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group m-0">
                                        <label class="control-label" for="">
                                            <b>Clinician Comment
                                            </b>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-12 text-left" style="margin-top: 5px;">
                                    <div class="form-group">
                                        <?php //if ($clinician_comments != '') { 
                                        ?>
                                        <div class="col-md-12 col-md-12">
                                            <textarea type="text" class="form-control input-md" name="clinician_comments" id="clinician_comments" rows="5" readonly="readonly"><?php echo $clinician_comments; ?></textarea>
                                        </div>
                                        <?php //} else {
                                        ?>
                                        <!-- <div class="col-md-12 col-md-12">
                                                <textarea type="text" class="form-control input-md" name="clinician_comments" id="clinician_comments" rows="5" readonly="readonly"><?php echo $clinician_comments; ?></textarea>
                                            </div> -->
                                        <?php //} 
                                        ?>
                                    </div>
                                </div>
                            </div>
                            <!-- student comments 21042021-->
                            <!-- school comments 21042021-->
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group m-0">
                                        <label class="control-label" for="">
                                            <b>School Comment
                                            </b>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-12 text-left" style="margin-top: 5px;">
                                    <div class="form-group">
                                        <?php //if ($school_comments != '') { 
                                        ?>
                                        <div class="col-md-12 col-md-12">
                                            <textarea type="text" class="form-control input-md" name="school_comments" id="school_comments" rows="5" readonly="readonly"><?php echo $school_comments; ?></textarea>
                                        </div>
                                        <?php //} else {
                                        ?>
                                        <!-- <div class="col-md-12 col-md-12">
                                                <textarea type="text" class="form-control input-md" name="school_comments" id="school_comments" rows="5" readonly="readonly"><?php echo $school_comments; ?></textarea>
                                            </div> -->
                                        <?php //} 
                                        ?>
                                    </div>
                                </div>
                            </div>
                            <!-- school comments 21042021-->

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group m-0">
                                        <label class="control-label" for="">
                                            <b>Student Comment
                                            </b>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-12 text-left">
                                    <div class="form-group">
                                        <div class="col-md-12">
                                            <textarea name="studentcomments" rows="6" id="studentcomments" class="form-control"><?php echo ($studentcomments); ?></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row" style="margin: 0;">
                            <div class="form-group" style="display: flex;margin-left: 0;">
                                <button class="button btn-navigate-form-step button-prev" type="button" step_number="5">Prev</button>
                                <!-- <label class="col-md-12 control-label"></label> -->
                                <div class="col-md-12 col-sm-12 col-xs-12 flex-end">
                                    <?php if ($view != 'V') { ?>
                                        <button style="margin-right: 10px;" id="btnSave" name="btnSave" class="btn btn-success" type="button">Submit</button>
                                    <?php } ?>
                                    <?php if ($IsMobile) { ?>
                                        <a type="button" href="<?php echo BASE_PATH ?>/webRedirect.html?status=Cancel&type=PACR" class="btn btn-default">Cancel</a>
                                    <?php } else { ?>
                                        <a href="caseStudyList.html" class="btn btn-default">Cancel</a>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3 flex-end" style="display: none;">
                            <button class="button btn-navigate-form-step" type="button" step_number="5">Prev</button>
                            <button class="button submit-btn" type="button">Save</button>
                        </div>
                        <!-- </div> -->
                    </section>
                </form>
            </div>
        </div>

        <!-- stepper end -->
        <!-- <form id="formCaseStudy" data-parsley-validate="data-parsley-validate" class="form-horizontal" method="POST" action="addCaseStudyPACRSubmit.html?caseStudyId=<?php echo encodeQueryData($caseStudyId) ?>">
        </form> -->

    </div>

    <?php include('includes/footer.php'); ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script src="https://cdn.ckeditor.com/ckeditor5/35.0.1/classic/ckeditor.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>




    <script>
        // load common.js
        loadScriptOnce("<?php echo ($dynamicOrgUrl); ?>/assets/js/common.js");

        $('.next').on('click', function() {
            event.preventDefault();

            var isValid = $('#step-1').parsley().validate();

            if (!isValid) {
                return false;
            }
        });



        /**
         * Define a function to navigate betweens form steps.
         * It accepts one parameter. That is - step number.
         */
        const navigateToFormStep = (stepNumber) => {

            document.querySelectorAll(".form-step").forEach((formStepElement) => {
                formStepElement.classList.add("d-none");
            });

            document.querySelectorAll(".form-stepper-list").forEach((formStepHeader) => {
                formStepHeader.classList.add("form-stepper-unfinished");
                formStepHeader.classList.remove("form-stepper-active", "form-stepper-completed");
            });

            document.querySelector("#step-" + stepNumber).classList.remove("d-none");

            const formStepCircle = document.querySelector('li[step="' + stepNumber + '"]');

            formStepCircle.classList.remove("form-stepper-unfinished", "form-stepper-completed");
            formStepCircle.classList.add("form-stepper-active");

            for (let index = 0; index < stepNumber; index++) {

                const formStepCircle = document.querySelector('li[step="' + index + '"]');

                if (formStepCircle) {

                    formStepCircle.classList.remove("form-stepper-unfinished", "form-stepper-active");
                    formStepCircle.classList.add("form-stepper-completed");
                }
            }
        };

        $(document).ready(function() {
            $('.btn-navigate-form-step').on('click', function() {
                const $btn = $(this);
                const stepNumber = parseInt($btn.attr('step_number'));
                const isNextButton = $btn.hasClass('next');
                var view = $('#hiddenView').val();
                if (view == 'V') {
                    navigateToFormStep(stepNumber);
                } else {

                    if (stepNumber === 2) {
                        const isValidStep1 = $('#step-1').parsley().validate();
                        if (!isValidStep1) return false;

                        if (isNextButton) {
                            saveCaseStudyDetails('details', 1, function() {
                                navigateToFormStep(stepNumber);
                            });
                            return; // prevent immediate navigation
                        }
                    }

                    if (stepNumber === 3) {
                        const isValidStep3 = $('#step-2').parsley().validate();
                        if (!isValidStep3) return false;

                        if (isNextButton) {
                            saveCaseStudyDetails('demographics', 2, function() {
                                navigateToFormStep(stepNumber);
                            });
                            return;
                        }
                    }

                    if (stepNumber === 4) {
                        const isValidStep4 = $('#step-3').parsley().validate();
                        if (!isValidStep4) return false;

                        if (isNextButton) {
                            saveCaseStudyDetails('vitals', 3, function() {
                                navigateToFormStep(stepNumber);
                            });
                            return;
                        }
                    }

                    if (stepNumber === 5) {
                        const isValidStep5 = $('#step-4').parsley().validate();
                        if (!isValidStep5) return false;

                        if (isNextButton) {
                            saveCaseStudyDetails('cbc', 4, function() {
                                navigateToFormStep(stepNumber);
                            });
                            return;
                        }
                    }

                    if (stepNumber === 6) {
                        const isValidStep6 = $('#step-5').parsley().validate();
                        if (!isValidStep6) return false;

                        if (isNextButton) {
                            saveCaseStudyDetails('medications', 5, function() {
                                navigateToFormStep(stepNumber);
                            });
                            return;
                        }
                    }
                }

                // For "Previous" buttons or non-saving steps, allow navigation directly
                navigateToFormStep(stepNumber);
            });

        });

        $(document).on('click', '#btnSave', function() {
            const isValidStep6 = $('#step-6').parsley().validate();
            if (!isValidStep6) return false;

            saveCaseStudyDetails('comments', 6, function() {
                // navigateToFormStep(stepNumber);
            });
            return;
        });


        $('.form-stepper-list').click(function() {
            var view = '<?php echo $view; ?>';
            var caseStudyId = '<?php echo $caseStudyId; ?>';
            stepId = $(this).attr('step');
            stepNextId = $('.form-stepper-active').attr('step');
            if (view == 'V' || caseStudyId > 0) {
                navigateToFormStep(stepId);
            } else {
                if (stepId < stepNextId)
                    navigateToFormStep(stepId);
            }

        });
    </script>

    <!-- collapsible start -->
    <script>
        const collapsibles = document.querySelectorAll('.collapsible');

        collapsibles.forEach(collapsible => {
            collapsible.addEventListener('click', () => {
                const content = collapsible.nextElementSibling;
                const icon = collapsible.querySelector('i.fa-chevron-down');

                // Check if the content is currently visible
                const isActive = content.classList.contains('active');

                // Close all content sections and reset arrows
                document.querySelectorAll('.content').forEach(item => {
                    item.classList.remove('active');
                });

                document.querySelectorAll('i.fa-chevron-down').forEach(item => {
                    item.classList.remove('rotate');
                });

                // Toggle the content's active state and arrow rotation
                content.classList.toggle('active', !isActive);
                icon.classList.toggle('rotate', !isActive);
            });
        });
    </script>
    <!-- collapsible end -->

    <script type="text/javascript">
        $('input:text').keyup(function() {
            var id = $(this).attr('id');
            var textValue = $(this).val();
            if (textValue != '')
                $("input:checkbox[isparent|='" + id + "']").prop('disabled', false);
            else
                $("input:checkbox[isparent|='" + id + "']").prop('disabled', true);

        });
        $(window).load(function() {
            $(".select2_single").select2();
            $('#select2-cborotation-container').addClass('required-select2');
            // $('input:checkbox').prop('disabled', true);
            // $('#formCaseStudy :input').prop('readonly', true);
            getHospitalSite();
            var pacrCBCCheckValues = '<?php echo $pacrCBCCheckValues; ?>';
            var arr = [];

            if (pacrCBCCheckValues !== '') {
                try {
                    arr = JSON.parse(pacrCBCCheckValues);
                } catch (error) {
                    console.error('Invalid JSON string:', error);
                }
            }


            if (arr !== null && arr.length > 0) {
                for (let i = 0; i < arr.length; ++i) {
                    $("#" + arr[i]).attr("checked", true);
                    var isParent = $("#" + arr[i]).attr("isParent");
                    $("#" + isParent).addClass('abnormal');
                }
            }

            $('input[type=checkbox]').each(function() {
                var isattr = $(this).attr("isParent");
                var textValue = $("#" + isattr).val();
                if (textValue != '')
                    $(this).prop('disabled', false);
                else
                    $(this).prop('disabled', true);
            });

            var caseStudyId = '<?php echo $caseStudyId; ?>';
            if (caseStudyId > 0) {
                $('#caseStudydate').prop('disabled', true);
                $('#cborotation').prop('disabled', true);
            }

            // $('#formCaseStudy')
            //     .parsley()
            //     .on('field:validated', function() {
            //         var ok = $('.parsley-error').length === 0;
            //     })
            //     .on('form:submit', function() {
            //         $('#cbohospitalsites').prop('disabled', false);
            //         $('#caseStudydate').prop('disabled', false);
            //         $('#cborotation').prop('disabled', false);
            //         ShowProgressAnimation();
            //         return true; // Don't submit form for this demo
            //     });

            var caseStudydate = '<?php echo $caseStudydate; ?>';
            // console.log(caseStudydate);
            if (caseStudydate != '') {
                $('#caseStudydate').datetimepicker({
                    format: 'MM/DD/YYYY'
                });
                $('#caseStudydate').val(caseStudydate);
            } else {
                $('#caseStudydate').datetimepicker({
                    format: 'MM/DD/YYYY',
                    maxDate: moment()
                });
            }
            $('#ClinicianDate').datetimepicker({
                format: 'MM/DD/YYYY'
            });
            $('#schoolDate').datetimepicker({
                format: 'MM/DD/YYYY'
            });
            $('#adultAdmissionDate').datetimepicker({
                format: 'MM/DD/YYYY'
            });
            $('#pacrAdmissionDate').datetimepicker({
                format: 'MM/DD/YYYY'
            });
            $('#pacrCBGDate').datetimepicker({
                format: 'MM/DD/YYYY',
                maxDate: moment()
            });
            $('#pacrTime1').datetimepicker({
                maxDate: moment()
            });
            $('#pacrTime2').datetimepicker({
                maxDate: moment()
            });
            $('#pacrTime3').datetimepicker({
                maxDate: moment()
            });
            $('#pacrTime1').datetimepicker();
            $('#pacrTime2').datetimepicker();
            $('#pacrTime3').datetimepicker();
            // $('#gestationalAOB').datetimepicker({     format: 'MM/DD/YYYY' });

            $('#studentDOB').datetimepicker({
                format: 'MM/DD/YYYY'
            });

            var view = $('#hiddenView').val();
            if (view == 'V') {
                $('.next').text('Next');
            }

        });
        $('.showMore_6_10').click(function() {
            var islast = $(this).attr('islast');
            var listId = parseInt(islast) + 1;
            $(this).attr('islast', listId);
            // var divHtml = '<div class="row" id="row_' + listId + '" isrow="' + listId + '"> <div class="col-md-6"> <div class="form-group"> <label class="col-md-6 control-label labelcount" for="adultMedicationsUse' + listId + '">' + listId + '</label> <div class="col-md-6 col-sm-4 col-xs-12"> <input type="text" class="form-control input-md" value="" name="adultMedicationsUseList[' + listId + '][]" id="adultMedicationsUse' + listId + '"> </div> </div> </div> <div class="col-md-6"> <div class="form-group"> <div class="col-md-10 col-sm-4 col-xs-10"> <textarea type="text" class="form-control input-md" rows="1" cols="5" name="adultModificationCarePlanList[' + listId + '][]" id="adultModificationCarePlan' + listId + '"></textarea> </div> <div> <a  href="javascript:void(0);" class="deleteRow" deleteRowId="' + listId + '" ><i class="fa-solid fa-trash" style="font-size: 18px;margin-top: 6px;"></i></a> </div> </div> </div> </div>';
            var divHtml = '<div class="col-md-12 p-0" id="row_' + listId + '" isrow="' + listId + '"> <div class="col-md-3"> <div class="form-group"> <div class="col-md-12 col-sm-12 col-xs-12 mobile-block"> <label class=" control-label margin-right" for="pacrMedications' + listId + '">' + listId + '</label> <input placeholder="Enter Medication" type="text" class="form-control input-md" value="" name="pacrMedications[' + listId + '][]" id="pacrMedications' + listId + '"> </div> </div> </div> <div class="col-md-3"> <div class="form-group"> <div class="col-md-12 col-sm-12 col-xs-12"> <input placeholder="Enter Classification" type="text" class="form-control input-md" value="" name="pacrClassifications[' + listId + '][]" id="pacrClassifications' + listId + '"> </div> </div> </div> <div class="col-md-3"> <div class="form-group" > <div class="col-md-12 col-sm-12 col-xs-12"> <input placeholder="Enter Mode of Action" type="text" class="form-control input-md" value="" name="pacrModeOfctions[' + listId + '][]" id="pacrModeOfctions' + listId + '"> </div> </div> </div> <div class="col-md-3"> <div class="form-group" style="display: flex;align-items: center;"> <div class="col-md-11 col-sm-12 col-xs-12"> <input placeholder="Enter Dosage" type="text" class="form-control input-md" value="" name="pacrDosage[' + listId + '][]" id="pacrDosage' + listId + '"></div> <div> <a  href="javascript:void(0);" class="deleteRow" deleteRowId="' + listId + '" ><i class="fa fa-trash-o row-delete-icon" aria-hidden="true" style="font-size: 18px;padding-left: 6px;"></i></a> </div> </div> </div> </div>';
            $('#MedicationDiv').append(divHtml);
        });
        $(document).on('click', '.deleteRow', function() {
            var lastrowId = $('#MedicationDiv').children().last().attr('isrow');
            var deleteRowId = $(this).attr('deleteRowId');
            $('#row_' + deleteRowId).remove();
            if (deleteRowId == lastrowId)
                var lastrowId = parseInt(lastrowId) - 1;

            $('.showMore_6_10').attr('islast', lastrowId);
            var islast = $('.showMore_6_10').attr('islast');
        });

        // $('.showMore_6_10').click(function () {

        //     $('.hidecol_6').removeClass('hide');
        //     $(this).addClass('hide');
        //     $('.showLess_1').removeClass('hide');
        // });
        // $('.showLess_10_6').click(function () {
        //     $('.hidecol_6').addClass('hide');
        //     $('.showMore_6_10').removeClass('hide');
        //     $('.showLess_1').addClass('hide');
        // });

        // $('.showMore_10_15').click(function () {
        //     $('.hidecol_6, .hidecol_10').removeClass('hide');
        //     $('.showLess_1').addClass('hide');
        //     $('.showLess_2').removeClass('hide');
        //     $('.showLess_15_10').removeClass('hide');
        // });
        // $('.showLess_15_10').click(function () {

        //     $('.hidecol_10').addClass('hide');
        //     $('.showLess_1').removeClass('hide');
        //     $('.showLess_10').removeClass('hide');
        //     $(this).addClass('hide');
        // });

        $('input[type="checkbox"]').change(function() {
            // alert('Checkbox checked!');
            var isParent = $(this).attr('isParent');
            var hasAbnormal = $("#" + isParent).hasClass("abnormal")
            if (hasAbnormal == true)
                $("#" + isParent).removeClass('abnormal');
            else
                $("#" + isParent).addClass('abnormal');



        });

        $('#cborotation').change(function() {
            getHospitalSite();

        })

        function getHospitalSite() {
            var selectedOption = $("#cborotation option:selected");
            var isHospitalSite = selectedOption.attr("isHospitalSite");
            $('#cbohospitalsites').val(isHospitalSite).trigger('change');
        }
    </script>

</body>

</html>