<?php
/**
 * VoiceApiTest
 * PHP version 5
 *
 * @category Class
 * @package  ClickSend
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * ClickSend v3 API
 *
 * This is an official SDK for [ClickSend](https://clicksend.com)  Below you will find a current list of the available methods for clicksend.  *NOTE: You will need to create a free account to use the API. You can register [here](https://dashboard.clicksend.com/#/signup/step1/)..*
 *
 * OpenAPI spec version: 3.1
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * ClickSend Codegen version: 3.0.51
 */
/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Please update the test case below to test the endpoint.
 */

namespace ClickSend;

use ClickSend\Configuration;
use ClickSend\ApiException;
use <PERSON>lickSend\ObjectSerializer;
use PHPUnit\Framework\TestCase;

/**
 * VoiceApiTest Class Doc Comment
 *
 * @category Class
 * @package  ClickSend
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */
class VoiceApiTest extends TestCase
{

    /**
     * Setup before running any test cases
     */
    public static function setUpBeforeClass(): void
    {
    }

    /**
     * Setup before running each test case
     */
    public function setUp(): void
    {
    }

    /**
     * Clean up after running each test case
     */
    public function tearDown(): void
    {
    }

    /**
     * Clean up after running all test cases
     */
    public static function tearDownAfterClass(): void
    {
    }

    /**
     * Test case for voiceCancelAllPut
     *
     * Update all voice messages as cancelled.
     *
     */
    public function testVoiceCancelAllPut()
    {
    }

    /**
     * Test case for voiceCancelByMessageIdPut
     *
     * Update voice message status as cancelled.
     *
     */
    public function testVoiceCancelByMessageIdPut()
    {
    }

    /**
     * Test case for voiceHistoryExportGet
     *
     * Export voice history.
     *
     */
    public function testVoiceHistoryExportGet()
    {
    }

    /**
     * Test case for voiceHistoryGet
     *
     * Get all voice history.
     *
     */
    public function testVoiceHistoryGet()
    {
    }

    /**
     * Test case for voiceLangGet
     *
     * Get all voice languages.
     *
     */
    public function testVoiceLangGet()
    {
    }

    /**
     * Test case for voicePricePost
     *
     * Calculate voice price.
     *
     */
    public function testVoicePricePost()
    {
    }

    /**
     * Test case for voiceReceiptsGet
     *
     * Get all delivery receipts.
     *
     */
    public function testVoiceReceiptsGet()
    {
    }

    /**
     * Test case for voiceReceiptsPost
     *
     * Add a delivery receipt.
     *
     */
    public function testVoiceReceiptsPost()
    {
    }

    /**
     * Test case for voiceReceiptsReadPut
     *
     * Mark delivery receipts as read.
     *
     */
    public function testVoiceReceiptsReadPut()
    {
    }

    /**
     * Test case for voiceSendPost
     *
     * Send voice message(s).
     *
     */
    public function testVoiceSendPost()
    {
    }
}
