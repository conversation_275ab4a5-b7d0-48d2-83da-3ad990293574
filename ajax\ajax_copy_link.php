<?php
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsExternalPreceptors.php');
include('../class/clsIrr.php');
include("../class/clsSendEmails.php");
include('../class/clsSMTPSettings.php');
include("../class/class.phpmailer.php");
// // include("../class/class.smtp.php");
// echo $schoolSlug;
// print_r($_POST);
// exit;
@session_start();

$dynamicOrgUrl = BASE_PATH;
$studentFullName = '';
$rotationId = isset($_POST['rotationId']) ? $_POST['rotationId'] : '';
$evaluationId = isset($_POST['evaluationId']) ? $_POST['evaluationId'] : '';
$preceptorNum = isset($_POST['preceptorNum']) ? $_POST['preceptorNum'] : '';
$evaluationType = isset($_POST['evaluationType']) ? $_POST['evaluationType'] : '';
$checkoffId = isset($_POST['checkoffId']) ? $_POST['checkoffId'] : '';
$schoolTopicId = isset($_POST['schoolTopicId']) ? $_POST['schoolTopicId'] : '';
$preceptorId = isset($_POST['preceptorId']) ? DecodeQueryData($_POST['preceptorId'])  : '';
$studentId = isset($_POST['studentId']) ? DecodeQueryData($_POST['studentId'])  : '';
$SchoolId = isset($_POST['SchoolId']) ? ($_POST['SchoolId'])  : '';
$getStudentCoarcId = isset($_POST['getStudentCoarcId']) ? ($_POST['getStudentCoarcId'])  : '';
$coarcSurveyMasterId = isset($_POST['coarcSurveyMasterId']) ? ($_POST['coarcSurveyMasterId'])  : '';
$clinicianId = isset($_POST['clinicianId']) ? ($_POST['clinicianId'])  : '';
$getClinicianCoarcId = isset($_POST['getClinicianCoarcId']) ? ($_POST['getClinicianCoarcId'])  : '';
$irrdetailId = isset($_POST['irrdetailId']) ? ($_POST['irrdetailId'])  : '';
$irrMasterId = isset($_POST['irrMasterId']) ? ($_POST['irrMasterId'])  : '';
$email = isset($_POST['email']) ? ($_POST['email'])  : '';
$isSendEmail = isset($_POST['isSendEmail']) ? ($_POST['isSendEmail'])  : 0;
$isEvalTypeLabel = '';
$activityId = isset($_POST['activityId']) ? $_POST['activityId'] : '';
$soapNoteId = isset($_POST['soapNoteId']) ? $_POST['soapNoteId'] : '';

$objSendEmails = new clsSendEmails($currentSchoolId);
// //For Save Preceptor
// $objExternalPreceptors = new clsExternalPreceptors();
// $objExternalPreceptors->mobile_num = $preceptorNum;
// $preceptorId = $objExternalPreceptors->SavePreceptors();

//Get schoolType
$objDB = new clsDB();
$isActiveCheckoff = $objDB->GetSingleColumnValueFromTable('schools', 'isActiveCheckoffForStudent', 'schoolId', $currentSchoolId);
unset($objDB);

$redirectUrl = '';
if ($evaluationType == 'dailyEval') {

	$isEvalTypeLabel = 'Daily/Weekly Evaluation';
	$URL = $dynamicOrgUrl . '/school/' . $schoolSlug . '/clinician/dailyEval.html?studentDailyMasterId=' . EncodeQueryData($evaluationId) . '&rotationId=' . EncodeQueryData($rotationId) . '&preceptorNum=' . EncodeQueryData($preceptorNum) . '&preceptorId=' . EncodeQueryData($preceptorId);

	// Create TinyUrl
	$randomUrl = getTinyUrl($URL);
	$redirectUrl = $dynamicOrgUrl . '/redirect/' . EncodeQueryData($randomUrl);
} else if ($evaluationType == 'formative') {
	$isEvalTypeLabel = 'Formative Evaluation';
	$URL = $dynamicOrgUrl . '/school/' . $schoolSlug . '/clinician/formative.html?studentFormativeMasterId=' . EncodeQueryData($evaluationId) . '&formativerotationid=' . EncodeQueryData($rotationId) . '&preceptorNum=' . EncodeQueryData($preceptorNum) . '&preceptorId=' . EncodeQueryData($preceptorId);
	$randomUrl = getTinyUrl($URL);
	$redirectUrl = $dynamicOrgUrl . '/redirect/' . EncodeQueryData($randomUrl);
} else if ($evaluationType == 'summative') {

	$isEvalTypeLabel = 'Summative Evaluation';
	$URL = $dynamicOrgUrl . '/school/' . $schoolSlug . '/clinician/summative.html?studentSummativeMasterId=' . EncodeQueryData($evaluationId) . '&summativerotationid=' . EncodeQueryData($rotationId) . '&preceptorNum=' . EncodeQueryData($preceptorNum) . '&preceptorId=' . EncodeQueryData($preceptorId);
	$randomUrl = getTinyUrl($URL);
	$redirectUrl = $dynamicOrgUrl . '/redirect/' . EncodeQueryData($randomUrl);
} else if ($evaluationType == 'midterm') {

	$isEvalTypeLabel = 'Midterm Evaluation';
	$URL = $dynamicOrgUrl . '/school/' . $schoolSlug . '/clinician/midterm.html?studentMidtermMasterId=' . EncodeQueryData($evaluationId) . '&midtermrotationid=' . EncodeQueryData($rotationId) . '&preceptorNum=' . EncodeQueryData($preceptorNum) . '&preceptorId=' . EncodeQueryData($preceptorId);
	$randomUrl = getTinyUrl($URL);
	$redirectUrl = $dynamicOrgUrl . '/redirect/' . EncodeQueryData($randomUrl);

	// sendSMS('+1'.$preceptorNum,$body);
} else if ($evaluationType == 'checkoff') {

	$isEvalTypeLabel = 'Checkoff';
	if ($isActiveCheckoff == 2)
		$URL = $dynamicOrgUrl . '/school/' . $schoolSlug . '/student/addusafcheckoff.html?schoolTopicId=' . EncodeQueryData($schoolTopicId) . '&checkoffId=' . EncodeQueryData($checkoffId) . '&preceptorNum=' . EncodeQueryData($preceptorNum) . '&preceptorId=' . EncodeQueryData($preceptorId);
	else
		$URL = $dynamicOrgUrl . '/school/' . $schoolSlug . '/student/addcheckoff.html?schoolTopicId=' . EncodeQueryData($schoolTopicId) . '&checkoffId=' . EncodeQueryData($checkoffId) . '&preceptorNum=' . EncodeQueryData($preceptorNum) . '&preceptorId=' . EncodeQueryData($preceptorId);
	// Create TinyUrl
	$randomUrl = getTinyUrl($URL);
	$redirectUrl = $dynamicOrgUrl . '/redirect/' . EncodeQueryData($randomUrl);
} else if ($evaluationType == 'floorTherapy') {
	$isEvalType = 0;
	$isEvalTypeLabel = 'Floor Therapy Evaluation';
	$URL = $dynamicOrgUrl . '/school/' . $schoolSlug . '/clinician/addFloorTherapyAndICUEvaluation.html?studentMasterId=' . EncodeQueryData($evaluationId) . '&rotationId=' . EncodeQueryData($rotationId) . '&preceptorNum=' . EncodeQueryData($preceptorNum) . '&isEvalType=' . EncodeQueryData($isEvalType) . '&preceptorId=' . EncodeQueryData($preceptorId);

	// Create TinyUrl
	$randomUrl = getTinyUrl($URL);
	$redirectUrl = $dynamicOrgUrl . '/redirect/' . EncodeQueryData($randomUrl);
} else if ($evaluationType == 'irr') {


	$isEvalTypeLabel = 'IRR';
	$URL = $dynamicOrgUrl . '/school/' . $schoolSlug . '/clinician/addirr.html?irrMasterId=' . EncodeQueryData($irrMasterId) . '&preceptorId=' . EncodeQueryData($preceptorId) . '&preceptorNum=' . EncodeQueryData($preceptorNum) . '&irrDetailId=' . EncodeQueryData($irrdetailId) . '&schoolTopicId=' . EncodeQueryData($schoolTopicId);

	// Create TinyUrl
	$randomUrl = getTinyUrl($URL);
	$redirectUrl = $dynamicOrgUrl . '/redirect/' . EncodeQueryData($randomUrl);
} else if ($evaluationType == 'student') {
	$objDB = new clsDB();

	$sql = "Select * From student Where studentId=" . $studentId;
	$row = $objDB->GetDataRow($sql);

	$Username = stripslashes($row['username']);

	$isEvalTypeLabel = 'Student CoARC Survey';
	$URL = $dynamicOrgUrl . '/school/' . $schoolSlug . '/student/login_CoARC_Survey.html?studentId=' . EncodeQueryData($studentId) . '&schoolId=' . EncodeQueryData($SchoolId) . '&coarcId=' . EncodeQueryData($getStudentCoarcId) . '&coarcSurveyMasterId=' . EncodeQueryData($coarcSurveyMasterId) . '&userName=' . $Username;

	// Create TinyUrl
	$randomUrl = getTinyUrl($URL);
	$redirectUrl = $dynamicOrgUrl . '/redirect/' . EncodeQueryData($randomUrl);
} else if ($evaluationType == 'personnel') {
	$objDB = new clsDB();
	$sql = "Select * From clinician Where clinicianId=" . $clinicianId;
	// 	 echo $sql; exit;
	$row = $objDB->GetDataRow($sql);
	$Username = stripslashes($row['username']);


	$isEvalTypeLabel = 'Personal CoARC Survey';
	$URL = $dynamicOrgUrl . '/school/' . $schoolSlug . '/clinician/login_CoARC_Survey.html?clinicianId=' . EncodeQueryData($clinicianId) . '&personnelcoarcId=' . EncodeQueryData($getClinicianCoarcId) . '&coarcSurveyMasterId=' . EncodeQueryData($coarcSurveyMasterId) . '&userName=' . $Username . '&schoolId=' . EncodeQueryData($SchoolId);

	$randomUrl = getTinyUrl($URL);
	$redirectUrl = BASE_PATH . '/redirect/' . EncodeQueryData($randomUrl);
} else if ($evaluationType == 'graduate') {
	$objDB = new clsDB();
	$sql = "Select * From student Where studentId=" . $studentId;
	// 	 echo $sql; exit;
	$row = $objDB->GetDataRow($sql);
	$Username = stripslashes($row['username']);


	$isEvalTypeLabel = 'Graduate CoARC Survey';
	$URL = $dynamicOrgUrl . '/school/' . $schoolSlug . '/student/login_CoARC_Survey.html?studentId=' . EncodeQueryData($studentId) . '&coarcId=' . EncodeQueryData($getStudentCoarcId) . '&coarcSurveyMasterId=' . EncodeQueryData($coarcSurveyMasterId) . '&userName=' . $Username . '&schoolId=' . EncodeQueryData($SchoolId) . '&type=G';
	// echo $URL;exit;
	$randomUrl = getTinyUrl($URL);
	$redirectUrl = BASE_PATH . '/redirect/' . EncodeQueryData($randomUrl);
} else if ($evaluationType == 'employer') {
	$objDB = new clsDB();
	$sql = "Select * From student Where studentId=" . $studentId;
	// 	 echo $sql; exit;
	$row = $objDB->GetDataRow($sql);
	$Username = stripslashes($row['username']);


	$isEvalTypeLabel = 'Employer CoARC Survey';
	$URL = $dynamicOrgUrl . '/school/' . $schoolSlug . '/student/login_CoARC_Survey.html?studentId=' . EncodeQueryData($studentId) . '&coarcId=' . EncodeQueryData($getStudentCoarcId) . '&coarcSurveyMasterId=' . EncodeQueryData($coarcSurveyMasterId) . '&userName=' . $Username . '&schoolId=' . EncodeQueryData($SchoolId) . '&type=E';

	$randomUrl = getTinyUrl($URL);
	$redirectUrl = BASE_PATH . '/redirect/' . EncodeQueryData($randomUrl);
} else if ($evaluationType == 'privateLink') {

	// $isEvalTypeLabel = 'Employer CoARC Survey';
	$URL = $dynamicOrgUrl . '/school/' . $schoolSlug . '/admin/studentRegister.html?email=' . EncodeQueryData($email) . '&isPrivate=' . EncodeQueryData(1);

	$randomUrl = getTinyUrl($URL);
	$redirectUrl = BASE_PATH . '/redirect/' . EncodeQueryData($randomUrl);
} else if ($evaluationType == 'activitySheet') {
	$isEvalType = 0;
	$isEvalTypeLabel = 'Activity Sheet';
	// $URL = $dynamicOrgUrl . '/school/' . $schoolSlug . '/clinician/addFloorTherapyAndICUEvaluation.html?studentMasterId=' . EncodeQueryData($evaluationId) . '&rotationId=' . EncodeQueryData($rotationId) . '&preceptorNum=' . EncodeQueryData($preceptorNum) .'&isEvalType='.EncodeQueryData($isEvalType). '&preceptorId=' . EncodeQueryData($preceptorId);
	$URL = $dynamicOrgUrl . '/school/' . $schoolSlug . '/student/activitySheet.html?editactivityid=' . EncodeQueryData($activityId) . '&rotationId=' . EncodeQueryData($rotationId) . '&preceptorNum=' . EncodeQueryData($preceptorNum) .  '&isEvalType=' . EncodeQueryData($isEvalType) . '&preceptorId=' . EncodeQueryData($preceptorId) . '&isSendToExternalPreceptor=1';

	// Create TinyUrl
	$randomUrl = getTinyUrl($URL);
	$redirectUrl = $dynamicOrgUrl . '/redirect/' . EncodeQueryData($randomUrl);
}
else if ($evaluationType == 'soapnote') {
	// echo $studentId; exit;
	$isEvalType = 0;
	$isEvalTypeLabel = 'Soap Note';
	// $URL = $dynamicOrgUrl . '/school/' . $schoolSlug . '/clinician/addFloorTherapyAndICUEvaluation.html?studentMasterId=' . EncodeQueryData($evaluationId) . '&rotationId=' . EncodeQueryData($rotationId) . '&preceptorNum=' . EncodeQueryData($preceptorNum) .'&isEvalType='.EncodeQueryData($isEvalType). '&preceptorId=' . EncodeQueryData($preceptorId);
    $URL = $dynamicOrgUrl . '/school/' . $schoolSlug . '/student/addSoapNote.html?editsoapNoteId=' . EncodeQueryData($soapNoteId) . '&rotationId=' . EncodeQueryData($rotationId) . '&preceptorNum=' . EncodeQueryData($preceptorNum) .  '&isEvalType=' . EncodeQueryData($isEvalType) . '&preceptorId=' . EncodeQueryData($preceptorId) . '&isSendToExternalPreceptor=1';

	// Create TinyUrl
	$randomUrl = getTinyUrl($URL);
	$redirectUrl = $dynamicOrgUrl . '/redirect/' . EncodeQueryData($randomUrl);
}
// echo $redirectUrl; exit;

if ($isSendEmail) {
	if($evaluationType == 'activitySheet' || $evaluationType == 'checkoff' || $evaluationType == 'dailyEval' || $evaluationType == 'floorTherapy' || $evaluationType == 'midterm' || $evaluationType == 'summative' || $evaluationType == 'formative'){
		
		$studentFirstName = isset($_SESSION['loggedStudentFirstName']) ? $_SESSION['loggedStudentFirstName'] : '';
		$studentLastName = isset($_SESSION['loggedStudentLastName']) ? $_SESSION['loggedStudentLastName'] : '';
		$studentFullName = ($studentFirstName != '' && $studentLastName != '') ? $studentFirstName . ' ' . $studentLastName : '';
	}
	$objSendEmails->sendLinkToEmail($email, $isEvalTypeLabel, $redirectUrl, '', $studentFullName);
	unset($objSendEmails);
} else {
	echo $redirectUrl;
}
