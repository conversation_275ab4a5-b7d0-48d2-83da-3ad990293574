<?php

include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsSchool.php');
include('../class/clsHospitalSite.php');
include('../class/clsStudent.php');
include('../class/clsClinician.php');
include('../class/clsRotation.php');
include('../class/clsLocations.php');
include('../class/clsJournal.php');
include('../class/clsSemester.php');
include('../class/clsProcedureCategory.php');
include('../class/clsStudentRankMaster.php');
include('../class/clscheckoff.php');
include('../class/clsschoolclinicalsiteunit.php');
include('../class/clsActivitysheet.php');
include('../class/clsSoapNote.php');
include('../class/clsCourses.php');
include('../setRequest.php');

$isActiveCheckoff = $_SESSION["isActiveCheckoff"];

$title = "Report";
$schoolId = $currentSchoolId;
$courseId = 0;
$locationId = 0;
$rotationId = 0;
$semesterId = 0;
$studentId  = 0;
$rankId  = 0;
$clinicianId  = 0;
$proceduteCountId  = 0;
$hospitalSiteId  = 0;
if (isset($_GET['schoolId'])) //Edit Mode
{
	$schoolId = $_GET['schoolId'];
	$schoolId = base64_decode($schoolId);
} else {
	$schoolId = $currentSchoolId;
}
// echo $schoolId;
//--------------------REPORT DATA POST START-----------------------------------------


//--------------------REPORT DATA POST END -----------------------------------------	
//Reports All Drop Downs      
$objLocations = new clsLocations();
$locations = $objLocations->GetAlllocation($schoolId);
unset($objLocations);

$objRotation = new clsRotation();
$rotation = $objRotation->GetRotationForReport($schoolId);
$subrotation = $objRotation->GetSubRotationForReport($schoolId);

unset($objRotation);

$objSemester = new clsSemester();
$Semester = $objSemester->GetAllSemester($schoolId);
unset($objSemester);

$objStudents = new clsStudent();
$SchoolStudents = $objStudents->GetSchoolStudents($currentSchoolId);
unset($objStudents);

$objStudentRankMaster = new clsStudentRankMaster();
$studentranks = $objStudentRankMaster->GetAllStudentRankBySchool($schoolId);
unset($objStudentRankMaster);

$objClinician = new clsClinician();
$clinicians = $objClinician->GetAllSchoolClinicians($schoolId);
unset($objClinician);
$objProcedure = new clsProcedureCategory();
$ProcedureCount = $objProcedure->GetProcedureCountDetailsForReport();
unset($objProcedure);

$objHospitalSite = new clsHospitalSite();
$isActiveHospital = 1;
$hospitalSite = $objHospitalSite->GetAllHospitalSite($currentSchoolId, $isActiveHospital);
unset($objHospitalSite);

//For Checkoff Topic
$objcheckoff = new clscheckoff();
$topic = $objcheckoff->GetAllCheckoffTopic($currentSchoolId);
unset($objcheckoff);

//Get hospital Site Unit
$objhospitalsiteunit = new clsschoolclinicalsiteunit;
$hospitalsiteunit = $objhospitalsiteunit->GetAllClinicalSiteUnit($currentSchoolId);
unset($objhospitalsiteunit);

//GetCouses
$objCourses = new clsCourses();
$courses = $objCourses->GetCousesByLocation($currentSchoolId);
$totalCourses = ($courses != '') ? mysqli_num_rows($courses) : 0;
unset($objCourses);

//Soap Note Module - Enabled/Disabled through superadmin -> admin 
$objDB = new clsDB();
$soapNote = $objDB->GetSingleColumnValueFromTable('schoolsettings', 'status', 'schoolId', $schoolId, 'type', 'soapNote');
unset($objDB);

?>
<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
	<!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
	<title><?php echo ($title); ?></title>
	<?php include('includes/headercss.php'); ?>

	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">

	<style>
		.form-group {
			margin-bottom: 10px !important;
		}

		.form-control {
			height: 45px;
		}

		.select2-container--default .select2-selection--multiple {
			min-height: 45px;
			background-color: #f6f9f9 !important;
			border-radius: 12px !important;
			box-shadow: none !important;
			border: none !important;
		}

		.select2-container--default .select2-selection--multiple .select2-selection__rendered li {
			text-wrap: wrap;
		}

		.select2-hidden-accessible~.select2-container--disabled {
			min-height: 45px !important;
			height: fit-content !important;
		}

		.select2-container--default .select2-selection--multiple .select2-selection__rendered {
			box-sizing: border-box;
			list-style: none;
			margin: 0;
			padding: 10px 10px;
			width: 100%;
		}

		.select2-container .select2-search--inline .select2-search__field {
			margin-top: 0 !important;
		}

		.formSubHeading {
			border-bottom: 2px solid #d9d6d657;
			padding: 3px 0;
			/* margin: 10px 15px 20px; */
			position: relative;
		}

		input[type="file"] {
			background-color: #fff !important;
		}



		@media screen and (max-width: 500px) {
			.formSubHeading {
				font-size: 16px;
				margin-bottom: 12px;
				padding-bottom: 0;
			}

			.breadcrumb-bg {
				margin-bottom: 5px;
			}
		}
	</style>
</head>

<body>
	<?php include('includes/header.php'); ?>

	<div class="row margin_zero breadcrumb-bg">
		<div class="container">
			<div class="pull-left">
				<ol class="breadcrumb">
					<li><a href="dashboard.html">Home</a></li>
					<li class="active">Reports</li>

				</ol>
			</div>

			<div class="pull-right" style="margin-top:5px">
				<a href="adminreportlist.html" class="addCommentpopup " data-organizationid="">Custom Report</a>
			</div>

		</div>
	</div>
	<div class="container">
		<div class="pageheading"></div>
		<form id="frmreport" data-parsley-validate class="form-horizontal" method="POST" action="reportsubmit.html" enctype="multipart/form-data">
			<div class="formSubHeading">FILTERS </div>
			<div class="row">
				<div class="col-md-6">
					<!-- Text input-->
					<div class="form-group">
						<label class="col-md-12 control-label" for="cboreporttype">Report Type</label>
						<div class="col-md-12">
							<select id="cboreporttype" name="cboreporttype" class="form-control input-md select2_single input-required" required data-parsley-errors-container="#error-cboreporttype">
								<option value="" selected>Select Report</option>
								<option value="Absence">Absence</option>
								<option value="Accreditation" id="Accreditation">Accreditation Tracking Information</option>
								<option value="ActivitySheet" id="ActivitySheet">Activity Sheet</option>
								<option value="AdditionalContactInformation">Additional Contact Information</option>
								<option value="Attendance">Attendance Detail</option>
								<option value="Attendance_Daily_Weekly">Attendance Daily/Weekly</option>
								<option value="AttendanceSummary">Attendance Summary</option>
								<option value="CallOff">Call Off</option>
								<option value="ClinicianAttendance">Clinician Attendance Detail</option>
								<option value="Certification">Certification Log</option>
								<option value="CaseStudy">Case Study Report</option>
								<option value="Checkoff">Checkoff</option>
								<option value="CheckoffByCourses">Checkoff by Courses</option>
								<option value="CI_Eval">CI Evaluation</option>
								<option value="ClinicalSiteUnit">Clinical Site Unit</option>
								<option value="DailyEval">Daily/Weekly Evaluation</option>
								<option value="DailyDetail">Daily/Weekly Detail Report</option>
								<option value="DCE_Eval">DCE CI Evaluation</option>
								<option value="Dr_Points">Dr.Interaction</option>
								<option value="Early_clockOut">Early Clock Out Report</option>
								<option value="Equipments">Equipments</option>
								<option value="Formative">Formative Evaluation</option>
								<option value="Journal">Journal</option>
								<option value="Late_clockIn">Late Clock In Report</option>
								<option value="Midterm">Midterm Evaluation</option>
								<?php if ($isActiveCheckoff == 0) { ?>
									<option value="Mastery">Mastery Evaluation</option>
								<?php } ?>
								<option value="No_clockout">No Clock Out Report</option>
								<option value="P_Eval">P Evaluation</option>
								<option value="Preceptor_Checkoff">Preceptor Checkoff Report</option>
								<option value="Procidure_Count">Procedure Count</option>
								<option value="Procidure_Count_Summary">Procedure Count Summary</option>
								<?php if ($schoolId == 75 || $schoolId == 123 && $isActiveCheckoff != 0) { ?>
									<option value="PEF_I_Evaluation">PEF I Evaluation</option>
									<option value="PEF_II_Evaluation">PEF II Evaluation</option>
								<?php } ?>
								<option value="Procedure_Details">Procedure Details</option>
								<option value="Site_Eval">Site Evaluation</option>
								<?php if ($soapNote) { ?>
									<option value="SoapNote" id="SoapNote">Soap Note</option>
								<?php } ?>
								<option value="Student">Student </option>
								<option value="StudentDetails">Student Portfolio</option>
								<option value="Summative">Summative Evaluation</option>
								<option value="Time_Exception">Time Exception</option>
								<option value="Affiliate">Upcoming Affiliate Agreement Expire</option>
								<!--option value="IRR Report">IRR Report</option-->
							</select>
							<div id="error-cboreporttype"></div>
						</div>
					</div>
					<!---/// End 1st dd ///-------->
					<!---/// End 2nd dd ///-------->
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-12 control-label" for="cbosemester">Semester</label>
						<div class="col-md-12">
							<select id="cbosemester" name="cbosemester[]" multiple="multiple" class="form-control input-md select2_tags">
								<?php
								if ($Semester != "") {
									while ($row = mysqli_fetch_assoc($Semester)) {
										$selsemesterId  = $row['semesterId'];
										$name  = stripslashes($row['title']);

								?>
										<option value="<?php echo ($selsemesterId); ?>" <?php if ($semesterId == $selsemesterId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
								<?php
									}
								}
								?>
							</select>
							<input type="hidden" id="rotation_hidden" name="rotation_hidden" value="">
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-12 control-label" for="">Start Date</label>
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class='input-group date w-full' id='startDateAll' style="position: relative;">
								<input type='text' name="startDate" id="startDate" class="form-control input-md  rotation_date dateInputFormat" value="" data-parsley-errors-container="#error-startDate" placeholder="MM-DD-YYYY" />
								<span class="input-group-addon calender-icon">
									<span class="glyphicon glyphicon-calendar"></span>
								</span>
							</div>
						</div>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-12 control-label" for="">End Date</label>
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class='input-group date w-full' style="position: relative;">
								<input type='text' name="endDate" id="endDate" class="form-control input-md  rotation_date dateInputFormat" value="" data-parsley-errors-container="#error-endDate" placeholder="MM-DD-YYYY" />
								<span class="input-group-addon calender-icon">
									<span class="glyphicon glyphicon-calendar"></span>
								</span>
							</div>
						</div>
					</div>
				</div>
			</div>
			<!---// Class Row 1st end//---->
			<div class="row">
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-12 control-label" for="cborotation">Rotation</label>
						<div class="col-md-12">
							<select id="cborotation" name="cborotation" class="form-control input-md select2_single input-required cborotation " onchange="getSubRotationList(this);" data-parsley-errors-container="#error-cborotation">
								<option value="" selected>Select</option>
								<?php
								if ($rotation != "") {
									while ($row = mysqli_fetch_assoc($rotation)) {
										$selrotationId  = $row['rotationId'];
										$name  = stripslashes($row['title']);

								?>
										<option value="<?php echo ($selrotationId); ?>" <?php if ($rotationId == $selrotationId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
								<?php
									}
								}
								?>
							</select>
							<div id="error-cborotation"></div>
							<input type="hidden" id="rotation_hidden" name="rotation_hidden" value="">
						</div>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-12 control-label" for="subcborotation">Hospital Site</label>
						<div class="col-md-12">
							<select id="subcborotation" name="subcborotation[]" multiple="multiple" class="form-control input-md select2_tags">
							</select>
						</div>
					</div>
				</div>
			</div>

			<div class="row ">
				<div class="col-md-6 ">
					<div class="form-group" id="cboCourseDiv">
						<label class="col-md-4 control-label" for="cboCourse">Course</label>
						<div class="col-md-8">
							<select id="cboCourse" name="cboCourse" class="form-control input-md select2_single input-required" data-parsley-errors-container="#error-cboCourse">
								<option value="" selected>Select</option>
								<?php
								if ($totalCourses) {
									while ($rowCourses = mysqli_fetch_assoc($courses)) {
										$selcourseId  = $rowCourses['courseId'];
										$courseName  = stripslashes($rowCourses['title']);

								?>
										<option value="<?php echo ($selcourseId); ?>" <?php if ($courseId == $selcourseId) { ?> selected="true" <?php } ?>><?php echo ($courseName); ?></option>
								<?php
									}
								}
								?>
							</select>
							<div id="error-cboCourse"></div>
						</div>
					</div>
				</div>

			</div>
			<div class="formSubHeading">ADDITIONAL FILTERS </div>
			<div class="row">
				<div class="col-md-6">
					<!-- Text input-->
					<div class="form-group">
						<label class="col-md-12 control-label" for="cboindividualstudent">Individual Student</label>
						<div class="col-md-12">
							<select id="cboindividualstudent" name="cboindividualstudent[]" multiple="multiple" class="form-control input-md select2_tags">
								<?php
								if ($SchoolStudents != "") {
									while ($row = mysqli_fetch_assoc($SchoolStudents)) {
										$sellocationId  = $row['studentId'];
										$firstName  = stripslashes($row['firstName']);
										$lastName  = stripslashes($row['lastName']);
										$name  = $firstName . ' ' . $lastName;

								?>
										<option value="<?php echo ($sellocationId); ?>" <?php if ($locationId == $sellocationId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
								<?php
									}
								}
								?>
							</select>
						</div>
					</div>
					<!---/// End 1st dd ///-------->
					<div class="form-group">
						<label class="col-md-12 control-label" for="cbostudentrank">Student Ranking</label>
						<div class="col-md-12">
							<select id="cbostudentrank" name="cbostudentrank" class="form-control input-md select2_single">
								<option value="" selected>Select</option>
								<?php
								if ($studentranks != "") {
									while ($row = mysqli_fetch_assoc($studentranks)) {
										$selrankId  = $row['rankId'];
										$name  = stripslashes($row['title']);

								?>
										<option value="<?php echo ($selrankId); ?>" <?php if ($rankId == $selrankId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
								<?php
									}
								}
								?>
							</select>
						</div>
					</div>
					<!---/// End  dd ///-------->
					<div class="form-group">
						<label class="col-md-12 control-label" for="cboevaluator">Clinical Instructor</label>
						<div class="col-md-12">
							<select id="cboevaluator" name="cboevaluator" class="form-control input-md select2_single">
								<option value="" selected>Select</option>
								<?php
								if ($clinicians != "") {
									while ($row = mysqli_fetch_assoc($clinicians)) {
										$selclinicianId = $row['clinicianId'];
										$firstName  = stripslashes($row['firstName']);
										$lastName  = stripslashes($row['lastName']);
										$name  = $firstName . ' ' . $lastName;

								?>
										<option value="<?php echo ($selclinicianId); ?>" <?php if ($clinicianId == $selclinicianId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
								<?php
									}
								}
								?>
							</select>
						</div>
					</div>
					<div class="form-group">
						<label class="col-md-12 control-label" for="checkoffTopic">Checkoff Topic</label>
						<div class="col-md-12">
							<select id="checkoffTopic" name="checkoffTopic" class="form-control input-md select2_single">
								<option value="" selected>Select</option>
								<?php
								if ($topic != "") {
									while ($row = mysqli_fetch_assoc($topic)) {
										$selTopicId  = $row['schoolTopicId'];
										$name  = stripslashes($row['schooltitle']);
										$loopRotationId  = $row['rotationId'];

								?>
										<option value="<?php echo ($selTopicId); ?>"><?php echo ($name); ?></option>
								<?php
									}
								}
								?>
							</select>
						</div>
					</div>

					<!-- For Checkoof by courses report only -->
					<div id="courseTopicCompletionDiv" class="form-group hide">
						<label class="col-md-12 control-label" for="courseTopicCompletion">Completion</label>
						<div class="col-md-12">
							<select id="courseTopicCompletion" name="courseTopicCompletion" class="form-control input-md select2_single" style="width: 100%">
								<option value="" selected>Select</option>
								<option value="1">Completed</option>
								<option value="2">Not Completed</option>

							</select>
						</div>
					</div>

				</div>
				<!---/// End col-md-6 ///-------->
				<div class="col-md-6">
					<!-- Text input-->
					<div class="form-group">
						<label class="col-md-12 control-label" for="cboskill">Skill</label>
						<div class="col-md-12">
							<select id="cboskill" name="cboskill" class="form-control input-md select2_single">
								<option value="" selected>Select</option>
								<?php
								if ($ProcedureCount != "") {
									while ($row = mysqli_fetch_assoc($ProcedureCount)) {
										$selproceduteCountId = $row['proceduteCountId'];
										$name  = stripslashes($row['procedures']);


								?>
										<option value="<?php echo ($selproceduteCountId); ?>" <?php if ($proceduteCountId == $selproceduteCountId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
								<?php
									}
								}
								?>
							</select>
						</div>
					</div>
					<!---/// End 1st dd ///-------->
					<div class="form-group">
						<label class="col-md-12 control-label" for="cbolocation">School Location</label>
						<div class="col-md-12">
							<select id="cbolocation" name="cbolocation" class="form-control input-md select2_single">
								<option value="" selected>Select</option>
								<?php
								if ($locations != "") {
									while ($row = mysqli_fetch_assoc($locations)) {
										$sellocationId  = $row['locationId'];
										$name  = stripslashes($row['title']);

								?>
										<option value="<?php echo ($sellocationId); ?>" <?php if ($locationId == $sellocationId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
								<?php
									}
								}
								?>
							</select>
							<input type="hidden" id="location_hidden" name="location_hidden" value="">
						</div>
					</div>
					<div class="form-group">
						<label class="col-md-12 control-label" for="cbohospitalsite">Hospital/Site</label>
						<div class="col-md-12">
							<select id="cbohospitalsite" name="cbohospitalsite" class="form-control input-md select2_single">
								<option value="" selected>Select</option>
								<?php
								if ($hospitalSite != "") {
									while ($row = mysqli_fetch_assoc($hospitalSite)) {
										$selhospitalSiteId  = $row['hospitalSiteId'];
										$name  = stripslashes($row['title']);

								?>
										<option value="<?php echo ($selhospitalSiteId); ?>" <?php if ($hospitalSiteId == $selhospitalSiteId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
								<?php
									}
								}
								?>
							</select>
						</div>
					</div>
					<div class="form-group">
						<label class="col-md-12 control-label" for="cbohospitalsiteunits">Clinical Site Unit</label>
						<div class="col-md-12">
							<select id="cbohospitalsiteunits" name="cbohospitalsiteunits" class="form-control input-md required-input select2_single" placeholder="Select Hospital site Unit">
								<option value="" selected>Select</option>
								<?php
								if ($hospitalsiteunit != "") {
									while ($row = mysqli_fetch_assoc($hospitalsiteunit)) {
										$selhospitalSiteUnitId  = $row['schoolClinicalSiteUnitId'];
										$name  = stripslashes($row['title']);

								?>
										<option value="<?php echo ($selhospitalSiteUnitId); ?>"><?php echo ($name); ?>
										</option>
								<?php
									}
								}
								?>
							</select>
						</div>
					</div>
					<!---/// End  dd ///-------->
				</div>
				<!---/// End col-md-6 ///-------->
			</div>
			<!---/// End 2nd row ///-------->
			<div class="formSubHeading">SORTING OPTIONS</div>
			<div class="row">
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-12 control-label" for="AscDesc">Order By</label>
						<div class="col-md-12">
							<select id="AscDesc" name="AscDesc" class="form-control input-md select2_single">
								<option value="0" selected>Ascending</option>
								<option value="1">Descending</option>
							</select>
							<input type="hidden" id="sordorder_hidden" name="sordorder_hidden" value="">
						</div>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-12 control-label" for="sordorder">Sort Order</label>
						<div class="col-md-12">
							<select id="sordorder" name="sordorder" class="form-control input-md select2_single">
								<option value="0" selected>Default</option>
								<option value="12">Approved Hours</option>
								<option value="4">End Date</option>
								<option value="13">Courses</option>
								<option value="7">Clinical Instructor</option>
								<option value="8">Hospital Site</option>
								<option value="9">Hospital Site Unit</option>
								<option value="11">Original Hours</option>
								<option value="5">Rotation</option>
								<option value="16">Procedure Count</option>
								<option value="6">School</option>
								<option value="3">Start Date</option>
								<option value="14">Student First Name</option>
								<option value="15">Student Last Name</option>
								<option value="1">Student Full Name</option>
								<option value="2">Student Ranking</option>
								<option value="10">Hospital Site</option>
							</select>
							<input type="hidden" id="sordorder_hidden" name="sordorder_hidden" value="">
						</div>
					</div>
				</div>
			</div>
			<div class="form-group m-0 mt-20">
				<!-- <label class="col-md-2 control-label"></label> -->
				<div class="col-md-12" style="display: flex; justify-content: center; margin: 0 0 20px 0;gap: 15px;">
					<button id="btnSubmit" name="btnSubmit" class="btn btn-success">Show</button>
				</div>
			</div>
		</form>
	</div>

	<?php include('includes/footer.php'); ?>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/jquery.cascadingdropdown.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>





	<script type="text/javascript">
		alertify.defaults.transition = "slide";
		alertify.defaults.theme.ok = "btn btn-primary";
		alertify.defaults.theme.cancel = "btn btn-danger";
		alertify.defaults.theme.input = "form-control";

		$(window).load(function() {

			$('#cborotation').trigger('change');
			$('#cboevaluator').trigger('change');

			// $('form').find("input[type=text], textarea").val("");
			// $(".select2_single").val(null).trigger("change"); 

			$(".select2_single").select2();
			$('#select2-cboreporttype-container').addClass('required-select2');
			$(".select2_tags").select2({
				'placeholder': 'Select'
			});


			$('#cborotation').change(function() {
				var x = $('#cborotation option:selected').text();
				$('#rotation_hidden').val(x);
			});

			$('#cbolocation').change(function() {
				var x = $('#cbolocation option:selected').text();
				$('#location_hidden').val(x);
			});


			$('#frmreport').parsley().on('field:validated', function() {
					var ok = $('.parsley-error').length === 0;
				})
				.on('form:submit', function() {
					ShowProgressAnimation();
					return true; // Don't submit form for this demo
				});




			$('#startDate').datetimepicker({
					format: 'MM/DD/YYYY',
				})
				.on('dp.change', function(e) {

					$('#endDate').val('');
					$(this).data("DateTimePicker").hide();
					var exporttype = $("#cboreporttype").val();
					if (exporttype == 'Attendance_Daily_Weekly') {
						var incrementDay = moment(new Date(e.date));
						incrementDay.add(+6, 'days');
						$('#endDate').val(incrementDay.format('MM/DD/YYYY'));
						$('#endDate').data('DateTimePicker').minDate(moment(new Date(e.date)));
						$('#endDate').data('DateTimePicker').maxDate(incrementDay);
						$(this).data("DateTimePicker").hide();
					}
				});

			$('#endDate').datetimepicker({
					format: 'MM/DD/YYYY'
				})
				.on('dp.change', function(e) {

				});

			$('#cboCourseDiv').addClass('hide');

		});

		$(document).ready(function() {
			$("#cboreporttype").change(function() {

				var exporttype = $(this).val();

				$("#cborotation").attr('required', false);
				// $("#cborotation").removeClass('input-required');
				$('#select2-cborotation-container').removeClass('required-select2');

				if (exporttype == 'Accreditation' || exporttype == 'Certification' || exporttype == 'AdditionalContactInformation') {
					$("#btnSubmit").html("Export to Excel");
					if (exporttype == 'Accreditation')
						window.location.href = "exportStudentAccreditationAllList.html";
					else if (exporttype == 'Certification')
						window.location.href = "exportStudentCertificationLogList.html";
					else if (exporttype == 'AdditionalContactInformation')
						window.location.href = "exportStudentAdditationlContactInformationAllList.html";
				} else {
					$("#btnSubmit").html("Show");
				}

				//Add Required to Start And End Date
				$("#startDate").attr('required', false);
				$("#endDate").attr('required', false);

				if (exporttype == 'Checkoff' || exporttype == 'Absence' || exporttype == 'Attendance' || exporttype == 'CI_Eval' || exporttype == 'P_Eval' || exporttype == 'ClinicalSiteUnit' || exporttype == 'Dr_Points' || exporttype == 'Equipments' || exporttype == 'Formative' || exporttype == 'Journal' || exporttype == 'Midterm' || exporttype == 'Site_Eval' || exporttype == 'Summative' || exporttype == 'Time_Exception' || exporttype == 'Attendance_Daily_Weekly' || exporttype == 'CheckoffByCourses' || exporttype == 'Preceptor_Checkoff' || exporttype == 'Late_clockIn' || exporttype == 'CaseStudy' || exporttype == 'Early_clockOut' || exporttype == 'ClinicianAttendance' || exporttype == 'ActivitySheet' || exporttype == 'CallOff' || exporttype == 'SoapNote') {
					$("#startDate").attr('required', true);
					$("#endDate").attr('required', true);
				}

				$('#courseTopicCompletionDiv').addClass('hide');
				//For Checkoff by Courses 
				if (exporttype == 'CheckoffByCourses') {

					// $("#cboCourse").attr('required', true);
					// $('#select2-cboCourse-container').addClass('required-select2');
					$('#courseTopicCompletionDiv').removeClass('hide');
					$('#cboCourseDiv').removeClass('hide');
					$('#courseTopicCompletion').trigger('change');
					$('#cborotation').val('').select2().trigger('change');
					$('#cborotation').prop('disabled', true);


				} else {
					$('#cboCourseDiv').addClass('hide');
					$('#cborotation').prop('disabled', false);
				}


				//For Ci Eval Ans Site Eval To select Multiple Clinical Instructor and Hospital sites and Clinial Site Units
				$('#cboevaluator').prop('multiple', '');
				$('#cboevaluator').select2().trigger('change');

				//Hospital sites
				$('#cbohospitalsite').prop('multiple', '');
				$('#cbohospitalsite').select2().trigger('change');

				//Clinical site units
				$('#cbohospitalsiteunits').prop('multiple', '');
				$('#cbohospitalsiteunits').select2().trigger('change');

				if (exporttype == 'Site_Eval' || exporttype == 'CI_Eval') {
					//Clinical Instructor
					$('#cboevaluator').prop('multiple', 'multiple');
					$('#cboevaluator').prop('name', 'cboevaluator[]');
					$('#cboevaluator').children("option")[0].remove();
					$('#cboevaluator').select2().trigger('change');

					//Hospital sites
					$('#cbohospitalsite').prop('multiple', 'multiple');
					$('#cbohospitalsite').prop('name', 'cbohospitalsite[]');
					$('#cbohospitalsite').children("option")[0].remove();
					$('#cbohospitalsite').select2().trigger('change');

					//Clinical site units
					$('#cbohospitalsiteunits').prop('multiple', 'multiple');
					$('#cbohospitalsiteunits').prop('name', 'cbohospitalsiteunits[]');
					$('#cbohospitalsiteunits').children("option")[0].remove();
					$('#cbohospitalsiteunits').select2().trigger('change');

				} else {
					//Clinical Instructor
					$('#cboevaluator').prop('name', 'cboevaluator');
					var isExistBlank = $("#cboevaluator option[value='']").length > 0;
					if (!isExistBlank)
						$("#cboevaluator").prepend("<option value='' selected>Select</option>").val('');

					//Hospital sites
					$('#cbohospitalsite').prop('name', 'cbohospitalsite');
					var isExistHospitalSiteBlank = $("#cbohospitalsite option[value='']").length > 0;
					if (!isExistHospitalSiteBlank)
						$("#cbohospitalsite").prepend("<option value='' selected>Select</option>").val('');

					//Clinical site units
					$('#cbohospitalsiteunits').prop('name', 'cbohospitalsiteunits');
					var isExistClinicalSiteBlank = $("#cbohospitalsiteunits option[value='']").length > 0;
					if (!isExistClinicalSiteBlank)
						$("#cbohospitalsiteunits").prepend("<option value='' selected>Select</option>").val('');

				}

				//For Checkoff To select Multiple Rotation
				$('#cborotation').prop('multiple', '');
				$('#cborotation').select2().trigger('change');
				if (exporttype == 'Checkoff' || exporttype == 'Attendance') {
					$('#cborotation').prop('multiple', 'multiple');
					$('#cborotation').prop('name', 'cborotation[]');
					// $('#cborotation').children("option")[0].remove();
					// $('#cborotation').empty();
					$('#cborotation').prop('selectedIndex', -1);
					$('#cborotation').select2().trigger('change');
				} else {
					$('#cborotation').prop('name', 'cborotation');
					var isExistRotationBlank = $("#cborotation option[value='']").length > 0;
					if (!isExistRotationBlank)
						$("#cborotation").prepend("<option value='' selected>Select</option>").val('');

				}


				//For Attendance Daily/Weekly Report
				$('#endDate').data('DateTimePicker').maxDate(false);
				$('#endDate').data('DateTimePicker').minDate(false);

				if (exporttype == 'Attendance_Daily_Weekly' && $('#startDate').val()) {
					var incrementDay = moment(new Date($('#startDate').val()));
					incrementDay.add(+6, 'days');
					$('#endDate').val(incrementDay.format('MM/DD/YYYY'));
					$('#endDate').data('DateTimePicker').maxDate(incrementDay);
					$('#endDate').data('DateTimePicker').minDate(moment(new Date($('#startDate').val())));
					$('#endDate').data("DateTimePicker").hide();
					$('#startDate').trigger('change');
				}




			});

			$("#cboindividualstudent").change(function() {

				var cboindividualstudent = $(this).val();
				var cboreporttype = $("#cboreporttype").val();

				$('#cborotation').prop('multiple', '');
				$('#cborotation').prop('name', 'cborotation');
				$('#cborotation').select2().trigger('change');

				if (cboindividualstudent != '' && cboreporttype == 'CheckoffByCourses') {
					$('#cborotation').prop('multiple', 'multiple');
					$('#cborotation').prop('name', 'cborotation[]');
					$('#cborotation').children("option")[0].remove();
					$('#cborotation').select2().trigger('change');
				}
			});
		});

		function getSubRotationList(eleObj) {
			var rotationId = $(eleObj).val();
			var currentSchoolId = '<?php echo $currentSchoolId; ?>';

			$.ajax({
				type: 'post',
				url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_get_subrotation_list.html",
				data: {
					rotationId: rotationId,
					currentSchoolId: currentSchoolId
				},
				success: function(response) {
					document.getElementById("subcborotation").innerHTML = response;
				}
			});
		}
	</script>




</body>

</html>