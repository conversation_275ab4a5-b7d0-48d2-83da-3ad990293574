<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsRotation.php');
include('../class/clsLocations.php');
include('../class/clsStudent.php');
include('../class/clsFormative.php');
include('../setRequest.php');
include('../class/clsQuestionOption.php');
include('../class/clsSectionStudentName.php');
include('../class/clsClinician.php');
include('../class/clsPEF.php');

$isPEFType = isset($_GET['isPEFType']) ? DecodeQueryData($_GET['isPEFType']) : '';

if ($isPEFType == 1) {
    $activebtn = 'pef2';
} else {
    $activebtn = 'pef1';
}

$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];

$schoolId = 0;
$rotationId = 0;
$studentId = 0;
$currentstudentId = 0;
$studentPEFMasterId = 0;
$clinicianId = 0;
$rotationId = 0;
$dateOfStudentSignature = '';
$evaluationDate = '';
$display_to_date = date('m/d/Y');
$schoolId = $currentSchoolId;
$DBRotationId = 0;
$objRotation = new clsRotation();
$objPEF = new clsPEF();

//For Rotation
$formativerotationid = isset($_GET['formativerotationid']) ? DecodeQueryData($_GET['formativerotationid']) : 0;

//For Student	
$currentstudentId = isset($_GET['studentId']) ? DecodeQueryData($_GET['studentId']) : 0;
$rotationId = isset($_GET['rotationId']) ? DecodeQueryData($_GET['rotationId']) : 0;

//For Edit Formative
if (isset($_GET['studentPEFMasterId']) && ($_GET['rotationId'])) {

    $schoolId = $currentSchoolId;

    if ($isPEFType == 1)
        $page_title = "Edit PEF II ";
    else
        $page_title = "Edit PEF I ";

    $bedCrumTitle = 'Edit';

    //For Formative Details
    $objPEF = new clsPEF();
    $studentPEFMasterId = DecodeQueryData($_GET['studentPEFMasterId']);
    $rowFormative = $objPEF->GetStudentPEFDetails($studentPEFMasterId);

    unset($objPEF);
    if ($rowFormative == '') {
        header('location:pefList.html');
        exit;
    }

    $DBRotationId = ($rowFormative['rotationId']);
    $clinicianId = ($rowFormative['clinicianId']);
    $evaluationDate = ($rowFormative['evaluationDate']);
    $courselocationId = $rowFormative['locationId'];
    $parentRotationId = stripslashes($rowFormative['parentRotationId']);
    $rotationLocationId = stripslashes($rowFormative['rotationLocationId']);
    $isStatus = $rowFormative['isStatus'];

    $locationId = 0;
    if ($rotationLocationId != $courselocationId && $parentRotationId > 0) {
        if ($parentRotationId > 0) {
            if (!$rotationLocationId)
                $locationId = $objRotation->GetLocationByRotation($DBRotationId);
            else
                $locationId  = $rotationLocationId;
        }
    } else {
        $locationId  = $courselocationId;
    }

    //Get Time Zone By Rotation 
    $objLocation = new clsLocations();
    $TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
    unset($objLocation);
    if ($TimeZone == '')
        $TimeZone = $_SESSION["loggedUserSchoolTimeZone"];
    $evaluationDate = converFromServerTimeZone($evaluationDate, $TimeZone);
    $evaluationDate = (date('m/d/Y', strtotime($evaluationDate)));
    $studentId = ($rowFormative['studentId']);
    $studentSignature = isset($rowFormative['studentsigniture']) ? $rowFormative['studentsigniture'] : '';
    // echo $studentSignature;exit;
    $dateOfStudentSignature = ($rowFormative['dateOfStudentSignature']);
    if ($dateOfStudentSignature != '' && $dateOfStudentSignature != '0000-00-00 00:00:00') {
        $dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $TimeZone);
        $dateOfStudentSignature = (date('m/d/Y', strtotime($dateOfStudentSignature)));
    }

    $studentComment = strip_tags($rowFormative['studentComment']);
    //For Rotation List
    $rowstudentrotation = $objRotation->GetRotationByStudent($schoolId, $currentstudentId);
} else {
    if ($isPEFType == 1)
        $page_title = "Add PEF II";
    else
        $page_title = "Add PEF I";

    $bedCrumTitle = 'Add';
    //For Rotation List
    $rowstudentrotation = $objRotation->GetCurrentRotationByStudent($schoolId, $currentstudentId);
}


//For Clinician Name
$objClinician = new clsClinician();
$Clinician = $objClinician->GetClinicianByRotation($currentSchoolId, $rotationId);
unset($objClinician);

//For Student Name
$objStudent = new clsStudent();
$Student = $objStudent->GetStudentsByRotation($currentSchoolId, $rotationId);
unset($objStudent);

$objSectionStudentName = new clsSectionStudentName();
$getStudentName = $objSectionStudentName->GetSectionStudentName($schoolId, $rotationId);
unset($objSectionStudentName);


$totalSection = 0;
$objPEF = new clsPEF();

if ($activebtn == 'pef1')
    $pefSection = $objPEF->GetSections('pef1');
elseif ($activebtn == 'pef2')
    $pefSection = $objPEF->GetSections('pef2');

$totalSection = ($pefSection != '') ? mysqli_num_rows($pefSection) : 0;


//For Rotation Name

$RotationName = $objRotation->GetrotationDetails($rotationId, $schoolId);
$rotationtitle = $RotationName ? $RotationName['title'] : '';
unset($objRotation);

//For Student Full Name
$objStudent = new clsStudent();
$Rowstudent = $objStudent->GetSingleStudent($schoolId, $currentstudentId);
$studentfullname =  $Rowstudent ? $Rowstudent['firstName'] . ' ' . $Rowstudent['lastName'] : '';
unset($objStudent);

if ($isPEFType == 1)
    $breadCrubTitle = 'PEF II Evaluation';
else
    $breadCrubTitle = 'PEF I Evaluation';

$bedCrumTitle = (isset($_GET['view']) && $_GET['view'] == '1') ? 'View' : $bedCrumTitle;

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($page_title); ?></title>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <?php include('includes/headercss.php'); ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <style type="text/css">
        .some-class {
            float: left;
            clear: none;
        }

        #listResults .checkbox * {
            cursor: pointer;
        }

        #listResults .checkbox {
            padding-left: 20px;
            width: 20px;
            height: 20px;
        }

        #listResults .checkbox label {
            display: inline-block;
            vertical-align: middle;
            position: relative;
            padding-left: 5px;
        }

        #listResults .checkbox label::before {
            content: "";
            display: inline-block;
            position: absolute;
            width: 20px;
            height: 20px;
            left: 0;
            margin-left: -20px;
            border: 1px solid #555;
            border-radius: 3px;
            background-color: #fff;
            -webkit-transition: border 0.15s ease-in-out, color 0.15s ease-in-out;
            -o-transition: border 0.15s ease-in-out, color 0.15s ease-in-out;
            transition: border 0.15s ease-in-out, color 0.15s ease-in-out;
        }

        #listResults .checkbox label::after {
            display: inline-block;
            position: absolute;
            width: 20px;
            height: 20px;
            left: 2px;
            top: 2px;
            margin-left: -21px;
            padding-left: 3px;
            padding-top: 1px;
            font-size: 11px;
            color: #555555;
        }

        #listResults .checkbox input[type="checkbox"] {
            opacity: 0;
            z-index: 1;
            width: 10px;
            height: 10px;
        }


        #listResults .checkbox input[type="checkbox"]:checked+label::after {
            font-family: "FontAwesome";
            content: "\f00c";
        }

        /* #listResults .checkbox.checkbox-circle label::before {
        border-radius: 50%;
        } */

        #listResults .checkbox.checkbox-inline {
            margin-top: 0;
        }

        #listResults .checkbox-red input[type="checkbox"]:checked+label::after {
            color: #fff;
        }

        #listResults .checkbox-red input[type="checkbox"]+label::before {
            background-color: #990000;
            border-color: #990000;
        }

        .form-horizontal .checkbox {
            padding-top: 0 !important;
        }

        .input-group-addon {
            position: absolute;
            right: 7px;
            /* width: 100%; */
            z-index: 99;
            width: 35px;
            margin: auto;
            top: 5px;
            border-left: 1px solid #ccc;
            border-radius: 50% !important;
            padding: 10px -2px;
            height: 35px;
            /* background: #01A750; */
            /* color: #fff; */
            color: #555;
            background: #f6f9f9;
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .panel-default>.panel-heading {
            background-color: transparent !important;
        }

        .btn-success,
        .btn-default {
            padding: 8px 25px;
            border-radius: 10px;
        }

        .panel {
            border-radius: 14px !important;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 45px !important;
        }

        .required-select2 {
            border-left: solid 3px red !important;
            border-radius: 12px !important;
        }

        .select2-container--default .select2-selection--single {
            background-color: #f6f9f9 !important;
            cursor: default !important;
            height: 45px !important;
            border-radius: 10px !important;
        }

        .select2-container--default .select2-selection--single {
            border: none !important;
        }

        .panel,
        .form-group {
            margin-bottom: 10px;
        }

        .bootstrap-datetimepicker-widget {
            border-radius: 12px !important;
        }

        .form-control {
            height: 45px;
        }

        /* .form-horizontal .form-group {
         margin-right: 0;
         margin-left: 0;
      } */

        /* Style for the collapsible content */
        .panel-collapse {
            display: none;
            /* Hidden by default */
            /* padding: 10px;
            border: 1px solid #ddd;
            background-color: #f9f9f9; */
        }

        /* Style for the collapsible button */
        .collapsible {
            /* background-color: #3498db;
            color: white;
            padding: 10px;
            border: none; */
            width: 100%;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            /* Align content horizontally */
        }

        .panel-heading {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        /* Style for the arrow icons */
        .arrow-icon {
            transition: transform 0.3s;
        }

        .collapsible.expanded .arrow-icon i {
            transform: rotate(180deg);
        }

        .panel-default>.panel-heading+.panel-collapse>.panel-body {
            border-top: none !important;
        }

        @media screen and (max-width: 500px) {
            .panel-body ol {
                padding-left: 20px;
            }

            .panel-default>.panel-heading {
                padding-left: 5px;
            }

            .mobile-px-0 {
                padding-left: 0;
                padding-right: 0;
            }

            /* .container-zero{
            padding: 0;
         } */
        }
    </style>
</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <?php if ($currentstudentId > 0) { ?>
                        <li><a href="clinical.html">Clinical</a></li>
                        <li><a href="clinical.html"><?php echo ($studentfullname); ?></a></li>
                        <li><a href="pefList.html?studentId=<?php echo EncodeQueryData($currentstudentId); ?>"><?php echo $breadCrubTitle; ?></a></li>
                    <?php } else { ?>
                        <li><a href="rotations.html">Rotations</a></li>
                        <li><a href="rotations.html"><?php echo ($rotationtitle); ?></a></li>
                        <li><a href="pefList.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>"><?php echo $breadCrubTitle; ?></a></li>
                    <?php } ?>
                    <li class="active"><?php echo ($bedCrumTitle); ?></li>
                </ol>
            </div>

        </div>
    </div>

    <div class="container">

        <form id="frmformative" data-parsley-validate class="form-horizontal" method="POST" <?php if ($currentstudentId > 0) { ?>action="addPEFEvaluationsubmit.html?studentId=<?php echo (EncodeQueryData($currentstudentId)); ?>&studentPEFMasterId=<?php echo (EncodeQueryData($studentPEFMasterId)); ?>" <?php } else { ?>action="addPEFEvaluationsubmit.html?studentPEFMasterId=<?php echo (EncodeQueryData($studentPEFMasterId)); ?>
		 &rotationId=<?php echo (EncodeQueryData($rotationId)); ?>" <?php } ?>>
            <input type="hidden" name="isPEFType" id="" value="<?php echo $isPEFType; ?>">
            <div class="row">
                <div class="col-md-6">

                    <div class="form-group">
                        <label class="col-md-12 control-label" for="cboclinician">Clinician</label>
                        <div class="col-md-12 flex-col-reverse">
                            <select id="cboclinician" name="cboclinician" required
                                class="form-control input-md required-input select2_single">
                                <option value="" selected>Select</option>
                                <?php
                                if ($Clinician != "") {
                                    while ($row = mysqli_fetch_assoc($Clinician)) {
                                        $selClinicianId  = $row['clinicianId'];
                                        $name  = stripslashes($row['firstName']);
                                        $lastName  = stripslashes($row['lastName']);
                                        $fullName = $name . ' ' . $lastName;
                                ?>
                                        <option value="<?php echo ($selClinicianId); ?>" <?php if ($clinicianId == $selClinicianId) { ?> selected="true" <?php } ?>><?php echo ($fullName); ?></option>

                                <?php

                                    }
                                }
                                ?>
                            </select>
                        </div>
                    </div>
                    <!-- ROTATION DD END -->
                </div>

                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="cbostudent">Student</label>
                        <div class="col-md-12">
                            <select id="cbostudent" name="cbostudent" class="form-control input-md required-input select2_single" required data-parsley-errors-container="#error-cbostudent">
                                <option value="" selected>Select</option>
                                <?php
                                if ($Student != "") {
                                    while ($row = mysqli_fetch_assoc($Student)) {
                                        $selstudentId  = $row['studentId'];
                                        $firstName  = stripslashes($row['firstName']);
                                        $lastName  = stripslashes($row['lastName']);
                                        $name =    $firstName . ' ' . $lastName;
                                        if ($currentstudentId > 0) { ?>
                                            <option value="<?php echo ($selstudentId); ?>" <?php if ($currentstudentId == $selstudentId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                                        <?php } else { ?>
                                            <option value="<?php echo ($selstudentId); ?>" <?php if ($studentId == $selstudentId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                                        <?php }  ?>
                                <?php

                                    }
                                }
                                ?>
                            </select>
                            <div id="error-cbostudent"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="evaluationDate">Evaluation Date</label>
                        <div class="col-md-12">
                            <div class='input-group date w-full relative' id='evaluationDate'>

                                <input type='text' name="evaluationDate" id="evaluationDate" class="form-control input-md required-input rotation_date" value="<?php echo ($evaluationDate); ?>" required data-parsley-errors-container="#error-txtDate" placeholder="MM-DD-YYYY" />
                                <span class="input-group-addon calender-icon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                            <div id="error-txtDate"></div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="studentsignitureDate">Date Of Student Signature</label>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <div class='input-group date w-full relative' id='studentsignitureDate'>

                                <input type='text' name="studentsignitureDate" readonly id="studentsignitureDate" class="form-control input-md required-input rotation_date" value="<?php echo ($dateOfStudentSignature);  ?>" data-parsley-errors-container="#error-txtDate" placeholder="MM-DD-YYYY" />
                                <span class="input-group-addon calender-icon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                            <div id="error-txtDate"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <!-- <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="studentsigniture">Student Signature</label>
                        <div class="col-md-12">
                            <input type='text' name="studentsigniture" readonly id="studentsigniture" class="form-control input-md required-input " value="<?php if (isset($_GET['studentPEFMasterId']))  echo ($studentSignature); ?>" />
                            <div id="error-txtDate"></div>
                        </div>
                    </div>
                </div> -->
                <?php if ($currentstudentId > 0) { ?>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="cborotation">Rotation</label>
                            <div class="col-md-12 flex-col-reverse">
                                <select id="cborotation" name="cborotation"
                                    class="form-control input-md required-input select2_single" required>
                                    <option value="" selected>Select</option>
                                    <?php
                                    if ($rowstudentrotation != "") {
                                        while ($row = mysqli_fetch_assoc($rowstudentrotation)) {
                                            $selrotationId  = $row['rotationId'];
                                            $title  = stripslashes($row['title']);
                                    ?>
                                            <option value="<?php echo ($selrotationId); ?>" <?php if ($DBRotationId == $selrotationId) { ?> selected="true" <?php } ?>><?php echo ($title); ?></option>
                                    <?php }
                                    }
                                    ?>
                                </select>

                            </div>
                        </div>
                    </div>
                <?php } ?>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="studentcomment">Student Comments</label>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <textarea name="studentcomment" id="studentcomment" class="form-control input-md " rows="4" cols="100" disabled><?php if (isset($_GET['studentPEFMasterId']))  echo ($studentComment); ?></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="instructions:"><b>DIRECTIONS</b></label>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <div class="panel panel-default">
                                <div class="panel-body">

                                    <p class="col-md-12"><b> PASSING CRITERIA: Obtain 90% or better on the procedure.</b> Tasks indicated by * must
                                        receive at least 1 point, ANY 0’s and the evaluation is terminated. Procedure must be performed
                                        within the designated time, or the performance receives a failing grade
                                    </p>
                                    <div class="col-md-12">
                                        SCORING:
                                    </div>
                                    <div class="col-md-12">
                                        <b>2 points = </b> Task performed satisfactorily <b><u>without prompting</u></b>.
                                        <br>
                                        <b>1 point = </b> Task performed satisfactorily <b><u>with self-initiated correction</u></b>.
                                        <br>
                                        <b>0 points = </b> Task performed incorrectly or <b><u>with prompting required</u></b>.

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="instructions:"></label>
                        <div class="col-md-12">
                            <h4> <b> FINAL PERFORMANCE EVALUATION FORM <?php if ($isPEFType == 1) { ?> II <?php } else { ?> I <?php } ?>: </b></h4>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="instructions:"></label>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <div class="panel-group" id="posts">
                                <?php

                                if ($pefSection) {
                                    while ($row = mysqli_fetch_array($pefSection)) {
                                        $sectionMasterId = $row['sectionMasterId'];
                                        $sectionDescription = isset($row['description']) ? $row['description'] : '';
                                        $title = $row['title'];
                                        //$firstName = $row['firstName'];
                                ?>

                                        <div class="panel panel-default">
                                            <a class="collapsible" style="color: #000; text-decoration: none;" href="#<?php echo $sectionMasterId; ?>" data-toggle="collapse" data-parent="#posts" aria-expanded="false" id="collapse-link">
                                                <div class="panel-heading">
                                                    <h4 class="panel-title">
                                                        <b><?php echo  $title;
                                                            if ($sectionDescription) {
                                                                echo $sectionDescription;
                                                            } ?> </b>
                                                    </h4>
                                                    <span class="arrow-icon"> <i class="fa fa-angle-down" aria-hidden="true" id="collapse-icon" style="font-size: 19px;"></i></span>
                                                </div>
                                            </a>
                                            <div id="<?php echo $sectionMasterId; ?>" class="panel-collapse collapse">
                                                <?php
                                                // for question
                                                $pefquestion = $objPEF->GetAllPEFQuestionMaster($sectionMasterId);
                                                $totalPEF = ($pefquestion != '') ? mysqli_num_rows($pefquestion) : 0;

                                                if ($totalPEF > 0) {
                                                    while ($row = mysqli_fetch_array($pefquestion)) {
                                                        if (isset($_GET['studentPEFMasterId']))
                                                            $studentPEFMasterId = DecodeQueryData($_GET['studentPEFMasterId']);
                                                        else
                                                            $studentPEFMasterId = 0;

                                                        $pefQuestionId = $row['pefQuestionId'];
                                                        $schoolPEFQuestionTitle = $row['optionText'];
                                                        $pefQuestionType = $row['pefQuestionType'];
                                                        $description = $row['description'];
                                                        $qhtml = GetPEFQuestionHtml($pefQuestionId, $pefQuestionType, $studentPEFMasterId, $currentSchoolId);

                                                ?>
                                                        <div class="panel-body isAllRadioButton">
                                                            <b class="questionDiv"><?php echo ($schoolPEFQuestionTitle); ?> </b><br /><br />
                                                            <?php echo $qhtml; ?>
                                                            <?php if ($description != '') { ?>
                                                                <br>
                                                                <div class="text-left margin_top_five margin_bottom_five" style="left: 5%;position: absolute;"><?php echo $description; ?></div>
                                                                <br><br>
                                                            <?php } ?>
                                                        </div>
                                                <?php
                                                    }
                                                }
                                                ?>
                                            </div>
                                        </div>
                                <?php
                                    }
                                }

                                ?>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- <div class="row">

                    <label class="col-md-2 control-label"></label>
                    <div class="col-md-10">
                        <div class="form-group">
                            <label class="col-md-1 control-label">Score:</label>
                            <div class="col-md-1 padding_zero">
                                <input type="text" name="score" id="score" class="form-control" readonly>
                            </div>
                            <label class="col-md-1 control-label" style="text-align: left;">/ <?php if ($isPEFType == 1) {
                                                                                                    echo "66";
                                                                                                } else {
                                                                                                    echo "48";
                                                                                                } ?> </label>
                            <label class="col-md-1 control-label padding_right_zero"> <label class="control-label">Pass</label> <input type="checkbox" name="isStatus" id="isPass" class="margin_left_twenty" value="Pass" onclick="return false;" / style="margin-left: 7px;height: 20px;width: 20px;" readonly> </label>

                            <label class="col-md-1 control-label">Fail</label>
                            <div class="col-md-1 padding_zero" id="listResults">

                                <div class="checkbox checkbox-circle checkbox-red">
                                    <input type="checkbox" name="isStatus" id="isFail" class="" value="Fail" style="margin-top: 10px;" onclick="return false;" />
                                    <label></label>
                                </div>
                            </div>

                        </div>

                    </div>
                </div> -->

                <!-- <div class="row">

                    <label class="col-md-2 control-label"></label>
                    <div class="col-md-10 padding_left_zero">
                        <div class="form-group">
                            <label class="col-md-2 control-label">TOTAL POINTS: </label>
                            <div class="col-md-2 padding_zero">
                                <input type="text" name="totalPoints" id="totalPoints" class="form-control" readonly>
                            </div>
                            <br>
                            <br>
                            <?php
                            if ($isPEFType == 1)
                                $bottomLabel = "(Max is 66 points, 2 points for each line – NEED 60 points to PASS! – and…NO Zero’s)";
                            else
                                $bottomLabel = "(Max is 48 points, 2 points for each line – NEED 43 points to PASS! – and…NO Zero’s)";

                            ?>
                            <p class="margin_left_fifty"><b><?php echo $bottomLabel; ?></b></p>
                        </div>

                    </div>
                </div> -->


                <div class="col-md-12">
                    <div class="grid-layout">
                        <div class="item card-body">

                            <div style="display: flex; align-items: center;">
                                <div class="card-image-section" style="background: #F5F2FD;">
                                    <img src="<?php echo ($dynamicOrgUrl); ?>/student/assets/images/note.png" alt="note">
                                </div>
                                <p class="card-title">
                                    Score
                                </p>
                            </div>
                            <div>

                                <p class="card-count">
                                    <input type="hidden" name="score" id="scoreInput" value="">
                                    <!-- <span class="card-count-span" id="score" name="score"></span> of 100 -->
                                    <span class="card-count-span" id="score" name="score"></span> of <?php if ($isPEFType == 1) {
                                                                                                            echo "66";
                                                                                                        } else {
                                                                                                            echo "48";
                                                                                                        } ?>
                                </p>

                            </div>
                        </div>

                        <div class="item card-body">

                            <div style="display: flex; align-items: center;">
                                <div class="card-image-section" style="background: #EEF5FF;">
                                    <img src="<?php echo ($dynamicOrgUrl); ?>/student/assets/images/award.png" alt="note">
                                </div>
                                <p class="card-title">
                                    Result
                                </p>
                            </div>
                            <div>

                                <p class="card-count" style="font-size: 16px;text-transform: uppercase; font-weight: 600;">
                                    <input type="hidden" name="isStatus" id="status" value="">
                                    <span class="card-count-span" id="isStatus" name="isStatus"></span>

                                </p>

                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="form-group">
                    <!-- <label class="col-md-2 control-label"></label> -->
                    <div class="col-md-12" style="display: flex; justify-content: center; margin: 20px 0;gap: 15px;">
                        <?php
                        $rotationStatus = checkRotationStatus($rotationId);
                        if ($rotationStatus == 0) {
                        ?>
                            <button id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
                        <?php } ?>

                        <?php if ($currentstudentId > 0) { ?>
                            <a type="button" href="pefList.html?studentId=<?php echo EncodeQueryData($currentstudentId); ?>" class="btn btn-default margin_left_ten">Cancel</a>
                        <?php } else { ?>
                            <a type="button" href="pefList.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>" class="btn btn-default margin_left_ten">Cancel</a>
                        <?php } ?>
                    </div>
                </div>
            </div>
    </div>
    </form>
    <?php include('includes/footer.php'); ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="https://cdn.ckeditor.com/ckeditor5/35.0.1/classic/ckeditor.js"></script>



    <script type="text/javascript">
        //  ClassicEditor
        // 		.create(document.querySelector('#studentcomment'))
        // 		.catch(error => {
        // 			console.error(error);
        // 		});	
        $(window).load(function() {

            $('.isAllRadioButton').trigger('click');

            $('#frmformative').parsley().on('field:validated', function() {
                    var ok = $('.parsley-error').length === 0;
                })
                .on('form:submit', function() {
                    ShowProgressAnimation();
                    return true; // Don't submit form for this demo
                });

            $('#evaluationDate').datetimepicker({
                format: 'MM/DD/YYYY'
            });
            $('#studentsignitureDate').datetimepicker({
                format: 'MM/DD/YYYY'
            });
            $('#InstructorDate').datetimepicker({
                format: 'MM/DD/YYYY'
            });


            //for searching dropdown
            $(".select2_single").select2();
            $('#select2-cborotation-container').addClass('required-select2');
            $('#select2-cboclinician-container').addClass('required-select2');
            $('#select2-cbostudent-container').addClass('required-select2');

            <?php if (isset($_GET['studentPEFMasterId']) && ($_GET['rotationId'])) { ?>
                $('#cbostudent').prop('disabled', true);
            <?php } ?>

            <?php if ($currentstudentId > 0) { ?>
                $('#cbostudent').prop('disabled', true);
            <?php }
            if ($rotationId > 0) { ?>
                $("#cborotation").prop("required", false);
            <?php } ?>


        });

        //For Fourth Section
        $(document).ready(function() {
            $(".isAllRadioButton").click(function() {
                var isPEFType = "<?php echo $isPEFType; ?>";

                var sumCheckedButton = 0;
                var checkedNACount = 0;
                var isFail = 0;
                $(".isAllRadioButton input[type=radio]:checked").each(function() {

                    var checkedRadio = ($(this).parent().text());
                    var question = $(this).closest(".isAllRadioButton").find('.questionDiv').removeClass('text-danger');
                    if (checkedRadio == 0) {
                        // alert(this);
                        var question = $(this).closest(".isAllRadioButton").find('.questionDiv').addClass('text-danger');
                        isFail = 1;

                    }
                    checkedNACount++;
                    sumCheckedButton += parseInt(($.trim(checkedRadio)));
                });

                if (isPEFType == 1) {
                    if (sumCheckedButton < 60 || isFail == 1) {
                        // $("#isFail").trigger('click');
                        // $("#isFail").prop('checked', true);
                        // $("#isPass").prop('checked', false);
                        $("#isStatus").text('Fail');
                    } else {
                        // $("#isPass").trigger('click');
                        // $("#isPass").prop('checked', true);
                        // $("#isFail").prop('checked', false);
                        $("#isStatus").text('Pass');

                    }
                } else {
                    if (sumCheckedButton < 43 || isFail == 1) {
                        // $("#isFail").trigger('click');
                        // $("#isFail").prop('checked', true);
                        // $("#isPass").prop('checked', false);
                        $("#isStatus").text('Fail');

                    } else {
                        // $("#isPass").trigger('click');
                        // $("#isPass").prop('checked', true);
                        // $("#isFail").prop('checked', false);
                        $("#isStatus").text('Pass');

                    }
                }

                $("#score").text(sumCheckedButton);
                $("#totalPoints").val(sumCheckedButton);
            });

        });

        document.getElementById('btnSubmit').addEventListener('click', function() {
            // Get the score from the span element
            var score = document.getElementById('score').textContent;
            var status = document.getElementById('isStatus').textContent;

            // Set the value of the hidden input field
            document.getElementById('scoreInput').value = score;
            document.getElementById('status').value = status;

            // Submit the form
            // document.getElementById('scoreForm').submit();
        });
    </script>

    <script>
        // Get all collapsible button elements
        var buttons = document.querySelectorAll(".collapsible");
        var contents = document.querySelectorAll(".panel-collapse");

        // Add click event listeners to all buttons
        buttons.forEach(function(button, index) {
            button.addEventListener("click", function() {
                // Check if the content is currently expanded
                var isExpanded = contents[index].style.display === "block";

                // Close all sections
                contents.forEach(function(content) {
                    content.style.display = "none";
                });

                // Reset the "expanded" class for all buttons
                buttons.forEach(function(btn) {
                    btn.classList.remove("expanded");
                });

                // Toggle the content for the clicked section
                if (!isExpanded) {
                    contents[index].style.display = "block";
                    button.classList.add("expanded");
                }
            });
        });
    </script>
</body>

</html>