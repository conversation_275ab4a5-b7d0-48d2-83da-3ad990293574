<?php
include('includes/config.php');
include('includes/commonfun.php');
include('class/clsDB.php');
include("class/clsStudent.php");
include('class/clsSchool.php');
include('class/clscheckoff.php');
include('class/clsRotation.php');
include('class/clsCaseStudy.php');
include("class/clsClinician.php");
include("class/clsActivitysheet.php");

// echo '<pre>';
// print_r($_GET);

$AccessToken = isset($_GET['AccessToken']) ? $_GET['AccessToken'] : 0;
$UserType = isset($_GET['UserType']) ? $_GET['UserType'] : '';
$UserId = isset($_GET['UserId']) ? DecodeQueryData($_GET['UserId']) : 0;
$RotationId = isset($_GET['RotationId']) ? DecodeQueryData($_GET['RotationId']) : 0;
$RedirectType = isset($_GET['RedirectType']) ? $_GET['RedirectType'] : '';
$RedirectId = isset($_GET['RedirectId']) ? DecodeQueryData($_GET['RedirectId']) : 0;
$IsMobile = isset($_GET['IsMobile']) ? $_GET['IsMobile'] : 0;

$CheckoffDate = isset($_GET['CheckoffDate']) ? $_GET['CheckoffDate'] : '';
$schoolTopicId = isset($_GET['SchoolTopicId']) ? $_GET['SchoolTopicId'] : 0;
$ClinicianId  = isset($_GET['ClinicianId']) ? DecodeQueryData($_GET['ClinicianId']) : 0;

$studentIds = isset($_GET['StudentId']) ?  ($_GET['StudentId']) : 0;

$studentIds = explode(",", $studentIds);

// echo $_GET['isPEFType'];exit;

//  echo $isPEFType;
// exit;


$isAdd = ($RedirectType == 'site' || $RedirectType == 'ciEvaluation' || $RedirectType == 'incident' || $RedirectType == 'pEvaluation' || $RedirectType == 'PACR' || $RedirectType == 'floorCaseStudy' || $RedirectType == 'adult' || $RedirectType == 'daily/weekly' || $RedirectType == 'checkoff' || $RedirectType == 'pefEvaluation' || $RedirectType == 'activitysheet' || $RedirectType == 'floorAndIcu' || $RedirectType == 'equipment' || $RedirectType == 'formative' || $RedirectType == 'midterm' || $RedirectType == 'summative' || $RedirectType == 'volunteer' || $RedirectType == 'mastery') ? 1 : 0;
$errorMsg = '';
$status = true;
$view = '';
$isPreceptor = '';
$objDB = new clsDB();
$objrotation = new clsRotation();
// echo '</br>rotationId'.$RotationId;
// echo '</br>RedirectType'.$RedirectType;
// echo '</br>RedirectId'.$RedirectId;
$redirectURL = "";
// validate type
if ($UserType == 'S') {
    if ($UserId) {
        //validate AccessToken
        if (ValidateUserAccessToken($UserId, $AccessToken) == True) {
            // validate user
            $objStudent = new clsStudent();
            $rowStudent = $objStudent->GetStudentDetails($UserId);
            if ($rowStudent != '') {

                $schoolId = $rowStudent['schoolId'];
                // Get school Details
                $objSchool = new clsSchool();
                $schoolDetails = $objSchool->GetschoolDetails($schoolId);
                $schoolSlug = stripslashes($schoolDetails['slug']);
                unset($objSchool);

                $dynamicLoginURL = BASE_PATH . '/school/' . $schoolSlug . '/student';

                $studentId = $rowStudent['studentId'];

                //Get System User profile Image
                $profileImageName = stripslashes($rowStudent['smallProfilePic']);
                $defaultProfileImagePath = GetStudentImagePath($studentId, $schoolId, $profileImageName);

                $profileLargeImageName = stripslashes($rowStudent['profilePic']);
                $defaultProfileLargeImagePath = GetStudentImagePath($studentId, $schoolId, $profileLargeImageName);

                $studentTimeZone = $objStudent->GetStudentTimeZoneByStudentId($studentId);
                if ($studentTimeZone == '')
                    $timezone = $rowStudent['timezone'];
                else
                    $timezone = $studentTimeZone;


                //Start Session
                //-----------------------------------
                @session_start();
                $_SESSION["loggedStudentId"] = $studentId;
                $_SESSION["loggedStudentName"] = stripslashes($rowStudent['username']);
                $_SESSION["loggedStudentFirstName"] =  stripslashes($rowStudent['firstName']);
                $_SESSION["loggedStudentLastName"] = stripslashes($rowStudent['lastName']);
                $_SESSION["loggedStudentSchoolId"] = stripslashes($rowStudent['schoolId']);
                $_SESSION["loggedStudentSchoolTimeZone"] = $timezone;
                $_SESSION["loggedStudentProfileImagePath"] = $defaultProfileImagePath;
                $_SESSION["loggedStudentProfileLargeImagePath"] = $defaultProfileLargeImagePath;
                $_SESSION["isActiveCheckoff"] = stripslashes($rowStudent['isActiveCheckoff']);
                $_SESSION["loggedStudentLocation"] = stripslashes($rowStudent['locationId']);
                $_SESSION["IsMobile"] = 1;

                $isActiveCheckoff = stripslashes($rowStudent['isActiveCheckoff']);
                if ($RedirectId || $isAdd) {
                    //check redirect type
                    if ($RedirectType != '') {
                        if ($RedirectType == 'daily/weekly') {
                            $row = $objDB->GetSingleRowValuesFromTable('studentdailymaster', 'studentDailyMasterId', $RedirectId);
                            if ($row != '') {
                                $preceptorId = $row['preceptorId'];
                                $isPreceptorCompletedStatus = $row['isPreceptorCompletedStatus'];
                                $dateOfStudentSignature = stripslashes($row['dateOfStudentSignature']);
                                $dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $timezone);
                                $dateOfStudentSignature = date("m/d/Y", strtotime($dateOfStudentSignature));

                                //Condition for view
                                if ($dateOfStudentSignature != '11/30/-0001' && $dateOfStudentSignature != '0000-00-00' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '12/31/1969') {
                                    $view = 'V';
                                    if ($preceptorId > 0)
                                        $isPreceptor = EncodeQueryData($preceptorId);
                                } else if ($preceptorId > 0 && $isPreceptorCompletedStatus == 0) {
                                    $view = 'V';
                                    $isPreceptor = EncodeQueryData($preceptorId);
                                } else {
                                    $view = '';
                                    $isPreceptor = EncodeQueryData($preceptorId);
                                }
                                $rotationStatus = checkRotationStatus($RotationId);
                                $view = ($rotationStatus) ? 'V' : $view;

                                $redirectURL = $dynamicLoginURL . '/dailyEval.html?studentDailyMasterId=' . EncodeQueryData($RedirectId);

                                if ($RotationId)
                                    $redirectURL = $redirectURL . '&rotationId=' . EncodeQueryData($RotationId);

                                if ($isPreceptor)
                                    $redirectURL = $redirectURL . '&isPreceptor=' . ($isPreceptor);

                                if ($view != '')
                                    $redirectURL = $redirectURL . '&view=' . $view;
                            } else {
                                $errorMsg = 'daily/weekly not found';
                            }
                        } else if ($RedirectType == 'checkoff') {
                            if ($RedirectId) {

                                $row = $objDB->GetSingleRowValuesFromTable('checkoff', 'checkoffId', $RedirectId);
                                if ($row != '') {

                                    $isExternalPreceptorcheckoff = $row['isExternalPreceptorcheckoff'];
                                    $schoolTopicId = $row['schoolTopicId'];
                                    $rotationId = stripslashes($row['rotationId']);
                                    if ($isActiveCheckoff == 1) {

                                        $preceptorId = $row['completion1stPreceptorId'];
                                        $isPreceptorCompletedStatus = $row['is1stCompletionStatus'];
                                        $studentId = stripslashes($row['studentId']);
                                        $checkoffId = stripslashes($row['checkoffId']);
                                        $dateOfStudentSignature = stripslashes($row['student_evaluationDate']);

                                        $dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $timezone);
                                        $dateOfStudentSignature = date("m/d/Y", strtotime($dateOfStudentSignature));
                                        $studentComment = $row['studentComment'];
                                        //Condition for view
                                        if ($dateOfStudentSignature != '11/30/-0001' && $dateOfStudentSignature != '0000-00-00' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '12/31/1969' || $studentComment != '')
                                            $view = 'V';
                                        else if ($preceptorId > 0 && $isPreceptorCompletedStatus == 0) {
                                            $view = 'V';
                                        } else {
                                            $view = '';
                                        }
                                        //Check the rotation is expired or not
                                        $rotationDetails = $objrotation->GetRotationByRotationId($rotationId);
                                        if ($rotationDetails != '') {
                                            $parentRotationId = $rotationDetails['parentRotationId'];
                                            $isSchedule = $rotationDetails['isSchedule'];
                                            if ($parentRotationId && $isSchedule) {
                                                $objDB = new clsDB();
                                                $rotationDate = $objDB->GetSingleColumnValueFromTable('rotation', 'endDate', 'rotationId', $parentRotationId);
                                                unset($objDB);
                                            } else {
                                                $rotationDate = $rotationDetails['endDate'];
                                            }
                                        }

                                        $givenDate = strtotime(date("Y-m-d", strtotime($rotationDate)));
                                        $currentDate = strtotime(date('Y-m-d'));
                                        if ($givenDate < $currentDate) {
                                            $view = 'V';
                                        }
                                        $redirectURL = $dynamicLoginURL . '/addcheckoff.html?schoolTopicId=' . EncodeQueryData($schoolTopicId) . '&checkoffId=' . EncodeQueryData($checkoffId);

                                        if ($RotationId)
                                            $redirectURL = $redirectURL . '&rotationId=' . EncodeQueryData($RotationId);

                                        // if ($isPreceptor)
                                        //     $redirectURL = $redirectURL . '&isPreceptor=' . EncodeQueryData($isPreceptor);

                                        if ($view != '')
                                            $redirectURL = $redirectURL . '&view=' . $view;
                                    } else if ($isActiveCheckoff == 2) {

                                        $preceptorId = $row['completion1stPreceptorId'];
                                        $isPreceptorCompletedStatus = $row['is1stCompletionStatus'];
                                        $studentId = stripslashes($row['studentId']);
                                        $checkoffId = stripslashes($row['checkoffId']);
                                        $dateOfStudentSignature = stripslashes($row['student_evaluationDate']);

                                        $dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $timezone);
                                        $dateOfStudentSignature = date("m/d/Y", strtotime($dateOfStudentSignature));

                                        //Condition for view
                                        if ($dateOfStudentSignature != '11/30/-0001' && $dateOfStudentSignature != '0000-00-00' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '12/31/1969')
                                            $view = 'v';
                                        else if ($preceptorId > 0 && $isPreceptorCompletedStatus == 0) {
                                            $view = 'v';
                                        } else {
                                            $view = '';
                                        }
                                        //Check the rotation is expired or not
                                        $rotationDetails = $objrotation->GetRotationByRotationId($rotationId);
                                        if ($rotationDetails != '') {
                                            $parentRotationId = $rotationDetails['parentRotationId'];
                                            $isSchedule = $rotationDetails['isSchedule'];
                                            if ($parentRotationId && $isSchedule) {
                                                $objDB = new clsDB();
                                                $rotationDate = $objDB->GetSingleColumnValueFromTable('rotation', 'endDate', 'rotationId', $parentRotationId);
                                                unset($objDB);
                                            } else {
                                                $rotationDate = $rotationDetails['endDate'];
                                            }
                                        }

                                        $givenDate = strtotime(date("Y-m-d", strtotime($rotationDate)));
                                        $currentDate = strtotime(date('Y-m-d'));
                                        if ($givenDate < $currentDate) {
                                            $view = 'v';
                                        }
                                        $redirectURL = $dynamicLoginURL . '/addusafcheckoff.html?schoolTopicId=' . EncodeQueryData($schoolTopicId) . '&checkoffId=' . EncodeQueryData($checkoffId);

                                        if ($RotationId)
                                            $redirectURL = $redirectURL . '&rotationId=' . EncodeQueryData($RotationId);

                                        // if ($isPreceptor)
                                        //     $redirectURL = $redirectURL . '&isPreceptor=' . EncodeQueryData($isPreceptor);

                                        if ($view != '')
                                            $redirectURL = $redirectURL . '&View=' . $view;
                                    } else {

                                        $preceptorId = $row['completion1stPreceptorId'];
                                        $isPreceptorCompletedStatus = $row['is1stCompletionStatus'];
                                        $studentId = stripslashes($row['studentId']);
                                        $checkoffId = stripslashes($row['checkoffId']);
                                        $dateOfStudentSignature = stripslashes($row['student_evaluationDate']);

                                        if ($dateOfStudentSignature != '' && $dateOfStudentSignature != '0000-00-00 00:00:00') {
                                            $dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $timezone);
                                            $dateOfStudentSignature = date("m/d/Y", strtotime($dateOfStudentSignature));
                                        } else {
                                            $dateOfStudentSignature = "";
                                        }

                                        //For Lab Selected Questions
                                        $objcheckoff = new clscheckoff();

                                        $selectedLab = $objcheckoff->GetSelectedLabQuestions($checkoffId);
                                        $selectedLab = ($selectedLab > 6) ? 1 : $selectedLab;
                                        // //For Clinical Selected Questions
                                        $selectedClinical = $objcheckoff->GetSelectedClinicalQuestions($checkoffId);
                                        $selectedClinical = ($selectedClinical > 6) ? 1 : $selectedClinical;

                                        //Get Selected Preceptor Questions 
                                        $selectedPreceptor = $objcheckoff->GetSelectedPreceptorQuestions($checkoffId);
                                        if ($selectedPreceptor > 0)
                                            $selectedPreceptor = ($selectedPreceptor * 20) . '%';
                                        else
                                            $selectedPreceptor = '0';
                                        //For Student Selected Questions
                                        $objcheckoff = new clscheckoff();

                                        $totalStudentQuestions = $objcheckoff->GetSelectedStudentQuestions($checkoffId);
                                        unset($objcheckoff);

                                        if ($totalStudentQuestions > 0)
                                            $totalStudentQuestions = ($totalStudentQuestions * 20) . '%';
                                        else
                                            $totalStudentQuestions = '0';

                                        //Condition for view
                                        if ($dateOfStudentSignature != '' && $totalStudentQuestions == '100%' && $selectedClinical  && $selectedLab  && $selectedPreceptor == '100%') {
                                            $view = 'V';
                                        } else if ($dateOfStudentSignature == '' && $totalStudentQuestions == '100%' && $selectedClinical  && $selectedLab  && $selectedPreceptor == '100%') {
                                            $view = '';
                                            // $selectedClinical != 5 || $selectedLab != 5
                                        } else if ($dateOfStudentSignature == '' && $totalStudentQuestions == '100%' && ($selectedClinical == 0 || $selectedLab == 0 || $selectedPreceptor != '100%')) {
                                            $view = 'V';
                                        } else if ($totalStudentQuestions != '100%') {
                                            $view = '';
                                        } else {
                                            $view = 'V';
                                        }

                                        $rotationStatus = checkRotationStatus($RotationId, $isMobile = 1);
                                        if ($rotationStatus) {
                                            $view = 'V';
                                        }
                                        $redirectURL = $dynamicLoginURL . '/addcheckoff.html?schoolTopicId=' . EncodeQueryData($schoolTopicId) . '&checkoffId=' . EncodeQueryData($checkoffId);

                                        if ($RotationId)
                                            $redirectURL = $redirectURL . '&rotationId=' . EncodeQueryData($RotationId);

                                        // if ($isPreceptor)
                                        //     $redirectURL = $redirectURL . '&isPreceptor=' . EncodeQueryData($isPreceptor);

                                        if ($view != '')
                                            $redirectURL = $redirectURL . '&view=' . $view;
                                        unset($objcheckoff);
                                    }
                                }
                            } else if ($isAdd && $isActiveCheckoff == 0) {
                                $redirectURL = $dynamicLoginURL . '/addcheckoff.html?studentId=' . EncodeQueryData($UserId) . '&startdatetime=' . $CheckoffDate . '&rotationId=' . EncodeQueryData($RotationId) . '&schoolTopicId=' . EncodeQueryData($schoolTopicId) . '&clinicianId=' . EncodeQueryData($ClinicianId) . '&isSendToExternalPreceptor=1';
                            } else {
                                $errorMsg = 'checkoff not found';
                            }
                        } else if ($RedirectType == 'formative') {
                            $row = $objDB->GetSingleRowValuesFromTable('studentformativemaster', 'studentFormativeMasterId', $RedirectId);
                            if ($row != '') {
                                $preceptorId = $row['preceptorId'];
                                $isPreceptorCompletedStatus = $row['isPreceptorCompletedStatus'];
                                $dateOfStudentSignature = stripslashes($row['dateOfStudentSignature']);
                                $dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $timezone);
                                $dateOfStudentSignature = date("m/d/Y", strtotime($dateOfStudentSignature));


                                if ($preceptorId > 0 && $isPreceptorCompletedStatus == 0) {
                                    $actionName = 'Pending';
                                    if ($dateOfStudentSignature != '' && $dateOfStudentSignature != '0000-00-00 00:00:00' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '0000-00-00' && $dateOfStudentSignature != '12/31/1969') {
                                        $dateOfStudentSignature = $dateOfStudentSignature;
                                    } else {
                                        $dateOfStudentSignature = "-";
                                    }
                                } else if ($dateOfStudentSignature != '' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '12/31/1969') {
                                    $dateOfStudentSignature = $dateOfStudentSignature;
                                    $actionName = 'View';
                                } else {
                                    $dateOfStudentSignature = '-';
                                    $actionName = 'Signoff';
                                }

                                if ($actionName == 'View') {
                                    $view = 1;
                                }
                                $rotationStatus = checkRotationStatus($RotationId, $isMobile = 1);
                                $view = ($rotationStatus) ? 1 : $view;

                                $redirectURL = $dynamicLoginURL . '/formative.html?studentFormativeMasterId=' . EncodeQueryData($RedirectId);

                                if ($RotationId)
                                    $redirectURL = $redirectURL . '&formativerotationid=' . EncodeQueryData($RotationId);

                                if ($preceptorId)
                                    $redirectURL = $redirectURL . '&isPreceptor=' . EncodeQueryData($preceptorId);

                                if ($view != '')
                                    $redirectURL = $redirectURL . '&view=' . $view;
                            } else {
                                $errorMsg = 'formative Evaluation not found';
                            }
                        } else if ($RedirectType == 'summative') {
                            $row = $objDB->GetSingleRowValuesFromTable('studentsummativemaster', 'studentSummativeMasterId', $RedirectId);
                            if ($row != '') {
                                $preceptorId = $row['preceptorId'];
                                $isPreceptorCompletedStatus = $row['isPreceptorCompletedStatus'];
                                $dateOfStudentSignature = stripslashes($row['dateOfStudentSignature']);
                                $dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $timezone);
                                $dateOfStudentSignature = date("m/d/Y", strtotime($dateOfStudentSignature));

                                if ($preceptorId > 0 && $isPreceptorCompletedStatus == 0) {
                                    $actionName = 'Pending';
                                    if ($dateOfStudentSignature != '' && $dateOfStudentSignature != '0000-00-00 00:00:00' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '0000-00-00' && $dateOfStudentSignature != '12/31/1969') {
                                        $dateOfStudentSignature = $dateOfStudentSignature;
                                    } else {
                                        $dateOfStudentSignature = "-";
                                    }
                                } else if ($dateOfStudentSignature != '' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '12/31/1969') {
                                    $dateOfStudentSignature = $dateOfStudentSignature;
                                    $actionName = 'View';
                                } else {
                                    $dateOfStudentSignature = '-';
                                    $actionName = 'Signoff';
                                }

                                if ($actionName == 'View') {
                                    $view = 1;
                                }

                                $rotationStatus = checkRotationStatus($RotationId, $isMobile = 1);
                                $view = ($rotationStatus) ? 1 : $view;

                                $redirectURL = $dynamicLoginURL . '/summative.html?studentSummativeMasterId=' . EncodeQueryData($RedirectId);

                                if ($RotationId)
                                    $redirectURL = $redirectURL . '&summativerotationid=' . EncodeQueryData($RotationId);

                                if ($preceptorId)
                                    $redirectURL = $redirectURL . '&isPreceptor=' . EncodeQueryData($preceptorId);

                                if ($view != '')
                                    $redirectURL = $redirectURL . '&view=' . $view;
                            } else {
                                $errorMsg = 'Summative Evaluation not found';
                            }
                        } else if ($RedirectType == 'midterm') {
                            $row = $objDB->GetSingleRowValuesFromTable('studentmidtermmaster', 'studentMidtermMasterId', $RedirectId);
                            if ($row != '') {
                                $preceptorId = $row['preceptorId'];
                                $isPreceptorCompletedStatus = $row['isPreceptorCompletedStatus'];
                                $dateOfStudentSignature = stripslashes($row['dateOfStudentSignature']);
                                $dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $timezone);
                                $dateOfStudentSignature = date("m/d/Y", strtotime($dateOfStudentSignature));
                                $studentId = $row['studentId'];
                                //Condition for view
                                // if ($dateOfStudentSignature != '11/30/-0001' && $dateOfStudentSignature != '0000-00-00' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '12/31/1969')
                                //     $view = 'V';
                                // else if ($preceptorId > 0 && $isPreceptorCompletedStatus == 0) {
                                //     $view = 'V';
                                //     $isPreceptor = EncodeQueryData($preceptorId);
                                // } else {
                                //     $view = '';
                                //     $isPreceptor = EncodeQueryData($preceptorId);
                                // }

                                if ($preceptorId > 0 && $isPreceptorCompletedStatus == 0) {
                                    $actionName = 'Pending';
                                    if ($dateOfStudentSignature != '' && $dateOfStudentSignature != '0000-00-00 00:00:00' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '0000-00-00' && $dateOfStudentSignature != '12/31/1969') {
                                        $dateOfStudentSignature = $dateOfStudentSignature;
                                    } else {
                                        $dateOfStudentSignature = "-";
                                    }
                                } else if ($dateOfStudentSignature != '' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '12/31/1969') {
                                    $dateOfStudentSignature = $dateOfStudentSignature;
                                    $actionName = 'View';
                                } else {
                                    $dateOfStudentSignature = '-';
                                    $actionName = 'Signoff';
                                }

                                if ($actionName == 'View') {
                                    $view = 1;
                                }
                                //Check the rotation is expired or not
                                // $rotationDetails = $objrotation->GetRotationByRotationId($rotationId);
                                // if ($rotationDetails != '') {
                                //     $parentRotationId = $rotationDetails['parentRotationId'];
                                //     $isSchedule = $rotationDetails['isSchedule'];
                                //     if ($parentRotationId && $isSchedule) {
                                //         $objDB = new clsDB();
                                //         $rotationDate = $objDB->GetSingleColumnValueFromTable('rotation', 'endDate', 'rotationId', $parentRotationId);
                                //         unset($objDB);
                                //     } else {
                                //         $rotationDate = $rotationDetails['endDate'];
                                //     }
                                // }

                                $givenDate = strtotime(date("Y-m-d", strtotime($rotationDate)));
                                $currentDate = strtotime(date('Y-m-d'));
                                // if ($givenDate < $currentDate) {
                                //     $view = 'V';
                                // }
                                $redirectURL = $dynamicLoginURL . '/midterm.html?studentMidtermMasterId=' . EncodeQueryData($RedirectId);


                                if ($studentId)
                                    $redirectURL = $redirectURL . '&studentId=' . EncodeQueryData($studentId);

                                if ($RotationId)
                                    $redirectURL = $redirectURL . '&midtermrotationid=' . EncodeQueryData($RotationId);

                                if ($preceptorId)
                                    $redirectURL = $redirectURL . '&isPreceptor=' . EncodeQueryData($preceptorId);

                                if ($view != '')
                                    $redirectURL = $redirectURL . '&view=' . $view;
                            } else {
                                $errorMsg = 'Midterm Evaluation not found';
                            }
                        } else if ($RedirectType == 'floorTherapy') {
                            $row = $objDB->GetSingleRowValuesFromTable('studentfloortherapyandicuevalmaster', 'studentMasterId', $RedirectId);
                            if ($row != '') {
                                $preceptorId = $row['preceptorId'];
                                $isPreceptorCompletedStatus = $row['isPreceptorCompletedStatus'];
                                $dateOfStudentSignature = stripslashes($row['dateOfStudentSignature']);
                                $dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $timezone);
                                $dateOfStudentSignature = date("m/d/Y", strtotime($dateOfStudentSignature));
                                $isType = $row['isType'];
                                // $isEval = ($isType == 1) ? 'ICU / ABG Rotation' : 'Floor Therapy';
                                //Condition for view
                                // if ($dateOfStudentSignature != '11/30/-0001' && $dateOfStudentSignature != '0000-00-00' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '12/31/1969')
                                //     $view = 'V';
                                // else if ($preceptorId > 0 && $isPreceptorCompletedStatus == 0) {
                                //     $view = 'V';
                                //     $isPreceptor = EncodeQueryData($preceptorId);
                                // } else {
                                //     $view = '';
                                //     $isPreceptor = EncodeQueryData($preceptorId);
                                // }

                                if ($preceptorId > 0 && $isPreceptorCompletedStatus == 0) {
                                    $actionName = 'View';
                                    if ($dateOfStudentSignature != '' && $dateOfStudentSignature != '0000-00-00 00:00:00' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '0000-00-00' && $dateOfStudentSignature != '12/31/1969') {
                                        $dateOfStudentSignature = $dateOfStudentSignature;
                                    } else {
                                        $dateOfStudentSignature = "-";
                                    }
                                }
                                // else if ($dateOfStudentSignature != '' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '12/31/1969') {
                                //     $dateOfStudentSignature = $dateOfStudentSignature;
                                //     $actionName = 'View';
                                // } 
                                else {
                                    $dateOfStudentSignature = '-';
                                    $actionName = 'Signoff';
                                }

                                if ($actionName == 'View') {
                                    $view = 1;
                                }
                                //Check the rotation is expired or not
                                // $rotationDetails = $objrotation->GetRotationByRotationId($rotationId);
                                // if ($rotationDetails != '') {
                                //     $parentRotationId = $rotationDetails['parentRotationId'];
                                //     $isSchedule = $rotationDetails['isSchedule'];
                                //     if ($parentRotationId && $isSchedule) {
                                //         $objDB = new clsDB();
                                //         $rotationDate = $objDB->GetSingleColumnValueFromTable('rotation', 'endDate', 'rotationId', $parentRotationId);
                                //         unset($objDB);
                                //     } else {
                                //         $rotationDate = $rotationDetails['endDate'];
                                //     }
                                // }

                                $givenDate = strtotime(date("Y-m-d", strtotime($rotationDate)));
                                $currentDate = strtotime(date('Y-m-d'));
                                // if ($givenDate < $currentDate) {
                                //     $view = 'V';
                                // }

                                $redirectURL = $dynamicLoginURL . '/addFloorTherapyAndICUEvaluation.html?studentMasterId=' . EncodeQueryData($RedirectId) . '&isEvalType=' . EncodeQueryData($isType);

                                if ($RotationId)
                                    $redirectURL = $redirectURL . '&rotationId=' . EncodeQueryData($RotationId);

                                if ($preceptorId)
                                    $redirectURL = $redirectURL . '&isPreceptor=' . EncodeQueryData($preceptorId);

                                if ($view != '')
                                    $redirectURL = $redirectURL . '&view=' . $view;
                            } else {
                                $errorMsg = 'Floor Therapy Evaluation not found';
                            }
                        } else if ($RedirectType == 'site') {
                            if ($RedirectId) {
                                $row = $objDB->GetSingleRowValuesFromTable('siteevaluationmaster', 'csEvaluationMasterId', $RedirectId);
                                if ($row != '') {

                                    $redirectURL = $dynamicLoginURL . '/siteevaluation.html?siteevaluationrotationid=' . EncodeQueryData($RotationId);

                                    if ($RedirectId)
                                        $redirectURL = $redirectURL . '&csEvaluationMasterId=' . EncodeQueryData($RedirectId);
                                } else {
                                    $errorMsg = 'Site Evaluation not found';
                                }
                            } else {
                                $redirectURL = $dynamicLoginURL . '/siteevaluation.html?siteevaluationrotationid=' . EncodeQueryData($RotationId);
                            }
                        } else if ($RedirectType == 'ciEvaluation') {
                            if ($RedirectId) {
                                $row = $objDB->GetSingleRowValuesFromTable('cievaluationmaster', 'ciEvaluationMasterId', $RedirectId);
                                if ($row != '') {

                                    $redirectURL = $dynamicLoginURL . '/cievaluation.html?cievaluationrotationid=' . EncodeQueryData($RotationId);

                                    if ($RedirectId)
                                        $redirectURL = $redirectURL . '&ciEvaluationMasterId=' . EncodeQueryData($RedirectId);
                                } else {
                                    $errorMsg = 'CI Evaluation not found';
                                }
                            } else {
                                $redirectURL = $dynamicLoginURL . '/cievaluation.html?cievaluationrotationid=' . EncodeQueryData($RotationId);
                            }
                        } else if ($RedirectType == 'pEvaluation') {
                            if ($RedirectId) {
                                $row = $objDB->GetSingleRowValuesFromTable('pevaluationmaster', 'pEvaluationMasterId', $RedirectId);
                                if ($row != '') {

                                    $redirectURL = $dynamicLoginURL . '/addpevaluation.html?pevaluationrotationid=' . EncodeQueryData($RotationId);

                                    if ($RedirectId)
                                        $redirectURL = $redirectURL . '&pEvaluationMasterId=' . EncodeQueryData($RedirectId);
                                } else {
                                    $errorMsg = 'P Evaluation not found';
                                }
                            } else {
                                $redirectURL = $dynamicLoginURL . '/addpevaluation.html?pevaluationrotationid=' . EncodeQueryData($RotationId);
                            }
                        } else if ($RedirectType == 'incident') {
                            if ($RedirectId) {

                                $row = $objDB->GetSingleRowValuesFromTable('studentsiteincidentmaster', 'studentSiteIncidentMasterId', $RedirectId);
                                if ($row != '') {

                                    $redirectURL = $dynamicLoginURL . '/addincident.html?rotationId=' . EncodeQueryData($RotationId);

                                    if ($RedirectId)
                                        $redirectURL = $redirectURL . '&incidentId=' . EncodeQueryData($RedirectId);
                                } else {
                                    $errorMsg = 'Incident not found';
                                }
                            } else {
                                $redirectURL = $dynamicLoginURL . '/addincident.html?rotationId=' . EncodeQueryData($RotationId);
                            }
                        } else if ($RedirectType == 'pefEvaluation') {
                            $row = $objDB->GetSingleRowValuesFromTable('studentpefmaster', 'studentPEFMasterId', $RedirectId);
                            if ($row != '') {

                                $dateOfStudentSignature = stripslashes($row['dateOfStudentSignature']);
                                $dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $timezone);
                                $dateOfStudentSignature = date("m/d/Y", strtotime($dateOfStudentSignature));
                                $isType = $row['isType'];
                                $studentId = $row['studentId'];


                                // if ($preceptorId > 0 && $isPreceptorCompletedStatus == 0) {
                                //     $actionName = 'View';
                                //     if ($dateOfStudentSignature != '' && $dateOfStudentSignature != '0000-00-00 00:00:00' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '0000-00-00' && $dateOfStudentSignature != '12/31/1969') {
                                //         $dateOfStudentSignature = $dateOfStudentSignature;
                                //     } else {
                                //         $dateOfStudentSignature = "-";
                                //     }
                                // }
                                // else if ($dateOfStudentSignature != '' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '12/31/1969') {
                                //     $dateOfStudentSignature = $dateOfStudentSignature;
                                //     $actionName = 'View';
                                // } 
                                // else {
                                //     $dateOfStudentSignature = '-';
                                //     $actionName = 'Signoff';
                                // }
                                if ($dateOfStudentSignature != '0000-00-00 00:00:00') {
                                    $actionName = 'View';
                                    $dateOfStudentSignature = $dateOfStudentSignature;
                                } else {
                                    $dateOfStudentSignature = "-";
                                    $actionName = 'Edit';
                                }

                                if ($actionName == 'View') {
                                    $view = 1;
                                }
                                //Check the rotation is expired or not
                                // $rotationDetails = $objrotation->GetRotationByRotationId($rotationId);
                                // if ($rotationDetails != '') {
                                //     $parentRotationId = $rotationDetails['parentRotationId'];
                                //     $isSchedule = $rotationDetails['isSchedule'];
                                //     if ($parentRotationId && $isSchedule) {
                                //         $objDB = new clsDB();
                                //         $rotationDate = $objDB->GetSingleColumnValueFromTable('rotation', 'endDate', 'rotationId', $parentRotationId);
                                //         unset($objDB);
                                //     } else {
                                //         $rotationDate = $rotationDetails['endDate'];
                                //     }
                                // }

                                // $givenDate = strtotime(date("Y-m-d", strtotime($rotationDate)));
                                // $currentDate = strtotime(date('Y-m-d'));
                                // if ($givenDate < $currentDate) {
                                //     $view = 'V';
                                // }

                                $redirectURL = $dynamicLoginURL . '/addPEFEvaluation.html?studentPEFMasterId=' . EncodeQueryData($RedirectId) . '&isPEFType=' . EncodeQueryData($isType);

                                if ($RotationId)
                                    $redirectURL = $redirectURL . '&rotationId=' . EncodeQueryData($RotationId);

                                if ($studentId)
                                    $redirectURL = $redirectURL . '&studentId=' . EncodeQueryData($studentId);


                                // if ($view != '')
                                //     $redirectURL = $redirectURL . '&view=' . $view;
                            } else {
                                $errorMsg = ' PEF Evaluation not found';
                            }
                        } else if ($RedirectType == 'mastery') {
                            if ($isActiveCheckoff == 0) {

                                $row = $objDB->GetSingleRowValuesFromTable('studentmasteryeval', 'studentMasteryEvalId', $RedirectId);
                                if ($row != '') {
                                    $dateOfStudentSignature = stripslashes($row['dateOfStudentSignature']);
                                    $dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $timezone);
                                    $dateOfStudentSignature = date("m/d/Y", strtotime($dateOfStudentSignature));
                                    $studentcomments = $row['studentcomments'];
                                    $studentsignature = $row['studentsignature'];

                                    //Condition for view
                                    // if ($dateOfStudentSignature != '11/30/-0001' && $dateOfStudentSignature != '0000-00-00' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '12/31/1969')
                                    //     $view = 'V';
                                    // else if ($preceptorId > 0 && $isPreceptorCompletedStatus == 0) {
                                    //     $view = 'V';
                                    //     $isPreceptor = EncodeQueryData($preceptorId);
                                    // } else {
                                    //     $view = '';
                                    //     $isPreceptor = EncodeQueryData($preceptorId);
                                    // }

                                    // if ($preceptorId > 0 && $isPreceptorCompletedStatus == 0) {
                                    //     $actionName = 'Pending';
                                    //     if ($dateOfStudentSignature != '' && $dateOfStudentSignature != '0000-00-00 00:00:00' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '0000-00-00' && $dateOfStudentSignature != '12/31/1969') {
                                    //         $dateOfStudentSignature = $dateOfStudentSignature;
                                    //     } else {
                                    //         $dateOfStudentSignature = "-";
                                    //     }
                                    // } else if ($dateOfStudentSignature != '' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '12/31/1969') {
                                    //     $dateOfStudentSignature = $dateOfStudentSignature;
                                    //     $actionName = 'View';
                                    // } else {
                                    //     $dateOfStudentSignature = '-';
                                    //     $actionName = 'Signoff';
                                    // }

                                    if ($studentcomments != '' && $studentsignature != '' && $dateOfStudentSignature != '11/30/-0001' && $dateOfStudentSignature != '0000-00-00 00:00:00' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '12/31/1969') {
                                        $dateOfStudentSignature = $dateOfStudentSignature;
                                        $actionName = 'View';
                                    } else {
                                        $dateOfStudentSignature = '-';
                                        $actionName = 'Edit';
                                    }


                                    if ($actionName == 'View') {
                                        $view = 'V';
                                    }
                                    //Check the rotation is expired or not
                                    // $rotationDetails = $objrotation->GetRotationByRotationId($rotationId);
                                    // if ($rotationDetails != '') {
                                    //     $parentRotationId = $rotationDetails['parentRotationId'];
                                    //     $isSchedule = $rotationDetails['isSchedule'];
                                    //     if ($parentRotationId && $isSchedule) {
                                    //         $objDB = new clsDB();
                                    //         $rotationDate = $objDB->GetSingleColumnValueFromTable('rotation', 'endDate', 'rotationId', $parentRotationId);
                                    //         unset($objDB);
                                    //     } else {
                                    //         $rotationDate = $rotationDetails['endDate'];
                                    //     }
                                    // }

                                    // $givenDate = strtotime(date("Y-m-d", strtotime($rotationDate)));
                                    // $currentDate = strtotime(date('Y-m-d'));
                                    // if ($givenDate < $currentDate) {
                                    //     $view = 'V';
                                    // }

                                    $rotationStatus = checkRotationStatus($RotationId, $isMobile = 1);
                                    $view = ($rotationStatus) ? 'V' : $view;
                                    $redirectURL = $dynamicLoginURL . '/addMastery.html?studentMasteryEvalId=' . EncodeQueryData($RedirectId);

                                    if ($RotationId)
                                        $redirectURL = $redirectURL . '&rotationId=' . EncodeQueryData($RotationId);

                                    // if ($preceptorId)
                                    //     $redirectURL = $redirectURL . '&isPreceptor=' . EncodeQueryData($preceptorId);

                                    if ($view != '')
                                        $redirectURL = $redirectURL . '&view=' . $view;
                                } else {
                                    $errorMsg = 'Mastery Evaluation not found';
                                }
                            } else {
                                $errorMsg = '';
                                header('location:' . BASE_PATH . '/webRedirect.html?status=Error&type=' . $RedirectType . '&ErrorMsg=' . $errorMsg);
                                exit();
                            }
                        } else if ($RedirectType == 'PACR') {
                            if ($RedirectId) {

                                $row = $objDB->GetSingleRowValuesFromTable('casestudypacr', 'caseStudyPacrId', $RedirectId);
                                if ($row != '') {

                                    $clinicianDate = stripslashes($row['ClinicianDate']);
                                    $schoolDate = stripslashes($row['schoolDate']);

                                    $objCaseStudy = new clsCaseStudy();
                                    $rsClinicianReviews = $objCaseStudy->GetClinicianReviews($RedirectId);
                                    $rsClinicianReviewsCount = ($rsClinicianReviews != '') ? mysqli_num_rows($rsClinicianReviews) : 0;
                                    unset($objCaseStudy);

                                    if ($clinicianDate) {
                                        $clinicianResponse = 'Yes';
                                    } else if ($clinicianDate == '' && $rsClinicianReviewsCount) {
                                        $clinicianResponse = '<p style="color: red;">Under Review</p>';
                                        $actionName = 'Edit';
                                    } else {
                                        $clinicianResponse = 'No';
                                    }

                                    if ($schoolDate) {
                                        $schoolResponse = 'Yes';
                                        $isSchoolAproved = 1;
                                    } else {
                                        $schoolResponse = 'No';
                                        $isSchoolAproved = 0;
                                    }

                                    $action = ($clinicianResponse == 'Yes' || $schoolResponse == 'Yes') ? $actionName = 'View' : $actionName = 'Edit';

                                    if ($actionName == 'View') {
                                        $view = 'V';
                                    }
                                    // Case Study Status
                                    $caseStudyStatus = $row['status'];
                                    if ($caseStudyStatus == '0') {
                                        $caseStudyStatus = 'Pending';
                                    } else if ($caseStudyStatus == '1') {
                                        $caseStudyStatus = 'Completed';
                                    }

                                    // $view = ($caseStudyStatus == 'Pending') ? 'V' : $view;

                                    //Check the rotation is expired or not
                                    // $rotationDetails = $objrotation->GetRotationByRotationId($rotationId);
                                    // if ($rotationDetails != '') {
                                    //     $parentRotationId = $rotationDetails['parentRotationId'];
                                    //     $isSchedule = $rotationDetails['isSchedule'];
                                    //     if ($parentRotationId && $isSchedule) {
                                    //         $objDB = new clsDB();
                                    //         $rotationDate = $objDB->GetSingleColumnValueFromTable('rotation', 'endDate', 'rotationId', $parentRotationId);
                                    //         unset($objDB);
                                    //     } else {
                                    //         $rotationDate = $rotationDetails['endDate'];
                                    //     }
                                    // }

                                    // $givenDate = strtotime(date("Y-m-d", strtotime($rotationDate)));
                                    // $currentDate = strtotime(date('Y-m-d'));
                                    // if ($givenDate < $currentDate) {
                                    //     $view = 'V';
                                    // }
                                    //Check the rotation is expired or not
                                    $rotationStatus = checkRotationStatus($RotationId);
                                    $view = ($rotationStatus) ? 'V' : $view;
                                    $redirectURL = $dynamicLoginURL . '/addCaseStudyPACR.html?caseStudyId=' . EncodeQueryData($RedirectId);

                                    if ($view != '')
                                        $redirectURL = $redirectURL . '&view=' . $view;
                                } else {
                                    $errorMsg = 'PACR Evaluation not found';
                                }
                            } else {
                                $redirectURL = $dynamicLoginURL . '/addCaseStudyPACR.html?caseStudyId=' . EncodeQueryData($RedirectId);
                            }
                        } else if ($RedirectType == 'floorCaseStudy') {
                            if ($RedirectId) {

                                $row = $objDB->GetSingleRowValuesFromTable('casestudyfloor', 'caseStudyFloorId', $RedirectId);
                                if ($row != '') {

                                    $clinicianDate = stripslashes($row['ClinicianDate']);
                                    $schoolDate = stripslashes($row['schoolDate']);

                                    $objCaseStudy = new clsCaseStudy();
                                    $rsClinicianReviews = $objCaseStudy->GetClinicianReviews($RedirectId);
                                    $rsClinicianReviewsCount = ($rsClinicianReviews != '') ? mysqli_num_rows($rsClinicianReviews) : 0;
                                    unset($objCaseStudy);

                                    if ($clinicianDate) {
                                        $clinicianResponse = 'Yes';
                                    } else if ($clinicianDate == '' && $rsClinicianReviewsCount) {
                                        $clinicianResponse = '<p style="color: red;">Under Review</p>';
                                        $actionName = 'Edit';
                                    } else {
                                        $clinicianResponse = 'No';
                                    }

                                    if ($schoolDate) {
                                        $schoolResponse = 'Yes';
                                        $isSchoolAproved = 1;
                                    } else {
                                        $schoolResponse = 'No';
                                        $isSchoolAproved = 0;
                                    }

                                    $action = ($clinicianResponse == 'Yes' || $schoolResponse == 'Yes') ? $actionName = 'View' : $actionName = 'Edit';

                                    if ($actionName == 'View') {
                                        $view = 'V';
                                    }
                                    //Check the rotation is expired or not
                                    // $rotationDetails = $objrotation->GetRotationByRotationId($rotationId);
                                    // if ($rotationDetails != '') {
                                    //     $parentRotationId = $rotationDetails['parentRotationId'];
                                    //     $isSchedule = $rotationDetails['isSchedule'];
                                    //     if ($parentRotationId && $isSchedule) {
                                    //         $objDB = new clsDB();
                                    //         $rotationDate = $objDB->GetSingleColumnValueFromTable('rotation', 'endDate', 'rotationId', $parentRotationId);
                                    //         unset($objDB);
                                    //     } else {
                                    //         $rotationDate = $rotationDetails['endDate'];
                                    //     }
                                    // }

                                    // $givenDate = strtotime(date("Y-m-d", strtotime($rotationDate)));
                                    // $currentDate = strtotime(date('Y-m-d'));
                                    // if ($givenDate < $currentDate) {
                                    //     $view = 'V';
                                    // }
                                    //Check the rotation is expired or not
                                    $rotationStatus = checkRotationStatus($RotationId);
                                    $view = ($rotationStatus) ? 'V' : $view;

                                    $redirectURL = $dynamicLoginURL . '/addCaseStudy.html?caseStudyId=' . EncodeQueryData($RedirectId);

                                    if ($view != '')
                                        $redirectURL = $redirectURL . '&view=' . $view;
                                } else {
                                    $errorMsg = 'Floor Therapy Evaluation not found';
                                }
                            } else {
                                $redirectURL = $dynamicLoginURL . '/addCaseStudy.html?caseStudyId=' . EncodeQueryData($RedirectId);
                            }
                        } else if ($RedirectType == 'adult') {
                            //  echo $RedirectId;
                            if ($RedirectId) {

                                $row = $objDB->GetSingleRowValuesFromTable('casestudyadult', 'caseStudyAdultId', $RedirectId);
                                if ($row != '') {

                                    $clinicianDate = stripslashes($row['ClinicianDate']);
                                    $schoolDate = stripslashes($row['schoolDate']);

                                    $objCaseStudy = new clsCaseStudy();
                                    $rsClinicianReviews = $objCaseStudy->GetClinicianReviews($RedirectId);
                                    $rsClinicianReviewsCount = ($rsClinicianReviews != '') ? mysqli_num_rows($rsClinicianReviews) : 0;
                                    unset($objCaseStudy);

                                    if ($clinicianDate) {
                                        $clinicianResponse = 'Yes';
                                    } else if ($clinicianDate == '' && $rsClinicianReviewsCount) {
                                        $clinicianResponse = '<p style="color: red;">Under Review</p>';
                                        $actionName = 'Edit';
                                    } else {
                                        $clinicianResponse = 'No';
                                    }

                                    if ($schoolDate) {
                                        $schoolResponse = 'Yes';
                                        $isSchoolAproved = 1;
                                    } else {
                                        $schoolResponse = 'No';
                                        $isSchoolAproved = 0;
                                    }

                                    $action = ($clinicianResponse == 'Yes' || $schoolResponse == 'Yes') ? $actionName = 'View' : $actionName = 'Edit';

                                    if ($actionName == 'View') {
                                        $view = 'V';
                                    }
                                    //Check the rotation is expired or not
                                    // $rotationDetails = $objrotation->GetRotationByRotationId($rotationId);
                                    // if ($rotationDetails != '') {
                                    //     $parentRotationId = $rotationDetails['parentRotationId'];
                                    //     $isSchedule = $rotationDetails['isSchedule'];
                                    //     if ($parentRotationId && $isSchedule) {
                                    //         $objDB = new clsDB();
                                    //         $rotationDate = $objDB->GetSingleColumnValueFromTable('rotation', 'endDate', 'rotationId', $parentRotationId);
                                    //         unset($objDB);
                                    //     } else {
                                    //         $rotationDate = $rotationDetails['endDate'];
                                    //     }
                                    // }

                                    // $givenDate = strtotime(date("Y-m-d", strtotime($rotationDate)));
                                    // $currentDate = strtotime(date('Y-m-d'));
                                    // if ($givenDate < $currentDate) {
                                    //     $view = 'V';
                                    // }
                                    //Check the rotation is expired or not
                                    $rotationStatus = checkRotationStatus($RotationId);
                                    $view = ($rotationStatus) ? 'V' : $view;
                                    $redirectURL = $dynamicLoginURL . '/addCaseStudyAdult.html?caseStudyId=' . EncodeQueryData($RedirectId);

                                    if ($view != '')
                                        $redirectURL = $redirectURL . '&view=' . $view;
                                } else {
                                    $errorMsg = 'Adult ICU & NICU/PICU Evaluation not found';
                                }
                            } else {
                                $redirectURL = $dynamicLoginURL . '/addCaseStudyAdult.html?caseStudyId=' . EncodeQueryData($RedirectId);
                            }
                        } else if ($RedirectType == 'activitysheet') {
                            //  echo $RedirectId;
                            if ($RedirectId) {

                                $row = $objDB->GetSingleRowValuesFromTable('activitysheetdetails', 'activityId', $RedirectId);
                                if ($row != '') {

                                    $clinicianDate = stripslashes($row['clinicianSignatureDate']);

                                    if ($clinicianDate != '0000-00-00')
                                        $clinicianResponse = 'Yes';
                                    else
                                        $clinicianResponse = 'No';

                                    $action = ($clinicianResponse == 'Yes') ? $actionName = 'View' : $actionName = 'Edit';

                                    if ($actionName == 'View') {
                                        $view = 'V';
                                    }

                                    $redirectURL = $dynamicLoginURL . '/activitySheet.html?editactivityid=' . EncodeQueryData($RedirectId);

                                    if ($view != '')
                                        $redirectURL = $redirectURL . '&view=' . $view;
                                } else {
                                    $errorMsg = 'Activity Sheet not found';
                                }
                            } else {
                                $redirectURL = $dynamicLoginURL . '/activitySheet.html?activityId=' . $RedirectId;
                            }
                        } else if ($RedirectType == 'volunteer') {

                            if ($RedirectId) {

                                $row = $objDB->GetSingleRowValuesFromTable('volunteereval', 'volunteerEvalId', $RedirectId);
                                if ($row != '') {

                                    $dateOfClinicianSignature = stripslashes($row['clinicianDate']);

                                    if ($dateOfClinicianSignature != '' && $dateOfClinicianSignature != '01/01/1970' && $dateOfClinicianSignature != '12/31/1969') {
                                        $dateOfClinicianSignature = $dateOfClinicianSignature;
                                        $actionName = 'View';
                                    } else {
                                        $dateOfClinicianSignature = '-';
                                        $actionName = 'Edit';
                                    }


                                    $rotationStatus = checkRotationStatus($RotationId, $isMobile = 1);
                                    $view = ($rotationStatus) ? 1 : $view;

                                    $redirectURL = $dynamicLoginURL . '/addVolunteerEval.html?volunteerEvalId=' . EncodeQueryData($RedirectId);
                                } else {
                                    $errorMsg = 'volunteer evaluation not found';
                                }
                            } else {
                                $redirectURL = $dynamicLoginURL . '/addVolunteerEval.html?rotationId=' . EncodeQueryData($RotationId);
                            }
                        } else {
                            $errorMsg = 'Invalid Redirect Type';
                        }
                    } else {
                        $errorMsg = 'Invalid Redirect Type';
                    }
                } else {
                    $errorMsg = 'Invalid redirect Id';
                }
            } else {
                $errorMsg = 'Invalid User';
            }
        } else {
            $errorMsg = 'Invalid Access Token';
        }
    } else {
        $errorMsg = 'Invalid User Id';
    }
} elseif ($UserType == 'C') {
    if ($UserId) {
        //validate AccessToken
        if (ValidateUserAccessToken($UserId, $AccessToken) == True) {
            $objClinician = new clsClinician();
            $rowClinician =  $objClinician->GetClinicianDetails($UserId);

            if ($rowClinician != '') {
                $schoolId = $rowClinician['schoolId'];
                $clinicianId = $rowClinician['clinicianId'];

                // Get school Details
                $objSchool = new clsSchool();
                $schoolDetails = $objSchool->GetschoolDetails($schoolId);
                $schoolSlug = stripslashes($schoolDetails['slug']);
                unset($objSchool);

                $dynamicLoginURL = BASE_PATH . '/school/' . $schoolSlug . '/clinician';

                //Get System User profile Image
                $profileImageName = stripslashes($rowClinician['smallProfilePic']);
                $defaultProfileImagePath = GetClinicianImagePath($clinicianId, $schoolId, $profileImageName);

                $profileLargeImageName = stripslashes($rowClinician['profilePic']);
                $defaultProfileLargeImagePath = GetClinicianImagePath($clinicianId, $schoolId, $profileLargeImageName);

                $clinicianTimeZone = $objClinician->GetClinicianTimeZoneByClinicianId($UserId);
                if ($clinicianTimeZone == '')
                    $timezone = $rowClinician['timezone'];
                else
                    $timezone = $clinicianTimeZone;



                @session_start();
                $_SESSION["loggedClinicianId"] = $UserId;
                $_SESSION["loggedclinicianRoleId"] = stripslashes($rowClinician['clinicianRoleId']);
                $_SESSION["loggedClinicianName"] = stripslashes($rowClinician['username']);
                $_SESSION["loggedClinicianFirstName"] =  stripslashes($rowClinician['firstName']);
                $_SESSION["loggedClinicianLastName"] = stripslashes($rowClinician['lastName']);
                $_SESSION["loggedClinicianSchoolId"] = stripslashes($rowClinician['schoolId']);
                $_SESSION["loggedClinicianEmail"] = stripslashes($rowClinician['email']);
                $_SESSION["loggedClinicianProfileImagePath"] = $defaultProfileImagePath;
                $_SESSION["loggedClinicianProfileLargeImagePath"] = $defaultProfileLargeImagePath;
                $_SESSION["loggedClinicianSchoolTimeZone"] = ($rowClinician['timezone']);
                $_SESSION["isActiveCheckoff"] = ($rowClinician['isActiveCheckoff']);
                $_SESSION["loggedClinicianType"] = ($rowClinician['loggedClinicianType']);
                $_SESSION["isDefaultCiEval"] = ($rowClinician['isDefaultCiEval']);
                $_SESSION["IsMobile"] = 1;

                $isActiveCheckoff = stripslashes($rowClinician['isActiveCheckoff']);


                if ($RedirectId || $isAdd) {
                    //check redirect type
                    if ($RedirectType != '') {
                        if ($RedirectType == 'PACR') {

                            $row = $objDB->GetSingleRowValuesFromTable('casestudypacr', 'caseStudyPacrId', $RedirectId);
                            if ($row != '') {

                                $clinicianDate = stripslashes($row['ClinicianDate']);
                                $schoolDate = stripslashes($row['schoolDate']);

                                $objCaseStudy = new clsCaseStudy();
                                $rsClinicianReviews = $objCaseStudy->GetClinicianReviews($RedirectId);
                                $rsClinicianReviewsCount = ($rsClinicianReviews != '') ? mysqli_num_rows($rsClinicianReviews) : 0;
                                unset($objCaseStudy);
                                if ($clinicianDate) {
                                    $clinicianResponse = 'Yes';
                                } else if ($clinicianDate == '' && $rsClinicianReviewsCount) {
                                    $clinicianResponse = '<p style="color: red;">Under Review</p>';
                                    $actionName = 'Edit';
                                    $action = ($clinicianResponse == 'Yes') ? $actionName = 'View' : $actionName = 'Edit';

                                    if ($action == 'View') {
                                        $view = 'V';
                                    }
                                    //Check the rotation is expired or not
                                    // $rotationDetails = $objrotation->GetRotationByRotationId($rotationId);
                                    // if ($rotationDetails != '') {
                                    //     $parentRotationId = $rotationDetails['parentRotationId'];
                                    //     $isSchedule = $rotationDetails['isSchedule'];
                                    //     if ($parentRotationId && $isSchedule) {
                                    //         $objDB = new clsDB();
                                    //         $rotationDate = $objDB->GetSingleColumnValueFromTable('rotation', 'endDate', 'rotationId', $parentRotationId);
                                    //         unset($objDB);
                                    //     } else {
                                    //         $rotationDate = $rotationDetails['endDate'];
                                    //     }
                                    // }
                                    // $givenDate = strtotime(date("Y-m-d", strtotime($rotationDate)));
                                    // $currentDate = strtotime(date('Y-m-d'));
                                    // if ($givenDate < $currentDate) {
                                    //     $view = 'V';
                                    // }

                                    $redirectURL = $dynamicLoginURL . '/addCaseStudyPACR.html?caseStudyId=' . EncodeQueryData($RedirectId);

                                    if ($view != '')
                                        $redirectURL = $redirectURL . '&view=' . $view;
                                } else {
                                    $clinicianResponse = 'No';
                                }
                                if ($schoolDate) {
                                    $schoolResponse = 'Yes';
                                    $isSchoolAproved = 1;
                                } else {
                                    $schoolResponse = 'No';
                                    $isSchoolAproved = 0;
                                }

                                $action = ($clinicianResponse == 'Yes' || $schoolResponse == 'Yes') ? $actionName = 'View' : $actionName = 'Edit';

                                if ($actionName == 'View') {
                                    $view = 'V';
                                }

                                // Case Study Status
                                $caseStudyStatus = $row['status'];
                                if ($caseStudyStatus == '0') {
                                    $caseStudyStatus = 'Pending';
                                } else if ($caseStudyStatus == '1') {
                                    $caseStudyStatus = 'Completed';
                                }

                                $view = ($caseStudyStatus == 'Pending') ? 'V' : $view;

                                //Check the rotation is expired or not
                                // $rotationDetails = $objrotation->GetRotationByRotationId($rotationId);
                                // if ($rotationDetails != '') {
                                //     $parentRotationId = $rotationDetails['parentRotationId'];
                                //     $isSchedule = $rotationDetails['isSchedule'];
                                //     if ($parentRotationId && $isSchedule) {
                                //         $objDB = new clsDB();
                                //         $rotationDate = $objDB->GetSingleColumnValueFromTable('rotation', 'endDate', 'rotationId', $parentRotationId);
                                //         unset($objDB);
                                //     } else {
                                //         $rotationDate = $rotationDetails['endDate'];
                                //     }
                                // }
                                // $givenDate = strtotime(date("Y-m-d", strtotime($rotationDate)));
                                // $currentDate = strtotime(date('Y-m-d'));
                                // if ($givenDate < $currentDate) {
                                //     $view = 'V';
                                // }

                                $redirectURL = $dynamicLoginURL . '/addCaseStudyPACR.html?caseStudyId=' . EncodeQueryData($RedirectId);

                                if ($view != '')
                                    $redirectURL = $redirectURL . '&view=' . $view;
                            } else {
                                $errorMsg = 'PACR Evaluation not found';
                            }
                        } else if ($RedirectType == 'floorCaseStudy') {

                            $row = $objDB->GetSingleRowValuesFromTable('casestudyfloor', 'caseStudyFloorId', $RedirectId);
                            if ($row != '') {

                                $clinicianDate = stripslashes($row['ClinicianDate']);
                                $schoolDate = stripslashes($row['schoolDate']);

                                $objCaseStudy = new clsCaseStudy();
                                $rsClinicianReviews = $objCaseStudy->GetClinicianReviews($RedirectId);
                                $rsClinicianReviewsCount = ($rsClinicianReviews != '') ? mysqli_num_rows($rsClinicianReviews) : 0;
                                unset($objCaseStudy);
                                if ($clinicianDate) {
                                    $clinicianResponse = 'Yes';
                                } else if ($clinicianDate == '' && $rsClinicianReviewsCount) {
                                    $clinicianResponse = '<p style="color: red;">Under Review</p>';
                                    $actionName = 'Edit';
                                } else {
                                    $clinicianResponse = 'No';
                                }
                                if ($schoolDate) {
                                    $schoolResponse = 'Yes';
                                    $isSchoolAproved = 1;
                                } else {
                                    $schoolResponse = 'No';
                                    $isSchoolAproved = 0;
                                }

                                $action = ($clinicianResponse == 'Yes' || $schoolResponse == 'Yes') ? $actionName = 'View' : $actionName = 'Edit';

                                if ($actionName == 'View') {
                                    $view = 'V';
                                }
                                //Check the rotation is expired or not
                                // $rotationDetails = $objrotation->GetRotationByRotationId($rotationId);
                                // if ($rotationDetails != '') {
                                //     $parentRotationId = $rotationDetails['parentRotationId'];
                                //     $isSchedule = $rotationDetails['isSchedule'];
                                //     if ($parentRotationId && $isSchedule) {
                                //         $objDB = new clsDB();
                                //         $rotationDate = $objDB->GetSingleColumnValueFromTable('rotation', 'endDate', 'rotationId', $parentRotationId);
                                //         unset($objDB);
                                //     } else {
                                //         $rotationDate = $rotationDetails['endDate'];
                                //     }
                                // }
                                // $givenDate = strtotime(date("Y-m-d", strtotime($rotationDate)));
                                // $currentDate = strtotime(date('Y-m-d'));
                                // if ($givenDate < $currentDate) {
                                //     $view = 'V';
                                // }

                                // Case Study Status
                                $caseStudyStatus = $row['status'];
                                if ($caseStudyStatus == '0') {
                                    $caseStudyStatus = 'Pending';
                                } else if ($caseStudyStatus == '1') {
                                    $caseStudyStatus = 'Completed';
                                }

                                $view = ($caseStudyStatus == 'Pending') ? 'V' : $view;

                                $redirectURL = $dynamicLoginURL . '/addCaseStudy.html?caseStudyId=' . EncodeQueryData($RedirectId);

                                if ($view != '')
                                    $redirectURL = $redirectURL . '&view=' . $view;
                            } else {
                                $errorMsg = 'Floor Therapy Evaluation not found';
                            }
                        } else if ($RedirectType == 'adult') {

                            $row = $objDB->GetSingleRowValuesFromTable('casestudyadult', 'caseStudyAdultId', $RedirectId);
                            if ($row != '') {

                                $clinicianDate = stripslashes($row['ClinicianDate']);
                                $schoolDate = stripslashes($row['schoolDate']);

                                $objCaseStudy = new clsCaseStudy();
                                $rsClinicianReviews = $objCaseStudy->GetClinicianReviews($RedirectId);
                                $rsClinicianReviewsCount = ($rsClinicianReviews != '') ? mysqli_num_rows($rsClinicianReviews) : 0;
                                unset($objCaseStudy);
                                if ($clinicianDate) {
                                    $clinicianResponse = 'Yes';
                                } else if ($clinicianDate == '' && $rsClinicianReviewsCount) {
                                    $clinicianResponse = '<p style="color: red;">Under Review</p>';
                                    $actionName = 'Edit';
                                } else {
                                    $clinicianResponse = 'No';
                                }
                                if ($schoolDate) {
                                    $schoolResponse = 'Yes';
                                    $isSchoolAproved = 1;
                                } else {
                                    $schoolResponse = 'No';
                                    $isSchoolAproved = 0;
                                }

                                // Case Study Status
                                $caseStudyStatus = $row['status'];
                                if ($caseStudyStatus == '0') {
                                    $caseStudyStatus = 'Pending';
                                } else if ($caseStudyStatus == '1') {
                                    $caseStudyStatus = 'Completed';
                                }

                                $view = ($caseStudyStatus == 'Pending') ? 'V' : $view;
                                $action = ($clinicianResponse == 'Yes' || $schoolResponse == 'Yes') ? $actionName = 'View' : $actionName = 'Edit';

                                if ($actionName == 'View') {
                                    $view = 'V';
                                }
                                //Check the rotation is expired or not
                                // $rotationDetails = $objrotation->GetRotationByRotationId($rotationId);
                                // if ($rotationDetails != '') {
                                //     $parentRotationId = $rotationDetails['parentRotationId'];
                                //     $isSchedule = $rotationDetails['isSchedule'];
                                //     if ($parentRotationId && $isSchedule) {
                                //         $objDB = new clsDB();
                                //         $rotationDate = $objDB->GetSingleColumnValueFromTable('rotation', 'endDate', 'rotationId', $parentRotationId);
                                //         unset($objDB);
                                //     } else {
                                //         $rotationDate = $rotationDetails['endDate'];
                                //     }
                                // }
                                // $givenDate = strtotime(date("Y-m-d", strtotime($rotationDate)));
                                // $currentDate = strtotime(date('Y-m-d'));
                                // if ($givenDate < $currentDate) {
                                //     $view = 'V';
                                // }

                                $redirectURL = $dynamicLoginURL . '/addCaseStudyAdult.html?caseStudyId=' . EncodeQueryData($RedirectId);

                                if ($view != '')
                                    $redirectURL = $redirectURL . '&view=' . $view;
                            } else {
                                $errorMsg = 'Adult ICU & NICU/PICU Evaluation not found';
                            }
                        } else if ($RedirectType == 'daily/weekly') {
                            if ($RedirectId) {
                                $row = $objDB->GetSingleRowValuesFromTable('studentdailymaster', 'studentDailyMasterId', $RedirectId);
                                if ($row != '') {
                                    // $preceptorId = $row['preceptorId'];
                                    // $isPreceptorCompletedStatus = $row['isPreceptorCompletedStatus'];
                                    $dateOfStudentSignature = stripslashes($row['dateOfStudentSignature']);
                                    $dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $timezone);
                                    $dateOfStudentSignature = date("m/d/Y", strtotime($dateOfStudentSignature));
                                    $rotationId = $row['rotationId'];
                                    //Condition for view
                                    // if ($dateOfStudentSignature != '11/30/-0001' && $dateOfStudentSignature != '0000-00-00' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '12/31/1969') {
                                    //     $view = 'V';
                                    //     if ($preceptorId > 0)
                                    //         $isPreceptor = EncodeQueryData($preceptorId);
                                    // } else if ($preceptorId > 0 && $isPreceptorCompletedStatus == 0) {
                                    //     $view = 'V';
                                    //     $isPreceptor = EncodeQueryData($preceptorId);
                                    // } else {
                                    //     $view = '';
                                    //     $isPreceptor = EncodeQueryData($preceptorId);
                                    // }
                                    if ($dateOfStudentSignature != '11/30/-0001' && $dateOfStudentSignature != '0000-00-00' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '12/31/1969') {

                                        $actionName = 'View';
                                    } else if ($RotationId > 0) {
                                        $actionName = 'Edit';
                                    } else {
                                        $actionName = 'Edit';
                                    }

                                    if ($actionName == 'View') {
                                        $view = 'V';
                                    }

                                    $redirectURL = $dynamicLoginURL . '/dailyEval.html?studentDailyMasterId=' . EncodeQueryData($RedirectId);

                                    if ($RotationId) {
                                        $redirectURL = $redirectURL . '&rotationId=' . EncodeQueryData($RotationId) . '&Type=' . 'A';
                                    }

                                    if ($view != '')
                                        $redirectURL = $redirectURL . '&view=' . $view;
                                } else {
                                    $errorMsg = 'daily/weekly not found';
                                }
                            } else {
                                $RedirectId = 0;
                                $redirectURL = $dynamicLoginURL . '/dailyEval.html?studentDailyMasterId=' . EncodeQueryData($RedirectId);

                                if ($RotationId) {
                                    $redirectURL = $redirectURL . '&rotationId=' . EncodeQueryData($RotationId);
                                }
                            }
                        } else if ($RedirectType == 'checkoff') {

                            if ($RedirectId) {

                                $row = $objDB->GetSingleRowValuesFromTable('checkoff', 'checkoffId', $RedirectId);
                                if ($row != '') {
                                    $isExternalPreceptorcheckoff = $row['isExternalPreceptorcheckoff'];
                                    $schoolTopicId = $row['schoolTopicId'];
                                    $rotationId = stripslashes($row['rotationId']);
                                    // if($isActiveCheckoff ==0){
                                    // $preceptorId = $row['completion1stPreceptorId'];
                                    // $isPreceptorCompletedStatus = $row['is1stCompletionStatus'];
                                    // $studentId = stripslashes($row['studentId']);
                                    // $checkoffId = stripslashes($row['checkoffId']);
                                    // $studentComment  = stripslashes($row['studentComment']);

                                    // $dateOfStudentSignature = stripslashes($row['student_evaluationDate']);


                                    // $dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $timezone);
                                    // $dateOfStudentSignature = date("m/d/Y", strtotime($dateOfStudentSignature));

                                    // if ($dateOfStudentSignature != '11/30/-0001' && $dateOfStudentSignature != '0000-00-00' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '12/31/1969' || $studentComment != '')
                                    //         $view = 'V';

                                    // }else 
                                    if ($isActiveCheckoff == 1) {

                                        $preceptorId = $row['completion1stPreceptorId'];
                                        $isPreceptorCompletedStatus = $row['is1stCompletionStatus'];
                                        $studentId = stripslashes($row['studentId']);
                                        $checkoffId = stripslashes($row['checkoffId']);
                                        $dateOfStudentSignature = stripslashes($row['student_evaluationDate']);
                                        $studentComment  = stripslashes($row['studentComment']);

                                        $dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $timezone);
                                        $dateOfStudentSignature = date("m/d/Y", strtotime($dateOfStudentSignature));

                                        //Condition for view
                                        if ($dateOfStudentSignature != '11/30/-0001' && $dateOfStudentSignature != '0000-00-00' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '12/31/1969')
                                            $view = 'V';
                                        else
                                            $view = '';

                                        //Check the rotation is expired or not
                                        $rotationDetails = $objrotation->GetRotationByRotationId($rotationId);
                                        if ($rotationDetails != '') {
                                            $parentRotationId = $rotationDetails['parentRotationId'];
                                            $isSchedule = $rotationDetails['isSchedule'];
                                            if ($parentRotationId && $isSchedule) {
                                                $objDB = new clsDB();
                                                $rotationDate = $objDB->GetSingleColumnValueFromTable('rotation', 'endDate', 'rotationId', $parentRotationId);
                                                unset($objDB);
                                            } else {
                                                $rotationDate = $rotationDetails['endDate'];
                                            }
                                        }

                                        // $givenDate = strtotime(date("Y-m-d", strtotime($rotationDate)));
                                        // $currentDate = strtotime(date('Y-m-d'));
                                        // if ($givenDate < $currentDate) {
                                        //     $view = 'V';
                                        // }
                                        $redirectURL = $dynamicLoginURL . '/addcheckoff.html?schoolTopicId=' . EncodeQueryData($schoolTopicId) . '&checkoffId=' . EncodeQueryData($checkoffId);

                                        if ($RotationId)
                                            $redirectURL = $redirectURL . '&rotationId=' . EncodeQueryData($RotationId);

                                        // if ($isPreceptor)
                                        //     $redirectURL = $redirectURL . '&isPreceptor=' . EncodeQueryData($isPreceptor);

                                        if ($view != '')
                                            $redirectURL = $redirectURL . '&view=' . $view;
                                    } else if ($isActiveCheckoff == 2) {

                                        $preceptorId = $row['completion1stPreceptorId'];
                                        $isPreceptorCompletedStatus = $row['is1stCompletionStatus'];
                                        $studentId = stripslashes($row['studentId']);
                                        $checkoffId = stripslashes($row['checkoffId']);
                                        $dateOfStudentSignature = stripslashes($row['student_evaluationDate']);
                                        $studentComment  = stripslashes($row['studentComment']);

                                        $dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $timezone);
                                        $dateOfStudentSignature = date("m/d/Y", strtotime($dateOfStudentSignature));

                                        //Condition for view
                                        if ($dateOfStudentSignature != '11/30/-0001' && $dateOfStudentSignature != '0000-00-00' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '12/31/1969')
                                            $view = 'V';
                                        // else if ($preceptorId > 0 && $isPreceptorCompletedStatus == 0) {
                                        //     $view = 'V';
                                        // } 
                                        else
                                            //  {
                                            $view = '';
                                        // }
                                        //Check the rotation is expired or not
                                        $rotationDetails = $objrotation->GetRotationByRotationId($rotationId);
                                        if ($rotationDetails != '') {
                                            $parentRotationId = $rotationDetails['parentRotationId'];
                                            $isSchedule = $rotationDetails['isSchedule'];
                                            if ($parentRotationId && $isSchedule) {
                                                $objDB = new clsDB();
                                                $rotationDate = $objDB->GetSingleColumnValueFromTable('rotation', 'endDate', 'rotationId', $parentRotationId);
                                                unset($objDB);
                                            } else {
                                                $rotationDate = $rotationDetails['endDate'];
                                            }
                                        }

                                        // $givenDate = strtotime(date("Y-m-d", strtotime($rotationDate)));
                                        // $currentDate = strtotime(date('Y-m-d'));
                                        // if ($givenDate < $currentDate) {
                                        //     $view = 'V';
                                        // }

                                        $studentIdsEncoded = array_map('EncodeQueryData', $studentIds);
                                        $studentIdParam = implode(',', $studentIdsEncoded);
                                        $redirectURL = $dynamicLoginURL . '/addusafcheckoff.html?schoolTopicId=' . EncodeQueryData($schoolTopicId) . '&checkoffId=' . EncodeQueryData($RedirectId) . '&studentId=' . $studentIdParam;

                                        if ($RotationId)
                                            $redirectURL = $redirectURL . '&rotationId=' . EncodeQueryData($RotationId);

                                        // if ($isPreceptor)
                                        //     $redirectURL = $redirectURL . '&isPreceptor=' . EncodeQueryData($isPreceptor);

                                        if ($view != '')
                                            $redirectURL = $redirectURL . '&view=' . $view;
                                    } else {

                                        $preceptorId = $row['completion1stPreceptorId'];
                                        $isPreceptorCompletedStatus = $row['is1stCompletionStatus'];
                                        $studentId = stripslashes($row['studentId']);
                                        $checkoffId = stripslashes($row['checkoffId']);
                                        $dateOfStudentSignature = stripslashes($row['student_evaluationDate']);

                                        if ($dateOfStudentSignature != '' && $dateOfStudentSignature != '0000-00-00 00:00:00') {
                                            $dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $timezone);
                                            $dateOfStudentSignature = date("m/d/Y", strtotime($dateOfStudentSignature));
                                        } else {
                                            $dateOfStudentSignature = "";
                                        }

                                        //For Lab Selected Questions
                                        $objcheckoff = new clscheckoff();

                                        $selectedLab = $objcheckoff->GetSelectedLabQuestions($checkoffId);
                                        $selectedLab = ($selectedLab > 6) ? 1 : $selectedLab;
                                        // //For Clinical Selected Questions
                                        $selectedClinical = $objcheckoff->GetSelectedClinicalQuestions($checkoffId);
                                        $selectedClinical = ($selectedClinical > 6) ? 1 : $selectedClinical;

                                        //Get Selected Preceptor Questions 
                                        $selectedPreceptor = $objcheckoff->GetSelectedPreceptorQuestions($checkoffId);
                                        if ($selectedPreceptor > 0)
                                            $selectedPreceptor = ($selectedPreceptor * 20) . '%';
                                        else
                                            $selectedPreceptor = '0';
                                        //For Student Selected Questions
                                        $objcheckoff = new clscheckoff();

                                        $totalStudentQuestions = $objcheckoff->GetSelectedStudentQuestions($checkoffId);
                                        unset($objcheckoff);

                                        if ($totalStudentQuestions > 0)
                                            $totalStudentQuestions = ($totalStudentQuestions * 20) . '%';
                                        else
                                            $totalStudentQuestions = '0';

                                        //Condition for view
                                        if ($dateOfStudentSignature != '' && $totalStudentQuestions == '100%' && $selectedClinical  && $selectedLab  && $selectedPreceptor == '100%') {
                                            $view = 'V';
                                        } else {
                                            $view = ' ';
                                        }

                                        // $rotationStatus = checkRotationStatus($RotationId, $isMobile = 1);
                                        // if ($rotationStatus) {
                                        //     $view = 'V';
                                        // }
                                        $redirectURL = $dynamicLoginURL . '/addcheckoff.html?schoolTopicId=' . EncodeQueryData($schoolTopicId) . '&checkoffId=' . EncodeQueryData($checkoffId);

                                        if ($RotationId)
                                            $redirectURL = $redirectURL . '&rotationId=' . EncodeQueryData($RotationId);

                                        // if ($isPreceptor)
                                        //     $redirectURL = $redirectURL . '&isPreceptor=' . EncodeQueryData($isPreceptor);

                                        if ($view != '')
                                            $redirectURL = $redirectURL . '&view=' . $view;
                                        unset($objcheckoff);
                                    }
                                }
                            } else {

                                $studentIdsEncoded = array_map('EncodeQueryData', $studentIds);
                                $studentIdParam = implode(',', $studentIdsEncoded);

                                if ($isActiveCheckoff == 2) {
                                    $redirectURL = $dynamicLoginURL . '/addusafcheckoff.html?UserId=' . EncodeQueryData($UserId) . '&startdatetime=' . $CheckoffDate . '&rotationId=' . EncodeQueryData($RotationId) . '&schoolTopicId=' . EncodeQueryData($schoolTopicId) . '&studentId=' . $studentIdParam;
                                } else
                                    $redirectURL = $dynamicLoginURL . '/addcheckoff.html?UserId=' . EncodeQueryData($UserId) . '&startdatetime=' . $CheckoffDate . '&rotationId=' . EncodeQueryData($RotationId) . '&schoolTopicId=' . EncodeQueryData($schoolTopicId) . '&studentId=' . $studentIdParam;
                            }
                        } else if ($RedirectType == 'activitysheet') {

                            $row = $objDB->GetSingleRowValuesFromTable('activitysheetdetails', 'activityId', $RedirectId);
                            if ($row != '') {

                                $clinicianDate = stripslashes($row['clinicianSignatureDate']);

                                if ($clinicianDate != '0000-00-00')
                                    $clinicianResponse = 'Yes';
                                else
                                    $clinicianResponse = 'No';

                                $action = ($clinicianResponse == 'Yes') ? $actionName = 'View' : $actionName = 'Edit';

                                if ($actionName == 'View') {
                                    $view = 'V';
                                }

                                $redirectURL = $dynamicLoginURL . '/activitySheet.html?editactivityid=' . EncodeQueryData($RedirectId);

                                if ($view != '')
                                    $redirectURL = $redirectURL . '&view=' . $view;
                            } else {
                                $errorMsg = 'Activity Sheet not found';
                            }
                        } else if ($RedirectType == 'ciEvaluation') {
                            if ($RedirectId) {
                                $row = $objDB->GetSingleRowValuesFromTable('cievaluationmaster', 'ciEvaluationMasterId', $RedirectId);
                                if ($row != '') {

                                    $redirectURL = $dynamicLoginURL . '/cievaluation.html?cievaluationrotationid=' . EncodeQueryData($RotationId);

                                    if ($RedirectId)
                                        $redirectURL = $redirectURL . '&ciEvaluationMasterId=' . EncodeQueryData($RedirectId);
                                } else {
                                    $errorMsg = 'CI Evaluation not found';
                                }
                            } else {
                                $redirectURL = $dynamicLoginURL . '/cievaluation.html?cievaluationrotationid=' . EncodeQueryData($RotationId);
                            }
                        } else if ($RedirectType == 'DCEciEvaluation') {
                            if ($RedirectId) {
                                // echo $RedirectId;exit;
                                $row = $objDB->GetSingleRowValuesFromTable('admincievaluationmaster', 'ciEvaluationMasterId', $RedirectId);
                                if ($row != '') {

                                    $clinicianSignatureDate = $row['clinicianSignatureDate'];
                                    $view = '';
                                    if ($clinicianSignatureDate != '' && $clinicianSignatureDate != '11/30/-0001' && $clinicianSignatureDate != '0000-00-00' && $clinicianSignatureDate != '01/01/1970' && $clinicianSignatureDate != '12/31/1969') {
                                        $view = 'V';
                                    }


                                    $redirectURL = $dynamicLoginURL . '/admincievaluation.html?cievaluationrotationid=' . EncodeQueryData($RotationId);

                                    if ($RedirectId)
                                        $redirectURL = $redirectURL . '&ciEvaluationMasterId=' . EncodeQueryData($RedirectId);

                                    if ($view != '')
                                        $redirectURL = $redirectURL . '&view=' . $view;
                                } else {
                                    $errorMsg = 'DCE CI Evaluation not found';
                                }
                            } else {
                                $redirectURL = $dynamicLoginURL . '/admincievaluation.html?cievaluationrotationid=' . EncodeQueryData($RotationId);
                            }
                        } else if ($RedirectType == 'pefEvaluation') {


                            if ($RedirectId) {
                                // echo $RedirectId;exit;
                                $row = $objDB->GetSingleRowValuesFromTable('studentpefmaster', 'studentPEFMasterId', $RedirectId);
                                if ($row != '') {

                                    $studentId = $row['studentId'];
                                    $isPEFType = $row['isType'];
                                    // exit;

                                } else {
                                    $errorMsg = 'PEF Evaluation not found';
                                }

                                $redirectURL = $dynamicLoginURL . '/addPEFEvaluation.html?studentPEFMasterId=' . EncodeQueryData($RedirectId);


                                if ($RotationId)
                                    $redirectURL = $redirectURL . '&rotationId=' . EncodeQueryData($RotationId);
                                if ($studentId)
                                    $redirectURL = $redirectURL . '&studentId=' . EncodeQueryData($studentId);
                                // if ($isPEFType)
                                $redirectURL = $redirectURL . '&isPEFType=' . EncodeQueryData($isPEFType);


                                // if ($view != '')
                                //     $redirectURL = $redirectURL . '&view=' . $view;

                            } else if ($isAdd && $RotationId) {
                                // $isPEFType = ($_GET['isPEFType']) ? ($_GET['isPEFType']) : 0;
                                if (isset($_GET['isPEFType'])) {
                                    $isPEFType = DecodeQueryData($_GET['isPEFType']);
                                    $isPEFType = ($isPEFType == 1) ? EncodeQueryData(0) : EncodeQueryData(1);
                                }
                                $redirectURL = $dynamicLoginURL . '/addPEFEvaluation.html?rotationId=' . EncodeQueryData($RotationId) . '&isPEFType=' . $isPEFType;
                            } else {
                                $redirectURL = $dynamicLoginURL . '/addPEFEvaluation.html?rotationId=' . EncodeQueryData($RotationId);
                            }
                        } else if ($RedirectType == 'floorAndIcu') {

                            // $isEvalType = ($_GET['isEvalType']) ? ($_GET['isEvalType']) : 0;

                            if ($RedirectId) {
                                // echo $RedirectId;exit;
                                $row = $objDB->GetSingleRowValuesFromTable('studentfloortherapyandicuevalmaster', 'studentMasterId', $RedirectId);
                                if ($row != '') {

                                    $studentId = $row['studentId'];
                                    $isEvalType = $row['isType'];

                                    $dateOfStudentSignature = $row['dateOfStudentSignature'];
                                    $view = '';
                                    if ($dateOfStudentSignature != '' && $dateOfStudentSignature != '11/30/-0001' && $dateOfStudentSignature != '0000-00-00 00:00:00' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '12/31/1969') {
                                        $view = 'V';
                                    }


                                    $redirectURL = $dynamicLoginURL . '/addFloorTherapyAndICUEvaluation.html?studentMasterId=' . EncodeQueryData($RedirectId);


                                    if ($RotationId)
                                        $redirectURL = $redirectURL . '&rotationId=' . EncodeQueryData($RotationId);
                                    if ($studentId)
                                        $redirectURL = $redirectURL . '&studentId=' . EncodeQueryData($studentId);
                                    // if ($isEvalType)
                                    $redirectURL = $redirectURL . '&isEvalType=' . EncodeQueryData($isEvalType);
                                    if ($view != '')
                                        $redirectURL = $redirectURL . '&view=' . $view;

                                    // if ($view != '')
                                    //     $redirectURL = $redirectURL . '&view=' . $view;
                                } else {
                                    $errorMsg = 'Floor And ICU Evaluation not found';
                                }
                            } else if ($isAdd && $RotationId) {
                                if (isset($_GET['isEvalType'])) {
                                    $isEvalType = DecodeQueryData($_GET['isEvalType']);
                                    $isEvalType = ($isEvalType == 1) ? EncodeQueryData(0) : EncodeQueryData(1);
                                }
                                $redirectURL = $dynamicLoginURL . '/addFloorTherapyAndICUEvaluation.html?rotationId=' . EncodeQueryData($RotationId) . '&isEvalType=' . $isEvalType;
                            } else {
                                $redirectURL = $dynamicLoginURL . '/addFloorTherapyAndICUEvaluation.html?rotationId=' . EncodeQueryData($RotationId);
                            }
                        } else if ($RedirectType == 'IRR') {

                            if ($RedirectId) {

                                $schoolTopicId = (($_GET['schoolTopicId'])) ? $_GET['schoolTopicId'] : 0;
                                $irrDetailId = (($_GET['irrDetailId'])) ? $_GET['irrDetailId'] : 0;
                                $currentDate = date("m/d/Y");

                                // echo $irrDetailId;exit;
                                // echo $RedirectId;exit;
                                $row = $objDB->GetSingleRowValuesFromTable('irrmaster', 'irrMasterId', $RedirectId);

                                $irrdetailsrow = $objDB->GetSingleRowValuesFromTable('irrdetails', 'irrMasterId', $RedirectId);
                                if ($row != '' && $irrdetailsrow != '') {


                                    $clinicianCompletionDate = $irrdetailsrow['clinicianCompletionDate'];
                                    $irrEndDate = stripslashes($row['irrEndDate']);
                                    $view = '';
                                    if (strtotime($currentDate) >= strtotime($irrEndDate)) {
                                        $view = 'V';
                                    } else if ($clinicianCompletionDate != '11/30/-0001 01:00 AM' && $clinicianCompletionDate != '' && $clinicianCompletionDate != '0000-00-00 00:00:00' && $clinicianCompletionDate != '01/01/1970' && $clinicianCompletionDate != '0000-00-00' && $clinicianCompletionDate != '11/30/-0001 12:00 AM') {
                                        $view = 'V';
                                    }

                                    $redirectURL = $dynamicLoginURL . '/addirr.html?irrMasterId=' . EncodeQueryData($RedirectId) . '&clinicianId=' . EncodeQueryData($UserId) . '&schoolTopicId=' . $schoolTopicId . '&irrDetailId=' . $irrDetailId;



                                    if ($view != '')
                                        $redirectURL = $redirectURL . '&view=' . $view;
                                    // echo $redirectURL;exit;
                                    // if ($view != '')
                                    //     $redirectURL = $redirectURL . '&view=' . $view;
                                } else {
                                    $errorMsg = 'IRR not found';
                                }
                            } else {
                                $redirectURL = $dynamicLoginURL . '/addirr.html?irrMasterId=' . EncodeQueryData($RedirectId);
                            }
                        } else if ($RedirectType == 'incident') {

                            if ($RedirectId) {

                                $row = $objDB->GetSingleRowValuesFromTable('studentsiteincidentmaster', 'studentSiteIncidentMasterId', $RedirectId);
                                if ($row != '') {

                                    $redirectURL = $dynamicLoginURL . '/addincident.html?rotationId=' . EncodeQueryData($RotationId);

                                    if ($RedirectId)
                                        $redirectURL = $redirectURL . '&incidentId=' . EncodeQueryData($RedirectId);
                                } else {
                                    $errorMsg = 'Incident not found';
                                }
                            } else {
                                $redirectURL = $dynamicLoginURL . '/addincident.html?rotationId=' . EncodeQueryData($RotationId);
                            }
                        } else if ($RedirectType == 'equipment') {

                            if ($RedirectId) {

                                $row = $objDB->GetSingleRowValuesFromTable('studentequipmentmaster', 'studentEquipmentMasterId', $RedirectId);
                                if ($row != '') {

                                    $redirectURL = $dynamicLoginURL . '/equipment.html?equipmentrotationid=' . EncodeQueryData($RotationId);

                                    if ($RedirectId)
                                        $redirectURL = $redirectURL . '&studentEquipmentMasterId=' . EncodeQueryData($RedirectId);
                                } else {
                                    $errorMsg = 'Equipment not found';
                                }
                            } else {
                                $redirectURL = $dynamicLoginURL . '/equipment.html?equipmentrotationid=' . EncodeQueryData($RotationId);
                            }
                        } else if ($RedirectType == 'formative') {
                            if ($RedirectId) {
                                $row = $objDB->GetSingleRowValuesFromTable('studentformativemaster', 'studentFormativeMasterId', $RedirectId);
                                if ($row != '') {

                                    $dateOfStudentSignature = stripslashes($row['dateOfStudentSignature']);
                                    $dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $timezone);
                                    $dateOfStudentSignature = date("m/d/Y", strtotime($dateOfStudentSignature));
                                    $rotationId = $row['rotationId'];

                                    if ($dateOfStudentSignature != '11/30/-0001' && $dateOfStudentSignature != '0000-00-00' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '12/31/1969') {

                                        $actionName = 'View';
                                    } else {
                                        $actionName = 'Edit';
                                    }

                                    if ($actionName == 'View') {
                                        $view = 'V';
                                    }

                                    $redirectURL = $dynamicLoginURL . '/formative.html?studentFormativeMasterId=' . EncodeQueryData($RedirectId);

                                    if ($RotationId) {
                                        $redirectURL = $redirectURL . '&formativerotationid=' . EncodeQueryData($RotationId);
                                    }

                                    if ($view != '')
                                        $redirectURL = $redirectURL . '&view=' . $view;
                                } else {
                                    $errorMsg = 'formative not found';
                                }
                            } else {

                                $redirectURL = $dynamicLoginURL . '/formative.html?formativerotationid=' . EncodeQueryData($RotationId);
                            }
                        } else if ($RedirectType == 'midterm') {
                            if ($RedirectId) {
                                $row = $objDB->GetSingleRowValuesFromTable('studentmidtermmaster', 'studentMidtermMasterId', $RedirectId);
                                if ($row != '') {

                                    $dateOfStudentSignature = stripslashes($row['dateOfStudentSignature']);
                                    $dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $timezone);
                                    $dateOfStudentSignature = date("m/d/Y", strtotime($dateOfStudentSignature));
                                    $rotationId = $row['rotationId'];

                                    if ($dateOfStudentSignature != '11/30/-0001' && $dateOfStudentSignature != '0000-00-00' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '12/31/1969') {

                                        $actionName = 'View';
                                    } else {
                                        $actionName = 'Edit';
                                    }

                                    if ($actionName == 'View') {
                                        $view = 'V';
                                    }

                                    $redirectURL = $dynamicLoginURL . '/midterm.html?studentMidtermMasterId=' . EncodeQueryData($RedirectId);

                                    if ($RotationId) {
                                        $redirectURL = $redirectURL . '&midtermrotationid=' . EncodeQueryData($RotationId);
                                    }

                                    if ($view != '')
                                        $redirectURL = $redirectURL . '&view=' . $view;
                                } else {
                                    $errorMsg = 'midterm not found';
                                }
                            } else {

                                $redirectURL = $dynamicLoginURL . '/midterm.html?midtermrotationid=' . EncodeQueryData($RotationId);
                            }
                        } else if ($RedirectType == 'summative') {
                            if ($RedirectId) {
                                // echo ' hi';
                                $row = $objDB->GetSingleRowValuesFromTable('studentsummativemaster', 'studentSummativeMasterId', $RedirectId);
                                if ($row != '') {

                                    $dateOfStudentSignature = stripslashes($row['dateOfStudentSignature']);
                                    $dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $timezone);
                                    $dateOfStudentSignature = date("m/d/Y", strtotime($dateOfStudentSignature));
                                    $rotationId = $row['rotationId'];

                                    if ($dateOfStudentSignature != '11/30/-0001' && $dateOfStudentSignature != '0000-00-00' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '12/31/1969') {

                                        $actionName = 'View';
                                    } else {
                                        $actionName = 'Edit';
                                    }

                                    if ($actionName == 'View') {
                                        $view = 'V';
                                    }

                                    $redirectURL = $dynamicLoginURL . '/summative.html?studentSummativeMasterId=' . EncodeQueryData($RedirectId);

                                    if ($RotationId) {
                                        $redirectURL = $redirectURL . '&summativerotationid=' . EncodeQueryData($RotationId);
                                    }

                                    if ($view != '')
                                        $redirectURL = $redirectURL . '&view=' . $view;
                                } else {
                                    $errorMsg = 'summative not found';
                                }
                            } else {

                                $redirectURL = $dynamicLoginURL . '/summative.html?summativerotationid=' . EncodeQueryData($RotationId);
                            }
                        } else if ($RedirectType == 'volunteer') {

                            // if ($RedirectId) {

                            $row = $objDB->GetSingleRowValuesFromTable('volunteereval', 'volunteerEvalId', $RedirectId);
                            if ($row != '') {

                                $dateOfClinicianSignature = stripslashes($row['clinicianDate']);

                                if ($dateOfClinicianSignature != '' && $dateOfClinicianSignature != '01/01/1970' && $dateOfClinicianSignature != '12/31/1969') {
                                    $dateOfClinicianSignature = $dateOfClinicianSignature;
                                    $actionName = 'View';
                                } else {
                                    $dateOfClinicianSignature = '-';
                                    $actionName = 'Edit';
                                }


                                $rotationStatus = checkRotationStatus($RotationId, $isMobile = 1);
                                $view = ($rotationStatus) ? 1 : $view;

                                $redirectURL = $dynamicLoginURL . '/addVolunteerEval.html?volunteerEvalId=' . EncodeQueryData($RedirectId);
                            } else {
                                $errorMsg = 'volunteer evaluation not found';
                            }
                            // } else {
                            //     $redirectURL = $dynamicLoginURL . '/addVolunteerEval.html?rotationId=' . EncodeQueryData($RotationId);
                            // }
                        } else if ($RedirectType == 'mastery') {
                            if ($isActiveCheckoff == 0) {

                                if ($RedirectId) {
                                    $row = $objDB->GetSingleRowValuesFromTable('studentmasteryeval', 'studentMasteryEvalId', $RedirectId);
                                    if ($row != '') {
                                        $dateOfStudentSignature = stripslashes($row['dateOfStudentSignature']);
                                        $dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $timezone);
                                        $dateOfStudentSignature = date("m/d/Y", strtotime($dateOfStudentSignature));
                                        $studentcomments = $row['studentcomments'];
                                        $studentsignature = $row['studentsignature'];



                                        if ($studentcomments != '' && $studentsignature != '' && $dateOfStudentSignature != '11/30/-0001' && $dateOfStudentSignature != '0000-00-00 00:00:00' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '12/31/1969') {
                                            $dateOfStudentSignature = $dateOfStudentSignature;
                                            $actionName = 'View';
                                        } else {
                                            $dateOfStudentSignature = '-';
                                            $actionName = 'Edit';
                                        }


                                        if ($actionName == 'View') {
                                            $view = 'V';
                                        }


                                        $rotationStatus = checkRotationStatus($RotationId, $isMobile = 1);
                                        $view = ($rotationStatus) ? 'V' : $view;
                                        $redirectURL = $dynamicLoginURL . '/addMastery.html?studentMasteryEvalId=' . EncodeQueryData($RedirectId);

                                        if ($RotationId)
                                            $redirectURL = $redirectURL . '&rotationId=' . EncodeQueryData($RotationId);

                                        // if ($preceptorId)
                                        //     $redirectURL = $redirectURL . '&isPreceptor=' . EncodeQueryData($preceptorId);

                                        if ($view != '')
                                            $redirectURL = $redirectURL . '&view=' . $view;
                                    } else {
                                        $errorMsg = 'Mastery Evaluation not found';
                                    }
                                } else {
                                    $redirectURL = $dynamicLoginURL . '/addMastery.html?rotationId=' . EncodeQueryData($RotationId);
                                }
                            } else {
                                $errorMsg = '';
                                header('location:' . BASE_PATH . '/webRedirect.html?status=Error&type=' . $RedirectType . '&ErrorMsg=' . $errorMsg);
                                exit();
                            }
                        }
                    } else {
                        $errorMsg = 'Invalid Redirect Type';
                    }
                } else {
                    $errorMsg = 'Invalid redirect Id';
                }
            } else {
                $errorMsg = 'Invalid User';
            }
        } else {
            $errorMsg = 'Invalid Access Token';
        }
    } else {
        $errorMsg = 'Invalid User Id';
    }
} else {
    $errorMsg = 'Invalid User Type';
}
if ($errorMsg == '') {
    if ($IsMobile)
        $redirectURL .= '&IsMobile=1';

    // echo '</br>redirectURL' . $redirectURL;
    // exit;
    header('location:' . $redirectURL);
    exit();
} else {
    echo $errorMsg;
    exit;
    header('location:' . BASE_PATH . '/webRedirect.html?status=Error&type=' . $RedirectType . '&ErrorMsg=' . $errorMsg);
    exit();
}
// echo '</br>redirectURL' . $redirectURL;
// print_r($_SESSION);