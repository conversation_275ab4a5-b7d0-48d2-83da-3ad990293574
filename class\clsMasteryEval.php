<?php
class clsMasteryEval
{
	function SaveAdminDailyEval($studentMasteryEvalId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($studentMasteryEvalId > 0) {

			$sql = "UPDATE studentmasteryeval SET 						
						 clinicianId = '" . addslashes($this->clinicianId) . "',
						 studentId = '" . addslashes($this->studentId) . "',
						 rotationId = '" . addslashes($this->rotationId) . "',		
						 evaluationDate = '" . addslashes($this->evaluationDate) . "',		
						 firstSectionAvg='" . addslashes($this->firstSectionAvg) . "',
						 secondSectionAvg='" . addslashes($this->secondSectionAvg) . "',
						 thirdSectionAvg='" . addslashes($this->thirdSectionAvg) . "',
						 fourthSectionAvg='" . addslashes($this->fourthSectionAvg) . "',
						 totalAvg='" . addslashes($this->totalAvg) . "',
						 studentcomments = '" . addslashes($this->studentcomments) . "',
						 updatedBy = '" . addslashes($this->updatedBy) . "',
						 updatedDate = '" . (date("Y-m-d h:i:s")) . "' 
						 Where studentMasteryEvalId= " . $studentMasteryEvalId;
			//echo 'update->'.$sql;exit;
			$objDB->ExecuteQuery($sql);
		} else {
			$sql = "INSERT INTO studentmasteryeval (schoolId,clinicianId, studentId, rotationId,
											evaluationDate,firstSectionAvg,secondSectionAvg,thirdSectionAvg,fourthSectionAvg,totalAvg,studentcomments,createdBy, createdDate) 
				VALUES ('" . addslashes($this->schoolId) . "',
						'" . addslashes($this->clinicianId) . "',
						'" . addslashes($this->studentId) . "',
						'" . addslashes($this->rotationId) . "',
						'" . addslashes($this->evaluationDate) . "',
						'" . addslashes($this->firstSectionAvg) . "',
						'" . addslashes($this->secondSectionAvg) . "',
						'" . addslashes($this->thirdSectionAvg) . "',
						'" . addslashes($this->fourthSectionAvg) . "',
						'" . addslashes($this->totalAvg) . "',
						'" . addslashes($this->studentcomments) . "',
						'" . addslashes($this->createdBy) . "',
						'" . (date("Y-m-d h:i:s")) . "'						
						)";
			//echo '<hr>Insert->'.$sql;exit;
			$studentMasteryEvalId = $objDB->ExecuteInsertQuery($sql);
		}
		unset($objDB);
		return $studentMasteryEvalId;
	}

	function SaveClinicianMasteryEval($studentMasteryEvalId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($studentMasteryEvalId > 0) {

			$sql = "UPDATE studentmasteryeval SET 						
						 clinicianId = '" . addslashes($this->clinicianId) . "',
						 rotationId = '" . addslashes($this->rotationId) . "',
						 studentId = '" . addslashes($this->studentId) . "',
						 evaluationDate = '" . addslashes($this->evaluationDate) . "',
						 dateOfInstructorSignature='" . addslashes($this->dateOfInstructorSignature) . "',
						 studentsignature='" . addslashes($this->studentsignature) . "',
						 dateOfStudentSignature='" . addslashes($this->dateOfStudentSignature) . "',
						 firstSectionAvg='" . addslashes($this->firstSectionAvg) . "',
						 secondSectionAvg='" . addslashes($this->secondSectionAvg) . "',
						 thirdSectionAvg='" . addslashes($this->thirdSectionAvg) . "',
						 fourthSectionAvg='" . addslashes($this->fourthSectionAvg) . "',
						 totalAvg='" . addslashes($this->totalAvg) . "',	
						 studentcomments = '" . addslashes($this->studentcomments) . "',					
						 updatedBy = '" . addslashes($this->createdBy) . "',
						 updatedDate = '" . (date("Y-m-d h:i:s")) . "' 
						 Where studentMasteryEvalId= " . $studentMasteryEvalId;
			//echo 'update->'.$sql;exit;
			$objDB->ExecuteQuery($sql);
		} else {
			$sql = "INSERT INTO studentmasteryeval (schoolId,clinicianId, studentId, rotationId,evaluationDate,dateOfInstructorSignature,studentsignature,dateOfStudentSignature,firstSectionAvg,secondSectionAvg,thirdSectionAvg,fourthSectionAvg,totalAvg,studentcomments,createdBy, createdDate) 
				VALUES ('" . addslashes($this->schoolId) . "',
						'" . addslashes($this->clinicianId) . "',
						'" . addslashes($this->studentId) . "',
						'" . addslashes($this->rotationId) . "',
						'" . addslashes($this->evaluationDate) . "',
						'" . addslashes($this->dateOfInstructorSignature) . "',	
						'" . addslashes($this->studentsignature) . "',	
						'" . addslashes($this->dateOfStudentSignature) . "',	
						'" . addslashes($this->firstSectionAvg) . "',
						'" . addslashes($this->secondSectionAvg) . "',
						'" . addslashes($this->thirdSectionAvg) . "',
						'" . addslashes($this->fourthSectionAvg) . "',
						'" . addslashes($this->totalAvg) . "',		
						'" . addslashes($this->studentcomments) . "',			
						'" . addslashes($this->createdBy) . "',
						'" . (date("Y-m-d h:i:s")) . "'						
						)";
			//echo '<hr>Insert->'.$sql;exit;
			$studentMasteryEvalId = $objDB->ExecuteInsertQuery($sql);
		}
		unset($objDB);
		return $studentMasteryEvalId;
	}

	function SaveStudentMastery($studentMasteryEvalId)
	{
		$objDB = new clsDB();
		$sql = '';

		$sql = "UPDATE studentmasteryeval SET						 
						 dateOfStudentSignature	='" . addslashes($this->dateOfStudentSignature) . "',
						 firstSectionAvg='" . addslashes($this->firstSectionAvg) . "',
						 secondSectionAvg='" . addslashes($this->secondSectionAvg) . "',
						 thirdSectionAvg='" . addslashes($this->thirdSectionAvg) . "',
						 fourthSectionAvg='" . addslashes($this->fourthSectionAvg) . "',
						 studentsignature='" . addslashes($this->studentsignature) . "',
						 totalAvg='" . addslashes($this->totalAvg) . "',						
						 studentcomments = '" . addslashes($this->studentcomments) . "'
						 Where studentMasteryEvalId= " . $studentMasteryEvalId;
		//echo 'update->'.$sql;exit;
		$objDB->ExecuteQuery($sql);


		unset($objDB);
		return $studentMasteryEvalId;
	}

	function DeleteStudentDailyDetails($studentMasteryEvalId)
	{
		$objDB = new clsDB();
		$sql = "DELETE  FROM studentmasteryevaldetails WHERE studentMasteryEvalId=" . $studentMasteryEvalId;
		//ECHO 'DELETE->'.$sql;EXIT;
		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}
	function SaveMasteryDetails($studentMasteryEvalId)
	{
		$objDB = new clsDB();

		$sql = "INSERT INTO studentmasteryevaldetails (studentMasteryEvalId,studentQuestionId,
								studentoptionvalue,studentOptionAnswerText) 
					 VALUES ('" . ($this->studentMasteryEvalId) . "',
							 '" . ($this->studentQuestionId) . "',
							 '" . ($this->studentoptionvalue) . "',
							 '" . addslashes($this->studentOptionAnswerText) . "'
							 
							)";
		//echo 'INSERT->'.$sql;exit;
		$studentMasteryEvalId = $objDB->ExecuteInsertQuery($sql);
		unset($objDB);
		return $studentMasteryEvalId;
	}

	function SaveStudentMasterySignoffForApp($studentMasteryEvalId, $studentsignitureDate, $studentId, $StudentComment)
	{

		$objDB = new clsDB();
		$sql = '';
		if ($studentMasteryEvalId > 0) {

			$sql = "UPDATE studentmasteryeval SET 						
					
					dateOfStudentSignature='" . $studentsignitureDate . "',
					studentComments = '" . $StudentComment . "',
					updatedBy = '" . $studentId . "',
					updatedDate = '" . (date("Y-m-d h:i:s")) . "' 
					Where studentMasteryEvalId= " . $studentMasteryEvalId;
			// echo 'update->'.$sql;exit;
			$objDB->ExecuteQuery($sql);
		}
		unset($objDB);
		return $studentMasteryEvalId;
	}

	function DeleteMasteryEval($studentMasteryEvalId)
	{
		$objDB = new clsDB();
		$result = "";
		if ($studentMasteryEvalId > 0) {
			$objDB = new clsDB();
			$sql = "DELETE studentmasteryeval,studentmasteryevaldetails FROM studentmasteryeval
									LEFT JOIN studentmasteryevaldetails ON studentmasteryeval.studentMasteryEvalId=
									studentmasteryevaldetails.studentMasteryEvalId
									WHERE studentmasteryeval.studentMasteryEvalId = " . $studentMasteryEvalId;
			// ECHO $sql;exit;
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
		return $result;
	}



	function GetAllMasteryEval($rotationId = 0, $clinicianId = 0, $studentId = 0, $courseId)
	{
		$objDB = new clsDB();
		$sql = "SELECT studentmasteryeval.*,
						studentmasteryeval.studentMasteryEvalId AS MasteryEvalID,studentmasteryevaldetails.*,
						student.studentId,
						rotation.rotationId,rotation.title,student.firstName,student.lastName,student.rankId,
						rankmaster.rankId,rankmaster.title AS Ranktitle,rotation.parentRotationId,rotation.locationId as rotationLocationId,courses.locationId
						FROM studentmasteryeval
						LEFT JOIN studentmasteryevaldetails ON studentmasteryeval.studentMasteryEvalId=
						studentmasteryevaldetails.studentMasteryEvalId
						LEFT JOIN rotation ON studentmasteryeval.rotationId=rotation.rotationId
						LEFT JOIN student ON studentmasteryeval.studentId=student.studentId
						LEFT JOIN courses ON rotation.courseId=courses.courseId
						LEFT JOIN rankmaster ON student.rankId=rankmaster.rankId";
		$sql .= " WHERE  studentmasteryeval.studentMasteryEvalId !=0";
		if ($rotationId > 0) {
			$sql .= "  AND studentmasteryeval.rotationId=" . $rotationId;
		}
		if ($studentId > 0) {
			$sql .= "  AND studentmasteryeval.studentId=" . $studentId;
		}
		if ($clinicianId > 0) {
			$sql .= "  AND studentmasteryeval.clinicianId=" . $clinicianId;
		}
		if ($courseId > 0) {
			$sql .= "  AND rotation.courseId=" . $courseId;
		}
		$sql .= " GROUP BY studentmasteryeval.studentMasteryEvalId";
		$sql .= " ORDER BY `studentmasteryeval`.`evaluationDate` DESC";

		// echo $sql;
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}


	function GetAllDailyweeklyEvalForAllClinician($rotationId = 0, $schoolId, $courseId)
	{
		$objDB = new clsDB();
		$sql = "SELECT studentmasteryeval.*,
						studentmasteryeval.studentMasteryEvalId AS MasteryEvalID,studentmasteryevaldetails.*,
						student.studentId,
						rotation.rotationId,rotation.title,student.firstName,student.lastName,student.rankId,
						rankmaster.rankId,rankmaster.title AS Ranktitle,rotation.parentRotationId,rotation.locationId as rotationLocationId,courses.locationId
						FROM studentmasteryeval
						LEFT JOIN studentmasteryevaldetails ON studentmasteryeval.studentMasteryEvalId=
						studentmasteryevaldetails.studentMasteryEvalId
						LEFT JOIN rotation ON studentmasteryeval.rotationId=rotation.rotationId
						LEFT JOIN student ON studentmasteryeval.studentId=student.studentId
						LEFT JOIN courses ON rotation.courseId=courses.courseId
						LEFT JOIN rankmaster ON student.rankId=rankmaster.rankId";
		$sql .= " WHERE  studentmasteryeval.studentMasteryEvalId !=0";
		if ($rotationId > 0) {
			$sql .= "  AND studentmasteryeval.rotationId=" . $rotationId;
		}
		if ($schoolId > 0) {
			$sql .= "  AND studentmasteryeval.schoolId=" . $schoolId;
		}
		if ($courseId > 0) {
			$sql .= "  AND rotation.courseId=" . $courseId;
		}

		$sql .= " GROUP BY studentmasteryeval.studentMasteryEvalId";
		$sql .= " ORDER BY `studentmasteryeval`.`evaluationDate` DESC";

		//ECHO $sql;
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}

	function GetAllDailyweeklyEvalForAdmin($rotationId, $schoolId, $courseId, $studentId)
	{
		$objDB = new clsDB();
		$sql = "SELECT studentmasteryeval.*,
						studentmasteryeval.studentMasteryEvalId AS MasteryEvalID,studentmasteryevaldetails.*,
						student.studentId,
						rotation.rotationId,rotation.title,student.firstName,student.lastName,student.rankId,
						rankmaster.rankId,rankmaster.title AS Ranktitle
						FROM studentmasteryeval
						LEFT JOIN studentmasteryevaldetails ON studentmasteryeval.studentMasteryEvalId=
						studentmasteryevaldetails.studentMasteryEvalId
						LEFT JOIN rotation ON studentmasteryeval.rotationId=rotation.rotationId
						LEFT JOIN student ON studentmasteryeval.studentId=student.studentId
						LEFT JOIN rankmaster ON student.rankId=rankmaster.rankId";
		$sql .= " WHERE  studentmasteryeval.studentMasteryEvalId !=0";
		if ($schoolId > 0) {
			$sql .= "  AND studentmasteryeval.schoolId =" . $schoolId;
		}
		if ($rotationId > 0) {
			$sql .= "  AND studentmasteryeval.rotationId=" . $rotationId;
		}
		if ($courseId > 0) {
			$sql .= "  AND rotation.courseId=" . $courseId;
		}
		if ($studentId > 0) {
			$sql .= "  AND studentmasteryeval.studentId=" . $studentId;
		}
		$sql .= " GROUP BY studentmasteryeval.studentMasteryEvalId";
		$sql .= " ORDER BY `studentmasteryeval`.`evaluationDate` DESC";

		//ECHO $sql;
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}
	function GetStudentMasteryDetails($studentMasteryEvalId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT studentmasteryeval.*,studentmasteryevaldetails.*,rotation.parentRotationId,rotation.locationId as rotationLocationId,courses.locationId,
						rotation.title as rotationName, clinician.firstName, clinician.lastName AS clinicianName
							FROM  studentmasteryeval 
							LEFT JOIN studentmasteryevaldetails ON studentmasteryevaldetails.studentMasteryEvalId= 
							studentmasteryeval.`studentMasteryEvalId`
							 LEFT JOIN rotation ON studentmasteryeval.rotationId=rotation.rotationId
							 LEFT JOIN courses ON rotation.courseId=courses.courseId
							 LEFT JOIN clinician on studentmasteryeval.clinicianId = clinician.clinicianId
							WHERE studentmasteryeval.studentMasteryEvalId=" . $studentMasteryEvalId;
		//echo $sql;			 
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}

	function GetAllMasteryQuestionMaster($currentSchoolId, $sectionMasterId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM  schoolmasterysectionmaster
						WHERE schoolId=" . $currentSchoolId . " AND masterySectionId=" . $sectionMasterId .
			" ORDER BY sortOrder ASC";
		// echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetSections($currentSchoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM  schoolmasterysection
						WHERE schoolId=" . $currentSchoolId." ORDER BY sortOrder ASC";
		//echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetAllDefaultSections()
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM  defaultmasteryevalsectionmaster";
		//echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}
	function CopyAllMasteryQuestionMaster($currentSchoolId)
	{
		$currentschoolSectionId = '';
		$this->schoolId = $currentSchoolId;
		$objDB = new clsDB();
		$savedQuestionIds = array();
		$savedSectionIds = array();

		$sql = "select defaultmasteryevalsectionmaster.* from defaultmasteryevalquestionmaster 
								INNER JOIN defaultmasteryevalsectionmaster ON  defaultmasteryevalsectionmaster.masterySectionId  
																				= defaultmasteryevalquestionmaster.masterySectionId 
								GROUP BY defaultmasteryevalsectionmaster.masterySectionId ";

		$rowsSectionMaster = $objDB->GetResultset($sql);
		$count = mysqli_num_rows($rowsSectionMaster);

		if ($count > 0) {
			while ($sectionMaster = mysqli_fetch_array($rowsSectionMaster)) {
				$currentschoolQuestionId = array();
				//default assignment
				$currentschoolQuestionId[] = 0;
				$sectionId = $sectionMaster['masterySectionId'];

				//Skipp
				if (array_key_exists($sectionId, $savedSectionIds)) {
					$currentschoolSectionId = $savedSectionIds[$sectionId];
				} else {
					$this->title = $sectionMaster['title'];
					$this->sortOrder = $sectionMaster['sortOrder'];
					$this->schoolId = $currentSchoolId;
					$currentschoolSectionId = $this->SaveSchoolSectionMaster($sectionId);
					$savedSectionIds[$sectionId] = $currentschoolSectionId;
				}

				$sql = "select defaultmasteryevalquestionmaster.* from 
								defaultmasteryevalquestionmaster 
										left JOIN defaultmasteryevalquestiondetails ON  defaultmasteryevalquestionmaster.masteryQuestionId
																			=defaultmasteryevalquestiondetails.masteryQuestionId
										WHERE masterySectionId =" . $sectionId;
				// what is optionvalue
				$rowsQuestionMaster = $objDB->GetResultset($sql);
				if ($rowsQuestionMaster != "") {
					while ($row = mysqli_fetch_array($rowsQuestionMaster)) {
						$masteryQuestionId = $row['masteryQuestionId'];
						// If already used then skipp
						if (array_key_exists($masteryQuestionId, $savedQuestionIds)) {
							$currentschoolQuestionId[] = $savedQuestionIds[$masteryQuestionId];
							continue;
						} else {
							$this->optionText = $row['optionText'];
							$this->masteryQuestionType = $row['masteryQuestionType'];
							$this->masteryQuestionId = $masteryQuestionId;
							$this->masterySectionId = $currentschoolSectionId;
							$this->sortOrder = $row['sortOrder'];
							$schoolQuestionId = $this->CopyMasterQuestionToSchool($currentSchoolId, $masteryQuestionId, $currentschoolSectionId);
							//Bind in array
							$savedQuestionIds[$masteryQuestionId] = $schoolQuestionId;
							$currentschoolQuestionId[] = $schoolQuestionId;
							//-----------------------------------------------------
							//Copy Question Choices
							//-----------------------------------------------------
							$this->CopyMasterQuestionChoicesToSchool($masteryQuestionId, $schoolQuestionId);
							//-----------------------------------------------------
						}
					} //while end


				} //if end
			} //1st while end
		} //1st if end 


	}

	function SaveSchoolSectionMaster($schoolSectionId)
	{
		$objDB = new clsDB();
		$sql = "INSERT INTO schoolmasterysection (title,schoolId,sortOrder) 
					 VALUES (								
								'" . addslashes($this->title) . "',
								" . addslashes($this->schoolId) . ",
								" . addslashes($this->sortOrder) . "							 
							)";
		//echo 'section->'. $sql;exit;
		$schoolSectionId = $objDB->ExecuteInsertQuery($sql);
		$this->schoolSectionId = $schoolSectionId;
		unset($objDB);
		return $schoolSectionId;
	}
	function CopyMasterQuestionToSchool($schoolId, $questionId, $currentschoolSectionId)
	{
		$objDB = new clsDB();
		$sql = "INSERT INTO schoolmasterysectionmaster (optionText,masteryQuestionType,masterySectionId,schoolId,sortOrder) 
                
                SELECT optionText,masteryQuestionType, " . $currentschoolSectionId . "," . $schoolId . ",sortOrder  FROM defaultmasteryevalquestionmaster
				WHERE masteryQuestionId=" . $questionId;
		//echo $sql;exit;
		$schoolQuestionId = $objDB->ExecuteInsertQuery($sql);
		unset($objDB);
		return $schoolQuestionId;
		;
	}
	// i don't undestand why option value is used
	function CopyMasterQuestionChoicesToSchool($questionMasterId, $schoolQuestionId)
	{
		$sql = "INSERT INTO schoolmasteryevaldetails (masteryQuestionId,optionText,description) 
                        SELECT " . $schoolQuestionId . ",optionText,description
                        FROM defaultmasteryevalquestiondetails  WHERE masteryQuestionId=" . $questionMasterId;
		//echo $sql;exit;
		$objDB = new clsDB();
		$schoolQuestionDId = $objDB->ExecuteInsertQuery($sql);
		unset($objDB);
	}

	function GetAllMasteryEvalForReport($schoolId, $rotationId, $studentId, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $ascdesc = '', $sordorder = '', $cbosemester = '', $subcborotation = '')
	{
		$cbosemester = str_replace(" ", ",", $cbosemester);
		$subcborotation = str_replace(" ", ",", $subcborotation);

		$studentIdCount = ($studentId != '') ? count($studentId) : 0;
		$studentIds = ($studentIdCount > 0) ? implode(',', $studentId) : '';

		$objDB = new clsDB();
		$sql = "SELECT studentmasteryeval.*,
						studentmasteryeval.studentMasteryEvalId AS DailyEvalID,studentmasteryevaldetails.*,
						student.studentId,
						rotation.rotationId,rotation.title as rotationname,student.firstName,student.lastName,student.rankId,
						rankmaster.rankId,rankmaster.title AS Ranktitle,clinician.firstName as clinicianFirstName, clinician.lastName as clinicianLastName
						FROM studentmasteryeval
						LEFT JOIN studentmasteryevaldetails ON studentmasteryeval.studentMasteryEvalId=
						studentmasteryevaldetails.studentMasteryEvalId
						LEFT JOIN rotation ON studentmasteryeval.rotationId=rotation.rotationId
						LEFT JOIN courses ON courses.`courseId` = rotation.`courseId`
						LEFT JOIN semestermaster ON courses.`semesterId` = semestermaster.`semesterId`
						LEFT JOIN student ON studentmasteryeval.studentId=student.studentId
						LEFT JOIN clinician ON studentmasteryeval.clinicianId=clinician.clinicianId
						
						LEFT JOIN rankmaster ON student.rankId=rankmaster.rankId";
		$sql .= " WHERE  studentmasteryeval.studentMasteryEvalId !=0 AND studentmasteryeval.schoolId =" . $schoolId;

		if ($cbosemester > 0)
			$sql .= " AND semestermaster.semesterId IN ($cbosemester)";
		if ($rotationId > 0)
			$sql .= " AND rotation.rotationId=" . $rotationId . " OR  rotation.parentRotationId = " . $rotationId;
		if ($subcborotation > 0)
			$sql .= " AND rotation.rotationId IN ($subcborotation)";

		if ($studentIdCount > 0)
			$sql .= " AND student.studentId IN ($studentIds)";
		if ($student_rank > 0) {
			$sql .= " AND student.rankId =" . $student_rank;
		}
		if ($startDate > 0 || $endDate > 0)
			$sql .= " AND studentmasteryeval.evaluationDate >= '" . $startDate . "' and studentmasteryeval.evaluationDate <= '" . $endDate . "'";

		$sql .= " GROUP BY studentmasteryeval.studentMasteryEvalId";

		if ($ascdesc && $sordorder == 1)
			$sql .= "  ORDER BY student.firstName " . $ascdesc;
		else if ($ascdesc && $sordorder == 2)
			$sql .= "  ORDER BY rankmaster.title " . $ascdesc;
		else if ($ascdesc && $sordorder == 5)
			$sql .= "  ORDER BY rotation.title " . $ascdesc;
		else if ($ascdesc && $sordorder == 10)
			$sql .= "  ORDER BY rotation.`title` " . $ascdesc;
		else if ($ascdesc && $sordorder == 14)
			$sql .= "  ORDER BY student.firstName " . $ascdesc;
		else if ($ascdesc && $sordorder == 15)
			$sql .= "  ORDER BY student.lastName " . $ascdesc;
		else
			$sql .= " ORDER BY `studentmasteryeval`.`evaluationDate`" . $ascdesc;

		// echo $sql;exit;
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}


	function GetAllMasteryEvalForExportReport($schoolId, $rotationId, $studentId, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $ascdesc = '', $sordorder = '', $cbosemester = '', $subcborotation = '')
	{
		$cbosemester = str_replace(" ", ",", $cbosemester);
		$subcborotation = str_replace(" ", ",", $subcborotation);

		if (count($studentId) > 0) {
			$studentIds = is_array($studentId) ? implode(",", $studentId) : '';
			// $studentIds =  implode(',', $studentId);
		}
		$objDB = new clsDB();
		$sql = "SELECT studentmasteryeval.*,
						studentmasteryeval.studentMasteryEvalId AS DailyEvalID,studentmasteryevaldetails.*,
						student.studentId,
						rotation.rotationId,rotation.title as rotationname,student.firstName,student.lastName,student.rankId,
						rankmaster.rankId,rankmaster.title AS Ranktitle,clinician.firstName as clinicianFirstName, clinician.lastName as clinicianLastName
						FROM studentmasteryeval
						LEFT JOIN studentmasteryevaldetails ON studentmasteryeval.studentMasteryEvalId=
						studentmasteryevaldetails.studentMasteryEvalId
						LEFT JOIN rotation ON studentmasteryeval.rotationId=rotation.rotationId
						LEFT JOIN courses ON courses.`courseId` = rotation.`courseId`
						LEFT JOIN semestermaster ON courses.`semesterId` = semestermaster.`semesterId`
						LEFT JOIN student ON studentmasteryeval.studentId=student.studentId
						LEFT JOIN clinician ON studentmasteryeval.clinicianId=clinician.clinicianId
						
						LEFT JOIN rankmaster ON student.rankId=rankmaster.rankId";
		$sql .= " WHERE  studentmasteryeval.studentMasteryEvalId !=0 AND studentmasteryeval.schoolId =" . $schoolId;

		if ($cbosemester > 0)
			$sql .= " AND semestermaster.semesterId IN ($cbosemester)";
		if ($rotationId > 0)
			$sql .= " AND rotation.rotationId=" . $rotationId . " OR  rotation.parentRotationId = " . $rotationId;
		if ($subcborotation > 0)
			$sql .= " AND rotation.rotationId IN ($subcborotation)";

		if ($studentIds > 0) {
			$sql .= " AND student.studentId IN ($studentIds)";
		}
		if ($student_rank > 0) {
			$sql .= " AND student.rankId =" . $student_rank;
		}
		if ($startDate > 0 || $endDate > 0)
			$sql .= " AND studentmasteryeval.evaluationDate >= '" . $startDate . "' and studentmasteryeval.evaluationDate <= '" . $endDate . "'";

		$sql .= " GROUP BY studentmasteryeval.studentMasteryEvalId";

		if ($ascdesc && $sordorder == 1)
			$sql .= "  ORDER BY student.firstName " . $ascdesc;
		else if ($ascdesc && $sordorder == 2)
			$sql .= "  ORDER BY rankmaster.title " . $ascdesc;
		else if ($ascdesc && $sordorder == 5)
			$sql .= "  ORDER BY rotation.title " . $ascdesc;
		else if ($ascdesc && $sordorder == 10)
			$sql .= "  ORDER BY rotation.`title` " . $ascdesc;
		else
			$sql .= " ORDER BY `studentmasteryeval`.`evaluationDate`" . $ascdesc;

		//echo $sql;exit;
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}

	function CopyDefaultQuestions()
	{
		$objDB = new clsDB();
		$sql = "select * from schooldailyevalmaster WHERE  dailyQuestionType !=5 ";

		$rowsQuestionMaster = $objDB->GetResultset($sql);

		if ($rowsQuestionMaster != "") {
			while ($row = mysqli_fetch_array($rowsQuestionMaster)) {
				$masterQuestionId = $row['dailyQuestionId'];
				$this->optionText = 'N/A';
				$this->dailyOptionValue = '0';

				$this->CopyQuestionChoicesToSchool($masterQuestionId);
			} //End while defaultquestionmaster
		}
	}
	function CopyQuestionChoicesToSchool($questionMasterId)
	{
		$sql = "INSERT INTO schooldailyevaldetails (dailyQuestionId, optionText,dailyOptionValue) 
						VALUES ('" . addslashes($questionMasterId) . "',
								'" . addslashes($this->optionText) . "',
								'" . addslashes($this->dailyOptionValue) . "'
								)";

		$objDB = new clsDB();
		$schoolQuestionDId = $objDB->ExecuteInsertQuery($sql);
		unset($objDB);
	}

	function GetAllSections()
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM defaultdailyevalsectionmaster ORDER BY sortOrder";
		//echo $sql;	exit;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetDefaultMasteryQuestionCountbySections($sectionMasterId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT count(masterySectionId) as count
					FROM defaultmasteryevalquestionmaster 
					WHERE masterySectionId=" . $sectionMasterId;
		//echo $sql;		
		$rows = $objDB->GetSingleFieldValue($sql);
		return $rows;
		unset($objDB);
	}
	function GetDefaultMasteryEvaluationSectionDetail($sectionMasterId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM defaultmasteryevalsectionmaster 
				WHERE masterySectionId= " . $sectionMasterId;
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}


	function GetMasterySchoolEvaluationSectionDetail($sectionMasterId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM schoolmasterysection 
				WHERE masterySectionId= " . $sectionMasterId;
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}

	function DeleteMasteryEvaluationSection($sectionMasterId)
	{
		$objDB = new clsDB();

		$sql = "DELETE  FROM schoolmasterysection WHERE masterySectionId=" . $sectionMasterId;
		//ECHO 'DELETE->'.$sql;EXIT;
		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}

	function DeleteDefaultMasteryEvaluationSection($sectionMasterId)
	{
		$objDB = new clsDB();

		$sql = "DELETE  FROM defaultmasteryevalsectionmaster WHERE masterySectionId=" . $sectionMasterId;
		//ECHO 'DELETE->'.$sql;EXIT;
		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}

	function SaveDailySection($sectionId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($sectionId > 0) {

			$sql = "UPDATE defaultdailyevalsectionmaster SET 						
                             title = '" . addslashes($this->title) . "',						
                             sortOrder = '" . addslashes($this->sortOrder) . "'
                             Where sectionMasterId= " . $sectionId;
			//echo 'update->'.$sql;exit;
			$objDB->ExecuteQuery($sql);
		} else {
			$sql = "INSERT INTO defaultdailyevalsectionmaster (title,sortOrder) 
                    VALUES ('" . addslashes($this->title) . "',
                            '" . addslashes($this->sortOrder) . "'						
                            )";
			//echo 'Insert->'.$sql;exit;
			$sectionId = $objDB->ExecuteInsertQuery($sql);
		}

		unset($objDB);
		return $sectionId;
	}
	function DeleteDailyEvaluationSection($sectionMasterId)
	{
		$objDB = new clsDB();

		$sql = "DELETE  FROM defaultdailyevalsectionmaster WHERE sectionMasterId=" . $sectionMasterId;
		//ECHO 'DELETE->'.$sql;EXIT;
		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}

	function GetAllDailyEvaluationQuestionToSetting($sectionMasterId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT defaultdailyevalquestionmaster.*,questiontypemaster.title as questionType FROM  defaultdailyevalquestionmaster
					LEFT JOIN questiontypemaster ON defaultdailyevalquestionmaster.dailyQuestionType=questiontypemaster.questionType
					WHERE sectionMasterId=" . $sectionMasterId;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}
	function GetAllDailyEvaluationAssignQuestionToSection($sectionMasterId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM  defaultdailyevalsectionmaster
					WHERE sectionMasterId=" . $sectionMasterId;
		//echo $sql;		
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}
	function GetDailyEvaluationQuestionDetail($questionId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM  defaultdailyevalquestionmaster
                            WHERE  dailyQuestionId=" . $questionId;
		//echo $sql;exit;
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}

	function GetDailyQuestionOptionDetails($questionId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT defaultdailyevalquestionmaster. *,defaultdailyevalquestiondetails.*
					FROM defaultdailyevalquestionmaster 
					INNER JOIN defaultdailyevalquestiondetails ON defaultdailyevalquestionmaster.dailyQuestionId=
						defaultdailyevalquestiondetails.dailyQuestionId
					WHERE defaultdailyevalquestionmaster.dailyQuestionId=" . $questionId .
			" ORDER BY defaultdailyevalquestiondetails.dailyOptionValue";
		//echo $sql;exit;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function SaveDailyevaluationQuestion($questionId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($questionId > 0) {

			$sql = "UPDATE defaultdailyevalquestionmaster SET 						
								optionText = '" . addslashes($this->optionText) . "',
								dailyQuestionType = '" . addslashes($this->dailyQuestionType) . "'
								Where dailyQuestionId= " . $questionId;
			//echo 'Insert->'.$sql;exit;
			$objDB->ExecuteQuery($sql);
		} else {
			$sql = "INSERT INTO defaultdailyevalquestionmaster (optionText,dailyQuestionType, sectionMasterId) 
					VALUES ('" . addslashes($this->optionText) . "',
							'" . addslashes($this->dailyQuestionType) . "',				
							'" . addslashes($this->sectionMasterId) . "'
							)";
			//echo 'Insert->'.$sql;exit;
			$questionId = $objDB->ExecuteInsertQuery($sql);
		}

		unset($objDB);
		return $questionId;
	}

	function DeleteDailyEvaluationOptions($questionId)
	{
		$objDB = new clsDB();

		$sql = "DELETE  FROM defaultdailyevalquestiondetails WHERE dailyQuestionId=" . $questionId;

		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}

	function SaveDailyEvaluationQuestionOptions()
	{
		$objDB = new clsDB();
		$sql = '';
		$sql = "INSERT INTO defaultdailyevalquestiondetails(dailyQuestionId,optionText,dailyOptionValue) 
				VALUES ('" . addslashes($this->dailyQuestionId) . "',
						'" . addslashes($this->optionText) . "',					
						'" . addslashes($this->dailyOptionValue) . "'					
						)";
		//echo 'Insert->'.$sql;exit;
		$objDB->ExecuteInsertQuery($sql);
		unset($objDB);
	}

	function DeleteDailyEvaluationQuestion($questionId)
	{
		$objDB = new clsDB();

		$sql = "DELETE  FROM defaultdailyevalquestionmaster WHERE  dailyQuestionId=" . $questionId;

		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}

	function GetAllSchoolSections($currentSchoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM schoolmasterysection WHERE  schoolId=" . $currentSchoolId . " ORDER BY sortOrder";
		//echo $sql;	exit;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function SaveMasterySchoolSection($sectionId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($sectionId > 0) {

			$sql = "UPDATE schoolmasterysection SET 						
                             title = '" . addslashes($this->title) . "',						
                             sortOrder = '" . addslashes($this->sortOrder) . "'
                             Where masterySectionId = " . $sectionId;
			//echo 'update->'.$sql;exit;
			$objDB->ExecuteQuery($sql);
		} else {
			$sql = "INSERT INTO schoolmasterysection (schoolId,title,sortOrder) 
                    VALUES ('" . addslashes($this->schoolId) . "',
                            '" . addslashes($this->title) . "',
                            '" . addslashes($this->sortOrder) . "'						
                            )";
			//echo 'Insert->'.$sql;exit;
			$sectionId = $objDB->ExecuteInsertQuery($sql);
		}

		unset($objDB);
		return $sectionId;
	}
	function SaveDefaultMasterySection($sectionId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($sectionId > 0) {

			$sql = "UPDATE defaultmasteryevalsectionmaster SET 						
                             title = '" . addslashes($this->title) . "',						
                             sortOrder = '" . addslashes($this->sortOrder) . "'
                             Where masterySectionId = " . $sectionId;
			//echo 'update->'.$sql;exit;
			$objDB->ExecuteQuery($sql);
		} else {
			$sql = "INSERT INTO defaultmasteryevalsectionmaster (title,sortOrder) 
                    VALUES ('" . addslashes($this->title) . "',
                            '" . addslashes($this->sortOrder) . "'						
                            )";
			//echo 'Insert->'.$sql;exit;
			$sectionId = $objDB->ExecuteInsertQuery($sql);
		}

		unset($objDB);
		return $sectionId;
	}

	function GetAllMasteryEvaluationQuestionToSetting($currentSchoolId, $sectionMasterId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT schoolmasterysectionmaster.*,questiontypemaster.title as questionType FROM  schoolmasterysectionmaster
                                LEFT JOIN questiontypemaster ON schoolmasterysectionmaster.masteryQuestionType=questiontypemaster.questionType
                                WHERE schoolId=" . $currentSchoolId . " AND schoolmasterysectionmaster.masterySectionId=" . $sectionMasterId;
		// if($currentSchoolId == '118')
		$sql .= " ORDER BY sortOrder ASC";
		// echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetAllDefaultMasteryEvaluationQuestionToSetting($sectionMasterId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT defaultmasteryevalquestionmaster.*,questiontypemaster.title as questionType FROM  defaultmasteryevalquestionmaster
                                LEFT JOIN questiontypemaster ON defaultmasteryevalquestionmaster.masteryQuestionType=questiontypemaster.questionType
                                WHERE defaultmasteryevalquestionmaster.masterySectionId=" . $sectionMasterId;
		//echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetMasteryEvaluationQuestionDetail($currentSchoolId, $questionId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM  schoolmasterysectionmaster
                                WHERE schoolId=" . $currentSchoolId . " AND masteryQuestionId=" . $questionId;
		//echo $sql;		
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}

	function GetDefaultMasteryEvaluationQuestionDetail($questionId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM  defaultmasteryevalquestionmaster
                                WHERE masteryQuestionId=" . $questionId;
		//echo $sql;		
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}

	function GetMasteryeevaluationQuestionOptionDetails($questionId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT schoolmasterysectionmaster. *,schoolmasteryevaldetails.*
						FROM schoolmasterysectionmaster 
						INNER JOIN schoolmasteryevaldetails ON schoolmasterysectionmaster.masteryQuestionId=
						schoolmasteryevaldetails.masteryQuestionId
						WHERE schoolmasterysectionmaster.masteryQuestionId=" . $questionId;
		// echo $sql;exit;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}
	function GetDefaultMasteryevaluationQuestionOptionDetails($questionId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT defaultmasteryevalquestionmaster.*,defaultmasteryevalquestiondetails.*,defaultmasteryevalquestiondetails.defaultOptionValue as schoolOptionValue
						FROM defaultmasteryevalquestionmaster 
						INNER JOIN defaultmasteryevalquestiondetails ON defaultmasteryevalquestionmaster.masteryQuestionId=
						defaultmasteryevalquestiondetails.masteryQuestionId
						WHERE defaultmasteryevalquestionmaster.masteryQuestionId=" . $questionId .
			" ORDER BY defaultmasteryevalquestiondetails.defaultOptionValue";
		//echo $sql;exit;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}



	function SaveMasteryevaluationQuestion($questionId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($questionId > 0) {

			$sql = "UPDATE schoolmasterysectionmaster SET 						
						 optionText = '" . addslashes($this->optionText) . "',
						 masteryQuestionType = '" . addslashes($this->masteryQuestionType) . "',
						 sortOrder = '" . addslashes($this->sortOrder) . "'
						 Where masteryQuestionId = " . $questionId;
			//echo 'Insert->'.$sql;exit;	
			$objDB->ExecuteQuery($sql);
		} else {
			$sql = "INSERT INTO schoolmasterysectionmaster(optionText,masteryQuestionType,masterySectionId,sortOrder,schoolId) 
				VALUES ('" . addslashes($this->optionText) . "',
						'" . addslashes($this->masteryQuestionType) . "',					
						'" . addslashes($this->sectionMasterId) . "',
						'" . addslashes($this->sortOrder) . "',
						'" . addslashes($this->schoolId) . "'					
						)";
			// echo 'Insert->'.$sql;exit;
			$questionId = $objDB->ExecuteInsertQuery($sql);
		}

		unset($objDB);
		return $questionId;
	}
	function SaveDefaultMasteryevaluationQuestion($questionId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($questionId > 0) {

			$sql = "UPDATE defaultmasteryevalquestionmaster SET 						
						optionText = '" . addslashes($this->questionText) . "',
						masteryQuestionType = '" . addslashes($this->masteryQuestionType) . "'

						Where masteryQuestionId = " . $questionId;

			$objDB->ExecuteQuery($sql);
		} else {
			$sql = "INSERT INTO defaultmasteryevalquestionmaster (optionText,masteryQuestionType,masterySectionId) 
				VALUES ('" . addslashes($this->questionText) . "',				
						'" . addslashes($this->masteryQuestionType) . "',					
						'" . addslashes($this->sectionMasterId) . "'			
						)";

			$questionId = $objDB->ExecuteInsertQuery($sql);
		}

		unset($objDB);
		return $questionId;
	}

	function DeleteMasteryEvaluationQuestion($questionId)
	{
		$objDB = new clsDB();

		$sql = "DELETE  FROM schoolmasterysectionmaster WHERE masteryQuestionId=" . $questionId;
		//ECHO 'DELETE->'.$sql;EXIT;
		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}

	function DeleteDefaultMasteryEvaluationQuestion($questionId)
	{
		$objDB = new clsDB();

		$sql = "DELETE  FROM defaultdailyevalquestionmaster WHERE masteryQuestionId=" . $questionId;

		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}

	function DeleteMasteryEvaluationQuestionDetail($questionId)
	{
		$objDB = new clsDB();

		$sql = "DELETE  FROM schoolmasteryevaldetails WHERE masteryQuestionId=" . $questionId;
		//ECHO 'DELETE->'.$sql;EXIT;
		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}

	function DeleteDefaultMasteryEvaluationQuestionDetail($questionId)
	{
		$objDB = new clsDB();

		$sql = "DELETE  FROM defaultmasteryevalquestiondetails WHERE masteryQuestionId=" . $questionId;

		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}


	function SaveMasteryEvaluationQuestionOptions()
	{
		$objDB = new clsDB();
		$sql = '';
		$sql = "INSERT INTO schoolmasteryevaldetails(masteryQuestionId,optionText,description) 
						VALUES ('" . addslashes($this->masteryQuestionId) . "',
								'" . addslashes($this->optionText) . "',					
								'" . addslashes($this->description) . "'					
								)";
		// echo 'Insert->'.$sql;exit;
		$objDB->ExecuteInsertQuery($sql);
		unset($objDB);
	}
	function SaveDefaultMasteryEvaluationQuestionOptions()
	{
		$objDB = new clsDB();
		$sql = '';
		$sql = "INSERT INTO defaultmasteryevalquestiondetails(masteryQuestionId,optionText,description) 
						VALUES ('" . addslashes($this->masteryQuestionId) . "',
								'" . addslashes($this->optionText) . "',					
								'" . addslashes($this->description) . "'					
								)";
		///echo 'Insert->'.$sql;exit;
		$objDB->ExecuteInsertQuery($sql);
		unset($objDB);
	}


	function GetDailySchoolMidtermEvaluationSectionDetail($sectionMasterId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM  schooldailyevalsectionmaster
						WHERE sectionMasterId=" . $sectionMasterId;
		//echo $sql;		
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}

	function DeleteDailySchoolEvaluationSection($sectionMasterId)
	{
		$objDB = new clsDB();
		$sql = "DELETE  FROM schooldailyevalsectionmaster WHERE sectionMasterId=" . $sectionMasterId;
		//ECHO 'DELETE->'.$sql;EXIT;
		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}


	function GetAllDailySchoolEvaluationQuestionToSetting($sectionMasterId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT schooldailyevalmaster.*,questiontypemaster.title as questionType FROM  schooldailyevalmaster
					INNER JOIN questiontypemaster ON schooldailyevalmaster.dailyQuestionType=questiontypemaster.questionType
					WHERE sectionMasterId=" . $sectionMasterId;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}
	function GetDailySchoolEvaluationQuestionDetail($questionId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM  schooldailyevalmaster
					WHERE  dailyQuestionId=" . $questionId;
		//echo $sql;exit;
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}

	function GetDailySchoolQuestionOptionDetails($questionId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT schooldailyevalmaster. *,schooldailyevaldetails.*
					FROM schooldailyevalmaster 
					INNER JOIN schooldailyevaldetails ON schooldailyevalmaster.dailyQuestionId=
						schooldailyevaldetails.dailyQuestionId
					WHERE schooldailyevalmaster.dailyQuestionId=" . $questionId .
			" ORDER BY schooldailyevaldetails.dailyOptionValue";
		//echo $sql;exit;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function SaveDailySchoolevaluationQuestion($questionId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($questionId > 0) {

			$sql = "UPDATE schooldailyevalmaster SET 						
								optionText = '" . addslashes($this->optionText) . "',
								dailyQuestionType = '" . addslashes($this->dailyQuestionType) . "'
								Where dailyQuestionId= " . $questionId;
			//echo 'Insert->'.$sql;exit;
			$objDB->ExecuteQuery($sql);
		} else {
			$sql = "INSERT INTO schooldailyevalmaster (optionText,dailyQuestionType, sectionMasterId, schoolId) 
					VALUES ('" . addslashes($this->optionText) . "',
							'" . addslashes($this->dailyQuestionType) . "',				
							'" . addslashes($this->sectionMasterId) . "',				
							'" . addslashes($this->schoolId) . "'
							)";
			//echo 'Insert->'.$sql;exit;
			$questionId = $objDB->ExecuteInsertQuery($sql);
		}

		unset($objDB);
		return $questionId;
	}
	function DeleteDailySchoolEvaluationOptions($questionId)
	{
		$objDB = new clsDB();

		$sql = "DELETE  FROM schooldailyevaldetails WHERE dailyQuestionId=" . $questionId;

		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}
	function SaveDailySchoolEvaluationQuestionOptions()
	{
		$objDB = new clsDB();
		$sql = '';
		$sql = "INSERT INTO schooldailyevaldetails(dailyQuestionId,optionText,dailyOptionValue) 
				VALUES ('" . addslashes($this->dailyQuestionId) . "',
						'" . addslashes($this->optionText) . "',					
						'" . addslashes($this->dailyOptionValue) . "'					
						)";
		//echo 'Insert->'.$sql;exit;
		$objDB->ExecuteInsertQuery($sql);
		unset($objDB);
	}

	function DeleteDailySchoolEvaluationQuestion($questionId)
	{
		$objDB = new clsDB();

		$sql = "DELETE  FROM schooldailyevalmaster WHERE  dailyQuestionId=" . $questionId;

		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}

	function GetMasterySchoolQuestionCountbySections($schoolId, $sectionMasterId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT count(masterySectionId) as count
					FROM schoolmasterysectionmaster 
					WHERE schoolId = $schoolId AND masterySectionId=" . $sectionMasterId;
		// echo $sql;		exit;
		$rows = $objDB->GetSingleFieldValue($sql);
		return $rows;
		unset($objDB);
	}

	function GetMasterySectionsId($masteryQuestionId, $schoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		// $sql = "SELECT schoolmasterysection. *,schoolmasterysectionmaster.*
		// 			FROM schoolmasterysection 
		// 			INNER JOIN schoolmasterysectionmaster ON schoolmasterysection.masterySectionId=
		// 			schoolmasterysectionmaster.masterySectionId
		// 			WHERE schoolmasterysection.masterySectionId=".$masterySectionId." GROUP BY schoolmasterysection.masterySectionId";

		$sql = "SELECT masterySectionId FROM schoolmasterysectionmaster WHERE masteryQuestionId=" . $masteryQuestionId . " AND schoolId=" . $schoolId;
		//echo $sql;		
		$rows = $objDB->GetSingleFieldValue($sql);
		return $rows;
		unset($objDB);
	}

	function GetMasterySectionsTitle($masterySectionId, $schoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		// $sql = "SELECT schoolmasterysection. *,schoolmasterysectionmaster.*
		// 			FROM schoolmasterysection 
		// 			INNER JOIN schoolmasterysectionmaster ON schoolmasterysection.masterySectionId=
		// 			schoolmasterysectionmaster.masterySectionId
		// 			WHERE schoolmasterysection.masterySectionId=".$masterySectionId." GROUP BY schoolmasterysection.masterySectionId";

		$sql = "SELECT title FROM schoolmasterysection WHERE masterySectionId=" . $masterySectionId . " AND schoolId=" . $schoolId;
		//echo $sql;		
		$rows = $objDB->GetSingleFieldValue($sql);
		return $rows;
		unset($objDB);
	}

	function GetAllMasteryEvalDatewise($selrotationId = 0, $from_date = '', $to_date = '', $studentId = 0)
	{
		$objDB = new clsDB();
		$sql = "SELECT studentmasteryeval.*,
						studentmasteryeval.studentMasteryEvalId AS MasteryEvalID,studentmasteryevaldetails.*,
						student.studentId,
						rotation.rotationId,rotation.title,student.firstName,student.lastName,student.rankId,
						rankmaster.rankId,rankmaster.title AS Ranktitle,rotation.parentRotationId,rotation.locationId as rotationLocationId,courses.locationId
						FROM studentmasteryeval
						LEFT JOIN studentmasteryevaldetails ON studentmasteryeval.studentMasteryEvalId=
						studentmasteryevaldetails.studentMasteryEvalId
						LEFT JOIN rotation ON studentmasteryeval.rotationId=rotation.rotationId
						LEFT JOIN student ON studentmasteryeval.studentId=student.studentId
						LEFT JOIN courses ON rotation.courseId=courses.courseId
						LEFT JOIN rankmaster ON student.rankId=rankmaster.rankId";
		$sql .= " WHERE  studentmasteryeval.studentMasteryEvalId !=0";
		if ($selrotationId > 0) {
			$sql .= "  AND studentmasteryeval.rotationId=" . $selrotationId;
		}
		if ($studentId > 0) {
			$sql .= "  AND studentmasteryeval.studentId=" . $studentId;
		}
		if ($from_date != '') {
			$sql .= " AND date(studentmasteryeval.evaluationDate) >= '" . $from_date . "'";
		}
		if ($to_date != '') {
			$sql .= " AND date(studentmasteryeval.evaluationDate) <= '" . $to_date . "'";
		}

		$sql .= " GROUP BY studentmasteryeval.studentMasteryEvalId";
		$sql .= " ORDER BY `studentmasteryeval`.`evaluationDate` DESC";

		//echo $sql;
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}
	function SaveAdminMastery($studentMasteryEvalId)
	{
		$objDB = new clsDB();
		$sql = '';

		$sql = "UPDATE studentmasteryeval SET						 
						 schoolDate	='" . addslashes($this->schoolDate) . "'
						 Where studentMasteryEvalId= " . $studentMasteryEvalId;
		//echo 'update->'.$sql;exit;
		$objDB->ExecuteQuery($sql);

		unset($objDB);
		return $studentMasteryEvalId;
	}

	//MasteryEvaluationList for app
	function GetAllMasteryEvalForApp($rotationId = 0, $clinicianId = 0, $studentId = 0, $courseId, $generateLimitString, $searchText)
	{
		$objDB = new clsDB();
		$sql = "SELECT studentmasteryeval.*,
						studentmasteryeval.studentMasteryEvalId AS MasteryEvalID,studentmasteryevaldetails.*,
						student.studentId,
						rotation.rotationId,rotation.title,student.firstName AS studentfName, student.lastName AS studentlname,student.rankId,
						rankmaster.rankId,rankmaster.title AS Ranktitle,rotation.parentRotationId,rotation.locationId as rotationLocationId,courses.locationId,
						hospitalsites.hospitalSiteId, hospitalsites.title AS Hospitalsite, courses.title AS course,
						clinician.firstName, clinician.lastName
						FROM studentmasteryeval
						LEFT JOIN studentmasteryevaldetails ON studentmasteryeval.studentMasteryEvalId=
						studentmasteryevaldetails.studentMasteryEvalId
						LEFT JOIN rotation ON studentmasteryeval.rotationId=rotation.rotationId
						LEFT JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
						LEFT JOIN student ON studentmasteryeval.studentId=student.studentId
						LEFT JOIN courses ON rotation.courseId=courses.courseId
						LEFT JOIN clinician ON studentmasteryeval.clinicianId = clinician.clinicianId
						LEFT JOIN rankmaster ON student.rankId=rankmaster.rankId";
		$sql .= " WHERE  studentmasteryeval.studentMasteryEvalId !=0";
		if ($rotationId > 0) {
			$sql .= "  AND studentmasteryeval.rotationId=" . $rotationId;
		}
		if ($studentId > 0) {
			$sql .= "  AND studentmasteryeval.studentId=" . $studentId;
		}
		if ($clinicianId > 0) {
			$sql .= "  AND studentmasteryeval.clinicianId=" . $clinicianId;
		}
		if ($courseId > 0) {
			$sql .= "  AND rotation.courseId=" . $courseId;
		}
		if ($searchText != "") {
			$sql .= " AND (CONCAT(student.firstName, ' ', student.lastName) LIKE '%" . $searchText . "%' )";
		}
		$sql .= " GROUP BY studentmasteryeval.studentMasteryEvalId";
		$sql .= " ORDER BY `studentmasteryeval`.`evaluationDate` DESC" . $generateLimitString;

		// echo $sql;exit;
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}

	function GetStudentMasteryQuestionDetails($studentMasteryMasterId, $schoolMasteryQuestionId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT schoolmasteryevaldetails.masteryQuestionId,schoolmasterysectionmaster.optionText AS questionTitle,schoolmasteryevaldetails.optionText AS optionValue,schoolmasteryevaldetails.description,schoolmasteryevaldetails.masteryQuestionDetailId FROM schoolmasteryevaldetails 
				INNER JOIN studentmasteryevaldetails ON studentmasteryevaldetails.studentQuestionId = schoolmasteryevaldetails.masteryQuestionId AND studentmasteryevaldetails.studentoptionvalue = schoolmasteryevaldetails.masteryQuestionDetailId AND studentmasteryevaldetails.studentMasteryEvalId = " . $studentMasteryMasterId . "
				LEFT JOIN schoolmasterysectionmaster on schoolmasterysectionmaster.masteryQuestionId = schoolmasteryevaldetails.masteryQuestionId
				WHERE schoolmasteryevaldetails.masteryQuestionId=" . $schoolMasteryQuestionId . " Group by schoolmasteryevaldetails.optionText ORDER BY `schoolmasteryevaldetails`.`masteryQuestionId` ASC";

		// echo $sql;
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);

	}

	function GetStudentMasteryTextQuestionDetails($studentMasteryMasterId, $schoolMasteryQuestionId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT '' AS description, studentmasteryevaldetails.studentOptionAnswerText as optionValue, schoolmasterysectionmaster.optionText as questionTitle FROM `studentmasteryevaldetails` 

				INNER JOIN schoolmasterysectionmaster ON schoolmasterysectionmaster.masteryQuestionId = studentmasteryevaldetails.studentQuestionId
				WHERE `studentMasteryEvalId` =" . $studentMasteryMasterId . "
				AND studentmasteryevaldetails.studentQuestionId = " . $schoolMasteryQuestionId . " ";
		// echo $sql;
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}

	/**
	 * Retrieves Clinician Document details for a given clinicianDocument ID and school ID.
	 *
	 * @param int $studentMasteryEvalIds The ID of the Clinician.
	 * @param int $Type The ID of the school. If not provided, all schools will be considered.
	 *
	 * @return array An array containing the log data, row data, and additional data.
	 */
	function GetAllMasteryEvalDetailsForLogs($studentMasteryEvalId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT studentmasteryeval.*, concat(clinician.firstName,' ',clinician.lastName) AS clinicianName,concat(student.firstName,' ',student.lastName) AS studentName,schools.schoolId,schools.displayName as schoolName
				FROM studentmasteryeval 
				INNER JOIN schools ON studentmasteryeval.schoolId=schools.schoolId 
				LEFT JOIN clinician on studentmasteryeval.clinicianId = clinician.clinicianId 
				LEFT JOIN student on studentmasteryeval.studentId = student.studentId 
				WHERE studentmasteryeval.studentMasteryEvalId=" . $studentMasteryEvalId;
		// echo $sql;exit;			 
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);

	}


	// Mastery Evaluation Section for Audit Log
	function GetMasterySectionDetailsForlog($sectionMasterId, $isSuperAdmin = 0)
	{

		$objDB = new clsDB();
		$rows = "";
		if ($isSuperAdmin) {
			$sql = "SELECT * FROM  defaultmasteryevalsectionmaster WHERE masterySectionId=" . $sectionMasterId;
		} else {
			$sql = "SELECT schoolmasterysection.*,schools.schoolId,schools.displayName as schoolName FROM  schoolmasterysection
				INNER JOIN schools ON schoolmasterysection.schoolId=schools.schoolId
				WHERE masterySectionId=" . $sectionMasterId;
		}
		// echo $sql;exit;
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}

	// Mastery Evaulation Steps For Audit Log
	function GetMasteryEvalStepDetailsForLogs($questionId, $isSuperAdmin = 0)
	{

		$objDB = new clsDB();
		$rows = "";
		if ($isSuperAdmin) {
			$sql = "SELECT * FROM  defaultmasteryevalquestionmaster
					WHERE masteryQuestionId=" . $questionId;
		} else {
			$sql = "SELECT schoolmasterysectionmaster. *,schoolmasteryevaldetails.*,schools.displayName as schoolName
					FROM schoolmasterysectionmaster 
					INNER JOIN schools ON schools.schoolId = schoolmasterysectionmaster.schoolId
					INNER JOIN schoolmasteryevaldetails ON schoolmasterysectionmaster.masteryQuestionId=
					schoolmasteryevaldetails.masteryQuestionId
					WHERE schoolmasterysectionmaster.masteryQuestionId=" . $questionId;
		}
		// echo $sql;exit;
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}


	/**
	 * This function creates a log entry for a CI evaluation action.
	 *
	 * @param int $id The ID of the CI evaluation.
	 * @param string $action The action performed (Add, Edit, Delete, Signoff).
	 * @param int $userId The ID of the user performing the action.
	 * @param string $userType The type of user performing the action (Student, Preceptor, Admin).
	 *
	 * @return array An array containing the log data, row data, and additional data.
	 */
	function createMasteryLog($id, $action, $userId, $userType, $type, $isSuperAdmin)
	{
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$objMasteryEval = new clsMasteryEval(); // Assuming `Attendance` class is used for `prepareAttendanceLogData`

		// Get user details based on user type
		$userDetails = getUserDetails($userId, $userType);
		if ($type == 'section') {
			$rowData = $objMasteryEval->GetMasterySectionDetailsForlog($id, $isSuperAdmin);
		} else if ($type == 'step') {
			$rowData = $objMasteryEval->GetMasteryEvalStepDetailsForLogs($id, $isSuperAdmin);
		} else {
			$rowData = $objMasteryEval->GetAllMasteryEvalDetailsForLogs($id);
		}

		if ($rowData != '')
			$logData = $objLog->generateLogData($rowData, $userType);

		$additionalData = '';

		// Populate log data with user details
		$logData['userId'] = $userDetails['userId'];
		$logData['userName'] = $userDetails['userName'];
		$logMessage = '';

		if ($type == 'section') {
			if ($action == 'Add') {
				$logMessage = $logData['userName'] . ' added new section in Mastery Evaluation.';
			} else if ($action == 'Edit') {
				$logMessage = $logData['userName'] . ' updated the Section from Mastery Evaluation.';
			} else if ($action == 'Delete') {
				$logMessage = $logData['userName'] . ' deleted the section from Mastery Evaluation.';
			} else if ($action == 'Active') {
				$logMessage = $logData['userName'] . ' activate the section from Mastery Evaluation.';
			} 
			// else if ($action == 'Inactive') {
			// 	$logMessage = $logData['userName'] . ' deactivate the section from Mastery Evaluation.';
			// }
		} else if ($type == "step") {

			if ($action == 'Add') {
				$logMessage = $logData['userName'] . ' added new Step in Mastery Evaluation Section.';
			} else if ($action == 'Edit') {
				$logMessage = $logData['userName'] . ' updated Step from Mastery Evaluation Section.';
			} else if ($action == 'Delete') {
				$logMessage = $logData['userName'] . ' deleted Step from Mastery Evaluation Section.';
			}
		} else {
			if ($action == 'Add') {
				$logMessage = $logData['userName'] . '  added Mastery Evaluation.';
			} else if ($action == 'Edit' && ($userType == 'Clinician' || $userType == 'Admin')) {
				$logMessage = $logData['userName'] . ' updated Mastery Evaluation of ' . $rowData['studentName'] . ' Student.';
			} else if ($action == 'Edit' && $userType == 'Student') {
				$logMessage = $logData['userName'] . ' updated Mastery Evaluation.';
			} else if ($action == 'Delete') {
				$logMessage = $logData['userName'] . ' deleted Mastery Evaluation of ' . $rowData['studentName'] . ' Student.';
			}
		}
		// Construct log message
		$logData['message'] = $logMessage;

		// Return the data
		return [$logData, $rowData, $additionalData];
	}

	/**
	 * Saves audit log for CI evaluation actions
	 *
	 * @param int $id ID of the CI evaluation master record
	 * @param int $userId User ID of the user performing the action
	 * @param string $userType Type of user (e.g. student, clinician, etc.)
	 * @param string $action Action type (Add, Edit, Delete, Signoff)
	 * @param int $isMobile Indicates if the action was performed from a mobile device (0 = false, 1 = true)
	 *
	 * @return bool Returns true if successful
	 */
	function saveMasteryAuditLog($id, $userId, $userType, $action, $isMobile = 0, $type = '', $isSuperAdmin = 0)
	{
		// echo "isSuperAdmin ".$isSuperAdmin;exit;
		// Instantiate the Logger and Checkoff classes
		$objLog = new clsLogger();
		$objMasteryEval = new clsMasteryEval();


		// Prepare log data
		[$logData, $rowData, $additionalData] = $objMasteryEval->createMasteryLog($id, $action, $userId, $userType, $type, $isSuperAdmin);
		$logData['isMobile'] = $isMobile;

		if ($action == 'Delete') {

			if ($type == 'section') {
				if ($isSuperAdmin) {
					$objMasteryEval->DeleteDefaultMasteryEvaluationSection($id);
				} else {
					$objMasteryEval->DeleteMasteryEvaluationSection($id);
				}
			} else if ($type == 'step') {

				if ($isSuperAdmin) {
					// Initialize database object
					$objDB = new clsDB();

					// Fetch data from `siteevaluationdetail` table for the given master ID
					$evaluationDetailsResult = $objDB->GetAllSelectedDataFromTable('defaultmasteryevalquestiondetails', '', 'masteryQuestionId', $id);
					unset($objDB);

					if ($evaluationDetailsResult) {
						// Convert the result set into an array
						$evaluationDetailsArray = fetchResultAsArray($evaluationDetailsResult);

						// Generate the JSON array (if needed for logs or other purposes)
						$additionalData = $evaluationDetailsArray;

						$objMasteryEval->DeleteDefaultMasteryEvaluationQuestion($id);

					}
				} else {
					// Initialize database object
					$objDB = new clsDB();

					// Fetch data from `siteevaluationdetail` table for the given master ID
					$evaluationDetailsResult = $objDB->GetAllSelectedDataFromTable('schoolmasteryevaldetails', '', 'masteryQuestionId', $id);
					unset($objDB);

					if ($evaluationDetailsResult) {
						// Convert the result set into an array
						$evaluationDetailsArray = fetchResultAsArray($evaluationDetailsResult);

						// Generate the JSON array (if needed for logs or other purposes)
						$additionalData = $evaluationDetailsArray;
						// print_r($additionalData);exit;

						$objMasteryEval->DeleteMasteryEvaluationQuestion($id);

					}
				}


			} else {

				// Initialize database object
				$objDB = new clsDB();

				// Fetch data from `studentdailyevaldetails` table for the given master ID
				$evaluationDetailsResult = $objDB->GetAllSelectedDataFromTable('studentmasteryevaldetails', '', 'studentMasteryEvalId', $id);
				unset($objDB);

				if ($evaluationDetailsResult) {
					// Convert the result set into an array
					$evaluationDetailsArray = fetchResultAsArray($evaluationDetailsResult);

					// Generate the JSON array (if needed for logs or other purposes)
					$additionalData = $evaluationDetailsArray;

					$objMasteryEval->DeleteMasteryEval($id);
				}

			}
		}
		// Save the log details
		$objLog->saveLogs($logData, $action, $id, 'Mastery', $rowData, $additionalData);

		// Clean up
		unset($objLog);
		unset($objCIevaluation);

		return true; // Return success or handle further actions as needed
	}

}
