<?php

include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsSystemUser.php');
include('../class/clsSchool.php');
include('../setRequest.php');
include('../class/Zebra_Image.php');
include('../class/clsChatApp.php');


if (isset($_POST['btnSubmit'])) {
	$objSystemUser = new clsSystemUser();



	$userId = $_SESSION['loggedUserId'];

	$firstName = $_POST['txtFirstName'];
	$lastName = $_POST['txtLastName'];
	$email = $_POST['txtEmail'];
	$userName = $_POST['txtUsername'];

	$objSystemUser->firstName = $firstName;
	$objSystemUser->lastName = $lastName;
	$objSystemUser->email = $email;
	$objSystemUser->username = $userName;
	$objSystemUser->updatedBy = $userId;
	$objSystemUser->updatedDate = date('Y-m-d h:i:s');
	$retUserId = $objSystemUser->UpdateSystemUserProfile($userId);

	if ($retUserId > 0) {

		//Set Update Session
		$_SESSION["loggedUserName"] = $userName;
		$_SESSION["loggedUserFirstName"] = $firstName;
		$_SESSION["loggedUserLastName"] = $lastName;

		// Initialize Chat and Database objects
		$objChatApp = new clsChatApp();
		$objDB = new clsDB();

        $role_id = 2;
        $profileImagePath = '';
        $userRoleManagementId = $objDB->GetSingleColumnValueFromTable('userrolemanagement', 'id', 'userId', $retUserId, 'role_Id', $role_id);
        $phone = $objDB->GetSingleColumnValueFromTable('systemusermaster', 'phone', 'systemUserMasterId', $retUserId, 'schoolId', $currentSchoolId);
        $currenschoolDisplayname = $objDB->GetSingleColumnValueFromTable('schools', 'displayName', 'schoolId', $currentSchoolId);
        $userRoleManagementId = $userRoleManagementId ?: 0;

        if (isset($objChatApp) && $userRoleManagementId) {
            $objChatApp->prepareAndSendUserData($objChatApp, $retUserId, $firstName, $lastName, $email, $phone, $profileImagePath, $address1, $role_id, $currenschoolDisplayname, $currentSchoolId, $userRoleManagementId);
        }

        // Cropped image
        $coverImage = $_POST['fileLogo'] ?? '';
        if ($coverImage != '') {
            $ext = getFileExtensionFromBase64($coverImage);
            $image_array_2 = explode(",", explode(";", $coverImage)[1]);
            $coverImage = base64_decode($image_array_2[1]);

            $smallFilename = "PROFILE_SMALL_{$retUserId}.{$ext}";
            $largeFilename = "PROFILE_LARGE_{$retUserId}.{$ext}";
            $basePath = "../upload/schools/{$currentSchoolId}/users/{$retUserId}/";

            if (!file_exists($basePath)) {
                mkdir($basePath, 0777, true);
            }

            file_put_contents($basePath . $smallFilename, $coverImage);
            file_put_contents($basePath . $largeFilename, $coverImage);

            $objSystemUser->UpdateUserPhotosFileName($retUserId, $smallFilename, $largeFilename);
            $profileImageName = $objDB->GetSingleColumnValueFromTable('systemusermaster', 'profilePic', 'systemUserMasterId', $retUserId);
            if (!empty($profileImageName)) {
                $profileImagePath = BASE_PATH . "/upload/schools/{$currentSchoolId}/users/{$retUserId}/{$profileImageName}?id=" . rand(1, 10000);
                $objDB->UpdateSingleColumnValueToTable('userrolemanagement', 'profileImagePath', $profileImagePath, 'id', $userRoleManagementId);
                if (isset($objChatApp)) {
                    $objChatApp->UpdateUserProfilePicTochat($profileImagePath, $retUserId, $role_id);
                }
            }
        }

        // Traditional file upload
        if (!empty($_FILES['filePhoto']['name'])) {
            $Image = $_FILES['filePhoto']['name'];
            $ext = strtolower(pathinfo($Image, PATHINFO_EXTENSION));
            if (!in_array($ext, ['png', 'jpg', 'jpeg', 'gif'])) {
                header('location:editprofile.html?status=InvalidFile');
                exit;
            }

            $smallFilename = "PROFILE_SMALL_{$retUserId}.{$ext}";
            $largeFilename = "PROFILE_LARGE_{$retUserId}.{$ext}";
            $basePath = "../upload/schools/{$currentSchoolId}/users/{$retUserId}/";
            if (!file_exists($basePath)) {
                mkdir($basePath, 0777, true);
            }

            copy($_FILES['filePhoto']['tmp_name'], $basePath . $smallFilename);
            copy($_FILES['filePhoto']['tmp_name'], $basePath . $largeFilename);

            if (isset($_POST['chkAutoCrop'])) {
                // Resize small
                $image = new Zebra_Image();
                $image->source_path = $basePath . $smallFilename;
                $image->target_path = $basePath . $smallFilename;
                $image->resize(50, 50, ZEBRA_IMAGE_CROP_CENTER, '-1');

                // Resize large
                $image = new Zebra_Image();
                $image->source_path = $basePath . $largeFilename;
                $image->target_path = $basePath . $largeFilename;
                $image->resize(200, 200, ZEBRA_IMAGE_CROP_CENTER, '-1');
            }

            $_SESSION["loggedUserProfileImagePath"] = GetUserImagePath($retUserId, $currentSchoolId, $smallFilename);
            $_SESSION["loggedUserProfileLargeImagePath"] = GetUserImagePath($retUserId, $currentSchoolId, $largeFilename);

            $objSystemUser->UpdateUserPhotosFileName($retUserId, $smallFilename, $largeFilename);
            $profileImageName = $objDB->GetSingleColumnValueFromTable('systemusermaster', 'profilePic', 'systemUserMasterId', $retUserId);

            if (!empty($profileImageName)) {
                $profileImagePath = BASE_PATH . "/upload/schools/{$currentSchoolId}/users/{$retUserId}/{$profileImageName}?id=" . rand(1, 10000);
                $objDB->UpdateSingleColumnValueToTable('userrolemanagement', 'profileImagePath', $profileImagePath, 'id', $userRoleManagementId);
                if (isset($objChatApp)) {
                    $objChatApp->UpdateUserProfilePicTochat($profileImagePath, $retUserId, $role_id);
                }
            }
        }

        // Audit log
        $objLog = new clsLogger();
        $action = $objLog::EDIT;
        $type = isset($_SESSION['loggedAsBackUserId']) ? '' : 'Profile';
        $userType = $objLog::ADMIN;
        $IsMobile = 0;
        $objSystemUser->saveSchoolAdminAuditLog($retUserId, $userId, $userType, $action, $IsMobile, 0, $type);

        header('location:editprofile.html?status=Updated');
        exit;
    } else {
        header('location:editprofile.html?status=Error');
        exit;
    }
} else {
    header('location:editprofile.html');
    exit;
}
