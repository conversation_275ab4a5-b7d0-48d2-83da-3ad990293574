<?php
// Redirect from mobile
$IsMobile = isset($_GET['IsMobile']) ? ($_GET['IsMobile']) : 0;

include('../class/clsExternalPreceptors.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');

$preceptorId = 0;
$externalPreceptorId = 0;

$preceptorNum = isset($_GET['preceptorNum']) ? DecodeQueryData($_GET['preceptorNum']) : '';
$isCompletion = isset($_GET['isCompletion']) ? DecodeQueryData($_GET['isCompletion']) : 0;
$externalPreceptorId = isset($_GET['preceptorId']) ? DecodeQueryData($_GET['preceptorId']) : 0;
$isRegister = isset($_GET['isRegister']) ? ($_GET['isRegister']) : 0;

$externalPreceptorFirstName = '';
$standardPreceptors = '';
$objExternalPreceptors = new clsExternalPreceptors();

if ($preceptorNum) {
    $isPreceptorExist = $objExternalPreceptors->GetExternalPreceptorDetail($externalPreceptorId, $preceptorNum);
    $externalPreceptorFirstName = isset($isPreceptorExist['firstName']) ? $isPreceptorExist['firstName'] : '';
    $externalPreceptorLastName = isset($isPreceptorExist['lastName']) ? $isPreceptorExist['lastName'] : '';
    $externalPreceptorEmail = isset($isPreceptorExist['email']) ? $isPreceptorExist['email'] : '';
    $externalPreceptorId = isset($isPreceptorExist['id']) ? $isPreceptorExist['id'] : '';
    $standardPreceptors = serialize($preceptorNum);
}

if (!$preceptorNum)
    include('includes/validateUserLogin.php');
include('../class/clsSchool.php');
include('../class/clsRotation.php');
include('../class/clsHospitalSite.php');
include('../class/clsLocations.php');
include('../class/clsClinician.php');
include('../setRequest.php');
include('../class/clsStudent.php');
include('../class/clsStudentMinCharacterEntry.php');
include('../class/clsPreceptorDetails.php');
include('../class/clsSoapNote.php');

$page_title = "Add Soap Note";
$bedCrumTitle = 'Add';

$view = '';
$schoolId = 0;
$rotationId = '';
$studentId = 0;
$clinicianId = 0;
$getrotationId = 0;
$isSendToPreceptor = 0;
$studentId = 0;
$isSendToExternalPreceptor = isset($_GET['isSendToExternalPreceptor']) ? $_GET['isSendToExternalPreceptor'] : 0;
if ($isSendToExternalPreceptor)
    $studentSignatureDate = '';
else
    $studentSignatureDate = '';
$studentId = isset($_GET['studentId']) ? ($_GET['studentId']) : 0;

$TimeZone = isset($_SESSION["loggedClinicianSchoolTimeZone"]) ? $_SESSION["loggedClinicianSchoolTimeZone"] : '';
$soapNoteId = 0;

$objHospitalSite = new clsHospitalSite();
$objRotation = new clsRotation();

$hospitalSiteId = 0;

$soapNoteDate = '';
$patientDescription = '';
$cheifComplaint = '';
$admittingDiagnosis = '';
$hpi = '';
$pmh = '';
$medication = '';
$allergies = '';
$familyHistory = '';
$socialHistroy = '';
$ros = '';
$temperature = '';
$heartRate = '';
$bloodPressure = '';
$respiratoryRate = '';
$oxygenSaturation = '';
$physicalExam = '';
$laboratoryImaging = '';
$assessment = '';
$medicationTreatment = '';
$testOrder = '';
$referrals = '';
$patientEducationCounseling = '';
$followUp = '';
$precptorName = '';
$preceptorStatus = '';
$studentSignatureDate = '';
$clinicianSignatureDate = '';

$objSoapNote = new clsSoapNote();

$status = isset($_GET["status"]) ? $_GET["status"] : '';

//Edit Mode
if (isset($_GET['editsoapNoteId'])) {

    $page_title = "Signoff Soap Note";
    $bedCrumTitle = 'Signoff';

    $soapNoteId = DecodeQueryData($_GET['editsoapNoteId']);
    $row = $objSoapNote->GetAllSoapNoteDetails($soapNoteId, $schoolId);
    $rotationId = $row['rotationId'] ?? 0;
    $getrotationId = $row['rotationId'] ?? 0;
    if ($rotationId > 0) {
        $getrotationId = $rotationId;
    } else {
        $getrotationId;
    }

    $hospitalSiteId = $row['hospitalSiteId'] ?? 0;
    $isSendToPreceptor = $row['isSendToPreceptor'] ?? '';
    $studentId = $row['studentId'] ?? 0;
    $clinicianId = $row['clinicianId'] ?? 0;
    $soapNoteDate = $row['soapNoteDate'] ?? '';
    if ($soapNoteDate != '0000-00-00' && $soapNoteDate != '' && $soapNoteDate != '0000-00-00 00:00:00' && $soapNoteDate != '01/01/1970') {
        $soapNoteDate = date("m/d/Y", strtotime($soapNoteDate));
    } else {
        $soapNoteDate = '';
    }
    $patientDescription = $row['patientDescription'] ?? '';
    $cheifComplaint = $row['cheifComplaint'] ?? '';
    $admittingDiagnosis = $row['admittingDiagnosis'] ?? '';
    $hpi = $row['hpi'] ?? '';
    $pmh = $row['pmh'] ?? '';
    $medication = $row['medication'] ?? '';
    $allergies = $row['allergies'] ?? '';
    $familyHistory = $row['familyHistory'] ?? '';
    $socialHistroy = $row['socialHistroy'] ?? '';
    $ros = $row['ros'] ?? '';
    $temperature = $row['temperature'] ?? '';
    $heartRate = $row['heartRate'] ?? '';
    $bloodPressure = $row['bloodPressure'] ?? '';
    $respiratoryRate = $row['respiratoryRate'] ?? '';
    $oxygenSaturation = $row['oxygenSaturation'] ?? '';
    $physicalExam = $row['physicalExam'] ?? '';
    $laboratoryImaging = $row['laboratoryImaging'] ?? '';
    $assessment = $row['assessment'] ?? '';
    $medicationTreatment = $row['medicationTreatment'] ?? '';
    $testOrder = $row['testOrder'] ?? '';
    $referrals = $row['referrals'] ?? '';
    $patientEducationCounseling = $row['patientEducationCounseling']  ?? '';
    $followUp = $row['followUp'] ?? '';
    $signatureDate = $row['signatureDate'] ?? '';

    $studentSignatureDate = isset($row['studentSignatureDate']) ? stripslashes($row['studentSignatureDate']) : 0;
    if ($studentSignatureDate != '0000-00-00' && $studentSignatureDate != '' && $studentSignatureDate != '0000-00-00 00:00:00' && $studentSignatureDate != '01/01/1970') {
        $studentSignatureDate = converFromServerTimeZone($studentSignatureDate, $TimeZone);
        $studentSignatureDate = date("m/d/Y", strtotime($studentSignatureDate));
    } else {
        $studentSignatureDate = '';
    }

    // Get preceptor details
    $objPreceptorDetails = new clsPreceptorDetails();
    $preceptorDetails = $objPreceptorDetails->GetSoapNotePreceptorDetails($soapNoteId);
    if ($preceptorDetails != '') {

        $preceptorId = $preceptorDetails['preceptorId'];
        $status = $preceptorDetails['status'];
        $signatureDate = $preceptorDetails['signatureDate'];
        $mobile_num = $preceptorDetails['mobile_num'];
        $firstName = $preceptorDetails['firstName'];
        $lastName = $preceptorDetails['lastName'];
        $precptorName = ($firstName != '' && $lastName != '') ? $firstName . ' ' . $lastName : '';
        $clinicianSignatureDate = $signatureDate;
    }

    if ($isSendToPreceptor == 0) {
        $clinicianSignatureDate = isset($row['clinicianSignatureDate']) ? stripslashes($row['clinicianSignatureDate']) : 0;
        if ($clinicianSignatureDate != '12/31/1969' && $clinicianSignatureDate != '' && $clinicianSignatureDate != '11/30/-0001' && $clinicianSignatureDate != '0000-00-00') {
            $clinicianSignatureDate = converFromServerTimeZone($clinicianSignatureDate, $TimeZone);
            $clinicianSignatureDate = date("m/d/Y", strtotime($clinicianSignatureDate));
        } else
            $clinicianSignatureDate = '-';
    } else {
        $clinicianSignatureDate = stripslashes($row['signatureDate']); //preceptor signature date
        if ($clinicianSignatureDate != '12/31/1969' && $clinicianSignatureDate != '' && $clinicianSignatureDate != '11/30/-0001' && $clinicianSignatureDate != '0000-00-00') {
            $clinicianSignatureDate = converFromServerTimeZone($clinicianSignatureDate, $TimeZone);
            $clinicianSignatureDate = date("m/d/Y", strtotime($clinicianSignatureDate));
        } else
            $clinicianSignatureDate = '-';
    }

    //For Rotation List
    $rotationStatus = checkRotationStatus($getrotationId);
    if ($rotationStatus)
        $rotation = $objRotation->GetAllRotationForDropdown($currentSchoolId, $studentId);
    else
        $rotation = $objRotation->GetAllActiveRotationForDropdown($currentSchoolId, $studentId);
} else {
    $schoolId = $currentSchoolId;
    $page_title = "Add Soap Note";
    $bedCrumTitle = 'Add';
    if (isset($_GET['rotationId'])) {
        $getrotationId = DecodeQueryData($_GET['rotationId']);
    }
}

//Get Rotation Title
$objrotation = new clsRotation();
$rowsrotation = $objrotation->GetrotationTitleForInteraction($getrotationId, $currentSchoolId);
$rotationtitle = $rowsrotation ? $rowsrotation['title'] : '';

if (isset($_GET['view'])) {
    $view = $_GET['view'];
    $bedCrumTitle = 'View';
}
$view = ($view == 'V') ? 1 : 0;

$objStudent = new clsStudent();
$studentId = ($studentId > 0) ? $studentId : $studentId;
$StudentDetails = $objStudent->GetStudentDetails($studentId);

if ($StudentDetails != '') {
    $firstName = $StudentDetails['firstName'];
    $lastName = $StudentDetails['lastName'];
    $fullName = $firstName . ' ' . $lastName;
}
unset($objStudent);

?>


<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content
        must come *after* these tags -->
    <title>
        <?php echo ($page_title); ?>
    </title>
    <?php include('includes/headercss.php'); ?>
    <?php include("includes/datatablecss.php") ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/toggleButton.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/stepper.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">
    <script type="module" src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.esm.js"></script>
    <script nomodule src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.js"></script>
    <!-- <link rel="stylesheet" href="<?php //echo ($dynamicOrgUrl); ?>/assets/css/style.css"> -->

    <style>
        .d-flex {
            display: flex;
        }

        .margin-right {
            margin-right: 7px;
        }

        .checkboxStyle {
            margin: 0 !important;
            width: 50%;
            border: 1px solid #ccc;
        }

        .alignCenter {
            align-items: center !important;
        }

        .abnormal {
            color: red !important;
        }

        .btn-container {
            display: flex;
            align-items: center;
            width: fit-content;
        }

        .btn-color-mode-switch {
            display: flex;
            align-items: center;
            width: fit-content;
            margin-left: 7px;
        }

        .panel {
            border: 2px solid #ddd;
        }

        .precedure-count-card {
            padding: 15px;
            border-radius: 15px;
            border: 2px solid #ddd;
            /* box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 5px 0px, rgba(0, 0, 0, 0.1) 0px 0px 1px 0px; */
        }

        .procedure-count-card-header {
            display: flex;
            justify-content: space-between;
        }

        .procedure-count-name {
            font-size: 15px;
            font-weight: 600;
        }

        .mb-0 {
            margin-bottom: 0;
        }

        .input-group-addon {
            position: absolute;
            right: 7px;
            /* width: 100%; */
            z-index: 99;
            width: 35px;
            margin: auto;
            top: 5px;
            border-left: 1px solid #ccc;
            border-radius: 50% !important;
            padding: 10px -2px;
            height: 35px;
            /* background: #01A750; */
            /* color: #fff; */
            color: #555;
            background: #f6f9f9;
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .panel-default>.panel-heading {
            background-color: transparent !important;
        }

        .btn-success,
        .btn-default {
            padding: 8px 25px;
            border-radius: 10px;
        }

        .panel {
            border-radius: 14px !important;
        }


        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 45px !important;
        }

        .required-select2 {
            border-left: solid 3px red !important;
            border-radius: 12px !important;
        }

        .select2-container--default .select2-selection--single {
            background-color: #f6f9f9 !important;
            cursor: default !important;
            height: 45px !important;
            border-radius: 10px !important;
        }

        .select2-container--default .select2-selection--single {
            border: none !important;
        }

        .panel,
        .form-group {
            margin-bottom: 10px;
        }

        .bootstrap-datetimepicker-widget {
            border-radius: 12px !important;
        }

        .form-control {
            height: 45px;
        }

        textarea.form-control {
            height: auto;
        }

        .pl-0 {
            padding-left: 0;
        }

        .blood-pressure-input {
            width: 80px;
            display: inline-block;
            margin: 5px 10px;
        }

        .abnormal-value-checkbox {
            padding: 0;
            width: 37px;
            height: 37px;
            margin-left: 10px;
        }

        .row-delete-icon {
            font-size: 18px;
            padding-left: 6px;
        }

        .mobile-block {
            display: flex;
            align-items: center;
        }

        /* .form-horizontal .form-group {
            margin-right: 0;
            margin-left: 0;
        } */


        .select2-container {
            width: 100% !important;
        }

        ion-icon {
            pointer-events: none;
        }

        .icon-inner,
        .ionicon,
        svg {
            color: #01A750;
        }

        .form-stepper-horizontal li:not(:last-child):after {
            height: 2px;
            top: 33%;
        }

        .button {
            padding: 10px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 500;
        }

        ul.form-stepper li a .form-stepper-circle {
            width: 35px;
            height: 35px;
            line-height: 1.7rem;
            margin-top: -5px;
            font-size: 20px;
            font-weight: 600;
            box-shadow: rgba(0, 0, 0, 0.15) 2.4px 2.4px 3.2px;
        }

        .stepper-label {
            font-size: 15px !important;
        }

        .form-stepper .label {
            padding: 0.8em .6em .3em;
            line-height: 1;
        }

        .arrow-icon {
            transition: transform 0.3s;
        }

        .collapsible.expanded .arrow-icon i {
            transform: rotate(180deg);
        }

        .collapsible-arrow.rotate {
            transform: rotate(180deg);
        }

        .panel-collapse.collapse.active {
            display: block;
            max-height: fit-content !important;
            transition: max-height 0.3s ease;
        }

        .panel-heading {
            padding: 5px;
        }

        .panel-body {
            overflow-x: auto;
            padding-top: 5px;
        }

        .position-relative {
            position: relative;
        }

        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .bootstrap-datetimepicker-widget {
            z-index: 9999;
        }

        .procedeure-count-form-field-main {
            display: flex;
        }

        .procedure-count-form-field {
            width: 50%;
            padding-right: 15px;
            padding-left: 15px;
        }

        .text-right {
            text-align: right;
        }

        /* new style start */

        .hidden-cards {
            display: none;
        }

        .hr-margin {
            margin-top: 5px !important;
            margin-bottom: 5px !important;
            /* margin-left: -15px; */
        }

        .getProcedureSteps {
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
        }

        .mobile-mb-0 {
            margin-bottom: 10px;
        }

        .mobile-mt-0 {
            margin-top: 20px;
        }

        .procedure-name-section {
            max-width: 75%;
        }

        /* new style end */

        @media screen and (max-width: 768px) {
            .panel-body {
                padding: 0 5px;
            }

            /* .card-mobile-padding {
                padding: 0;
            } */

            .mobile-mb-0 {
                margin-bottom: 0px;
            }

            .mobile-mt-0 {
                margin-top: 0px;
            }

        }


        @media screen and (max-width: 500px) {
            .panel-body ol {
                padding-left: 20px;
            }

            .panel-default>.panel-heading {
                padding-left: 5px;
            }

            .blood-pressure-input {
                width: 100%;
                margin: 5px 0;
            }

            /* .panel-body {
                padding: 0 5px;
            } */

            .panel-body {
                padding: 0 15px;
            }

            .btn-color-mode-switch {
                margin-left: 0px;
            }

            .card-mobile-padding {
                /* padding: 4px; */
                padding: 0;
            }

            /* .procedure-count-form-field {
                padding-right: 7px;
                padding-left: 7px;
            } */

            .btn-container {
                width: fit-content;
                flex-direction: column;
                align-items: self-start;
                gap: 5px;
            }

            .mobile-margin {
                margin-left: 0;
                margin-right: 0;
            }

            .panel-group .panel {
                padding: 10px;
            }

            /* .container-zero{
            padding: 0;
         } */

            .mobile-gray-border {
                border: 1px solid #ececec;
            }

            .panel-group .panel-heading {
                width: 100%;
            }

            .collapsible {
                cursor: pointer;
                width: 100%;
                /* padding: 15px; */
                /* border: 1px solid #181818; */
                /* background-color: #f9f9f9; */
                display: flex;
                justify-content: space-between;
                align-items: center;
                /* border-radius: 14px; */
            }

            .collapsible p {
                margin: 0;
            }

            .collapsible-arrow {
                font-size: 18px;
                transition: transform 0.3s ease;
            }

            .content {
                display: none;
                padding: 10px 0;
                /* border-top: 1px solid #ececec; */
            }

            .content.active {
                display: block;
            }

            .active.collapsible-arrow {
                transform: rotate(180deg);
            }

            .row-delete-icon {
                position: absolute;
                top: -82px;
                right: 20px;
            }

            .mobile-block {
                display: block;
            }

            /* .form-stepper-horizontal li {
                min-width: 90px;
            } */

            .form-step {
                padding: 2rem 1rem;
            }

            .pt-0 {
                padding-top: 0 !important;
            }

            .mobile-mb-0 {
                margin-bottom: 0px;
            }

            .mobile-mt-0 {
                margin-top: 0px;
            }

            .stepper-label {
                font-size: 14px !important;
            }

            #step-1,
            #step-2,
            #step-4 {
                padding-left: 0;
                padding-right: 0;
            }
        }

        .gray-box {
            background-color: white;
            border-radius: 15px;
            padding: 25px;
            border: 1px solid #ccc;
            margin-bottom: 30px;
        }

        .gray-box h4 {
            margin-bottom: 5px;
        }

        .gray-box p {
            margin-bottom: 20px;
            font-weight: 500;
        }

        .gray-box label {
            font-weight: 500;
        }

        .mb-2 {
            line-height: 2.7;
        }
    </style>
</head>

<body>

    <?php if (!$preceptorNum && $IsMobile == 0) { ?>

        <?php include('includes/header.php'); ?>
        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
                        <li><a href="dashboard.html">Home</a></li>
                        <li><a href="rotations.html">Rotation</a></li>
                        <?php if ($getrotationId > 0) { ?>
                            <li>
                                <a href="rotations.html">
                                    <?php echo ($rotationtitle); ?>
                                </a>
                            </li>
                        <?php } ?>

                        <li><a href="soapNoteList.html?rotationId=<?php echo EncodeQueryData($getrotationId); ?>">Soap Note</a></li>
                        <li class="active">
                            <?php echo ($bedCrumTitle); ?>
                        </li>
                    </ol>
                </div>
            </div>
        </div>
    <?php  } else  ?>
    <!-- <br> -->
    <div class="container mb-15 mobile-padding-4">
        <form id="formSoapNote" autocomplete="off" data-parsley-validate="data-parsley-validate" class="form-horizontal" method="POST" <?php if (isset($_GET['isSendToExternalPreceptor'])) { ?> action="soapNoteSubmit.html?editsoapNoteId=<?php echo EncodeQueryData($soapNoteId); ?>&isPreceptor=1" <?php } elseif ($soapNoteId) { ?> action="soapNoteSubmit.html?editsoapNoteId=<?php echo EncodeQueryData($soapNoteId); ?>" <?php } else { ?> action="soapNoteSubmit.html" <?php } ?>>
            <!-- Mobile redirect -->

            <input type="hidden" id="stuentId" name="studentId" value="<?php echo ($studentId) ?>">
            <div class="row mobile-margin mobile-mb-0">
                <div class="col-md-6">

                    <div class="form-group">
                        <label class="col-md-12 control-label" for="cborotation">Rotation</label>
                        <div class="col-md-12">
                            <select id="cborotation" name="cborotation" class="form-control input-md  select2_single required-input" onChange="getHospital(this.value);getclinician(this.value);" required data-parsley-errors-container="#error-cborotation">
                                <option value="" selected>Select</option>
                                <?php
                                if ($rotation != "") {
                                    while ($row = mysqli_fetch_assoc($rotation)) {
                                        $selrotationId  = $row['rotationId'];
                                        $name  = stripslashes($row['title']);
                                ?>
                                        <option value="<?php echo EncodeQueryData($selrotationId); ?>" <?php if ($getrotationId == $selrotationId) { ?> selected="true" <?php } ?>>
                                            <?php echo ($name); ?>
                                        </option>

                                <?php

                                    }
                                }
                                ?>
                            </select>
                            <div id="error-cborotation"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="cbohospitalsites">Hospital Site</label>
                        <div class="col-md-12">
                            <select id="cbohospitalsites" name="cbohospitalsites" class="form-control input-md select2_single">

                                <option value=""> </option>
                            </select>
                            <div id="cbohospitalsitese-error"></div>
                        </div>
                    </div>
                </div>

            </div>

            <div class="row mobile-margin mobile-mb-0">

                <div class="col-md-6">

                    <div class="form-group">
                        <label class="col-md-12 control-label" for="studentId">Student</label>
                        <div class="col-md-12">
                            <input type="text" class="form-control input-md required-input" required data-parsley-errors-container="#error-studentId" value="<?php echo ($fullName); ?>" name="fullName" id="studentId">

                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="soapnotedate">Date</label>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <div class='input-group date w-full' id='soapnotedate1' style="position: relative;">
                                <input type='text' name="soapnotedate" id="soapnotedate" placeholder="MM-DD-YYYY" class="form-control input-md required-input rotation_date" value="<?php echo ($soapNoteDate); ?>" data-parsley-errors-container="#error-txtDate" />
                                <span class="input-group-addon calender-icon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                            <div id="error-txtDate"></div>
                        </div>
                    </div>
                </div>

            </div>
            <div class="row mobile-margin mobile-mb-0">

                <div class="col-md-6">

                    <div class="form-group">
                        <label class="col-md-12 control-label mobile-hide" style="visibility: hidden;" for=""> </label>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <div class="btn-container">
                                <label>Soap Note Send To</label>
                                <label class="switch btn-color-mode-switch">
                                    <!-- &nbsp;&nbsp; -->

                                    <input type="checkbox" name="isSendToPreceptor" id="mode" value="1">
                                    <label for="mode" data-on="Preceptor" data-off="Clinician" class="btn-color-mode-switch-inner"></label>

                                </label>

                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mobile-mt-0">

                    <?php
                    if ($soapNoteId && $isSendToPreceptor) {
                    ?>
                        <div id="preceptorDetails" class="form-group">
                            <label class="col-md-12 control-label" for="">Preceptor</label>
                            <div class="col-md-12">
                                <input id="preceptorName" name="preceptorName" type="text" placeholder="" value="<?php echo $precptorName; ?>" class="form-control input-md">
                            </div>
                        </div>
                    <?php  } else { ?>
                        <div id="preceptor-mobile-number" class="form-group" style="display: none;">
                            <label class="col-md-12 control-label" for="">Preceptor Mobile Number</label>
                            <div class="col-md-12">
                                <input id="preceptorNo" maxlength="12" data-inputmask-alias="************" name="preceptorNo" maxlength="12" type="text" placeholder="___-___-____" class="form-control input-md required-input">
                            </div>
                            <span id="txtpreceptorNo" style="color: red; margin-left: 25px;"> </span>
                        </div>
                    <?php } ?>
                    <?php if (!$isSendToPreceptor) { ?>

                        <div class="form-group" id="clinician">
                            <label class="col-md-12 control-label" for="cboClinicalinstructor">Clinical Instructor</label>
                            <div class="col-md-12">
                                <select id="cboClinicalinstructor" name="cboClinicalinstructor" class="form-control select2_single required-input" data-parsley-errors-container="#error-cboClinicalinstructor">
                                    <option value="" selected>Select</option>
                                    <option value=""> </option>
                                </select>
                                <div id="error-cboClinicalinstructor"></div>
                            </div>
                        </div>
                    <?php } ?>

                </div>

            </div>

            <div class="row mobile-margin mobile-mb-0">

                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="studentDate">Student Signoff Date</label>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <div class='input-group date w-full' id='studentDate' style="position: relative;">

                                <input type='text' name="studentDate" id="studentDate" class="form-control input-md dateInputFormat" placeholder="MM/DD/YYYY" value="<?php echo ($studentSignatureDate);  ?>" <?php if (!$isSendToPreceptor || $view) echo 'readonly'; ?> data-parsley-errors-container="#error-studentsignitureDate" />
                                <span class="input-group-addon calender-icon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">

                    <div class="form-group">
                        <label class="col-md-12 control-label" for="ClinicianDate">Clinician/Preceptor Signoff Date</label>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <div class='input-group date w-full' id='ClinicianDate1' style="position: relative;">
                                <input type='text' name="ClinicianDate" id="ClinicianDate" class="form-control input-md required-input rotation_date" placeholder="MM/DD/YYYY" value="<?php echo ($clinicianSignatureDate); ?>" <?php //if (!$preceptorNum || $view) echo 'readonly'; 
                                                                                                                                                                                                                                ?> />
                                <span class="input-group-addon calender-icon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>

                            </div>
                            <div></div>
                        </div>

                    </div>
                </div>

            </div>

            <div class="row mobile-margin mobile-mb-0">
                <div class="col-md-12">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="Student Journal Entry">Patient Description</label>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <textarea name="patientDescription" id="patientDescription" class="form-control input-md mb-10" rows="2" cols="100"><?php echo ($patientDescription); ?></textarea>

                        </div>
                    </div>
                </div>
            </div>

            <div class="row mobile-margin mobile-mb-0">
                <div class="col-md-12">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="Student Journal Entry">Chief Complaint</label>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <textarea name="cheifComplaint" id="cheifComplaint" class="form-control input-md mb-10" rows="2" cols="100"><?php echo ($cheifComplaint); ?></textarea>

                        </div>
                    </div>
                </div>
            </div>

            <div class="row mobile-margin mobile-mb-0">
                <div class="col-md-12">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="Student Journal Entry">Admitting Diagnosis</label>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <textarea name="admittingDiagnosis" id="admittingDiagnosis" class="form-control input-md mb-10" rows="2" cols="100"><?php echo ($admittingDiagnosis); ?></textarea>

                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="instructions:"></label>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <div class="panel panel-default">
                                <div class="panel-body">
                                    <br>
                                    <h4>S – Subjective</h4>
                                    <p><b>Patient's statements, history, and symptoms in their own words</b></p>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="Student Journal Entry">History of Present Illness (HPI):</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input id="hpi" name="hpi" type="text" placeholder="" value="<?php echo ($hpi); ?>" class="form-control input-md">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="Student Journal Entry">Past Medical History (PMH):</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input id="pmh" name="pmh" type="text" placeholder="" value="<?php echo ($pmh); ?>" class="form-control input-md">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="Student Journal Entry">Medications:</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input id="medication" name="medication" type="text" placeholder="" value="<?php echo ($medication); ?>" class="form-control input-md">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="Student Journal Entry">Allergies:</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input id="allergies" name="allergies" type="text" placeholder="" value="<?php echo ($allergies); ?>" class="form-control input-md">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="Student Journal Entry">Family History:</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input id="familyHistory" name="familyHistory" type="text" placeholder="" value="<?php echo ($familyHistory); ?>" class="form-control input-md">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="Student Journal Entry">Social History (Smoking, Alcohol, Occupation, etc.):</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <textarea name="socialHistroy" id="socialHistroy" class="form-control input-md mb-10" rows="2" cols="100"><?php echo ($socialHistroy); ?></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="Student Journal Entry">Review of Systems (ROS):</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <textarea name="ros" id="ros" class="form-control input-md mb-10" rows="2" cols="100"><?php echo ($ros); ?></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <hr>
                                    <h4>O – Objective</h4>
                                    <p><b>Observations and measurements obtained during examination</b></p>
                                    <br>
                                    <h4>Vital Signs: </h4>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="Student Journal Entry">Temperature:</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input id="temperature" name="temperature" type="text" placeholder="" value="<?php echo ($temperature); ?>" class="form-control input-md">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="Student Journal Entry">Heart Rate:</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input id="heartRate" name="heartRate" type="text" placeholder="" value="<?php echo ($heartRate); ?>" class="form-control input-md">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="Student Journal Entry">Blood Pressure:</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input id="bloodPressure" name="bloodPressure" type="text" placeholder="" value="<?php echo ($bloodPressure); ?>" class="form-control input-md">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="Student Journal Entry">Respiratory Rate:</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input id="respiratoryRate" name="respiratoryRate" type="text" placeholder="" value="<?php echo ($respiratoryRate); ?>" class="form-control input-md">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="Student Journal Entry">Oxygen Saturation:</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input id="oxygenSaturation" name="oxygenSaturation" type="text" placeholder="" value="<?php echo ($oxygenSaturation); ?>" class="form-control input-md">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <br>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="Student Journal Entry">Physical Examination Findings:</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <textarea name="physicalExam" id="physicalExam" class="form-control input-md mb-10" rows="2" cols="100"><?php echo ($physicalExam); ?></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="Student Journal Entry">Laboratory & Imaging Results (if applicable): </label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <textarea name="laboratoryImaging" id="laboratoryImaging" class="form-control input-md mb-10" rows="2" cols="100"><?php echo ($laboratoryImaging); ?></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <hr>
                                    <h4>A – Assessment</h4>
                                    <p><b>Diagnosis and clinical impressions</b></p>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="form-group">

                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <textarea name="assessment" id="assessment" class="form-control input-md mb-10" rows="3" cols="100"><?php echo ($assessment); ?></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <hr>
                                    <h4>P – Plan</h4>
                                    <p><b>Treatment and management plan</b></p>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="Student Journal Entry">Medications and Treatments:</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input id="medicationTreatment" name="medicationTreatment" type="text" placeholder="" value="<?php echo ($medicationTreatment); ?>" class="form-control input-md">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="Student Journal Entry">Tests Ordered: </label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input id="testOrder" name="testOrder" type="text" placeholder="" value="<?php echo ($testOrder); ?>" class="form-control input-md">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="Student Journal Entry">Referrals: </label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input id="referrals" name="referrals" type="text" placeholder="" value="<?php echo ($referrals); ?>" class="form-control input-md">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="Student Journal Entry">Patient Education & Counseling: </label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <textarea name="patientEducationCounseling" id="patientEducationCounseling" class="form-control input-md mb-10" rows="2" cols="100"><?php echo ($patientEducationCounseling); ?></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="Student Journal Entry">Follow-up Instructions: </label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <textarea name="followUp" id="followUp" class="form-control input-md mb-10" rows="2" cols="100"><?php echo ($followUp); ?></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; justify-content: center; margin: 20px 0;">
                            <?php if ($view == 1) { ?>
                                <a href="soapNoteList.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>" class="btn btn-default">Cancel</a>
                                <?php
                            } else {
                                if ($clinicianId) { ?>
                                    <button id="btnSubmit" name="btnSubmit" class="btn btn-success" style="margin-right: 10px;">Signoff</button>
                                <?php }
                                ?>
                                <a href="soapNoteList.html?rotationId=<?php echo EncodeQueryData($getrotationId); ?>" class="btn btn-default">Cancel</a>
                            <?php
                            } ?>
                        </div>
                    </div>
                </div>
            </div>

        </form>

    </div>


    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/inputmask/jquery.inputmask.bundle.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/inputmask/inputmask.js"></script>

    <script>
        var currentSchoolId = '<?php echo $currentSchoolId; ?>';
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";
        $(window).load(function() {

            $('#formSoapNote')
                .parsley()
                .on('field:validated', function() {
                    var ok = $('.parsley-error').length === 0;
                })
                .on('form:submit', function() {
                    ShowProgressAnimation();
                    $('#formSoapNote input, #formSoapNote select, #formSoapNote textarea').prop('disabled', false);
                    return true; // Don't submit form for this demo

                    var isSendToPreceptor = $('#mode').is(':checked') ? 1 : 0;
                    $('<input>').attr({
                        // type: 'hidden',
                        name: 'isSendToPreceptor',
                        value: isSendToPreceptor
                    }).appendTo(this);
                    return true;

                });
            $(".select2_single").select2();
            $('#select2-cborotation-container').addClass('required-select2');
            $('#select2-cbohospitalsites-container').addClass('required-select2');
            $('#select2-cboclinician-container').addClass('required-select2');
            $('#select2-cboClinicalinstructor-container').addClass('required-select2');
            $('#select2-cbodrhospitalsiteunits-container').addClass('required-select2');


            var studentSignatureDate = '<?php echo $studentSignatureDate; ?>';
            var soapNoteId = "<?php echo $soapNoteId; ?>";
            var SoapNoteDate = '<?php echo $soapNoteDate; ?>';
            $('.studentDate').val(studentSignatureDate);
            if (SoapNoteDate) {

            }
            $('#soapnotedate').datetimepicker({
                format: 'MM/DD/YYYY',
                maxDate: soapNoteId > 0 ? false : moment() // maxDate is applied only if soapNoteId <= 0
            });

            $('#ClinicianDate').datetimepicker({
                format: 'MM/DD/YYYY',
                maxDate: 'now'
            });

            $('#studentDate').datetimepicker({
                format: 'MM/DD/YYYY',
                maxDate: 'now'
            });

            var soapNoteId = '<?php echo $soapNoteId; ?>';
            var rotationId = $('#cborotation').val();
            // console.log(rotationId);
        
            getclinician(rotationId, <?php echo $clinicianId; ?>);

            <?php
            if (isset($_GET['rotationId'])) {
                $rotationId = DecodeQueryData($_GET['rotationId']);
                echo "getHospital({$rotationId}, {$hospitalSiteId});";
            } else {
                echo "getHospital({$rotationId}, {$hospitalSiteId});";
            }
            ?>
        });

        function getclinician(val, selectedval) {
            var schoolId = '<?php echo $currentSchoolId; ?>';
            var clinicianId = '<?php echo $clinicianId; ?>';
            selectedval = selectedval == undefined ? 0 : selectedval;
            $.ajax({
                type: "POST",
                url: "<?php echo ($dynamicOrgUrl); ?>/clinician/get_clinician_rotation.html",
                data: 'Rotation_Id=' + val + "&schoolId=" + schoolId + "&clinicianId=" + selectedval,
                success: function(data) {
                    $("#cboClinicalinstructor").html(data);
                    // Ensure the selected value is set
                    if (selectedval > 0) {
                        $("#cboClinicalinstructor").val(selectedval).trigger('change');
                    }

                }
            });
        }

        function getHospital(val, selectedval) {
            selectedval = selectedval == undefined ? 0 : selectedval;
            $.ajax({
                type: "POST",
                url: "<?php echo ($dynamicOrgUrl); ?>/clinician/getAllhospitalForDailyEval.html",
                data: 'rotationId=' + val + "&HospitalSite_Id=" + selectedval,
                success: function(data) {
                    $("#cbohospitalsites").html(data);
                }
            });
        }

        $(document).ready(function() {
            var externalPreceptorFirstName = '<?php echo $externalPreceptorFirstName; ?>';
            var preceptorNum = '<?php echo $preceptorNum; ?>';
            var isRegister = '<?php echo $isRegister; ?>';
            var externalPreceptorId = '<?php echo $externalPreceptorId; ?>';
            var soapNoteId = '<?php echo $soapNoteId; ?>';
            var preceptorId = '<?php echo $preceptorId; ?>';

            var isSendToPreceptor = <?php echo $isSendToPreceptor ? 'true' : 'false'; ?>;
            $("#mode").prop("checked", isSendToPreceptor).trigger("change");

            if (soapNoteId > 0) {
                //Edit 
                // Set the mode checkbox
                $("#mode").prop("checked", <?php echo $isSendToPreceptor ? 'true' : 'false'; ?>).trigger("change");
                $("#mode").attr("disabled", true);

                // List of fields to disable in Edit mode
                const fieldsToDisable = [
                    "#cborotation", "#soapnotedate", "#studentId", "#cbohospitalsites",
                    "#patientDescription", "#cheifComplaint", "#admittingDiagnosis", "#hpi", "#pmh",
                    "#medication", "#allergies", "#familyHistory", "#socialHistroy", "#ros",
                    "#temperature", "#heartRate", "#bloodPressure", "#respiratoryRate", "#oxygenSaturation",
                    "#physicalExam", "#laboratoryImaging", "#assessment", "#medicationTreatment",
                    "#testOrder", "#referrals", "#patientEducationCounseling", "#followUp",
                    "#studentDate", "#cboClinicalinstructor"
                ];

                // Disable each field
                fieldsToDisable.forEach(function(selector) {
                    $(selector).attr("disabled", true);
                });
                $("#ClinicianDate").attr("disabled", false).attr("required", true);

            } else {
                //Add 
                $("#ClinicianDate").attr("disabled", true);
            }

            $('#cborotation').on('change', function() {

                var rotationId = $(this).val();
                var IsMobile = '<?php echo $IsMobile; ?>';
                // console.log(IsMobile);
                // console.log(rotationId);
                if (IsMobile) {
                    window.location.href = "addSoapNote.html?rotationId=" + btoa(rotationId) + "&IsMobile=" + (IsMobile);
                } else {
                    window.location.href = "addSoapNote.html?rotationId=" + btoa(rotationId);
                }

            });

            var view = '<?php echo $view; ?>';
            if (view == 1) {
                $('#formSoapNote input, #formSoapNote select, #formSoapNote textarea').prop('disabled', true);
                $(".sumTotal").attr("disabled", true);
            }

            $("#cborotation").change(function() {
                var rotationId = $(this).val();
                if (rotationId) {
                    window.location.href = "addSoapNote.html?rotationId=" + rotationId;
                }

            });
        });
    </script>
</body>

</html>