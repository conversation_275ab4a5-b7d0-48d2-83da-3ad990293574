<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsSchool.php');
include('../class/clsPEF.php');
include('../setRequest.php');

// print_r($_POST);exit;
if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit'])) {
	$isPEFType = isset($_POST['isPEFType']) ? ($_POST['isPEFType']) : 0;

	$sectionId = isset($_GET['editid']) ? DecodeQueryData($_GET['editid']) : 0;
	$status = ($sectionId > 0) ? 'updated' : 'added';

	$title = $_POST['txtsection'];
	$sortOrder = $_POST['txtsortorder'];

	//Save data
	$objPef = new clsPEF();
	$objPef->title = $title;
	$objPef->sortOrder = $sortOrder;
	$objPef->isPEFType = $isPEFType;

	$retsectionmasterId = $objPef->SavePefEvaluationSection($sectionId, $isPEFType);
	if ($retsectionmasterId > 0) {
		//Audit Log Start
		// Instantiate the Logger class
		$objLog = new clsLogger();

		// Determine the action type (EDIT or ADD) based on the presence of a journal ID
		$action = ($sectionId > 0) ? $objLog::EDIT : $objLog::ADD;
		$type = "section";
		$userType = $objLog::SUPERADMIN; 
		$objPef = new clsPEF();
		$objPef->savePEFEvalAuditLog(0,$retsectionmasterId, $_SESSION["loggedUserId"], $userType, $action, $IsMobile = 0, $type);

		unset($objPef);
		unset($objLog);
		//Audit Log End

		if ($isPEFType == 0) {
			$activeType = 'pef1';
		} else {
			$activeType = 'pef2';
		}

		header('location:pefEvaluationSectionList.html?active=' . $activeType . '&status=' . $status);
	} else {
		header('location:addPEFEvaluationSection.html?status=error');
	}
} else {
	header('location:pefEvaluationSectionList.html?status=' . $status);
	exit();
}
