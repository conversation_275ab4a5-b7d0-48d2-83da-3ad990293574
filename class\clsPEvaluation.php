<?php
class clsPEvaluation
{
    var $schoolId = '';
    var $clinicianId = '';
    var $studentId = '';
    var $rotationId = '';
    var $schoolClinicalSiteUnitId = '';
    var $evaluationDate = '';
    var $patientCareAdultAreaId = '';
    var $patientCarePediatricAreaId = '';
    var $patientCareNeonatalaAreaId = '';
    var $preceptorName = '';
    var $createdBy = '';
    var $pEvaluationMasterId = '';
    var $schoolPEvaluationQuestionId = '';
    var $schoolPEvaluationOptionValue = '';
    var $schoolPEvaluationOptionAnswerText = '';
    var $comment = '';
    var $questionText = '';
    var $schoolCIEvaluationQuestionType = '';
    var $sectionMasterId = '';
    var $title = '';
    var $sortOrder = '';
    function SaveEvaluation($pEvaluationMasterId)
    {
        $objDB = new clsDB();
        $sql = '';
        if ($pEvaluationMasterId > 0) {

            $sql = "UPDATE pevaluationmaster SET 						
						 clinicianId = '" . addslashes($this->clinicianId) . "',						
						 rotationId = '" . addslashes($this->rotationId) . "',
						 schoolClinicalSiteUnitId = '" . addslashes($this->schoolClinicalSiteUnitId) . "',
						 evaluationDate='" . addslashes($this->evaluationDate) . "',
						 patientCareAdultAreaId = '" . addslashes($this->patientCareAdultAreaId) . "',
						 patientCarePediatricAreaId = '" . addslashes($this->patientCarePediatricAreaId) . "',
						 patientCareNeonatalaAreaId = '" . addslashes($this->patientCareNeonatalaAreaId) . "',
						 preceptorName = '" . addslashes($this->preceptorName) . "',
						 updatedBy = '" . addslashes($this->createdBy) . "',
						 updatedDate = '" . (date("Y-m-d h:i:s")) . "' 
						 Where pEvaluationMasterId= " . $pEvaluationMasterId;
            //echo 'update->'.$sql;exit;
            $objDB->ExecuteQuery($sql);
        } else {
            $sql = "INSERT INTO pevaluationmaster (schoolId,clinicianId, studentId, rotationId,schoolClinicalSiteUnitId,
									evaluationDate,patientCareAdultAreaId,patientCarePediatricAreaId,patientCareNeonatalaAreaId,preceptorName,createdBy, createdDate) 
				VALUES ('" . addslashes($this->schoolId) . "',
						'" . addslashes($this->clinicianId) . "',
						'" . addslashes($this->studentId) . "',
						'" . addslashes($this->rotationId) . "',
						" . ($this->schoolClinicalSiteUnitId) . ",
						'" . addslashes($this->evaluationDate) . "',
						'" . addslashes($this->patientCareAdultAreaId) . "',
						'" . addslashes($this->patientCarePediatricAreaId) . "',
						'" . addslashes($this->patientCareNeonatalaAreaId) . "',
						'" . addslashes($this->preceptorName) . "',
						'" . addslashes($this->createdBy) . "',
						'" . (date("Y-m-d h:i:s")) . "'
						)";
            // echo 'Insert->'.$sql;exit;
            $pEvaluationMasterId = $objDB->ExecuteInsertQuery($sql);
        }

        unset($objDB);
        return $pEvaluationMasterId;
    }

    function DeletePevaluationDetails($pEvaluationMasterId)
    {
        $objDB = new clsDB();

        $sql = "DELETE  FROM pevaluationdetail WHERE pEvaluationMasterId=" . $pEvaluationMasterId;
        //ECHO 'DELETE->'.$sql;EXIT;
        $result = $objDB->ExecuteQuery($sql);
        unset($objDB);
        return $result;
    }

    function SaveEvaluationDetails()
    {
        $objDB = new clsDB();

        $sql = "INSERT INTO pevaluationdetail (pEvaluationMasterId,schoolPEvaluationQuestionId,
                        schoolPEvaluationOptionValue,schoolPEvaluationOptionAnswerText,comment) 
                VALUES ('" . ($this->pEvaluationMasterId) . "',
                        '" . ($this->schoolPEvaluationQuestionId) . "',
                        '" . ($this->schoolPEvaluationOptionValue) . "',
                        '" . addslashes($this->schoolPEvaluationOptionAnswerText) . "',
                        '" . addslashes($this->comment) . "'
                        
                    )";

        // echo 'INSERT->'.$sql;exit;
        $ciEvaluationDetaild = $objDB->ExecuteInsertQuery($sql);
        unset($objDB);
        return $ciEvaluationDetaild;
    }

    function SaveAdminevaluation($pEvaluationMasterId)
    {
        $objDB = new clsDB();
        $sql = '';
        if ($pEvaluationMasterId > 0) {

            $sql = "UPDATE pevaluationmaster SET 						
						 clinicianId = '" . addslashes($this->clinicianId) . "',	
						 schoolClinicalSiteUnitId = '" . addslashes($this->schoolClinicalSiteUnitId) . "',
						 evaluationDate='" . addslashes($this->evaluationDate) . "',						
						 updatedBy = '" . addslashes($this->createdBy) . "',
						 updatedDate = '" . (date("Y-m-d h:i:s")) . "' 
						 Where pEvaluationMasterId= " . $pEvaluationMasterId;
            //echo 'update->'.$sql;exit;
            $objDB->ExecuteQuery($sql);
        } else {
            $sql = "INSERT INTO pevaluationmaster (schoolId,clinicianId, studentId, rotationId,schoolClinicalSiteUnitId,
									evaluationDate,createdBy, createdDate) 
				VALUES ('" . addslashes($this->schoolId) . "',
						'" . addslashes($this->clinicianId) . "',
						'" . addslashes($this->studentId) . "',
						'" . addslashes($this->rotationId) . "',
						" . ($this->schoolClinicalSiteUnitId) . ",
						'" . addslashes($this->evaluationDate) . "',						
						'" . addslashes($this->createdBy) . "',
						'" . (date("Y-m-d h:i:s")) . "'						
						)";
            //echo 'Insert->'.$sql;exit;
            $pEvaluationMasterId = $objDB->ExecuteInsertQuery($sql);
        }

        unset($objDB);
        return $pEvaluationMasterId;
    }


    function GetAllEvaluation($rotationId, $currentstudentId, $canvasStatus = '', $schoolId = 0)
    {
        $objDB = new clsDB();
        $sql = "SELECT pevaluationmaster.*,
                hospitalsites.title as hospitalSiteTitle,
                pevaluationmaster.pEvaluationMasterId AS StudentPEvaluationMasterId,
                pevaluationdetail.*,student.studentId,
                rotation.rotationId,rotation.title,student.firstName,student.lastName,
                rotation.parentRotationId,rotation.locationId as rotationLocationId, courses.title AS Coursename,courses.locationId 
                FROM pevaluationmaster
                LEFT JOIN pevaluationdetail ON pevaluationmaster.pEvaluationMasterId=
                                                    pevaluationdetail.pEvaluationMasterId
                LEFT JOIN rotation ON pevaluationmaster.rotationId=rotation.rotationId
                INNER JOIN hospitalsites ON hospitalsites.hospitalSiteId=pevaluationmaster.schoolClinicalSiteUnitId
                LEFT JOIN courses ON rotation.courseId=courses.courseId
                LEFT JOIN student ON pevaluationmaster.studentId=student.studentId";
        $sql .= " WHERE pevaluationmaster.pEvaluationMasterId !=0 ";
        if ($rotationId > 0) {
            $sql .= " AND pevaluationmaster.rotationId=" . $rotationId;
        }
        if ($currentstudentId > 0) {
            $sql .= "  AND pevaluationmaster.studentId=" . $currentstudentId;
        }

        if ($canvasStatus != '')
            $sql .= " AND pevaluationmaster.isSendToCanvas=" . $canvasStatus . " AND pevaluationmaster.schoolId=" . $schoolId;

        $sql .= " GROUP BY pevaluationdetail.pEvaluationMasterId";
        // ECHO $sql;
        $row = $objDB->GetResultset($sql);
        unset($objDB);
        return $row;
    }

    function CopyAllPEvalQuestionMaster($currentSchoolId, $isActiveCheckoff)
    {
        $currentschoolSectionId = '';
        $this->schoolId = $currentSchoolId;
        $objDB = new clsDB();
        $savedQuestionIds = array();
        $savedSectionIds = array();

        $sql = "select defaultpsectionmaster.* from 
                            defaultpevaluationquestionmaster 
                            RIGHT JOIN defaultpsectionmaster ON  defaultpsectionmaster.sectionMasterId = defaultpevaluationquestionmaster.sectionMasterId
                            GROUP BY defaultpsectionmaster.sectionMasterId ";
        $rowsSectionMaster = $objDB->GetResultset($sql);
        //echo $count=mysqli_num_rows($rowsSectionMaster);exit;
        if ($rowsSectionMaster != "") {
            while ($sectionMaster = mysqli_fetch_array($rowsSectionMaster)) {
                $currentschoolQuestionId = array();
                //default assignment
                $currentschoolQuestionId[] = 0;
                $sectionId = $sectionMaster['sectionMasterId'];

                //Skipp
                if (array_key_exists($sectionId, $savedSectionIds)) {
                    $currentschoolSectionId = $savedSectionIds[$sectionId];
                } else {
                    $this->title = $sectionMaster['title'];
                    $this->sortOrder = $sectionMaster['sortOrder'];
                    $this->schoolId = $currentSchoolId;
                    $currentschoolSectionId = $this->SaveSchoolSectionMaster($sectionId);
                    $savedSectionIds[$sectionId] = $currentschoolSectionId;
                }
                $sql = "select defaultpevaluationquestionmaster.* from 
                                defaultpevaluationquestiondetail 
                                RIGHT JOIN defaultpevaluationquestionmaster ON  defaultpevaluationquestionmaster.defaultPEvaluationQuestionId
                                                                    =defaultpevaluationquestiondetail .defaultPEvaluationQuestionId
                                                                    WHERE defaultpevaluationquestionmaster.sectionMasterId=" . $sectionId;
                ;
                $rowsQuestionMaster = $objDB->GetResultset($sql);
                if ($rowsQuestionMaster != "") {
                    while ($row = mysqli_fetch_array($rowsQuestionMaster)) {
                        $masterQuestionId = $row['defaultPEvaluationQuestionId'];


                        // If already used then skipp
                        if (array_key_exists($masterQuestionId, $savedQuestionIds)) {
                            $currentschoolQuestionId[] = $savedQuestionIds[$masterQuestionId];
                            continue;
                        } else {
                            $this->questionText = $row['questionText'];
                            $this->schoolCIEvaluationQuestionType = $row['defaultPEvaluationQuestionType'];
                            $this->schoolPEvaluationQuestionId = $masterQuestionId;
                            $this->sectionMasterId = $currentschoolSectionId;
                            $schoolQuestionId = $this->CopyMasterQuestionToSchool($currentSchoolId, $masterQuestionId, $currentschoolSectionId);
                            //Bind in array
                            $savedQuestionIds[$masterQuestionId] = $schoolQuestionId;
                            $currentschoolQuestionId[] = $schoolQuestionId;
                            //-----------------------------------------------------
                            //Copy Question Choices
                            //-----------------------------------------------------
                            $this->CopyMasterQuestionChoicesToSchool($masterQuestionId, $schoolQuestionId);
                            //-----------------------------------------------------
                        }
                    } //while end


                } //if end
            } //1st while end
        } //1st if end

    }

    function SaveSchoolSectionMaster($schoolSectionId)
    {
        $objDB = new clsDB();
        $sql = "INSERT INTO schoolpevalsectionmaster (schoolId,title,sortOrder) 
                VALUES (
                        " . addslashes($this->schoolId) . ",
                        '" . addslashes($this->title) . "',
                        " . addslashes($this->sortOrder) . "							 
                    )";
        //echo 'section->'. $sql;exit;
        $schoolSectionId = $objDB->ExecuteInsertQuery($sql);
        $this->schoolSectionId = $schoolSectionId;
        unset($objDB);
        return $schoolSectionId;
    }

    function CopyMasterQuestionToSchool($schoolId, $questionId, $currentschoolSectionId)
    {
        $objDB = new clsDB();
        $sql = "INSERT INTO schoolpevaluationquestionmaster (questionText,schoolPEvaluationQuestionType,schoolId,sectionMasterId) 
        
        SELECT questionText,defaultPEvaluationQuestionType," . $schoolId . "," . $currentschoolSectionId . "  FROM defaultpevaluationquestionmaster
        WHERE defaultPEvaluationQuestionId=" . $questionId;
        $schoolQuestionId = $objDB->ExecuteInsertQuery($sql);
        unset($objDB);
        return $schoolQuestionId;
        ;
    }

    function CopyMasterQuestionChoicesToSchool($questionMasterId, $schoolQuestionId)
    {
        $objDB = new clsDB();
        $sql = "INSERT INTO schoolpevaluationquestiondetail (schoolPEvaluationQuestionId,optionText,schoolOptionValue) 
                SELECT " . $schoolQuestionId . ",optionText,defaultOptionValue
                FROM defaultpevaluationquestiondetail  WHERE defaultPEvaluationQuestionId=" . $questionMasterId;

        $schoolQuestionDId = $objDB->ExecuteInsertQuery($sql);
        unset($objDB);
    }

    function GetEvaluationScore($pEvaluationMasterId)
    {
        $objDB = new clsDB();
        $CIEvaluation = "";
        $sql = "SELECT
                AVG(schoolpevaluationquestiondetail.schoolOptionValue) AS EvaluationScore, 
                pevaluationdetail.pEvaluationDetaild,
                schoolpevaluationquestiondetail.schoolPEvaluationQuestionId 						
                    FROM pevaluationdetail
                    
                INNER JOIN pevaluationmaster ON pevaluationdetail.pEvaluationMasterId=
                                                pevaluationmaster.pEvaluationMasterId
                                                
                INNER JOIN schoolpevaluationquestiondetail  ON pevaluationdetail.schoolPEvaluationOptionValue=
                                            schoolpevaluationquestiondetail.schoolPEvaluationQuestionDetailId
                                            
                AND schoolpevaluationquestiondetail.schoolPEvaluationQuestionId=	
                                            pevaluationdetail.schoolPEvaluationQuestionId";
        $sql .= " WHERE  pevaluationmaster.pEvaluationMasterId=" . $pEvaluationMasterId;
        //echo $sql;
        $CIEvaluation = $objDB->GetDataRow($sql);
        return $CIEvaluation;
        unset($objDB);
    }

    function GetWhoPreceptorStoodOutToYouQuestion($pEvaluationMasterId)
    {
        $objDB = new clsDB();
        $CIEvaluation = "";

        $sql = "SELECT pevaluationdetail.schoolPEvaluationOptionAnswerText FROM `pevaluationdetail`
        INNER JOIN pevaluationmaster on pevaluationmaster.pEvaluationMasterId = pevaluationdetail.pEvaluationMasterId
        INNER JOIN schoolpevaluationquestionmaster on schoolpevaluationquestionmaster.schoolPEvaluationQuestionId = pevaluationdetail.schoolPEvaluationQuestionId
        WHERE pevaluationdetail.pEvaluationMasterId =" . $pEvaluationMasterId . " AND schoolpevaluationquestionmaster.questionText like '%Preceptor that stood out to you%'";

        // ECHO $sql;
        $CIEvaluation = $objDB->GetSingleFieldValue($sql);
        return $CIEvaluation;
        unset($objDB);
    }
    function GetWhoDidNotMeetYourExpectionQuestion($pEvaluationMasterId)
    {
        $objDB = new clsDB();
        $CIEvaluation = "";

        $sql = "SELECT pevaluationdetail.schoolPEvaluationOptionAnswerText FROM `pevaluationdetail`
        INNER JOIN pevaluationmaster on pevaluationmaster.pEvaluationMasterId = pevaluationdetail.pEvaluationMasterId
        INNER JOIN schoolpevaluationquestionmaster on schoolpevaluationquestionmaster.schoolPEvaluationQuestionId = pevaluationdetail.schoolPEvaluationQuestionId
        WHERE pevaluationdetail.pEvaluationMasterId =" . $pEvaluationMasterId . " AND schoolpevaluationquestionmaster.questionText like '%Preceptor who did not meet your expection%' ";

        $CIEvaluation = $objDB->GetSingleFieldValue($sql);
        return $CIEvaluation;
        unset($objDB);
    }

    function GetEvaluationDetails($pEvaluationMasterId)
    {

        $objDB = new clsDB();
        $rows = "";
        $sql = "SELECT pevaluationmaster.*,pevaluationdetail.*,rotation.parentRotationId,rotation.locationId as rotationLocationId, courses.title AS Coursename,courses.locationId
                FROM  pevaluationmaster 
                LEFT JOIN pevaluationdetail ON pevaluationdetail.pEvaluationMasterId= 
                pevaluationmaster.`PEvaluationMasterId`
                 LEFT JOIN rotation ON pevaluationmaster.rotationId=rotation.rotationId 
                LEFT JOIN courses ON rotation.courseId=courses.courseId
                WHERE pevaluationmaster.pEvaluationMasterId=" . $pEvaluationMasterId;
        //echo $sql;		
        $rows = $objDB->GetDataRow($sql);
        return $rows;
        unset($objDB);
    }

    // for Suepr Admin
    function GetAllDefaultSections()
    {
        $objDB = new clsDB();
        $rows = "";
        $sql = "SELECT * FROM defaultpsectionmaster";
        //echo $sql;		
        $rows = $objDB->GetResultset($sql);
        return $rows;
        unset($objDB);
    }

    function GetSections($currentSchoolId, $isActive = 0)
    {
        $objDB = new clsDB();
        $rows = "";
        $sql = "SELECT schoolpevalsectionmaster.* ,schools.schoolId
                FROM schoolpevalsectionmaster 
                LEFT JOIN schools ON schoolpevalsectionmaster.schoolId=schools.schoolId
                WHERE schoolpevalsectionmaster.schoolId=" . $currentSchoolId;
        if ($isActive > 0)
            $sql .= " and schoolpevalsectionmaster.isActive=1";

        $sql .= " ORDER BY schoolpevalsectionmaster.sortOrder ASC";
        // echo $sql;exit;
        $rows = $objDB->GetResultset($sql);
        return $rows;
        unset($objDB);
    }

    function GetPEvaluationQuestionCountbySections($currentSchoolId, $sectionMasterId)
    {
        $objDB = new clsDB();
        $rows = "";
        $sql = "SELECT count(sectionMasterId) as count
                                FROM schoolpevaluationquestionmaster 
                                WHERE schoolId=" . $currentSchoolId . " AND sectionMasterId=" . $sectionMasterId;
        //echo $sql;		
        $rows = $objDB->GetSingleFieldValue($sql);
        return $rows;
        unset($objDB);
    }

    // For Super Admin
    function GetDefaultPQuestionCountbySections($sectionMasterId)
    {
        $objDB = new clsDB();
        $rows = "";
        $sql = "SELECT count(sectionMasterId) as count
                                FROM defaultpevaluationquestionmaster 
                                WHERE  sectionMasterId=" . $sectionMasterId;
        //echo $sql;		
        $rows = $objDB->GetSingleFieldValue($sql);
        return $rows;
        unset($objDB);
    }

    function SetPEvaluationStatus($sectionMasterId, $status)
    {
        if ($sectionMasterId > 0) {
            $objDB = new clsDB();
            $sql = "Update schoolpevalsectionmaster set isActive = " . $status . " Where sectionMasterId = " . $sectionMasterId;
            // echo $sql;exit;
            $result = $objDB->ExecuteQuery($sql);
            unset($objDB);
        }
    }


    function DeletePEvaluationSection($sectionMasterId)
    {
        $objDB = new clsDB();

        $sql = "DELETE  FROM schoolpevalsectionmaster WHERE sectionMasterId=" . $sectionMasterId;
        //ECHO 'DELETE->'.$sql;EXIT;
        $result = $objDB->ExecuteQuery($sql);
        unset($objDB);
        return $result;
    }

    function DeleteDefaultPEvaluationSection($sectionMasterId)
    {
        $objDB = new clsDB();

        $sql = "DELETE  FROM defaultpsectionmaster WHERE sectionMasterId=" . $sectionMasterId;
        //ECHO 'DELETE->'.$sql;EXIT;
        $result = $objDB->ExecuteQuery($sql);
        unset($objDB);
        return $result;
    }

    function DeletePEvaluationQuestion($questionId)
	{
		$objDB = new clsDB();

		$sql = "DELETE  FROM schoolpevaluationquestionmaster WHERE schoolPEvaluationQuestionId=" . $questionId;
		//ECHO 'DELETE->'.$sql;EXIT;
		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}
	function DeleteDefaultPEvaluationQuestion($questionId)
	{
		$objDB = new clsDB();

		$sql = "DELETE  FROM defaultpevaluationquestionmaster WHERE defaultPEvaluationQuestionId=" . $questionId;
		//ECHO 'DELETE->'.$sql;EXIT;
		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}

    function GetPEvaluationSectionDetail($sectionMasterId)
    {

        $objDB = new clsDB();
        $rows = "";
        $sql = "SELECT * FROM  schoolpevalsectionmaster
                                WHERE sectionMasterId=" . $sectionMasterId;
        $rows = $objDB->GetDataRow($sql);
        return $rows;
        unset($objDB);
    }
    function GetDefaultPEvaluationSectionDetail($sectionMasterId)
    {

        $objDB = new clsDB();
        $rows = "";
        $sql = "SELECT * FROM  defaultpsectionmaster WHERE sectionMasterId=" . $sectionMasterId;
        $rows = $objDB->GetDataRow($sql);
        return $rows;
        unset($objDB);
    }

    function SavePevaluationSection($sectionId)
    {
        $objDB = new clsDB();
        $sql = '';
        if ($sectionId > 0) {

            $sql = "UPDATE schoolpevalsectionmaster SET 						
                                         title = '" . addslashes($this->title) . "',						
                                         sortOrder = '" . addslashes($this->sortOrder) . "'
                                         Where sectionMasterId= " . $sectionId;
            //echo 'update->'.$sql;exit;
            $objDB->ExecuteQuery($sql);
        } else {
            $sql = "INSERT INTO schoolpevalsectionmaster (schoolId,title,sortOrder) 
                                VALUES ('" . addslashes($this->schoolId) . "',
                                        '" . addslashes($this->title) . "',
                                        '" . addslashes($this->sortOrder) . "'						
                                        )";
            //echo 'Insert->'.$sql;exit;
            $sectionId = $objDB->ExecuteInsertQuery($sql);
        }

        unset($objDB);
        return $sectionId;
    }

    function SaveDefaultPevaluationSection($sectionId)
    {
        $objDB = new clsDB();
        $sql = '';
        if ($sectionId > 0) {

            $sql = "UPDATE defaultpsectionmaster SET 						
										title = '" . addslashes($this->title) . "',						
										sortOrder = '" . addslashes($this->sortOrder) . "'
										Where sectionMasterId= " . $sectionId;
            //echo 'update->'.$sql;exit;
            $objDB->ExecuteQuery($sql);
        } else {
            $sql = "INSERT INTO defaultpsectionmaster (title,sortOrder) 
                                VALUES ('" . addslashes($this->title) . "',
                                        '" . addslashes($this->sortOrder) . "'						
                                        )";
            //echo 'Insert->'.$sql;exit;
            $sectionId = $objDB->ExecuteInsertQuery($sql);
        }

        unset($objDB);
        return $sectionId;
    }

    function GetPatientCareAdultArea($currentSchoolId)
    {
        $objDB = new clsDB();
        $rows = "";
        $sql = "SELECT * FROM defaultpatientcareadultareas";
        //echo $sql;		
        $rows = $objDB->GetResultset($sql);
        return $rows;
        unset($objDB);
    }
    function GetPatientCarePediatricArea($currentSchoolId)
    {
        $objDB = new clsDB();
        $rows = "";
        $sql = "SELECT * FROM defaultpatientcarepediatricareas";
        //echo $sql;		
        $rows = $objDB->GetResultset($sql);
        return $rows;
        unset($objDB);
    }
    function GetPatientCareNeonatalArea($schoolId)
    {
        $objDB = new clsDB();
        $rows = "";
        $sql = "SELECT * FROM defaultpatientcareneonatalareas";
        //echo $sql;		
        $rows = $objDB->GetResultset($sql);
        return $rows;
        unset($objDB);
    }

    function GetAllEvaluationQuestionMaster($currentSchoolId, $sectionMasterId)
    {

        $objDB = new clsDB();
        $rows = "";
        $sql = "SELECT * FROM  schoolpevaluationquestionmaster
                WHERE schoolId=" . $currentSchoolId . " AND sectionMasterId=" . $sectionMasterId;
        $sql .= " ORDER BY sortOrder ASC";
        //echo $sql;		
        $rows = $objDB->GetResultset($sql);
        return $rows;
        unset($objDB);
    }

    function DeletePEvaluation($pEvaluationMasterId)
    {
        $objDB = new clsDB();
        $result = "";
        if ($pEvaluationMasterId > 0) {
            $objDB = new clsDB();
            $sql = "DELETE pevaluationmaster,pevaluationdetail FROM pevaluationmaster
                    INNER JOIN pevaluationdetail ON pevaluationmaster.pEvaluationMasterId=
                    pevaluationdetail.pEvaluationMasterId
                    WHERE pevaluationmaster.pEvaluationMasterId = " . $pEvaluationMasterId;
            // ECHO $sql;EXIT;
            $result = $objDB->ExecuteQuery($sql);
            unset($objDB);
        }
        return $result;
    }

    function GetAllPEvaluationQuestionToSetting($currentSchoolId, $sectionMasterId)
    {

        $objDB = new clsDB();
        $rows = "";
        $sql = "SELECT schoolpevaluationquestionmaster.*,questiontypemaster.title as questionType FROM  schoolpevaluationquestionmaster
                                LEFT JOIN questiontypemaster ON schoolpevaluationquestionmaster.schoolPEvaluationQuestionType=questiontypemaster.questionType
                                WHERE schoolId=" . $currentSchoolId . " AND schoolpevaluationquestionmaster.sectionMasterId=" . $sectionMasterId;
        // if($currentSchoolId == '118')
        $sql .= " ORDER BY sortOrder ASC";
        // echo $sql;	exit;	
        $rows = $objDB->GetResultset($sql);
        return $rows;
        unset($objDB);
    }

    function GetAllDefaultPEvaluationQuestionToSetting($sectionMasterId)
    {

        $objDB = new clsDB();
        $rows = "";
        $sql = "SELECT defaultpevaluationquestionmaster.*,questiontypemaster.title as questionType FROM  defaultpevaluationquestionmaster
                                LEFT JOIN questiontypemaster ON defaultpevaluationquestionmaster.defaultPEvaluationQuestionType=questiontypemaster.questionType
                                WHERE defaultpevaluationquestionmaster.sectionMasterId=" . $sectionMasterId;
        //echo $sql;		
        $rows = $objDB->GetResultset($sql);
        return $rows;
        unset($objDB);
    }


    function GetPEvaluationQuestionDetail($currentSchoolId, $questionId)
    {

        $objDB = new clsDB();
        $rows = "";
        $sql = "SELECT * FROM  schoolpevaluationquestionmaster
                                WHERE schoolId=" . $currentSchoolId . " AND schoolPEvaluationQuestionId=" . $questionId;
        //echo $sql;		
        $rows = $objDB->GetDataRow($sql);
        return $rows;
        unset($objDB);
    }

    function GetDefaultPEvaluationQuestionDetail($questionId)
    {

        $objDB = new clsDB();
        $rows = "";
        $sql = "SELECT * FROM  defaultpevaluationquestionmaster
                                WHERE defaultPEvaluationQuestionId=" . $questionId;
        //echo $sql;		
        $rows = $objDB->GetDataRow($sql);
        return $rows;
        unset($objDB);
    }

    function GetPevaluationQuestionOptionDetails($questionId)
    {

        $objDB = new clsDB();
        $rows = "";
        $sql = "SELECT schoolpevaluationquestionmaster. *,schoolpevaluationquestiondetail.*
						FROM schoolpevaluationquestionmaster 
						INNER JOIN schoolpevaluationquestiondetail ON schoolpevaluationquestionmaster.schoolPEvaluationQuestionId=
						schoolpevaluationquestiondetail.schoolPEvaluationQuestionId
						WHERE schoolpevaluationquestionmaster.schoolPEvaluationQuestionId=" . $questionId .
            " ORDER BY schoolpevaluationquestiondetail.schoolOptionValue";
        //echo $sql;exit;
        $rows = $objDB->GetResultset($sql);
        return $rows;
        unset($objDB);
    }
    function GetDefaultPevaluationQuestionOptionDetails($questionId)
    {

        $objDB = new clsDB();
        $rows = "";
        $sql = "SELECT defaultpevaluationquestionmaster.*,defaultpevaluationquestiondetail.*,defaultpevaluationquestiondetail.defaultOptionValue as schoolOptionValue
						FROM defaultpevaluationquestionmaster 
						INNER JOIN defaultpevaluationquestiondetail ON defaultpevaluationquestionmaster.defaultPEvaluationQuestionId=
						defaultpevaluationquestiondetail.defaultPEvaluationQuestionId
						WHERE defaultpevaluationquestionmaster.defaultPEvaluationQuestionId=" . $questionId .
            " ORDER BY defaultpevaluationquestiondetail.defaultOptionValue";
        //echo $sql;exit;
        $rows = $objDB->GetResultset($sql);
        return $rows;
        unset($objDB);
    }


    function SavePevaluationQuestion($questionId)
    {
        $objDB = new clsDB();
        $sql = '';
        if ($questionId > 0) {

            $sql = "UPDATE schoolpevaluationquestionmaster SET 						
						 questionText = '" . addslashes($this->questionText) . "',
						 schoolPEvaluationQuestionType = '" . addslashes($this->schoolPEvaluationQuestionType) . "',
						 isPosition = '" . addslashes($this->isPosition) . "',
						 sortOrder = '" . addslashes($this->sortOrder) . "'
						 Where schoolPEvaluationQuestionId= " . $questionId;
            //echo 'Insert->'.$sql;exit;
            $objDB->ExecuteQuery($sql);
        } else {
            $sql = "INSERT INTO schoolpevaluationquestionmaster (questionText,schoolId,schoolPEvaluationQuestionType,sectionMasterId,isPosition,sortOrder) 
				VALUES ('" . addslashes($this->questionText) . "',
						'" . addslashes($this->schoolId) . "',					
						'" . addslashes($this->schoolPEvaluationQuestionType) . "',					
						'" . addslashes($this->sectionMasterId) . "'	,			
						'" . addslashes($this->isPosition) . "',
						'" . addslashes($this->sortOrder) . "'
						)";
            // echo 'Insert->'.$sql;exit;
            $questionId = $objDB->ExecuteInsertQuery($sql);
        }

        unset($objDB);
        return $questionId;
    }
    function SaveDefaultPevaluationQuestion($questionId)
    {
        $objDB = new clsDB();
        $sql = '';
        if ($questionId > 0) {

            $sql = "UPDATE defaultpevaluationquestionmaster SET 						
						questionText = '" . addslashes($this->questionText) . "',
						defaultPEvaluationQuestionType = '" . addslashes($this->schoolPEvaluationQuestionType) . "',
						isPosition = '" . addslashes($this->isPosition) . "'

						Where defaultPEvaluationQuestionId= " . $questionId;

            $objDB->ExecuteQuery($sql);
        } else {
            $sql = "INSERT INTO defaultpevaluationquestionmaster (questionText,defaultPEvaluationQuestionType,sectionMasterId,isPosition) 
				VALUES ('" . addslashes($this->questionText) . "',				
						'" . addslashes($this->schoolPEvaluationQuestionType) . "',					
						'" . addslashes($this->sectionMasterId) . "'	,			
						'" . addslashes($this->isPosition) . "'			
						)";

            $questionId = $objDB->ExecuteInsertQuery($sql);
        }

        unset($objDB);
        return $questionId;
    }

    function DeletePEvaluationQuestionDetail($questionId)
    {
        $objDB = new clsDB();

        $sql = "DELETE  FROM schoolpevaluationquestiondetail WHERE schoolPEvaluationQuestionId=" . $questionId;
        //ECHO 'DELETE->'.$sql;EXIT;
        $result = $objDB->ExecuteQuery($sql);
        unset($objDB);
        return $result;
    }

    function DeleteDefaultPEvaluationQuestionDetail($questionId)
    {
        $objDB = new clsDB();

        $sql = "DELETE  FROM defaultpevaluationquestiondetail WHERE defaultPEvaluationQuestionId=" . $questionId;

        $result = $objDB->ExecuteQuery($sql);
        unset($objDB);
        return $result;
    }

    function SavePEvaluationQuestionOptions()
    {
        $objDB = new clsDB();
        $sql = '';
        $sql = "INSERT INTO schoolpevaluationquestiondetail(schoolPEvaluationQuestionId,optionText,schoolOptionValue) 
						VALUES ('" . addslashes($this->schoolPEvaluationQuestionId) . "',
								'" . addslashes($this->optionText) . "',					
								'" . addslashes($this->schoolOptionValue) . "'					
								)";
        ///echo 'Insert->'.$sql;exit;
        $objDB->ExecuteInsertQuery($sql);
        unset($objDB);
    }
    function SaveDefaultPEvaluationQuestionOptions()
    {
        $objDB = new clsDB();
        $sql = '';
        $sql = "INSERT INTO defaultpevaluationquestiondetail(defaultPEvaluationQuestionId,optionText,defaultOptionValue) 
						VALUES ('" . addslashes($this->schoolPEvaluationQuestionId) . "',
								'" . addslashes($this->optionText) . "',					
								'" . addslashes($this->schoolOptionValue) . "'					
								)";
        ///echo 'Insert->'.$sql;exit;
        $objDB->ExecuteInsertQuery($sql);
        unset($objDB);
    }

    function GetAllPEvaluationByClinician($schoolId, $clinicianId = 0)
    {
        $objDB = new clsDB();
        $sql = "SELECT pevaluationmaster.*,hospitalsites.title as hospitalSiteTitle,
                pevaluationmaster.pEvaluationMasterId AS StudentPEvaluationMasterId,
                pevaluationdetail.*,student.studentId,
                rotation.rotationId,rotation.title,student.firstName,student.lastName,
                rotation.parentRotationId,rotation.locationId as rotationLocationId,courses.locationId
                FROM pevaluationmaster
                LEFT JOIN pevaluationdetail ON pevaluationmaster.pEvaluationMasterId=
                                                    pevaluationdetail.pEvaluationMasterId
                INNER JOIN rotation ON pevaluationmaster.rotationId=rotation.rotationId
                INNER JOIN hospitalsites ON hospitalsites.hospitalSiteId=pevaluationmaster.schoolClinicalSiteUnitId
                INNER JOIN clinicianhospitalsite ON rotation.hospitalSiteId=
                                                    clinicianhospitalsite.hospitalSiteId
                INNER JOIN clinician ON pevaluationmaster.clinicianId=clinician.clinicianId
                LEFT JOIN student ON pevaluationmaster.studentId=student.studentId
                LEFT JOIN courses ON rotation.courseId=courses.courseId
                WHERE pevaluationmaster.schoolId=" . $schoolId;
        if ($clinicianId > 0) {
            $sql .= " AND pevaluationmaster.clinicianId=" . $clinicianId;
        }


        $sql .= "  GROUP BY pevaluationdetail.pEvaluationMasterId";
        $sql .= "  ORDER BY pevaluationmaster.evaluationDate DESC ";

        //ECHO $sql;
        $row = $objDB->GetResultset($sql);
        unset($objDB);
        return $row;
    }

    function GetStudentPevaluationDetailsForreport(
        $schoolId,
        $rotationId = 0,
        $studentId,
        $rankId = 0,
        $evaluator = '',
        $locationId = 0,
        $hospitalSiteId = 0,
        $startdate = '',
        $endtdate = '',
        $ascdesc,
        $sordorder,
        $cbosemester,
        $subcborotation
    ) {
        if ($evaluator != '')
            $evaluator = str_replace(" ", ",", $evaluator);

        if ($hospitalSiteId > 0)
            $hospitalSiteId = str_replace(" ", ",", $hospitalSiteId);

        $studentIds = $studentId ? implode(',', $studentId) : 0;

        $objDB = new clsDB();
        $rows = "";
        $cbosemester = str_replace(" ", ",", $cbosemester);
        $subcborotation = str_replace(" ", ",", $subcborotation);
        $sql = "SELECT pevaluationmaster.*,schools.schoolId, rotation.rotationId,hospitalsites.title as hospitalSiteTitle,
                rotation.title AS Rotationname,rankmaster.rankId,rankmaster.title AS Rankname,
                student.studentId,student.rankId,student.firstName,student.lastName
                FROM pevaluationmaster
                INNER JOIN schools ON pevaluationmaster.schoolId=schools.schoolId
                INNER JOIN hospitalsites ON hospitalsites.hospitalSiteId=pevaluationmaster.schoolClinicalSiteUnitId
                INNER JOIN rotation ON pevaluationmaster.rotationId=rotation.rotationId
                INNER JOIN courses ON courses.`courseId` = rotation.`courseId`
                INNER JOIN semestermaster ON courses.`semesterId` = semestermaster.`semesterId`
                -- INNER JOIN clinician ON pevaluationmaster.clinicianId=clinician.clinicianId
                INNER JOIN student ON pevaluationmaster.studentId=student.studentId
                INNER JOIN rankmaster ON student.rankId=rankmaster.rankId";
        $sql .= " WHERE schools.schoolId= " . $schoolId;

        if ($cbosemester > 0)
            $sql .= " AND semestermaster.semesterId IN ($cbosemester)";

        if ($studentIds > 0)
            $sql .= " AND student.studentId IN ($studentIds)";
        if ($rankId > 0)
            $sql .= " AND rankmaster.rankId=" . $rankId;
        if ($startdate > 0 || $endtdate > 0)
            $sql .= " AND pevaluationmaster.createdDate >= '" . $startdate . "'
                        AND pevaluationmaster.createdDate <= '" . $endtdate . "' ";
        if ($evaluator)
            $sql .= " AND pevaluationmaster.clinicianId IN ($evaluator) ";

        if ($hospitalSiteId)
            $sql .= " AND pevaluationmaster.schoolClinicalSiteUnitId IN ($hospitalSiteId) ";


        if ($subcborotation > 0)
            $sql .= " AND (rotation.rotationId IN ($subcborotation) OR rotation.rotationId=" . $rotationId . " )";
        else if ($rotationId > 0)
            $sql .= " AND rotation.rotationId=" . $rotationId;

        if ($ascdesc && $sordorder == 1)
            $sql .= "  ORDER BY student.firstName " . $ascdesc;
        elseif ($ascdesc && $sordorder == 2)
            $sql .= "  ORDER BY rankmaster.title " . $ascdesc;
        elseif ($ascdesc && $sordorder == 10)
            $sql .= "  ORDER BY rotation.`title` " . $ascdesc;
        elseif ($ascdesc && $sordorder == 7)
            $sql .= "  ORDER BY clinicianfname,clinicianlname " . $ascdesc;
        else if ($ascdesc && $sordorder == 14)
            $sql .= "  ORDER BY student.firstName " . $ascdesc;
        else if ($ascdesc && $sordorder == 15)
            $sql .= "  ORDER BY student.lastName " . $ascdesc;

        // echo $sql;exit;									
        $rows = $objDB->GetResultset($sql);
        unset($objDB);
        return $rows;
    }

    function GetAllPEvaluationEvalForApp($rotationId = 0, $studentId = 0, $courseId, $generateLimitString = '', $searchText = '')
    {
        $objDB = new clsDB();
        $sql = "SELECT pevaluationmaster.*,
                hospitalsites.title as hospitalSiteTitle,
                pevaluationmaster.pEvaluationMasterId AS StudentPEvaluationMasterId,
                pevaluationdetail.*,student.studentId,
                rotation.rotationId,rotation.title,student.firstName,student.lastName,
                rotation.parentRotationId,rotation.locationId as rotationLocationId, courses.title AS Coursename,courses.locationId ,
                hospitalsites.hospitalSiteId, hospitalsites.title AS Hospitalsite
                FROM pevaluationmaster
                LEFT JOIN pevaluationdetail ON pevaluationmaster.pEvaluationMasterId=pevaluationdetail.pEvaluationMasterId
                LEFT JOIN rotation ON pevaluationmaster.rotationId=rotation.rotationId
                INNER JOIN hospitalsites ON hospitalsites.hospitalSiteId=pevaluationmaster.schoolClinicalSiteUnitId
                LEFT JOIN courses ON rotation.courseId=courses.courseId
                LEFT JOIN student ON pevaluationmaster.studentId=student.studentId";
        $sql .= " WHERE pevaluationmaster.pEvaluationMasterId !=0 ";

        if ($rotationId > 0) {
            $sql .= "  AND pevaluationmaster.rotationId=" . $rotationId;
        }
        if ($studentId > 0) {
            $sql .= "  AND pevaluationmaster.studentId=" . $studentId;
        }
        if ($courseId > 0) {
            $sql .= "  AND rotation.courseId=" . $courseId;
        }
        if ($searchText != "") {
            $sql .= " AND (hospitalsites.title LIKE '%" . $searchText . "%' )";
        }
        $sql .= " GROUP BY pevaluationmaster.pEvaluationMasterId";
        $sql .= " ORDER BY `pevaluationmaster`.`evaluationDate` DESC" . $generateLimitString;

        // echo $sql;
        $row = $objDB->GetResultset($sql);
        unset($objDB);
        return $row;
    }
    function savePEvalAuditLog($pEvalMasterId, $retPEvalId, $clinicianId, $userType, $action, $isMobile = 0, $type = '', $isSuperAdmin = 0)
    {
        // Instantiate the Logger and P Eval classes
        $objLog = new clsLogger();
        $objPevaluation = new clsPEvaluation();

        // Prepare log data
        [$logData, $rowData, $additionalData] = $objPevaluation->createPEvalLog($retPEvalId, $action, $clinicianId, $userType, $type, $isSuperAdmin);
        $logData['isMobile'] = $isMobile;

        if ($action == 'Delete') {

            if ($type == 'section') {
                if ($isSuperAdmin) {
                    $objPevaluation->DeleteDefaultPEvaluationSection($retPEvalId);
                } else {
                    $objPevaluation->DeletePEvaluationSection($retPEvalId);
                }
            } else if ($type == 'step') {

                if ($isSuperAdmin) {
                    // Initialize database object
                    $objDB = new clsDB();

                    // Fetch data from `siteevaluationdetail` table for the given master ID
                    $evaluationDetailsResult = $objDB->GetAllSelectedDataFromTable('defaultpevaluationquestiondetail', '', 'defaultPEvaluationQuestionId', $retPEvalId);
                    unset($objDB);

                    if ($evaluationDetailsResult) {
                        // Convert the result set into an array
                        $evaluationDetailsArray = fetchResultAsArray($evaluationDetailsResult);

                        // Generate the JSON array (if needed for logs or other purposes)
                        $additionalData = $evaluationDetailsArray;

                        $objPevaluation->DeleteDefaultPEvaluationQuestion($retPEvalId);

                    }
                } else {
                    // Initialize database object
                    $objDB = new clsDB();

                    // Fetch data from `siteevaluationdetail` table for the given master ID
                    $evaluationDetailsResult = $objDB->GetAllSelectedDataFromTable('schoolpevaluationquestiondetail', '', 'schoolPEvaluationQuestionId', $retPEvalId);
                    unset($objDB);

                    if ($evaluationDetailsResult) {
                        // Convert the result set into an array
                        $evaluationDetailsArray = fetchResultAsArray($evaluationDetailsResult);

                        // Generate the JSON array (if needed for logs or other purposes)
                        $additionalData = $evaluationDetailsArray;
                        // print_r($additionalData);exit;

                        $objPevaluation->DeletePEvaluationQuestion($retPEvalId); 

                    }
                }


            } else {

                // Initialize database object
                $objDB = new clsDB();

                // Fetch data from `pevaluationdetail` table for the given master ID
                $evaluationDetailsResult = $objDB->GetAllSelectedDataFromTable('pevaluationdetail', '', 'pEvaluationMasterId', $retPEvalId);
                unset($objDB);

                if ($evaluationDetailsResult) {
                    // Convert the result set into an array
                    $evaluationDetailsArray = fetchResultAsArray($evaluationDetailsResult);

                    // Generate the JSON array (if needed for logs or other purposes)
                    $additionalData = $evaluationDetailsArray;

                    $objPevaluation->DeletePEvaluation($retPEvalId);
                }
            }
        }
        // Save the log details
        $objLog->saveLogs($logData, $action, $retPEvalId, 'P Evaluation', $rowData, $additionalData);

        // Clean up
        unset($objLog);
        unset($objPevaluation);

        return true; // Return success or handle further actions as needed
    }

    function createPEvalLog($pEvalMasterId, $action, $userId, $userType, $type, $isSuperAdmin)
    {
        // Instantiate the Logger class
        $objLog = new clsLogger();
        $objPevaluation = new clsPEvaluation(); // Assuming `P Evaluation` class is used for `preparePevalLogData`

        // Get user details based on user type
        $userDetails = getUserDetails($userId, $userType);

        // Retrieve  Site Evaluation details for the given siteeval ID and school ID
        if ($type == 'section') {
            $rowData = $objPevaluation->GetPSectionDetailsForlog($pEvalMasterId, $isSuperAdmin);
        } else if ($type == 'step') {
            $rowData = $objPevaluation->GetPEvalStepDetailsForLogs($pEvalMasterId, $isSuperAdmin);
        } else {

            // Retrieve P Evaluation details for the given peval ID and school ID
            $rowData = $objPevaluation->GetAllPEvalDetailsForLogs($pEvalMasterId);
        }


        // echo '<pre>';
        // print_r($rowData);
        // exit;
        if ($rowData != '')
            $logData = $objLog->generateLogData($rowData, $userType);

        $additionalData = '';

        // Populate log data with user details
        $logData['userId'] = $userDetails['userId'];
        $logData['userName'] = $userDetails['userName'];
        $logMessage = '';

        if ($type == 'section') {
            if ($action == 'Add') {
                $logMessage = $logData['userName'] . ' added new section in P Evaluation.';
            } else if ($action == 'Edit') {
                $logMessage = $logData['userName'] . ' updated the Section from P Evaluation.';
            } else if ($action == 'Delete') {
                $logMessage = $logData['userName'] . ' deleted the section from P Evaluation.';
            } else if ($action == 'Active') {
                $logMessage = $logData['userName'] . ' activate the section from P Evaluation.';
            } else if ($action == 'Inactive') {
                $logMessage = $logData['userName'] . ' deactivate the section from P Evaluation.';
            }
        } else if ($type == "step") {

            if ($action == 'Add') {
                $logMessage = $logData['userName'] . ' added new Step in P Evaluation Section.';
            } else if ($action == 'Edit') {
                $logMessage = $logData['userName'] . ' updated Step from P Evaluation Section.';
            } else if ($action == 'Delete') {
                $logMessage = $logData['userName'] . ' deleted Step from P Evaluation Section.';
            }
        } else {

            if ($action == 'Add') {
                $logMessage = $logData['userName'] . ' added P Evaluation for ' . $logData['rotationName'] . ' rotation.';
            } else if ($action == 'Edit') {
                $logMessage = $logData['userName'] . ' updated P Evaluation from ' . $logData['rotationName'] . ' rotation.';
            } else if ($action == 'Delete') {
                $logMessage = $logData['userName'] . ' deleted P Evaluation from ' . $logData['rotationName'] . ' rotation.';
            }
        }

        // Construct log message
        $logData['message'] = $logMessage;

        // Return the data
        return [$logData, $rowData, $additionalData];
    }



    function GetAllPEvalDetailsForLogs($pEvalMasterId, $schoolId = 0)
    {
        $objDB = new clsDB();

        $sql = "SELECT pevaluationmaster.*, rotation.title as rotationName,pevaluationmaster.studentId as userId,
                CONCAT(student.firstName, ' ', student.lastName) AS userName,hospitalsites.title AS hospitalSiteName, 
                schools.displayName as schoolName
		FROM `pevaluationmaster` 
		INNER JOIN rotation ON pevaluationmaster.`rotationId` = rotation.`rotationId` 
		INNER JOIN student ON student.studentId=pevaluationmaster.studentId
		LEFT JOIN hospitalsites ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
		INNER JOIN schools ON schools.schoolId = pevaluationmaster.schoolId
		WHERE pEvaluationmasterId =" . $pEvalMasterId;

        if ($schoolId) {
            $sql .= " AND rotation.schoolId=" . $schoolId;
        }
        // echo $sql;
        // exit;
        $row = $objDB->GetDataRow($sql);
        unset($objDB);
        return $row;
    }

    // P Evaluation Section for Audit Log
    function GetPSectionDetailsForlog($sectionMasterId, $isSuperAdmin = 0)
    {

        $objDB = new clsDB();
        $rows = "";
        if ($isSuperAdmin) {
            $sql = "SELECT * FROM  defaultpsectionmaster WHERE sectionMasterId=" . $sectionMasterId;
        } else {
            $sql = "SELECT schoolpevalsectionmaster.*,schools.schoolId,schools.displayName as schoolName FROM  schoolpevalsectionmaster
				INNER JOIN schools ON schoolpevalsectionmaster.schoolId=schools.schoolId
				WHERE sectionMasterId=" . $sectionMasterId;
        }
        // echo $sql;exit;s
        $rows = $objDB->GetDataRow($sql);
        return $rows;
        unset($objDB);
    }

    // P Evaulation Steps For Audit Log
    function GetPEvalStepDetailsForLogs($questionId, $isSuperAdmin = 0)
    {

        $objDB = new clsDB();
        $rows = "";
        if ($isSuperAdmin) {
            $sql = "SELECT * FROM  defaultpevaluationquestionmaster
					WHERE defaultPEvaluationQuestionId=" . $questionId;
        } else {
            $sql = "SELECT schoolpevaluationquestionmaster. *,schoolpevaluationquestiondetail.*,schools.displayName as schoolName
					FROM schoolpevaluationquestionmaster 
					INNER JOIN schools ON schools.schoolId = schoolpevaluationquestionmaster.schoolId
					LEFT JOIN schoolpevaluationquestiondetail ON schoolpevaluationquestionmaster.schoolPEvaluationQuestionId=
					schoolpevaluationquestiondetail.schoolPEvaluationQuestionId
					WHERE schoolpevaluationquestionmaster.schoolPEvaluationQuestionId=" . $questionId;

                    
        }
        // echo $sql;exit; 
        $rows = $objDB->GetDataRow($sql);
        return $rows;
        unset($objDB);
    }
}
