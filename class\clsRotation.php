<?php
class clsRotation
{
	var $rotationId = 0;
	var $parentRotationId = 0;
	var $schoolId = 0;
	var $studentId = '';
	var $title = '';
	var $hospitalSiteId = '';
	var $courseId = '';
	var $startDate = '';
	var $endDate = '';
	var $isActive = 1;
	var $createdBy = '';
	var $createdDate = '';
	var $updatedBy = '';
	var $updatedDate = '';
	var $duration = '';
	var $locationId = '';
	var $isSchedule = '';
	var $dayOfWeek = 0;
	var $rotationScheduleDate = '';


	function SaveRotation($rotationId, $parentRotationId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($rotationId > 0) {
			if ($parentRotationId > 0) {
				$sql = "UPDATE rotation SET " . "		
								
								schoolId = " . ($this->schoolId) . ",								
								title = '" . addslashes($this->title) . "',								
								startDate = '" . addslashes($this->startDate) . "',
								endDate = '" . addslashes($this->endDate) . "',					
								hospitalSiteId = '" . addslashes($this->hospitalSiteId) . "',					
								updatedBy = '" . addslashes($this->createdBy) . "',
								duration = '" . addslashes($this->duration) . "',
								locationId = '" . addslashes($this->locationId) . "',
								updatedDate = '" . (date("Y-m-d h:i:s")) . "'								
								Where rotationId= " . $rotationId;
				// echo 'UPDATE1->' . $sql;
				// exit;
				$objDB->ExecuteQuery($sql);
			} else {
				$sql = "UPDATE rotation SET " . "								
								parentRotationId = " . ($this->parentRotationId) . ",
								schoolId = " . ($this->schoolId) . ",								
								title = '" . addslashes($this->title) . "',								
								courseId = '" . addslashes($this->courseId) . "',
								startDate = '" . ($this->startDate) . "',
								endDate = '" . ($this->endDate) . "',					
								hospitalSiteId = '" . addslashes($this->hospitalSiteId) . "',					
								updatedBy = '" . addslashes($this->createdBy) . "',
								duration = '" . addslashes($this->duration) . "',
								locationId = '" . addslashes($this->locationId) . "',
								updatedDate = '" . (date("Y-m-d h:i:s")) . "'								
								Where rotationId= " . $rotationId;
				// echo 'UPDATE2->' . $sql;
				// exit;
				$objDB->ExecuteQuery($sql);
			}
		} else {
			if ($parentRotationId > 0) {
				$sql = "Select courseId from rotation where rotationId=" . $parentRotationId;
				$rows = $objDB->GetDataRow($sql);
				//$courseId=$rows['courseId'];
				$courseId = $this->courseId = $rows['courseId'];
				$sql = "INSERT INTO rotation (parentRotationId, schoolId,  title, 
								 courseId, startDate, endDate,hospitalSiteId, createdBy,duration,locationId,createdDate,isSchedule
								 ) VALUES (
								 '" . addslashes($this->parentRotationId) . "', 
								 '" . addslashes($this->schoolId) . "',
								 '" . addslashes($this->title) . "',
								 '" . addslashes($courseId) . "', 
								 '" . addslashes($this->startDate) . "',
								 '" . addslashes($this->endDate) . "',
								 '" . addslashes($this->hospitalSiteId) . "',
								 '" . addslashes($this->createdBy) . "',
								 '" . addslashes($this->duration) . "',
								 '" . addslashes($this->locationId) . "',
								 '" . (date("Y-m-d h:i:s")) . "',								 
								 '" . addslashes($this->isSchedule) . "'
								 )";
				// echo 'INSERT1->' . $sql;
				// exit;
				$rotationId = $objDB->ExecuteInsertQuery($sql);
			} else {
				$sql = "INSERT INTO rotation (parentRotationId, schoolId,  title, 
								 courseId, startDate, endDate,hospitalSiteId, createdBy,duration,locationId,createdDate,isSchedule
								 ) VALUES (
								 '" . addslashes($this->parentRotationId) . "', 
								 '" . addslashes($this->schoolId) . "',
								 '" . addslashes($this->title) . "',
								 '" . addslashes($this->courseId) . "', 
								 '" . addslashes($this->startDate) . "',
								 '" . addslashes($this->endDate) . "',
								 '" . addslashes($this->hospitalSiteId) . "',
								 '" . addslashes($this->createdBy) . "',
								 '" . addslashes($this->duration) . "',
								 '" . addslashes($this->locationId) . "',
								 '" . (date("Y-m-d h:i:s")) . "',
								 '" . addslashes($this->isSchedule) . "'
								 )";

				// echo 'INSERT->' . $sql;
				// exit;
				// echo $sql;exit;
				$rotationId = $objDB->ExecuteInsertQuery($sql);
				//echo $sql;exit;

			}
		}

		unset($objDB);
		return $rotationId;
	}



	function DeleteRotationRepeatDays($rotationId, $day = '')
	{
		$objDB = new clsDB();
		$sql = "DELETE  FROM rotationrepeatdays 
					WHERE rotationId=" . $rotationId;
		if ($day != '')
			$sql .= " AND rotationrepeatdays.dayOfWeek=" . $day;
		//ECHO 'DELETE->'.$sql;EXIT;
		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}

	function GetRotationRepeatDaysDetails($rotationId, $schoolId)
	{
		$sql = "SELECT rotationrepeatdays.*
				FROM  rotationrepeatdays 
				INNER JOIN rotation ON rotationrepeatdays.rotationId=rotation.rotationId
				WHERE rotationrepeatdays.rotationId=" . $rotationId . " AND rotation.schoolId =" . $schoolId;
		// ECHO $sql;
		$objDB = new clsDB();
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}

	function GetRotationRepeatDaysBYRotation($rotationId)
	{
		$objDB = new clsDB();
		$sql = "SELECT GROUP_CONCAT(dayOfWeek)
				FROM  rotationrepeatdays WHERE rotationId=" . $rotationId;

		// echo $sql; 
		$row = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $row;
	}

	function GetRotationRepeatDays($rotationId)
	{
		$sql = "SELECT GROUP_CONCAT( rotationrepeatdaysmaster.title )
				FROM rotationrepeatdays
				INNER JOIN rotation ON rotationrepeatdays.rotationId = rotation.rotationId
				INNER JOIN rotationrepeatdaysmaster ON rotationrepeatdaysmaster.value = rotationrepeatdays.dayOfWeek
				WHERE rotationrepeatdays.rotationId =" . $rotationId;
		//ECHO $sql;
		$objDB = new clsDB();
		$row = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $row;
	}
	function Deleterotation($rotationId, $deletedUserId, $title)
	{
		$result = "";

		$word = " Deleted";
		if (strpos($title, $word) !== false) {
			$originalTitle = $title;
		} else {
			$originalTitle = $title . " Deleted";
		}

		if ($rotationId > 0) {
			$objDB = new clsDB();
			$sqlsub = "UPDATE rotation SET " . " isDelete=1, deleteDate =  '" . (date("Y-m-d h:i:s")) . "', deletedUserId = " . $deletedUserId . ", title = '" . addslashes($originalTitle) . "'
						   WHERE  rotationId = " . $rotationId;
			// $sqlsub = "DELETE rotation.*, rotationdetails.*,rotationrepeatdays.* 						
			// FROM rotation							
			// LEFT JOIN rotationdetails ON rotation.rotationId=rotationdetails.rotationId
			// LEFT JOIN rotationrepeatdays ON rotation.rotationId=rotationrepeatdays.rotationId
			// WHERE rotation.parentRotationId = ".$rotationId;				
			// echo $sqlsub;exit;
			$SubRotationId = $objDB->ExecuteQuery($sqlsub);
			$sql = "DELETE FROM rotationdetails	WHERE rotationId = " . $rotationId;
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
		return $result;
	}
	function GetAllrotation($schoolId, $locationId = 0, $courseId = 0, $rotationId, $from_date = '', $to_date = '') //A: admin / C:Clinician
	{
		$objDB = new clsDB();
		$rows = "";

		$sql = " SELECT rotation.*, courses.courseId, courses.title as courseTitle,
		location.locationId, location.title as locationTitle, IFNULL(count(rotationdetails.rotationId),0) as studentCount,
		(select count(*) from rotation SUBR WHERE SUBR.parentRotationId= rotation.rotationId) AS subratoncount,
		hospitalsites.title as hospitalSite
		FROM rotation
		LEFT JOIN courses ON rotation.courseId = courses.courseId
		LEFT JOIN hospitalsites  ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
		LEFT JOIN location ON courses.locationId = location.locationId
		LEFT JOIN rotationdetails ON rotationdetails.rotationId = rotation.rotationId ";

		$sql .= " WHERE rotation.schoolId=" . $schoolId . " AND rotation.parentRotationId= 0";
		if ($locationId > 0) //Clinician
		{
			$sql .= " AND courses.locationId=" . $locationId;
		}
		if ($courseId > 0) //course filter
		{
			$sql .= " AND rotation.courseId=" . $courseId;
		}

		if ($to_date != '' && $from_date != '') {
			$sql .= " AND (date(rotation.startDate) >= '" . $from_date . "' AND date(rotation.endDate) <= '" . $to_date . "')";
		}

		$sql .= " GROUP BY rotation.rotationId ORDER BY rotation.title";
		//	echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);

		return $rows;
	}

	function GetAllrotationByCI($schoolId, $clinicianId) //A: admin / C:Clinician
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = " SELECT rotation.*, courses.courseId, courses.title as courseTitle,
		location.locationId, location.title as locationTitle, IFNULL(count(rotationdetails.rotationId),0) as studentCount,
		(select count(*) from rotation SUBR WHERE SUBR.parentRotationId= rotation.rotationId) AS subratoncount
		FROM rotation
		LEFT JOIN courses ON rotation.courseId = courses.courseId
		LEFT JOIN location ON courses.locationId = location.locationId
		LEFT JOIN rotationdetails ON rotationdetails.rotationId = rotation.rotationId 
		INNER JOIN clinicianhospitalsite ON rotation.hospitalSiteId=clinicianhospitalsite.hospitalSiteId
		INNER JOIN clinician ON clinicianhospitalsite.clinicianId=clinician.clinicianId
		";

		$sql .= " WHERE rotation.schoolId=" . $schoolId . " AND rotation.parentRotationId= 0";
		$sql .= " AND clinician.clinicianId=" . $clinicianId;
		$sql .= " GROUP BY rotation.rotationId ORDER BY rotation.title";
		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllrotationByClinician($schoolId, $clinicianId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = " SELECT rotation.*, courses.courseId, courses.title as courseTitle, hospitalsites.title as hospitalTitle, hospitalsites.hospitalSiteId,
		location.locationId, location.title as locationTitle, IFNULL(count(rotationdetails.rotationId),0) as studentCount,
		(select count(*) from rotation SUBR WHERE SUBR.parentRotationId= rotation.rotationId) AS subratoncount,CONCAT(rotation.title, ' (', hospitalsites.title, ')') as title
		FROM rotation
		LEFT JOIN courses ON rotation.courseId = courses.courseId
		LEFT JOIN location ON courses.locationId = location.locationId
		LEFT JOIN rotationdetails ON rotationdetails.rotationId = rotation.rotationId 
		INNER JOIN clinicianhospitalsite ON rotation.hospitalSiteId=clinicianhospitalsite.hospitalSiteId
		INNER JOIN clinician ON clinicianhospitalsite.clinicianId=clinician.clinicianId
		LEFT JOIN hospitalsites ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
		";

		$sql .= " WHERE rotation.schoolId=" . $schoolId;
		if ($clinicianId > 0) {
			$sql .= " AND clinician.clinicianId=" . $clinicianId;
		}
		$sql .= " AND rotation.isDelete =0 AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId) ";
		// $sql .= " 
		// 	AND (
		// 				  (CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
		// 				  OR
		// 				  (rotation.parentRotationId > 0
		// 				   AND EXISTS (
		// 					   SELECT 1
		// 					   FROM rotation AS parent
		// 					   WHERE parent.rotationId = rotation.parentRotationId
		// 					   AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
		// 					   AND parent.schoolId = " . $schoolId . " AND rotation.isSchedule = 1 
		// 				   )
		// 				  )
		// 			  )";
		$sql .= " GROUP BY rotation.rotationId ORDER BY rotation.title";
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetCurrentrotationByClinician($schoolId, $clinicianId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = " SELECT rotation.*, courses.courseId, courses.title as courseTitle,
		location.locationId, location.title as locationTitle, IFNULL(count(rotationdetails.rotationId),0) as studentCount,
		(select count(*) from rotation SUBR WHERE SUBR.parentRotationId= rotation.rotationId) AS subratoncount,CONCAT(rotation.title, ' (', hospitalsites.title, ')') as title
		FROM rotation
		LEFT JOIN courses ON rotation.courseId = courses.courseId
		LEFT JOIN location ON courses.locationId = location.locationId
		LEFT JOIN rotationdetails ON rotationdetails.rotationId = rotation.rotationId 
		LEFT JOIN clinicianhospitalsite ON rotation.hospitalSiteId=clinicianhospitalsite.hospitalSiteId
		LEFT JOIN clinician ON clinicianhospitalsite.clinicianId=clinician.clinicianId
		LEFT JOIN hospitalsites ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
		";

		$sql .= " WHERE rotation.schoolId=" . $schoolId . " AND rotation.isDelete =0 ";
		if ($clinicianId > 0)
			$sql .= " AND clinician.clinicianId=" . $clinicianId;
		// $sql .= " AND rotation.isDelete =0 AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId) AND CURDATE() between startDate AND endDate ";
		// $sql .= " GROUP BY rotation.rotationId ORDER BY rotation.title";

		$sql .= " AND rotation.isDelete = 0 AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)
		AND (
              (CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
              OR
              (rotation.parentRotationId > 0
               AND EXISTS (
                   SELECT 1
                   FROM rotation AS parent
                   WHERE parent.rotationId = rotation.parentRotationId
                   AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
                   AND parent.schoolId = " . $schoolId . " AND rotation.isSchedule = 1 
               )
              )
          )
		";

		$sql .= " GROUP BY rotation.rotationId ORDER BY rotation.title";

		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllActiverotationByClinician($schoolId, $studentId = 0, $clinicianId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = " SELECT rotation.*, courses.courseId, courses.title as courseTitle,
					location.locationId, location.title as locationTitle, IFNULL(count(rotationdetails.rotationId),0) as studentCount,
					(select count(*) from rotation SUBR WHERE SUBR.parentRotationId= rotation.rotationId) AS subratoncount, CONCAT(rotation.title, ' (', hospitalsites.title, ')') as title
					FROM rotation
					LEFT JOIN courses ON rotation.courseId = courses.courseId
					LEFT JOIN location ON courses.locationId = location.locationId
					LEFT JOIN rotationdetails ON rotationdetails.rotationId = rotation.rotationId 
					LEFT JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
					INNER JOIN clinicianhospitalsite ON rotation.hospitalSiteId=clinicianhospitalsite.hospitalSiteId
					INNER JOIN clinician ON clinicianhospitalsite.clinicianId=clinician.clinicianId
					";
		$sql .= " WHERE rotation.schoolId=" . $schoolId . " AND 
				  CURDATE() BETWEEN date(rotation.startDate) AND date(rotation.endDate)";
		$sql .= " AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId) ";
		$sql .= " AND clinician.clinicianId=" . $clinicianId;
		if ($studentId > 0) {
			$sql .= " AND rotationdetails.studentId=" . $studentId;
		}
		$sql .= " GROUP BY rotation.rotationId ORDER BY rotation.title";
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	function GetStudentRotations($studentId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT rotation.*, rotationdetails.*,
				courses.title as courseTitle,location.locationId, location.title as locationTitle,
				hospitalsites.hospitalSiteId,hospitalsites. title as hospitalTitle, attendance.rotationId as clockedOutRotationId ,CONCAT(rotation.title, ' (', hospitalsites.title, ')') as title
				FROM rotation
				LEFT JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
				LEFT JOIN courses ON rotation.courseId = courses.courseId
				LEFT JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
				LEFT JOIN location ON courses.locationId = location.locationId
				LEFT JOIN attendance ON rotation.rotationId = attendance.rotationId AND attendance.isClockOut = 1
				WHERE rotationdetails.studentId=" . $studentId . "
				 GROUP BY rotation.rotationId";

		// AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetStudentRotationsForAdvance($studentId, $schoolId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT rotation.*, rotationdetails.*,
				courses.title as courseTitle,location.locationId, location.title as locationTitle,
				hospitalsites.hospitalSiteId,hospitalsites. title as hospitalTitle, attendance.rotationId as clockedOutRotationId ,CONCAT(rotation.title, ' (', hospitalsites.title, ')') as title
				FROM rotation
				LEFT JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
				LEFT JOIN courses ON rotation.courseId = courses.courseId
				LEFT JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
				LEFT JOIN location ON courses.locationId = location.locationId
				LEFT JOIN attendance ON rotation.rotationId = attendance.rotationId AND attendance.isClockOut = 1
				WHERE rotationdetails.studentId=" . $studentId . "";
		//  GROUP BY rotation.rotationId";

		$sql .= " 
				 AND (
							   (CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
							   OR
							   (rotation.parentRotationId > 0
								AND EXISTS (
									SELECT 1
									FROM rotation AS parent
									WHERE parent.rotationId = rotation.parentRotationId
									AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
									AND parent.schoolId = " . $schoolId . " AND rotation.isSchedule = 1 
								)
							   )
						   )";
		$sql .= " GROUP BY rotation.rotationId";

		// AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetStudentRotationDetails($studentId, $rotationId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT rotation.*, rotationdetails.*,
				courses.title as courseTitle,location.locationId, location.title as locationTitle,
				hospitalsites.hospitalSiteId,hospitalsites. title as hospitalTitle
				FROM rotation
				INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
				INNER JOIN courses ON rotation.courseId = courses.courseId
				INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
				INNER JOIN location ON courses.locationId = location.locationId
				WHERE rotationdetails.studentId=" . $studentId . " AND rotation.rotationId=" . $rotationId;

		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}

	function GetrotationDetails($rotationId, $schoolId)
	{
		$sql = "SELECT rotation.*, hospitalsites.hospitalSiteId, hospitalsites.title as hospitalSite,parentrotation.title as parentRotationTitle,
		parentrotation.startDate as parentStartDate, parentrotation.endDate as parentEndDate,
		rotation.parentRotationId,rotation.locationId as rotationLocationId,courses.locationId
		FROM  rotation 
		LEFT JOIN hospitalsites  ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
		LEFT JOIN rotation as parentrotation  ON parentrotation.rotationId = rotation.parentRotationId
	    LEFT JOIN courses ON rotation.courseId=courses.courseId
		WHERE rotation.rotationId=" . $rotationId . " AND rotation.schoolId =" . $schoolId;
		// echo $sql;
		$objDB = new clsDB();
		$row = $objDB->GetDataRow($sql);
		unset($objDB);

		return $row;
	}

	function GetrotationDetailsToAdmin($rotationId, $schoolId)
	{
		$sql = "SELECT rotation.*, hospitalsites.title as hospitalSite,parentrotation.title as parentRotationTitle,
		parentrotation.startDate as parentStartDate, parentrotation.endDate as parentEndDate,rotation.locationId as rotationLocationId
		FROM  rotation 
		LEFT JOIN hospitalsites  ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
		LEFT JOIN rotation as parentrotation  ON parentrotation.rotationId = rotation.parentRotationId
		WHERE rotation.rotationId=" . $rotationId . " AND rotation.schoolId =" . $schoolId;

		$objDB = new clsDB();
		$row = $objDB->GetDataRow($sql);
		unset($objDB);

		return $row;
	}

	function GetSubRotationByHospital($SchoolId, $clinicianId)
	{
		$objDB = new clsDB();
		$rows = "";

		$sql = "select count(*) AS subratoncount
					from rotation 
					RIGHT JOIN rotation SUBR ON SUBR.rotationId = rotation.parentRotationId
					INNER JOIN clinicianhospitalsite ON rotation.hospitalSiteId=clinicianhospitalsite.hospitalSiteId
					INNER JOIN clinician ON clinicianhospitalsite.clinicianId=clinician.clinicianId";
		$sql .= " WHERE clinicianhospitalsite.clinicianId=" . $clinicianId;

		//ECHO $sql;
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}
	function GetrotationTitleForInteraction($rotationId, $schoolId)
	{

		$objDB = new clsDB();
		$sql = "SELECT title,startDate,endDate FROM  rotation WHERE rotationId=" . $rotationId . " AND schoolId =" . $schoolId;
		//   echo $sql;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}
	function GetParentRotations($schoolId, $locationId=0, $rotationId = 0)
	{
		$objDB = new clsDB();
		//$sql="SELECT rotation.rotationId ,rotation.title FROM rotation WHERE rotation.rotationId=".'1';
		$sql = "SELECT rotation.* 
				FROM  rotation 
				INNER JOIN schools ON rotation.schoolId=schools.schoolId
				LEFT JOIN courses ON rotation.courseId = courses.courseId
				LEFT JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId AND 
					 rotation.parentRotationId =0
				WHERE rotation.parentRotationId =0  AND rotationdetails.rotationDetailsId IS NULL ";
		/*if($locationId > 0)	
		{			
			$sql .= " AND courses.locationId =".$locationId;
		}*/
		if ($schoolId > 0) {
			$sql .= " AND schools.schoolId =" . $schoolId . " AND rotation.isDelete =0 ";
		}
		if ($rotationId > 0) {
			$sql .= " AND rotation.rotationId =" . $rotationId;
		}

		$sql .= " AND ( (CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate)) OR rotation.startDate >= CURDATE() )";

		// $sql .= "
		// AND (
		// 			  (CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
		// 			  OR
		// 			  (rotation.parentRotationId > 0
		// 			   AND EXISTS (
		// 				   SELECT 1
		// 				   FROM rotation AS parent
		// 				   WHERE parent.rotationId = rotation.parentRotationId
		// 				   AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
		// 				   AND parent.schoolId = " . $schoolId . " AND rotation.isSchedule = 1 
		// 			   )
		// 			  )
		// 		  )";

		$sql .= " GROUP BY rotation.title ORDER BY rotation.title asc";
		// echo $sql;
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}
	function GetRotationCounts($schoolId)
	{
		$returnCount = 0;
		$objDB = new clsDB();
		$sql = " SELECT A.TotalCount, B.ActiveCount , C.UpcomingCount FROM 
				(SELECT COUNT(*) As TotalCount FROM `rotation` WHERE schoolId=" . $schoolId . ") As A,
				(SELECT COUNT(*) As ActiveCount FROM `rotation` WHERE 
					CURDATE() BETWEEN date(startDate) AND date(endDate) AND schoolId=" . $schoolId . ") As B,
				(SELECT COUNT(*) As UpcomingCount FROM `rotation` WHERE schoolId= " . $schoolId .
			" AND startDate >= ( CURDATE() + INTERVAL '3' DAY)) As C";
		//echo $sql;
		$returnCount = $objDB->GetDataRow($sql);
		unset($objDB);
		return $returnCount;
	}
	function GetRotationCountByClinician($SchoolId, $clinicianId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = " SELECT rotation.rotationId,
				(SELECT COUNT(*)  FROM `rotation`
					INNER JOIN hospitalsites  ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
					INNER JOIN clinicianhospitalsite ON rotation.hospitalSiteId = clinicianhospitalsite.hospitalSiteId 
					INNER JOIN clinician ON clinicianhospitalsite.clinicianId=clinician.clinicianId" .
			" WHERE rotation.schoolId=" . $SchoolId . " AND rotation.parentRotationId =0 AND clinician.clinicianId=" . $clinicianId . " 
				) As TotalCount,
				(SELECT COUNT(*) FROM `rotation` 
					INNER JOIN hospitalsites  ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
					INNER JOIN clinicianhospitalsite ON rotation.hospitalSiteId = clinicianhospitalsite.hospitalSiteId 
					INNER JOIN clinician ON clinicianhospitalsite.clinicianId=clinician.clinicianId" .
			" WHERE   CURDATE() BETWEEN date(rotation.startDate) AND date(rotation.endDate) AND  rotation.schoolId=" . $SchoolId . " 
					AND clinician.clinicianId=" . $clinicianId . " 
				) As ActiveCount, 
				(SELECT COUNT(*)  FROM `rotation` 
					INNER JOIN hospitalsites  ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
					INNER JOIN clinicianhospitalsite ON rotation.hospitalSiteId = clinicianhospitalsite.hospitalSiteId 
					INNER JOIN clinician ON clinicianhospitalsite.clinicianId=clinician.clinicianId" .
			" WHERE     rotation.schoolId=" . $SchoolId . " 
					 AND clinician.clinicianId=" . $clinicianId . " 
				  AND rotation.startDate >= ( CURDATE() + INTERVAL '3' DAY)				 
				 ) As UpcomingCount
				 FROM rotation	
				INNER JOIN hospitalsites  ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
					INNER JOIN clinicianhospitalsite ON rotation.hospitalSiteId = clinicianhospitalsite.hospitalSiteId 
					INNER JOIN clinician ON clinicianhospitalsite.clinicianId=clinician.clinicianId" .
			" WHERE     rotation.schoolId=" . $SchoolId . " AND rotation.parentRotationId= 0
					 AND clinician.clinicianId=" . $clinicianId;

		//echo $sql;
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}
	function GetStudentRotationCounts($studentId)
	{
		$returnCount = 0;
		$objDB = new clsDB();
		$sql = "SELECT A.TotalCount, B.ActiveCount  FROM (SELECT COUNT(*) As TotalCount FROM `rotation` WHERE studentId=" . $studentId . ") As A,
				(SELECT COUNT(*) As ActiveCount FROM `rotation` WHERE isActive=1 AND studentId=" . $studentId . ") As B";

		$returnCount = $objDB->GetDataRow($sql);
		unset($objDB);
		return $returnCount;
	}
	function GetRotationStudentsCounts($rotationId)
	{
		$returnCount = 0;
		$objDB = new clsDB();
		$sql = "SELECT rotationId  FROM (SELECT COUNT(*) As TotalCount FROM `rotation` WHERE studentId=" . $studentId . ") As A,
				(SELECT COUNT(*) As ActiveCount FROM `rotation` WHERE isActive=1 AND studentId=" . $studentId . ") As B";

		$returnCount = $objDB->GetDataRow($sql);
		unset($objDB);
		return $returnCount;
	}



	function GetStudentClockInRotations($studentId)
	{
		$objDB = new clsDB();
		$rows = "";
		$curdate = date("y-m-d h:i:s");
		$curdates = date("Y-m-d");
		$repeatday = date('w', strtotime($curdate));
		//CLOCKOUT
		$sql = "SELECT rotation.*,
				courses.title as courseTitle, 
				hospitalsites. title as hospitalTitle, 
				IFNULL(attendance.rotationId,0) as clockedOutRotationId,
				0 as isUpcoming, attendance.isClockOut,
				IFNULL(attendance.status,0) as status, IFNULL(attendance.attendanceId, 0) as attendanceId,courses.locationId,
				rotation.locationId as rotationLocationId , 0 as isAlreadyClockOut
				FROM rotation
				INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
				INNER JOIN courses ON rotation.courseId = courses.courseId
				INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
				INNER JOIN attendance ON rotation.rotationId = attendance.rotationId AND attendance.studentId =" . $studentId . "
				WHERE attendance.isClockOut = '0' 
				AND attendance.studentId=" . $studentId . " 
				AND rotation.isDelete =0 AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId) 
				 GROUP BY rotation.rotationId";


		$sql .= " UNION ";

		//1ST TIME CLOCKIN
		// we need to remove weekofday condition, because extended rotation not display correctly
		// removed line(585) = AND rotationrepeatdays.dayOfWeek=".$repeatday."
		$sql .= "SELECT rotation.*,
				courses.title as courseTitle, 
				hospitalsites. title as hospitalTitle, 
				IFNULL(attendance.rotationId,0) as clockedOutRotationId,
				0 as isUpcoming, 0 as isClockOut, 
				IFNULL(attendance.status,0) as status, IFNULL(attendance.attendanceId, 0) as attendanceId, courses.locationId, 
				rotation.locationId as rotationLocationId, 0 as isAlreadyClockOut
				FROM rotation
				INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
				INNER JOIN courses ON rotation.courseId = courses.courseId
				LEFT JOIN attendance ON rotation.rotationId = attendance.rotationId 
				LEFT JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
				INNER JOIN rotationrepeatdays ON rotation.rotationId=rotationrepeatdays.rotationId
				WHERE   CURDATE() BETWEEN date(rotation.startDate) AND date(rotation.endDate)	
				AND rotationdetails.studentId=" . $studentId . "
				AND rotation.rotationId NOT IN( SELECT attendance.rotationId FROM attendance WHERE studentId=" . $studentId . ")
			    AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)
				 GROUP BY rotation.rotationId ";

		$sql .= " UNION ";
		//DAILY CLOCKIN
		// we need to remove weekofday condition, because extended rotation not display correctly
		// removed line(608) = AND rotationrepeatdays.dayOfWeek=".$repeatday."
		$sql .= "SELECT rotation.*,
				courses.title as courseTitle, 
				hospitalsites. title as hospitalTitle, 
				IFNULL(attendance.rotationId,0) as clockedOutRotationId,
				0 as isUpcoming, 0, 
				2 as status, IFNULL(attendance.attendanceId, 0) as attendanceId, courses.locationId,rotation.locationId as rotationLocationId	,0 as isAlreadyClockOut		
				FROM rotation
				INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
				INNER JOIN courses ON rotation.courseId = courses.courseId
				INNER JOIN attendance ON rotation.rotationId = attendance.rotationId AND attendance.studentId =" . $studentId . " 
				 AND attendance.isClockOut = '1'
				LEFT JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
				INNER JOIN rotationrepeatdays ON rotation.rotationId=rotationrepeatdays.rotationId
				WHERE   CURDATE() BETWEEN date(rotation.startDate) AND date(rotation.endDate)	
				AND rotationdetails.studentId=" . $studentId .
			" AND   rotation.rotationId NOT IN( SELECT attendance.rotationId FROM 
				attendance WHERE date(attendance.clockInDateTime)= CURDATE() AND  studentId=" . $studentId . ") 
				AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId) 
				 GROUP BY rotation.rotationId";

		$sql .= " UNION ";
		//UPCOMING
		$sql .= "SELECT rotation.*,
				courses.title as courseTitle, 
				hospitalsites. title as hospitalTitle, 0 as clockedOutRotationId ,1 as isUpcoming ,
				0 as isClockOut, 0 as status, 0,courses.locationId,
				rotation.locationId as rotationLocationId, 0 as isAlreadyClockOut

				FROM rotation
				INNER JOIN rotation parrotation ON rotation.parentRotationId != parrotation.rotationId 
				INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
				INNER JOIN courses ON rotation.courseId = courses.courseId
				LEFT JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
				WHERE  date(rotation.startDate) > CURDATE()
				AND rotationdetails.studentId=" . $studentId .
			"  AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)";
		$sql .= " GROUP BY rotation.rotationId";

		$sql .= " UNION ";

		$sql .= "SELECT rotation.*,
            	courses.title as courseTitle, 
            	hospitalsites. title as hospitalTitle, 
            	IFNULL(attendance.rotationId,0) as clockedOutRotationId,
            	0 as isUpcoming, attendance.isClockOut, 1 as isAlreadyClockOut,
            	IFNULL(attendance.status,0) as status, IFNULL(attendance.attendanceId, 0) as attendanceId,courses.locationId,
            	rotation.locationId as rotationLocationId				
            	FROM rotation
            	INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
            	INNER JOIN courses ON rotation.courseId = courses.courseId
            	INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
            	INNER JOIN attendance ON rotation.rotationId = attendance.rotationId AND attendance.studentId =" . $studentId . "
            	WHERE attendance.isClockOut = '1'  
            	AND  date(attendance.clockInDateTime) = CURDATE()
            	AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)
            	GROUP BY rotation.rotationId";

		//	echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	function GetAllStudentClockInRotations($studentId, $courseId = 0)
	{

		$objDB = new clsDB();
		$rows = "";
		$todaysDate = date("Y-m-d");
		$curdate = date("y-m-d h:i:s");
		$repeatday = date('w', strtotime($curdate));
		$sql = "SELECT rotation.*,
				courses.title as courseTitle,courses.courseHours,
				hospitalsites. title as hospitalTitle, 
				IFNULL(attendance.rotationId,0) as clockedOutRotationId,
				0 as isUpcoming, attendance.isClockOut,
				0 as isInActiveRotation, IFNULL(attendance.attendanceId, 0) as attendanceId, courses.locationId,
				rotation.parentRotationId,rotation.locationId as rotationLocationId,courses.locationId			
				FROM rotation
				INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
				INNER JOIN courses ON rotation.courseId = courses.courseId
				INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
				INNER JOIN rotationrepeatdays ON rotation.rotationId=rotationrepeatdays.rotationId AND rotationrepeatdays.dayOfWeek !=" . $repeatday . "
				LEFT JOIN attendance ON rotation.rotationId = attendance.rotationId AND attendance.studentId =" . $studentId . "
				WHERE  rotationdetails.studentId=" . $studentId . "  and date(rotation.endDate) < '" . $todaysDate . "' AND date(rotation.startDate) < '" . $todaysDate . "'
				AND rotation.isDelete =0 AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)  
			";
		if ($courseId > 0)
			$sql .= " AND rotation.courseId=" . $courseId;

		$sql .= " group by rotation.rotationId";
		$sql .= " UNION ";

		$sql .= "SELECT rotation.*,
				courses.title as courseTitle,courses.courseHours, 
				hospitalsites. title as hospitalTitle, 
				0 as clockedOutRotationId,
				0 as isUpcoming, 0,
				1 as isInActiveRotation, 0 as attendanceId, courses.locationId,
				rotation.parentRotationId,rotation.locationId as rotationLocationId,courses.locationId
				FROM rotation
				INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
				INNER JOIN courses ON rotation.courseId = courses.courseId
				INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
				INNER JOIN rotationrepeatdays ON rotation.rotationId=rotationrepeatdays.rotationId 
				WHERE  rotationdetails.studentId=" . $studentId . " 
				AND rotation.isDelete =0  AND rotation.isSchedule = 0
				 ";

		$sql .= " AND '" . $todaysDate . "' BETWEEN date(rotation.startDate) AND date(rotation.endDate)";

		if ($courseId > 0)
			$sql .= " AND rotation.courseId=" . $courseId;

		$sql .= " group by rotation.rotationId";

		// // schedule
		$sql .= " UNION ";

		$sql .= "SELECT rotation.*, courses.title as courseTitle,courses.courseHours, hospitalsites. title as hospitalTitle, 0 as clockedOutRotationId, 0 as isUpcoming, 0, 1 as isInActiveRotation, 0 as attendanceId,
		  courses.locationId, rotation.parentRotationId,rotation.locationId as rotationLocationId,courses.locationId 
		  FROM rotation INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
		  INNER JOIN courses ON rotation.courseId = courses.courseId 
		  INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId 
		  INNER JOIN rotationrepeatdays ON rotation.rotationId=rotationrepeatdays.rotationId 
		  INNER JOIN rotationscheduledates ON rotation.rotationId = rotationscheduledates.rotationId
				WHERE  rotationdetails.studentId=" . $studentId . " 
				AND rotation.isDelete =0  
				 ";

		$sql .= " AND '" . $todaysDate . "' BETWEEN date(rotation.startDate) AND date(rotation.endDate)   AND rotation.isSchedule = 1  ";

		$sql .= "  AND rotation.rotationId NOT IN (
			SELECT rotationId
			FROM rotationscheduledates
			WHERE  DATE(scheduleDate) = '" . $todaysDate . "'
		) ";

		if ($courseId > 0)
			$sql .= " AND rotation.courseId=" . $courseId;

		$sql .= " group by rotation.rotationId";
		// AND NOT EXISTS (
		// 	SELECT 1
		// 	FROM rotationscheduledates AS rd2
		// 	WHERE rd2.rotationId = rotation.rotationId
		// 	AND DATE_FORMAT(rd2.scheduleDate, '%Y-%m-%d') = '" . $todaysDate . "' )";
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllStudentRotationsToApp($studentId, $limitString)
	{
		$objDB = new clsDB();
		$rows = "";
		$curdate = date("y-m-d h:i:s");
		$repeatday = date('w', strtotime($curdate));
		$sql = "SELECT rotation.*,
				courses.title as courseTitle, 
				hospitalsites. title as hospitalTitle,hospitalsites.hospitalSiteId, 
				IFNULL(attendance.rotationId,0) as clockedOutRotationId,
				0 as isUpcoming, attendance.isClockOut,
				2 as status, IFNULL(attendance.attendanceId, 0) as attendanceId				
				FROM rotation
				INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
				INNER JOIN courses ON rotation.courseId = courses.courseId
				INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
				INNER JOIN rotationrepeatdays ON rotation.rotationId=rotationrepeatdays.rotationId
				LEFT JOIN attendance ON rotation.rotationId = attendance.rotationId AND attendance.studentId =" . $studentId . "
				WHERE  rotationdetails.studentId=" . $studentId . " 
				AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)
			";

		$sql .= " GROUP BY rotation.title " . $limitString;
		// echo $sql;
		// exit;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	function GetActiveRotations($studentId)
	{
		$sql = "SELECT COUNT(rotation.rotationId)AS rotationCount				
				FROM rotation 
				LEFT JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId
				WHERE CURDATE() BETWEEN rotation.startDate AND rotation.endDate
				AND rotationdetails.studentId=" . $studentId;
		//echo $sql;exit;
		$objDB = new clsDB();
		$rotationCount = $objDB->GetSingleFieldValue($sql);
		unset($objDB);

		return $rotationCount;
	}
	function Getrotationdate($rotationId)
	{
		$sql = "SELECT (courses.`courseEndDate`),(rotation.`endDate`), courses.title 
              FROM courses 
              INNER JOIN rotation ON rotation.`courseId` = courses.`courseId` 
              where courses.courseEndDate >= rotation.endDate";
		//echo $sql;exit;
		$objDB = new clsDB();
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	// done by tejas
	function GetAllSubRotation($schoolId, $locationId = 0, $courseId = 0, $rotationId, $studentId = 0) //A: admin / C:Clinician
	{
		$rotationId = str_replace(" ", ",", $rotationId);

		$objDB = new clsDB();
		$rows = "";

		$sql = " SELECT rotation.*, courses.courseId, courses.title as courseTitle,courses.courseHours,
		location.locationId, location.title as locationTitle, IFNULL(count(rotationdetails.rotationId),0) as studentCount,
		(select count(*) from rotation SUBR WHERE SUBR.parentRotationId= rotation.rotationId) AS subratoncount,
		hospitalsites.title as hospitalSite,rotation.locationId as rotationLocationId
		FROM rotation
		LEFT JOIN courses ON rotation.courseId = courses.courseId
		LEFT JOIN hospitalsites  ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
		LEFT JOIN location ON courses.locationId = location.locationId
		LEFT JOIN rotationdetails ON rotationdetails.rotationId = rotation.rotationId";

		$sql .= " WHERE rotation.schoolId=" . $schoolId;
		if ($rotationId)
			$sql .= " AND rotation.parentRotationId IN (" . $rotationId . ")";
		if ($locationId > 0) //Clinician
		{
			$sql .= " AND courses.locationId=" . $locationId;
		}
		if ($courseId > 0) //course filter
		{
			$sql .= " AND rotation.courseId=" . $courseId;
		}

		if ($studentId > 0) //STUDENT filter
		{
			$sql .= " AND rotationdetails.studentId=" . $studentId;
		}
		$sql .= " GROUP BY rotation.rotationId ORDER BY rotation.title ASC";
		// echo $sql;exit;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllSubRotationForClinician($schoolId, $clinicianId) //A: admin / C:Clinician
	{
		$objDB = new clsDB();
		$rows = "";

		$sql = " SELECT rotation.*, courses.courseId, courses.title as courseTitle,
		location.locationId, location.title as locationTitle, IFNULL(count(rotationdetails.rotationId),0) as studentCount,
		(select count(*) from rotation SUBR WHERE SUBR.parentRotationId != rotation.rotationId) AS subratoncount,
		hospitalsites.title as hospitalSite,rotation.locationId as rotationLocationId
		FROM rotation
		INNER JOIN courses ON rotation.courseId = courses.courseId
		INNER JOIN hospitalsites  ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
		INNER JOIN clinicianhospitalsite ON hospitalsites.hospitalSiteId=clinicianhospitalsite.hospitalSiteId
		INNER JOIN clinician ON clinicianhospitalsite.clinicianId=clinician.clinicianId
		INNER JOIN location ON courses.locationId = location.locationId
		INNER JOIN rotationdetails ON rotationdetails.rotationId = rotation.rotationId 
		
		";

		$sql .= " WHERE rotation.schoolId=" . $schoolId . " AND clinician.clinicianId=" . $clinicianId .
			" AND rotation.parentRotationId !=0";

		$sql .= " GROUP BY rotation.rotationId ORDER BY rotation.title";
		//echo $sql;exit;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetRotationBySchool($schoolId, $studentId = 0)
	{
		$objDB = new clsDB();
		$rows = "";

		$sql = "SELECT rotation.rotationId,rotation.title,startDate,CONCAT(rotation.title, ' (', hospitalsites.title, ')') as title
		FROM rotation  
		LEFT JOIN hospitalsites ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
		LEFT JOIN rotationdetails ON rotationdetails.rotationId = rotation.rotationId 

			   WHERE rotation.schoolId=" . $schoolId . " AND isDelete =0 AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)";

		// $sql .= " 
		// AND (
		// 			(CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
		// 			OR
		// 			(rotation.parentRotationId > 0
		// 			AND EXISTS (
		// 				SELECT 1
		// 				FROM rotation AS parent
		// 				WHERE parent.rotationId = rotation.parentRotationId
		// 				AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
		// 				AND parent.schoolId = " . $schoolId . " AND rotation.isSchedule = 1 
		// 			)
		// 			)
		// 		)";
		if ($studentId > 0) {
			$sql .= " AND rotationdetails.studentId=" . $studentId;
		}
		$sql .= " GROUP BY rotation.rotationId";
		$sql .=   " ORDER BY rotation.title";
		// echo $sql;exit;

		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetCurrentRotationBySchool($schoolId)
	{
		$objDB = new clsDB();
		$rows = "";

		$sql = "SELECT rotationId,rotation.title,startDate,CONCAT(rotation.title, ' (', hospitalsites.title, ')') as title,  rotation.rotationId
		FROM rotation 
				LEFT JOIN hospitalsites ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
			   WHERE rotation.schoolId=" . $schoolId . " AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)
			   AND (
                (CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
                OR
                (rotation.parentRotationId > 0
                    AND EXISTS (
                        SELECT 1
                        FROM rotation AS parent
                        WHERE parent.rotationId = rotation.parentRotationId
                        AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
                        AND parent.schoolId = $schoolId AND rotation.isSchedule = 1
                    )
                )
            )
			   ORDER BY rotation.title";
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetCurrentRotationBySchoolForActiveHospitals($schoolId, $studentId)
	{
		$objDB = new clsDB();
		$rows = "";

		$sql = "SELECT
            rotation.rotationId,
            rotation.parentRotationId,
            rotation.title,
            rotation.startDate,
            rotation.endDate,
            rotation.schoolId,
            hospitalsites.title as hospitalTitle,
			CONCAT(rotation.title, ' (', hospitalsites.title, ')') as title
        FROM
            rotation
        INNER JOIN hospitalsites ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
        INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId
        WHERE
            rotation.schoolId = $schoolId
            AND (
                (CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
                OR
                (rotation.parentRotationId > 0
                    AND EXISTS (
                        SELECT 1
                        FROM rotation AS parent
                        WHERE parent.rotationId = rotation.parentRotationId
                        AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
                        AND parent.schoolId = $schoolId AND rotation.isSchedule = 1 
                    )
                )
            )
            AND hospitalsites.isHospital = 1
            AND hospitalsites.isSms = 1
            AND rotationdetails.studentId = $studentId
        ORDER BY rotation.rotationId DESC";
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetRotationForReport($schoolId)
	{
		$objDB = new clsDB();
		$rows = "";

		$sql = "SELECT rotationId,title,startDate FROM rotation 
			   WHERE schoolId=" . $schoolId . " AND parentRotationId = 0  AND rotation.isDelete =0
			   ORDER BY title DESC";
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetSubRotationForReport($schoolId, $rotationId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT rotationId,title,startDate FROM rotation WHERE schoolId=" . $schoolId;
		if ($rotationId)
			$sql .= " AND parentRotationId IN(" . $rotationId . ")";
		$sql .= " ORDER BY title";
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetActiveRotationBySchool($schoolId, $studentId = 0)
	{
		$objDB = new clsDB();
		$rows = "";

		$sql = "SELECT rotation.rotationId,rotation.title,rotation.startDate, hospitalsites.hospitalSiteId, hospitalsites.title as hospitalTitle, CONCAT(rotation.title, ' (', hospitalsites.title, ')') as title FROM rotation 
				INNER JOIN rotationdetails on rotationdetails.rotationId = rotation.rotationId
				LEFT JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
			   WHERE rotation.schoolId=" . $schoolId . "   AND rotation.isDelete =0
			   AND (
              (CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
              OR
              (rotation.parentRotationId > 0
               AND EXISTS (
                   SELECT 1
                   FROM rotation AS parent
                   WHERE parent.rotationId = rotation.parentRotationId
                   AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
                   AND parent.schoolId = " . $schoolId . " AND rotation.isSchedule = 1 
               )
              )
          )
			   AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)";
		if ($studentId > 0) {
			$sql .= " AND rotationdetails.studentId=" . $studentId;
		}
		$sql .= " GROUP by rotation.rotationId ORDER BY rotation.title";

		// ECHO $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	function GetRotationExceptParentBySchool($schoolId)
	{
		$objDB = new clsDB();
		$rows = "";

		$sql = "SELECT rotationId,title,startDate FROM rotation 
			   WHERE schoolId=" . $schoolId . "
			   AND rotationId NOT IN(SELECT parentRotationId FROM rotation WHERE schoolId=" . $schoolId . ")
			   ORDER BY title";
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetSubRotationDetails($subrotationId, $schoolId)
	{

		$objDB = new clsDB();
		$sql = "SELECT * FROM  rotation WHERE rotationId=" . $subrotationId . " AND schoolId =" . $schoolId;

		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}
	function GetRotationByAttendance($schoolId, $studentId, $rotationId)
	{
		$objDB = new clsDB();
		$rows = "";

		$sql = "SELECT  rotation.`title`,rotation.`rotationId`
				 FROM attendance 
				 RIGHT JOIN rotation ON attendance.`rotationId` = rotation.`rotationId` 
				 RIGHT JOIN student  ON attendance.studentId = student.`studentId`
				 RIGHT JOIN checkoff ON checkoff.studentId = student.`studentId`";

		$sql .= " where rotation.schoolId=" . $schoolId;
		if ($studentId) {

			$sql .= "   AND student.studentId=" . $studentId;
		}
		if ($rotationId) {

			$sql .= "  AND rotation.rotationId=" . $rotationId;
		}


		$sql .= " Group By rotation.`title`";

		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetrotationForProcedureCount($schoolId, $rotationId, $studentId)
	{
		$objDB = new clsDB();
		$rows = "";

		$sql = "SELECT  rotation.title,rotation.rotationId,
				checkoff.rotationId,
				checkoff.studentId,
				student.studentId,
				rotationdetails.rotationId,
				rotationdetails.studentId,CONCAT(rotation.title, ' (', hospitalsites.title, ')') as title
				FROM rotation 
				LEFT JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId
				LEFT JOIN student ON rotationdetails.studentId=student.studentId
				LEFT JOIN checkoff ON checkoff.rotationId=rotation.rotationId
				LEFT JOIN hospitalsites ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId";
		/* if($rotationId > 0)
		 {
			$sql .= " WHERE rotation.rotationId=".$rotationId;	
		 }*/
		if ($schoolId > 0) {
			$sql .= " WHERE rotation.schoolId=" . $schoolId;
		}
		if ($studentId > 0) {
			$sql .= " AND  rotationdetails.studentId=" . $studentId;
		}
		// $sql .= " AND CURDATE() BETWEEN date(rotation.startDate) AND date(rotation.endDate)";
		$sql .= " AND rotation.isDelete =0 AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId) 
		--  AND (
        --       (CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
        --       OR
        --       (rotation.parentRotationId > 0
        --        AND EXISTS (
        --            SELECT 1
        --            FROM rotation AS parent
        --            WHERE parent.rotationId = rotation.parentRotationId
        --            AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
        --            AND parent.schoolId = " . $schoolId . " AND rotation.isSchedule = 1 
        --        )
        --       )
        --   )
		  ";
		$sql .= " GROUP BY rotation.rotationId order by rotation.title";

		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	function getAllCheckoffRotationsByStudent($schoolId, $studentId)
	{
		$objDB = new clsDB();
		$rows = "";

		$sql = "Select rotation.*, CONCAT(rotation.title, ' (', hospitalsites.title, ')') as title 
		from checkoff
		 inner join rotation on rotation.rotationId = checkoff.rotationId
		 LEFT JOIN hospitalsites ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
		  where checkoff.studentId = $studentId 
		 
		 AND rotation.schoolId = $schoolId group by checkoff.rotationId";

		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	function GetUpcomingRotation($schoolId)
	{
		$date = Date('Y-m-d');

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT title,startDate,endDate,rotation.parentRotationId,rotation.locationId as rotationLocationId,courses.locationId FROM  rotation 			   
					LEFT JOIN courses ON rotation.courseId=courses.courseId
				   WHERE schoolId=" . $schoolId . " 
				   AND DATE_FORMAT(startDate, '%Y-%m-%d') >='" . $date . "'";
		$sql .= " ORDER BY startDate DESC LIMIT 10";
		//ECHO $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetUpcomingRotationByClinician($schoolId, $clinicianId)
	{
		$date = Date('Y-m-d', strtotime("+3 days"));

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT rotation.*, rotationdetails.*,rankmaster.title as rank,student.studentId,student.firstName,
				   rotation.parentRotationId,rotation.locationId as rotationLocationId,courses.locationId
				   FROM  rotation 
				   INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId
				   INNER JOIN student ON rotationdetails.studentId = student.studentId
				   INNER JOIN clinicianhospitalsite ON rotation.hospitalSiteId=clinicianhospitalsite.hospitalSiteId
				   INNER JOIN clinician ON clinicianhospitalsite.clinicianId=clinician.clinicianId
				   INNER JOIN rankmaster ON rankmaster.rankId=student.rankId
				   LEFT JOIN courses ON rotation.courseId=courses.courseId
				   WHERE rotation.schoolId=" . $schoolId . " AND DATE_FORMAT(rotation.startDate, '%Y-%m-%d')='" . $date . "'";
		$sql .= " AND clinician.clinicianId=" . $clinicianId;
		//ECHO $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}


	function GetUpcomingRotationByStudent($schoolId, $studentId)
	{
		$date = Date('Y-m-d', strtotime("+3 days"));
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT rotation.*, rotationdetails.*,rankmaster.title as rank,student.studentId,
				student.firstName 
			   FROM  rotation 
			   INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId
			   INNER JOIN student ON rotationdetails.studentId = student.studentId
			   INNER JOIN rankmaster ON rankmaster.rankId=student.rankId
			   WHERE rotation.schoolId=" . $schoolId . " AND DATE_FORMAT(rotation.startDate, '%Y-%m-%d')='" . $date . "'" .
			" AND student.studentId=" . $studentId;
		//ECHO $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetRotationByStudent($schoolId, $studentId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT rotation.*, rotationdetails.*,
				courses.title as courseTitle,location.locationId, location.title as locationTitle,
				schools.schoolId,student.*,
				hospitalsites.hospitalSiteId,hospitalsites. title as hospitalTitle, attendance.rotationId as clockedOutRotationId , CONCAT(rotation.title, ' (', hospitalsites.title, ')') as title
				FROM rotation
				INNER JOIN schools ON rotation.schoolId=schools.schoolId
				LEFT JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
				LEFT JOIN  student ON rotationdetails.studentId =student.studentId
				LEFT JOIN courses ON rotation.courseId = courses.courseId
				LEFT JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
				LEFT JOIN location ON courses.locationId = location.locationId
				LEFT JOIN attendance ON rotation.rotationId = attendance.rotationId AND attendance.isClockOut = 1
				WHERE schools.schoolId=" . $schoolId . " AND rotation.isDelete =0";
		if ($studentId)
			$sql .= " AND rotationdetails.studentId=" . $studentId;

		// $sql .= " 
		// 	AND (
		// 				  (CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
		// 				  OR
		// 				  (rotation.parentRotationId > 0
		// 				   AND EXISTS (
		// 					   SELECT 1
		// 					   FROM rotation AS parent
		// 					   WHERE parent.rotationId = rotation.parentRotationId
		// 					   AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
		// 					   AND parent.schoolId = " . $schoolId . " AND rotation.isSchedule = 1 
		// 				   )
		// 				  )
		// 			  )";
		$sql .= " GROUP BY rotation.rotationId";
		// ECHO $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetCurrentRotationByStudent($schoolId, $studentId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT rotation.*, rotationdetails.*,
				courses.title as courseTitle,location.locationId, location.title as locationTitle,
				schools.schoolId,student.*,
				hospitalsites.hospitalSiteId,hospitalsites. title as hospitalTitle, attendance.rotationId as clockedOutRotationId 
				FROM rotation
				INNER JOIN schools ON rotation.schoolId=schools.schoolId
				LEFT JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
				LEFT JOIN  student ON rotationdetails.studentId =student.studentId
				LEFT JOIN courses ON rotation.courseId = courses.courseId
				LEFT JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
				LEFT JOIN location ON courses.locationId = location.locationId
				LEFT JOIN attendance ON rotation.rotationId = attendance.rotationId AND attendance.isClockOut = 1
				WHERE rotationdetails.studentId=" . $studentId . " AND schools.schoolId=" . $schoolId .
			" AND CURDATE() between startDate AND endDate GROUP BY rotation.rotationId";
		//ECHO $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetRotationHistoryByStudent($schoolId, $studentId, $rotationId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT rotation.*, rotationdetails.*, courses.title as courseTitle,
				courses.semesterId, semestermaster.semesterId, 
				semestermaster.title AS Semestertitle,
				location.locationId, location.title as locationTitle, 
				schools.schoolId,student.*,rankmaster.rankId,rankmaster.title AS rankname,clinicianhospitalsite.hospitalSiteId,
				clinicianhospitalsite.clinicianId,clinician.clinicianId,clinician.firstName AS clinicianFName,clinician.lastName AS clinicianLName,
				hospitalsites.hospitalSiteId,hospitalsites. title as hospitalTitle, attendance.rotationId as clockedOutRotationId 
				FROM rotation 
				INNER JOIN schools ON rotation.schoolId=schools.schoolId 
				INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
				LEFT JOIN student ON rotationdetails.studentId =student.studentId 
				LEFT JOIN rankmaster ON student.rankId =rankmaster.rankId 
				INNER JOIN courses ON rotation.courseId = courses.courseId 
				LEFT JOIN semestermaster ON semestermaster.semesterId=courses.semesterId 
				INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId 
				LEFT JOIN clinicianhospitalsite ON hospitalsites.hospitalSiteId = clinicianhospitalsite.hospitalSiteId 
				LEFT JOIN clinician ON clinicianhospitalsite.clinicianId=clinician.clinicianId 
				INNER JOIN location ON courses.locationId = location.locationId 
				LEFT JOIN attendance ON rotation.rotationId = attendance.rotationId AND attendance.isClockOut = 1";
		$sql .= " WHERE rotationdetails.studentId=" . $studentId . " AND schools.schoolId=" . $schoolId;

		$sql .= " AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId) ";

		if ($rotationId) {
			$sql .= "  AND  rotation.rotationId=" . $rotationId;
		}

		$sql .= " GROUP BY rotation.rotationId";
		//ECHO $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetSingleRotation($schoolId, $rotationId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT rotation.rotationId,rotation.title,rotation.schoolId,schools.schoolId
				FROM rotation
				INNER JOIN schools ON rotation.schoolId=schools.schoolId
				WHERE rotation.rotationId=" . $rotationId;
		//echo 	$sql;	
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllrotationDetails($schoolId, $rotationId) //A: admin / C:Clinician
	{
		$objDB = new clsDB();
		$rows = "";

		$sql = " SELECT rotation.*,hospitalsites.hospitalSiteId,hospitalsites.title As hospitalname, 
				clinicianhospitalsite.hospitalSiteId,clinicianhospitalsite.clinicianId,clinician.clinicianId,
				clinician.firstName,clinician.lastName,rotationdetails.*,student.studentId,
				student.firstName AS Studentfname,student.lastName AS Studentlname
				FROM rotation
				INNER JOIN rotationdetails ON rotation.rotationId=rotationdetails.rotationId
				INNER JOIN student ON rotationdetails.studentId=student.studentId
				INNER JOIN hospitalsites ON rotation.hospitalSiteId=hospitalsites.hospitalSiteId
				INNER JOIN clinicianhospitalsite ON hospitalsites.hospitalSiteId=clinicianhospitalsite.hospitalSiteId
				INNER JOIN clinician ON clinicianhospitalsite.clinicianId=clinician.clinicianId";

		$sql .= " WHERE rotation.schoolId=" . $schoolId . " AND rotation.rotationId=" . $rotationId;

		$sql .= " GROUP BY rotation.rotationId ORDER BY rotation.title";
		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetRotationCountForNotification($schoolId)
	{
		$objDB = new clsDB();
		$rows = "";

		$sql = "select * from notification ORDER BY notificationId ASC limit 5";
		//echo $sql;exit;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		//return $rows;
	}

	function UpdateNotification($schoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "UPDATE notification SET isread=1 WHERE isread=0";
		$rows = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $rows;
	}

	//API CLOCK IN
	function GetAPI_StudentClockInRotations($studentId)
	{
		//Student Timezone
		$objStudent = new clsStudent();
		$timezone = $objStudent->GetStudentTimezone($studentId);
		unset($objStudent);

		$objDB = new clsDB();
		//Get current date
		$CurrentDate = date("Y-m-d h:i:s");
		$repeatday = date('w', strtotime($CurrentDate));
		$CurrentDate = converToServerTimeZone($CurrentDate, $timezone);
		$CurrentDate = date("Y-m-d h:i:s", strtotime($CurrentDate));

		$sql = "SELECT rotation.*,
				courses.title as courseTitle, 
				hospitalsites. title as hospitalTitle, 
				IFNULL(attendance.rotationId,0) as clockedOutRotationId,
				0 as isUpcoming, IFNULL(attendance.isClockOut,0) as isClockOut,
				IFNULL(attendance.status,0) as status,courses.locationId as courseLocationId
				FROM rotation
				INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
				INNER JOIN courses ON rotation.courseId = courses.courseId				
				LEFT JOIN attendance ON (rotation.rotationId = attendance.rotationId 
				AND  date_format(date(attendance.clockInDateTime),'%Y-%m-%d')=date_format(date('" . $CurrentDate . "'),'%Y-%m-%d')
				AND attendance.studentId =" . $studentId . ")
				LEFT JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId 
				INNER JOIN rotationrepeatdays ON rotation.rotationId=rotationrepeatdays.rotationId
							AND rotationrepeatdays.dayOfWeek=" . $repeatday . "
				WHERE date('" . $CurrentDate . "') BETWEEN date(rotation.startDate) AND date(rotation.endDate)
				AND rotationdetails.studentId=" . $studentId .
			"  AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)
				GROUP BY rotation.rotationId";
		// echo $sql;
		$rows = $objDB->GetResultSet($sql);
		unset($objDB);

		return $rows;
	}

	//API CLOCK IN
	function GetAPI_StudentClockOutRotations($studentId)
	{
		$sql = "SELECT rotation.*,
				courses.title as courseTitle,
				attendance.clockInDateTime,
				courses.locationId as courseLocationId
				FROM rotation
				INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
				INNER JOIN courses ON rotation.courseId = courses.courseId				
				INNER JOIN attendance ON rotation.rotationId = attendance.rotationId AND attendance.studentId =" . $studentId .
			" WHERE attendance.isClockOut = 0
				AND rotationdetails.studentId=" . $studentId . " GROUP BY rotation.rotationId";
		// echo $sql;
		$objDB = new clsDB();
		$rows = $objDB->GetResultset($sql);
		unset($objDB);

		return $rows;
	}

	//API CLOCK IN
	function GetAPI_StudentPendingClockOutCount($studentId)
	{
		$sql = "SELECT COUNT(rotation.rotationId) as PendingClockOutCount

				FROM rotation
				INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
				INNER JOIN courses ON rotation.courseId = courses.courseId				
				INNER JOIN attendance ON rotation.rotationId = attendance.rotationId AND attendance.studentId =" . $studentId .
			" LEFT JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId 
				WHERE attendance.isClockOut = 0
				AND rotationdetails.studentId=" . $studentId . " GROUP BY rotation.rotationId";

		$objDB = new clsDB();
		$rows = $objDB->GetSingleFieldValue($sql);
		unset($objDB);

		return $rows;
	}

	function GetRotationByClinician($SchoolId, $clinicianId, $rotationId = 0, $from_date = '', $to_date = '', $isActive = 0)
	{
		$objDB = new clsDB();
		$rows = "";

		$sql = " SELECT rotation.*, courses.courseId, courses.title as courseTitle,hospitalsites.title as hospitalSite,
				location.locationId, location.title as locationTitle, 
				IFNULL(count(rotationdetails.rotationId),0) as studentCount	,
				(select count(*) from rotation SUBR WHERE SUBR.parentRotationId= rotation.rotationId) AS subratoncount ,
				rotation.locationId as rotationLocationId, CONCAT(rotation.title, ' (', hospitalsites.title, ')') as title
				FROM rotation
				LEFT JOIN courses ON rotation.courseId = courses.courseId
				LEFT JOIN location ON courses.locationId = location.locationId
				INNER JOIN hospitalsites  ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
				LEFT JOIN rotationdetails ON rotation.rotationId  = rotationdetails.rotationId 
				INNER JOIN clinicianhospitalsite ON rotation.hospitalSiteId = clinicianhospitalsite.hospitalSiteId 
				INNER JOIN clinician ON clinicianhospitalsite.clinicianId=clinician.clinicianId";

		$sql .= " WHERE rotation.schoolId=" . $SchoolId;
		// AND rotation.isDelete =0 AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)";
		$sql .= " AND clinician.clinicianId=" . $clinicianId;

		if ($rotationId) {
			$sql .= " AND rotation.rotationId=" . $rotationId;
		}
		if ($to_date != '') {
			$sql .= " AND date(rotation.endDate) <= '" . $to_date . "'";
		}
		if ($from_date != '') {
			$sql .= " AND date(rotation.endDate) >= '" . $from_date . "'";
		}

		if ($isActive > 0) {
			$sql .= " 
			AND (
						  (CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
						  OR
						  (rotation.parentRotationId > 0
						   AND EXISTS (
							   SELECT 1
							   FROM rotation AS parent
							   WHERE parent.rotationId = rotation.parentRotationId
							   AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
							   AND parent.schoolId = " . $SchoolId . " AND rotation.isSchedule = 1 
						   )
						  )
					  )";
		}

		$sql .= " GROUP BY rotation.rotationId ORDER BY `rotation`.`title` ASC";

		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetRotationByClinicianRole($SchoolId, $clinicianId, $rotationId, $from_date = '', $to_date = '',  $studentId = 0, $hospitalsiteId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$curdate = date("y-m-d h:i:s");
		$todaysDate = date("Y-m-d");
		$repeatday = date('w', strtotime($curdate));

		$sql = " SELECT rotation.rotationId, rotation.title  as rotationName, rotation.startDate, rotation.endDate,
		rotation.parentRotationId, rotation.duration, rotation.courseId, rotation.hospitalSiteId, rotation.isSchedule, courses.courseId, courses.title as courseTitle,courses.courseHours,hospitalsites.title as hospitalTitle,
				location.locationId, location.title as locationTitle, 
				IFNULL(count(rotationdetails.rotationId),0) as studentCount	,
				(select count(*) from rotation SUBR WHERE SUBR.parentRotationId= rotation.rotationId) AS subratoncount ,
				rotation.locationId as rotationLocationId
				FROM rotation
				LEFT JOIN courses ON rotation.courseId = courses.courseId
				LEFT JOIN location ON courses.locationId = location.locationId
				INNER JOIN hospitalsites  ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
				LEFT JOIN rotationdetails ON rotation.rotationId  = rotationdetails.rotationId 
				INNER JOIN clinicianhospitalsite ON rotation.hospitalSiteId = clinicianhospitalsite.hospitalSiteId 
				INNER JOIN clinician ON clinicianhospitalsite.clinicianId=clinician.clinicianId
				INNER JOIN student on rotationdetails.studentId = student.studentId";

		$sql .= " WHERE rotation.schoolId=" . $SchoolId . " AND rotation.isDelete =0 AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)";
		$sql .= " AND clinician.clinicianId=" . $clinicianId;

		if ($rotationId) {
			$sql .= " AND rotation.rotationId=" . $rotationId;
		}
		if ($to_date != '') {
			$sql .= " AND date(rotation.endDate) <= '" . $to_date . "'";
		}
		if ($from_date != '') {
			$sql .= " AND date(rotation.endDate) >= '" . $from_date . "'";
		}

		if ($studentId) {
			$sql .= " AND rotationdetails.studentId=" . $studentId;
		}

		if ($hospitalsiteId) {
			$sql .= " AND rotation.hospitalsiteId=" . $hospitalsiteId;
		}

		// $sql .= " AND CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate)";
		$sql .= " 
				AND (
					(CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate) AND isSchedule = 0)
					OR
					(rotation.parentRotationId > 0
					AND EXISTS (
						SELECT 1
						FROM rotation AS parent
						WHERE parent.rotationId = rotation.parentRotationId
						AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
						AND rotation.startDate BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
						AND rotation.endDate BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
						AND parent.schoolId = " . $SchoolId . " AND rotation.isSchedule = 1
						AND DATE_FORMAT(rotation.startDate, '%Y-%m-%d') <= '" . $todaysDate . "'    

					)
					)
		)";
		$sql .= " GROUP BY rotation.rotationId ORDER BY `rotation`.`title` ASC";

		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	function GetRotationForClinicianBySchoolId($SchoolId, $rotationId, $from_date, $to_date, $studentId = 0, $hospitalsiteId = 0)
	{
		// echo $isActive;
		$objDB = new clsDB();
		$rows = "";
		$curdate = date("y-m-d h:i:s");
		$todaysDate = date("Y-m-d");
		$repeatday = date('w', strtotime($curdate));

		$sql = " SELECT rotation.rotationId, rotation.title  as rotationName, rotation.startDate, rotation.endDate,
		rotation.parentRotationId, rotation.duration, rotation.courseId, rotation.hospitalSiteId, rotation.isSchedule,
		courses.courseId, courses.title as courseTitle,courses.courseHours,hospitalsites.title as hospitalTitle,
				location.locationId, location.title as locationTitle, 
				IFNULL(count(rotationdetails.rotationId),0) as studentCount	,
				(select count(*) from rotation SUBR WHERE SUBR.parentRotationId= rotation.rotationId) AS subratoncount ,
				rotation.locationId as rotationLocationId
				FROM rotation
    			LEFT JOIN courses ON rotation.courseId = courses.courseId
        		LEFT JOIN location ON courses.locationId = location.locationId
        		INNER JOIN hospitalsites  ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
        		INNER JOIN rotationdetails ON rotationdetails.rotationId = rotation.rotationId
				INNER JOIN student on rotationdetails.studentId = student.studentId";


		$sql .= " WHERE rotation.schoolId=" . $SchoolId . " 
					AND rotation.isDelete =0 AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)";

		if ($rotationId) {
			$sql .= " AND rotation.rotationId=" . $rotationId;
		}
		if ($to_date != '') {
			$sql .= " AND date(rotation.endDate) <= '" . $to_date . "'";
		}
		if ($from_date != '') {
			$sql .= " AND date(rotation.endDate) >= '" . $from_date . "'";
		}

		if ($studentId) {
			$sql .= " AND rotationdetails.studentId=" . $studentId;
		}

		if ($hospitalsiteId) {
			$sql .= " AND rotation.hospitalsiteId=" . $hospitalsiteId;
		}

		$sql .= " 
			AND (
						  (CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate) AND isSchedule = 0)
						  OR
						  (rotation.parentRotationId > 0
						   AND EXISTS (
							   SELECT 1
							   FROM rotation AS parent
							   WHERE parent.rotationId = rotation.parentRotationId
							   AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
							   AND rotation.startDate BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
							   AND rotation.endDate BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
							   AND parent.schoolId = " . $SchoolId . " AND rotation.isSchedule = 1
						       AND DATE_FORMAT(rotation.startDate, '%Y-%m-%d') <= '" . $todaysDate . "'    
						   )
						  )
					  )";

		$sql .= " GROUP BY rotation.rotationId ORDER BY `rotation`.`title` ASC";

		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}


	function CopyOfOldRotations($rotationId)
	{
		$objDB = new clsDB();
		$CurrentRotationId = 0;
		$rowsRotationDetails = "";
		$currentcopiedrotationId = "";
		// For Rotation Copy
		$sql = "INSERT INTO rotation (parentRotationId, schoolId,  title, 
				courseId, startDate, endDate,hospitalSiteId,locationId, createdBy,duration,createdDate
				) VALUES (
				'" . addslashes($this->parentRotationId) . "', 
				'" . addslashes($this->schoolId) . "',
				'" . addslashes($this->title) . "',
				'" . addslashes($this->courseId) . "', 
				'" . addslashes($this->startDate) . "',
				'" . addslashes($this->endDate) . "',
				'" . addslashes($this->hospitalSiteId) . "',
				'" . addslashes($this->locationId) . "',
				'" . addslashes($this->createdBy) . "',
				'" . addslashes($this->duration) . "',
				'" . (date("Y-m-d h:i:s")) . "'								 
				)";

		$CurrentRotationId = $objDB->ExecuteInsertQuery($sql);
		// //copy rotation details
		// $sql ="insert into rotationdetails (studentId,schoolClinicalSiteUnitId,rotationId)
		// 		select studentId,schoolClinicalSiteUnitId,".$CurrentRotationId."
		// 		from rotationdetails
		// 		where rotationId =".$rotationId;

		//$CurrentDetailsRotationId = $objDB->ExecuteInsertQuery($sql);

		//copy rotation repeat days
		$sql = "insert into rotationrepeatdays (rotationId,dayOfWeek)
				select " . $CurrentRotationId . ",dayOfWeek
				from rotationrepeatdays
				where rotationId =" . $rotationId;

		$CurrentRotationRepeatDaysId = $objDB->ExecuteInsertQuery($sql);

		$subrotationSql = "SELECT " . $CurrentRotationId . ", rotationId as OldSubrotationId ,schoolId,  title,hospitalSiteId,locationId, courseId, startDate,
								endDate,createdBy,createdDate,duration FROM rotation
				WHERE parentRotationId=" . $rotationId . " and isDelete = 0";

		$rowsSubRotation = $objDB->GetResultset($subrotationSql);
		if ($rowsSubRotation != "") {
			while ($row = mysqli_fetch_array($rowsSubRotation)) {
				$this->parentRotationId = $CurrentRotationId;
				$OldSubrotationId = $row['OldSubrotationId'];
				$this->schoolId = $row['schoolId'];
				$this->title = 'Copy of ' . $row['title'];
				$this->hospitalSiteId = $row['hospitalSiteId'];
				$this->locationId = $row['locationId'];
				$this->courseId = $row['courseId'];
				$this->startDate;
				$this->endDate;
				$this->createdBy = $row['createdBy'];
				$this->createdDate = $row['createdDate'];
				$this->duration = $row['duration'];

				$this->SaveSubRotation($CurrentRotationId, $rotationId, $OldSubrotationId);
			}
		}



		unset($objDB);
		return $CurrentRotationId;
	}
	function SaveSubRotation($CurrentRotationId, $rotationId, $OldSubrotationId)
	{
		$objDB = new clsDB();
		$sql = "INSERT INTO rotation (parentRotationId, schoolId,  title,hospitalSiteId,locationId, 
								 courseId,startDate,endDate,createdBy,createdDate,duration) 
					VALUES 
				('" . ($CurrentRotationId) . "',
                '" . ($this->schoolId) . "',
                '" . ($this->title) . "',
                '" . ($this->hospitalSiteId) . "',
                '" . ($this->locationId) . "',
                '" . ($this->courseId) . "',
                '" . ($this->startDate) . "',
                '" . ($this->endDate) . "',
                '" . ($this->createdBy) . "',
                '" . ($this->createdDate) . "',
                '" . ($this->duration) . "'
                )";

		$SubRotationId = $objDB->ExecuteInsertQuery($sql);

		// 		$sql ="insert into rotationdetails (studentId,schoolClinicalSiteUnitId,rotationId)
		// 				select studentId,schoolClinicalSiteUnitId,".$SubRotationId."
		// 				from rotationdetails						
		// 				where rotationId =".$OldSubrotationId;
		// 		//echo '<hr>'.$sql; 
		// $CurrentDetailsSubRotationId = $objDB->ExecuteInsertQuery($sql);
		unset($objDB);
	}


	function GetAllRepeatDays()
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM rotationrepeatdaysmaster
			 Group By title  ORDER BY rotationRepeatDaysMasterId";
		//echo $sql;		 
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function CopyRemainingRotationRepeatDays()
	{
		$objDB = new clsDB();
		$rowsRotation = '';
		$sql = "SELECT rotation.rotationId 
						FROM rotation 
						LEFT JOIN rotationrepeatdays ON rotation.rotationId = rotationrepeatdays.rotationId
                        WHERE rotationrepeatdays.rotationId IS NULL
						GROUP BY rotation.rotationId";
		//echo $sql;exit;
		$rowsRotation = $objDB->GetResultset($sql);
		if ($rowsRotation != "") {
			while ($row = mysqli_fetch_array($rowsRotation)) {
				$remainrotationId = $row['rotationId'];
				$sql = "SELECT * FROM `rotationrepeatdaysmaster` 
										GROUP BY rotationRepeatDaysMasterId";
				$rowsRepeatDays = $objDB->GetResultset($sql);
				while ($rows = mysqli_fetch_array($rowsRepeatDays)) {
					$value = $rows['value'];
					$this->rotationId = $remainrotationId;
					$this->dayOfWeek = $value;
					$this->SaveRotationRepeatDays();
				}
			}
		}
		unset($objDB);
	}

	function SaveRotationRepeatDays()
	{
		$objDB = new clsDB();
		$sql = '';
		$sql = "INSERT INTO rotationrepeatdays (rotationId,dayOfWeek) 
							VALUES (
								 '" . addslashes($this->rotationId) . "', 
								 '" . addslashes($this->dayOfWeek) . "'
								 )";

		$rotationRepeatDaysId = $objDB->ExecuteInsertQuery($sql);
		unset($objDB);
		return $rotationRepeatDaysId;
	}

	function GetRotation($schoolId, $studentId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT rotation.*, rotationdetails.*,
				courses.title as courseTitle,location.locationId, location.title as locationTitle,
				schools.schoolId,student.*,
				hospitalsites.hospitalSiteId,hospitalsites. title as hospitalTitle, attendance.rotationId as clockedOutRotationId, CONCAT(rotation.title, ' (', hospitalsites.title, ')') as title,
				 rotation.rotationId
				FROM rotation
				INNER JOIN schools ON rotation.schoolId=schools.schoolId
				LEFT JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
				LEFT JOIN  student ON rotationdetails.studentId =student.studentId
				LEFT JOIN courses ON rotation.courseId = courses.courseId
				LEFT JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
				LEFT JOIN location ON courses.locationId = location.locationId
				LEFT JOIN attendance ON rotation.rotationId = attendance.rotationId AND attendance.isClockOut = 1
				WHERE schools.schoolId=" . $schoolId . " ";
		// AND CURDATE() BETWEEN date(rotation.startDate) AND date(rotation.endDate)";

		if ($studentId > 0) {
			$sql .= " AND rotationdetails.studentId=" . $studentId;
		}

		// 			$sql .= " AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)";	
		// $sql .=	" AND rotation.isDelete =0  GROUP BY rotation.rotationId";
		$sql .= " AND rotation.isDelete =0 AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId) 
		/*AND (
              (CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
              OR
              (rotation.parentRotationId > 0
               AND EXISTS (
                   SELECT 1
                   FROM rotation AS parent
                   WHERE parent.rotationId = rotation.parentRotationId
                   AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
                   AND parent.schoolId = " . $schoolId . " AND rotation.isSchedule = 1 
               )
              )
          )*/
		";
		$sql .= " GROUP BY rotation.rotationId ORDER BY rotation.title";
		// ECHO $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetRotationNameById($rotationId)
	{
		$objDB = new clsDB();
		$rows = "";
		$rotationId = str_replace(" ", ",", $rotationId);
		$sql = "SELECT Group_concat(title) FROM rotation where rotationId IN ($rotationId) ";
		$rows = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $rows;
	}

	function GetTopicNameByRotation($rotationId, $studentId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT schooltopicmaster.*,checkoff.checkoffId, rotation.rotationId
				FROM schooltopicmaster 
				INNER JOIN coursetopicdetail ON schooltopicmaster.schoolTopicId=coursetopicdetail.schoolTopicId
				INNER JOIN courses ON coursetopicdetail.courseId=courses.courseId
				INNER JOIN rotation ON rotation.courseId=courses.courseId 
				left JOIN checkoff ON schooltopicmaster.schoolTopicId =checkoff.schoolTopicId
				WHERE rotation.rotationId= " . $rotationId . "
				AND  EXISTS (select checkoff.schoolTopicId from checkoff where schooltopicmaster.schoolTopicId =checkoff.schoolTopicId AND checkoff.studentId ='" . ($studentId) . "' AND checkoff.rotationId = " . $rotationId . ")				
				GROUP BY `schooltopicmaster`.`checkoffTitleId` ASC
				ORDER BY `schooltopicmaster`.`checkoffTitleId` ASC ";
		//echo $sql;		
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllRotationByStudent($schoolId, $studentId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT rotation.rotationId,rotation.title
				FROM rotation
				LEFT JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
				LEFT JOIN  student ON rotationdetails.studentId =student.studentId
				WHERE rotationdetails.studentId=" . $studentId . " AND rotation.schoolId=" . $schoolId;
		$sql .= " GROUP BY rotation.rotationId";

		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetCountForRotationByStudent($studentId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT count(rotationId) from rotationdetails WHERE rotationdetails.studentId=" . $studentId;
		$rows = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $rows;
	}

	function GetCountRotationByStudent($rotationId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT count(rotationId) FROM `rotationdetails` INNER JOIN student on rotationdetails.studentId = student.studentId WHERE rotationdetails.rotationId =" . $rotationId . " AND student.isActive = 1";
		$rows = $objDB->GetSingleFieldValue($sql);

		unset($objDB);
		return $rows;
	}

	function GetCountSchedule($rotationId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT count(rotationId) FROM `rotation` WHERE `parentRotationId` =" . $rotationId . " AND isSchedule=1";
		$rows = $objDB->GetSingleFieldValue($sql);
		// echo $sql;
		unset($objDB);
		return $rows;
	}

	function GetLocationByRotation($rotationId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT location.locationId from rotation 
		LEFT JOIN courses ON rotation.courseId = courses.courseId
		LEFT JOIN location ON courses.locationId = location.locationId
		WHERE rotation.rotationId=" . $rotationId;

		$rows = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllStudentAttendance($rotationId, $studentId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM attendance WHERE attendance.studentId=" . $studentId . " AND rotation.rotationId=" . $rotationId;

		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllRotationForStudent($schoolId, $studentId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT rotation.*, rotationdetails.*,
				courses.title as courseTitle,location.locationId, location.title as locationTitle,
				schools.schoolId,student.*,
				hospitalsites.hospitalSiteId,hospitalsites. title as hospitalTitle, attendance.rotationId as clockedOutRotationId,CONCAT(rotation.title, ' (', hospitalsites.title, ')') as title
				FROM rotation
				INNER JOIN schools ON rotation.schoolId=schools.schoolId
				LEFT JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
				LEFT JOIN  student ON rotationdetails.studentId =student.studentId
				LEFT JOIN courses ON rotation.courseId = courses.courseId
				LEFT JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
				LEFT JOIN location ON courses.locationId = location.locationId
				LEFT JOIN attendance ON rotation.rotationId = attendance.rotationId AND attendance.isClockOut = 1
		    	WHERE schools.schoolId=" . $schoolId . " ";
		// AND CURDATE() BETWEEN date(rotation.startDate) AND date(rotation.endDate)";
		$sql .= " AND rotation.isDelete =0 AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId) 
				-- AND (
				-- 	  (CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
				-- 	  OR
				-- 	  (rotation.parentRotationId > 0
				-- 	   AND EXISTS (
				-- 		   SELECT 1
				-- 		   FROM rotation AS parent
				-- 		   WHERE parent.rotationId = rotation.parentRotationId
				-- 		   AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
				-- 		   AND parent.schoolId = " . $schoolId . " AND rotation.isSchedule = 1 
				-- 	   )
				-- 	  )
				--   )
				";
		if ($studentId > 0) {
			$sql .= " AND rotationdetails.studentId=" . $studentId . " and date(rotation.startDate) < CURDATE()";
		}
		$sql .= " GROUP BY rotation.rotationId ORDER BY rotation.title";
		// 			$sql .=" AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)";
		// $sql .=	" AND rotation.isDelete =0  GROUP BY rotation.rotationId";
		// $sql .= " ORDER By CURDATE() BETWEEN date(rotation.startDate) AND date(rotation.endDate) DESC";
		//ECHO $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function DeleteParentrotation($rotationId, $deletedUserId, $title)
	{
		$result = "";
		$word = " Deleted";
		if (strpos($title, $word) !== false) {
			$originalTitle = $title;
		} else {
			$originalTitle = $title . " Deleted";
		}

		if ($rotationId > 0) {
			$objDB = new clsDB();
			$sqlsub = "UPDATE rotation SET " . " isDelete=1, deleteDate =  '" . (date("Y-m-d h:i:s")) . "', title = '" . $originalTitle . "', deletedUserId = " . $deletedUserId . "
						   WHERE  parentRotationId = " . $rotationId;

			// echo $sqlsub;exit;
			$SubRotationId = $objDB->ExecuteQuery($sqlsub);
			unset($objDB);
		}
		return $result;
	}

	function GetAllActiveRotation($schoolId, $studentId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT rotation.*, rotationdetails.*,
				courses.title as courseTitle,location.locationId, location.title as locationTitle,
				schools.schoolId,student.*,
				hospitalsites.hospitalSiteId,hospitalsites. title as hospitalTitle, attendance.rotationId as clockedOutRotationId, CONCAT(rotation.title, ' (', hospitalsites.title, ')') as title
				FROM rotation
				INNER JOIN schools ON rotation.schoolId=schools.schoolId
				LEFT JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
				LEFT JOIN  student ON rotationdetails.studentId =student.studentId
				LEFT JOIN courses ON rotation.courseId = courses.courseId
				LEFT JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
				LEFT JOIN location ON courses.locationId = location.locationId
				LEFT JOIN attendance ON rotation.rotationId = attendance.rotationId AND attendance.isClockOut = 1
				WHERE schools.schoolId=" . $schoolId . "";
		//  AND CURDATE() BETWEEN date(rotation.startDate) AND date(rotation.endDate)";
		$sql .= " AND rotation.isDelete =0 AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId) 
				AND (
					  (CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
					  OR
					  (rotation.parentRotationId > 0
					   AND EXISTS (
						   SELECT 1
						   FROM rotation AS parent
						   WHERE parent.rotationId = rotation.parentRotationId
						   AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
						   AND parent.schoolId = " . $schoolId . " AND rotation.isSchedule = 1 
					   )
					  )
				  )
				";
		if ($studentId > 0) {
			$sql .= " AND rotationdetails.studentId=" . $studentId;
		}

		// // 			$sql .= " AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)";
		// $sql .=	" AND rotation.isDelete =0  GROUP BY rotation.rotationId";
		// $sql .= " ORDER By CURDATE() BETWEEN date(rotation.startDate) AND date(rotation.endDate) DESC";
		$sql .= " GROUP BY rotation.rotationId ORDER BY rotation.title";
		// ECHO $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllDisplayrotation($schoolId, $locationId = 0, $courseId = 0, $rotationId) //A: admin 
	{
		$objDB = new clsDB();
		$rows = "";

		$sql = " SELECT rotation.*, courses.courseId, courses.title as courseTitle,courses.courseHours,
		location.locationId, location.title as locationTitle, IFNULL(count(rotationdetails.rotationId),0) as studentCount,
		(select count(*) from rotation SUBR WHERE SUBR.parentRotationId= rotation.rotationId) AS subratoncount,
		hospitalsites.title as hospitalSite, rotation.isdelete
		FROM rotation
		LEFT JOIN courses ON rotation.courseId = courses.courseId
		LEFT JOIN hospitalsites  ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
		LEFT JOIN location ON courses.locationId = location.locationId
		LEFT JOIN rotationdetails ON rotationdetails.rotationId = rotation.rotationId";

		$sql .= " WHERE rotation.schoolId=" . $schoolId . " AND rotation.parentRotationId= 0";
		if ($locationId > 0) //Clinician
		{
			$sql .= " AND courses.locationId=" . $locationId;
		}
		if ($courseId > 0) //course filter
		{
			$sql .= " AND rotation.courseId=" . $courseId;
		}

		$sql .= " GROUP BY rotation.rotationId ORDER BY  rotation.isdelete ASC, rotation.title asc";

		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		// echo $sql;
		return $rows;
	}

	// Active rotations + Expired + Deleted rotations show in admin 
	function GetAllActiverotationByAdmin($schoolId, $locationId = 0, $courseId = 0, $rotationId, $from_date = '', $to_date = '') //A: admin / C:Clinician
	{
		$objDB = new clsDB();
		$rows = "";
		$CurrentDate = date('Y-m-d H:i:s');
		$sql = " SELECT rotation.*, courses.courseId, courses.title as courseTitle,courses.courseHours,
		location.locationId, location.title as locationTitle, IFNULL(count(rotationdetails.rotationId),0) as studentCount,
		(select count(*) from rotation SUBR WHERE SUBR.parentRotationId= rotation.rotationId) AS subratoncount,
		hospitalsites.title as hospitalSite
		FROM rotation
		LEFT JOIN courses ON rotation.courseId = courses.courseId
		LEFT JOIN hospitalsites  ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
		LEFT JOIN location ON courses.locationId = location.locationId
		LEFT JOIN rotationdetails ON rotationdetails.rotationId = rotation.rotationId ";

		$sql .= " WHERE rotation.schoolId=" . $schoolId . " AND rotation.parentRotationId= 0 AND rotation.isDelete= 0";

		if ($locationId > 0) //Clinician
		{
			$sql .= " AND courses.locationId=" . $locationId;
		}
		if ($courseId > 0) //course filter
		{
			$sql .= " AND rotation.courseId=" . $courseId;
		}

		if ($to_date != '' && $from_date != '') {
			$sql .= " AND (('" . date("Y-m-d", strtotime($from_date)) . "' BETWEEN date(rotation.startDate) AND date(rotation.endDate)) 
			OR ('" . date("Y-m-d", strtotime($to_date)) . "' BETWEEN date(rotation.startDate) AND date(rotation.endDate)))";
		} else {
			$sql .= " AND '" . date("Y-m-d", strtotime($CurrentDate)) . "' <= Date(rotation.endDate)";
		}

		$sql .= " GROUP BY rotation.rotationId ORDER BY rotation.title asc";
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);

		return $rows;
	}
	function Undeleterotation($rotationId, $deletedUserId)
	{
		$objDB = new clsDB();
		$sql = '';

		if ($rotationId > 0) {
			$sql = "UPDATE rotation SET 
					isDelete = '0'
					Where rotationId= " . $rotationId;

			$objDB->ExecuteQuery($sql);
		}
		unset($objDB);
		return $rotationId;
	}

	function UndeleteParentRotation($rotationId, $deletedUserId)
	{
		$objDB = new clsDB();
		$sql = '';

		if ($rotationId > 0) {
			$sql = "UPDATE rotation SET 
					isDelete = '0'
					Where parentRotationId= " . $rotationId;
			// echo $sql;exit;
			$objDB->ExecuteQuery($sql);
		}
		unset($objDB);
		return $rotationId;
	}

	function GetrotationTitle($rotationId, $schoolId)
	{

		$objDB = new clsDB();
		$sql = "SELECT title FROM  rotation WHERE rotationId=" . $rotationId . " AND schoolId =" . $schoolId;
		//echo $sql;
		$row = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $row;
	}

	function GetRotationBySchoolByisPeerToPeer($schoolId)
	{
		$objDB = new clsDB();
		$rows = "";

		$sql = "SELECT rotation.rotationId,rotation.title,rotation.startDate,hospitalsites.isPeerToPeer FROM rotation 
			   LEFT JOIN hospitalsites  ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId

			   WHERE rotation.schoolId=" . $schoolId . " AND rotation.isDelete =0 AND
			   hospitalsites.isPeerToPeer =1 AND 
				0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)
			   ORDER BY title";

		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	// function GetStudentClockInRotations1($studentId, $schoolId)
	// {
	// 	$objDB = new clsDB();
	// 	$rows = "";

	// 	$CurrentDate = date("Y-m-d h:i:s");
	// 	$todaysDate = date("Y-m-d");
	// 	$repeatday = date('w', strtotime($CurrentDate));

	// 	// AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)
	// 	//CLOCKOUT
	// 	$sql = "SELECT rotation.*,
	// 			courses.title as courseTitle, 
	// 			hospitalsites. title as hospitalTitle, 0 as isUpcoming ,0 as isClockIn,
	// 			courses.locationId,
	// 			rotation.locationId as rotationLocationId
	// 			FROM rotation
	// 			INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
	// 			INNER JOIN courses ON rotation.courseId = courses.courseId
	// 			INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
	// 			INNER JOIN rotationrepeatdays ON rotation.rotationId=rotationrepeatdays.rotationId
	// 						AND rotationrepeatdays.dayOfWeek=" . $repeatday . "
	// 			WHERE  rotationdetails.studentId=" . $studentId . " 
	// 			AND rotation.isDelete =0  
	// 			 ";

	// 	$sql .= " AND CURDATE() BETWEEN date(rotation.startDate) AND date(rotation.endDate)";


	// 	$sql .= " and rotation.schoolId=" . $schoolId . " AND rotation.isSchedule = 0  GROUP BY rotation.rotationId";

	// 	$sql .= " UNION ";
	// 	//UPCOMING
	// 	$sql .= "SELECT rotation.*,
	// 			courses.title as courseTitle, 
	// 			hospitalsites. title as hospitalTitle, 1 as isUpcoming ,0 as isClockIn,
	// 			courses.locationId,
	// 			rotation.locationId as rotationLocationId
	// 			FROM rotation
	// 			INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
	// 			INNER JOIN courses ON rotation.courseId = courses.courseId
	// 			INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId

	// 			WHERE  rotationdetails.studentId=" . $studentId . " 
	// 			AND rotation.isDelete =0  
	// 			 ";

	// 	$sql .= " AND  date(rotation.startDate) > CURDATE()";


	// 	$sql .= " and rotation.schoolId=" . $schoolId . "  GROUP BY rotation.rotationId";

	// 	// schedule
	// 	$sql .= " UNION ";

	// 	$sql .= "SELECT rotation.*,
	// 	  courses.title as courseTitle, 
	// 	  hospitalsites. title as hospitalTitle, 0 as isUpcoming ,0 as isClockIn,
	// 	  courses.locationId,
	// 	  rotation.locationId as rotationLocationId
	// 	  FROM rotation
	// 	  INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
	// 	  INNER JOIN courses ON rotation.courseId = courses.courseId
	// 	  INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
	// 	  INNER JOIN rotationrepeatdays ON rotation.rotationId=rotationrepeatdays.rotationId
	// 				  AND rotationrepeatdays.dayOfWeek=" . $repeatday . "
	// 	INNER JOIN rotationscheduledates ON rotation.rotationId = rotationscheduledates.rotationId
	// 	  WHERE  rotationdetails.studentId=" . $studentId . "  AND rotation.isSchedule = 1 AND DATE_FORMAT(scheduleDate, '%Y-%m-%d') = '" . $todaysDate . "'
	// 	  AND rotation.isDelete =0  
	// 	   ";

	// 	$sql .= " AND CURDATE() BETWEEN date(rotation.startDate) AND date(rotation.endDate)";


	// 	$sql .= " and rotation.schoolId=" . $schoolId . "  GROUP BY rotation.rotationId";

	// 	// PM to AM
	// 	$sql .= " UNION ";

	// 	$sql .= "SELECT 
	// 	rotation.*, 
	// 	courses.title as courseTitle, 
	// 	hospitalsites.title as hospitalTitle, 
	// 	0 as isUpcoming,
	// 	0 as isClockIn, 
	// 	courses.locationId, 
	// 	rotation.locationId as rotationLocationId 
	// FROM 
	// 	rotation 
	// INNER JOIN 
	// 	rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
	// INNER JOIN 
	// 	courses ON rotation.courseId = courses.courseId 
	// INNER JOIN 
	// 	hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId 
	// INNER JOIN 
	// 	rotationrepeatdays ON rotation.rotationId = rotationrepeatdays.rotationId 

	// WHERE 
	// rotationdetails.studentId=" . $studentId . " 
	// 	AND CURDATE() BETWEEN date(rotation.startDate) AND date(rotation.endDate) 
	// 	AND rotation.isDelete = 0  AND rotation.isSchedule = 1
	// 	AND (
	// 		TIME(rotation.startDate) >= TIME('12:00:00') 
	// 		AND TIME(rotation.endDate) <= TIME('11:59:59') 
	// 	)
	// 	AND rotation.schoolId=" . $schoolId . "

	// GROUP BY 
	// 	rotation.rotationId";


	// 	echo $sql;
	// 	$rows = $objDB->GetResultset($sql);
	// 	unset($objDB);
	// 	return $rows;
	// }
	function GetStudentClockInRotations1($studentId, $schoolId)
	{
		$objDB = new clsDB();
		$rows = "";

		$CurrentDate = date("Y-m-d h:i:s");
		// $todaysDate = '2023-11-15';
		$todaysDate = date("Y-m-d");
		$repeatday = date('w', strtotime($CurrentDate));
		// echo $repeatday = date('w', strtotime($todaysDate));

		// AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)
		// CLOCKOUT
		$sql = "SELECT rotation.*,
				courses.title as courseTitle,courses.courseHours, 
				hospitalsites.title as hospitalTitle, 
				0 as isUpcoming,
				0 as isClockIn,
				courses.locationId,
				rotation.locationId as rotationLocationId
				FROM rotation
				INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
				INNER JOIN courses ON rotation.courseId = courses.courseId
				INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
				INNER JOIN rotationrepeatdays ON rotation.rotationId = rotationrepeatdays.rotationId
					AND rotationrepeatdays.dayOfWeek = " . $repeatday . "
				WHERE rotationdetails.studentId = " . $studentId . " 
				AND rotation.isDelete = 0  
				";

		$sql .= " AND '" . $todaysDate . "' BETWEEN date(rotation.startDate) AND date(rotation.endDate)";


		$sql .= " and rotation.schoolId = " . $schoolId . " AND rotation.isSchedule = 0  GROUP BY rotation.rotationId";

		$sql .= " UNION DISTINCT ";

		// UPCOMING
		$sql .= "SELECT rotation.*,
				courses.title as courseTitle, courses.courseHours,
				hospitalsites.title as hospitalTitle, 
				1 as isUpcoming,
				0 as isClockIn,
				courses.locationId,
				rotation.locationId as rotationLocationId
				FROM rotation
				INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
				INNER JOIN courses ON rotation.courseId = courses.courseId
				INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
	
				WHERE rotationdetails.studentId = " . $studentId . " 
				AND rotation.isDelete = 0  
				";

		$sql .= " AND date(rotation.startDate) > '" . $todaysDate . "'";


		$sql .= " and rotation.schoolId = " . $schoolId . "  GROUP BY rotation.rotationId";

		// SCHEDULE and PM to AM combined subquery
		$sql .= " UNION DISTINCT ";

		$sql .= "SELECT DISTINCT * FROM (";
		$sql .= "SELECT rotation.*,
			courses.title as courseTitle,courses.courseHours, 
			hospitalsites.title as hospitalTitle, 
			0 as isUpcoming,
			0 as isClockIn,
			courses.locationId as courseLocationId, 
			rotation.locationId as rotationLocationId
			FROM rotation
			INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
			INNER JOIN courses ON rotation.courseId = courses.courseId
			INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
			INNER JOIN rotationrepeatdays ON rotation.rotationId = rotationrepeatdays.rotationId
			AND rotationrepeatdays.dayOfWeek = " . $repeatday . "
			INNER JOIN rotationscheduledates ON rotation.rotationId = rotationscheduledates.rotationId
			WHERE rotationdetails.studentId = " . $studentId . " 
			AND rotation.isSchedule = 1 AND DATE_FORMAT(scheduleDate, '%Y-%m-%d') = '" . $todaysDate . "'
			AND rotation.isDelete = 0  
			";

		$sql .= " AND '" . $todaysDate . "' BETWEEN date(rotation.startDate) AND date(rotation.endDate)";


		$sql .= " and rotation.schoolId = " . $schoolId . "  GROUP BY rotation.rotationId";

		$sql .= " UNION DISTINCT ";

		// PM to AM
		$sql .= "SELECT rotation.*, 
			courses.title as courseTitle, courses.courseHours,
			hospitalsites.title as hospitalTitle, 
			0 as isUpcoming, 
			0 as isClockIn, 
			courses.locationId as courseLocationId, 
			rotation.locationId as rotationLocationId 
			FROM rotation 
			INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
			INNER JOIN courses ON rotation.courseId = courses.courseId 
			INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId 
			INNER JOIN rotationrepeatdays ON rotation.rotationId = rotationrepeatdays.rotationId
	
			WHERE rotationdetails.studentId = " . $studentId . " 
			AND '" . $todaysDate . "' BETWEEN date(rotation.startDate) AND DATE_ADD(date(rotation.endDate), INTERVAL 1 DAY) 
			AND rotation.isDelete = 0  AND rotation.isSchedule = 1
			AND (
				TIME(rotation.startDate) >= TIME('12:00:00') 
				AND TIME(rotation.endDate) <= TIME('11:59:59') 
			)
			AND rotation.schoolId = " . $schoolId . "
			
			GROUP BY rotation.rotationId
		) AS combined_subquery";

		// Debugging
		// echo $sql;

		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllClinicianWiseRotation($schoolId, $locationId = 0, $courseId = 0, $rotationId, $clinicianId) //A: admin / C:Clinician
	{
		$objDB = new clsDB();
		$rows = "";

		$sql = " SELECT rotation.*, courses.courseId, courses.title as courseTitle,
		location.locationId, location.title as locationTitle, IFNULL(count(rotationdetails.rotationId),0) as studentCount,
		(select count(*) from rotation SUBR WHERE SUBR.parentRotationId= rotation.rotationId) AS subratoncount,
		hospitalsites.title as hospitalSite
		FROM rotation
		LEFT JOIN courses ON rotation.courseId = courses.courseId
		LEFT JOIN hospitalsites  ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
		LEFT JOIN location ON courses.locationId = location.locationId
		LEFT JOIN rotationdetails ON rotationdetails.rotationId = rotation.rotationId
		INNER JOIN clinicianhospitalsite ON rotation.hospitalSiteId=clinicianhospitalsite.hospitalSiteId
		INNER JOIN clinician ON clinicianhospitalsite.clinicianId=clinician.clinicianId";

		$sql .= " WHERE rotation.schoolId=" . $schoolId;

		$sql .= " AND rotation.isDelete =0 AND clinician.clinicianId=" . $clinicianId;

		if ($locationId > 0) //Clinician
		{
			$sql .= " AND courses.locationId=" . $locationId;
		}
		if ($courseId > 0) //course filter
		{
			$sql .= " AND rotation.courseId=" . $courseId;
		}


		$sql .= " GROUP BY rotation.rotationId ORDER BY rotation.title";
		//	echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);

		return $rows;
	}

	function GetAllrotationByHospitalWiseClinician($schoolId, $hospitalSiteId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = " SELECT rotation.*, courses.courseId, courses.title as courseTitle,clinician.clinicianId,
		location.locationId, location.title as locationTitle, IFNULL(count(rotationdetails.rotationId),0) as studentCount,
		(select count(*) from rotation SUBR WHERE SUBR.parentRotationId= rotation.rotationId) AS subratoncount
		FROM rotation
		LEFT JOIN courses ON rotation.courseId = courses.courseId
		LEFT JOIN location ON courses.locationId = location.locationId
		LEFT JOIN rotationdetails ON rotationdetails.rotationId = rotation.rotationId 
		INNER JOIN clinicianhospitalsite ON rotation.hospitalSiteId=clinicianhospitalsite.hospitalSiteId
		INNER JOIN clinician ON clinicianhospitalsite.clinicianId=clinician.clinicianId
		";

		$sql .= " WHERE rotation.schoolId=" . $schoolId;
		$sql .= " AND rotation.hospitalSiteId=" . $hospitalSiteId;
		$sql .= " AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId) ";
		$sql .= " GROUP BY rotation.rotationId ORDER BY rotation.title";
		//echo $sql; exit;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetIsPeerHospitalSite($schoolId, $locationId, $studentId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT hospitalsites.isPeerToPeer FROM student 
		INNER JOIN rotationdetails ON rotationdetails.studentId = student.studentId
		INNER JOIN rotation ON rotationdetails.rotationId = rotation.rotationId
		INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
		WHERE student.studentId=" . $studentId . " AND rotation.schoolId=" . $schoolId . " AND rotation.locationId=" . $locationId . "
		GROUP BY student.studentId";
		// echo $sql;exit;
		$rows = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $rows;
	}

	function GetIsLabHospitalSite($schoolId, $locationId, $studentId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT hospitalsites.hospitalSiteId FROM student 
		INNER JOIN rotationdetails ON rotationdetails.studentId = student.studentId
		INNER JOIN rotation ON rotationdetails.rotationId = rotation.rotationId
		INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
		WHERE student.studentId=" . $studentId . " AND rotation.schoolId=" . $schoolId . " AND rotation.locationId=" . $locationId . "
		AND hospitalsites.hospitalSiteId=897 GROUP BY student.studentId";
		// 		echo $sql;exit;
		$rows = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllSubRotationDetails($rotationId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "select * from rotation where parentRotationId=" . $rotationId;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllAssignedRotations($schoolId, $isActiveRotations = 0, $studentId = '', $rotationId = 0, $subcborotation = '', $sortorder = 0, $ascdesc = '')
	{
		$studentIds = '';
		// echo 'sortorder'.$sortorder;
		if ($studentId)
			$studentIds = implode(",", unserialize($studentId));

		$subcborotation = ($subcborotation != '') ? str_replace(" ", ",", $subcborotation) : '';

		$objDB = new clsDB();
		$rows = "";
		$sql = "select rotation.rotationId,rotation.title,rotation.startDate,rotation.endDate,
		GROUP_CONCAT(rotationrepeatdays.dayOfWeek) as dayOfWeek,GROUP_CONCAT(DISTINCT rotationdetails.studentId,'~',student.firstName,'~',student.lastName,'~',rankmaster.title) as students  
		,student.firstName,student.lastName,rankmaster.title AS Ranktitle,rotationdetails.studentId,hospitalsites.title as hospitalTitle from rotation 
		inner join rotationdetails on rotationdetails.rotationId=rotation.rotationId
		inner join rotationrepeatdays on rotationrepeatdays.rotationId=rotation.rotationId
		inner join student on rotationdetails.studentId=student.studentId
		inner JOIN rankmaster ON student.rankId=rankmaster.rankId
		INNER JOIN hospitalsites ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
		where rotation.schoolId=" . $schoolId;

		if ($studentIds != '')
			$sql .= " AND student.studentId IN ($studentIds)";

		if ($subcborotation != '')
			$sql .= " AND (rotation.rotationId IN ($subcborotation) OR rotation.rotationId=" . $rotationId . " )";
		else if ($rotationId > 0)
			$sql .= " AND rotation.rotationId=" . $rotationId;

		if ($ascdesc && $sortorder == 14)
			$sql .= "  ORDER BY student.firstName " . $ascdesc;

		if ($ascdesc && $sortorder == 15)
			$sql .= "  ORDER BY student.lastName " . $ascdesc;

		$sql .= " GROUP BY rotation.rotationId";
		// if($isActiveRotations > 0)
		// 	$sql .=" AND CURDATE() BETWEEN date(rotation.startDate) AND date(rotation.endDate) ";
		// echo $sql;exit;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllDaysBetweenRotationStartAndEndDate($rotationStartDate, $rotationEndDate, $weekDays, $currentDate, $isDashBoard = 1, $fromDate = '', $endDate = '')
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "select a.Date,WEEKDAY(a.Date)
		from (
			select curdate() - INTERVAL (a.a + (10 * b.a) + (100 * c.a) + (1000 * d.a) ) DAY as Date
			from (select 0 as a union all select 1 union all select 2 union all select 3 union all select 4 union all select 5 union all select 6 union all select 7 union all select 8 union all select 9) as a
			cross join (select 0 as a union all select 1 union all select 2 union all select 3 union all select 4 union all select 5 union all select 6 union all select 7 union all select 8 union all select 9) as b
			cross join (select 0 as a union all select 1 union all select 2 union all select 3 union all select 4 union all select 5 union all select 6 union all select 7 union all select 8 union all select 9) as c
			cross join (select 0 as a union all select 1 union all select 2 union all select 3 union all select 4 union all select 5 union all select 6 union all select 7 union all select 8 union all select 9) as d
		) a
		where a.Date between '" . $rotationStartDate . "' and '" . $rotationEndDate . "' AND WEEKDAY(a.Date) IN (" . $weekDays . ") ";

		if ($isDashBoard == 1)
			$sql .= " AND (a.Date BETWEEN '" . $currentDate . "' - INTERVAL 10 DAY AND '" . $currentDate . "') limit 0,10";
		else
			$sql .= " AND (a.Date between '" . $fromDate . "' and '" . $endDate . "')";

		// echo $sql;exit;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetTodayActiveRotations()
	{
		$objDB = new clsDB();
		$rows = "";

		$CurrentDate = date("Y-m-d h:i:s");
		$repeatday = date('w', strtotime($CurrentDate));

		$sql = "SELECT rotation.*,GROUP_CONCAT(DISTINCT(rotationdetails.studentId)) as studentIds,schools.absenceSMSTime,schools.absenceSMSSendTo,adminRolesAbsenceSMSSendTo,clinitionRoleAbsenceSMSSendTo,timezonemaster.timezone,
				 GROUP_CONCAT(DISTINCT DATE(rotationscheduledates.scheduleDate)) as scheduleDates
				FROM rotation
				INNER JOIN courses ON rotation.courseId = courses.courseId
				INNER JOIN schools ON schools.schoolId = rotation.schoolId
				INNER JOIN timezonemaster ON schools.timeZoneId = timezonemaster.timeZoneId
				INNER JOIN rotationdetails ON rotationdetails.rotationId = rotation.rotationId
				INNER JOIN student ON student.studentId = rotationdetails.studentId 
				INNER JOIN rankmaster ON student.rankId=rankmaster.rankId
				INNER JOIN rotationrepeatdays ON rotation.rotationId=rotationrepeatdays.rotationId
							AND rotationrepeatdays.dayOfWeek=" . $repeatday . "
				LEFT JOIN rotationscheduledates ON rotationscheduledates.rotationId = rotation.rotationId AND rotationscheduledates.dayOfWeek =" . $repeatday . "
				WHERE  rotation.isDelete =0 
								 ";
		$sql .= " AND  schools.absenceSMSStatus = 1";
		$sql .= " AND CURDATE() BETWEEN date(rotation.startDate) AND date(rotation.endDate) AND student.isActive = 1 GROUP BY rotation.rotationId";

		// echo $sql;
		// exit; 
		$rows = $objDB->GetResultset($sql);

		unset($objDB);
		return $rows;
	}
	// function GetAllActiveRotationForApp($studentId, $schoolId, $limitString = '', $courseId = 0, $title = "")
	// {
	// 	// $title = "Test";
	// 	$objDB = new clsDB();
	// 	$rows = "";

	// 	$CurrentDate = date("Y-m-d h:i:s");
	// 	$todaysDate = date("Y-m-d");
	// 	$repeatday = date('w', strtotime($CurrentDate));

	// 	// AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)
	// 	//CLOCKOUT
	// 	$sql = "SELECT rotation.*,
	// 			courses.title as courseTitle, 
	// 			hospitalsites. title as hospitalTitle, 0 as isUpcoming ,0 as isClockIn,
	// 			courses.locationId,
	// 			rotation.locationId as rotationLocationId
	// 			FROM rotation
	// 			INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
	// 			INNER JOIN courses ON rotation.courseId = courses.courseId
	// 			INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
	// 			INNER JOIN rotationrepeatdays ON rotation.rotationId=rotationrepeatdays.rotationId
	// 						AND rotationrepeatdays.dayOfWeek=" . $repeatday . "
	// 			WHERE  rotationdetails.studentId=" . $studentId . " 
	// 			AND rotation.isDelete =0  AND rotation.isSchedule = 0
	// 			 ";

	// 	$sql .= " AND CURDATE() BETWEEN date(rotation.startDate) AND date(rotation.endDate)";

	// 	if ($courseId > 0) {
	// 		$sql .= " AND courses.courseId=" . $courseId;
	// 	}

	// 	if ($title != '') {
	// 		$sql .= " AND (rotation.title LIKE '%" . $title . "%' OR hospitalsites.title LIKE '%" . $title . "%' OR courses.title LIKE '%" . $title . "%') ";
	// 	}

	// 	$sql .= " and rotation.schoolId=" . $schoolId . "  GROUP BY rotation.rotationId";

	// 	// schedule
	// 	$sql .= " UNION ";

	// 	$sql .= "SELECT rotation.*,
	// 	 courses.title as courseTitle, 
	// 	 hospitalsites. title as hospitalTitle, 0 as isUpcoming ,0 as isClockIn,
	// 	 courses.locationId,
	// 	 rotation.locationId as rotationLocationId
	// 	 FROM rotation
	// 	 INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
	// 	 INNER JOIN courses ON rotation.courseId = courses.courseId
	// 	 INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
	// 	 INNER JOIN rotationrepeatdays ON rotation.rotationId=rotationrepeatdays.rotationId
	// 				 AND rotationrepeatdays.dayOfWeek=" . $repeatday . "
	//    INNER JOIN rotationscheduledates ON rotation.rotationId = rotationscheduledates.rotationId
	// 	 WHERE  rotationdetails.studentId=" . $studentId . "  AND rotation.isSchedule = 1 AND DATE_FORMAT(scheduleDate, '%Y-%m-%d') = '" . $todaysDate . "'
	// 	 AND rotation.isDelete =0  
	// 	  ";

	// 	$sql .= " AND CURDATE() BETWEEN date(rotation.startDate) AND date(rotation.endDate)";

	// 	if ($title != '') {
	// 		$sql .= " AND (rotation.title LIKE '%" . $title . "%' OR hospitalsites.title LIKE '%" . $title . "%' OR courses.title LIKE '%" . $title . "%') ";
	// 	}

	// 	$sql .= " and rotation.schoolId=" . $schoolId . "  GROUP BY rotation.rotationId";

	// 	//  $sql .= " UNION ";
	// 	//  //UPCOMING
	// 	//  $sql .= "SELECT rotation.*,
	// 	// 		courses.title as courseTitle, 
	// 	// 		hospitalsites. title as hospitalTitle, 1 as isUpcoming ,0 as isClockIn,
	// 	// 		courses.locationId,
	// 	// 		rotation.locationId as rotationLocationId
	// 	// 		FROM rotation
	// 	// 		INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
	// 	// 		INNER JOIN courses ON rotation.courseId = courses.courseId
	// 	// 		INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId

	// 	// 		WHERE  rotationdetails.studentId=".$studentId ." AND rotation.isDelete =0";

	// 	//  $sql .=" AND  date(rotation.startDate) > CURDATE() ";

	// 	//  if($courseId>0)
	// 	//  {
	// 	//      $sql.=" AND courses.courseId=".$courseId;
	// 	//  }

	// 	//  if($title != '')
	// 	//  {
	// 	// 	$sql .=" AND (rotation.title LIKE '%".$title."%' OR hospitalsites.title LIKE '%".$title."%' OR courses.title LIKE '%".$title."%') ";

	// 	// }
	// 	// // $sql .=" AND isUpcoming = 0";
	// 	//  $sql .=" and rotation.schoolId=".$schoolId ." GROUP BY rotation.rotationId";


	// 	if ($limitString != '') {
	// 		$sql .= $limitString;
	// 	}
	// 	// ECHO $sql;
	// 	$rows = $objDB->GetResultset($sql);
	// 	unset($objDB);
	// 	return $rows;
	// }

	function GetAllActiveRotationForApp($studentId, $schoolId, $limitString = '', $courseId = 0, $title = "")
	{
		$objDB = new clsDB();
		$rows = "";

		$CurrentDate = date("Y-m-d");
		$todaysDate = date("Y-m-d");
		$repeatday = date('w', strtotime($CurrentDate));

		$sql = "SELECT rotation.rotationId, rotation.title, rotation.startDate, rotation.endDate, rotation.parentRotationId, rotation.duration, rotation.courseId, rotation.hospitalSiteId,
					courses.title as courseTitle, hospitalsites.title as hospitalTitle,
					0 as isUpcoming, 0 as isClockIn, courses.locationId, rotation.locationId as rotationLocationId
				FROM rotation
				INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
				INNER JOIN courses ON rotation.courseId = courses.courseId
				INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
				INNER JOIN rotationrepeatdays ON rotation.rotationId = rotationrepeatdays.rotationId
					AND rotationrepeatdays.dayOfWeek = " . $repeatday . "
				WHERE rotationdetails.studentId = " . $studentId . "
				AND rotation.isDelete = 0 AND rotation.isSchedule = 0
				AND '" . $todaysDate . "' BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate)";

		if ($courseId > 0) {
			$sql .= " AND courses.courseId = " . $courseId;
		}

		if ($title != '') {
			$sql .= " AND (rotation.title LIKE '%" . $title . "%' OR hospitalsites.title LIKE '%" . $title . "%' OR courses.title LIKE '%" . $title . "%')";
		}

		$sql .= " AND rotation.schoolId = " . $schoolId . " GROUP BY rotation.rotationId";

		// schedule
		$sql .= " UNION ";

		$sql .= "SELECT rotation.rotationId, rotation.title, rotation.startDate, rotation.endDate, rotation.parentRotationId, rotation.duration, rotation.courseId, rotation.hospitalSiteId,
					courses.title as courseTitle, hospitalsites.title as hospitalTitle,
					0 as isUpcoming, 0 as isClockIn, courses.locationId, rotation.locationId as rotationLocationId
				FROM rotation
				INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
				INNER JOIN courses ON rotation.courseId = courses.courseId
				INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
				INNER JOIN rotationrepeatdays ON rotation.rotationId = rotationrepeatdays.rotationId
					AND rotationrepeatdays.dayOfWeek = " . $repeatday . "
				INNER JOIN rotationscheduledates ON rotation.rotationId = rotationscheduledates.rotationId
				WHERE rotationdetails.studentId = " . $studentId . "
				AND rotation.isSchedule = 1 AND DATE_FORMAT(rotationscheduledates.scheduleDate, '%Y-%m-%d') = '" . $todaysDate . "'
				AND rotation.isDelete = 0
				AND '" . $todaysDate . "' BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate)";

		if ($title != '') {
			$sql .= " AND (rotation.title LIKE '%" . $title . "%' OR hospitalsites.title LIKE '%" . $title . "%' OR courses.title LIKE '%" . $title . "%')";
		}

		$sql .= " AND rotation.schoolId = " . $schoolId . " GROUP BY rotation.rotationId";

		if ($limitString != '') {
			$sql .= $limitString;
		}

		// For debugging
		// echo $sql;

		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	function GetAllActiveRotationForAppV2($studentId, $schoolId, $limitString = '', $courseId = 0, $title = "")
	{
		// $title = "Test";
		$objDB = new clsDB();
		$rows = "";

		$CurrentDate = date("Y-m-d h:i:s");
		$todaysDate = date("Y-m-d");
		$repeatday = date('w', strtotime($CurrentDate));

		// AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)
		//CLOCKOUT
		$sql = "SELECT rotation.rotationId, rotation.title, rotation.startDate, rotation.endDate,
		rotation.parentRotationId, rotation.duration, rotation.courseId, rotation.hospitalSiteId, rotation.isSchedule,
				courses.title as courseTitle, 
				hospitalsites. title as hospitalTitle, 0 as isUpcoming ,0 as isClockIn,
				courses.locationId as courseLocationId,
				rotation.locationId as rotationLocationId
			FROM rotation
			INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId
			INNER JOIN courses ON rotation.courseId = courses.courseId
			INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
			INNER JOIN rotationrepeatdays ON rotation.rotationId = rotationrepeatdays.rotationId
							AND rotationrepeatdays.dayOfWeek=" . $repeatday . "
				WHERE  rotationdetails.studentId=" . $studentId . " 
				AND rotation.isDelete =0  AND rotation.isSchedule = 0
				 ";


		$sql .= " AND '" . $todaysDate . "' BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate)";

		if ($courseId > 0) {
			$sql .= " AND courses.courseId=" . $courseId;
		}

		if ($title != '') {
			$sql .= " AND (rotation.title LIKE '%" . $title . "%' OR hospitalsites.title LIKE '%" . $title . "%' OR courses.title LIKE '%" . $title . "%') ";
		}

		$sql .= " and rotation.schoolId=" . $schoolId . "  GROUP BY rotation.rotationId";

		// schedule
		$sql .= " UNION DISTINCT ";
		$sql .= "SELECT DISTINCT * FROM (";

		$sql .= "SELECT rotation.rotationId, rotation.title, rotation.startDate, rotation.endDate,
		rotation.parentRotationId, rotation.duration, rotation.courseId, rotation.hospitalSiteId, rotation.isSchedule,
        courses.title as courseTitle,
        hospitalsites.title as hospitalTitle,
        0 as isUpcoming,
        0 as isClockIn,
        courses.locationId as courseLocationId,
        rotation.locationId as rotationLocationId
		FROM rotation
		INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId
		INNER JOIN courses ON rotation.courseId = courses.courseId
		INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
		INNER JOIN rotationrepeatdays ON rotation.rotationId = rotationrepeatdays.rotationId
						AND rotationrepeatdays.dayOfWeek=" . $repeatday . "
	   INNER JOIN rotationscheduledates ON rotation.rotationId = rotationscheduledates.rotationId
		 WHERE  rotationdetails.studentId=" . $studentId . "  AND rotation.isSchedule = 1 AND DATE_FORMAT(scheduleDate, '%Y-%m-%d') = '" . $todaysDate . "'
		 AND rotation.isDelete =0  
		  ";

		$sql .= " AND '" . $todaysDate . "' BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate)";

		if ($title != '') {
			$sql .= " AND (rotation.title LIKE '%" . $title . "%' OR hospitalsites.title LIKE '%" . $title . "%' OR courses.title LIKE '%" . $title . "%') ";
		}

		$sql .= " and rotation.schoolId=" . $schoolId . "  GROUP BY rotation.rotationId";

		$sql .= " UNION DISTINCT ";

		// PM to AM
		$sql .= "SELECT rotation.rotationId, rotation.title, rotation.startDate, rotation.endDate,
		rotation.parentRotationId, rotation.duration, rotation.courseId, rotation.hospitalSiteId, rotation.isSchedule,
        courses.title as courseTitle,
        hospitalsites.title as hospitalTitle,
        0 as isUpcoming,
        0 as isClockIn,
        courses.locationId as courseLocationId,
        rotation.locationId as rotationLocationId
		FROM rotation
		INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId
		INNER JOIN courses ON rotation.courseId = courses.courseId
		INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
		INNER JOIN rotationrepeatdays ON rotation.rotationId = rotationrepeatdays.rotationId
	
			WHERE rotationdetails.studentId = " . $studentId . " 
			AND '" . $todaysDate . "' BETWEEN date(rotation.startDate) AND DATE_ADD(date(rotation.endDate), INTERVAL 1 DAY) 
			AND rotation.isDelete = 0  AND rotation.isSchedule = 1
			AND (
				TIME(rotation.startDate) >= TIME('12:00:00') 
				AND TIME(rotation.endDate) <= TIME('11:59:59') 
			)";
		$sql .= " and rotation.schoolId=" . $schoolId . "  GROUP BY rotation.rotationId
			) AS combined_subquery";





		if ($limitString != '') {
			$sql .= $limitString;
		}
		// echo '------------------' . $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllActiveRotationIdsForAppV2($studentId, $schoolId, $limitString = '', $courseId = 0, $title = "")
	{
		// $title = "Test";
		$objDB = new clsDB();
		$rows = "";

		$CurrentDate = date("Y-m-d h:i:s");
		$todaysDate = date("Y-m-d");
		$repeatday = date('w', strtotime($CurrentDate));

		// AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)
		//CLOCKOUT
		$sql = "SELECT rotation.rotationId
			FROM rotation
			INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId
			INNER JOIN courses ON rotation.courseId = courses.courseId
			INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
			INNER JOIN rotationrepeatdays ON rotation.rotationId = rotationrepeatdays.rotationId
							AND rotationrepeatdays.dayOfWeek=" . $repeatday . "
				WHERE  rotationdetails.studentId=" . $studentId . " 
				AND rotation.isDelete =0  AND rotation.isSchedule = 0
				 ";

		$sql .= " AND CURDATE() BETWEEN date(rotation.startDate) AND date(rotation.endDate)";

		if ($courseId > 0) {
			$sql .= " AND courses.courseId=" . $courseId;
		}

		if ($title != '') {
			$sql .= " AND (rotation.title LIKE '%" . $title . "%' OR hospitalsites.title LIKE '%" . $title . "%' OR courses.title LIKE '%" . $title . "%') ";
		}

		$sql .= " and rotation.schoolId=" . $schoolId . "  GROUP BY rotation.rotationId";

		// schedule
		$sql .= " UNION DISTINCT ";
		$sql .= "SELECT DISTINCT * FROM (";

		$sql .= "SELECT rotation.rotationId
		FROM rotation
		INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId
		INNER JOIN courses ON rotation.courseId = courses.courseId
		INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
		INNER JOIN rotationrepeatdays ON rotation.rotationId = rotationrepeatdays.rotationId
						AND rotationrepeatdays.dayOfWeek=" . $repeatday . "
	   INNER JOIN rotationscheduledates ON rotation.rotationId = rotationscheduledates.rotationId
		 WHERE  rotationdetails.studentId=" . $studentId . "  AND rotation.isSchedule = 1 AND DATE_FORMAT(scheduleDate, '%Y-%m-%d') = '" . $todaysDate . "'
		 AND rotation.isDelete =0  
		  ";

		$sql .= " AND CURDATE() BETWEEN date(rotation.startDate) AND date(rotation.endDate)";

		if ($title != '') {
			$sql .= " AND (rotation.title LIKE '%" . $title . "%' OR hospitalsites.title LIKE '%" . $title . "%' OR courses.title LIKE '%" . $title . "%') ";
		}

		$sql .= " and rotation.schoolId=" . $schoolId . "  GROUP BY rotation.rotationId";

		$sql .= " UNION DISTINCT ";

		// PM to AM
		$sql .= "SELECT rotation.rotationId
		FROM rotation
		INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId
		INNER JOIN courses ON rotation.courseId = courses.courseId
		INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
		INNER JOIN rotationrepeatdays ON rotation.rotationId = rotationrepeatdays.rotationId
	
			WHERE rotationdetails.studentId = " . $studentId . " 
			AND CURDATE() BETWEEN date(rotation.startDate) AND DATE_ADD(date(rotation.endDate), INTERVAL 1 DAY) 
			AND rotation.isDelete = 0  AND rotation.isSchedule = 1
			AND (
				TIME(rotation.startDate) >= TIME('12:00:00') 
				AND TIME(rotation.endDate) <= TIME('11:59:59') 
			)";
		$sql .= " and rotation.schoolId=" . $schoolId . "  GROUP BY rotation.rotationId
			) AS combined_subquery";





		if ($limitString != '') {
			$sql .= $limitString;
		}
		// echo '------------------' . $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		// Fetch the result set as an associative array
		$rowsArray = mysqli_fetch_all($rows, MYSQLI_ASSOC);

		// Extract rotationIds and concatenate them into a comma-separated string
		$rotationIds = implode(',', array_column($rowsArray, 'rotationId'));
		return $rotationIds;
	}

	// function GetAllStudentClockInRotationsForApp($studentId, $courseId = 0, $limitString = '', $title = "")
	// {
	// 	$objDB = new clsDB();
	// 	$rows = "";
	// 	$curdate = date("y-m-d h:i:s");
	// 	$todaysDate = date("Y-m-d");
	// 	$repeatday = date('w', strtotime($curdate));
	// 	$sql = "SELECT rotation.*,
	// 			courses.title as courseTitle, 
	// 			hospitalsites. title as hospitalTitle, 
	// 			IFNULL(attendance.rotationId,0) as clockedOutRotationId,
	// 			0 as isUpcoming, attendance.isClockOut,
	// 			0 as isInActiveRotation, IFNULL(attendance.attendanceId, 0) as attendanceId, courses.locationId,
	// 			rotation.parentRotationId,rotation.locationId as rotationLocationId,courses.locationId			
	// 			FROM rotation
	// 			INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
	// 			INNER JOIN courses ON rotation.courseId = courses.courseId
	// 			INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
	// 			INNER JOIN rotationrepeatdays ON rotation.rotationId=rotationrepeatdays.rotationId AND rotationrepeatdays.dayOfWeek !=" . $repeatday . "
	// 			LEFT JOIN attendance ON rotation.rotationId = attendance.rotationId AND attendance.studentId =" . $studentId . "
	// 			WHERE  rotationdetails.studentId=" . $studentId . "  and date(rotation.endDate) < CURDATE() AND date(rotation.startDate) < CURDATE()
	// 			AND rotation.isDelete =0 AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId) 
	// 		";
	// 	if ($courseId > 0)
	// 		$sql .= " AND rotation.courseId=" . $courseId;

	// 	if ($title != '') {
	// 		$sql .= " AND (rotation.title LIKE '%" . $title . "%' OR hospitalsites.title LIKE '%" . $title . "%' OR courses.title LIKE '%" . $title . "%') ";
	// 	}
	// 	$sql .= " group by rotation.rotationId";
	// 	$sql .= " UNION ";

	// 	$sql .= "SELECT rotation.*,
	// 			courses.title as courseTitle, 
	// 			hospitalsites. title as hospitalTitle, 
	// 			0 as clockedOutRotationId,
	// 			0 as isUpcoming, 0,
	// 			1 as isInActiveRotation, 0 as attendanceId, courses.locationId,
	// 			rotation.parentRotationId,rotation.locationId as rotationLocationId,courses.locationId
	// 			FROM rotation
	// 			INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
	// 			INNER JOIN courses ON rotation.courseId = courses.courseId
	// 			INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
	// 			INNER JOIN rotationrepeatdays ON rotation.rotationId=rotationrepeatdays.rotationId AND NOT EXISTS (
	//     SELECT *
	//     FROM rotationrepeatdays
	//     WHERE rotationrepeatdays.rotationId = rotation.rotationId
	//     AND rotationrepeatdays.dayofweek = $repeatday
	// )
	// 			WHERE  rotationdetails.studentId=" . $studentId . " 
	// 			AND rotation.isDelete =0  
	// 			 ";
	// 	if ($courseId > 0)
	// 		$sql .= " AND rotation.courseId=" . $courseId;

	// 	if ($title != '') {
	// 		$sql .= " AND (rotation.title LIKE '%" . $title . "%' OR hospitalsites.title LIKE '%" . $title . "%' OR courses.title LIKE '%" . $title . "%') ";
	// 	}

	// 	$sql .= " AND CURDATE() BETWEEN date(rotation.startDate) AND date(rotation.endDate)";
	// 	$sql .= " group by rotation.rotationId";

	// 	// schedule
	// 	$sql .= " UNION ";

	// 	$sql .= "SELECT rotation.*, courses.title as courseTitle, hospitalsites. title as hospitalTitle, 0 as clockedOutRotationId, 0 as isUpcoming, 0, 1 as isInActiveRotation, 0 as attendanceId,
	// 	  courses.locationId, rotation.parentRotationId,rotation.locationId as rotationLocationId,courses.locationId 
	// 	  FROM rotation INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
	// 	  INNER JOIN courses ON rotation.courseId = courses.courseId 
	// 	  INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId 
	// 	  INNER JOIN rotationrepeatdays ON rotation.rotationId=rotationrepeatdays.rotationId 
	// 	  INNER JOIN rotationscheduledates ON rotation.rotationId = rotationscheduledates.rotationId
	// 			WHERE  rotationdetails.studentId=" . $studentId . " 
	// 			AND rotation.isDelete =0  
	// 			 ";

	// 	$sql .= " AND CURDATE() BETWEEN date(rotation.startDate) AND date(rotation.endDate)   AND rotation.isSchedule = 1 AND DATE_FORMAT(scheduleDate, '%Y-%m-%d') != '" . $todaysDate . "'";

	// 	if ($courseId > 0)
	// 		$sql .= " AND rotation.courseId=" . $courseId;

	// 	if ($title != '') {
	// 		$sql .= " AND (rotation.title LIKE '%" . $title . "%' OR hospitalsites.title LIKE '%" . $title . "%' OR courses.title LIKE '%" . $title . "%') ";
	// 	}

	// 	$sql .= " group by rotation.rotationId";
	// 	// if ($limitString != '') {
	// 	// 	$sql .= $limitString;
	// 	// }
	// 	// echo $sql;
	// 	// exit;
	// 	$rows = $objDB->GetResultset($sql);
	// 	unset($objDB);
	// 	return $rows;
	// }

	function GetAllStudentClockInRotationsForApp($studentId, $courseId = 0, $limitString = '', $title = "")
	{
		$objDB = new clsDB();
		$rows = "";
		$curdate = date("y-m-d h:i:s");
		$todaysDate = date("Y-m-d");
		$repeatday = date('w', strtotime($curdate));

		// Combine similar queries
		$sql = "
			SELECT 
				rotation.rotationId, rotation.title, rotation.startDate, rotation.endDate,
				rotation.parentRotationId, rotation.duration, rotation.courseId, rotation.hospitalSiteId,rotation.isSchedule,
				courses.title as courseTitle, hospitalsites.title as hospitalTitle,
				IFNULL(attendance.rotationId, 0) as clockedOutRotationId,
				0 as isUpcoming, attendance.isClockOut,
				0 as isInActiveRotation, IFNULL(attendance.attendanceId, 0) as attendanceId, courses.locationId,
				rotation.locationId as rotationLocationId, courses.locationId
			FROM rotation
			INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId
			INNER JOIN courses ON rotation.courseId = courses.courseId
			INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
			INNER JOIN rotationrepeatdays ON rotation.rotationId = rotationrepeatdays.rotationId AND rotationrepeatdays.dayOfWeek != $repeatday
			LEFT JOIN attendance ON rotation.rotationId = attendance.rotationId AND attendance.studentId = $studentId
			WHERE rotationdetails.studentId = $studentId  and date(rotation.endDate) < CURDATE() AND date(rotation.startDate) < CURDATE()
				AND rotation.isDelete = 0 AND 0 = (SELECT count(*) FROM rotation r WHERE r.parentRotationId = rotation.rotationId)";

		// Additional conditions
		if ($courseId > 0) $sql .= " AND rotation.courseId = $courseId";
		if ($title != '') $sql .= " AND (rotation.title LIKE '%$title%' OR hospitalsites.title LIKE '%$title%' OR courses.title LIKE '%$title%')";
		$sql .= " GROUP BY rotation.rotationId";

		// UNION
		$sql .= " UNION ";

		// Combine similar queries
		$sql .= "
			SELECT 
				rotation.rotationId, rotation.title, rotation.startDate, rotation.endDate,
				rotation.parentRotationId, rotation.duration, rotation.courseId, rotation.hospitalSiteId,rotation.isSchedule,
				courses.title as courseTitle, hospitalsites.title as hospitalTitle,
				0 as clockedOutRotationId,
				0 as isUpcoming, 0,
				1 as isInActiveRotation, 0 as attendanceId, courses.locationId,
				rotation.locationId as rotationLocationId, courses.locationId
			FROM rotation
			INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId
			INNER JOIN courses ON rotation.courseId = courses.courseId
			INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
			INNER JOIN rotationrepeatdays ON rotation.rotationId = rotationrepeatdays.rotationId AND NOT EXISTS (
				SELECT *
				FROM rotationrepeatdays
				WHERE rotationrepeatdays.rotationId = rotation.rotationId
				AND rotationrepeatdays.dayofweek = $repeatday
			)
			WHERE rotationdetails.studentId = $studentId
				AND rotation.isDelete = 0";

		// Additional conditions
		if ($courseId > 0) $sql .= " AND rotation.courseId = $courseId";
		if ($title != '') $sql .= " AND (rotation.title LIKE '%$title%' OR hospitalsites.title LIKE '%$title%' OR courses.title LIKE '%$title%')";
		$sql .= " AND CURDATE() BETWEEN date(rotation.startDate) AND date(rotation.endDate)";
		$sql .= " GROUP BY rotation.rotationId";

		// UNION
		$sql .= " UNION ";

		// Combine similar queries
		$sql .= "
			SELECT 
				rotation.rotationId, rotation.title, rotation.startDate, rotation.endDate,
				rotation.parentRotationId, rotation.duration, rotation.courseId, rotation.hospitalSiteId,rotation.isSchedule,
				courses.title as courseTitle, hospitalsites.title as hospitalTitle,
				0 as clockedOutRotationId, 0 as isUpcoming, 0, 1 as isInActiveRotation, 0 as attendanceId,
				courses.locationId, rotation.locationId as rotationLocationId, courses.locationId
			FROM rotation
			INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId
			INNER JOIN courses ON rotation.courseId = courses.courseId
			INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
			INNER JOIN rotationrepeatdays ON rotation.rotationId = rotationrepeatdays.rotationId
			INNER JOIN rotationscheduledates ON rotation.rotationId = rotationscheduledates.rotationId
			WHERE rotationdetails.studentId = $studentId
				AND rotation.isDelete = 0";

		// Additional conditions
		if ($courseId > 0) $sql .= " AND rotation.courseId = $courseId";
		if ($title != '') $sql .= " AND (rotation.title LIKE '%$title%' OR hospitalsites.title LIKE '%$title%' OR courses.title LIKE '%$title%')";
		$sql .= " AND CURDATE() BETWEEN date(rotation.startDate) AND date(rotation.endDate) AND rotation.isSchedule = 1 AND DATE_FORMAT(scheduleDate, '%Y-%m-%d') != '$todaysDate'";
		$sql .= " GROUP BY rotation.rotationId";

		// echo $sql;
		// Execute the query
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllStudentClockInRotationsForAppV2($studentId, $courseId = 0, $limitString = '', $title = "")
	{
		$objDB = new clsDB();
		$rows = "";
		$curdate = date("y-m-d h:i:s");
		$todaysDate = date("Y-m-d");
		$repeatday = date('w', strtotime($curdate));

		// Combine similar queries using derived tables
		$sql = "
	    SELECT 
	        derived_table.rotationId, derived_table.title, derived_table.startDate, derived_table.endDate,
	        derived_table.parentRotationId, derived_table.duration, derived_table.courseId, derived_table.hospitalSiteId, derived_table.isSchedule,
	        derived_table.courseTitle, derived_table.hospitalTitle,
	        IFNULL(attendance.rotationId, 0) as clockedOutRotationId,
	        0 as isUpcoming, attendance.isClockOut,
	        0 as isInActiveRotation, IFNULL(attendance.attendanceId, 0) as attendanceId, derived_table.locationId,
	        derived_table.rotationLocationId, derived_table.locationId
	    FROM (
	        SELECT 
	            rotation.rotationId, rotation.title, rotation.startDate, rotation.endDate,
	            rotation.parentRotationId, rotation.duration, rotation.courseId, rotation.hospitalSiteId, rotation.isSchedule,
	            courses.title as courseTitle, hospitalsites.title as hospitalTitle, courses.locationId,
	            rotation.locationId as rotationLocationId
	        FROM rotation
	        INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId
	        INNER JOIN courses ON rotation.courseId = courses.courseId
	        INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
	        INNER JOIN rotationrepeatdays ON rotation.rotationId = rotationrepeatdays.rotationId AND rotationrepeatdays.dayOfWeek != $repeatday
	        WHERE rotationdetails.studentId = $studentId  
	            AND date(rotation.endDate) < '" . $todaysDate . "' 
	            AND date(rotation.startDate) < '" . $todaysDate . "'
	            AND rotation.isDelete = 0 
	            AND 0 = (
	                SELECT count(*) 
	                FROM rotation r 
	                WHERE r.parentRotationId = rotation.rotationId
	            )
	            " . ($courseId > 0 ? "AND rotation.courseId = $courseId" : "") . "
	            " . ($title != '' ? "AND (rotation.title LIKE '%$title%' OR hospitalsites.title LIKE '%$title%' OR courses.title LIKE '%$title%')" : "") . "
	        GROUP BY rotation.rotationId

	        UNION

	        SELECT 
	            rotation.rotationId, rotation.title, rotation.startDate, rotation.endDate,
	            rotation.parentRotationId, rotation.duration, rotation.courseId, rotation.hospitalSiteId, rotation.isSchedule,
	            courses.title as courseTitle, hospitalsites.title as hospitalTitle, courses.locationId,
	            rotation.locationId as rotationLocationId
	        FROM rotation
	        INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId
	        INNER JOIN courses ON rotation.courseId = courses.courseId
	        INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
	        INNER JOIN rotationrepeatdays ON rotation.rotationId = rotationrepeatdays.rotationId AND NOT EXISTS (
	            SELECT *
	            FROM rotationrepeatdays
	            WHERE rotationrepeatdays.rotationId = rotation.rotationId
	            AND rotationrepeatdays.dayofweek = $repeatday
	        )
	        WHERE rotationdetails.studentId = $studentId
	            AND rotation.isDelete = 0
	            " . ($courseId > 0 ? "AND rotation.courseId = $courseId" : "") . "
	            " . ($title != '' ? "AND (rotation.title LIKE '%$title%' OR hospitalsites.title LIKE '%$title%' OR courses.title LIKE '%$title%')" : "") . "
	        AND '" . $todaysDate . "' BETWEEN date(rotation.startDate) AND date(rotation.endDate)
	        GROUP BY rotation.rotationId

	        UNION

	        SELECT 
	            rotation.rotationId, rotation.title, rotation.startDate, rotation.endDate,
	            rotation.parentRotationId, rotation.duration, rotation.courseId, rotation.hospitalSiteId, rotation.isSchedule,
	            courses.title as courseTitle, hospitalsites.title as hospitalTitle, courses.locationId,
	            rotation.locationId as rotationLocationId
	        FROM rotation
	        INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId
	        INNER JOIN courses ON rotation.courseId = courses.courseId
	        INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
	        INNER JOIN rotationrepeatdays ON rotation.rotationId = rotationrepeatdays.rotationId
	        INNER JOIN rotationscheduledates ON rotation.rotationId = rotationscheduledates.rotationId
	        WHERE rotationdetails.studentId = $studentId
	            AND rotation.isDelete = 0
	            " . ($courseId > 0 ? "AND rotation.courseId = $courseId" : "") . "
	            " . ($title != '' ? "AND (rotation.title LIKE '%$title%' OR hospitalsites.title LIKE '%$title%' OR courses.title LIKE '%$title%')" : "") . "
	        AND '" . $todaysDate . "' BETWEEN date(rotation.startDate) AND date(rotation.endDate)
	        AND rotation.isSchedule = 1 
			AND rotation.rotationId NOT IN (
				SELECT rotationId
				FROM rotationscheduledates
				WHERE  DATE(scheduleDate) = '$todaysDate'
			)
	        GROUP BY rotation.rotationId
	    ) AS derived_table
	    LEFT JOIN attendance ON derived_table.rotationId = attendance.rotationId AND attendance.studentId = $studentId";

		// Additional conditions
		$sql .= " GROUP BY derived_table.rotationId";

		// Add a common ORDER BY for all query blocks
		$sql .= " ORDER BY derived_table.startDate DESC"; // Adjust the sorting order as needed

		// Add a common LIMIT for all query blocks
		if ($limitString != '') $sql .= " $limitString";

		// echo '-----------------------' . $sql;

		// Execute the query
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	// function GetAllStudentClockInRotationsForAppV2($studentId, $courseId = 0, $limitString = '', $title = "")
	// {
	// 	$objDB = new clsDB();
	// 	$rows = "";
	// 	$curdate = date("y-m-d h:i:s");
	// 	$todaysDate = date("Y-m-d");
	// 	$repeatday = date('w', strtotime($curdate));

	// 	// Combine similar queries using derived tables
	// 	$sql = "
	//     SELECT 
	// 	rotation.rotationId, 
	// 	rotation.title, 
	// 	rotation.startDate, 
	// 	rotation.endDate,
	// 	rotation.parentRotationId, 
	// 	rotation.duration, 
	// 	rotation.courseId, 
	// 	rotation.hospitalSiteId, 
	// 	rotation.isSchedule,
	// 	courses.title as courseTitle, 
	// 	hospitalsites.title as hospitalTitle, 
	// 	courses.locationId,
	// 	rotation.locationId as rotationLocationId,
	// 	IFNULL(attendance.rotationId, 0) as clockedOutRotationId,
	// 	0 as isUpcoming, 
	// 	attendance.isClockOut,
	// 	0 as isInActiveRotation, 
	// 	IFNULL(attendance.attendanceId, 0) as attendanceId
	// 	FROM rotation
	// 	INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId
	// 	INNER JOIN courses ON rotation.courseId = courses.courseId
	// 	INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
	// 	LEFT JOIN attendance ON rotation.rotationId = attendance.rotationId AND attendance.studentId = '" . $studentId . "'
	// 	LEFT JOIN rotationrepeatdays ON rotation.rotationId = rotationrepeatdays.rotationId
	// 	LEFT JOIN rotationscheduledates ON rotation.rotationId = rotationscheduledates.rotationId
	// 	WHERE rotationdetails.studentId = '" . $studentId . "'
	// 		AND rotation.isDelete = 0
	// 		AND (
	// 			-- Condition 1
	// 			(DATE(rotation.endDate) < '" . $todaysDate . "' AND DATE(rotation.startDate) < '" . $todaysDate . "' AND rotationrepeatdays.dayOfWeek != '" . $repeatday . "')
	// 			OR
	// 			-- Condition 2
	// 			(DATE(rotation.endDate) >= '" . $todaysDate . "' AND DATE(rotation.startDate) <= '" . $todaysDate . "' AND NOT EXISTS (
	// 				SELECT 1 FROM rotationrepeatdays 
	// 				WHERE rotationrepeatdays.rotationId = rotation.rotationId AND rotationrepeatdays.dayOfWeek = '" . $repeatday . "'
	// 			))
	// 			OR
	// 			-- Condition 3
	// 			(DATE(rotation.endDate) >= '" . $todaysDate . "' AND DATE(rotation.startDate) <= '" . $todaysDate . "' 
	// 				AND rotation.isSchedule = 1 
	// 				AND DATE_FORMAT(rotationscheduledates.scheduleDate, '%Y-%m-%d') != '" . $todaysDate . "')
	// 		)";
	// 	// -- Additional Conditions
	// 	if ($courseId)
	// 		$sql .= " AND (rotation.courseId = '" . $courseId . "')";
	// 	if ($title != '')
	// 		$sql .= " 	AND ((rotation.title LIKE '%" . $title . "%' OR hospitalsites.title LIKE '%" . $title . "%' OR courses.title LIKE '%" . $title . "%')) ";

	// 	$sql .= " GROUP BY rotation.rotationId
	// 		ORDER BY rotation.startDate DESC
	// 		";
	// 	// ";

	// 	// Add a common LIMIT for all query blocks
	// 	if ($limitString != '') $sql .= " $limitString";

	// 	// echo '-----------------------' . $sql;

	// 	// Execute the query
	// 	$rows = $objDB->GetResultset($sql);
	// 	unset($objDB);
	// 	return $rows;
	// }
	function GetRotationRepeatDay($rotationId)
	{
		$curdate = date("y-m-d h:i:s");
		$curdates = date("Y-m-d");
		$repeatday = date('w', strtotime($curdate));
		$sql = "SELECT rotationRepeatDaysId FROM `rotationrepeatdays` WHERE `rotationId` =" . $rotationId . " AND dayOfWeek = " . $repeatday;
		// ECHO $sql;
		$objDB = new clsDB();
		$row = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $row;
	}

	function GetStudentRotationDetailsForApp($studentId, $rotationId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT rotation.rotationId, rotation.title, rotation.startDate, rotation.endDate,
		rotation.parentRotationId, rotation.duration, rotation.courseId, rotation.hospitalSiteId, rotation.isSchedule,courses.title as courseTitle, hospitalsites. title as hospitalTitle,0 as isUpcoming 
				FROM rotation
				INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
				INNER JOIN courses ON rotation.courseId = courses.courseId
				INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
				INNER JOIN location ON courses.locationId = location.locationId
				WHERE rotationdetails.studentId=" . $studentId . " AND rotation.rotationId=" . $rotationId;

		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}

	function GetRotationlistForApp($studentId, $schoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT rotation.rotationId, rotation.title, rotation.startDate, rotation.endDate,
		rotation.parentRotationId, rotation.duration, rotation.courseId, rotation.hospitalSiteId, rotation.isSchedule,
				courses.title as courseTitle,location.locationId, location.title as locationTitle,
				hospitalsites.hospitalSiteId,hospitalsites. title as hospitalTitle
				FROM rotation
				INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
				INNER JOIN courses ON rotation.courseId = courses.courseId
				INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
				INNER JOIN location ON courses.locationId = location.locationId
				WHERE rotationdetails.studentId=" . $studentId;
		//  $sql .=" AND CURDATE() BETWEEN date(rotation.startDate) AND date(rotation.endDate)";
		$sql .= " AND rotation.isDelete =0 AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId) 
				-- AND (
				-- 	  (CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
				-- 	  OR
				-- 	  (rotation.parentRotationId > 0
				-- 	   AND EXISTS (
				-- 		   SELECT 1
				-- 		   FROM rotation AS parent
				-- 		   WHERE parent.rotationId = rotation.parentRotationId
				-- 		   AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
				-- 		   AND parent.schoolId = " . $schoolId . " AND rotation.isSchedule = 1 
				-- 	   )
				-- 	  )
				--   )
				";
		// echo $sql;
		$rows = $objDB->GetResultSet($sql);
		unset($objDB);
		return $rows;
	}

	function GetRotationlistWithoutExpireRotationsForApp($studentId, $schoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT rotation.rotationId, rotation.title, rotation.startDate, rotation.endDate,
		rotation.parentRotationId, rotation.duration, rotation.courseId, rotation.hospitalSiteId, rotation.isSchedule,
				courses.title as courseTitle,location.locationId, location.title as locationTitle,
				hospitalsites.hospitalSiteId,hospitalsites. title as hospitalTitle
				FROM rotation
				INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
				INNER JOIN courses ON rotation.courseId = courses.courseId
				INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
				INNER JOIN location ON courses.locationId = location.locationId
				WHERE rotationdetails.studentId=" . $studentId;
		//  $sql .=" AND CURDATE() BETWEEN date(rotation.startDate) AND date(rotation.endDate)";
		$sql .= " AND rotation.isDelete =0 AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId) 
				AND (
					  (CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
					  OR
					  (rotation.parentRotationId > 0
					   AND EXISTS (
						   SELECT 1
						   FROM rotation AS parent
						   WHERE parent.rotationId = rotation.parentRotationId
						   AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
						   AND parent.schoolId = " . $schoolId . " AND rotation.isSchedule = 1 
					   )
					  )
				  )
				";

		$rows = $objDB->GetResultSet($sql);
		unset($objDB);
		return $rows;
	}
	function GetAllRotationlistForApp($studentId, $courseId = 0, $limitString = "", $searchText = "", $IsExpire = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT rotation.rotationId, rotation.title, rotation.startDate, rotation.endDate,
				rotation.parentRotationId, rotation.duration, rotation.courseId, rotation.hospitalSiteId, rotation.isSchedule,
				courses.title as courseTitle,location.locationId, location.title as locationTitle,
				hospitalsites.hospitalSiteId,hospitalsites. title as hospitalTitle
				FROM rotation
				INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
				INNER JOIN courses ON rotation.courseId = courses.courseId
				INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
				INNER JOIN location ON courses.locationId = location.locationId
				WHERE rotationdetails.studentId=" . $studentId . "  ";
		if ($courseId) {
			$sql .= " AND courses.courseId = " . $courseId;
		}

		if ($searchText != "") {
			$sql .= " AND (rotation.title LIKE '%" . $searchText . "%' OR hospitalsites.title LIKE '%" . $searchText . "%' OR courses.title LIKE '%" . $searchText . "%' ) ";
		}
		// $IsExpire > 0 means show all records
		if ($IsExpire == 0) {
			$sql .= " AND rotation.isDelete =0 AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId) 
				AND (
					  (CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
					  OR
					  (rotation.parentRotationId > 0
					   AND EXISTS (
						   SELECT 1
						   FROM rotation AS parent
						   WHERE parent.rotationId = rotation.parentRotationId
						   AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
						   AND rotation.isSchedule = 1 
					   )
					  )
				  )
				";
		}

		if ($limitString != '') {
			$sql .= $limitString;
		}
		// echo $sql;
		$rows = $objDB->GetResultSet($sql);
		unset($objDB);
		return $rows;
	}

	function GetRotationsForEvaluationForApp($studentId, $schoolId, $courseId = 0, $limitString = "", $searchText = "")
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT rotation.*, rotationdetails.*,
				courses.title as courseTitle,location.locationId, location.title as locationTitle,
				hospitalsites.hospitalSiteId,hospitalsites. title as hospitalTitle
				FROM rotation
				INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
				INNER JOIN courses ON rotation.courseId = courses.courseId
				INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
				INNER JOIN location ON courses.locationId = location.locationId
				WHERE rotationdetails.studentId=" . $studentId . " AND hospitalsites.isHospital = 1 AND hospitalsites.isSms = 1 ";
		// AND CURDATE() BETWEEN date(startDate) AND date(endDate) ";
		$sql .= " AND rotation.isDelete =0 AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId) 
				AND (
					  (CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
					  OR
					  (rotation.parentRotationId > 0
					   AND EXISTS (
						   SELECT 1
						   FROM rotation AS parent
						   WHERE parent.rotationId = rotation.parentRotationId
						   AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
						   AND parent.schoolId = " . $schoolId . " AND rotation.isSchedule = 1 
					   )
					  )
				  )
				";
		if ($courseId) {
			$sql .= " AND courses.courseId = " . $courseId;
		}

		if ($searchText != "") {
			$sql .= " AND (rotation.title LIKE '%" . $searchText . "%' OR hospitalsites.title LIKE '%" . $searchText . "%' OR courses.title LIKE '%" . $searchText . "%' ) ";
		}

		if ($limitString != '') {
			$sql .= $limitString;
		}
		// echo $sql;
		$rows = $objDB->GetResultSet($sql);
		unset($objDB);
		return $rows;
	}

	function GetAssignedStudentsIdForRotation($rotationId)
	{
		$objDB = new clsDB();
		// $sql = "SELECT * FROM  clinicianreports WHERE clinicianRoleId=".$clinicianRoleId;
		$sql = "SELECT GROUP_CONCAT(studentId SEPARATOR ',') AS studentIds FROM rotationdetails WHERE rotationId = " . $rotationId;
		$row = $objDB->GetSingleFieldValue($sql);
		// echo $sql;
		unset($objDB);
		return $row;
	}

	function GetRotationScheduleDates($rotationId)
	{
		$objDB = new clsDB();
		// $sql = "SELECT * FROM  clinicianreports WHERE clinicianRoleId=".$clinicianRoleId;
		$sql = "SELECT GROUP_CONCAT(DATE_FORMAT(scheduleDate, '%Y-%m-%d') SEPARATOR ',') AS ScheduleDates FROM rotationscheduledates WHERE rotationId = " . $rotationId;

		$row = $objDB->GetSingleFieldValue($sql);
		// echo $sql;
		unset($objDB);
		return $row;
	}

	// schedule dates
	function SaveRotationScheduleDates()
	{
		$objDB = new clsDB();
		$sql = '';
		$sql = "INSERT INTO rotationscheduledates (rotationId,dayOfWeek,scheduleDate) 
							VALUES (
								 '" . addslashes($this->rotationId) . "', 
								 '" . addslashes($this->dayOfWeek) . "',
								 '" . addslashes($this->rotationScheduleDate) . "'
								 )";
		// echo $sql;exit;
		$scheduleDateId = $objDB->ExecuteInsertQuery($sql);

		unset($objDB);
		return $scheduleDateId;
	}

	function DeleteAllRotationScheduleDates($rotationId)
	{
		$result = "";
		if ($rotationId > 0) {
			$objDB = new clsDB();
			$sql = "DELETE FROM rotationscheduledates WHERE rotationId = " . $rotationId;
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
		return $result;
	}

	function GetScheduleDateById($rotationId)
	{
		$curdate = date("y-m-d h:i:s");

		$objDB = new clsDB();
		$sql = "SELECT GROUP_CONCAT(DATE_FORMAT(scheduleDate, '%m/%d/%Y') SEPARATOR ',') AS ScheduleDates FROM rotationscheduledates WHERE rotationId = " . $rotationId;
		// $sql = "SELECT scheduleDateId FROM `rotationscheduledates` WHERE DATE_FORMAT(scheduleDate, '%Y-%m-%d') = '".$curdate."' AND rotationId = ".$rotationId;

		$row = $objDB->GetSingleFieldValue($sql);
		// echo $sql;
		unset($objDB);
		return $row;
	}

	function GetRotationByStudentForApp($schoolId, $studentId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT rotation.*, rotationdetails.*,
				courses.title as courseTitle,location.locationId, location.title as locationTitle,
				schools.schoolId,student.*,
				hospitalsites.hospitalSiteId,hospitalsites. title as hospitalTitle, attendance.rotationId as clockedOutRotationId, hospitalsites.isHospital, hospitalsites.isSms
				FROM rotation
				INNER JOIN schools ON rotation.schoolId=schools.schoolId
				LEFT JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
				LEFT JOIN  student ON rotationdetails.studentId =student.studentId
				LEFT JOIN courses ON rotation.courseId = courses.courseId
				LEFT JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
				LEFT JOIN location ON courses.locationId = location.locationId
				LEFT JOIN attendance ON rotation.rotationId = attendance.rotationId AND attendance.isClockOut = 1
				WHERE schools.schoolId=" . $schoolId . " AND rotation.isDelete =0";
		if ($studentId)
			$sql .= " AND rotationdetails.studentId=" . $studentId;

		$sql .= " 
			AND (
						  (CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
						  OR
						  (rotation.parentRotationId > 0
						   AND EXISTS (
							   SELECT 1
							   FROM rotation AS parent
							   WHERE parent.rotationId = rotation.parentRotationId
							   AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
							   AND parent.schoolId = " . $schoolId . " AND rotation.isSchedule = 1 
						   )
						  )
					  )";
		$sql .= " GROUP BY rotation.rotationId";
		// ECHO $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetRotationByRotationId($rotationId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
				*
				FROM rotation where rotationId = $rotationId";

		// echo $sql;exit;
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}

	function GetRotationIdByhospitalSiteId($hospitalSiteId = 0, $parentRotationId = 0, $studentId = 0, $title = '')
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
				rotation.rotationId,rotation.startDate,rotation.endDate
				FROM rotation 
				INNER JOIN rotationdetails ON rotationdetails.rotationId = rotation.rotationId
				where hospitalSiteId = $hospitalSiteId";
		if ($parentRotationId)
			$sql .= " And parentRotationId = " . $parentRotationId;
		if ($studentId)
			$sql .= " And rotationdetails.studentId = " . $studentId;
		if ($title != '')
			$sql .= " And rotation.title = '" . $title . "'";
		// echo $sql;exit;
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}
	function UpdateScheduleDate($rotationId, $scheduleDate)
	{
		$scheduleDate = date('Y-m-d', strtotime($scheduleDate));
		$objDB = new clsDB();
		$rows = "";
		$sql = "delete FROM `rotationscheduledates`
		WHERE `rotationId` = $rotationId and
		STR_TO_DATE(scheduleDate, '%Y-%m-%d') = '" . $scheduleDate . "'
				";
		// echo $sql;exit;
		$result = $objDB->ExecuteQuery($sql);

		return $result;
		unset($objDB);
	}
	// DATE_FORMAT(scheduleDate, '%Y-%m-%d') = '".$curdate."'

	function GetInactiveRotationsForApp($studentId, $courseId = 0, $limitString = '', $title = "", $rotationIds = '')
	{
		$objDB = new clsDB();
		$rows = "";
		$curdate = date("y-m-d h:i:s");
		$todaysDate = date("Y-m-d");
		$repeatday = date('w', strtotime($curdate));

		// 	$sql = "SELECT 
		// 	rotation.rotationId, 
		// 	rotation.title, 
		// 	rotation.startDate, 
		// 	rotation.endDate,
		// 	rotation.parentRotationId, 
		// 	rotation.duration, 
		// 	rotation.courseId, 
		// 	rotation.hospitalSiteId, 
		// 	rotation.isSchedule,
		// 	courses.title as courseTitle, 
		// 	hospitalsites.title as hospitalTitle,
		// 	0 as clockedOutRotationId,
		// 	0 as isUpcoming,
		// 	0 as isInActiveRotation, 
		// 	0 as attendanceId, 
		// 	courses.locationId,
		// 	rotation.locationId as rotationLocationId, 
		// 	courses.locationId AS courseLocationId,
		// 	GROUP_CONCAT(rotationrepeatdays.dayOfWeek) AS rotationRepeatDays
		// FROM rotation
		// INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId
		// INNER JOIN rotationrepeatdays ON rotation.rotationId = rotationrepeatdays.rotationId AND rotationrepeatdays.dayOfWeek != $repeatday
		// INNER JOIN courses ON rotation.courseId = courses.courseId
		// INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
		// INNER JOIN student ON rotationdetails.studentId = student.studentId
		// WHERE student.studentId =" . $studentId;

		// 	// Additional conditions
		// 	if ($courseId > 0) $sql .= " AND rotation.courseId = $courseId";
		// 	if ($title != '') $sql .= " AND (rotation.title LIKE '%$title%' OR hospitalsites.title LIKE '%$title%' OR courses.title LIKE '%$title%')";
		// 	// $sqal .= " AND CURDATE() BETWEEN date(rotation.startDate) AND date(rotation.endDate)";
		// 	$sql .= " and rotation.rotationId = 9247 GROUP BY rotation.rotationId ORDER BY rotation.startDate DESC ";

		// 	// "GROUP BY rotation.rotationId 
		// 	// ORDER BY rotation.startDate DESC 
		// 	// ";
		// 	$sql .= $limitString;

		// ...

		// ...

		$sql = "SELECT 
		rotation.rotationId, 
		rotation.title, 
		rotation.startDate, 
		rotation.endDate,
		rotation.parentRotationId, 
		rotation.duration, 
		rotation.courseId, 
		rotation.hospitalSiteId, 
		rotation.isSchedule,
		courses.title as courseTitle, 
		hospitalsites.title as hospitalTitle,
		0 as clockedOutRotationId,
		0 as isUpcoming,
		0 as isInActiveRotation, 
		0 as attendanceId, 
		courses.locationId,
		rotation.locationId as rotationLocationId, 
		courses.locationId AS courseLocationId,
		GROUP_CONCAT(rotationrepeatdays.dayOfWeek) AS rotationRepeatDays
		FROM rotation
		INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId
		INNER JOIN rotationrepeatdays ON rotation.rotationId = rotationrepeatdays.rotationId 
		INNER JOIN courses ON rotation.courseId = courses.courseId
		INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
		INNER JOIN student ON rotationdetails.studentId = student.studentId
		WHERE student.studentId = " . $studentId . " AND 0 = (SELECT count(*) FROM rotation r WHERE r.parentRotationId = rotation.rotationId)
		";


		// ...

		// Additional conditions
		if ($courseId > 0) $sql .= " AND rotation.courseId = $courseId";
		if ($title != '') $sql .= " AND (rotation.title LIKE '%$title%' OR hospitalsites.title LIKE '%$title%' OR courses.title LIKE '%$title%')";
		if ($rotationIds != '') $sql .= " AND rotation.rotationId NOT IN ($rotationIds)";
		// $sqal .= " AND CURDATE() BETWEEN date(rotation.startDate) AND date(rotation.endDate)";
		$sql .= " GROUP BY rotation.rotationId  ORDER BY rotation.startDate DESC ";


		$sql .= $limitString;

		// ... 

		// Execute the query
		// echo $sql; exit;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function UpdateRotationDates($rotationId)
	{
		$objDB = new clsDB();
		$rows = "";
		if ($rotationId > 0) {
			$sql = "UPDATE rotation SET " . "		
				startDate = '" . addslashes($this->startDate) . "',
				endDate = '" . addslashes($this->endDate) . "',					
				updatedDate = '" . (date("Y-m-d h:i:s")) . "'								
				Where rotationId= " . $rotationId;
			// echo $sql;exit;
			$rows = $objDB->ExecuteQuery($sql);
			unset($objDB);
			return $rotationId;
		}
	}

	function GetScheduleDatesCount($rotationId)
	{

		$objDB = new clsDB();
		$sql = "SELECT count(*) FROM `rotationscheduledates` WHERE rotationId =" . $rotationId;
		// $sql = "SELECT scheduleDateId FROM `rotationscheduledates` WHERE DATE_FORMAT(scheduleDate, '%Y-%m-%d') = '".$curdate."' AND rotationId = ".$rotationId;

		$row = $objDB->GetSingleFieldValue($sql);
		// echo $sql;
		unset($objDB);
		return $row;
	}

	function GettudentCountByRotationId($rotationId)
	{

		$objDB = new clsDB();
		$sql = "SELECT COUNT(DISTINCT studentId)  FROM rotationdetails WHERE rotationId =" . $rotationId;
		// $sql = "SELECT scheduleDateId FROM `rotationscheduledates` WHERE DATE_FORMAT(scheduleDate, '%Y-%m-%d') = '".$curdate."' AND rotationId = ".$rotationId;

		$row = $objDB->GetSingleFieldValue($sql);
		// echo $sql;
		unset($objDB);
		return $row;
	}

	function GetRotationScheduleMinMaxDates($rotationId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT MIN(scheduleDate) AS minDate , MAX(scheduleDate) AS maxDate FROM `rotationscheduledates`  WHERE rotationId = $rotationId";

		// echo $sql;exit;
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}

	function GetrotationDatesByRotationId($rotationId, $schoolId)
	{
		$sql = "SELECT rotation.startDate,rotation.endDate from rotation
		WHERE rotation.rotationId=" . $rotationId . " AND rotation.schoolId =" . $schoolId;
		// echo $sql;exit;
		$objDB = new clsDB();
		$row = $objDB->GetDataRow($sql);
		unset($objDB);

		return $row;
	}

	function GetRotationForClinicianBySchoolIdForApp($SchoolId = 0, $rotationId, $from_date, $to_date, $isActive = 0, $limitString = '', $searchText = '')
	{
		$objDB = new clsDB();
		$rows = "";
		$todaysDate = date("Y-m-d");
		$sql = " SELECT rotation.title as rotationName, rotation.startDate,rotation.endDate, rotation.rotationId, rotation.startDate, rotation.endDate, rotation.duration, courses.courseId, 
		courses.title as courseTitle,hospitalsites.hospitalSiteId,hospitalsites.title as hospitalTitle,
				location.locationId, location.title as locationTitle, 
				IFNULL(
				COUNT(
					CASE
						WHEN  (student.isActive = 1 AND (rankmaster.title != 'Graduate' AND rankmaster.title NOT LIKE '%Graduate%') AND rankmaster.title != 'Dropout')
						THEN rotationdetails.rotationId
					END
				),
				0) AS studentCount	,
				(select count(*) from rotation SUBR WHERE SUBR.parentRotationId= rotation.rotationId) AS subratoncount ,
				rotation.locationId as rotationLocationId
				FROM rotation
    			LEFT JOIN courses ON rotation.courseId = courses.courseId
        		LEFT JOIN location ON courses.locationId = location.locationId
        		LEFT JOIN hospitalsites  ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
        		LEFT JOIN rotationdetails ON rotationdetails.rotationId = rotation.rotationId
				INNER JOIN student on rotationdetails.studentId = student.studentId
				INNER JOIN rankmaster ON student.rankId = rankmaster.rankId";


		$sql .= " WHERE rotation.schoolId=" . $SchoolId . " 
					AND rotation.isDelete =0 AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)";

		if ($rotationId) {
			$sql .= " AND rotation.rotationId=" . $rotationId;
		}
		if ($to_date != '') {
			$sql .= " AND date(rotation.endDate) <= '" . $to_date . "'";
		}
		if ($from_date != '') {
			$sql .= " AND date(rotation.startDate) >= '" . $from_date . "'";
		}

		if ($isActive == 0) {

			// $sql .= " AND ('" . $todaysDate . "' BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))";
			$sql .= "	AND '" . $todaysDate . "' BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate)";
		} else {

			$sql .= " AND '" . $todaysDate . "' NOT BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate)";
		}

		if ($searchText != "") {
			$sql .= " AND (hospitalsites.title LIKE '%" . $searchText . "%' OR rotation.title LIKE '%" . $searchText . "%' OR courses.title LIKE '%" . $searchText . "%') ";
		}

		$sql .= " GROUP BY rotation.rotationId ORDER BY rotation.startDate DESC" . $limitString;

		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllActiveRotationsBySchool($schoolId = 0,  $rotationId, $from_date, $to_date, $courseId = 0, $studentId = 0, $hospitalsiteId = 0, $limitString = '', $searchText = '')
	{
		$objDB = new clsDB();
		$rows = "";

		$CurrentDate = date("Y-m-d h:i:s");
		$todaysDate = date("Y-m-d");
		$repeatday = date('w', strtotime($CurrentDate));

		// AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)
		//CLOCKOUT
		$sql = "SELECT rotation.rotationId, rotation.title as rotationName, rotation.startDate, rotation.endDate,
		rotation.parentRotationId, rotation.duration, rotation.courseId, rotation.hospitalSiteId, rotation.isSchedule,location.locationId, location.title as locationTitle, 
        courses.title as courseTitle,courses.courseHours,
        hospitalsites.title as hospitalTitle,
        0 as isUpcoming,
        0 as isClockIn,
        courses.locationId as courseLocationId,
        rotation.locationId as rotationLocationId,
		IFNULL(
				COUNT(
					CASE
						WHEN  (student.isActive = 1 AND (rankmaster.title != 'Graduate' AND rankmaster.title NOT LIKE '%Graduate%') AND rankmaster.title != 'Dropout')
						THEN rotationdetails.rotationId
					END
				),
				0) AS studentCount,
				(select count(*) from rotation SUBR WHERE SUBR.parentRotationId= rotation.rotationId) AS subratoncount 

			FROM rotation
			INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId
			INNER JOIN student on rotationdetails.studentId = student.studentId
			INNER JOIN rankmaster ON student.rankId = rankmaster.rankId
			INNER JOIN courses ON rotation.courseId = courses.courseId
			LEFT JOIN location ON courses.locationId = location.locationId
			INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
			INNER JOIN rotationrepeatdays ON rotation.rotationId = rotationrepeatdays.rotationId
							AND rotationrepeatdays.dayOfWeek=" . $repeatday . "
				WHERE rotation.isDelete =0 AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId) AND rotation.isSchedule = 0
				 ";


		$sql .= " AND '" . $todaysDate . "' BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate)";

		if ($to_date != '') {
			$sql .= " AND date(rotation.startDate) <= '" . $to_date . "'";
		}
		if ($from_date != '') {
			$sql .= " AND date(rotation.startDate) >= '" . $from_date . "'";
		}

		if ($courseId > 0) {
			$sql .= " AND courses.courseId=" . $courseId;
		}

		if ($studentId) {
			$sql .= " AND rotationdetails.studentId=" . $studentId;
		}

		if ($hospitalsiteId) {
			$sql .= " AND rotation.hospitalsiteId=" . $hospitalsiteId;
		}

		if ($searchText != '') {
			$sql .= " AND (rotation.title LIKE '%" . $searchText . "%' OR hospitalsites.title LIKE '%" . $searchText . "%' OR courses.title LIKE '%" . $searchText . "%') ";
		}

		$sql .= " and rotation.schoolId=" . $schoolId . "  GROUP BY rotation.rotationId";

		// schedule
		$sql .= " UNION DISTINCT ";
		$sql .= "SELECT DISTINCT * FROM (";

		$sql .= "SELECT rotation.rotationId, rotation.title as rotationName, rotation.startDate, rotation.endDate,
		rotation.parentRotationId, rotation.duration, rotation.courseId, rotation.hospitalSiteId, rotation.isSchedule,location.locationId, location.title as locationTitle, 
        courses.title as courseTitle,courses.courseHours,
        hospitalsites.title as hospitalTitle,
        0 as isUpcoming,
        0 as isClockIn,
        courses.locationId as courseLocationId,
        rotation.locationId as rotationLocationId,
		IFNULL(
				COUNT(
					CASE
						WHEN  (student.isActive = 1 AND (rankmaster.title != 'Graduate' AND rankmaster.title NOT LIKE '%Graduate%') AND rankmaster.title != 'Dropout')
						THEN rotationdetails.rotationId
					END
				),
				0) AS studentCount,
				(select count(*) from rotation SUBR WHERE SUBR.parentRotationId= rotation.rotationId) AS subratoncount 

		FROM rotation
		INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId
		INNER JOIN student on rotationdetails.studentId = student.studentId
		INNER JOIN rankmaster ON student.rankId = rankmaster.rankId
		INNER JOIN courses ON rotation.courseId = courses.courseId
		LEFT JOIN location ON courses.locationId = location.locationId
		INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
		INNER JOIN rotationrepeatdays ON rotation.rotationId = rotationrepeatdays.rotationId
						AND rotationrepeatdays.dayOfWeek=" . $repeatday . "
	   INNER JOIN rotationscheduledates ON rotation.rotationId = rotationscheduledates.rotationId
		 WHERE rotation.isSchedule = 1 AND DATE_FORMAT(scheduleDate, '%Y-%m-%d') = '" . $todaysDate . "'
		 AND rotation.isDelete =0  AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)
		  ";

		$sql .= " AND (
				(rotation.parentRotationId > 0
				AND EXISTS (
					SELECT 1
					FROM rotation AS parent
					WHERE parent.rotationId = rotation.parentRotationId
					AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
					AND rotation.startDate BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
					AND rotation.endDate BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
					AND parent.schoolId = " . $schoolId . " AND rotation.isSchedule = 1
					AND DATE_FORMAT(rotation.startDate, '%Y-%m-%d') <= '" . $todaysDate . "'    
				)
				)
		)";

		if ($searchText != '') {
			$sql .= " AND (rotation.title LIKE '%" . $searchText . "%' OR hospitalsites.title LIKE '%" . $searchText . "%' OR courses.title LIKE '%" . $searchText . "%') ";
		}

		if ($to_date != '') {
			$sql .= " AND date(rotation.startDate) <= '" . $to_date . "'";
		}
		if ($from_date != '') {
			$sql .= " AND date(rotation.startDate) >= '" . $from_date . "'";
		}

		if ($studentId) {
			$sql .= " AND rotationdetails.studentId=" . $studentId;
		}

		if ($hospitalsiteId) {
			$sql .= " AND rotation.hospitalsiteId=" . $hospitalsiteId;
		}

		$sql .= " and rotation.schoolId=" . $schoolId . "  GROUP BY rotation.rotationId";

		$sql .= " UNION DISTINCT ";

		// PM to AM
		$sql .= "SELECT rotation.rotationId, rotation.title as rotationName, rotation.startDate, rotation.endDate,
		rotation.parentRotationId, rotation.duration, rotation.courseId, rotation.hospitalSiteId, rotation.isSchedule,location.locationId, location.title as locationTitle, 
        courses.title as courseTitle,courses.courseHours,
        hospitalsites.title as hospitalTitle,
        0 as isUpcoming,
        0 as isClockIn,
        courses.locationId as courseLocationId,
        rotation.locationId as rotationLocationId,
		IFNULL(
				COUNT(
					CASE
						WHEN  (student.isActive = 1 AND (rankmaster.title != 'Graduate' AND rankmaster.title NOT LIKE '%Graduate%') AND rankmaster.title != 'Dropout')
						THEN rotationdetails.rotationId
					END
				),
				0) AS studentCount,
				(select count(*) from rotation SUBR WHERE SUBR.parentRotationId= rotation.rotationId) AS subratoncount 

		FROM rotation
		INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId
		INNER JOIN student on rotationdetails.studentId = student.studentId
		INNER JOIN rankmaster ON student.rankId = rankmaster.rankId
		INNER JOIN courses ON rotation.courseId = courses.courseId
		LEFT JOIN location ON courses.locationId = location.locationId
		INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
		INNER JOIN rotationrepeatdays ON rotation.rotationId = rotationrepeatdays.rotationId
	
			WHERE '" . $todaysDate . "' BETWEEN date(rotation.startDate) AND DATE_ADD(date(rotation.endDate), INTERVAL 1 DAY) 
			AND rotation.isDelete = 0 AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId) AND rotation.isSchedule = 1
			AND (
				TIME(rotation.startDate) >= TIME('12:00:00') 
				AND TIME(rotation.endDate) <= TIME('11:59:59') 
			)";

		if ($searchText != '') {
			$sql .= " AND (rotation.title LIKE '%" . $searchText . "%' OR hospitalsites.title LIKE '%" . $searchText . "%' OR courses.title LIKE '%" . $searchText . "%') ";
		}
		if ($to_date != '') {
			$sql .= " AND date(rotation.startDate) <= '" . $to_date . "'";
		}
		if ($from_date != '') {
			$sql .= " AND date(rotation.startDate) >= '" . $from_date . "'";
		}

		if ($studentId) {
			$sql .= " AND rotationdetails.studentId=" . $studentId;
		}

		if ($hospitalsiteId) {
			$sql .= " AND rotation.hospitalsiteId=" . $hospitalsiteId;
		}

		$sql .= " and rotation.schoolId=" . $schoolId . "  GROUP BY rotation.rotationId
			
		) AS combined_subquery";

		if ($limitString != '') {
			$sql .= $limitString;
		}
		// echo $sql;
		//  exit;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetInactiveRotationsBySchool($schoolId = 0,  $rotationId, $from_date, $to_date, $courseId = 0, $studentId = 0, $hospitalsiteId = 0, $limitString = '', $searchText = '')
	{
		$objDB = new clsDB();
		$rows = "";
		$curdate = date("y-m-d h:i:s");
		$todaysDate = date("Y-m-d");
		$repeatday = date('w', strtotime($curdate));

		// Combine similar queries using derived tables
		$sql = "
	    SELECT 
	        derived_table.rotationId, derived_table.rotationName AS rotationName, derived_table.startDate, derived_table.endDate,
	        derived_table.parentRotationId, derived_table.duration, derived_table.courseId, derived_table.hospitalSiteId,  derived_table.studentId, derived_table.isSchedule,
	        derived_table.courseTitle,derived_table.courseHours, derived_table.hospitalTitle,
	        0 as isInActiveRotation,  derived_table.locationId,derived_table.locationTitle AS locationTitle,
	        derived_table.rotationLocationId, derived_table.locationId,
			IFNULL(
				COUNT(
					CASE
						WHEN  (student.isActive = 1 AND (rankmaster.title != 'Graduate' AND rankmaster.title NOT LIKE '%Graduate%') AND rankmaster.title != 'Dropout')
						THEN rotationdetails.rotationId
					END
				),
				0
			) AS studentCount, 
			(select count(*) from rotation SUBR WHERE SUBR.parentRotationId= derived_table.rotationId) AS subratoncount 
	    FROM (

			SELECT 
			rotation.rotationId, rotation.title  as rotationName, rotation.startDate, rotation.endDate,
			rotation.parentRotationId, rotation.duration, rotation.courseId, rotation.hospitalSiteId, rotationdetails.studentId, rotation.isSchedule,
			courses.title as courseTitle,courses.courseHours, hospitalsites.title as hospitalTitle, location.locationId, location.title as locationTitle,
			rotation.locationId as rotationLocationId
		FROM rotation
		INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId
		INNER JOIN courses ON rotation.courseId = courses.courseId
		INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
		LEFT JOIN location ON courses.locationId = location.locationId
		INNER JOIN rotationscheduledates ON rotationscheduledates.rotationId = rotation.rotationId AND rotationscheduledates.scheduleDate != '$todaysDate'
		WHERE rotation.isSchedule=1 AND  rotation.schoolId = " . $schoolId . " 
			
			AND rotation.isDelete = 0 
			AND 0 = (
				SELECT count(*) 
				FROM rotation r 
				WHERE r.parentRotationId = rotation.rotationId
			)

			AND (
				(CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
				OR
				(rotation.parentRotationId > 0
				 AND EXISTS (
					 SELECT 1
					 FROM rotation AS parent
					 WHERE parent.rotationId = rotation.parentRotationId
					 AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
					 AND rotation.startDate BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
					 AND rotation.endDate BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
					 AND parent.schoolId = " . $schoolId . " AND rotation.isSchedule = 1
					 AND rotation.startDate <= '$todaysDate'
				 )
				)
			)
			AND rotation.rotationId NOT IN (
				SELECT rotationId
				FROM rotationscheduledates
				WHERE  DATE(scheduleDate) = '$todaysDate'
			)

			" . ($courseId > 0 ? "AND rotation.courseId = $courseId" : "") . "
			" . ($searchText != '' ? "AND (rotation.title LIKE '%$searchText%' OR hospitalsites.title LIKE '%$searchText%' OR courses.title LIKE '%$searchText%')" : "") . "
		GROUP BY rotation.rotationId
		
		UNION

		SELECT 
			rotation.rotationId, rotation.title as rotationName, rotation.startDate, rotation.endDate,
			rotation.parentRotationId, rotation.duration, rotation.courseId, rotation.hospitalSiteId,  rotationdetails.studentId, rotation.isSchedule,
			courses.title as courseTitle,courses.courseHours, hospitalsites.title as hospitalTitle,location.locationId, location.title as locationTitle, 
			rotation.locationId as rotationLocationId
		FROM rotation
		INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId
		INNER JOIN courses ON rotation.courseId = courses.courseId
		INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
		LEFT JOIN location ON courses.locationId = location.locationId
		INNER JOIN rotationrepeatdays ON rotation.rotationId = rotationrepeatdays.rotationId AND NOT EXISTS (
			SELECT *
			FROM rotationrepeatdays
			WHERE rotationrepeatdays.rotationId = rotation.rotationId
			AND rotationrepeatdays.dayofweek = $repeatday
		)
		WHERE rotation.isDelete = 0 AND rotation.isSchedule = 0 
			" . ($courseId > 0 ? "AND rotation.courseId = $courseId" : "") . "
			" . ($searchText != '' ? "AND (rotation.title LIKE '%$searchText%' OR hospitalsites.title LIKE '%$searchText%' OR courses.title LIKE '%$searchText%')" : "") . "
			AND rotation.schoolId=" . $schoolId . " 
			AND '" . $todaysDate . "' BETWEEN date(rotation.startDate) AND date(rotation.endDate)
			
		GROUP BY rotation.rotationId	       
	    ) AS derived_table
		INNER JOIN rotationdetails ON derived_table.rotationId = rotationdetails.rotationId
		INNER JOIN student ON rotationdetails.studentId = student.studentId
		INNER JOIN rankmaster ON student.rankId = rankmaster.rankId
	    ";
		if ($to_date != '') {
			$sql .= " AND date(derived_table.startDate) <= '" . $to_date . "'";
		}
		if ($from_date != '') {
			$sql .= " AND date(derived_table.startDate) >= '" . $from_date . "'";
		}
		if ($studentId) {
			$sql .= " AND derived_table.studentId=" . $studentId;
		}

		if ($hospitalsiteId) {
			$sql .= " AND derived_table.hospitalsiteId=" . $hospitalsiteId;
		}
		if ($rotationId) {
			$sql .= " AND derived_table.rotationId=" . $rotationId;
		}
		// Additional conditions
		$sql .= " GROUP BY derived_table.rotationId";

		// Add a common ORDER BY for all query blocks
		$sql .= " ORDER BY derived_table.startDate DESC"; // Adjust the sorting order as needed

		// Add a common LIMIT for all query blocks
		if ($limitString != '') $sql .= " $limitString";

		// echo '-----------------------' . $sql;

		// Execute the query
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllActiveRotationsByClinicianRoleForApp($schoolId = 0,  $clinicianId = 0, $rotationId, $from_date, $to_date, $courseId = 0, $studentId = 0, $hospitalsiteId = 0, $limitString = '', $searchText = '')
	{
		$objDB = new clsDB();
		$rows = "";

		$CurrentDate = date("Y-m-d h:i:s");
		$todaysDate = date("Y-m-d");
		$repeatday = date('w', strtotime($CurrentDate));

		// AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)
		//CLOCKOUT
		$sql = "SELECT rotation.rotationId, rotation.title as rotationName, rotation.startDate, rotation.endDate,
		rotation.parentRotationId, rotation.duration, rotation.courseId, rotation.hospitalSiteId, rotation.isSchedule,
				courses.title as courseTitle,courses.courseHours, 
				hospitalsites. title as hospitalTitle, 0 as isUpcoming ,0 as isClockIn,location.locationId, location.title as locationTitle, 
				courses.locationId as courseLocationId, clinicianhospitalsite.clinicianId,
				rotation.locationId as rotationLocationId,
				IFNULL(
				COUNT(
					CASE
						WHEN  (student.isActive = 1 AND (rankmaster.title != 'Graduate' AND rankmaster.title NOT LIKE '%Graduate%') AND rankmaster.title != 'Dropout')
						THEN rotationdetails.rotationId
					END
				),
				0) AS studentCount	,
				(select count(*) from rotation SUBR WHERE SUBR.parentRotationId= rotation.rotationId) AS subratoncount 

			FROM rotation
			INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId
			INNER JOIN student on rotationdetails.studentId = student.studentId
			INNER JOIN rankmaster ON student.rankId = rankmaster.rankId
			INNER JOIN courses ON rotation.courseId = courses.courseId
			LEFT JOIN location ON courses.locationId = location.locationId
			INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
			INNER JOIN clinicianhospitalsite ON clinicianhospitalsite.hospitalSiteId=rotation.hospitalSiteId
			INNER JOIN rotationrepeatdays ON rotation.rotationId = rotationrepeatdays.rotationId
							AND rotationrepeatdays.dayOfWeek=" . $repeatday . "
				WHERE clinicianhospitalsite.clinicianId =" . $clinicianId . " AND rotation.isDelete =0 AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId) AND rotation.isSchedule = 0
				 ";


		$sql .= " AND '" . $todaysDate . "' BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate)";

		if ($to_date != '') {
			$sql .= " AND date(rotation.startDate) <= '" . $to_date . "'";
		}
		if ($from_date != '') {
			$sql .= " AND date(rotation.startDate) >= '" . $from_date . "'";
		}
		if ($studentId) {
			$sql .= " AND rotationdetails.studentId=" . $studentId;
		}

		if ($hospitalsiteId) {
			$sql .= " AND rotation.hospitalsiteId=" . $hospitalsiteId;
		}

		if ($courseId > 0) {
			$sql .= " AND courses.courseId=" . $courseId;
		}

		if ($searchText != '') {
			$sql .= " AND (rotation.title LIKE '%" . $searchText . "%' OR hospitalsites.title LIKE '%" . $searchText . "%' OR courses.title LIKE '%" . $searchText . "%') ";
		}

		$sql .= " and rotation.schoolId=" . $schoolId . "  GROUP BY rotation.rotationId";

		// schedule
		$sql .= " UNION DISTINCT ";
		$sql .= "SELECT DISTINCT * FROM (";

		$sql .= "SELECT rotation.rotationId, rotation.title as rotationName, rotation.startDate, rotation.endDate,
		rotation.parentRotationId, rotation.duration, rotation.courseId, rotation.hospitalSiteId, rotation.isSchedule,location.locationId, location.title as locationTitle, 
        courses.title as courseTitle, courses.courseHours,  clinicianhospitalsite.clinicianId,
        hospitalsites.title as hospitalTitle,
        0 as isUpcoming,
        0 as isClockIn,
        courses.locationId as courseLocationId,
        rotation.locationId as rotationLocationId,
		IFNULL(
				COUNT(
					CASE
						WHEN  (student.isActive = 1 AND (rankmaster.title != 'Graduate' AND rankmaster.title NOT LIKE '%Graduate%') AND rankmaster.title != 'Dropout')
						THEN rotationdetails.rotationId
					END
				),
				0) AS studentCount,
				(select count(*) from rotation SUBR WHERE SUBR.parentRotationId= rotation.rotationId) AS subratoncount 

		FROM rotation
		INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId
		INNER JOIN student on rotationdetails.studentId = student.studentId
		INNER JOIN rankmaster ON student.rankId = rankmaster.rankId
		INNER JOIN courses ON rotation.courseId = courses.courseId
		LEFT JOIN location ON courses.locationId = location.locationId
		INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
		INNER JOIN clinicianhospitalsite ON clinicianhospitalsite.hospitalSiteId=rotation.hospitalSiteId
		INNER JOIN rotationrepeatdays ON rotation.rotationId = rotationrepeatdays.rotationId
						AND rotationrepeatdays.dayOfWeek=" . $repeatday . "
	   INNER JOIN rotationscheduledates ON rotation.rotationId = rotationscheduledates.rotationId
		 WHERE clinicianhospitalsite.clinicianId =" . $clinicianId . " AND rotation.isSchedule = 1 AND DATE_FORMAT(scheduleDate, '%Y-%m-%d') = '" . $todaysDate . "'
		 AND rotation.isDelete =0  AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)
		  ";
		$sql .= " AND (
			(rotation.parentRotationId > 0
			AND EXISTS (
				SELECT 1
				FROM rotation AS parent
				WHERE parent.rotationId = rotation.parentRotationId
				AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
				AND rotation.startDate BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
				AND rotation.endDate BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
				AND parent.schoolId = " . $schoolId . " AND rotation.isSchedule = 1
				AND DATE_FORMAT(rotation.startDate, '%Y-%m-%d') <= '" . $todaysDate . "'    
			)
			)
	)";

		$sql .= " AND '" . $todaysDate . "' BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate)";

		if ($searchText != '') {
			$sql .= " AND (rotation.title LIKE '%" . $searchText . "%' OR hospitalsites.title LIKE '%" . $searchText . "%' OR courses.title LIKE '%" . $searchText . "%') ";
		}

		if ($to_date != '') {
			$sql .= " AND date(rotation.startDate) <= '" . $to_date . "'";
		}
		if ($from_date != '') {
			$sql .= " AND date(rotation.startDate) >= '" . $from_date . "'";
		}
		if ($studentId) {
			$sql .= " AND rotationdetails.studentId=" . $studentId;
		}

		if ($hospitalsiteId) {
			$sql .= " AND rotation.hospitalsiteId=" . $hospitalsiteId;
		}

		$sql .= " and rotation.schoolId=" . $schoolId . "  GROUP BY rotation.rotationId";

		$sql .= " UNION DISTINCT ";

		// PM to AM
		$sql .= "SELECT rotation.rotationId, rotation.title as rotationName, rotation.startDate, rotation.endDate,
		rotation.parentRotationId, rotation.duration, rotation.courseId, rotation.hospitalSiteId, rotation.isSchedule,location.locationId, location.title as locationTitle, 
        courses.title as courseTitle, courses.courseHours,  clinicianhospitalsite.clinicianId,
        hospitalsites.title as hospitalTitle,
        0 as isUpcoming,
        0 as isClockIn,
        courses.locationId as courseLocationId,
        rotation.locationId as rotationLocationId,
		IFNULL(
				COUNT(
					CASE
						WHEN  (student.isActive = 1 AND (rankmaster.title != 'Graduate' AND rankmaster.title NOT LIKE '%Graduate%') AND rankmaster.title != 'Dropout')
						THEN rotationdetails.rotationId
					END
				),
				0) AS studentCount,
				(select count(*) from rotation SUBR WHERE SUBR.parentRotationId= rotation.rotationId) AS subratoncount 

		FROM rotation
		INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId
		INNER JOIN student on rotationdetails.studentId = student.studentId
		INNER JOIN rankmaster ON student.rankId = rankmaster.rankId
		INNER JOIN courses ON rotation.courseId = courses.courseId
		LEFT JOIN location ON courses.locationId = location.locationId
		INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
		INNER JOIN clinicianhospitalsite ON clinicianhospitalsite.hospitalSiteId=rotation.hospitalSiteId
		INNER JOIN rotationrepeatdays ON rotation.rotationId = rotationrepeatdays.rotationId
	
			WHERE clinicianhospitalsite.clinicianId =" . $clinicianId . " AND '" . $todaysDate . "' BETWEEN date(rotation.startDate) AND DATE_ADD(date(rotation.endDate), INTERVAL 1 DAY) 
			AND rotation.isDelete = 0 AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId) AND rotation.isSchedule = 1
			AND (
				TIME(rotation.startDate) >= TIME('12:00:00') 
				AND TIME(rotation.endDate) <= TIME('11:59:59') 
			)";
		if ($searchText != '') {
			$sql .= " AND (rotation.title LIKE '%" . $searchText . "%' OR hospitalsites.title LIKE '%" . $searchText . "%' OR courses.title LIKE '%" . $searchText . "%') ";
		}
		if ($to_date != '') {
			$sql .= " AND date(rotation.startDate) <= '" . $to_date . "'";
		}
		if ($from_date != '') {
			$sql .= " AND date(rotation.startDate) >= '" . $from_date . "'";
		}
		if ($studentId) {
			$sql .= " AND rotationdetails.studentId=" . $studentId;
		}

		if ($hospitalsiteId) {
			$sql .= " AND rotation.hospitalsiteId=" . $hospitalsiteId;
		}
		$sql .= " and rotation.schoolId=" . $schoolId . "  GROUP BY rotation.rotationId
			
		) AS combined_subquery";

		if ($limitString != '') {
			$sql .= $limitString;
		}
		// echo $sql; exit;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}


	function GetInactiveRotationsByClinicianRoleForApp($schoolId = 0, $clinicianId = 0, $rotationId, $from_date, $to_date, $courseId = 0, $studentId = 0, $hospitalsiteId = 0,  $limitString = '', $searchText = '')
	{
		$objDB = new clsDB();
		$rows = "";
		$curdate = date("y-m-d h:i:s");
		$todaysDate = date("Y-m-d");
		$repeatday = date('w', strtotime($curdate));

		// Combine similar queries using derived tables
		$sql = "
	    SELECT 
	        derived_table.rotationId, derived_table.rotationName AS rotationName, derived_table.startDate, derived_table.endDate,
	        derived_table.parentRotationId, derived_table.duration, derived_table.courseId, derived_table.hospitalSiteId,derived_table.studentId, derived_table.isSchedule,
	        derived_table.courseTitle,derived_table.courseHours, derived_table.hospitalTitle,
	        0 as isInActiveRotation,  derived_table.locationId,derived_table.locationTitle AS locationTitle,
	        derived_table.rotationLocationId, derived_table.locationId, derived_table.clinicianId,
			IFNULL(
				COUNT(
					CASE
						WHEN  (student.isActive = 1 AND (rankmaster.title != 'Graduate' AND rankmaster.title NOT LIKE '%Graduate%') AND rankmaster.title != 'Dropout')
						THEN rotationdetails.rotationId
					END
				),
				0
			) AS studentCount,
			(select count(*) from rotation SUBR WHERE SUBR.parentRotationId= derived_table.rotationId) AS subratoncount 

	    FROM (

			SELECT 
			rotation.rotationId, rotation.title  as rotationName, rotation.startDate, rotation.endDate,
			rotation.parentRotationId, rotation.duration, rotation.courseId, rotation.hospitalSiteId,  rotationdetails.studentId, rotation.isSchedule,
			courses.title as courseTitle,courses.courseHours, hospitalsites.title as hospitalTitle, location.locationId, location.title as locationTitle,
			rotation.locationId as rotationLocationId, clinicianhospitalsite.clinicianId
		FROM rotation
		INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId
		INNER JOIN courses ON rotation.courseId = courses.courseId
		INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
		LEFT JOIN location ON courses.locationId = location.locationId
		INNER JOIN clinicianhospitalsite ON clinicianhospitalsite.hospitalSiteId=rotation.hospitalSiteId
		INNER JOIN rotationscheduledates ON rotationscheduledates.rotationId = rotation.rotationId AND rotationscheduledates.scheduleDate != '$todaysDate'
		WHERE clinicianhospitalsite.clinicianId =" . $clinicianId . " AND rotation.isSchedule=1 AND  rotation.schoolId = " . $schoolId . " 
			
			AND rotation.isDelete = 0 
			AND 0 = (
				SELECT count(*) 
				FROM rotation r 
				WHERE r.parentRotationId = rotation.rotationId
			)
			AND (
				(CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
				OR
				(rotation.parentRotationId > 0
				 AND EXISTS (
					 SELECT 1
					 FROM rotation AS parent
					 WHERE parent.rotationId = rotation.parentRotationId
					 AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
					 AND rotation.startDate BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
					 AND rotation.endDate BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
					 AND parent.schoolId = " . $schoolId . " AND rotation.isSchedule = 1
					 AND rotation.startDate <= '$todaysDate'
				 )
				)
			)
			AND rotation.rotationId NOT IN (
				SELECT rotationId
				FROM rotationscheduledates
				WHERE  DATE(scheduleDate) = '$todaysDate'
			)
			" . ($courseId > 0 ? "AND rotation.courseId = $courseId" : "") . "
			" . ($searchText != '' ? "AND (rotation.title LIKE '%$searchText%' OR hospitalsites.title LIKE '%$searchText%' OR courses.title LIKE '%$searchText%')" : "") . "
		GROUP BY rotation.rotationId

		UNION

	        SELECT 
	            rotation.rotationId, rotation.title as rotationName, rotation.startDate, rotation.endDate,
	            rotation.parentRotationId, rotation.duration, rotation.courseId, rotation.hospitalSiteId,  rotationdetails.studentId, rotation.isSchedule,
	            courses.title as courseTitle,courses.courseHours, hospitalsites.title as hospitalTitle,location.locationId, location.title as locationTitle, 
	            rotation.locationId as rotationLocationId, clinicianhospitalsite.clinicianId
	        FROM rotation
	        INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId
	        INNER JOIN courses ON rotation.courseId = courses.courseId
	        INNER JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
			LEFT JOIN location ON courses.locationId = location.locationId
			INNER JOIN clinicianhospitalsite ON clinicianhospitalsite.hospitalSiteId=rotation.hospitalSiteId
	        INNER JOIN rotationrepeatdays ON rotation.rotationId = rotationrepeatdays.rotationId AND NOT EXISTS (
	            SELECT *
	            FROM rotationrepeatdays
	            WHERE rotationrepeatdays.rotationId = rotation.rotationId
	            AND rotationrepeatdays.dayofweek = $repeatday
	        )
	        WHERE  clinicianhospitalsite.clinicianId =" . $clinicianId . " AND rotation.isDelete = 0
	            " . ($courseId > 0 ? "AND rotation.courseId = $courseId" : "") . "
	            " . ($searchText != '' ? "AND (rotation.title LIKE '%$searchText%' OR hospitalsites.title LIKE '%$searchText%' OR courses.title LIKE '%$searchText%')" : "") . "
				AND rotation.schoolId=" . $schoolId . " 
				AND '" . $todaysDate . "' BETWEEN date(rotation.startDate) AND date(rotation.endDate)
				
	        GROUP BY rotation.rotationId
	    ) AS derived_table
		INNER JOIN rotationdetails ON derived_table.rotationId = rotationdetails.rotationId
		INNER JOIN student ON rotationdetails.studentId = student.studentId
		INNER JOIN rankmaster ON student.rankId = rankmaster.rankId
	    ";
		if ($to_date != '') {
			$sql .= " AND date(derived_table.startDate) <= '" . $to_date . "'";
		}
		if ($from_date != '') {
			$sql .= " AND date(derived_table.startDate) >= '" . $from_date . "'";
		}

		if ($studentId) {
			$sql .= " AND derived_table.studentId=" . $studentId;
		}

		if ($hospitalsiteId) {
			$sql .= " AND derived_table.hospitalsiteId=" . $hospitalsiteId;
		}
		// Additional conditions
		$sql .= " GROUP BY derived_table.rotationId";

		// Add a common ORDER BY for all query blocks
		$sql .= " ORDER BY derived_table.startDate DESC"; // Adjust the sorting order as needed

		// Add a common LIMIT for all query blocks
		if ($limitString != '') $sql .= " $limitString";

		// echo '-----------------------' . $sql;

		// Execute the query
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}


	function GetRotationByClinicianRoleForApp($SchoolId, $clinicianId, $rotationId, $from_date = '', $to_date = '', $isActive = 0, $limitString = '', $searchText = '')
	{
		$objDB = new clsDB();
		$rows = "";
		$todaysDate = date("Y-m-d");
		$sql = " SELECT rotation.title as rotationName, rotation.startDate,rotation.endDate,rotation.rotationId, rotation.startDate, rotation.endDate, rotation.duration, 
				courses.courseId, courses.title as courseTitle,hospitalsites.title as hospitalSite,hospitalsites.hospitalSiteId,
				location.locationId, location.title as locationTitle, 
				IFNULL(
				COUNT(
					CASE
						WHEN  (student.isActive = 1 AND (rankmaster.title != 'Graduate' AND rankmaster.title NOT LIKE '%Graduate%') AND rankmaster.title != 'Dropout')
						THEN rotationdetails.rotationId
					END
				),
				0) AS studentCount	,
				(select count(*) from rotation SUBR WHERE SUBR.parentRotationId= rotation.rotationId) AS subratoncount ,
				rotation.locationId as rotationLocationId
				FROM rotation
				LEFT JOIN courses ON rotation.courseId = courses.courseId
				LEFT JOIN location ON courses.locationId = location.locationId
				INNER JOIN hospitalsites  ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
				LEFT JOIN rotationdetails ON rotation.rotationId  = rotationdetails.rotationId 
				INNER JOIN clinicianhospitalsite ON rotation.hospitalSiteId = clinicianhospitalsite.hospitalSiteId 
				INNER JOIN clinician ON clinicianhospitalsite.clinicianId=clinician.clinicianId
				INNER JOIN student on rotationdetails.studentId = student.studentId
				INNER JOIN rankmaster ON student.rankId = rankmaster.rankId";

		$sql .= " WHERE rotation.schoolId=" . $SchoolId . " AND rotation.isDelete =0 AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)";
		$sql .= " AND clinician.clinicianId=" . $clinicianId;

		if ($rotationId) {
			$sql .= " AND rotation.rotationId=" . $rotationId . " AND student.isActive= 1";
		}
		if ($to_date != '') {
			$sql .= " AND date(rotation.endDate) <= '" . $to_date . "'";
		}
		if ($from_date != '') {
			$sql .= " AND date(rotation.startDate) >= '" . $from_date . "'";
		}

		if ($isActive != 1) {
			$sql .= " AND CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate)";

			// $sql .= " 
			// AND (
			// 			  ('" . $todaysDate . "' BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
			// 			  OR
			// 			  (rotation.parentRotationId > 0
			// 			   AND EXISTS (
			// 				   SELECT 1
			// 				   FROM rotation AS parent
			// 				   WHERE parent.rotationId = rotation.parentRotationId
			// 				   AND '" . $todaysDate . "' BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
			// 				   AND parent.schoolId = " . $SchoolId . " AND rotation.isSchedule = 1 
			// 			   )
			// 			  )
			// 		  )";
		} else {
			$sql .= " AND '" . $todaysDate . "'NOT BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate)";
		}
		if ($searchText != "") {
			$sql .= " AND (hospitalsites.title LIKE '%" . $searchText . "%' OR rotation.title LIKE '%" . $searchText . "%' OR courses.title LIKE '%" . $searchText . "%') ";
		}
		$sql .= " GROUP BY rotation.rotationId ORDER BY rotation.startDate DESC" . $limitString;

		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function getAllRotationsByStudent($schoolId, $studentId)
	{
		$objDB = new clsDB();
		$rows = "";

		$sql = "Select rotation.*, CONCAT(rotation.title, ' (', hospitalsites.title, ')') as title 
		from checkoff
		 inner join rotation on rotation.rotationId = checkoff.rotationId
		 LEFT JOIN hospitalsites ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
		  where checkoff.studentId = $studentId 
		 
		 AND rotation.schoolId = $schoolId group by checkoff.rotationId";

		echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetrotationForProcedureCountForApp($schoolId, $studentId)
	{
		$objDB = new clsDB();
		$rows = "";

		$sql = "SELECT  rotation.title,rotation.rotationId,hospitalsites.hospitalSiteId, hospitalsites.title as hospitalTitle,
				checkoff.rotationId, checkoff.studentId, student.studentId,
				rotationdetails.rotationId, rotationdetails.studentId,CONCAT(rotation.title, ' (', hospitalsites.title, ')') as title
				FROM rotation 
				LEFT JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId
				LEFT JOIN student ON rotationdetails.studentId=student.studentId
				LEFT JOIN checkoff ON checkoff.rotationId=rotation.rotationId
				LEFT JOIN hospitalsites ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId";
		/* if($rotationId > 0)
		 {
			$sql .= " WHERE rotation.rotationId=".$rotationId;	
		 }*/
		if ($schoolId > 0) {
			$sql .= " WHERE rotation.schoolId=" . $schoolId;
		}
		if ($studentId > 0) {
			$sql .= " AND  rotationdetails.studentId=" . $studentId;
		}
		// $sql .= " AND CURDATE() BETWEEN date(rotation.startDate) AND date(rotation.endDate)";
		$sql .= " AND rotation.isDelete =0 AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId) 
		 AND (
              (CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
              OR
              (rotation.parentRotationId > 0
               AND EXISTS (
                   SELECT 1
                   FROM rotation AS parent
                   WHERE parent.rotationId = rotation.parentRotationId
                   AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
                   AND parent.schoolId = " . $schoolId . " AND rotation.isSchedule = 1 
               )
              )
          )
		  ";
		$sql .= " GROUP BY rotation.rotationId order by rotation.title";

		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetRotationForApp($schoolId, $studentId = 0, $type = '')
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT rotation.*, rotationdetails.*,
				courses.title as courseTitle,location.locationId, location.title as locationTitle,
				schools.schoolId,student.*,
				hospitalsites.hospitalSiteId,hospitalsites. title as hospitalTitle, attendance.rotationId as clockedOutRotationId, CONCAT(rotation.title, ' (', hospitalsites.title, ')') as title,
				 rotation.rotationId
				FROM rotation
				INNER JOIN schools ON rotation.schoolId=schools.schoolId
				LEFT JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
				LEFT JOIN  student ON rotationdetails.studentId =student.studentId
				LEFT JOIN courses ON rotation.courseId = courses.courseId
				LEFT JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
				LEFT JOIN location ON courses.locationId = location.locationId
				LEFT JOIN attendance ON rotation.rotationId = attendance.rotationId AND attendance.isClockOut = 1
				WHERE schools.schoolId=" . $schoolId . " ";
		// AND CURDATE() BETWEEN date(rotation.startDate) AND date(rotation.endDate)";

		if ($studentId > 0 && $type != 'dailyweekly') {
			$sql .= " AND rotationdetails.studentId=" . $studentId;
		}

		// 			$sql .= " AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)";	
		// $sql .=	" AND rotation.isDelete =0  GROUP BY rotation.rotationId";
		// $sql .= " AND rotation.isDelete =0 AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId) 
		// 			AND (
		// 				(CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
		// 				OR
		// 				(rotation.parentRotationId > 0
		// 				AND EXISTS (
		// 					SELECT 1
		// 					FROM rotation AS parent
		// 					WHERE parent.rotationId = rotation.parentRotationId
		// 					AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
		// 					AND parent.schoolId = " . $schoolId . " AND rotation.isSchedule = 1 
		// 				)
		// 				)
		// 			)
		// 		";
		$sql .= " GROUP BY rotation.rotationId ORDER BY rotation.title";
		// ECHO $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function getAllCheckoffRotationsByStudentForApp($schoolId, $studentId)
	{
		$objDB = new clsDB();
		$rows = "";

		$sql = "SELECT rotation.rotationId,rotation.title,rotation.startDate,hospitalsites.hospitalSiteId, hospitalsites.title as hospitalTitle, CONCAT(rotation.title, ' (', hospitalsites.title, ')') as title FROM rotation 
				INNER JOIN rotationdetails on rotationdetails.rotationId = rotation.rotationId
				LEFT JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
			WHERE rotation.schoolId=" . $schoolId . "   AND rotation.isDelete =0
			AND (
			(CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
			OR
			(rotation.parentRotationId > 0
			AND EXISTS (
				SELECT 1
				FROM rotation AS parent
				WHERE parent.rotationId = rotation.parentRotationId
				AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
				AND parent.schoolId = " . $schoolId . " AND rotation.isSchedule = 1 
			)
			)
		)
			AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)";
		if ($studentId > 0) {
			$sql .= " AND rotationdetails.studentId=" . $studentId;
		}
		$sql .= " GROUP by rotation.rotationId ORDER BY rotation.title";

		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllrotationByClinicianForApp($schoolId, $clinicianId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = " SELECT rotation.*, courses.courseId, courses.title as courseTitle, hospitalsites.title as hospitalTitle, hospitalsites.hospitalSiteId,
		location.locationId, location.title as locationTitle, IFNULL(count(rotationdetails.rotationId),0) as studentCount,
		(select count(*) from rotation SUBR WHERE SUBR.parentRotationId= rotation.rotationId) AS subratoncount,CONCAT(rotation.title, ' (', hospitalsites.title, ')') as title
		FROM rotation
		LEFT JOIN courses ON rotation.courseId = courses.courseId
		LEFT JOIN location ON courses.locationId = location.locationId
		LEFT JOIN rotationdetails ON rotationdetails.rotationId = rotation.rotationId 
		INNER JOIN clinicianhospitalsite ON rotation.hospitalSiteId=clinicianhospitalsite.hospitalSiteId
		INNER JOIN clinician ON clinicianhospitalsite.clinicianId=clinician.clinicianId
		LEFT JOIN hospitalsites ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
		";

		$sql .= " WHERE rotation.schoolId=" . $schoolId;
		$sql .= " AND clinician.clinicianId=" . $clinicianId;
		$sql .= " AND rotation.isDelete =0 AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId) ";
		$sql .= " 
			AND (
						  (CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
						  OR
						  (rotation.parentRotationId > 0
						   AND EXISTS (
							   SELECT 1
							   FROM rotation AS parent
							   WHERE parent.rotationId = rotation.parentRotationId
							   AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
							   AND parent.schoolId = " . $schoolId . " AND rotation.isSchedule = 1 
						   )
						  )
					  )";
		$sql .= " GROUP BY rotation.rotationId ORDER BY rotation.title";
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetRotationDetailsByRotationId($rotationId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
				rotationId,parentRotationId,schoolId,title,startDate,endDate,isSchedule 
				FROM rotation where rotationId = $rotationId";

		// echo $sql;exit;
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}

	function GetAllActiveRotationForDropdown($schoolId, $studentId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT rotation.rotationId, CONCAT(rotation.title, ' (', hospitalsites.title, ')') as title, hospitalsites.hospitalSiteId
				FROM rotation
				INNER JOIN schools ON rotation.schoolId=schools.schoolId
				LEFT JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
				LEFT JOIN  student ON rotationdetails.studentId =student.studentId
				LEFT JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
				WHERE schools.schoolId=" . $schoolId . "";
		//  AND CURDATE() BETWEEN date(rotation.startDate) AND date(rotation.endDate)";
		$sql .= " AND rotation.isDelete =0 AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId) 
				AND (
					  (CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
					  OR
					  (rotation.parentRotationId > 0
					   AND EXISTS (
						   SELECT 1
						   FROM rotation AS parent
						   WHERE parent.rotationId = rotation.parentRotationId
						   AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
						   AND parent.schoolId = " . $schoolId . " AND rotation.isSchedule = 1 
					   )
					  )
				  )
				";
		if ($studentId > 0) {
			$sql .= " AND rotationdetails.studentId=" . $studentId;
		}

		$sql .= " GROUP BY rotation.rotationId ORDER BY rotation.title";
		// ECHO $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllRotationForDropdown($schoolId, $studentId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT rotation.rotationId, CONCAT(rotation.title, ' (', hospitalsites.title, ')') as title,hospitalsites.hospitalSiteId,  hospitalsites.title as hospitalTitle
				FROM rotation
				INNER JOIN schools ON rotation.schoolId=schools.schoolId
				LEFT JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
				LEFT JOIN  student ON rotationdetails.studentId =student.studentId
				LEFT JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
				WHERE schools.schoolId=" . $schoolId . "";
		//  AND CURDATE() BETWEEN date(rotation.startDate) AND date(rotation.endDate)";
		$sql .= " AND rotation.isDelete =0 AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId) ";
		// 		$sql .= " AND (
		// 			  (CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
		// 			  OR
		// 			  (rotation.parentRotationId > 0
		// 			   AND EXISTS (
		// 				   SELECT 1
		// 				   FROM rotation AS parent
		// 				   WHERE parent.rotationId = rotation.parentRotationId
		// 				   AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
		// 				   AND parent.schoolId = " . $schoolId . " AND rotation.isSchedule = 1 
		// 			   )
		// 			  )
		// 		  )
		// 		";
		if ($studentId > 0) {
			$sql .= " AND rotationdetails.studentId=" . $studentId;
		}

		$sql .= " GROUP BY rotation.rotationId ORDER BY rotation.title";
		// ECHO $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllrotationByClinicianForDropdown($schoolId, $clinicianId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT rotation.rotationId, CONCAT(rotation.title, ' (', hospitalsites.title, ')') as title
		FROM rotation
		
		LEFT JOIN rotationdetails ON rotationdetails.rotationId = rotation.rotationId 
		INNER JOIN clinicianhospitalsite ON rotation.hospitalSiteId=clinicianhospitalsite.hospitalSiteId
		INNER JOIN clinician ON clinicianhospitalsite.clinicianId=clinician.clinicianId
		LEFT JOIN hospitalsites ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
		";

		$sql .= " WHERE rotation.schoolId=" . $schoolId;
		$sql .= " AND clinician.clinicianId=" . $clinicianId;
		$sql .= " AND rotation.isDelete =0 AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId) ";
		// $sql .= " 
		// 	AND (
		// 				  (CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
		// 				  OR
		// 				  (rotation.parentRotationId > 0
		// 				   AND EXISTS (
		// 					   SELECT 1
		// 					   FROM rotation AS parent
		// 					   WHERE parent.rotationId = rotation.parentRotationId
		// 					   AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
		// 					   AND parent.schoolId = " . $schoolId . " AND rotation.isSchedule = 1 
		// 				   )
		// 				  )
		// 			  )";
		$sql .= " GROUP BY rotation.rotationId ORDER BY rotation.title";
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetCurrentrotationByClinicianForDropdown($schoolId, $clinicianId, $studentId = 0, $rotationId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = " SELECT rotation.rotationId, CONCAT(rotation.title, ' (', hospitalsites.title, ')') as title, hospitalsites.hospitalSiteId, hospitalsites.title as hospitalTitle
		FROM rotation
		
		LEFT JOIN rotationdetails ON rotationdetails.rotationId = rotation.rotationId 
		INNER JOIN clinicianhospitalsite ON rotation.hospitalSiteId=clinicianhospitalsite.hospitalSiteId
		INNER JOIN clinician ON clinicianhospitalsite.clinicianId=clinician.clinicianId
		LEFT JOIN hospitalsites ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
		";

		$sql .= " WHERE rotation.schoolId=" . $schoolId . " AND rotation.isDelete =0 ";
		if ($clinicianId > 0)
			$sql .= " AND clinician.clinicianId=" . $clinicianId;
		if ($studentId > 0) {
			$sql .= " AND rotationdetails.studentId=" . $studentId;
		}
		if ($rotationId > 0) {
			$sql .= " AND rotation.rotationId=" . $rotationId;
		}
		// $sql .= " AND rotation.isDelete =0 AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId) AND CURDATE() between startDate AND endDate ";
		// $sql .= " GROUP BY rotation.rotationId ORDER BY rotation.title";

		$sql .= " AND rotation.isDelete = 0 AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)
		AND (
              (CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
              OR
              (rotation.parentRotationId > 0
               AND EXISTS (
                   SELECT 1
                   FROM rotation AS parent
                   WHERE parent.rotationId = rotation.parentRotationId
                   AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
                   AND parent.schoolId = " . $schoolId . " AND rotation.isSchedule = 1 
               )
              )
          )
		";

		$sql .= " GROUP BY rotation.rotationId ORDER BY rotation.title";

		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllActiverotationByClinicianForDropdown($schoolId, $studentId = 0, $clinicianId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = " SELECT rotation.*, courses.courseId, courses.title as courseTitle,
					location.locationId, location.title as locationTitle, IFNULL(count(rotationdetails.rotationId),0) as studentCount,
					(select count(*) from rotation SUBR WHERE SUBR.parentRotationId= rotation.rotationId) AS subratoncount, CONCAT(rotation.title, ' (', hospitalsites.title, ')') as title
					FROM rotation
					LEFT JOIN courses ON rotation.courseId = courses.courseId
					LEFT JOIN location ON courses.locationId = location.locationId
					LEFT JOIN rotationdetails ON rotationdetails.rotationId = rotation.rotationId 
					LEFT JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
					INNER JOIN clinicianhospitalsite ON rotation.hospitalSiteId=clinicianhospitalsite.hospitalSiteId
					INNER JOIN clinician ON clinicianhospitalsite.clinicianId=clinician.clinicianId
					";
		$sql .= " WHERE rotation.schoolId=" . $schoolId . " AND 
				  CURDATE() BETWEEN date(rotation.startDate) AND date(rotation.endDate)";
		$sql .= " AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId) ";
		$sql .= " AND clinician.clinicianId=" . $clinicianId;
		if ($studentId > 0) {
			$sql .= " AND rotationdetails.studentId=" . $studentId;
		}
		$sql .= " GROUP BY rotation.rotationId ORDER BY rotation.title";
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllActiveRotationByStudent($schoolId, $studentId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT rotation.*, rotationdetails.*,
				courses.title as courseTitle,location.locationId, location.title as locationTitle,
				schools.schoolId,student.*,
				hospitalsites.hospitalSiteId,hospitalsites. title as hospitalTitle, attendance.rotationId as clockedOutRotationId , CONCAT(rotation.title, ' (', hospitalsites.title, ')') as title
				FROM rotation
				INNER JOIN schools ON rotation.schoolId=schools.schoolId
				LEFT JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
				LEFT JOIN  student ON rotationdetails.studentId =student.studentId
				LEFT JOIN courses ON rotation.courseId = courses.courseId
				LEFT JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
				LEFT JOIN location ON courses.locationId = location.locationId
				LEFT JOIN attendance ON rotation.rotationId = attendance.rotationId AND attendance.isClockOut = 1
				WHERE schools.schoolId=" . $schoolId . " AND rotation.isDelete =0";
		if ($studentId)
			$sql .= " AND rotationdetails.studentId=" . $studentId;

		$sql .= " AND CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate)";

		// $sql .= " 
		// 	AND (
		// 				  (CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
		// 				  OR
		// 				  (rotation.parentRotationId > 0
		// 				   AND EXISTS (
		// 					   SELECT 1
		// 					   FROM rotation AS parent
		// 					   WHERE parent.rotationId = rotation.parentRotationId
		// 					   AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
		// 					   AND parent.schoolId = " . $schoolId . " AND rotation.isSchedule = 1 
		// 				   )
		// 				  )
		// 			  )";
		$sql .= " GROUP BY rotation.rotationId";
		// ECHO $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetHospitalSiteStatusByRotationId($rotationId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT hospitalsites.hospitalSiteId FROM `rotation` 
				INNER JOIN hospitalsites ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
				WHERE rotation.rotationId = " . $rotationId . " AND hospitalsites.isHospital = 1 AND hospitalsites.isSms = 1";

		// echo $sql;exit;
		$rows = $objDB->GetSingleFieldValue($sql);
		return $rows;
		unset($objDB);
	}

	function GetLocationNameBySubRotationId($rotationId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT location.title FROM `location` 
				INNER JOIN rotation ON rotation.locationId = location.locationId
				WHERE rotation.rotationId = " . $rotationId . " AND rotation.parentRotationId > 0";

		// echo $sql;exit;
		$rows = $objDB->GetSingleFieldValue($sql);
		return $rows;
		unset($objDB);
	}

	function GetAllRotationDetailsForLogs($rotationId, $schoolId = 0)
	{
		$objDB = new clsDB();

		$sql = "SELECT r1.*, r1.title AS rotationName, hs.title AS hospitalSiteName, s.displayName AS schoolName, c.title AS courseName, r2.title AS parentRotationName FROM `rotation` r1
				INNER JOIN hospitalsites hs ON hs.hospitalSiteId = r1.hospitalSiteId
				INNER JOIN schools s ON s.schoolId = r1.schoolId 
				LEFT JOIN courses c ON r1.courseId = c.courseId 
				LEFT JOIN `rotation` r2 ON r1.parentRotationId = r2.rotationId
				WHERE r1.rotationId =" . $rotationId;

		if ($schoolId) {
			$sql .= " AND r1.schoolId=" . $schoolId;
		}
		// echo $sql;
		// exit;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}

	function getRotaionLogs($retrotationId, $currentSchoolId, $rotationId = 0, $isCopy = 0, $copiedRotaionName = '')
	{
		// Instantiate the Logger class
		$objLog = new clsLogger();

		// Determine the action type (EDIT or ADD) based on the presence of a journal ID
		$action = ($rotationId > 0) ? $objLog::EDIT : $objLog::ADD;

		// Instantiate the Journal class
		$objRotation = new clsRotation();

		// Retrieve journal details for the given journal ID and school ID
		$rowData = $objRotation->GetAllRotationDetailsForLogs($retrotationId, $currentSchoolId);

		// Free up resources by unsetting the Journal object after use
		unset($objRotation);

		// Initialize additional data (if any)
		$additionalData = array();

		// Define the fields to extract for logging
		$fieldsForLogData = [
			'schoolId' => 0,       // Default school ID
			'schoolName' => '',    // Default school name
			'rotationName' => ''   // Default rotation name
		];

		// Get the logged-in user's details from the session
		$userId = isset($_SESSION['loggedUserId']) ? $_SESSION['loggedUserId'] : 0;
		$loggedUserFirstName = isset($_SESSION['loggedUserFirstName']) ? trim($_SESSION['loggedUserFirstName']) : '';
		$loggedUserLastName = isset($_SESSION['loggedUserLastName']) ? trim($_SESSION['loggedUserLastName']) : '';
		$userName = ($loggedUserFirstName && $loggedUserLastName) ? $loggedUserFirstName . ' ' . $loggedUserLastName : '';


		// Extract the required fields for logging from the retrieved data
		$logData = extractFieldsForLogData($rowData, $fieldsForLogData);

		$isSchedule = isset($rowData['isSchedule']) ? $rowData['isSchedule'] : 0;

		if ($isSchedule == 1) {
			$studentList = $this->GetStudentListByRotationId($retrotationId);
			$studentListCount = ($studentList != '') ? mysqli_num_rows($studentList) : 0;
			if ($studentListCount > 0) {
				// Convert the result set into an array
				$studentData =  fetchResultAsArray($studentList);
				$additionalData['students'] = $studentData;
			}
		}
		// echo '<pre>';
		// print_r($additionalData);exit;


		$actionMessage = ($action == $objLog::ADD) ? 'Added' : 'updated';

		// Add extra details to the log data
		$logData['userId'] = $userId;                // ID of the logged-in user
		$logData['userName'] = $userName;            // Full name of the logged-in user
		$logData['userType'] = $objLog::ADMIN; 		 // User type (e.g., admin)
		$text = ($action == 'Add') ? 'for' : 'from';
		if ($isCopy == 1) {
			$action = $objLog::COPY;
			$logMessage = $logData['userName'] . ' made the copy of ' . $copiedRotaionName . ' Rotation.';
		} else {
			if ($rowData['parentRotationId'] == 0 && $rowData['isSchedule'] == 0) {
				$logMessage = $logData['userName'] . ' ' . $actionMessage . ' the ' . $logData['rotationName'] . ' Rotation.';
			} elseif ($rowData['parentRotationId'] > 0 && $rowData['isSchedule'] == 0) {
				$logMessage = $logData['userName'] . ' ' . $actionMessage . ' the ' . $logData['rotationName'] . ' Hospital Site ' . $text . ' ' . $rowData['parentRotationName'] . ' Rotation.';
			} else {
				$logMessage = $logData['userName'] . ' ' . $actionMessage . ' the ' . $logData['rotationName'] . ' Schedule ' . $text . ' ' . $rowData['parentRotationName'] . ' Rotation.';
			}
		}
		// Add extra details to the log data
		$logData['message'] = $logMessage;

		// Save the action (add/edit) details to the logs
		$objLog->saveLogs($logData, $action, $retrotationId, 'Rotation', $rowData, $additionalData);

		unset($objLog);
	}

	/**
	 * Retrieves a list of students assigned to a given rotation ID
	 *
	 * @param int $rotationId The rotation ID to retrieve students for
	 *
	 * @return array An array of student objects with their rotation details
	 */
	function GetStudentListByRotationId($rotationId, $studentIds = '')
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT rotationdetails.*, rotation.title AS rotationName,concat(student.firstName, ' ', student.lastName) AS studentName  FROM `rotationdetails` 
				INNER JOIN rotation ON rotation.rotationId = rotationdetails.rotationId
				LEFT JOIN student on student.studentId = rotationdetails.studentId
				WHERE rotationdetails.rotationId = $rotationId";

		if ($studentIds != '') {
			$sql .= " AND rotationdetails.studentId IN ($studentIds)";
		}

		// echo $sql;exit;
		$rows = $objDB->GetResultSet($sql);
		return $rows;
		unset($objDB);
	}

	function prepareChangeHospitalSiteLogData($id, $action, $userType = '', $userId = 0)
	{
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$objRotation = new clsRotation();

		// Retrieve Details
		$rowData = $objRotation->GetAllRotationDetailsForLogs($id);

		// Use the helper function to generate the log data
		if ($rowData != '')
			$logData = $objLog->generateLogData($rowData, $userType);

		$userDetails = getUserDetails($userId, $userType);
		$logData['userId'] = $userDetails['userId'];
		$logData['userName'] = $userDetails['userName'];
		// print_r($logData);exit;

		// Initialize additional data (if any)
		$additionalData = '';

		$logMessage = $logData['userName'] . ' changed the Hospital Site of ' . $logData['rotationName'] . ' rotation.';

		$logData['message'] = $logMessage;

		unset($objLog);
		unset($objRotation);
		return [$logData, $rowData, $additionalData];
	}

	function GetTodayActiveRotationsForClockOutNotification()
	{
		$objDB = new clsDB();
		$rows = "";

		$CurrentDate = date("Y-m-d h:i:s");
		$repeatday = date('w', strtotime($CurrentDate));

		$sql = "SELECT rotation.rotationId, rotation.parentRotationId, rotation.title,rotation.startDate,rotation.endDate, clockoutnotification.*,GROUP_CONCAT(rotationdetails.studentId) as studentIds, schools.countryCode,timezonemaster.timezone
				FROM rotation 
				INNER JOIN courses ON rotation.courseId = courses.courseId
				INNER JOIN schools ON schools.schoolId = rotation.schoolId
				INNER JOIN timezonemaster ON schools.timeZoneId = timezonemaster.timeZoneId
				INNER JOIN rotationdetails ON rotationdetails.rotationId = rotation.rotationId
				INNER JOIN student ON student.studentId = rotationdetails.studentId 
				INNER JOIN clockoutnotification ON clockoutnotification.schoolId = rotation.schoolId
				INNER JOIN rotationrepeatdays ON rotation.rotationId=rotationrepeatdays.rotationId
							AND rotationrepeatdays.dayOfWeek=" . $repeatday . "
				WHERE  rotation.isDelete =0 ";
		$sql .= " AND clockoutnotification.status = 1";

		$sql .= " AND CURDATE() BETWEEN date(rotation.startDate) AND date(rotation.endDate) AND student.isActive = 1 GROUP BY rotation.rotationId";

		// echo $sql;exit; 
		$rows = $objDB->GetResultset($sql);

		unset($objDB);
		return $rows;
	}
}
