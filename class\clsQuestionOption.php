<?php
class clsQuestionOption
{
	function GetQuestionOptionsOfsummative($questionId, $studentSummativeMasterId = 0)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
						schoolsummativequestiondetail.schoolSummativeQuestionDetailId AS OptionValue,
						schoolsummativequestiondetail.schoolSummativeQuestionId,
						schoolsummativequestiondetail.optionText,
						schoolsummativequestiondetail.schoolOptionValue,
						studentsummativedetail.studentSummativeMasterId,						
						studentsummativedetail.	schoolSummativeQuestionId,					
						studentsummativedetail.	schoolSummativeOptionValue AS SelectedOptionValue,					
						studentsummativedetail.	schoolSummativeOptionAnswerText AS TextAnswer, 					
						studentsummativedetail.	studentSummativeDetailId  AS DetailId					
						FROM schoolsummativequestiondetail
						LEFT JOIN studentsummativedetail ON studentsummativedetail.schoolSummativeQuestionId 
									= schoolsummativequestiondetail.schoolSummativeQuestionId
									AND studentsummativedetail.schoolSummativeOptionValue = 
									schoolsummativequestiondetail.schoolSummativeQuestionDetailId
									AND studentsummativedetail.studentSummativeMasterId = " . $studentSummativeMasterId;

		$sql .= " WHERE schoolsummativequestiondetail.schoolSummativeQuestionId=" . $questionId;
		$sql .= "  ORDER BY schoolsummativequestiondetail.schoolOptionValue";
		// echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}
	function GetTextAnsOfsummative($questionId, $studentSummativeMasterId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
						studentsummativedetail.studentSummativeDetailId,
						studentsummativedetail.schoolSummativeQuestionId,
						studentsummativedetail.schoolSummativeOptionAnswerText AS TextAnswer,
						studentsummativemaster.studentSummativeMasterId
						FROM studentsummativedetail
						INNER JOIN studentsummativemaster ON studentsummativedetail.studentSummativeMasterId=
													studentsummativemaster.studentSummativeMasterId";
		$sql .= " WHERE studentsummativedetail.schoolSummativeQuestionId=" . $questionId . " AND
						studentsummativemaster.studentSummativeMasterId=" . $studentSummativeMasterId;
		//echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}
	function GetQuestionOptionsOfCIEvaluation($questionId, $ciEvaluationMasterId = 0)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
						schoolcievaluationquestiondetail.schoolCIEvaluationQuestionDetailId AS OptionValue,
						schoolcievaluationquestiondetail.schoolCIEvaluationQuestionId,
						schoolcievaluationquestiondetail.optionText,
						schoolcievaluationquestiondetail.schoolOptionValue,
						cievaluationdetail.ciEvaluationMasterId,
						cievaluationdetail.schoolCIEvaluationQuestionId,
						cievaluationdetail.schoolCIEvaluationOptionValue AS SelectedOptionValue,
						cievaluationdetail.schoolCIEvaluationOptionAnswerText,
						cievaluationdetail.ciEvaluationDetaild AS DetailId
						FROM
						schoolcievaluationquestiondetail
						LEFT JOIN
						cievaluationdetail ON cievaluationdetail.schoolCIEvaluationQuestionId = schoolcievaluationquestiondetail.schoolCIEvaluationQuestionId
						AND cievaluationdetail.schoolCIEvaluationOptionValue = schoolcievaluationquestiondetail.schoolCIEvaluationQuestionDetailId 
						AND cievaluationdetail.ciEvaluationMasterId =" . $ciEvaluationMasterId;
		$sql .= " WHERE schoolcievaluationquestiondetail.schoolCIEvaluationQuestionId=" . $questionId;

		$sql .= "  ORDER BY schoolcievaluationquestiondetail.schoolOptionValue";
		//echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetQuestionOptionsOfPEvaluation($questionId, $pEvaluationMasterId = 0)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
						schoolpevaluationquestiondetail.schoolPEvaluationQuestionDetailId AS OptionValue,
						schoolpevaluationquestiondetail.schoolPEvaluationQuestionId,
						schoolpevaluationquestiondetail.optionText,
						schoolpevaluationquestiondetail.schoolOptionValue,
						pevaluationdetail.pEvaluationMasterId,
						pevaluationdetail.schoolPEvaluationQuestionId,
						pevaluationdetail.schoolPEvaluationOptionValue AS SelectedOptionValue,
						pevaluationdetail.schoolPEvaluationOptionAnswerText,
						pevaluationdetail.pEvaluationDetaild AS DetailId
						FROM
						schoolpevaluationquestiondetail
						LEFT JOIN
						pevaluationdetail ON pevaluationdetail.schoolPEvaluationQuestionId = schoolpevaluationquestiondetail.schoolPEvaluationQuestionId
						AND pevaluationdetail.schoolPEvaluationOptionValue = schoolpevaluationquestiondetail.schoolPEvaluationQuestionDetailId 
						AND pevaluationdetail.pEvaluationMasterId =" . $pEvaluationMasterId;
		$sql .= " WHERE schoolpevaluationquestiondetail.schoolPEvaluationQuestionId=" . $questionId;

		$sql .= "  ORDER BY schoolpevaluationquestiondetail.schoolOptionValue";
		//echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetQuestionOptionsOfFloorTherapyAndICUEvaluation($questionId, $studentMasterId = 0)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
						schoolfloortherapyandicuevaldetails.questionDetailId AS OptionValue,
						schoolfloortherapyandicuevaldetails.questionId,
						schoolfloortherapyandicuevaldetails.optionText,
						schoolfloortherapyandicuevaldetails.optionValue,
						studentfloortherapyandicuevaldetails.studentMasterId,
						studentfloortherapyandicuevaldetails.schoolQuestionId,
						studentfloortherapyandicuevaldetails.schoolOptionValue AS SelectedOptionValue,
						studentfloortherapyandicuevaldetails.schoolOptionAnswerText,
						studentfloortherapyandicuevaldetails.studentDetailId AS DetailId,
						schoolfloortherapyandicuevalmaster.isPosition
						FROM
						schoolfloortherapyandicuevaldetails
						LEFT JOIN 
						schoolfloortherapyandicuevalmaster ON schoolfloortherapyandicuevalmaster.questionId = schoolfloortherapyandicuevaldetails.questionId
						LEFT JOIN
						studentfloortherapyandicuevaldetails ON studentfloortherapyandicuevaldetails.schoolQuestionId = schoolfloortherapyandicuevaldetails.questionId
						
						AND studentfloortherapyandicuevaldetails.schoolOptionValue = schoolfloortherapyandicuevaldetails.questionDetailId 
						AND studentfloortherapyandicuevaldetails.studentMasterId =" . $studentMasterId;
		$sql .= " WHERE schoolfloortherapyandicuevaldetails.questionId=" . $questionId;
        
		$sql .= "  GROUP BY schoolfloortherapyandicuevaldetails.questionDetailId
		           ORDER BY schoolfloortherapyandicuevaldetails.optionValue";
		// echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetTextAnsOfFloorTherapyAndICUEvaluation($questionId, $studentMasterId = 0)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
						studentfloortherapyandicuevaldetails.studentDetailId ,
						studentfloortherapyandicuevaldetails.schoolQuestionId,
						studentfloortherapyandicuevaldetails.schoolOptionAnswerText AS TextAnswer,
						studentfloortherapyandicuevalmaster.studentMasterId
						FROM studentfloortherapyandicuevaldetails
						INNER JOIN studentfloortherapyandicuevalmaster ON studentfloortherapyandicuevaldetails.studentMasterId=
						studentfloortherapyandicuevalmaster.studentMasterId";
		$sql .= " WHERE studentfloortherapyandicuevaldetails.schoolQuestionId=" . $questionId . " AND
						studentfloortherapyandicuevalmaster.studentMasterId=" . $studentMasterId;;
		// echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetTextAnsOfPEvaluation($questionId, $pEvaluationMasterId = 0)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
						pevaluationdetail.pEvaluationDetaild,
						pevaluationdetail.schoolPEvaluationQuestionId,
						pevaluationdetail.schoolPEvaluationOptionAnswerText AS TextAnswer,
						pevaluationmaster.pEvaluationMasterId
						FROM pevaluationdetail
						INNER JOIN pevaluationmaster ON pevaluationdetail.pEvaluationMasterId=
													pevaluationmaster.pEvaluationMasterId";
		$sql .= " WHERE pevaluationdetail.schoolPEvaluationQuestionId=" . $questionId . " AND
						pevaluationmaster.pEvaluationMasterId=" . $pEvaluationMasterId;;
		// echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetTextAnsOfCIEvaluation($questionId, $ciEvaluationMasterId = 0)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
						cievaluationdetail.ciEvaluationDetaild,
						cievaluationdetail.schoolCIEvaluationQuestionId,
						cievaluationdetail.schoolCIEvaluationOptionAnswerText AS TextAnswer,
						cievaluationmaster.ciEvaluationMasterId
						FROM cievaluationdetail
						INNER JOIN cievaluationmaster ON cievaluationdetail.ciEvaluationMasterId=
													cievaluationmaster.ciEvaluationMasterId";
		$sql .= " WHERE cievaluationdetail.schoolCIEvaluationQuestionId=" . $questionId . " AND
						cievaluationmaster.ciEvaluationMasterId=" . $ciEvaluationMasterId;;
		//echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}
	function GetQuestionOptionsOfCSEvaluation($questionId, $csEvaluationMasterId = 0)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
						schoolsiteevaluationquestiondetail.schoolCSEvaluationQuestionDetailId AS OptionValue,
						schoolsiteevaluationquestiondetail.schoolCSEvaluationQuestionId,
						schoolsiteevaluationquestiondetail.optionText,
						schoolsiteevaluationquestiondetail.schoolOptionValue,
						siteevaluationdetail.csEvaluationMasterId,
						siteevaluationdetail.schoolCSEvaluationQuestionId,
						siteevaluationdetail.schoolCSEvaluationOptionValue AS SelectedOptionValue,
						siteevaluationdetail.schoolCSEvaluationOptionAnswerText,
						siteevaluationdetail.csEvaluationDetaild AS DetailId
						FROM
						schoolsiteevaluationquestiondetail
						LEFT JOIN
						siteevaluationdetail ON siteevaluationdetail.schoolCSEvaluationQuestionId = schoolsiteevaluationquestiondetail.schoolCSEvaluationQuestionId
						AND siteevaluationdetail.schoolCSEvaluationOptionValue = schoolsiteevaluationquestiondetail.schoolCSEvaluationQuestionDetailId 
						AND siteevaluationdetail.csEvaluationMasterId =" . $csEvaluationMasterId;


		$sql .= " WHERE schoolsiteevaluationquestiondetail.schoolCSEvaluationQuestionId=" . $questionId;



		$sql .= "  ORDER BY schoolsiteevaluationquestiondetail.schoolOptionValue";
		//echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetTextAnsOfCSEvaluation($questionId, $csEvaluationMasterId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
						siteevaluationdetail.csEvaluationDetaild,
						siteevaluationdetail.schoolCSEvaluationQuestionId,
						siteevaluationdetail.schoolCSEvaluationOptionAnswerText AS TextAnswer,
						siteevaluationmaster.csEvaluationMasterId
						FROM siteevaluationdetail
						INNER JOIN siteevaluationmaster ON siteevaluationdetail.csEvaluationMasterId=
													siteevaluationmaster.csEvaluationMasterId";
		$sql .= " WHERE siteevaluationdetail.schoolCSEvaluationQuestionId=" . $questionId . " AND
						siteevaluationmaster.csEvaluationMasterId=" . $csEvaluationMasterId;;
		//echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}
	//for incident Quition Option done by tejas
	function GetAllIncidentQuestionMaster($questionId, $incidentId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
						schoolincidentquestiondetail.schoolIncidentQuestionDetailId AS OptionValue,
						schoolincidentquestiondetail.schoolIncidentQuestionId,
						schoolincidentquestiondetail.optionText,
						schoolincidentquestiondetail.schoolOptionValue,
						studentincidentdetail.studentSiteIncidentMasterId,						
						studentincidentdetail.	schoolIncidentQuestionId,					
						studentincidentdetail.	schoolIncidentOptionValue AS SelectedOptionValue,					
						studentincidentdetail.	schoolIncidentOptionAnswerText, 					
						studentincidentdetail.	studentincidentdetailId  AS DetailId	
						
						FROM schoolincidentquestiondetail
						
						LEFT JOIN studentincidentdetail ON studentincidentdetail.schoolIncidentQuestionId 
									= schoolincidentquestiondetail.schoolIncidentQuestionId
									
									AND studentincidentdetail.schoolIncidentOptionValue = 
									schoolincidentquestiondetail.schoolIncidentQuestionDetailId
									
									 AND studentincidentdetail.studentSiteIncidentMasterId = 
									 " . $incidentId;

		$sql .= " WHERE schoolincidentquestiondetail.schoolIncidentQuestionId=" . $questionId;
		$sql .= "  ORDER BY schoolincidentquestiondetail.schoolOptionValue";
		//echo $sql;	
		$rows = $objDB->GetResultset($sql);

		return $rows;
		unset($objDB);
	}
	function GetTextAnsOfIncident($questionId, $incidentId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
						studentincidentdetail.studentincidentdetailId,
						studentincidentdetail.schoolIncidentQuestionId,
						studentincidentdetail.schoolIncidentOptionAnswerText AS TextAnswer,
						studentsiteincidentmaster.studentSiteIncidentMasterId
						FROM studentincidentdetail
						INNER JOIN studentsiteincidentmaster ON studentincidentdetail.studentSiteIncidentMasterId=
													studentsiteincidentmaster.studentSiteIncidentMasterId";
		$sql .= " WHERE studentincidentdetail.schoolIncidentQuestionId=" . $questionId . " AND
						studentsiteincidentmaster.studentSiteIncidentMasterId=" . $incidentId;;
		//echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}



	function GetEquipmentQuestionOptions($questionId, $studentEquipmentMasterId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
						schoolequipmentquestiondetail.schoolEquipmentQuestionDetailId AS OptionValue,
						schoolequipmentquestiondetail.schoolEquipmentQuestionId,
						schoolequipmentquestiondetail.optionText,
						schoolequipmentquestiondetail.schoolOptionValue,
						studentequipmentdetail.studentEquipmentMasterId,						
						studentequipmentdetail.	schoolEquipmentQuestionId,					
						studentequipmentdetail.	schoolEquipmentOptionValue AS SelectedOptionValue,					
						studentequipmentdetail.	schoolEquipmentOptionAnswerText, 					
						studentequipmentdetail.	studentEquipmentDetailId  AS DetailId					
						FROM schoolequipmentquestiondetail
						LEFT JOIN studentequipmentdetail ON studentequipmentdetail.schoolEquipmentQuestionId 
									= schoolequipmentquestiondetail.schoolEquipmentQuestionId
									AND studentequipmentdetail.schoolEquipmentOptionValue = 
									schoolequipmentquestiondetail.schoolEquipmentQuestionDetailId
									 AND studentequipmentdetail.studentEquipmentMasterId = " . $studentEquipmentMasterId;

		$sql .= " WHERE schoolequipmentquestiondetail.schoolEquipmentQuestionId=" . $questionId;

		/*if($studentEquipmentMasterId > 0)
					{
						$sql .="  AND(studentequipmentdetail.studentEquipmentMasterId = " .$studentEquipmentMasterId .  " or studentequipmentdetail.studentEquipmentMasterId IS NULL)
									";
					}*/

		$sql .= "  ORDER BY schoolequipmentquestiondetail.schoolOptionValue";
		//echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}
	function GetTextAnsOfEquipment($questionId, $studentEquipmentMasterId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
						studentequipmentdetail.studentEquipmentDetailId,
						studentequipmentdetail.schoolEquipmentQuestionId,
						studentequipmentdetail.schoolEquipmentOptionAnswerText AS TextAnswer,
						studentequipmentmaster.studentEquipmentMasterId
						FROM studentequipmentdetail
						INNER JOIN studentequipmentmaster ON studentequipmentdetail.studentEquipmentMasterId=
													studentequipmentmaster.studentEquipmentMasterId";
		$sql .= " WHERE studentequipmentdetail.schoolEquipmentQuestionId=" . $questionId . " AND
						studentequipmentmaster.studentEquipmentMasterId=" . $studentEquipmentMasterId;;
		//echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetFormativeQuestionOptions($questionId, $studentFormativeMasterId = 0)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
						schoolformativequestiondetail.schoolFormativeQuestionDetailId AS OptionValue,
						schoolformativequestiondetail.schoolFormativeQuestionId,
						schoolformativequestiondetail.optionText,
						schoolformativequestiondetail.schoolOptionValue,
						studentformativedetail.studentFormativeMasterId,						
						studentformativedetail.schoolFormativeQuestionId,					
						studentformativedetail.	schoolFormativeOptionValue AS SelectedOptionValue,					
						studentformativedetail.	schoolFormativeOptionAnswerText, 					
						studentformativedetail.	studentFormativeDetailId  AS DetailId					
						FROM schoolformativequestiondetail
						LEFT JOIN studentformativedetail ON studentformativedetail.schoolFormativeQuestionId 
									= schoolformativequestiondetail.schoolFormativeQuestionId
									AND studentformativedetail.schoolFormativeOptionValue = 
									schoolformativequestiondetail.schoolFormativeQuestionDetailId 
									AND studentformativedetail.studentFormativeMasterId = " . $studentFormativeMasterId;

		$sql .= " WHERE schoolformativequestiondetail.schoolFormativeQuestionId=" . $questionId;

		$sql .= "  ORDER BY schoolformativequestiondetail.schoolOptionValue";
		//echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetPEFQuestionOptions($questionId, $studentPEFMasterId = 0)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
						schoolpefevaldetails.pefQuestionDetailId AS OptionValue,
						schoolpefevaldetails.pefQuestionId,
						schoolpefevaldetails.optionText,
						schoolpefevaldetails.pefOptionValue,
						studentpefevaldetails.studentPEFMasterId,						
						studentpefevaldetails.	schoolQuestionId,					
						studentpefevaldetails.	schoolOptionvalue AS SelectedOptionValue,					
						studentpefevaldetails.	schoolOptionAnswerText, 					
						studentpefevaldetails.	studentPEFDetailId  AS DetailId,
					    schoolpefevalmaster.isPosition		
						FROM schoolpefevaldetails
						LEFT JOIN 
						schoolpefevalmaster ON schoolpefevalmaster.pefQuestionId = schoolpefevaldetails.pefQuestionId
						LEFT JOIN studentpefevaldetails ON studentpefevaldetails.schoolQuestionId 
									= schoolpefevaldetails.pefQuestionId
									AND studentpefevaldetails.schoolOptionvalue = 
									schoolpefevaldetails.pefQuestionDetailId 
									AND studentpefevaldetails.studentPEFMasterId = " . $studentPEFMasterId;
					    
		$sql .= " WHERE schoolpefevaldetails.pefQuestionId=" . $questionId;
		$sql .= " ORDER BY schoolpefevaldetails.pefOptionValue";

		// echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetTextAnsOfFormativeEval($questionId, $studentFormativeMasterId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
						studentformativedetail.studentFormativeDetailId,
						studentformativedetail.schoolFormativeQuestionId,
						studentformativedetail.schoolFormativeOptionAnswerText AS TextAnswer,
						studentformativemaster.studentFormativeMasterId
						FROM studentformativedetail
						INNER JOIN studentformativemaster ON studentformativedetail.studentFormativeMasterId=
													studentformativemaster.studentFormativeMasterId";
		$sql .= " WHERE studentformativedetail.schoolFormativeQuestionId=" . $questionId . " AND
						studentformativemaster.studentFormativeMasterId=" . $studentFormativeMasterId;
		// echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetTextAnsOfPEFEval($questionId, $studentPEFMasterId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
						studentpefevaldetails.studentPEFDetailId,
						studentpefevaldetails.schoolQuestionId,
						studentpefevaldetails.schoolOptionAnswerText AS TextAnswer,
						studentpefmaster.studentPEFMasterId
						FROM studentpefevaldetails
						INNER JOIN studentpefmaster ON studentpefevaldetails.studentPEFMasterId=
						studentpefmaster.studentPEFMasterId";
		$sql .= " WHERE studentpefevaldetails.schoolQuestionId=" . $questionId . " AND
						studentpefmaster.studentPEFMasterId=" . $studentPEFMasterId;
		//echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	//midterm quetion options
	function GetMidtermQuestionOptions($questionId, $studentMidtermMasterId = 0)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
						schoolmidtermevaldetails.midtermQuestionDetailId AS OptionValue,
						schoolmidtermevaldetails.midtermQuestionId,
						schoolmidtermevaldetails.optionText,
						schoolmidtermevaldetails.midtermOptionValue AS schoolOptionValue,
						studentmidtermevaldetails.studentMidtermMasterId,						
						studentmidtermevaldetails.studentQuestionId,					
						studentmidtermevaldetails.studentoptionvalue AS SelectedOptionValue,					
						studentmidtermevaldetails.studentOptionAnswerText, 					
						studentmidtermevaldetails.studentMidtermDetailId  AS DetailId					
						FROM schoolmidtermevaldetails
						LEFT JOIN studentmidtermevaldetails ON studentmidtermevaldetails.studentQuestionId 
									= schoolmidtermevaldetails.midtermQuestionId
									AND studentmidtermevaldetails.studentoptionvalue = 
									schoolmidtermevaldetails.midtermQuestionDetailId 
									AND studentmidtermevaldetails.studentMidtermMasterId = " . $studentMidtermMasterId;

		$sql .= " WHERE schoolmidtermevaldetails.midtermQuestionId=" . $questionId;

		$sql .= "  ORDER BY schoolmidtermevaldetails.midtermOptionValue";
		//echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	//Daily quetion options
	function GetDailyQuestionOptions($questionId, $studentDailyMasterId = 0)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
						schooldailyevaldetails.dailyQuestionDetailId AS OptionValue,
						schooldailyevaldetails.dailyQuestionId,
						schooldailyevaldetails.optionText,
						schooldailyevaldetails.dailyOptionValue AS schoolOptionValue,
						studentdailyevaldetails.studentDailyMasterId,						
						studentdailyevaldetails.studentQuestionId,					
						studentdailyevaldetails.studentoptionvalue AS SelectedOptionValue,					
						studentdailyevaldetails.studentOptionAnswerText, 					
						studentdailyevaldetails.studentDailyDetailId  AS DetailId					
						FROM schooldailyevaldetails
						LEFT JOIN studentdailyevaldetails ON studentdailyevaldetails.studentQuestionId 
									= schooldailyevaldetails.dailyQuestionId
									AND studentdailyevaldetails.studentoptionvalue = 
									schooldailyevaldetails.dailyQuestionDetailId 
									AND studentdailyevaldetails.studentDailyMasterId = " . $studentDailyMasterId;

		$sql .= " WHERE schooldailyevaldetails.dailyQuestionId=" . $questionId;

		$sql .= "  ORDER BY schooldailyevaldetails.optionText";
		// echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetTextAnsOfMidtermEval($questionId, $studentMidtermMasterId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
						studentmidtermevaldetails.studentMidtermMasterId,
						studentmidtermevaldetails.studentQuestionId,
						studentmidtermevaldetails.studentOptionAnswerText AS TextAnswer,
						studentmidtermmaster.studentMidtermMasterId
						FROM studentmidtermevaldetails
						INNER JOIN studentmidtermmaster ON studentmidtermevaldetails.studentMidtermMasterId=
													studentmidtermmaster.studentMidtermMasterId";
		$sql .= " WHERE studentmidtermevaldetails.studentQuestionId=" . $questionId . " AND
						studentmidtermmaster.studentMidtermMasterId=" . $studentMidtermMasterId;
		//echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetTextAnsOfDailyEval($questionId, $studentDailyMasterId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
						studentdailyevaldetails.studentDailyMasterId,
						studentdailyevaldetails.studentQuestionId,
						studentdailyevaldetails.studentOptionAnswerText AS TextAnswer,
						studentdailymaster.studentDailyMasterId
						FROM studentdailyevaldetails
						INNER JOIN studentdailymaster ON studentdailyevaldetails.studentDailyMasterId=
						studentdailymaster.studentDailyMasterId";
		$sql .= " WHERE studentdailyevaldetails.studentQuestionId=" . $questionId . " AND
						studentdailymaster.studentDailyMasterId=" . $studentDailyMasterId;
		// echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	//get chekoff option 
	function GetAllQuestiondetail($questionId = 0, $checkoffId = 0, $currentSchoolId = 0, $isActiveCheckoff)
	{
		// 	echo $questionId."=>".$checkoffId."=>".$currentSchoolId."=>".$isActiveCheckoff;
		$objDB = new clsDB();
		$rows = "";

		$sql = "SELECT 
						schooldefaultquestiondetail.schoolQuestionDId AS OptionValue,						
						schooldefaultquestiondetail.schoolOptionValue,						
						schooldefaultquestiondetail.schoolOptionText AS optionText,						
						checkoffdetail.schoolOptionValue AS SelectedOptionValue,						
						checkoffdetail.checkoffDId AS DetailId,		
						checkoffdetail.checkoffId AS checkoffId,	
						checkoffdetail.comments AS questionComment	
						
						FROM
						schooldefaultquestiondetail
						INNER JOIN schooldefaultquestionmaster ON schooldefaultquestiondetail.schoolQuestionId=
																	schooldefaultquestionmaster.schoolQuestionId
																	
						LEFT JOIN checkoffdetail ON checkoffdetail.schoolQuestionDId = 
																	schooldefaultquestiondetail.schoolQuestionId
						AND checkoffdetail.schoolOptionValue = schooldefaultquestiondetail.schoolQuestionDId 
						AND checkoffdetail.checkoffId =" . $checkoffId;

		$sql .= " WHERE schooldefaultquestiondetail.schoolQuestionId=" . $questionId .
			" AND schooldefaultquestionmaster.schoolId=" . $currentSchoolId;
		$sql .= "  group BY schooldefaultquestiondetail.schoolOptionText";
		if ($isActiveCheckoff == 0) {
			$sql .= "	ORDER BY schooldefaultquestiondetail.schoolOptionValue ASC";
		} else {
			$sql .= "	ORDER BY schooldefaultquestiondetail.schoolOptionText ASC";
		}
		// echo $sql;
		// exit;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	//get chekoff option 
	function GetAllQuestiondetailUSAF($questionId = 0, $checkoffId = 0, $currentSchoolId = 0, $isActiveCheckoff)
	{
		// 	echo $questionId."=>".$checkoffId."=>".$currentSchoolId."=>".$isActiveCheckoff;
		$objDB = new clsDB();
		$rows = "";

		$sql = "SELECT 
						schooldefaultquestiondetail.schoolQuestionDId AS OptionValue,						
						schooldefaultquestiondetail.schoolOptionValue,						
						schooldefaultquestiondetail.schoolOptionText AS optionText,						
						checkoffdetail.schoolOptionValue AS SelectedOptionValue,						
						checkoffdetail.checkoffDId AS DetailId,		
						checkoffdetail.checkoffId AS checkoffId		
						
						FROM
						schooldefaultquestiondetail
						INNER JOIN schooldefaultquestionmaster ON schooldefaultquestiondetail.schoolQuestionId=
																	schooldefaultquestionmaster.schoolQuestionId
																	
						LEFT JOIN checkoffdetail ON checkoffdetail.schoolQuestionDId = 
																	schooldefaultquestiondetail.schoolQuestionId
						AND checkoffdetail.schoolOptionValue = schooldefaultquestiondetail.schoolQuestionDId 
						AND checkoffdetail.checkoffId =" . $checkoffId;

		$sql .= " WHERE schooldefaultquestiondetail.schoolQuestionId=" . $questionId;
		$sql .= "  group BY schooldefaultquestiondetail.schoolOptionText";
		if ($isActiveCheckoff == 0) {
			$sql .= "	ORDER BY schooldefaultquestiondetail.schoolOptionValue ASC";
		} else {
			$sql .= "	ORDER BY schooldefaultquestiondetail.schoolOptionText ASC";
		}
		// echo $sql;
		// exit;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetTextAnsOfCheckoff($questionId = 0, $checkoffId = 0, $currentSchoolId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
						checkoffdetail.checkoffDId,
						checkoffdetail.schoolQuestionDId,
						checkoffdetail.schoolOptionAnswerText AS TextAnswer,
						checkoff.checkoffId
						FROM checkoffdetail
						INNER JOIN checkoff ON checkoffdetail.checkoffId=
													checkoff.checkoffId";
		$sql .= " WHERE checkoffdetail.schoolQuestionDId=" . $questionId . " AND
						 checkoff.checkoffId=" . $checkoffId .
			" AND checkoff.schoolId=" . $currentSchoolId;;
		// echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}
	function GetsingleQuestiondetail($questionId)
	{
		$objDB = new clsDB();
		$rows = "";

		$sql = "SELECT 
						schooldefaultquestiondetail.schoolQuestionDId AS OptionValue,
						0 AS DetailId,						
						schooldefaultquestiondetail.schoolIsCorrectAnswer,
						schooldefaultquestiondetail.schoolOptionText AS optionText,
						schooldefaultquestiondetail.schoolOptionValue,						
						schooldefaultquestiondetail.schoolOptionValue AS SelectedOptionValue,						
						schooldefaultquestionmaster.schoolQuestionId,
						schooldefaultquestionmaster.sortOrder
						FROM
						schooldefaultquestiondetail
						LEFT JOIN schooldefaultquestionmaster ON schooldefaultquestiondetail.schoolQuestionId=
																	schooldefaultquestionmaster.schoolQuestionId
							AND schooldefaultquestiondetail.schoolOptionValue=1";
		$sql .= " WHERE schooldefaultquestiondetail.schoolQuestionId=" . $questionId;
		$sql .= "  group BY schooldefaultquestiondetail.schoolOptionText";
		$sql .= "	ORDER BY schooldefaultquestiondetail.schoolOptionText";

		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetTextAnsOfsingleQuestion($questionId = 0)
	{
		$objDB = new clsDB();
		$rows = "";

		$sql = "SELECT 
						schooldefaultquestiondetail.schoolQuestionDId AS OptionValue,
						0 AS DetailId,						
						schooldefaultquestiondetail.schoolIsCorrectAnswer,
						schooldefaultquestiondetail.schoolOptionText AS optionText,
						schooldefaultquestiondetail.schoolOptionValue,						
						schooldefaultquestiondetail.schoolOptionValue AS SelectedOptionValue,						
						schooldefaultquestionmaster.schoolQuestionId,
						schooldefaultquestionmaster.sortOrder
						FROM
						schooldefaultquestiondetail
						LEFT JOIN schooldefaultquestionmaster ON schooldefaultquestiondetail.schoolQuestionId=
																	schooldefaultquestionmaster.schoolQuestionId
							AND schooldefaultquestiondetail.schoolOptionValue=1";
		$sql .= " WHERE schooldefaultquestiondetail.schoolQuestionId=" . $questionId;
		$sql .= "  group BY schooldefaultquestiondetail.schoolOptionText";
		$sql .= "	ORDER BY schooldefaultquestiondetail.schoolOptionText";

		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}
	function GetQuestionOptionsOfCoarc($questionId, $studentCoarcMasterId = 0)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
						schoolcoarcquestiondetail.schoolCoarcQuestionDetailId AS OptionValue,
						schoolcoarcquestiondetail.schoolCoarcQuestionId,
						schoolcoarcquestiondetail.optionText,
						schoolcoarcquestiondetail.schoolOptionValue,
						studentsitecoarcdetail.studentCoarcMasterId,
						studentsitecoarcdetail.schoolCoarcQuestionId,
						studentsitecoarcdetail.schoolCoarcOptionValue AS SelectedOptionValue,
						studentsitecoarcdetail.schoolCoarcOptionAnswerText,
						studentsitecoarcdetail.studentCoarcDetailId AS DetailId
						FROM
						schoolcoarcquestiondetail
						LEFT JOIN
						studentsitecoarcdetail ON studentsitecoarcdetail.schoolCoarcQuestionId = schoolcoarcquestiondetail.schoolCoarcQuestionId
						AND studentsitecoarcdetail.schoolCoarcOptionValue = schoolcoarcquestiondetail.schoolCoarcQuestionDetailId 
						AND studentsitecoarcdetail.studentCoarcMasterId =" . $studentCoarcMasterId;
		$sql .= " WHERE schoolcoarcquestiondetail.schoolCoarcQuestionId=" . $questionId;
		$sql .= "  ORDER BY schoolcoarcquestiondetail.schoolOptionValue";
		// echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}
	function GetTextAnsOfStudentCoarc($questionId, $studentCoarcMasterId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
						studentsitecoarcdetail.studentCoarcDetailId,
						studentsitecoarcdetail.schoolCoarcQuestionId,
						studentsitecoarcdetail.schoolCoarcOptionAnswerText AS TextAnswer,
						studentsitecoarcmaster.studentCoarcMasterId
						FROM studentsitecoarcdetail
						INNER JOIN studentsitecoarcmaster ON studentsitecoarcdetail.studentCoarcMasterId=
													studentsitecoarcmaster.studentCoarcMasterId";
		$sql .= " WHERE studentsitecoarcdetail.schoolCoarcQuestionId=" . $questionId . " AND
						studentsitecoarcmaster.studentCoarcMasterId=" . $studentCoarcMasterId;
		//echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}
	function GetQuestionOptionsOfGraduateCoarc($questionId, $graduateCoarcMasterId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
						schoolgraduatecoarcquestiondetail.schoolGraduateCoarcQuestionDetailId AS OptionValue,
						schoolgraduatecoarcquestiondetail.schoolGraduateCoarcQuestionId,
						schoolgraduatecoarcquestiondetail.optionText,
						schoolgraduatecoarcquestiondetail.schoolOptionValue,
						
						graduatecoarcdetail.graduateCoarcMasterId,						
						graduatecoarcdetail.schoolCoarcQuestionId,					
						graduatecoarcdetail.schoolCoarcOptionValue AS SelectedOptionValue,					
						graduatecoarcdetail.schoolCoarcOptionAnswerText, 					
						graduatecoarcdetail.graduateCoarcDetailId  AS DetailId					
						FROM schoolgraduatecoarcquestiondetail
						LEFT JOIN graduatecoarcdetail ON graduatecoarcdetail.schoolCoarcQuestionId 
									= schoolgraduatecoarcquestiondetail.schoolGraduateCoarcQuestionId
									AND graduatecoarcdetail.schoolCoarcOptionValue = 
									schoolgraduatecoarcquestiondetail.schoolGraduateCoarcQuestionDetailId 
									AND graduatecoarcdetail.graduateCoarcMasterId = " . $graduateCoarcMasterId;

		$sql .= " WHERE schoolgraduatecoarcquestiondetail.schoolGraduateCoarcQuestionId=" . $questionId;
		$sql .= "  ORDER BY schoolgraduatecoarcquestiondetail.schoolOptionValue";
		//echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetTextAnsOfGraduateCoarc($questionId, $graduateCoarcMasterId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
						graduatecoarcdetail.graduateCoarcDetailId,
						graduatecoarcdetail.schoolCoarcQuestionId,
						graduatecoarcdetail.schoolCoarcOptionAnswerText AS TextAnswer,
						graduatecoarcmaster.graduateCoarcMasterId
						FROM graduatecoarcdetail
						INNER JOIN graduatecoarcmaster ON graduatecoarcdetail.graduateCoarcMasterId=
													graduatecoarcmaster.graduateCoarcMasterId";
		$sql .= " WHERE graduatecoarcdetail.schoolCoarcQuestionId=" . $questionId . " AND
						graduatecoarcmaster.graduateCoarcMasterId=" . $graduateCoarcMasterId;
		//echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetQuestionOptionsOfEmployerCoarc($questionId, $employerCoarcMasterId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
						schoolemployercoarcquestiondetail.schoolEmployerCoarcQuestionDetailId AS OptionValue,
						schoolemployercoarcquestiondetail.schoolEmployerCoarcQuestionId,
						schoolemployercoarcquestiondetail.optionText,
						schoolemployercoarcquestiondetail.schoolOptionValue,
						employercoarcdetail.employerCoarcMasterId,
						employercoarcdetail.schoolCoarcQuestionId,
						employercoarcdetail.schoolCoarcOptionValue AS SelectedOptionValue,
						employercoarcdetail.schoolCoarcOptionAnswerText,
						employercoarcdetail.employerCoarcDetailId AS DetailId
						FROM
						schoolemployercoarcquestiondetail
						LEFT JOIN
						employercoarcdetail ON employercoarcdetail.schoolCoarcQuestionId = schoolemployercoarcquestiondetail.schoolEmployerCoarcQuestionId
						AND employercoarcdetail.schoolCoarcOptionValue = schoolemployercoarcquestiondetail.schoolEmployerCoarcQuestionDetailId 
						AND employercoarcdetail.employerCoarcMasterId =" . $employerCoarcMasterId;


		$sql .= " WHERE schoolemployercoarcquestiondetail.schoolEmployerCoarcQuestionId=" . $questionId;



		$sql .= "  ORDER BY schoolemployercoarcquestiondetail.schoolOptionValue";
		//echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}
	function GetTextAnsOfEmployerCoarc($questionId, $employerCoarcMasterId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
						employercoarcdetail.employerCoarcDetailId,
						employercoarcdetail.schoolCoarcQuestionId,
						employercoarcdetail.schoolCoarcOptionAnswerText AS TextAnswer,
						employercoarcmaster.employerCoarcMasterId
						FROM employercoarcdetail
						INNER JOIN employercoarcmaster ON employercoarcdetail.employerCoarcMasterId=
													employercoarcmaster.employerCoarcMasterId";
		$sql .= " WHERE employercoarcdetail.schoolCoarcQuestionId=" . $questionId . " AND
						employercoarcmaster.employerCoarcMasterId=" . $employerCoarcMasterId;
		//echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}
	function GetQuestionOptionsOfPersonnelCoarc($questionId, $personnelCoarcMasterId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
						schoolpersonnelcoarcquestiondetail.schoolPersonnelCoarcQuestionDetailId AS OptionValue,
						schoolpersonnelcoarcquestiondetail.schoolPersonnelCoarcQuestionId,
						schoolpersonnelcoarcquestiondetail.optionText,
						schoolpersonnelcoarcquestiondetail.schoolOptionValue,
						personnelcoarcdetail.personnelCoarcMasterId,
						personnelcoarcdetail.schoolCoarcQuestionId,
						personnelcoarcdetail.schoolCoarcOptionValue AS SelectedOptionValue,
						personnelcoarcdetail.schoolCoarcOptionAnswerText,
						personnelcoarcdetail.personnelCoarcDetailId AS DetailId
						FROM
						schoolpersonnelcoarcquestiondetail
						LEFT JOIN
						personnelcoarcdetail ON personnelcoarcdetail.schoolCoarcQuestionId = schoolpersonnelcoarcquestiondetail.schoolPersonnelCoarcQuestionId
						AND personnelcoarcdetail.schoolCoarcOptionValue = schoolpersonnelcoarcquestiondetail.schoolPersonnelCoarcQuestionDetailId 
						AND personnelcoarcdetail.personnelCoarcMasterId =" . $personnelCoarcMasterId;


		$sql .= " WHERE schoolpersonnelcoarcquestiondetail.schoolPersonnelCoarcQuestionId=" . $questionId;



		$sql .= "  ORDER BY schoolpersonnelcoarcquestiondetail.schoolOptionValue";
		// echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetTextAnsOfPersonnelCoarc($questionId, $personnelCoarcMasterId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
						personnelcoarcdetail.personnelCoarcDetailId,
						personnelcoarcdetail.schoolCoarcQuestionId,
						personnelcoarcdetail.schoolCoarcOptionAnswerText AS TextAnswer,
						personnelcoarcmaster.personnelCoarcMasterId
						FROM personnelcoarcdetail
						INNER JOIN personnelcoarcmaster ON personnelcoarcdetail.personnelCoarcMasterId=
													personnelcoarcmaster.personnelCoarcMasterId";
		$sql .= " WHERE personnelcoarcdetail.schoolCoarcQuestionId=" . $questionId . " AND
						personnelcoarcmaster.personnelCoarcMasterId=" . $personnelCoarcMasterId;
		//echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}
	function GetMastersingleQuestiondetail($questionId)
	{
		$objDB = new clsDB();
		$rows = "";

		$sql = "SELECT 
						defaultquestiondetail.questionDId ,
						0 AS DetailId,
						0 AS OptionValue,
						defaultquestiondetail.isCorrectAnswer,
						defaultquestiondetail.optionText AS optionText,
						defaultquestiondetail.optionValue AS schoolOptionValue,						
						defaultquestiondetail.optionValue AS SelectedOptionValue,						
						defaultquestionmaster.questionId,
						defaultquestionmaster.sortOrder
						FROM
						defaultquestiondetail
						LEFT JOIN defaultquestionmaster ON defaultquestiondetail.questionId=
																	defaultquestionmaster.questionId
							AND defaultquestiondetail.optionValue=1";
		$sql .= " WHERE defaultquestiondetail.questionId=" . $questionId;
		$sql .= "  group BY defaultquestiondetail.optionText";
		$sql .= "	ORDER BY defaultquestiondetail.optionText";

		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetUsafMastersingleQuestiondetail($questionId)
	{
		$objDB = new clsDB();
		$rows = "";

		$sql = "SELECT 
					  defaultusafquestiondetail.questionDId ,
					  0 AS DetailId,
					  0 AS OptionValue,
					  defaultusafquestiondetail.isCorrectAnswer,
					  defaultusafquestiondetail.optionText AS optionText,
					  defaultusafquestiondetail.optionValue AS schoolOptionValue,						
					  defaultusafquestiondetail.optionValue AS SelectedOptionValue,						
					  defaultusafquestionmaster.questionId,
					  defaultusafquestionmaster.sortOrder
					  FROM
					  defaultusafquestiondetail
					  LEFT JOIN defaultusafquestionmaster ON defaultusafquestiondetail.questionId=
					  defaultusafquestionmaster.questionId
						  AND defaultusafquestiondetail.optionValue=1";
		$sql .= " WHERE defaultusafquestiondetail.questionId=" . $questionId;
		$sql .= "  group BY defaultusafquestiondetail.optionText";
		$sql .= "	ORDER BY defaultusafquestiondetail.optionText";

		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetTextAnsOfMastersingleQuestiondetail($questionId)
	{
		$objDB = new clsDB();
		$rows = "";

		$sql = "SELECT 
						defaultquestiondetail.questionDId ,
						0 AS DetailId,						
						defaultquestiondetail.isCorrectAnswer,
						defaultquestiondetail.optionText AS optionText,
						defaultquestiondetail.optionValue AS schoolOptionValue,						
						defaultquestiondetail.optionValue AS SelectedOptionValue,						
						defaultquestionmaster.questionId,
						defaultquestionmaster.sortOrder
						FROM
						defaultquestiondetail
						LEFT JOIN defaultquestionmaster ON defaultquestiondetail.questionId=
																	defaultquestionmaster.questionId
							AND defaultquestiondetail.optionValue=1";
		$sql .= " WHERE defaultquestiondetail.questionId=" . $questionId;
		$sql .= "  group BY defaultquestiondetail.optionText";
		$sql .= "	ORDER BY defaultquestiondetail.optionText";

		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}
	function GetTextAnsOfUsafMastersingleQuestiondetail($questionId)
	{
		$objDB = new clsDB();
		$rows = "";

		$sql = "SELECT 
					  defaultusafquestiondetail.questionDId ,
					  0 AS DetailId,						
					  defaultusafquestiondetail.isCorrectAnswer,
					  defaultusafquestiondetail.optionText AS optionText,
					  defaultusafquestiondetail.optionValue AS schoolOptionValue,						
					  defaultusafquestiondetail.optionValue AS SelectedOptionValue,						
					  defaultusafquestionmaster.questionId,
					  defaultusafquestionmaster.sortOrder
					  FROM
					  defaultusafquestiondetail
					  LEFT JOIN defaultusafquestionmaster ON defaultusafquestiondetail.questionId=
					  defaultusafquestionmaster.questionId
						  AND defaultusafquestiondetail.optionValue=1";
		$sql .= " WHERE defaultusafquestiondetail.questionId=" . $questionId;
		$sql .= "  group BY defaultusafquestiondetail.optionText";
		$sql .= "	ORDER BY defaultusafquestiondetail.optionText";

		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	//get IRR option 
	function GetIrrQuestiondetail($questionId = 0, $irrMasterId = 0, $currentSchoolId = 0, $irrDetailId = 0)
	{
		$objDB = new clsDB();
		$rows = "";

		$sql = "SELECT 
						schooldefaultquestiondetail.schoolQuestionDId AS OptionValue,						
						schooldefaultquestiondetail.schoolOptionValue,						
						schooldefaultquestiondetail.schoolOptionText AS optionText,						
						clinicianirrdetails.schoolOptionValue AS SelectedOptionValue,						
						clinicianirrdetails.clinicianIrrDetailId AS DetailId,		
						clinicianirrdetails.irrMasterId AS irrMasterId,		
						clinicianirrdetails.irrDetailId AS irrDetailId		
						
						FROM
						schooldefaultquestiondetail
						LEFT JOIN schooldefaultquestionmaster ON schooldefaultquestiondetail.schoolQuestionId=
																	schooldefaultquestionmaster.schoolQuestionId
						 AND schooldefaultquestiondetail.schoolOptionText !='None'
																	
						LEFT JOIN clinicianirrdetails ON clinicianirrdetails.schoolQuestionDId = 
																	schooldefaultquestiondetail.schoolQuestionId
						AND clinicianirrdetails.schoolOptionValue = schooldefaultquestiondetail.schoolQuestionDId 
						AND clinicianirrdetails.irrMasterId =" . $irrMasterId . " AND clinicianirrdetails.irrDetailId="
			. $irrDetailId;

		$sql .= " WHERE schooldefaultquestiondetail.schoolQuestionId=" . $questionId .
			" AND schooldefaultquestionmaster.schoolId=" . $currentSchoolId;
		$sql .= "  group BY schooldefaultquestiondetail.schoolOptionText";
		$sql .= "	ORDER BY schooldefaultquestiondetail.schoolOptionText ASC";
		//echo $sql.'<hr>';
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}
	function GetTextAnsOfIrr($questionId = 0, $irrMasterId = 0, $currentSchoolId = 0, $irrDetailId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
						clinicianirrdetails.clinicianIrrDetailId,
						clinicianirrdetails.schoolQuestionDId,
						clinicianirrdetails.schoolOptionAnswerText AS TextAnswer,
						irrmaster.irrMasterId
						FROM clinicianirrdetails
						INNER JOIN irrmaster ON clinicianirrdetails.irrMasterId=
													irrmaster.irrMasterId";
		$sql .= " WHERE clinicianirrdetails.schoolQuestionDId=" . $questionId . " AND
						 irrmaster.irrMasterId=" . $irrMasterId . " AND clinicianirrdetails.irrDetailId=" . $irrDetailId .
			" AND irrmaster.schoolId=" . $currentSchoolId;;
		//echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	//Mastery quetion options
	function GetMasteryQuestionOptions($questionId, $studentMasteryEvalId = 0)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
			schoolmasteryevaldetails.masteryQuestionDetailId AS OptionValue,
			schoolmasteryevaldetails.masteryQuestionId,
			schoolmasteryevaldetails.optionText,
			schoolmasteryevaldetails.description AS schoolOptionValue,
			studentmasteryevaldetails.studentMasteryEvalId,						
			studentmasteryevaldetails.studentQuestionId,					
			studentmasteryevaldetails.studentoptionvalue AS SelectedOptionValue,					
			studentmasteryevaldetails.studentOptionAnswerText, 					
			studentmasteryevaldetails.studentMasteryDetailId  AS DetailId					
			FROM schoolmasteryevaldetails
			LEFT JOIN studentmasteryevaldetails ON studentmasteryevaldetails.studentQuestionId 
						= schoolmasteryevaldetails.masteryQuestionId
						AND studentmasteryevaldetails.studentoptionvalue = 
						schoolmasteryevaldetails.masteryQuestionDetailId 
						AND studentmasteryevaldetails.studentMasteryEvalId = " . $studentMasteryEvalId;

		$sql .= " WHERE schoolmasteryevaldetails.masteryQuestionId=" . $questionId;

		$sql .= "  Group by schoolmasteryevaldetails.optionText ORDER BY `schoolmasteryevaldetails`.`masteryQuestionId` ASC";
		// echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetTextAnsOfMasteryEval($questionId, $studentMasteryEvalId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
						studentmasteryevaldetails.studentMasteryEvalId,
						studentmasteryevaldetails.studentQuestionId,
						studentmasteryevaldetails.studentOptionAnswerText AS TextAnswer,
						studentmasteryeval.studentMasteryEvalId
						FROM studentmasteryevaldetails
						INNER JOIN studentmasteryeval ON studentmasteryevaldetails.studentMasteryEvalId=
						studentmasteryeval.studentMasteryEvalId";
		$sql .= " WHERE studentmasteryevaldetails.studentQuestionId=" . $questionId . " AND
						studentmasteryeval.studentMasteryEvalId=" . $studentMasteryEvalId;
		// echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetQuestionOptionsTypewisePersonnelCoarc($questionId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT  * FROM defaultpersonnelcoarcquestiondetail  WHERE personnelCoarcQuestionId=" . $questionId;
		// echo $sql;exit;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetPersonnelCoarcSchoolQuestionOptionsByQuestionId($questionId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT  * FROM schoolpersonnelcoarcquestiondetail  WHERE schoolPersonnelCoarcQuestionId=" . $questionId;
		// echo $sql;exit;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	//Daily quetion options for app
	function GetStudentDailyQuestionDetails($questionId, $studentDailyMasterId = 0)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
				schooldailyevaldetails.dailyQuestionDetailId AS OptionValue,
				schooldailyevaldetails.dailyQuestionId,
				schooldailyevaldetails.optionText,
				schooldailyevaldetails.dailyOptionValue AS schoolOptionValue,
				studentdailyevaldetails.studentDailyMasterId,						
				studentdailyevaldetails.studentQuestionId,					
				studentdailyevaldetails.studentoptionvalue AS SelectedOptionValue,					
				studentdailyevaldetails.studentOptionAnswerText, 					
				studentdailyevaldetails.studentDailyDetailId  AS DetailId					
				FROM schooldailyevaldetails
				LEFT JOIN studentdailyevaldetails ON studentdailyevaldetails.studentQuestionId 
							= schooldailyevaldetails.dailyQuestionId
							AND studentdailyevaldetails.studentoptionvalue = 
							schooldailyevaldetails.dailyQuestionDetailId 
							AND studentdailyevaldetails.studentDailyMasterId = " . $studentDailyMasterId;

		$sql .= " WHERE schooldailyevaldetails.dailyQuestionId=" . $questionId;

		$sql .= "  ORDER BY schooldailyevaldetails.optionText";
		// echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	// for Admin CI
	function GetQuestionOptionsOfAdminCIEvaluation($questionId, $ciEvaluationMasterId = 0)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
						schooladmincievaluationquestiondetail.schoolCIEvaluationQuestionDetailId AS OptionValue,
						schooladmincievaluationquestiondetail.schoolCIEvaluationQuestionId,
						schooladmincievaluationquestiondetail.optionText,
						schooladmincievaluationquestiondetail.schoolOptionValue,
						admincievaluationdetail.ciEvaluationMasterId,
						admincievaluationdetail.schoolCIEvaluationQuestionId,
						admincievaluationdetail.schoolCIEvaluationOptionValue AS SelectedOptionValue,
						admincievaluationdetail.schoolCIEvaluationOptionAnswerText,
						admincievaluationdetail.ciEvaluationDetaild AS DetailId
						FROM
						schooladmincievaluationquestiondetail
						LEFT JOIN
						admincievaluationdetail ON admincievaluationdetail.schoolCIEvaluationQuestionId = schooladmincievaluationquestiondetail.schoolCIEvaluationQuestionId
						AND admincievaluationdetail.schoolCIEvaluationOptionValue = schooladmincievaluationquestiondetail.schoolCIEvaluationQuestionDetailId 
						AND admincievaluationdetail.ciEvaluationMasterId =" . $ciEvaluationMasterId;
		$sql .= " WHERE schooladmincievaluationquestiondetail.schoolCIEvaluationQuestionId=" . $questionId;

		$sql .= "  ORDER BY schooladmincievaluationquestiondetail.schoolOptionValue";
		// echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetTextAnsOfAdminCIEvaluation($questionId, $ciEvaluationMasterId = 0)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
						admincievaluationdetail.ciEvaluationDetaild,
						admincievaluationdetail.schoolCIEvaluationQuestionId,
						admincievaluationdetail.schoolCIEvaluationOptionAnswerText AS TextAnswer,
						admincievaluationmaster.ciEvaluationMasterId
						FROM admincievaluationdetail
						INNER JOIN admincievaluationmaster ON admincievaluationdetail.ciEvaluationMasterId=
													admincievaluationmaster.ciEvaluationMasterId";
		$sql .= " WHERE admincievaluationdetail.schoolCIEvaluationQuestionId=" . $questionId . " AND
						admincievaluationmaster.ciEvaluationMasterId=" . $ciEvaluationMasterId;
		//echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}
}
