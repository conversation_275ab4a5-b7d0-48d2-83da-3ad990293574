<?php
/**
 * AccountTest
 *
 * PHP version 5
 *
 * @category Class
 * @package  ClickSend
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * ClickSend v3 API
 *
 * This is an official SDK for [ClickSend](https://clicksend.com)  Below you will find a current list of the available methods for clicksend.  *NOTE: You will need to create a free account to use the API. You can register [here](https://dashboard.clicksend.com/#/signup/step1/)..*
 *
 * OpenAPI spec version: 3.1
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * ClickSend Codegen version: 3.0.51
 */
/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Please update the test case below to test the model.
 */

namespace ClickSend;

use PHPUnit\Framework\TestCase;

/**
 * AccountTest Class Doc Comment
 *
 * @category    Class
 * @description Complete account details needed for the user.
 * @package     ClickSend
 * <AUTHOR> Codegen team
 * @link        https://github.com/swagger-api/swagger-codegen
 */
class AccountTest extends TestCase
{

    /**
     * Setup before running any test case
     */
    public static function setUpBeforeClass(): void
    {
    }

    /**
     * Setup before running each test case
     */
    public function setUp(): void
    {
    }

    /**
     * Clean up after running each test case
     */
    public function tearDown(): void
    {
    }

    /**
     * Clean up after running all test cases
     */
    public static function tearDownAfterClass(): void
    {
    }

    /**
     * Test "Account"
     */
    public function testAccount()
    {
    }

    /**
     * Test attribute "username"
     */
    public function testPropertyUsername()
    {
    }

    /**
     * Test attribute "password"
     */
    public function testPropertyPassword()
    {
    }

    /**
     * Test attribute "user_phone"
     */
    public function testPropertyUserPhone()
    {
    }

    /**
     * Test attribute "user_email"
     */
    public function testPropertyUserEmail()
    {
    }

    /**
     * Test attribute "user_first_name"
     */
    public function testPropertyUserFirstName()
    {
    }

    /**
     * Test attribute "user_last_name"
     */
    public function testPropertyUserLastName()
    {
    }

    /**
     * Test attribute "account_name"
     */
    public function testPropertyAccountName()
    {
    }

    /**
     * Test attribute "country"
     */
    public function testPropertyCountry()
    {
    }
}
