<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsRotation.php');
include('../class/clscheckoff.php');
include('../class/clsAttendance.php');
include('../class/clsCourses.php');
include('../class/clsLocations.php');
include('../setRequest.php');
include('../class/clsHospitalSite.php');

// echo "IP=>".$_SERVER['REMOTE_ADDR'];exit;
$checkoffId = 0;
$isAlreadyClockOut = 0;
$rotationId = 0;
$totalRotations = 0;
$isActiveOnly = 0;
$attendanceId = '';
$isClockOut = 0;
$status = '';
$isUpcoming = '';
$clockinoutstatus = "";
$clockInDateTime = "";
$clockOutDateTime = "";
$title = "Rotations ";
$totalHoursTominutes = 0;
$totalMinutes = 0;
$approvedTotalHoursTominutes = 0;
$getTotalApprovedHoursByClinician = 0;
$approvedTotalMinutes = 0;
$courseId = 0;
$schoolId = $currentSchoolId;
$loggegStudentId = $_SESSION["loggedStudentId"];

$TimeZone = $_SESSION["loggedStudentSchoolTimeZone"];

$isActiveCheckoff = '';
$isActiveCheckoff = $_SESSION["isActiveCheckoff"];

//Soap Note Module - Enabled/Disabled through superadmin -> admin 
$objDB = new clsDB();
$soapNote = $objDB->GetSingleColumnValueFromTable('schoolsettings', 'status', 'schoolId', $schoolId, 'type', 'soapNote');
unset($objDB);

//For Student Rotation
$objRotation = new clsRotation();

if (isset($_GET["courseId"])) {
    $courseId = DecodeQueryData($_GET["courseId"]);
}
if (isset($_GET["active"])) {

    if ($_GET["active"] == "1") {
        $isActiveOnly = 1;
        $rowsRotations = $objRotation->GetStudentClockInRotations1($loggegStudentId, $currentSchoolId, 'active');
    }
} else {
    $rowsRotations = $objRotation->GetAllStudentClockInRotations($loggegStudentId, $courseId);
}

if ($rowsRotations != '') {
    $totalRotations = mysqli_num_rows($rowsRotations);
}
//Get course name for dropdown
$objCourses = new clsCourses();
$courseList = $objCourses->GetCourseNameByRotation($schoolId, $loggegStudentId);
unset($objCourses);

//For Stduent Attendance
$objAttendance = new clsAttendance();
$pendingRotationId = $objAttendance->CheckPendingCkockOut($loggegStudentId);
$clsBtnAll = "btn-default";
$clsBtnActive = "btn-success";
if (isset($_GET['active'])) {
    $clsBtnAll = "btn-default";
    $clsBtnActive = "btn-success";
} else {
    $clsBtnAll = "btn-success";
    $clsBtnActive = "btn-default";
}

$curdate = date("y-m-d h:i:s");
$repeatday = date('w', strtotime($curdate));
$ispendingRotationId = isset($_GET['ispendingRotationId']) ? base64_decode($_GET['ispendingRotationId']) : 0;
$pendingRotationName = '';
if ($ispendingRotationId) {
    $objDB = new clsDB();
    $pendingRotationName = $objDB->GetSingleColumnValueFromTable('rotation', 'title', 'rotationId', $ispendingRotationId);
    unset($objDB);
}

$objHospitalSite = new clsHospitalSite();
$hospitalSites = $objHospitalSite->GetAllHospitalSiteWithClockInTime($currentSchoolId);

$objDB = new clsDB();
$isScheduleActive = $objDB->GetSingleColumnValueFromTable('schools', 'scheduleActive', 'schoolId', $currentSchoolId);
$studentChangeHospitalSite = $objDB->GetSingleColumnValueFromTable('schools', 'studentChangeHospitalSite', 'schoolId', $currentSchoolId);
unset($objDB);
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($title); ?></title>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <?php include('includes/headercss.php'); ?>
    <?php include("includes/datatablecss.php") ?>

    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/fullcalendar/fullcalendar_main.css" />


    <style>
        /* .w1 {
            width: 75px;
        } */

        .ml {
            margin-left: 10px;
        }


        .input-group-addon {
            position: absolute;
            right: 7px;
            /* width: 100%; */
            z-index: 99;
            width: 35px;
            margin: auto;
            top: 5px;
            border-left: 1px solid #ccc;
            border-radius: 50% !important;
            padding: 10px -2px;
            height: 35px;
            /* background: #01A750; */
            /* color: #fff; */
            color: #555;
            background: #f6f9f9;
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .panel-default>.panel-heading {
            background-color: transparent !important;
        }

        .btn-success,
        .btn-default {
            padding: 8px 25px;
            border-radius: 10px;
        }

        .panel {
            border-radius: 14px !important;
        }

        .input-group-addon {
            position: absolute;
            right: 7px;
            /* width: 100%; */
            z-index: 99;
            width: 35px;
            margin: auto;
            top: 5px;
            border-left: 1px solid #ccc;
            border-radius: 50% !important;
            padding: 10px -2px;
            height: 35px;
            /* background: #01A750; */
            /* color: #fff; */
            color: #555;
            background: #f6f9f9;
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .panel-default>.panel-heading {
            background-color: transparent !important;
        }

        .btn-success,
        .btn-default {
            padding: 8px 25px;
            border-radius: 10px;
        }

        .panel {
            border-radius: 14px !important;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 45px !important;
        }

        .required-select2 {
            border-left: solid 3px red !important;
            border-radius: 12px !important;
        }

        .select2-container--default .select2-selection--single {
            background-color: #f6f9f9 !important;
            cursor: default !important;
            height: 45px !important;
            border-radius: 10px !important;
        }

        .select2-container--default .select2-selection--single {
            border: none !important;
        }

        .panel,
        .form-group {
            margin-bottom: 10px;
        }

        .bootstrap-datetimepicker-widget {
            border-radius: 12px !important;
        }

        .form-control {
            height: 45px;
        }

        .input-group {
            width: 100%;
        }

        div.dataTables_wrapper div.dataTables_length select {
            height: 45px;
        }

        .dt-bootstrap {
            overflow-x: auto;
        }

        /* .form-horizontal .form-group {
                margin-right: 0;
                margin-left: 0;
            } */

        @media screen and (max-width: 500px) {
            .panel-body ol {
                padding-left: 20px;
            }

            .panel-default>.panel-heading {
                padding-left: 5px;
            }

            /* .container-zero{
                    padding: 0;
                } */

            div.dataTables_wrapper div.dataTables_length {
                text-align: right !important;
                margin-top: 10px;
            }

            div.dataTables_wrapper div.dataTables_filter {
                text-align: end !important;
            }
        }

        a {
            text-decoration: none !important;
        }

        .fc-direction-ltr {
            border: 1px solid #8080802b;
            padding: 8px;
            border-radius: 10px;
            margin-top: 20px;
            box-shadow: rgba(17, 17, 26, 0.05) 0px 1px 0px, rgba(17, 17, 26, 0.1) 0px 0px 8px;
        }

        .fc {
            font-size: 1em;
        }

        .fc .fc-toolbar-title {
            font-size: 1.2em;
            margin: 0;
            color: rgb(45, 105, 182);
            font-weight: 600;
        }

        .fc .fc-button-primary:disabled,
        .fc .fc-button {
            color: #fff;
            background-color: rgb(45, 105, 182) !important;
            border-color: rgb(45, 105, 182);
        }

        .fc .fc-button-primary:hover {
            border: none;
        }

        .fc .fc-button {
            font-weight: 400;
            padding: 0.4em 0.65em;
            font-size: .8em;
            line-height: 1.5;
            border-radius: 0.25em;
            background: transparent;
        }

        .fc-theme-standard td,
        .fc-theme-standard th,
        .fc-theme-standard .fc-scrollgrid {
            border: none;
        }

        .fc th {
            /* text-align: left; */
            text-align: center;
        }

        .fc-toolbar-chunk:nth-child(3) {
            display: flex;
        }

        .fc .fc-scrollgrid-section-body table,
        .fc .fc-scrollgrid-section-footer table,
        .fc .fc-daygrid-body,
        .fc .fc-scrollgrid-section table {
            width: 100% !important;
        }

        .fc .fc-view-harness {
            height: 162.074px !important;
        }

        .fc-direction-ltr {
            background: #ffffff !important;
        }

        .fc .fc-button-primary {
            border: none;
        }

        .fc .fc-bg-event,
        .fc .fc-non-business,
        .fc .fc-highlight {
            left: 4px;
        }

        .fc .fc-col-header-cell-cushion {
            color: #337ab7;
            background-color: transparent;
            padding: 2px 0px;
            font-weight: 600;
        }

        .fc .fc-daygrid-day-number {
            position: relative;
            z-index: 4;
            padding: 4px;
        }

        .fc .fc-bg-event {
            background: rgb(143, 223, 130);
            background: var(--fc-bg-event-color, rgb(143, 223, 130));
            opacity: 0.3;
            /* / opacity: var(--fc-bg-event-opacity, 0.3); / */
            color: #ffffff;
            border-radius: 50%;
            width: 28px;
            height: 28px;
            /* / display: flex; /
            / justify-content: end; / */
            float: right;
        }

        .fc .fc-scroller-liquid-absolute,
        .fc-scroller {
            overflow: hidden !important;
        }

        .fc .fc-daygrid-day-frame {
            position: relative;
            min-height: 100%;
            height: 28px;
        }

        .fc .fc-daygrid-day-top {
            display: flex;
            flex-direction: row;
            justify-content: center;
        }

        .hover-trigger {
            cursor: pointer;
            background: transparent;
            outline: none;
            border: none;
        }

        .fc-today-button {
            margin-left: 4px !important;
        }

        .table-flex {
            display: flex;
            justify-content: space-between;
        }

        .display-none {
            display: none;
        }

        .display-flex {
            display: flex;
        }

        .calendar-container {
            position: absolute;
            right: 5px;
            top: 10px;
        }

        .position-relative {
            position: relative;
        }
    </style>
</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li class="active">Rotations</li>
                </ol>
            </div>
        </div>
    </div>

    <div class="custom-container">

        <?php

        if (isset($_GET["status"])) {
            if ($_GET["status"] == "Added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Rotation added successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "clockedin") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Rotation Clocked In successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "clockedout") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Rotation Clocked Out successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "alreadyclockedin") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button>
                    <!-- Clock out previously Clocked in Rotation. -->
                    <?php echo $pendingRotationName; ?>&nbsp;Rotation is already running.
                </div>
            <?php
            } else if ($_GET["status"] == "Updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Rotation updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Deleted") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Rotation deleted successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
        <?php
            }
        }
        ?>

        <div id="divTopLoading">Loading...</div>

        <a class="showall btn <?php echo ($clsBtnAll); ?> pull-right w1 ml" href="rotations.html">Inactive</a>
        <a class="showactive btn <?php echo ($clsBtnActive); ?> pull-right w1" href="rotations.html?active=1">Active</a>
        <br /> <br>
        <div class="col-md-12 pull-right p-0 mt-10 mb-10" style="display: flex;justify-content: end;">
            <label class=" control-label " for="cboCourse" style="margin-top:8px">Course:</label>
            <div class="col-md-2 pull-right padding_right_zero">
                <select id="cboCourse" name="cboCourse" class="form-control input-md required-input select2_single">
                    <option value="" selected>Select</option>
                    <?php
                    if ($courseList != "") {
                        while ($row = mysqli_fetch_assoc($courseList)) {
                            $selcourseId  = $row['courseId'];
                            $name  = stripslashes($row['courseTitle']);

                    ?>
                            <option value="<?php echo (EncodeQueryData($selcourseId)); ?>" <?php if ($courseId == $selcourseId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                    <?php
                        }
                    }
                    ?>
                </select>
            </div>
        </div>


        <br><br>
        <div class="col-md-12 p-0 table-parenr">
            <table style="overflow-x: auto;" id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                <thead>
                    <tr>
                        <th>Rotation</th>
                        <th>Course</th>
                        <th>Start Date</th>
                        <th>End Date</th>
                        <th style="text-align: center">Duration</th>
                        <th style="text-align: center" class="">Clock In/Out</th>
                        <th style="text-align:center">Attendance<br>Approved Hours</th>
                        <th style="text-align:center">Attendance<br>Total Hours</th>
                        <th style="text-align: center">Action</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $scheduledatesArray = array();

                    if ($totalRotations > 0) {
                        while ($row = mysqli_fetch_array($rowsRotations)) {

                            $isActiveRotations = isset($_GET['active']) ? $_GET['active'] : 0;
                            $rotationId = $row['rotationId'];
                            $title = $row['title'];
                            $courseTitle = $row['courseTitle'];
                            $hospitalTitle = $row['hospitalTitle'];
                            $hospitalSiteId = $row['hospitalSiteId'];
                            $duration = $row['duration'];
                            //$status=$row['status'];
                            $isInActiveRotation = isset($row['isInActiveRotation']) ? $row['isInActiveRotation'] : 0;
                            $courseHours = $row['courseHours'];

                            $courselocationId = $row['locationId'];
                            $parentRotationId = stripslashes($row['parentRotationId']);
                            $rotationLocationId = stripslashes($row['rotationLocationId']);
                            $locationId = 0;

                            if ($rotationLocationId != $courselocationId && $parentRotationId > 0) {
                                if (!$rotationLocationId)
                                    $locationId = $objRotation->GetLocationByRotation($rotationId);
                                else
                                    $locationId  = $rotationLocationId;
                            } else {
                                $locationId  = $courselocationId;
                            }

                            //Get Time Zone By Rotation 
                            $objLocation = new clsLocations();
                            $TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
                            unset($objLocation);
                            if ($TimeZone == '')
                                $TimeZone = $_SESSION["loggedStudentSchoolTimeZone"];

                            if (isset($row['isUpcoming']))
                                $isUpcoming = $row['isUpcoming'];
                            //$isClockIn = $row['isClockIn'];

                            $endDate = $row['endDate'];
                            $endDate = converFromServerTimeZone($endDate, $TimeZone);

                            $scheduleEndDate = $endDate = $row['endDate'];
                            $endDate = converFromServerTimeZone($endDate, $TimeZone);

                            $scheduleStartDate = $startDate = $row['startDate'];
                            $startDate = converFromServerTimeZone($startDate, $TimeZone);

                            $startDateTimestamp = strtotime($startDate);
                            $endDateTimestamp = strtotime($endDate);

                            $rotationEndDate = date('m/d/Y', $endDateTimestamp);
                            $curDate = strtotime(date('m/d/Y'));


                            $attedanceDetails = $objAttendance->isClockOut($rotationId, $loggegStudentId, $schoolId);
                            $LatestattedanceId = $objAttendance->GetLatestIsClockOutAttendanceId($rotationId, $loggegStudentId, $schoolId);
                            $clockOutDateTime = "";
                            $clockInDateTime = "";
                            if ($attedanceDetails != '') {
                                $attendanceId = $attedanceDetails['attendanceId'];
                                $isClockOut = $attedanceDetails['isClockOut'];
                                $clockInDateTime = $attedanceDetails['clockInDateTime'];
                                $clockInDateTime = converFromServerTimeZone($clockInDateTime, $TimeZone);

                                $clockOutDateTime = $attedanceDetails['clockOutDateTime'];
                                $clockOutDateTime = converFromServerTimeZone($clockOutDateTime, $TimeZone);

                                $clockedOutRotationId = $attedanceDetails['rotationId'];
                                $schoolLocalDate = date('m/d/Y', strtotime($schoolLocalDate));
                                $clockINDate = date('m/d/Y', strtotime($clockInDateTime));
                                //echo  '$schoolLocalDate' ; echo $schoolLocalDate = date('m/d/Y', strtotime($schoolLocalDate));
                                //echo  '$clockINDate' ; echo $clockINDate = date('m/d/Y', strtotime($clockInDateTime));

                            }


                            $preRotationId = $objAttendance->GetAllStudentClockInAllRotations($rotationId, $loggegStudentId, $schoolId);
                            $clockinoutstatus = "";
                            //check rotation is schedule or not
                            $objDB = new clsDB();
                            $isSchedule = $objDB->GetSingleColumnValueFromTable('rotation', 'isSchedule', 'rotationId', $rotationId);
                            $studentCount = 0;
                            if ($isSchedule)
                                $studentCount = $objRotation->GettudentCountByRotationId($rotationId);
                            unset($objDB);
                            $scheduledates = '';

                            if ($isSchedule) {
                                $rowParentRotation = $objRotation->GetrotationDetails($parentRotationId, $schoolId);
                                $parentRotationEndDate = $rowParentRotation['endDate'];
                                $parentRotationEndDate = converFromServerTimeZone($parentRotationEndDate, $TimeZone);

                                $parentRotationStartDate = $rowParentRotation['startDate'];
                                $parentRotationStartDate = converFromServerTimeZone($parentRotationStartDate, $TimeZone);

                                $parentRotationStartDates = date('m/d/Y', strtotime($parentRotationStartDate));

                                $parentRotationStartDateTimestamp = strtotime($parentRotationStartDate);
                                $parentRotationEndDateTimestamp = strtotime($parentRotationEndDate);

                                $rotationScheduleDates = $objRotation->GetScheduleDateById($rotationId);
                                if ($rotationScheduleDates != '') {
                                    $scheduledates = explode(',', $rotationScheduleDates);
                                    $scheduledatesArray["calendar_" . $rotationId] = $scheduledates;
                                }
                            }

                            $isHideAction = 0;
                            if (isset($_GET["active"])) {
                                if ($isUpcoming == '0') //Active
                                {

                                    if ($isInActiveRotation) {
                                        $clockinoutstatus = '<span>-</span>';
                                    } else {

                                        if (($clockInDateTime != '' && $clockOutDateTime != '') && $clockINDate >= $curDate) //Active
                                        {

                                            //Rotation already clocked out
                                            if ($rotationId == $clockedOutRotationId && $isClockOut == 1) {

                                                $clockinoutstatus = '<span class="text-muted">Clocked Out</span>';
                                            } else {
                                                // $rotationScheduleId = $objRotation->GetScheduleDateById($rotationId);

                                                // if($isSchedule && $rotationScheduleId)
                                                $isClockInRotation = 0;

                                                $clockinoutstatus = '<a class="text-success" href="clockintranssubmit.html?type=clockin&id=' . $rotationId . '">Clock In</a>';
                                                if ($pendingRotationId) {
                                                    if ($pendingRotationId == $rotationId) {
                                                        $clockinoutstatus = '<a class="text-danger" href="clockintranssubmit.html?type=clockout&id=' . $rotationId . '&attendanceId=' . $LatestattedanceId . '">Clock Out
                                                        </a>';
                                                        $isClockInRotation = 1;
                                                    }
                                                }
                                            }
                                        } else //Upcoming
                                        {
                                            $isClockInRotation = 0;
                                            if (date('d/m/Y') == date('d/m/Y', strtotime($clockInDateTime)) && $isClockOut == 1) {
                                                $clockinoutstatus = '-';
                                                $attendanceCount = $objAttendance->GetAttendanceCountByDate($loggegStudentId, $rotationId);
                                                $attendanceCount = ($attendanceCount != '') ? $attendanceCount : 0;
                                                // Convert the datetime strings to date only
                                                $startDateOnly = date("Y-m-d", strtotime($scheduleStartDate));
                                                // Add one day to the end date
                                                $endDateOnly = date("Y-m-d", strtotime($scheduleEndDate . " +1 day"));

                                                // Get the current date
                                                $currentDate = date("Y-m-d");
                                                // Check if the start date and end date are between the current date (date-only check)
                                                // if ($currentDate >= $startDateOnly && $currentDate <= $endDateOnly) {
                                                // Extract the hours from the datetime strings
                                                $startHour = date("H", strtotime($scheduleStartDate));
                                                $endHour = date("H", strtotime($scheduleEndDate));

                                                // Check if the start time is in the afternoon (PM) and the end time is in the morning (AM)
                                                if ($startHour >= 12 && $endHour < 12 && $attendanceCount < 2) {
                                                    $clockinoutstatus = '<a class="text-success" href="clockintranssubmit.html?type=clockin&id=' . $rotationId . '">Clock In</a>';
                                                }
                                                // }
                                            } else {
                                                $clockinoutstatus = '<a class="text-success" href="clockintranssubmit.html?type=clockin&id=' . $rotationId . '">Clock In</a>';
                                                if ($pendingRotationId) {
                                                    if ($pendingRotationId == $rotationId) {
                                                        $clockinoutstatus = '<a class="text-danger" href="clockintranssubmit.html?type=clockout&id=' . $rotationId . '&attendanceId=' . $LatestattedanceId . '">Clock Out
                                                        </a>';
                                                        $isClockInRotation = 1;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                } else //Upcoming
                                {
                                    $clockinoutstatus = '<span>Upcoming</span>';
                                    $isHideAction = 1;
                                }
                            } else {
                                $rotationRepeatDays = $objRotation->GetRotationRepeatDaysBYRotation($rotationId);
                                $rotationRepeatDays = $rotationRepeatDays ? explode(',', $rotationRepeatDays) : [];
                                if ($isSchedule == 0) {
                                    if (in_array($repeatday, $rotationRepeatDays) && $isInActiveRotation)
                                        continue;
                                } else {

                                    // Convert the datetime strings to date only
                                    $startDateOnly = date("Y-m-d", strtotime($scheduleStartDate));
                                    // Add one day to the end date
                                    $endDateOnly = date("Y-m-d", strtotime($scheduleEndDate . " +1 day"));

                                    // Get the current date
                                    $currentDate = date("Y-m-d");
                                    // Check if the start date and end date are between the current date (date-only check)
                                    if ($currentDate >= $startDateOnly && $currentDate <= $endDateOnly) {
                                        // Extract the hours from the datetime strings
                                        $startHour = date("H", strtotime($scheduleStartDate));
                                        $endHour = date("H", strtotime($scheduleEndDate));

                                        // Check if the start time is in the afternoon (PM) and the end time is in the morning (AM)
                                        if ($startHour >= 12 && $endHour < 12) {
                                            continue;
                                        }
                                    }
                                }
                                $clockinoutstatus = '-';
                                if ($pendingRotationId) {
                                    if ($pendingRotationId == $rotationId) {
                                        $clockinoutstatus = '<a class="text-danger" href="clockintranssubmit.html?type=clockout&id=' . $rotationId . '&attendanceId=' . $LatestattedanceId . '">Clock Out
                                    </a>';
                                    }
                                }
                            }

                            $hospitalSite = $row['hospitalTitle'];
                            $hospitalSiteDispName = strlen($hospitalSite) > 30 ? (substr($hospitalSite, 0, 27)) . '...' : $hospitalSite;

                            $getTotals = $objAttendance->GetTotalHoursForReport($rotationId, $loggegStudentId, $courseId);
                            $getTotalApprovedHours = $objAttendance->GetApprovedHoursByClinician($rotationId, $loggegStudentId,  $courseId);
                            // print_r($getTotalApprovedHoursByClinician); exit;
                            // $getTotalApprovedHours = $getTotals['totalApprovedhours'];
                            if ($getTotalApprovedHours) {
                                $ApprovedHours = explode(':', $getTotalApprovedHours);
                                $Hours = $ApprovedHours[0];
                                $Minutes = $ApprovedHours[1];
                                $approvedTotalHoursTominutes += $Hours;
                                $approvedTotalMinutes += $Minutes;
                                $getTotalApprovedHours = $Hours . ':' . $Minutes;
                            } else {
                                $getTotalApprovedHours = "-";
                            }
                            $getTotalOrignalhours = $getTotals['totalOrignalhours'];
                            if ($getTotalOrignalhours) {
                                $ApprovedHours = explode(':', $getTotalOrignalhours);
                                $Hours = $ApprovedHours[0];
                                $Minutes = $ApprovedHours[1];
                                $totalHoursTominutes += $Hours;
                                $totalMinutes += $Minutes;
                                $getTotalOrignalhours = $Hours . ':' . $Minutes;
                            } else {
                                $getTotalOrignalhours = "-";
                            }

                            $rotationLocation = $objRotation->GetLocationNameBySubRotationId($rotationId);

                    ?>
                            <tr>

                                <td>
                                    <div>
                                        <?php echo ($title) . '<br><div><small><label title="' . $hospitalSite . '" style="margin-bottom: 0 !important;" >Hospital: ' . $hospitalSiteDispName . '</label></small></div>'; ?>
                                        <?php
                                        if ($rotationLocation != '') {
                                            echo '<div><small><label title="' . $rotationLocation . '">Location: ' . $rotationLocation . '</label></small></div>';
                                        }
                                        ?>
                                    </div>

                                </td>
                                <td>
                                    <?php echo $courseTitle; ?>
                                    <?php if ($courseHours != '') { ?>
                                        <br><small>Required Hours: </small><?php echo '<small>' . $courseHours . 'Hrs </small>'; ?>
                                    <?php } ?>
                                </td>

                                <td class="position-relative">
                                    <div class="flex" style="padding-right: 20px;">
                                        <?php echo (date('m/d/Y', $startDateTimestamp)); ?>
                                    </div>
                                    <?php if ($isSchedule) { ?>
                                        <div class="calendar-container">
                                            <div class="hover-container" style="position: relative;">
                                                <button class="hover-trigger" style="cursor: pointer;">
                                                    <span class="glyphicon glyphicon-calendar"></span>
                                                </button>
                                                <div class="calendar display-none" style="min-width: 255px; position: absolute; z-index: 9999; background: #fff; border: 1px solid #ccc; padding: 10px;top: 2px;" id="calendar_<?php echo $rotationId; ?>" isScheduleDates='<?php echo $rotationScheduleDates; ?>'>
                                                </div>
                                            </div>
                                        </div>
                                    <?php } ?>

                                </td>
                                <td>
                                    <?php echo (date('m/d/Y', $endDateTimestamp)); ?>
                                </td>
                                <td>
                                    <small>From </small> <?php echo (date('h:i A', $startDateTimestamp)); ?>
                                    <small> To </small> <?php echo (date('h:i A', $endDateTimestamp)); ?>
                                </td>
                                <td style="text-align:center">
                                    <?php echo ($clockinoutstatus); ?>
                                </td>
                                <td class="text-center"><?php echo $getTotalApprovedHours; ?></td>
                                <td class="text-center"><?php echo $getTotalOrignalhours; ?></td>
                                <td style="text-align: center">
                                    <?php

                                    $timestamp = ($isSchedule) ? $parentRotationEndDateTimestamp : $endDateTimestamp;

                                    // if ($curDate >=  $timestamp) { 
                                    ?>
                                    <!-- <a href="javascript:void(0);">Rotation Expired.</a> -->
                                    <?php //} elseif ($isHideAction) { 
                                    ?>
                                    <!-- <a href="javascript:void(0);"> - </a> -->
                                    <?php //} else {

                                    if ($isClockOut == 0) { ?>

                                        <!-- <a class="showactive btn " href="#">Please ClockIn/out</a>| -->
                                    <?php } ?>

                                    <a href="journal.html?rotationId=<?php echo EncodeQueryData($rotationId) ?>">Daily Journal</a> |
                                    <a href="interaction.html?rotationId=<?php echo (EncodeQueryData($rotationId)); ?>">Dr.Interaction</a>|
                                    <?php if ($isActiveCheckoff == 1) { ?>
                                        <a href="checkoff.html?rotationId=<?php echo EncodeQueryData($rotationId) ?>"> Checkoffs</a>
                                    <?php } elseif ($isActiveCheckoff == 2) { ?>
                                        <a href="checkoffusaf.html?rotationId=<?php echo EncodeQueryData($rotationId) ?>"> Checkoffs</a>
                                    <?php } else { ?>
                                        <a href="checkoffs.html?rotationId=<?php echo EncodeQueryData($rotationId) ?>"> Checkoffs</a>
                                    <?php } ?>
                                    |<a href="formativelist.html?formativerotationid=<?php echo EncodeQueryData($rotationId); ?>">Formative</a>|
                                    <a href="dailyEvalList.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>&Type=A">Daily/Weekly</a>|
                                    <a href="midtermlist.html?midtermrotationid=<?php echo EncodeQueryData($rotationId); ?>">Midterm</a>|
                                    <a href="summativelist.html?summativerotationid=<?php echo EncodeQueryData($rotationId); ?>">Summative</a>|
                                    <?php if ($isActiveCheckoff == "0") { ?>
                                        <a href="masteryList.html?masteryrotationid=<?php echo EncodeQueryData($rotationId); ?>">Mastery Evaluation</a>|
                                    <?php } ?>
                                    <a href="cievaluationlist.html?cievaluationrotationid=<?php echo EncodeQueryData($rotationId); ?>">CI Evaluation</a>|
                                    <a href="pevaluationlist.html?pevaluationrotationid=<?php echo EncodeQueryData($rotationId); ?>">P Evaluation</a>|
                                    <?php if (isset($_GET["active"])) { ?>
                                        <a href="siteevaluationlist.html?siteevaluationrotationid=<?php echo EncodeQueryData($rotationId); ?>&active=A">Site Evaluation</a>|
                                    <?php } else { ?>
                                        <a href="siteevaluationlist.html?siteevaluationrotationid=<?php echo EncodeQueryData($rotationId); ?>">Site Evaluation</a>|
                                    <?php } ?>
                                    <?php if ($isActiveCheckoff == '1') { ?>
                                        <a href="volunteerEvalList.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>">Volunteer Evaluation</a>|
                                    <?php } ?>

                                    <?php if ($schoolId == 75 || $schoolId == 121) { ?>
                                        <a href="pefList.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>">PEF Evaluation</a>|
                                    <?php } ?>
                                    <?php if ($schoolId == 75 || $schoolId == 127) { ?>
                                        <a href="floorTherapyAndICUEvaluationlist.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>">Floor Therapy And ICU Evaluation</a>|
                                    <?php } ?>
                                    <a href="procedurecounts.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>">Procedure Count</a>
                                      <?php if ($soapNote) { ?>
                                        | <a href="soapNoteList.html?type=soapnote&rotationId=<?php echo EncodeQueryData($rotationId) ?>">Soap Note</a> 
                                    <?php } ?>
                                    |<a href="incident.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>">Incident</a>

                                    <?php if ($isActiveOnly == "1") { ?>
                                        |<a href="studentAttendance.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>">Exception</a>
                                        <?php if ($isSchedule && $isScheduleActive && $studentChangeHospitalSite) {
                                            // if ($isClockInRotation || $studentCount) {
                                            if ($isClockInRotation) {
                                        ?>
                                                |<a href="javascript:void(0);" class="changeHospitalSiteWarning" isType="ClockIn">Change Hospital Site</a>
                                            <?php } else if ($studentCount > 1) {
                                            ?>
                                                |<a href="javascript:void(0);" class="changeHospitalSiteWarning" isType="student">Change Hospital Site</a>

                                            <?php //}
                                            } else { ?>
                                                |<a href="javascript:void(0);" class="changeHospitalSite" data-toggle="modal" data-target="#changeHospitalSiteModal" isHospitalSiteId="<?php echo $hospitalSiteId ?>" rotationId="<?php echo $rotationId ?>" rotationTitle="<?php echo $title; ?>" onclick="ShowRotationDetails(this)">Change Hospital Site</a>
                                        <?php }
                                        }
                                    } else { ?>
                                        |<a href="studentAttendance.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>">Exception</a>|
                                    <?php }
                                    // }
                                    ?>
                                  
                                </td>
                            </tr>
                    <?php
                        }
                        $calendarIds = array_keys($scheduledatesArray);
                    }

                    unset($objAttendance);
                    unset($objRotation);
                    ?>
                </tbody>
                <tfoot>
                    <tr>
                        <?php
                        //For Approved Hours
                        $totalHoursTominutes = $totalHoursTominutes * 60;
                        $totalOriginalHoursForAllStudent = $totalHoursTominutes + $totalMinutes;

                        //For Original Hours

                        $approvedTotalHoursTominutes = $approvedTotalHoursTominutes * 60;
                        $totalApprovedHoursForAllStudent = $approvedTotalHoursTominutes + $approvedTotalMinutes;

                        ?>
                        <td colspan="6" align="right"><b>Total:</b></td>
                        <td align="center"><?php echo sprintf("%02d:%02d", floor($totalApprovedHoursForAllStudent / 60), $totalApprovedHoursForAllStudent % 60);  ?></td>
                        <td align="center"><?php echo sprintf("%02d:%02d", floor($totalOriginalHoursForAllStudent / 60), $totalOriginalHoursForAllStudent % 60);  ?></td>
                        <td></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
    <!-- Modal -->
    <div class="modal fade" id="changeHospitalSiteModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">Change Hospital Site For: <span id="titleSpan"></span> </h4>
                </div>
                <div class="modal-body">
                    <div class="modal-body">

                        <form id="resendForm" data-parsley-validate class="form-horizontal" method="POST">

                            <div class="form-group isPreceptorDiv">
                                <table id="preceptorTable" width="100%">
                                    <tr>
                                        <td>
                                            <input type="hidden" id="rotationId" name="rotationId" value="0"><br>
                                            <div class="form-group hospitalSiteClass">
                                                <input type="hidden" name="default_courseId" id="default_courseId" value="<?php echo $default_courseId; ?>">
                                                <input type="hidden" name="hiddenModalhospitalId" id="hiddenModalhospitalId" value="0">
                                                <label class="col-md-12 " for="cbohospitalsites">Hospital Site</label>
                                                <div class="col-md-12">
                                                    <select id="cbohospitalsites" name="cbohospitalsites" class="form-control input-md required-input select2_single" style="width: 100%;" required>
                                                        <option value="" selected>Select</option>
                                                        <?php
                                                        $hospitalSiteId = 0;
                                                        if ($hospitalSites != "") {
                                                            while ($row = mysqli_fetch_assoc($hospitalSites)) {
                                                                $selhospitalSiteId  = $row['hospitalSiteId'];
                                                                $name  = stripslashes($row['title']);
                                                                $clockIn  = $row['clockIn'];
                                                                $clockOut  = $row['clockOut'];
                                                                $name = ($clockIn != '' && $clockOut != '') ? $name . ' (' . $clockIn . '-' . $clockOut . ')' : $name;

                                                        ?>
                                                                <option value="<?php echo ($selhospitalSiteId); ?>" <?php if ($hospitalSiteId == $selhospitalSiteId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>

                                                        <?php

                                                            }
                                                        }
                                                        ?>
                                                    </select>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                </table>

                            </div>
                            <center>
                                <button id="btnChange" name="btnChange" type="button" class="btn btn-primary btnChange ">Change</button>
                            </center>
                        </form>

                    </div>

                    <div class="modal-footer">

                    </div><!-- /.modal-content -->
                </div>
                <!-- /.modal-dialog -->
            </div>
        </div>
    </div>
    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/fullcalendar/fullcalendar_main.js"></script>

    <script type="text/javascript">
        $('.addCommentpopup').magnificPopup({
            'type': 'ajax',
        });

        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        $(document).ready(function() {
            $('#changeHospitalSiteModal').on('shown.bs.modal', function() {
                // console.log("Modal shown");
                $('#cbohospitalsites').select2();
            });

        });


        $(window).load(function() {
            $(".select2_single").select2();

            $("#divTopLoading").addClass('hide');


        });

        $("#cboCourse").change(function() {
            var courseId = $(this).val();
            if (courseId) {
                window.location.href = "rotations.html?courseId=" + courseId;
            } else {
                window.location.href = "rotations.html";
            }
        });

        var current_datatable = $("#datatable-responsive").DataTable({
            "ordering": true,
            "aaSorting": [],
            "order": [
                [2, "desc"]
            ],

            "aoColumns": [{
                    "sWidth": "10%"
                }, {
                    "sWidth": "5%",
                    "sClass": "alignCenter"
                }, {
                    "sWidth": "5%",
                    "sClass": "alignCenter"

                }, {
                    "sWidth": "5%",
                    "sClass": "alignCenter"
                },
                {
                    "sWidth": "10%",
                    "sClass": "alignCenter"
                }, {
                    "sWidth": "5%",
                    "sClass": "alignCenter",
                    "bSortable": false
                }, {
                    "sWidth": "10%",
                    "sClass": "alignCenter"
                }, {
                    "sWidth": "5%",
                    "sClass": "alignCenter"
                }, {
                    "sWidth": "55%",
                    "sClass": "alignCenter",
                    "bSortable": false
                }
            ]
        });

        function ShowRotationDetails(eleObj) {
            var isHospitalSiteId = eleObj.hasAttribute('isHospitalSiteId') ? $(eleObj).attr('isHospitalSiteId') : 0;
            var rotationId = eleObj.hasAttribute('rotationId') ? $(eleObj).attr('rotationId') : 0;
            var rotationTitle = eleObj.hasAttribute('rotationTitle') ? $(eleObj).attr('rotationTitle') : '';

            $('#titleSpan').text(rotationTitle);
            // $('#cbohospitalsites').val(isHospitalSiteId).trigger('change');
            $('#rotationId').val(rotationId);
            $('#hiddenModalhospitalId').val(isHospitalSiteId);
            $('.btnSendEmail').html('Send');
            $('.btnSendEmail').prop('disabled', false);
            // $('#email_evaluationId').val(email_preceptorNum);
        }

        $(document).on('click', '.btnChange', function() {
            var hospitalSiteId = $('#cbohospitalsites').val();
            var hiddenModalhospitalId = $('#hiddenModalhospitalId').val();
            var rotationId = $('#rotationId').val();
            loggegStudentId = '<?php echo $loggegStudentId; ?>';

            var emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/;
            if (hospitalSiteId == '' || hospitalSiteId == 0) {
                alertify.error('Please Select Hospital Site');
                return false;
            }

            if (hospitalSiteId == hiddenModalhospitalId) {
                alertify.error('Please select another Hospital Site');
                return false;
            }


            // $('.btnChange').html(loadingText());
            // $('.btnChange').prop('disabled', true);
            $.ajax({
                type: "POST",
                url: "../ajax/ajax_change_schedule_hospital_site.html",
                data: {
                    hospitalSiteId: hospitalSiteId,
                    rotationId: rotationId,
                    loggegStudentId: loggegStudentId
                },
                success: function(data) {
                    // console.log(data);
                    if (data == '1') {
                        alertify.success('Hospital site Changed');
                        window.location.reload();
                    } else
                        alertify.success('Error Occur');

                    $('.btnSendEmail').html('Send');
                    $('.btnSendEmail').prop('disabled', false);
                    // window.location.reload();
                }
            });
        });



        $(document).on('click', '.changeHospitalSiteWarning', function() {
            var isType = $(this).attr('isType');
            if (isType == 'ClockIn')
                alertify.error('First Clock Out the Rotation');
            else if (isType == 'student')
                alertify.error('Another Students are assigned to this Rotation');
            // console.log(isType);
            return false;
        });


        // Wait for the DOM content to be loaded before executing the script
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize the calendars for the first time
            initializeCalendars();

            // Reinitialize calendars after DataTable redraws
            $('#datatable-responsive').on('draw.dt', function() {
                // console.log('DataTable redrawn. Reinitializing calendars...');
                initializeCalendars();
            });
        });

        function initializeCalendars() {
            setTimeout(function() {
                // Find all elements with IDs starting with "calendar_"
                const calendarElements = document.querySelectorAll('[id^="calendar_"]');
                // console.log('Found calendars:', calendarElements.length);

                calendarElements.forEach(function(calendarEl) {
                    const id = calendarEl.id;
                    const dates = $('#' + id).attr('isScheduleDates');
                    // console.log(`Processing calendar: ${id} | Dates: ${dates}`);

                    // Destroy any existing calendar instance
                    if (calendarEl._fullCalendar) {
                        // console.log(`Destroying existing calendar for: ${id}`);
                        calendarEl._fullCalendar.destroy();
                    }

                    const eventData = [];

                    if (dates && dates.trim() !== '') {
                        const datesList = dates.split(',');

                        // Prepare event data
                        datesList.forEach(function(date) {
                            const parts = date.split('/');
                            const isoDate = `${parts[2]}-${parts[0].padStart(2, '0')}-${parts[1].padStart(2, '0')}`;
                            eventData.push({
                                start: isoDate
                            });
                        });
                    }

                    // Create a new FullCalendar instance
                    const calendar = new FullCalendar.Calendar(calendarEl, {
                        initialView: 'dayGridMonth',
                        events: eventData,
                        eventDisplay: 'background',
                        eventBackgroundColor: '#5cb85c',
                        selectable: false,
                        headerToolbar: {
                            start: 'prev,today',
                            center: 'title',
                            end: 'next'
                        },
                        titleFormat: {
                            month: 'short',
                            year: 'numeric'
                        },
                    });

                    calendar.render();
                    calendarEl._fullCalendar = calendar; // Store the calendar instance for cleanup
                });
            }, 100); // Slight delay to ensure all elements are ready
        }

        // Handle calendar visibility toggle
        $(document).on('click', '.hover-container', function(e) {
            e.stopPropagation(); // Prevent event bubbling to document

            // Find the specific calendar div within this hover-container
            const $calendar = $(this).find('.calendar');

            // Hide all other calendars
            $('.calendar').not($calendar).hide();

            // Toggle the visibility of the current calendar
            $calendar.toggle();
        });

        // Prevent hiding the calendar when clicking inside it
        $(document).on('click', '.calendar', function(e) {
            e.stopPropagation();
        });

        // Hide calendars when clicking outside any hover-container or calendar
        $(document).on('click', function() {
            $('.calendar').hide();
        });
    </script>
</body>

</html>