<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsHospitalSite.php');
include('../class/clsSystemUser.php');
include("../class/PasswordHash.php");
include('../class/Zebra_Image.php');
include('../setRequest.php');

// echo '<pre>';
// print_r($_GET);
// print_r($_POST);
// exit;
if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit'])) {

	$currentSchoolId = DecodeQueryData($_GET['schoolId']);
	$hospitalSiteId = isset($_GET['id']) ? DecodeQueryData($_GET['id']) : 0;
	$status = ($hospitalSiteId > 0) ? 'updated' : 'added';

	$isCopy = isset($_GET['isCopy']) ? DecodeQueryData($_GET['isCopy']) : 0;
	$status = ($isCopy > 0) ? 'copied' : $status;

	$hospitalSitetitle  = trim($_POST['txttitle']);
	$email  = trim($_POST['txtEmail']);
	$phone  = trim($_POST['txtPhone']);
	$cellPhone  = trim($_POST['txtcellPhone']);
	$address1  = trim($_POST['txtAddress1']);
	$address2  = trim($_POST['txtAddress2']);
	$cboCountry  = trim($_POST['cboCountry']);
	$city  = trim($_POST['txtCity']);
	$state  = trim($_POST['cboState']);
	$zipCode  = trim($_POST['txtZipCode']);
	$contactPerson = trim($_POST['txtcontactPerson']);
	$adminNotes = trim($_POST['adminNotes']);
	$txtfaxNumber = ($_POST['txtfaxNumber']);
	$clockIn = ($_POST['clockIn']);
	$clockOut = ($_POST['clockOut']);
	$notificationOption = ($_POST['cbonotification']);
	// $intialDate= GetDateStringInServerFormat($_POST['initialDate']);
	// $intialDate = str_replace('00:00:00','12:00 PM',$intialDate);
	// $intialDate = date('Y-m-d H:i',strtotime($intialDate));
	if (isset($_POST['initialDate']) && !empty($_POST['initialDate'])) {
		$initialDate = GetDateStringInServerFormat($_POST['initialDate']);
		$initialDate = str_replace('00:00:00', '12:00 PM', $initialDate);
		$initialDate = date('Y-m-d H:i', strtotime($initialDate));
	} else {

		$initialDate = null;
	}
	if (isset($_POST['cbonotificationdate']) && !empty($_POST['cbonotificationdate'])) {

		$notificationDate = GetDateStringInServerFormat($_POST['cbonotificationdate']);
		$notificationDate = str_replace('00:00:00', '12:00 PM', $notificationDate);
		$notificationDate = date('Y-m-d H:i', strtotime($notificationDate));
	} else {

		$notificationDate = null;
	}
	if (isset($_POST['expirationDate']) && !empty($_POST['expirationDate'])) {
		$expireDate = GetDateStringInServerFormat($_POST['expirationDate']);
		$expireDate = str_replace('00:00:00', '12:00 PM', $expireDate);
		$expireDate = date('Y-m-d H:i', strtotime($expireDate));
	} else {

		$expireDate = null;
	}


	$lattitude = ($_POST['txtlattitude']);
	$longitude = ($_POST['txtlongitude']);

	$hospitalDays = isset($_POST['hospitalDays']) ? $_POST['hospitalDays'] : array();
	$hospitalDays = implode(',', $hospitalDays);
	$hospitalDays = ($hospitalDays) ? $hospitalDays : '';
	$dailyVisits = isset($_POST['dailyVisits']) ? $_POST['dailyVisits'] : 0;
	$hospitalSiteCode = isset($_POST['hospitalSiteCode']) ? $_POST['hospitalSiteCode'] : "";


	//Check FirstName, LastName,email and Contact Person blank
	if ($hospitalSitetitle == ""  || $email == "" || $contactPerson == "") {
		header('location:addhospitalsites.html?status=mandatory&id=' . EncodeQueryData($hospitalSiteId));
		exit();
	}


	$objHospitalSite = new clsHospitalSite();

	//save data
	$objHospitalSite->title = ucfirst($hospitalSitetitle);
	$objHospitalSite->email = $email;
	$objHospitalSite->phone = $phone;
	$objHospitalSite->cellPhone = $cellPhone;
	$objHospitalSite->address1 = $address1;
	$objHospitalSite->address2 = $address2;
	$objHospitalSite->cboCountry = $cboCountry;
	$objHospitalSite->city = $city;
	$objHospitalSite->stateId = $state;
	$objHospitalSite->zip = $zipCode;
	$objHospitalSite->faxNumber = $txtfaxNumber;
	$objHospitalSite->adminNotes = $adminNotes;
	$objHospitalSite->contactPerson = $contactPerson;
	$objHospitalSite->clockIn = $clockIn;
	$objHospitalSite->clockOut = $clockOut;
	$objHospitalSite->schoolId = $currentSchoolId;
	$objHospitalSite->intialDate = $initialDate;
	$objHospitalSite->expireDate = $expireDate;
	$objHospitalSite->notificationOption = $notificationOption;
	$objHospitalSite->notificationDate = $notificationDate;
	$objHospitalSite->lattitude = $lattitude;
	$objHospitalSite->longitude = $longitude;
	$objHospitalSite->hospitalDays = $hospitalDays;
	$objHospitalSite->dailyVisits = $dailyVisits;
	$objHospitalSite->hospitalSiteCode = $hospitalSiteCode;
	$objHospitalSite->createdBy = $_SESSION['loggedUserId'];
	if ($isCopy)
		$rethospitalSiteId = $objHospitalSite->SaveHospitalSite(0);
	else
		$rethospitalSiteId = $objHospitalSite->SaveHospitalSite($hospitalSiteId);
	unset($objHospitalSite);

	if ($rethospitalSiteId > 0) {
		//Audit Log Start
		// Instantiate the Logger class
		$objLog = new clsLogger();

		// Determine the action type (EDIT or ADD) based on the presence of a journal ID
		$action = ($hospitalSiteId > 0) ? $objLog::EDIT : $objLog::ADD;
		$userType = $objLog::ADMIN; // User type is set to STUDENT

		$objHospitalSite = new clsHospitalSite();
		$objHospitalSite->saveHospitalSiteAuditLog($rethospitalSiteId, $_SESSION["loggedUserId"], $userType, $action, $IsMobile = 0);
		unset($objHospitalSite);
		unset($objLog);
		//Audit Log End
		header('location:hospitalsites.html?status=' . $status);
		exit();
	} else {
		header('location:addhospitalsites.html?status=error');
		exit();
	}
} else {
	header('location:addhospitalsites.html');
	exit();
}
