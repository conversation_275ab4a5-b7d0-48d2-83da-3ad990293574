<?php
$IsMobile  = isset($_POST['IsMobile']) ? $_POST['IsMobile'] : 0;

$isPreceptor = isset($_GET['isPreceptor']) ? $_GET['isPreceptor'] : 0;

if (!$isPreceptor)
    include('includes/validateUserLogin.php');

include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsSchool.php');
include('../class/clsSystemUser.php');
include('../class/clsStudent.php');
include('../setRequest.php');
include('../class/clsSoapNote.php');
include('../class/clsPreceptorDetails.php');
include('../class/clsExternalPreceptors.php');

$objDB = new clsDB();
$objSoapNote = new clsSoapNote();
$objpreceptor = new clsPreceptorDetails();


// echo '<pre>';
// print_r($_POST);
// exit();

//Get Country Code
$countryCode = $objDB->GetSingleColumnValueFromTable('schools', 'countryCode', 'schoolId', $currentSchoolId);

// $studentId = $_SESSION['loggedStudentId']; 

if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit'])) {

    $isPreceptor = isset($_GET['isPreceptor']) ? $_GET['isPreceptor'] : 0;

    $soapNoteId = isset($_GET['editsoapNoteId']) ? DecodeQueryData($_GET['editsoapNoteId']) : 0;

    $date  = GetDateStringInServerFormat($_POST['soapnotedate']);
    $date = str_replace('00:00:00', '12:00 PM', $date);
    $date = date('Y-m-d H:i', strtotime($date));
    $rotationId = isset($_POST['cborotation']) ? DecodeQueryData($_POST['cborotation']) : null;

    $isSendToPreceptor = isset($_POST['isSendToPreceptor']) ? ($_POST['isSendToPreceptor']) : 0;
    $preceptorNo = isset($_POST['preceptorNo']) ? ($_POST['preceptorNo']) : '';
    $studentSignatureDate = isset($_POST['studentDate']) ? ($_POST['studentDate']) : '';
    $clinicianSignatureDate = isset($_POST['ClinicianDate']) ? ($_POST['ClinicianDate']) : '';
    if ($clinicianSignatureDate) {

        $clinicianSignatureDate  = GetDateStringInServerFormat($_POST['ClinicianDate']);
        $clinicianSignatureDate = str_replace('00:00:00', '12:00 PM', $clinicianSignatureDate);
        $clinicianSignatureDate = date('Y-m-d H:i', strtotime($clinicianSignatureDate));
    } else {
        $clinicianSignatureDate = '';
    }

    $preceptorStatus = isset($_POST['preceptorStatus']) ? ($_POST['preceptorStatus']) : 0;

    if (isset($_POST['studentDate']) && !empty($_POST['studentDate'])) {
        $studentSignatureDate = GetDateStringInServerFormat($_POST['studentDate']);
        $studentSignatureDate = str_replace('00:00:00', '12:00 PM', $studentSignatureDate);
        $studentSignatureDate = date('Y-m-d H:i', strtotime($studentSignatureDate));
    } else {
        $studentSignatureDate = null; // Set to null if not provided
    }

    $studentId = isset($_POST['studentId']) ? ($_POST['studentId']) : 0;
    $clinicianId  = isset($_POST['cboClinicalinstructor']) ? $_POST['cboClinicalinstructor'] : '';
    $hospitalSiteId = isset($_POST['cbohospitalsites']) ? ($_POST['cbohospitalsites']) : '';
    $patientDescription = isset($_POST['patientDescription']) ? ($_POST['patientDescription']) : '';
    $cheifComplaint = isset($_POST['cheifComplaint']) ? ($_POST['cheifComplaint']) : '';
    $admittingDiagnosis = isset($_POST['admittingDiagnosis']) ? ($_POST['admittingDiagnosis']) : '';
    $hpi = isset($_POST['hpi']) ? ($_POST['hpi']) : '';
    $pmh = isset($_POST['pmh']) ? ($_POST['pmh']) : '';
    $medication = isset($_POST['medication']) ? ($_POST['medication']) : '';
    $allergies = isset($_POST['allergies']) ? ($_POST['allergies']) : '';
    $familyHistory = isset($_POST['familyHistory']) ? ($_POST['familyHistory']) : '';
    $socialHistroy = isset($_POST['socialHistroy']) ? ($_POST['socialHistroy']) : '';
    $ros = isset($_POST['ros']) ? ($_POST['ros']) : '';
    $temperature = isset($_POST['temperature']) ? ($_POST['temperature']) : '';
    $heartRate = isset($_POST['heartRate']) ? ($_POST['heartRate']) : '';
    $bloodPressure = isset($_POST['bloodPressure']) ? ($_POST['bloodPressure']) : '';
    $respiratoryRate = isset($_POST['respiratoryRate']) ? ($_POST['respiratoryRate']) : '';
    $oxygenSaturation = isset($_POST['oxygenSaturation']) ? ($_POST['oxygenSaturation']) : '';
    $physicalExam = isset($_POST['physicalExam']) ? ($_POST['physicalExam']) : '';
    $laboratoryImaging = isset($_POST['laboratoryImaging']) ? ($_POST['laboratoryImaging']) : '';
    $assessment = isset($_POST['assessment']) ? ($_POST['assessment']) : '';
    $medicationTreatment = isset($_POST['medicationTreatment']) ? ($_POST['medicationTreatment']) : '';
    $testOrder = isset($_POST['testOrder']) ? ($_POST['testOrder']) : '';
    $referrals = isset($_POST['referrals']) ? ($_POST['referrals']) : '';
    $patientEducationCounseling = isset($_POST['patientEducationCounseling']) ? ($_POST['patientEducationCounseling']) : '';
    $followUp = isset($_POST['followUp']) ? ($_POST['followUp']) : '';

    if ($soapNoteId > 0) {
        if (!empty($clinicianSignatureDate)) {
            $status = "SignOff";
        } else {
            $status = "Updated";
        }
    } else {
        $status = "Added";
    }

    if (!$isPreceptor) {
       
        $objSoapNote = new clsSoapNote();

        // $objSoapNote->soapNoteId = $retSoapNote;
        $objSoapNote->isSendToPreceptor = $isSendToPreceptor;
        $objSoapNote->studentId = $studentId;
        $objSoapNote->schoolId = $currentSchoolId;
        $objSoapNote->rotationId = $rotationId;
        $objSoapNote->clinicianId = $clinicianId;
        $objSoapNote->hospitalSiteId = $hospitalSiteId;
        $objSoapNote->date = $date;
        $objSoapNote->patientDescription = $patientDescription;
        $objSoapNote->cheifComplaint = $cheifComplaint;
        $objSoapNote->admittingDiagnosis = $admittingDiagnosis;
        $objSoapNote->hpi = $hpi;
        $objSoapNote->pmh = $pmh;
        $objSoapNote->medication = $medication;
        $objSoapNote->allergies = $allergies;
        $objSoapNote->familyHistory = $familyHistory;
        $objSoapNote->socialHistroy = $socialHistroy;
        $objSoapNote->ros = $ros;
        $objSoapNote->temperature = $temperature;
        $objSoapNote->heartRate = $heartRate;
        $objSoapNote->bloodPressure = $bloodPressure;
        $objSoapNote->respiratoryRate = $respiratoryRate;
        $objSoapNote->oxygenSaturation = $oxygenSaturation;
        $objSoapNote->physicalExam = $physicalExam;
        $objSoapNote->laboratoryImaging = $laboratoryImaging;
        $objSoapNote->assessment = $assessment;
        $objSoapNote->medicationTreatment = $medicationTreatment;
        $objSoapNote->testOrder = $testOrder;
        $objSoapNote->referrals = $referrals;
        $objSoapNote->patientEducationCounseling = $patientEducationCounseling;
        $objSoapNote->followUp = $followUp;
        $objSoapNote->clinicianSignatureDate = $clinicianSignatureDate;
        $objSoapNote->studentSignatureDate = $studentSignatureDate;
        $objSoapNote->createdBy = $studentId;

        $retSoapNote = $objSoapNote->SaveSoapNoteDetails($soapNoteId);
        // }

        if ($retSoapNote) {
            if ($preceptorNo != '') {
                //For Save Preceptor
                $objExternalPreceptors = new clsExternalPreceptors();
                $objExternalPreceptors->mobile_num = $preceptorNo;
                $preceptorId = $objExternalPreceptors->SavePreceptors();
                if ($preceptorId) {
                    $objPreceptorDetails = new clsPreceptorDetails();
                    $objPreceptorDetails->schoolId = $currentSchoolId;
                    $objPreceptorDetails->referenceId = $retSoapNote;
                    $objPreceptorDetails->preceptorId = $preceptorId;
                    $objPreceptorDetails->type = 'soapnote';
                    $objPreceptorDetails->status = $preceptorStatus;
                    $objPreceptorDetails->signatureDate = $clinicianSignatureDate;
                    $objPreceptorDetails->createdBy = $_SESSION['loggedStudentId'];

                    $retpreceptorDId = $objPreceptorDetails->SavePreceptorDetails();
                    $isEvalTypeLabel = 'Soap Note';

                    $objStudent = new clsStudent();
                    $studentname = $objStudent->GetStudentNameById($studentId);
                    // $studentId = $objStudent->GetStudentDetails($studentId)
                    // echo $studentId;
                    $URL = $dynamicOrgUrl . '/school/' . $schoolSlug . '/student/soapNote.html?editsoapNoteId=' . EncodeQueryData($retSoapNote) . '&rotationId=' . EncodeQueryData($rotationId) . '&preceptorNum=' . EncodeQueryData($preceptorNo) . '&preceptorId=' . EncodeQueryData($preceptorId) . '&isSendToExternalPreceptor=1';

                    // Create TinyUrl
                    $randomUrl = getTinyUrl($URL);
                    $redirectUrl = $dynamicOrgUrl . '/redirect/' . EncodeQueryData($randomUrl);
                    // exit;
                    $body = 'Hello, You received a Student (' . $studentname . ') ' . $isEvalTypeLabel . ' Message request. Click the link below to complete. ' . $redirectUrl;

                    sendSMS('+' . $countryCode . $preceptorNo, $body);
                }
            }
           
            //Audit Log Start
            // Instantiate the Logger class
            $objLog = new clsLogger();
            $userType = $objLog::STUDENT; 

            if ($soapNoteId > 0) {
                if (!empty($clinicianSignatureDate) && !empty($studentSignatureDate)) {
                    // Student signed off 
                    $action = $objLog::SIGNOFF;
                } else {
                    //  edit
                    $action = $objLog::EDIT;
                }
            } else {
                // add
                $action = $objLog::ADD;
            }
            $objSoapNote->saveSoapNoteAuditLog($retSoapNote, $studentId, $userType, $action, $IsMobile);
            unset($objLog);

            // Audit Log End
        }
    } else {
        //   echo "hi1"; exit; 
        // preceptor signoff

        $soapNoteId = $soapNoteId;
        $objSoapNote->clinicianSignatureDate = $clinicianSignatureDate;
        $objSoapNote->UpdateSoapNoteByPreceptor($soapNoteId);

        //Audit Log Start
        // Instantiate the Logger class
        $objLog = new clsLogger();

        // Determine the action type (EDIT or ADD) based on the presence of a journal ID
        $action = $objLog::SIGNOFF;
        $userType = $objLog::PRECEPTOR; // User type is set to STUDENT
        $objDB = new clsDB();
        $preceptorId = $objDB->GetSingleColumnValueFromTable('preceptordetails', 'preceptorId', 'referenceId', $soapNoteId, 'type', 'soapnote');
        unset($objDB);
        $objSoapNote->saveSoapNoteAuditLog($soapNoteId, $preceptorId, $userType, $action, $IsMobile);

        unset($objLog);
        //Audit Log End

        
    }

    if ($isPreceptor) {
      
        if ($IsMobile) {
            header('location:' . BASE_PATH . '/webRedirect.html?status=Success&type=soapnote');
            exit;
        } else {
            header('location:thankyou.html?isType=Soap Note');
            exit();
        }
    } else if ($soapNoteId > 0) {
       
        if ($IsMobile) {
            header('location:' . BASE_PATH . '/webRedirect.html?status=Success&type=soapnote');
            exit;
        } else {
            header('location:soapNoteList.html?rotationId=' . EncodeQueryData($rotationId) . '&status=' . $status);
            exit();
        }
    } else {
       
        if ($IsMobile) {
            header('location:' . BASE_PATH . '/webRedirect.html?status=Success&type=soapnote');
            exit;
        } else {
            header('location:soapNoteList.html?rotationId=' . EncodeQueryData($rotationId) . '&status=' . $status);
            exit();
        }
    }
} else {
    
    if ($IsMobile) {
        header('location:' . BASE_PATH . '/webRedirect.html?status=Error&type=soapnote');
        exit();
    } else {
        header('location:soapNoteList.html');
        exit();
    }
}
