<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsSchool.php');
include('../class/clsCountryStateMaster.php');
include('../setRequest.php');
include('../class/clsSchoolSettigs.php');


$isSuperAdmin = $_SESSION['isSuperAdmin'];

$timeZoneId = '';
$contractPeriodId = '';
$billingId = '';
$schoolName = '';
$contactPerson = '';
$email = '';
$phone = '';
$address1 = '';
$address2 = '';
$image_name = '';
$defaultSchoolImagePath = '';
$city = '';
$zip = "";

$dbStateId = '';
$dbCountryId = '';
$displayName = "";
$contactEmail = "";
$coarcId = "";
$isdefaultFloorAndIcuEval = 0;
$isActiveCheckoff = 0;
$checkoffType = 0;
$scheduleActive = 0;
$studentChangeHospitalSite = 0;
$activitySheet = 0;
$procedureCount = 0;
$selfUnlock = 0;



//For Country List
$objLocation = new clsCountryStateMaster();
$countries = $objLocation->GetAllCountry();


if ($currentSchoolId > 0) //Edit Mode
{

    //For School Details
    $objSchool = new clsSchool();
    $row = $objSchool->GetSchoolDetails($currentSchoolId);
    $rowContarctPeriods = $objSchool->GetContarctPeriods();
    $rowBilling = $objSchool->GetBilling();
    $rowTimezone = $objSchool->GetTimezone();
    unset($objSchool);
    if ($row == '') {
        header('location:settings.html');
        exit;
    }


    $activitySheet = stripslashes($row['activitySheet']);
    $studentChangeHospitalSite = isset($row['studentChangeHospitalSite']) ? stripslashes($row['studentChangeHospitalSite']) : 0;
    $selfUnlock = isset($row['selfUnlock']) ? stripslashes($row['selfUnlock']) : 0;
    $checkoffType = stripslashes($row['checkoffType']);
    $scheduleActive = stripslashes($row['scheduleActive']);

    $objclsSchoolSettigs = new clsSchoolSettigs();
    $schoolSettings = $objclsSchoolSettigs->GetSchoolSetting($currentSchoolId);

    $schoolSettingsCount = ($schoolSettings != '') ? mysqli_num_rows($schoolSettings) : 0;

    $exportToPdf = 0;
    $chat = 0;
    $callOff = 0;
    $soapNote = 0;
    if ($schoolSettingsCount) {
        while ($rowSetting = mysqli_fetch_array($schoolSettings)) {
            $settingType = $rowSetting['type'];
            $status = $rowSetting['status'];
            if ($settingType == 'exportToPdf') {
                $exportToPdf = $status;
            } elseif ($settingType == 'procedureCount') {
                $procedureCount = $status;
            } elseif ($settingType == 'chat') {
                $chat = $status;
            } elseif ($settingType == 'callOff') {
                $callOff = $status;
            } elseif ($settingType == 'soapNote') {
                $soapNote = $status;
            }
            
        }
    }
}

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title>Additional Settings</title>
    <?php include('includes/headercss.php'); ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">

    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />


</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="settings.html">Settings</a></li>
                    <li class="active">Additional Settings</li>
                </ol>
            </div>

        </div>
    </div>

    <div class="container">

        <br />

        <form id="frmAdditionalSettings" data-parsley-validate class="form-horizontal" method="POST" action="additionalsettingsubmit.html" enctype="multipart/form-data">



            <div class="row">
                <div class="col-md-6">

                    <!-- Activity Sheet  Enabled/Disabled-->
                    <div class="form-group">
                        <label class="col-md-12 control-label px-0" style="padding-top: 0">Activity Sheet</label>
                        <input style="width: 27px; vertical-align: text-top;" type="radio" id="enable" isType="activitySheet" class="radioButtonClass disable" name="activitySheetActive" value="1" <?php if ($activitySheet == 1 && $activitySheet != '') echo 'checked'; ?>>Enable
                        <input style="width: 27px; vertical-align: text-top;" type="radio" id="disable" isType="activitySheet" class="radioButtonClass disable" name="activitySheetActive" value="0" <?php if ($activitySheet == 0 && $activitySheet != '') echo 'checked'; ?>> Disable
                    </div>

                </div>

                <div class="col-md-6">

                    <!-- Export to PDF  Enabled/Disabled-->
                    <div class="form-group">
                        <label class="col-md-12 control-label px-0" style="padding-top: 0">Export to PDF</label>
                        <input style="width: 27px; vertical-align: text-top;" type="radio" id="enable" isType="exportToPdf" class="radioButtonClass disable" name="exportToPdf" value="1" <?php if ($exportToPdf == 1 && $exportToPdf != '') echo 'checked'; ?>>Enable
                        <input style="width: 27px; vertical-align: text-top;" type="radio" id="disable" isType="exportToPdf" class="radioButtonClass disable" name="exportToPdf" value="0" <?php if ($exportToPdf == 0) echo 'checked'; ?>> Disable
                    </div>

                </div>
            </div>


            <div class="row">
                <div class="col-md-6">

                    <!-- Activity Sheet  Enabled/Disabled-->
                    <div class="form-group defaultCIEval">
                        <label class="col-md-12 control-label px-0" style="padding-top: 0">Schedule</label>
                        <input style="width: 27px; vertical-align: text-top;" type="radio" id="enable" class="scheduleActive radioButtonClass disable" isType="schedule" name="scheduleActive" value="1" <?php if ($scheduleActive == 1 && $scheduleActive != '') echo 'checked'; ?>>Enable
                        <input style="width: 27px; vertical-align: text-top;" type="radio" id="disable" class="scheduleActive radioButtonClass disable" isType="schedule" name="scheduleActive" value="0" <?php if ($scheduleActive == 0 && $scheduleActive != '') echo 'checked'; ?>> Disable
                    </div>

                </div>
                <div class="col-md-6">

                    <!-- Activity Sheet  Enabled/Disabled-->
                    <div class="form-group defaultCIEval studentChangeHospitalSite">
                        <label class="col-md-12 control-label px-0" style="padding-top: 0">Change Hospital Site</label>
                        <input style="width: 27px; vertical-align: text-top;" type="radio" class="radioButtonClass disable" isType="hospitalsite" id="enable" name="studentChangeHospitalSite" value="1" <?php if ($studentChangeHospitalSite == 1 && $studentChangeHospitalSite != '') echo 'checked'; ?>>Enable
                        <input style="width: 27px; vertical-align: text-top;" type="radio" class="radioButtonClass disable" isType="hospitalsite" id="disable" name="studentChangeHospitalSite" value="0" <?php if ($studentChangeHospitalSite == 0 && $studentChangeHospitalSite != '') echo 'checked'; ?>> Disable
                    </div>

                </div>
            </div>
            <div class="row">
                <?php if ($isSuperAdmin == 1) {
                    if ($isActiveCheckoff == 1) { ?>
                        <div class="col-md-6">

                            <!-- Procedure Count Enabled/Disabled-->
                            <div class="form-group">
                                <label class="col-md-12 control-label px-0" style="padding-top: 0">Procedure Count By Course Topic</label>
                                <input style="width: 27px; vertical-align: text-top;" type="radio" id="enable" isType="procedureCount" class="radioButtonClass disable" name="procedureCount" value="1" <?php if ($procedureCount == 1 && $procedureCount != '') echo 'checked'; ?>>Enable
                                <input style="width: 27px; vertical-align: text-top;" type="radio" id="disable" isType="procedureCount" class="radioButtonClass disable" name="procedureCount" value="0" <?php if ($procedureCount == 0) echo 'checked'; ?>> Disable
                            </div>
                        </div>
                <?php }
                } ?>
                <div class="col-md-6">
                    <!-- Chat  Enabled/Disabled-->
                    <div class="form-group">
                        <label class="col-md-12 control-label px-0" style="padding-top: 0">Chat</label>
                        <input style="width: 27px; vertical-align: text-top;" type="radio" id="enable" isType="chat" class="radioButtonClass disable" name="chat" value="1" <?php if ($chat == 1 && $chat != '') echo 'checked'; ?>>Enable
                        <input style="width: 27px; vertical-align: text-top;" type="radio" id="disable" isType="chat" class="radioButtonClass disable" name="chat" value="0" <?php if ($chat == 0) echo 'checked'; ?>> Disable
                    </div>

                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <!-- Self Unlock for Student  Enabled/Disabled-->
                    <div class="form-group ">
                        <label class="col-md-12 control-label px-0" style="padding-top: 0">Self Unlock for Student</label>
                        <input style="width: 27px; vertical-align: text-top;" type="radio" class="radioButtonClass disable" isType="selfunlock" id="enable" name="selfunlock" value="1" <?php if ($selfUnlock == 1 && $selfUnlock != '') echo 'checked'; ?>>Enable
                        <input style="width: 27px; vertical-align: text-top;" type="radio" class="radioButtonClass disable" isType="selfunlock" id="disable" name="selfunlock" value="0" <?php if ($selfUnlock == 0 && $selfUnlock != '') echo 'checked'; ?>> Disable
                    </div>

                </div>
                <div class="col-md-6">
                    <!-- Checkoff Type Enabled/Disabled-->
                    <?php if ($isActiveCheckoff == 1) { ?>
                        <div class="form-group defaultCIEval">
                            <label class="col-md-12 control-label px-0" style="padding-top: 0">Checkoff Type</label>
                            <input style="width: 27px; vertical-align: text-top;" type="radio" class="radioButtonClass disable" isType="checkofftype" id="checkoffTypeBoth" name="checkoffType" value="0" <?php if ($checkoffType == 0 && $checkoffType != '') echo 'checked'; ?>> Both
                            <input style="width: 27px; vertical-align: text-top;" type="radio" class="radioButtonClass disable" isType="checkofftype" id="checkoffTypeA" name="checkoffType" value="1" <?php if ($checkoffType == 1 && $checkoffType != '') echo 'checked'; ?>>A
                            <input style="width: 27px; vertical-align: text-top;" type="radio" class="radioButtonClass disable" isType="checkofftype" id="checkoffTypeAA" name="checkoffType" value="2" <?php if ($checkoffType == 2 && $checkoffType != '') echo 'checked'; ?>>AA
                            <?php if ($currentSchoolId == 127) { ?>
                                <input style="width: 27px; vertical-align: text-top;" type="radio" id="checkoffTypeMupa" name="checkoffType" value="3" <?php if ($checkoffType == 3 && $checkoffType != '') echo 'checked'; ?>> Mansfield
                            <?php } ?>

                            <!-- <input style="width: 27px; vertical-align: text-top;" type="radio" id="pacrMap1BPNormal" name="visi1Check" value="0" > normal<input style="width: 27px; vertical-align: text-top;" type="radio" id="pacrMap1BPAbnormal" name="visi1Check" value="1" <?php if ($visi1Check == 1) echo 'checked'; ?>>abnormal -->

                        </div>
                    <?php } ?>

                </div>

            </div>
            <div class="row">
                <div class="col-md-6">
                    <!-- Self Unlock for Student  Enabled/Disabled-->
                    <div class="form-group ">
                        <label class="col-md-12 control-label px-0" style="padding-top: 0">Call Off</label>
                        <input style="width: 27px; vertical-align: text-top;" type="radio" class="radioButtonClass disable" isType="callOff" id="enable" name="callOff" value="1" <?php if ($callOff == 1 && $callOff != '') echo 'checked'; ?>>Enable
                        <input style="width: 27px; vertical-align: text-top;" type="radio" class="radioButtonClass disable" isType="callOff" id="disable" name="callOff" value="0" <?php if ($callOff == 0 && $callOff != '') echo 'checked'; ?>> Disable
                    </div>

                </div>
                <div class="col-md-6">

                    <!-- soap note  Enabled/Disabled-->
                    <div class="form-group">
                        <label class="col-md-12 control-label px-0" style="padding-top: 0">Soap Note</label>
                       <input style="width: 27px; vertical-align: text-top;" type="radio" class="radioButtonClass disable" isType="soapNote" id="enable" name="soapNote" value="1" <?php if ($soapNote == 1 && $soapNote != '') echo 'checked'; ?>>Enable
                        <input style="width: 27px; vertical-align: text-top;" type="radio" class="radioButtonClass disable" isType="soapNote" id="disable" name="soapNote" value="0" <?php if ($soapNote == 0 && $soapNote != '') echo 'checked'; ?>> Disable
                    </div>

                </div>
            </div>
            <br />
            <!-- <div class="form-group">
                <label class="col-md-2 control-label"></label>
                <div class="col-md-10">

                    <button id="btnSubmit" class="btn btn-success" name="btnSubmit">Save</button>

                    <a type="button" href="settings.html" class="btn btn-default">Cancel</a>
                </div>
            </div> -->

        </form>


    </div>

    <?php include('includes/footer.php'); ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/jquery.cascadingdropdown.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/inputmask/jquery.inputmask.bundle.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/inputmask/inputmask.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>

    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";
        $(window).load(function() {


            $('.image-preview').magnificPopup({
                type: 'image'
            });

            $('#frmAdditionalSettings').parsley().on('field:validated', function() {
                    var ok = $('.parsley-error').length === 0;
                })
                .on('form:submit', function() {
                    ShowProgressAnimation();
                    return true; // Don't submit form for this demo
                });

            var isSuperAdmin = '<?php echo $isSuperAdmin; ?>';
            if (isSuperAdmin == 0) {
                // console.log('hi');
                $('.disable').prop('disabled', true);

            }

            studentChangeHospitalSite();

        });

        $(document).on('click', '.scheduleActive', function() {
            studentChangeHospitalSite();
        });

        function studentChangeHospitalSite() {
            var radioBtnVal = $('input[name="scheduleActive"]:checked').val();
            // console.log('radioBtnVal' + radioBtnVal);
            if (radioBtnVal == 1) {
                $('.studentChangeHospitalSite').show();
            } else {
                $('.studentChangeHospitalSite').hide();
            }
        }
        $('.radioButtonClass').change(function() {
            // Get the value of the selected radio button
            var selectedValue = $(this).val();
            var isType = $(this).attr('isType');
            var schoolId = '<?php echo $currentSchoolId; ?>';
            // Do something with the selected value
            // console.log("Selected value: " + selectedValue);
            // console.log("Selected isType: " + isType);
            $.ajax({
                type: "post",
                url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_updateSchoolSettings.html",
                data: {
                    schoolId: schoolId,
                    type: isType,
                    value: selectedValue

                },
                success: function(data) {
                    // console.log(data);
                    // current_datatable.row(current_datatable_row).remove().draw(false);
                    if (data = 'success')
                        alertify.success('Updated');
                    else
                        alertify.error('Not Updated');
                }
            });
        });
    </script>

</body>

</html>