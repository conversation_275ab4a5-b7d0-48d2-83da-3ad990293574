<?php
// ini_set('display_errors', 1);
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsSchool.php');
include('../class/clsStudent.php');
include('../class/clsJournal.php');
include('../class/clsCIevaluation.php');
include('../class/clsPEvaluation.php');
include('../class/clsIrr.php');
include('../class/clscheckoff.php');
include('../class/clsInteraction.php');
include('../class/clsSiteevaluation.php');
include('../class/clsFormative.php');
include('../class/clsEquipment.php');
include('../class/clsSummative.php');
include('../class/clsAttendance.php');
include('../class/clsMidterm.php');
include('../class/clsDaily.php');
include('../class/clsProcedureCount.php');
include('../class/clsSemester.php');
include('../class/clsRotation.php');
include('../class/clsStudentRankMaster.php');
include('../class/clsMasteryEval.php');
include('../class/clsHospitalSite.php');
include('../class/clsPEF.php');
include('../class/clsClinician.php');
include('../class/clsCaseStudy.php');
include('../class/clsSoapNote.php');
include('../class/clsExternalPreceptors.php');


include('../setRequest.php');
// print_r($_SESSION);
$schoolId = 0;
$rotationId = 0;
$individual_student = 0;
$student_rank = 0;
$evaluator = 0;
$school_location = 0;
$hospital_site = 0;
$checkoffTopic = '';
$transchooldisplayName = '';
$subcborotation = 0;
$rotationName = 0;
$mainRotationName = 0;
$TimeZone = $_SESSION["loggedClinicianSchoolTimeZone"];
$isActiveCheckoff = $_SESSION["isActiveCheckoff"];
$title = "Report " . $transchooldisplayName;
$cbosemester = 0;
$totalPEFIdetailsCount = 0;
$totalPEFIIdetailsCount = 0;
$clinicianId = 0;
// print_r($_POST);
if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit'])) {
   $objRotation = new clsRotation();

   $reportType = $_POST['cboreporttype'];
   $rotationId = isset($_POST['cborotation']) ? $_POST['cborotation'] : '';

   if (is_array($rotationId)) // For Multiple Rotation
      $rotationId = implode(" ", $rotationId);

   $skill = $_POST['cboskill'];
   $student_rank = $_POST['cbostudentrank'];
   $school_location = $_POST['cbolocation'];
   $evaluator = isset($_POST['cboevaluator']) ? $_POST['cboevaluator'] : '';
   $hospital_site = isset($_POST['cbohospitalsite']) ? $_POST['cbohospitalsite'] : '';
   $selTopicId = isset($_POST['cbohospitalsiteunits']) ? $_POST['cbohospitalsiteunits'] : '';

   if ($reportType == 'CI_Eval' || $reportType == 'Site_Eval') {
      //Clinical Instructor
      $evaluator = isset($_POST['cboevaluator']) ? $_POST['cboevaluator'] : '';
      $evaluator = $evaluator ? implode(" ", $evaluator) : '';

      //Hospital site
      $hospital_site = isset($_POST['cbohospitalsite']) ? $_POST['cbohospitalsite'] : '';
      $hospital_site = $hospital_site ? implode(" ", $hospital_site) : '';

      //Clinical Site Unit
      $selTopicId = isset($_POST['cbohospitalsiteunits']) ? $_POST['cbohospitalsiteunits'] : '';
      $selTopicId = $selTopicId ? implode(" ", $selTopicId) : '';
   }

   $rotation_hidden = $_POST['rotation_hidden'];
   $location_hidden = $_POST['location_hidden'];
   $courseTopicCompletion = isset($_POST['courseTopicCompletion']) ? $_POST['courseTopicCompletion'] : '';

   $checkoffTopic = $_POST['checkoffTopic'];
   if (isset($_POST['cbosemester'])) {
      $cbosemester = $_POST['cbosemester'];
      $cbosemester = implode(" ", $cbosemester);
   }

   if (isset($_POST['subcborotation'])) {
      $subcborotation = $_POST['subcborotation'];
      $subcborotation = implode(" ", $subcborotation);
   } else {
      if ($rotationId) {
         $subRotations = [];
         $rowsRotations = $objRotation->GetAllSubRotation($currentSchoolId, 0, 0, $rotationId);
         if ($rowsRotations != '') {
            $totalRotations = mysqli_num_rows($rowsRotations);
         }
         if ($totalRotations > 0) {
            while ($row = mysqli_fetch_array($rowsRotations)) {
               $subRotationId = $row['rotationId'];
               $subRotations[] = $subRotationId;
            }
         }

         if (count($subRotations)) {
            $subcborotation = implode(" ", $subRotations);
         }
      }
   }

   $individual_student = '';
   $individualStudentArray = '';
   $individualStudentArrayToPost = '';
   if (isset($_POST['cboindividualstudent'])) {
      $individual_student = $_POST['cboindividualstudent'];
      $individualStudentArray = implode(',', $individual_student);
      $individualStudentArrayToPost = serialize($individual_student);
   }

   $AscDesc = $_POST['AscDesc'];
   if ($AscDesc == 0)
      $AscDesc = 'ASC';
   else
      $AscDesc = 'DESC';

   $sordorder = $_POST['sordorder'];
   $startDate = $_POST['startDate'];
   $startDate = ($startDate) ? date('Y-m-d', strtotime(str_replace('-', '/', $startDate))) : "";
   $endDate = $_POST['endDate'];
   $endDate = ($endDate) ? date('Y-m-d', strtotime(str_replace('-', '/', $endDate))) : "";

   $objSemester = new clsSemester();
   $semesterName = $objSemester->GetBySemesterId($cbosemester);
   unset($objSemester);


   $rotationName = $objRotation->GetRotationNameById($subcborotation);
   $mainRotationName = $objRotation->GetRotationNameById($rotationId);
   // unset($objRotation);

   //For Student Name
   $objStudent = new clsStudent();
   $studentFullNamesArray = array();
   $studentIdsArray = array();

   if ($individualStudentArray) {
      foreach ($individual_student as $key => $student) {
         $studentDetails = $objStudent->GetUserDetail($student);
         $studentfirstName = $studentDetails['firstName'];
         $studentLastName = $studentDetails['lastName'];
         $studentFullnameArray = $studentfirstName . ' ' . $studentLastName;
         $studentFullNamesArray[] = $studentFullnameArray;
         $studentIdsArray[] = $studentDetails['studentId'];
      }
   }

   $studentFullname = '';
   if (count($studentFullNamesArray))
      $studentFullname =  implode(",", $studentFullNamesArray);

   $studentIdArr = '';
   if (count($studentIdsArray))
      $studentIdArr =  implode(",", $studentIdsArray);

   // unset($objRotation);

   //For Rank Name
   $objStudentRank = new clsStudentRankMaster();
   $rankName = $objStudentRank->GetRankbyId($student_rank);
   unset($objStudentRank);

   //For hospital side name
   $objHospitalSite = new clsHospitalSite();
   $hospitalSideDetail = $objHospitalSite->GetHospitalSiteDetails($hospital_site);
   $hospitalSideName = isset($hospitalSideDetail['title']) ? $hospitalSideDetail['title'] : '';
   unset($objHospitalSite);

   //For Clinician Name
   $evaluatorName = '';
   if ($evaluator > 0) {
      $objClinician = new clsClinician();
      $clinicianDetails = $objClinician->GetClinicianDetails($evaluator);
      $clinicianFName = isset($clinicianDetails['firstName']) ? $clinicianDetails['firstName'] : '';
      $clinicianLName = isset($clinicianDetails['lastName']) ? $clinicianDetails['lastName'] : '';
      $evaluatorName = $clinicianFName . ' ' . $clinicianLName;
      unset($objClinician);
   }
} else {
   if (isset($_GET['reportType'])) {
      $reportType = $_GET['reportType'];
      $rotationId = '';
      $individual_student = '';
      $skill = '';
      $student_rank = '';
      $startDate = '';
      $endDate = '';
      $selTopicId = '';
      $AscDesc = '';
      $sordorder = '';
   }
}

$courseId = isset($_POST['cboCourse']) ? $_POST['cboCourse'] : 0;
$objDB = new clsDB();
$courseTitle = $objDB->GetSingleColumnValueFromTable('courses', 'title', 'courseId', $courseId);
unset($objDB);
?>
<!DOCTYPE html>
<html lang="en">

<head>
   <meta charset="utf-8">
   <meta http-equiv="X-UA-Compatible" content="IE=edge">
   <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
   <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
   <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
   <title><?php echo ($title); ?></title>
   <?php include('includes/headercss.php'); ?>
   <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
   <?php include("includes/datatablecss.php") ?>
   <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
   <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
   <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/css/tableStyle.css" />
   <link href="https://cdn.datatables.net/fixedcolumns/3.2.4/css/fixedColumns.bootstrap4.min.css" rel="stylesheet" />
   <style>
      .bar {
         background-color: #819FF7;
         width: 0%;
         height: 10px;
         border-radius: 3px;
      }

      .percent {
         position: absolute;
         display: inline-block;
         top: 3px;
         left: 48%;
      }

      #wrapper {
         width: 995px;
         padding: 0px;
         margin: 0px auto;
         font-family: helvetica;
         text-align: center;
      }

      h1 {
         text-align: center;
         font-size: 35px;
         margin-top: 60px;
         color: #A9BCF5;
      }

      h1 p {
         text-align: center;
         margin: 0px;
         font-size: 18px;
         text-decoration: underline;
         color: grey;
      }

      .tableTitle {
         text-align: center !important;
         background-color: #fff !important;
         color: #000 !important;
      }

      .btn-group {
         display: flex;
         justify-content: end;
         margin-bottom: 10px;
      }
   </style>
</head>

<body>
   <?php if ($reportType != 'Absence') { ?>
      <div id="loading-div-background" style="display: block;">
         <div id="loading-div" class="ui-corner-all">
            <img style="height:31px;width:31px;margin:30px;" src="<?php echo ($dynamicOrgUrl); ?>/assets/images/loader.gif" alt="Loading.."><br>PROCESSING. PLEASE WAIT...

            <div class='progress' id="progress_div" style="height: 10px;margin: 8px;">
               <div class='bar' id='bar1'></div>
               <div class='percent' id='percent1'></div>
            </div>
         </div>
      </div>
   <?php } ?>


   <input type="hidden" id="progress_width" value="0">

   <?php include('includes/header.php'); ?>
   <div class="row margin_zero breadcrumb-bg">
      <div class="container">
         <div class="pull-left">
            <ol class="breadcrumb">
               <li><a href="dashboard.html">Home</a></li>
               <?php if ($reportType == 'Attendance') { ?>
                  <li><a href="reports.html">Reports Attendance Detail</a></li>
               <?php } else { ?>
                  <li><a href="reports.html">Report</a></li>
               <?php } ?>
            </ol>
         </div>
         <div class="pull-right">
            <!--a href="exportreport.html?Type=currentSchool&currentSchoolId=<?php //echo($currentSchoolId); 
                                                                              ?>" onClick="return confirm('Are you sure you want to Export?');" >Export to Excel</a-->
            <form id="frmexportreport" data-parsley-validate class="form-horizontal" method="POST" action="exportreport.html" enctype="multipart/form-data">
               <input type="hidden" name="cboreporttype" value="<?php echo ($reportType); ?>">
               <input type="hidden" name="cborotation" value="<?php echo ($rotationId); ?>">
               <input type="hidden" name="cboindividualstudent" value='<?php echo $individualStudentArrayToPost; ?>'>
               <input type="hidden" name="cboskill" value="<?php echo ($skill); ?>">
               <input type="hidden" name="cbostudentrank" value="<?php echo ($student_rank); ?>">
               <input type="hidden" name="cbolocation" value="<?php echo ($school_location); ?>">
               <input type="hidden" name="cboevaluator" value="<?php echo ($evaluator); ?>">
               <input type="hidden" name="cbohospitalsite" value="<?php echo ($hospital_site); ?>">
               <input type="hidden" name="startDate" value="<?php echo ($startDate); ?>">
               <input type="hidden" name="endDate" value="<?php echo ($endDate); ?>">
               <input type="hidden" name="selTopicId" value="<?php echo ($selTopicId); ?>">
               <input type="hidden" name="AscDesc" value="<?php echo ($AscDesc); ?>">
               <input type="hidden" name="sordorder" value="<?php echo ($sordorder); ?>">
               <input type="hidden" name="checkoffTopic" value="<?php echo ($checkoffTopic); ?>">
               <input type="hidden" name="cbosemester" value="<?php echo ($cbosemester); ?>">
               <input type="hidden" name="subcborotation" value="<?php echo ($subcborotation); ?>">
               <input type="hidden" name="courseTopicCompletion" value="<?php echo ($courseTopicCompletion); ?>">
               <!--input  style="display:none" type="submit" name="btnExport" id="btnExport" class="btn btn-link" value="Export to Excel"-->
               <input style="display:none" type="submit" name="btnExport" id="btnExport" class="btn btn-link" value="Export to Excel">
            </form>
         </div>
      </div>
   </div>
   <div class="container">
      <div id="divTopLoading">Loading...</div>
      <?php switch ($reportType) {
         case "Absence":
            echo "<b>Absence</b><br>";
      ?>
            <div class="container-fluid">
               <div class="row">
                  <div class="form-group">
                     <?php if ($startDate && $endDate) { ?>
                        <div class="col-sm-4"><b>Start Date: </b><?php echo date('m/d/Y', strtotime($startDate)); ?> <br> <b>End Date: </b><?php echo date('m/d/Y', strtotime($endDate)); ?><br /> <?php if ($mainRotationName != '') { ?> <b>Rotation: </b><?php echo ($mainRotationName); ?> <?php } ?>
                        </div>
                     <?php } elseif ($startDate) { ?>
                        <div class="col-sm-3"><b>Start Date: </b><?php echo ($startDate); ?></div>
                     <?php } elseif ($endDate) { ?>
                        <div class="col-sm-3"><b>End Date: </b><?php echo ($endDate); ?></div>
                     <?php } elseif ($mainRotationName != '') { ?>
                        <!--<div class="col-sm-3" ><b>Rotation: </b><?php echo ($mainRotationName); ?></div>-->
                     <?php } elseif ($subcborotation != '') { ?>
                        <div class="col-sm-3"><b>Hospital Site: </b><?php echo ($rotationName); ?> </div>
                     <?php } elseif ($location_hidden) { ?>
                        <div class="col-sm-3"><b>Location: </b><?php echo ($location_hidden); ?></div>
                     <?php }  ?>
                     <?php if ($individual_student) { ?>
                        <div class="col-sm-3"><b>Student Name: </b><?php echo ($studentFullname); ?></div>
                     <?php }  ?>
                  </div>
               </div>
            </div>
            <br>
            <?php
            $objAttendance = new clsAttendance();
            $totalAttendance = 0;
            if (
               isset($_POST['cboreporttype']) || ($_POST['cborotation']) || ($_POST['cboindividualstudent'])
               || ($_POST['cbostudentrank']) || ($_POST['cboevaluator']) || ($_POST['cbolocation'])
               || ($_POST['cbohospitalsite']) || ($_POST['startDate']) || ($_POST['endDate'])
            ) {
               $currentDateTime = date('Y-m-d H:i:s');
               $currentDateTime = converFromServerTimeZone($currentDateTime, $TimeZone);
               // echo $sordorder;
               $assignedRotations = $objRotation->GetAllAssignedRotations($currentSchoolId, 1, $individualStudentArrayToPost, $rotationId, $subcborotation, $sordorder, $AscDesc);
               $totalAssignedRotations = ($assignedRotations != '') ? mysqli_num_rows($assignedRotations) : 0;
               $absenceArray = [];
               if ($totalAssignedRotations > 0) {
                  while ($row = mysqli_fetch_array($assignedRotations)) {
                     $rotationId = $row['rotationId'];
                     $students = $row['students'];
                     $rotationTitle = stripslashes($row['title']);

                     $rotationStartDate = date('Y-m-d', strtotime($row['startDate']));
                     $rotationEndDate = date('Y-m-d', strtotime($row['endDate']));
                     $repeatDays = $objRotation->GetRotationRepeatDaysBYRotation($rotationId);
                     $repeatDays = explode(",", $repeatDays);

                     $repeatDayUpdatedArray = [];
                     foreach ($repeatDays as $repeatDay) {
                        if ($repeatDay == 0)
                           $repeatDayUpdatedArray[] = 6;
                        elseif ($repeatDay == 1)
                           $repeatDayUpdatedArray[] = 0;
                        elseif ($repeatDay == 2)
                           $repeatDayUpdatedArray[] = 1;
                        elseif ($repeatDay == 3)
                           $repeatDayUpdatedArray[] = 2;
                        elseif ($repeatDay == 4)
                           $repeatDayUpdatedArray[] = 3;
                        elseif ($repeatDay == 5)
                           $repeatDayUpdatedArray[] = 4;
                        elseif ($repeatDay == 6)
                           $repeatDayUpdatedArray[] = 5;
                     }

                     $repeatDayUpdated = implode(",", $repeatDayUpdatedArray);

                     $currentDate = date('Y-m-d', strtotime($currentDateTime));
                     $startTime = date('H:i:s', strtotime($row['startDate']));

                     $rotationDay = $objRotation->GetAllDaysBetweenRotationStartAndEndDate($rotationStartDate, $rotationEndDate, $repeatDayUpdated, $currentDate, 0, $startDate, $endDate);
                     $totalRotationDay = ($rotationDay != '') ? mysqli_num_rows($rotationDay) : 0;

                     if ($totalRotationDay > 0) {
                        while ($rotationRow = mysqli_fetch_array($rotationDay)) {
                           $rotationDate = $rotationRow['Date'];
                           if ($rotationDate == $currentDate) {
                              $currentTime = date('H:i:s', strtotime($currentDateTime));
                              $currentTime = strtotime($currentTime);
                              $startTime = strtotime($startTime);
                              if ($currentTime < $startTime)
                                 continue;
                           }

                           //Student List
                           $studentsArray = explode(",", $students);
                           foreach ($studentsArray as $student) {
                              $studentArray = explode("-", $student);
                              $studentId = isset($studentArray[0]) ? $studentArray[0] : '';
                              $firstName = isset($studentArray[1]) ? stripslashes($studentArray[1]) : '';
                              $lastName = isset($studentArray[2]) ? stripslashes($studentArray[2]) : '';
                              $ranktitle = isset($studentArray[3]) ? stripslashes($studentArray[3]) : '';

                              $isStudenAbsenceExist = $objAttendance->GetAttendanceDetail($studentId, $rotationId, $rotationDate);
                              if (!$isStudenAbsenceExist) {
                                 $absence = [];
                                 $absence['rotationDate'] = $rotationDate;
                                 $absence['firstName'] = $firstName;
                                 $absence['lastName'] = $lastName;
                                 $absence['ranktitle'] = $ranktitle;
                                 $absence['rotationTitle'] = $rotationTitle;
                                 $absenceArray[] = $absence;
                              }
                           }
                        }
                     }
                  }
               }

               // Comparison function
               function date_compare($element1, $element2)
               {
                  $datetime1 = strtotime($element1['rotationDate']);
                  $datetime2 = strtotime($element2['rotationDate']);
                  return $datetime2 - $datetime1;
               }

               // Sort the array 
               usort($absenceArray, 'date_compare');

               // $RowAbsence = $objAttendance->GetStudentAbsenceDetailsForreport
               // ($currentSchoolId,$rotationId=0,$individual_student,$rankId=0,$hospitalSiteId=0);
            }


            if (count($absenceArray) == 0) {
               echo "<b>Student Not Found.</b>";
               exit;
            }

            unset($objAttendance);
            ?>
            <table id="absence-datatable" class="table table-bordered dt-responsive nowrap table-hover responsive-datatable" cellspacing="0" width="100%">
               <thead>
                  <tr>
                     <th>First Name</th>
                     <th>Last Name</th>
                     <th>Ranking</th>
                     <th>Rotation</th>
                     <th>Site</th>
                     <th>Date</th>
                     <th>Original</th>
                     <th>Adjusted</th>
                     <th>Reason</th>
                  </tr>

               </thead>
               <tbody>
                  <?php
                  $TotatlHoursorignalhours = 0;
                  $TotatlHoursapprovedhours = 0;
                  if (count($absenceArray)) {
                     foreach ($absenceArray as $absence) {
                        $rotationDate = $absence['rotationDate'];
                        $firstName = $absence['firstName'];
                        $lastName = $absence['lastName'];
                        $ranktitle = $absence['ranktitle'];
                        $rotationTitle = $absence['rotationTitle'];

                  ?>
                        <tr>
                           <td><?php echo $firstName; ?></td>
                           <td><?php echo $lastName; ?></td>
                           <td><?php echo $ranktitle; ?></td>
                           <td><?php echo $rotationTitle; ?></td>
                           <td></td>
                           <td><?php echo date('m/d/Y', strtotime($rotationDate));  ?></td>
                           <td></td>
                           <td></td>
                           <td><?php echo "Absence"; ?></td>
                        </tr>
                  <?php }
                  } ?>

               </tbody>
            </table>
         <?php
            break;
         case "Attendance":
            echo "<b>Attendance Detail</b><br>";
         ?>
            <div class="container-fluid">
               <div class="row">
                  <div class="form-group">
                     <?php if ($startDate && $endDate) { ?>
                        <div class="col-sm-5"><b>Start Date: </b><?php echo date('m/d/Y', strtotime($startDate)); ?> <br><b>End Date: </b><?php echo date('m/d/Y', strtotime($endDate)); ?> <?php if ($rotationId) { ?><br /> <b>Rotation: </b><?php echo ($mainRotationName); ?> <?php } ?></div>
                     <?php } elseif ($startDate) { ?>
                        <div class="col-sm-3"><b>Start Date: </b><?php echo ($startDate); ?></div>
                     <?php } elseif ($endDate) { ?>
                        <div class="col-sm-3"><b>End Date: </b><?php echo ($endDate); ?></div>
                     <?php } elseif ($rotationId) { ?>
                        <div class="col-sm-3"><b>Rotation: </b><?php echo ($mainRotationName); ?></div>
                     <?php } elseif ($location_hidden) { ?>
                        <div class="col-sm-3"><b>Location: </b><?php echo ($location_hidden); ?></div>
                     <?php }  ?>
                     <?php if ($cbosemester != '') { ?>
                        <div class="col-sm-4"><b>Semester Name: </b><?php echo ($semesterName); ?> </div>
                     <?php } ?>
                     <?php if ($subcborotation != '') { ?>
                        <div class="col-sm-5"><b>Hospital Site: </b><?php echo ($rotationName); ?> </div>
                     <?php } ?>
                     <?php if ($individual_student) { ?>
                        <div class="col-sm-4"><b>Student Name: </b><?php echo ($studentFullname); ?></div>
                     <?php }  ?>
                     <?php if ($student_rank) { ?>
                        <div class="col-sm-3"><b>Rank Name: </b><?php echo ($rankName); ?></div>
                     <?php }  ?>
                  </div>
               </div>
               <?php
               $objAttendance = new clsAttendance();
               $totalAttendance = 0;
               if (
                  isset($_POST['cboreporttype']) || ($_POST['cborotation']) || ($_POST['cboindividualstudent'])
                  || ($_POST['cbostudentrank']) || ($_POST['cboevaluator']) || ($_POST['cbolocation'])
                  || ($_POST['cbohospitalsite']) || ($_POST['startDate']) || ($_POST['endDate'])
               ) {
                  $evaluator = $loggedClinicianId;
                  $rowsAttendance = $objAttendance->GetStudentAttendanceDetailsForClinicianreport($currentSchoolId, $rotationId, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder, $cbosemester, $subcborotation);

                  $rowsTotalAttendanceHour = $objAttendance->GetStudentTotslAttendanceHoursForReport($currentSchoolId, $rotationId, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder, $cbosemester, $subcborotation);
               }

               $totalAttendanceHours = 0;
               if ($rowsTotalAttendanceHour != '') {
                  $totalAttendanceHours = mysqli_num_rows($rowsTotalAttendanceHour);
               }

               if ($rowsAttendance != '') {
                  $totalAttendance = mysqli_num_rows($rowsAttendance);
               } else {
                  echo "<b>Student Not Found.</b>";
                  exit;
               }

               ?>
               <div class="row">
                  <div class="col">
                     <table id="attendanceTable" class="table table-striped table-bordered dt-responsive nowrap " cellspacing="0" width="100%">
                        <thead>
                           <tr>
                              <th>First Name</th>
                              <th>Last Name</th>
                              <th>Ranking</th>
                              <th>Rotation</th>
                              <th>Clock In Time</th>
                              <th>Clock Out Time</th>
                              <th>Exception</th>
                              <th>Reason</th>
                              <th>Approved</th>
                              <th>Approved By</th>
                              <th>Notes</th>
                              <th>Original Hours</th>
                              <th>Approved Hours</th>
                           </tr>
                        </thead>
                        <tbody>
                           <?php
                           $Totalorignalhours = 0;
                           $Totalapprovedhours = 0;
                           $Totalminutesapprovedhours = 0;
                           $Totalminutesorignalhours = 0;
                           $totalorignalminstohours = 0;
                           $totalorignalmins = 0;
                           $Totalorignalhours = 0;
                           $totalapprovedminstohours = 0;
                           $totalapprovedmins = 0;
                           $Totalapprovedhours = 0;
                           $arrays = array();
                           if ($totalAttendance > 0) {
                              while ($row = mysqli_fetch_array($rowsAttendance)) {
                                 $arrays[] = $row;
                              }
                           }

                           /* $studentCounts = array_count_values(array_column($arrays, 'studentId')); */
                           $studentCounts = array();
                           $counter = 1;
                           $arrayCount = 0;
                           $previous = 0;
                           foreach ($arrays as $row) {

                              foreach ($studentCounts as $key => $value) {
                                 if ($key == $row['studentId']) {
                                    $arrayCount = $value;
                                 }
                              }
                              if ($counter == 1) {
                                 $previousRotationId = $row['rotationId'];
                                 $previous = $row['studentId'];
                              }

                              $approvedhours = '';
                              $studentId = '';
                              $studentId = $row[0];
                              $school = stripslashes($row['school']);
                              $attendanceId = stripslashes($row['attendanceId']);
                              $firstName = stripslashes($row['firstName']);
                              $lastName = stripslashes($row['lastName']);
                              $rank = stripslashes($row['rank']);
                              $clockInDateTime = stripslashes($row['clockInDateTime']);
                              $clockInDateTime = converFromServerTimeZone($clockInDateTime, $TimeZone);
                              $clockOutDateTime = stripslashes($row['clockOutDateTime']);
                              $clockOutDateTime = converFromServerTimeZone($clockOutDateTime, $TimeZone);
                              $rotationname = stripslashes($row['title']);
                              $reason = stripslashes($row['comment']);
                              $notes = stripslashes($row['notes']);
                              $isException = stripslashes($row['isException']);
                              $approvedhours1 = stripslashes($row['approvedhours']);
                              if ($approvedhours1 != '') {
                                 $approvedhours1 = $approvedhours1;
                              } else {
                                 $approvedhours1 = '00:00';
                              }
                              if ($isException == 1) {
                                 $isException = 'Yes';
                              } else {
                                 $isException = 'No';
                              }

                              $approved = stripslashes($row['status']);
                              if ($approved == 1) {
                                 $approved = 'Yes';
                                 $approvedhours = stripslashes($row['approvedhours']);
                                 $approvedby = stripslashes($row['adminname']);
                              } else {
                                 $approved = 'No';
                                 $approvedby = '';
                              }
                              $GetAttendance = '';
                              $ApprovedTotalHours = '-';
                              $GetAttendance = $objAttendance->GetAttendanceTotal($studentId);
                              if ($GetAttendance !== null) {
                                 $ApprovedTotalHours = $GetAttendance['ApprovedTotalHours'];
                              }
                              if ($ApprovedTotalHours != '') {
                                 $ApprovedTotalHours = explode(":", $ApprovedTotalHours);
                                 $approvedhours = isset($ApprovedTotalHours[0]) . ":" . isset($ApprovedTotalHours[1]);
                              }


                              $orignalhours = stripslashes($row['orignalhours']);

                              //For original hours

                              $splitorignalhours = explode(":", $orignalhours);
                              // print_r()
                              $orignalcount = ($splitorignalhours != '') ? count($splitorignalhours) : 0;

                              if ($orignalcount > 1) {
                                 $splitorignalhrs = ($splitorignalhours[0] > 0) ? $splitorignalhours[0] : 00;
                                 $splitorignalmin = ($splitorignalhours[1] > 0) ? $splitorignalhours[1] : 00;
                                 $totalorignalminstohours += $splitorignalhrs * 60;
                                 $totalorignalmins += $splitorignalmin;
                                 $Totalorignalhours = $totalorignalminstohours + $totalorignalmins;
                              }


                              //For Approved hours
                              $approvedhours1 = ($approved == 'No') ? '00:00' : $approvedhours1;
                              $splitapprovedhours = explode(":", $approvedhours1);
                              $approvedcount = count($splitapprovedhours);

                              if ($approvedcount > 1) {

                                 $totalapprovedminstohours += $splitapprovedhours[0] * 60;
                                 $totalapprovedmins += $splitapprovedhours[1];
                                 $Totalapprovedhours = $totalapprovedminstohours + $totalapprovedmins;
                              }
                              // $Totalapprovedhours +=$approvedhours;
                              // $Totalminutesapprovedhours =$Totalapprovedhours * 60;

                              if ($sordorder == 1) {
                                 if ($previous > 0) {
                                    if ($previous != $row['studentId']) {
                                       $rowsTotalAttendance = $objAttendance->GetStudentTotalHoursForReport($currentSchoolId, $previousRotationId, $previous, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder, $cbosemester, $subcborotation);

                                       $totalMinutes = 0;
                                       $totalHoursTominutes = 0;
                                       $totalApprovedHoursTominutes = 0;
                                       $totalApprovedMinutes = 0;
                                       $totalHours = $rowsTotalAttendance['AttendanceTotalHours'];
                                       if ($totalHours) {
                                          $ApprovedHours = explode(':', $totalHours);
                                          if (count($ApprovedHours)) {
                                             $Hours = isset($ApprovedHours[0]) ? $ApprovedHours[0] : 0;
                                             $Minutes = isset($ApprovedHours[1]) ? $ApprovedHours[1] : 0;
                                             $totalHoursTominutes += $Hours;
                                             $totalMinutes += $Minutes;
                                             $totalHours = $Hours . ':' . $Minutes;
                                          }
                                       } else {
                                          $totalHours = "-";
                                       }

                                       $ApprovedTotal = $rowsTotalAttendance['ApprovedTotalHours'];
                                       if ($ApprovedTotal) {
                                          $ApprovedTotalHours = explode(':', $ApprovedTotal);
                                          if (count($ApprovedTotalHours)) {
                                             $Hours = isset($ApprovedTotalHours[0]) ? $ApprovedTotalHours[0] : 0;
                                             $Minutes = isset($ApprovedTotalHours[1]) ? $ApprovedTotalHours[1] : 0;
                                             $totalApprovedHoursTominutes += $Hours;
                                             $totalApprovedMinutes += $Minutes;
                                             $ApprovedTotal = $Hours . ':' . $Minutes;
                                          }
                                       } else {
                                          $ApprovedTotal = "-";
                                       }
                                       echo "<tr bgcolor='#f1f1f1'><td colspan='10' align='right'>Sub Total</td><td>$totalHours</td><td>$ApprovedTotal</td></tr>";
                                    }
                                    $previous = $row['studentId'];
                                    $previousRotationId = $row['rotationId'];
                                 }
                              }

                           ?>
                              <tr>
                                 <td><?php echo ($firstName); ?></td>
                                 <td><?php echo ($lastName); ?></td>
                                 <td><?php echo ($rank); ?></td>
                                 <td><?php echo ($rotationname); ?></td>
                                 <td><?php echo ($clockInDateTime); ?></td>
                                 <td><?php if ($clockOutDateTime != '' && $clockOutDateTime != '0000-00-00 00:00:00' && $clockOutDateTime != '11/30/-0001 01:00 AM' && $clockOutDateTime != '11/30/-0001 12:00 AM'  && $clockOutDateTime != '11/29/-0001 10:00 PM') {
                                          echo ($clockOutDateTime);
                                       } ?></td>
                                 <td><?php echo ($isException); ?></td>
                                 <td><?php echo ($reason); ?></td>
                                 <td><?php echo ($approved); ?></td>
                                 <td><?php echo ($approvedby); ?></td>
                                 <td><?php echo ($notes); ?></td>
                                 <!-- <td style="text-align: center">
                                    <input style="text-align:center;" attendanceId="<?php echo EncodeQueryData($attendanceId); ?>" class="form-control notes" type="text" maxlength="100" id="notes_<?php echo ($attendanceId); ?>" loggedId="<?php echo ($currentSchoolId); ?>" name="notes" value="<?php echo $notes; ?>" placeholder="Notes"  >
                                 </td> -->
                                 <td><?php echo ($orignalhours); ?></td>
                                 <td><?php echo ($approvedhours1); ?></td>
                              </tr>
                           <?php
                              if ($sordorder == 1) {
                                 if ($totalAttendance == $counter) {
                                    $rowsTotalAttendance = $objAttendance->GetStudentTotalHoursForReport($currentSchoolId, $previousRotationId, $previous, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder, $cbosemester, $subcborotation);

                                    $totalHours = $rowsTotalAttendance['AttendanceTotalHours'];
                                    if ($totalHours) {
                                       $ApprovedHours = explode(':', $totalHours);
                                       $Hours = $ApprovedHours[0];
                                       $Minutes = $ApprovedHours[1];
                                       $totalHoursTominutes += $Hours;
                                       $totalMinutes += $Minutes;
                                       $totalHours = $Hours . ':' . $Minutes;
                                    } else {
                                       $totalHours = "-";
                                    }

                                    $ApprovedTotal = $rowsTotalAttendance['ApprovedTotalHours'];
                                    if ($ApprovedTotal) {
                                       $ApprovedHours = explode(':', $ApprovedTotal);
                                       $Hours = $ApprovedHours[0];
                                       $Minutes = $ApprovedHours[1];
                                       $totalHoursTominutes += $Hours;
                                       $totalMinutes += $Minutes;
                                       $ApprovedTotal = $Hours . ':' . $Minutes;
                                    } else {
                                       $ApprovedTotal = "-";
                                    }


                                    echo "<tr bgcolor='#f1f1f1'><td colspan='10' align='right'>Sub Total</td><td>$totalHours</td><td>$ApprovedTotal</td></tr>";
                                 }
                                 $counter++;
                              }
                           }

                           ?>
                           <tr>
                              <td></td>
                              <td></td>
                              <td></td>
                              <td></td>
                              <td></td>
                              <td></td>
                              <td></td>
                              <td></td>
                              <td></td>
                              <td></td>
                              <td align="right"><b>Total:</b></td>

                              <td style=""><?php echo sprintf("%02d:%02d", floor($Totalorignalhours / 60), $Totalorignalhours % 60); ?></td>
                              <td style=""><?php echo sprintf("%02d:%02d", floor($Totalapprovedhours / 60), $Totalapprovedhours % 60); ?></td>
                           </tr>
                        </tbody>
                        <tfoot>
                           <!--<tr>-->
                           <?php
                           // 			$TotatlHoursapprovedhours = floor($Totalminutesapprovedhours / 60).':'.($Totalminutesapprovedhours -   floor($Totalminutesapprovedhours / 60) * 60);
                           // 			$TotatlHoursorignalhours = floor($Totalminutesorignalhours / 60).':'.($Totalminutesorignalhours -   floor($Totalminutesorignalhours / 60) * 60);

                           ?>
                           <!--<td colspan="11" align="right" ><b>Total:</b></td>-->
                           <!--<td style="text-align:center">-->
                           <?php
                           //  echo sprintf("%02d:%02d", floor($Totalorignalhours/60), $Totalorignalhours%60); 
                           ?>
                           <!--   </td>-->
                           <!--<td style="text-align:center">-->
                           <?php
                           //  echo sprintf("%02d:%02d", floor($Totalapprovedhours/60), $Totalapprovedhours%60); 
                           ?>
                           <!--    </td>-->
                           <!--<tr>	-->
                        </tfoot>
                     </table>
                  </div>
               </div>
            </div>

         <?php
            break;
         case "Attendance_Daily_Weekly":
            echo "<b>Attendance Daily/Weekly</b><br>";
         ?>
            <div class="container-fluid">
               <div class="row">
                  <div class="form-group">
                     <?php if ($startDate && $endDate) { ?>
                        <div class="col-sm-5"><b>Start Date: </b><?php echo date('m/d/Y', strtotime($startDate)); ?> <br><b>End Date: </b><?php echo date('m/d/Y', strtotime($endDate)); ?> <?php if ($rotationId) { ?><br /> <b>Rotation: </b><?php echo ($mainRotationName); ?> <?php } ?></div>
                     <?php } elseif ($startDate) { ?>
                        <div class="col-sm-3"><b>Start Date: </b><?php echo ($startDate); ?></div>
                     <?php } elseif ($endDate) { ?>
                        <div class="col-sm-3"><b>End Date: </b><?php echo ($endDate); ?></div>
                     <?php } elseif ($rotationId) { ?>
                        <div class="col-sm-3"><b>Rotation: </b><?php echo ($mainRotationName); ?></div>
                     <?php } elseif ($location_hidden) { ?>
                        <div class="col-sm-3"><b>Location: </b><?php echo ($location_hidden); ?></div>
                     <?php }  ?>
                     <?php if ($cbosemester != '') { ?>
                        <div class="col-sm-4"><b>Semester Name: </b><?php echo ($semesterName); ?> </div>
                     <?php } ?>
                     <?php if ($subcborotation != '') { ?>
                        <div class="col-sm-5"><b>Hospital Site: </b><?php echo ($rotationName); ?> </div>
                     <?php } ?>
                     <?php if ($individual_student) { ?>
                        <div class="col-sm-4"><b>Student Name: </b><?php echo ($studentFullname); ?></div>
                     <?php }  ?>
                     <?php if ($student_rank) { ?>
                        <div class="col-sm-3"><b>Rank Name: </b><?php echo ($rankName); ?></div>
                     <?php }  ?>
                  </div>
               </div>
            </div>
            <br>
            <?php
            $objAttendance = new clsAttendance();
            $totalAttendance = 0;
            if (
               isset($_POST['cboreporttype']) || ($_POST['cborotation']) || ($_POST['cboindividualstudent'])
               || ($_POST['cbostudentrank']) || ($_POST['cboevaluator']) || ($_POST['cbolocation'])
               || ($_POST['cbohospitalsite']) || ($_POST['startDate']) || ($_POST['endDate'])
            ) {
               $rowsAttendance = $objAttendance->GetStudentAttendanceDetailsForreport($currentSchoolId, $rotationId, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder, $cbosemester, $subcborotation, $isAttendanceDailyWeekly = 1);
               // $evaluator =$loggedClinicianId;
               // $rowsAttendance = $objAttendance->GetStudentAttendanceDetailsForClinicianreport($currentSchoolId, $rotationId, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder, $cbosemester, $subcborotation, $isAttendanceDailyWeekly = 1);
            }

            if ($rowsAttendance != '') {
               $totalAttendance = mysqli_num_rows($rowsAttendance);
            } else {
               echo "<b>Student Not Found.</b>";
               exit;
            }

            ?>
            <div class="row">
               <table id="attendanceDailyWeekly" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                  <thead>
                     <tr>
                        <th>First Name</th>
                        <th>Last Name</th>
                        <th>Ranking</th>
                        <th>Rotation</th>
                        <?php
                        $fromDate = new DateTime($startDate);
                        $toDate = new DateTime($endDate);
                        for ($date = $fromDate; $date <= $toDate; $date->modify('+1 day')) {
                           echo "<th>" . $date->format('l') . "</th>";
                        }
                        ?>
                        <th>Approved Hours</th>
                     </tr>
                  </thead>
                  <tbody>
                     <?php

                     if ($totalAttendance > 0) {
                        while ($row = mysqli_fetch_array($rowsAttendance)) {
                           $rotationId = $row['rotationId'];
                           $studentId = $row['studentId'];
                           $firstName = $row['firstName'];
                           $lastName = $row['lastName'];
                           $rank = $row['rank'];
                           $rotationname = $row['title'];

                           $totalApprovedhours = 0;
                           $totalApprovedMinsToHours = 0;
                           $totalApprovedMins = 0;

                     ?>
                           <tr>
                              <td><?php echo ($firstName); ?></td>
                              <td><?php echo ($lastName); ?></td>
                              <td><?php echo ($rank); ?></td>
                              <td><?php echo ($rotationname); ?></td>
                              <?php
                              $fromDate = new DateTime($startDate);
                              $toDate = new DateTime($endDate);
                              for ($date = $fromDate; $date <= $toDate; $date->modify('+1 day')) {
                                 $attendanceDate = $date->format('Y-m-d');
                                 $getApprovedHoursByDate = $objAttendance->GetStudentAttendanceByDate($studentId, $rotationId, $attendanceDate);
                                 $approvedhours = isset($getApprovedHoursByDate['approvedhours']) ? $getApprovedHoursByDate['approvedhours'] : '-';
                                 $splitApprovedhours = $approvedhours ? explode(":", $approvedhours) : [];

                                 if (count($splitApprovedhours) > 1) {
                                    $totalApprovedMinsToHours += trim($splitApprovedhours[0]) * 60;
                                    $totalApprovedMins += $splitApprovedhours[1];
                                    $totalApprovedhours = $totalApprovedMinsToHours + $totalApprovedMins;
                                 }

                                 echo "<td class='text-center'>" . $approvedhours . "</td>";
                              }

                              ?>
                              <td class='text-center'><?php echo sprintf("%02d:%02d", floor($totalApprovedhours / 60), $totalApprovedhours % 60); ?></td>
                           </tr>
                     <?php

                        }
                     }
                     ?>

                  </tbody>
               </table>
            </div>

         <?php
            break;
         case "AttendanceSummary":
            echo "<b>Attendance Summary</b><br>";
         ?>
            <div class="container-fluid">
               <div class="row">
                  <div class="form-group">
                     <?php if ($startDate && $endDate) { ?>
                        <div class="col-sm-5"><b>Start Date: </b><?php echo date('m/d/Y', strtotime($startDate)); ?> <br><b>End Date: </b><?php echo date('m/d/Y', strtotime($endDate)); ?><br /> <b>Rotation: </b><?php echo ($mainRotationName); ?> </div>
                     <?php } elseif ($startDate) { ?>
                        <div class="col-sm-3"><b>Start Date: </b><?php echo ($startDate); ?></div>
                     <?php } elseif ($endDate) { ?>
                        <div class="col-sm-3"><b>End Date: </b><?php echo ($endDate); ?></div>
                     <?php } elseif ($rotationId) { ?>
                        <div class="col-sm-3"><b>Rotation: </b><?php echo ($mainRotationName); ?></div>
                     <?php } elseif ($location_hidden) { ?>
                        <div class="col-sm-3"><b>Location: </b><?php echo ($location_hidden); ?></div>
                     <?php }  ?>
                     <?php if ($cbosemester != '') { ?>
                        <div class="col-sm-4"><b>Semester Name: </b><?php echo ($semesterName); ?> </div>
                     <?php } ?>
                     <?php if ($subcborotation != '') { ?>
                        <div class="col-sm-5"><b>Hospital Site: </b><?php echo ($rotationName); ?> </div>
                     <?php } ?>
                     <?php if ($individual_student) { ?>
                        <div class="col-sm-4"><b>Student Name: </b><?php echo ($studentFullname); ?></div>
                     <?php }  ?>
                     <?php if ($student_rank) { ?>
                        <div class="col-sm-3"><b>Rank Name: </b><?php echo ($rankName); ?></div>
                     <?php }  ?>
                  </div>
               </div>
            </div>
            <br>
            <?php
            $objAttendance = new clsAttendance();
            if (
               isset($_POST['cboreporttype']) || ($_POST['cborotation']) || ($_POST['cboindividualstudent'])
               || ($_POST['cbostudentrank']) || ($_POST['cboevaluator']) || ($_POST['cbolocation'])
               || ($_POST['cbohospitalsite']) || ($_POST['startDate']) || ($_POST['endDate'])
            ) {
               // $rowsTotalAttendanceHour = $objAttendance->GetStudentTotslAttendanceHoursForReport($currentSchoolId, $rotationId, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder, $cbosemester, $subcborotation);
               $evaluator = $loggedClinicianId;
               $rowsTotalAttendanceHour = $objAttendance->GetStudentTotslAttendanceHoursForClinicianReport($currentSchoolId, $rotationId, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder, $cbosemester, $subcborotation);
            }

            $totalAttendanceHours = 0;
            if ($rowsTotalAttendanceHour != '') {
               $totalAttendanceHours = mysqli_num_rows($rowsTotalAttendanceHour);
            } else {
               echo "<b>Student Not Found.</b>";
               exit;
            }

            ?>
            <div class="row">
               <table id="attendance-Summery" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                  <thead>
                     <tr>
                        <th>First Name</th>
                        <th>Last Name</th>
                        <th>Ranking</th>
                        <th>Rotation Name</th>
                        <th style="text-align:center">Total Original Hours</th>
                        <th style="text-align:center">Total Approved Hours</th>
                     </tr>
                  </thead>
                  <tbody>
                     <?php
                     $arrays = array();
                     if ($totalAttendanceHours) {
                        while ($row = mysqli_fetch_array($rowsTotalAttendanceHour)) {
                           $arrays[] = $row;
                        }
                     }

                     $studentCounts = array_count_values(array_column($arrays, 'studentId'));

                     $counter = 1;
                     $arrayCount = 0;
                     $previous = 0;
                     $previousRotationId = 0;
                     $totalHoursTominutes = 0;
                     $totalMinutes = 0;
                     $totalorignalminstohours = 0;
                     $totalorignalmins = 0;
                     $totalApprovedminstohours = 0;
                     $totalApprovedmins = 0;
                     $Totalorignalhours = 0;
                     $TotalApprovedhours  = 0;
                     foreach ($arrays as $row) {

                        foreach ($studentCounts as $key => $value) {
                           if ($key == $row['studentId']) {
                              $arrayCount = $value;
                           }
                        }

                        if ($counter == 1) {
                           $previous = $row['studentId'];
                           $previousRotationId = $row['rotationId'];
                        }


                        $firstName = stripslashes($row['firstName']);
                        $lastName = stripslashes($row['lastName']);
                        $rank = stripslashes($row['rank']);

                        $title = stripslashes($row['title']);
                        $AttendanceTotalHours = stripslashes($row['AttendanceTotalHours']);
                        if ($AttendanceTotalHours) {
                           $TotalHours = explode(':', $AttendanceTotalHours);

                           if ($TotalHours) {
                              $Hours = $TotalHours[0];
                              $Minutes = $TotalHours[1];
                              if (is_numeric($Hours) != 'NULL' && is_numeric($Minutes) != 'NULL') {

                                 $totalHoursTominutes += is_numeric($Hours);
                                 $totalMinutes += is_numeric($Minutes);
                                 $AttendanceTotalHours = $Hours . ':' . $Minutes;
                              }
                              //Get Grand Total
                              $totalorignalminstohours += $TotalHours[0] * 60;
                              $totalorignalmins += $TotalHours[1];
                              $Totalorignalhours = $totalorignalminstohours + $totalorignalmins;
                           }
                        } else {
                           $AttendanceTotalHours = "-";
                        }

                        $ApprovedTotalHours = ($row['ApprovedTotalHours']);
                        if ($ApprovedTotalHours) {
                           $ApprovedHours = explode(':', $ApprovedTotalHours);
                           $Hours = $ApprovedHours[0];
                           $Minutes = $ApprovedHours[1];
                           if (is_numeric($Hours) != 'NULL' && is_numeric($Minutes) != 'NULL') {
                              $totalHoursTominutes += $Hours;
                              $totalMinutes += $Minutes;
                              $ApprovedTotalHours = $Hours . ':' . $Minutes;
                           }
                           //Get Grand Total
                           $totalApprovedminstohours += $ApprovedHours[0] * 60;
                           $totalApprovedmins += $ApprovedHours[1];
                           $TotalApprovedhours = $totalApprovedminstohours + $totalApprovedmins;
                        } else {
                           $ApprovedTotalHours = "-";
                        }

                        if ($sordorder == 1) {
                           if ($previous > 0) {
                              if ($previous != $row['studentId']) {
                                 $rowsTotalAttendance = $objAttendance->GetStudentTotalHoursForReport($currentSchoolId, $previousRotationId, $previous, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder, $cbosemester, $subcborotation);

                                 $totalHours = $rowsTotalAttendance['AttendanceTotalHours'];
                                 if ($totalHours) {
                                    $ApprovedHours = explode(':', $totalHours);
                                    $Hours = isset($ApprovedHours[0]) ? $ApprovedHours[0] : 0;
                                    $Minutes = isset($ApprovedHours[1]) ? $ApprovedHours[1] : 0;
                                    $totalHoursTominutes += $Hours;
                                    $totalMinutes += $Minutes;
                                    $totalHours = $Hours . ':' . $Minutes;
                                 } else {
                                    $totalHours = "-";
                                 }

                                 $ApprovedTotal = $rowsTotalAttendance['ApprovedTotalHours'];
                                 if ($ApprovedTotal) {
                                    $ApprovedHours = explode(':', $ApprovedTotal);
                                    $Hours = $ApprovedHours[0];
                                    $Minutes = $ApprovedHours[1];
                                    $totalHoursTominutes += $Hours;
                                    $totalMinutes += $Minutes;
                                    $ApprovedTotal = $Hours . ':' . $Minutes;
                                 } else {
                                    $ApprovedTotal = "-";
                                 }
                                 echo "<tr bgcolor='#f1f1f1'><td colspan='4' align='right'><b>Sub Total:</b></td><td align='center' >$totalHours</td><td align='center'>$ApprovedTotal</td></tr>";
                              }
                              $previous = $row['studentId'];
                              $previousRotationId = $row['rotationId'];
                           }
                        }

                     ?>
                        <tr>
                           <td><?php echo ($firstName); ?></td>
                           <td><?php echo ($lastName); ?></td>
                           <td><?php echo ($rank); ?></td>
                           <td><?php echo ($title); ?></td>
                           <td align="center"><?php echo ($AttendanceTotalHours); ?></td>
                           <td align="center"><?php echo ($ApprovedTotalHours); ?></td>
                        </tr>
                     <?php
                        if ($sordorder == 1) {
                           if ($totalAttendanceHours == $counter) {
                              $rowsTotalAttendance = $objAttendance->GetStudentTotalHoursForReport($currentSchoolId, $previousRotationId, $previous, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder, $cbosemester, $subcborotation);

                              $totalHours = $rowsTotalAttendance['AttendanceTotalHours'];
                              if ($totalHours) {
                                 $ApprovedHours = explode(':', $totalHours);
                                 $Hours = $ApprovedHours[0];
                                 $Minutes = $ApprovedHours[1];
                                 $totalHoursTominutes += $Hours;
                                 $totalMinutes += $Minutes;
                                 $totalHours = $Hours . ':' . $Minutes;
                              } else {
                                 $totalHours = "-";
                              }

                              $ApprovedTotal = $rowsTotalAttendance['ApprovedTotalHours'];
                              if ($ApprovedTotal) {
                                 $ApprovedHours = explode(':', $ApprovedTotal);
                                 $Hours = $ApprovedHours[0];
                                 $Minutes = $ApprovedHours[1];
                                 $totalHoursTominutes += $Hours;
                                 $totalMinutes += $Minutes;
                                 $ApprovedTotal = $Hours . ':' . $Minutes;
                              } else {
                                 $ApprovedTotal = "-";
                              }


                              echo "<tr bgcolor='#f1f1f1'><td colspan='4' align='right' ><b>Sub Total:</b></td><td align='center'>$totalHours</td><td align='center'>$ApprovedTotal</td></tr>";
                           }
                           $counter++;
                        }
                     }
                     unset($objAttendance);
                     $allOrignalhours = sprintf("%02d:%02d", floor($Totalorignalhours / 60), $Totalorignalhours % 60);
                     $allApprovedhours = sprintf("%02d:%02d", floor($TotalApprovedhours / 60), $TotalApprovedhours % 60);
                     ?>
                     <tr>

                        <td></td>
                        <td></td>
                        <td></td>
                        <td align="right"><b>Total:</b></td>

                        <td style="text-align:center"><?php echo $allOrignalhours; ?></td>
                        <td style="text-align:center"><?php echo $allApprovedhours; ?></td>
                     </tr>
                  </tbody>

               </table>
            </div>
         <?php
            break;
         case "Checkoff":

            echo "<b>Checkoff</b><br>";
            $totalcheckoff = 0;
         ?>
            <div class="container-fluid ">
               <div class="row">
                  <div class="form-group">
                     <?php if ($startDate && $endDate) { ?>
                        <div class="col-sm-4"><b>Start Date: </b><?php echo date('m/d/Y', strtotime($startDate)); ?> <b>End Date: </b><?php echo date('m/d/Y', strtotime($endDate)); ?><?php if ($rotationId) { ?> <br /> <b>Rotation: </b><?php echo ($mainRotationName); ?><?php } ?></div>
                     <?php } elseif ($startDate) { ?>
                        <div class="col-sm-3"><b>Start Date: </b><?php echo ($startDate); ?></div>
                     <?php } elseif ($endDate) { ?>
                        <div class="col-sm-3"><b>End Date: </b><?php echo ($endDate); ?></div>
                     <?php } elseif ($rotationId) { ?>
                        <div class="col-sm-3"><b>Rotation: </b><?php echo ($mainRotationName); ?></div>
                     <?php } elseif ($location_hidden) { ?>
                        <div class="col-sm-3"><b>Location: </b><?php echo ($location_hidden); ?></div>
                     <?php }  ?>
                     <?php if ($cbosemester != '') { ?>
                        <div class="col-sm-4"><b>Semester Name: </b><?php echo ($semesterName); ?> </div>
                     <?php } ?>
                     <?php if ($individual_student) { ?>
                        <div class="col-sm-3"><b>Student Name: </b><?php echo ($studentFullname); ?></div>
                     <?php }  ?>
                     <?php if ($evaluator) { ?>
                        <div class="col-sm-3"><b>Evaluator Name: </b><?php echo ($evaluatorName); ?></div>
                     <?php }  ?>
                     <?php if ($student_rank) { ?>
                        <div class="col-sm-3"><b>Rank Name: </b><?php echo ($rankName); ?></div>
                     <?php }  ?>
                  </div>
               </div>
               <br>
               <div class="row">
                  <div id="table-scroll" class="table-scroll">
                     <div class="table-wrap">
                        <table id="checkoff-datatable" class="table table-bordered nowrap table-hover" cellspacing="0" width="100%">
                           <?php
                           $objcheckoff = new clscheckoff();
                           $totalcheckoff = 0;
                           if (
                              isset($_POST['cboreporttype']) || ($_POST['cborotation']) || ($_POST['cboindividualstudent'])
                              || ($_POST['cbostudentrank']) || ($_POST['cboevaluator']) || ($_POST['cbolocation'])
                              || ($_POST['cbohospitalsite']) || ($_POST['startDate']) || ($_POST['endDate']) || ($_POST['checkoffTopic'])
                           ) {
                              if ($individual_student != '') {
                                 $individual_student = count($individual_student) ? serialize($individual_student) : '';
                              }
                              // $rowscheckoff = $objcheckoff->GetStudentCheckOffDetailsForreport($currentSchoolId, $rotationId, $subcborotation, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $checkoffTopic, $AscDesc, $sordorder, $cbosemester, 0, 0);
                              $clinicianId = $loggedClinicianId;
                              $rowscheckoff = $objcheckoff->GetStudentCheckOffDetailsForClinicianreport($currentSchoolId, $rotationId, $subcborotation, $individual_student, $student_rank, $evaluator, $clinicianId, $school_location, $hospital_site, $startDate, $endDate, $checkoffTopic, $AscDesc, $sordorder, $cbosemester, 0, 0);
                           }
                           if ($rowscheckoff != '') {
                              $totalcheckoff = mysqli_num_rows($rowscheckoff);
                           } else {
                              echo "<b>PEF Checkoff Not Found.</b>";
                              exit;
                           }

                           ?>
                           <thead>
                              <tr>
                                 <th style="visibility:visible;">First Name</th>
                                 <th style="visibility:visible;">Last Name</th>
                                 <th>Rank</th>
                                 <?php if ($isActiveCheckoff == 2) { ?>
                                    <th>Instructor</th>
                                    <th>Hospital Site</th>
                                    <th>Rotation</th>
                                    <th>Score</th>
                                    <th>PEF ID</th>
                                    <th>PEF Checkoff Title</th>
                                    <th>Student Signoff Date</th>
                                    <th>Instructor Signoff Date</th>
                                    <th>School Signoff Date</th>
                                 <?php } else { ?>
                                    <th>Evaluator</th>
                                    <th>Hospital Site</th>
                                    <th>Rotation</th>
                                    <th>Course Topics</th>
                                    <th>Date</th>
                                 <?php }
                                 if ($isActiveCheckoff == 1) { ?>
                                    <!--<th>Yes</th>   -->
                                    <!--<th>No</th>   -->
                                    <!--<th>Comments</th>   -->
                                    <th>Score</th>
                                 <?php } elseif ($isActiveCheckoff == 0) { ?>
                                    <th>Stud</th>
                                    <th>Precp</th>
                                    <th>Lab</th>
                                    <th>Clinical</th>
                                    <!-- <th>Rotation</th> -->
                                    <th style="text-align:center">Topic</th>
                                 <?php } ?>
                              </tr>
                           </thead>
                        </table>
                     </div>
                  </div>
               </div>
            </div>
         <?php
            break;
         case "CaseStudy":

            echo "<b>Case Study Report</b><br>";
            $totalCaseStudy = 0;
         ?>
            <div class="container-fluid ">
               <div class="row">
                  <div class="form-group">
                     <?php if ($startDate && $endDate) { ?>
                        <div class="col-sm-4"><b>Start Date: </b><?php echo date('m/d/Y', strtotime($startDate)); ?> <b>End Date: </b><?php echo date('m/d/Y', strtotime($endDate)); ?><?php if ($rotationId) { ?> <br /> <b>Rotation: </b><?php echo ($mainRotationName); ?><?php } ?></div>
                     <?php } elseif ($startDate) { ?>
                        <div class="col-sm-3"><b>Start Date: </b><?php echo ($startDate); ?></div>
                     <?php } elseif ($endDate) { ?>
                        <div class="col-sm-3"><b>End Date: </b><?php echo ($endDate); ?></div>
                     <?php } elseif ($rotationId) { ?>
                        <div class="col-sm-3"><b>Rotation: </b><?php echo ($mainRotationName); ?></div>
                     <?php } elseif ($location_hidden) { ?>
                        <div class="col-sm-3"><b>Location: </b><?php echo ($location_hidden); ?></div>
                     <?php }  ?>
                     <?php if ($cbosemester != '') { ?>
                        <div class="col-sm-4"><b>Semester Name: </b><?php echo ($semesterName); ?> </div>
                     <?php } ?>
                     <?php if ($individual_student) { ?>
                        <div class="col-sm-3"><b>Student Name: </b><?php echo ($studentFullname); ?></div>
                     <?php }  ?>
                     <?php if ($student_rank) { ?>
                        <div class="col-sm-3"><b>Rank Name: </b><?php echo ($rankName); ?></div>
                     <?php }  ?>
                  </div>
               </div>
               <br>
               <div class="row">
                  <div id="table-scroll" class="table-scroll">
                     <div class="table-wrap">
                        <table id="casestudy-datatable" class="table table-bordered nowrap table-hover" cellspacing="0" width="100%">
                           <?php
                           $objCaseStudy = new clsCaseStudy();

                           $totalCaseStudy = 0;
                           if (
                              isset($_POST['cboreporttype']) || ($_POST['cborotation']) || ($_POST['cboindividualstudent'])
                              || ($_POST['cbostudentrank']) || ($_POST['cboevaluator']) || ($_POST['cbolocation'])
                              || ($_POST['cbohospitalsite']) || ($_POST['startDate']) || ($_POST['endDate']) || ($_POST['checkoffTopic'])
                           ) {
                              // print_r($individual_student);
                              // if($individual_student !='')
                              //  {
                              //    echo  $individual_student = count($individual_student) ? serialize($individual_student) : '';
                              //  }
                              $evaluator = $loggedClinicianId;
                              $getCaseStudydetails = $objCaseStudy->GetStudentCaseStudyForClinicianReport($currentSchoolId, $rotationId, $subcborotation, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder, $cbosemester);
                           }
                           if ($getCaseStudydetails != '') {
                              $totalCaseStudy = mysqli_num_rows($getCaseStudydetails);
                           } else {
                              echo "<b>Case Study Not Found.</b>";
                              exit;
                           }

                           ?>
                           <thead>
                              <tr>
                                 <th style="visibility:visible;">First Name</th>
                                 <th style="visibility:visible;">Last Name</th>
                                 <th>Rank</th>
                                 <th>Rotation</th>
                                 <th>Case Study Date</th>
                                 <th>Case Study</th>
                                 <th>Chief Complaint/<br>Admitting Diagnosis</th>
                                 <th style="text-align: center">Clinician</th>
                                 <th style="text-align: center">School</th>

                              </tr>
                           </thead>
                           <tbody>
                              <?php
                              if ($totalCaseStudy > 0) {
                                 while ($row = mysqli_fetch_array($getCaseStudydetails)) {
                                    $type = $row['type'];
                                    if ($type == 'floor')
                                       $caseStudyName = 'Floor Therapy';
                                    else if ($type == 'PACR')
                                       $caseStudyName = 'PACR';
                                    else
                                       $caseStudyName = 'Adult ICU & NICU/PICU';


                                    $DBStudentId = $row['studentId'];
                                    $studentfirstName = $row['studentfirstName'];
                                    $studentlastName = $row['studentlastName'];
                                    $studentfullName = $studentfirstName . ' ' . $studentlastName;
                                    $caseStudyId = $row['caseStudyId'];
                                    $caseStudydate = $row['caseStudydate'];
                                    $caseStudydate = converFromServerTimeZone($caseStudydate, $TimeZone);
                                    $caseStudydate = date("m/d/Y", strtotime($caseStudydate));
                                    $rotationname = $row['rotationname'];
                                    $ChiefComplaint = $row['ChiefComplaint'];
                                    $clinicianDate = $row['ClinicianDate'];
                                    $rankTitle = $row['rankTitle'];
                                    $rsClinicianReviewsCount = 0;
                                    if ($type == 'PACR') {
                                       //For Clinician Reviews
                                       $objCaseStudy = new clsCaseStudy();
                                       $rsClinicianReviews = $objCaseStudy->GetClinicianReviews($caseStudyId);
                                       $rsClinicianReviewsCount = ($rsClinicianReviews != '') ? mysqli_num_rows($rsClinicianReviews) : 0;
                                       unset($objCaseStudy);
                                    }
                                    if ($clinicianDate) {
                                       $clinicianDate = 'Yes';
                                    } else if ($clinicianDate == '' && $rsClinicianReviewsCount) {
                                       $clinicianDate = '<p style="color: red;">Under Review</p>';
                                       $isActionBtnName = 'Edit';
                                    } else {
                                       $clinicianDate = 'No';
                                    }
                                    $schoolDate = $row['schoolDate'];
                                    if ($schoolDate) {
                                       $schoolDate = 'Yes';
                                    } else {
                                       $schoolDate = 'No';
                                    }

                                    $shortTitlelen = strlen($ChiefComplaint);
                                    $caseChiefComplaint = wordwrap($ChiefComplaint, 40, "<br>\n", true);



                              ?>
                                    <tr>
                                       <td><?php echo ($studentfirstName); ?></td>
                                       <td><?php echo ($studentlastName); ?></td>
                                       <td><?php echo ($rankTitle); ?></td>
                                       <td><?php echo ($rotationname); ?></td>
                                       <td><?php echo ($caseStudydate); ?></td>
                                       <td><?php echo $caseStudyName; ?></td>
                                       <td><?php echo ($caseChiefComplaint); ?></td>
                                       <td class="text-center"><?php echo ($clinicianDate); ?></td>
                                       <td class="text-center"><?php echo ($schoolDate); ?></td>
                                    </tr>
                              <?php }
                              }
                              ?>
                           </tbody>
                        </table>
                     </div>
                  </div>
               </div>
            </div><br>
         <?php
            break;
         case "Preceptor_Checkoff":

            echo "<b>Preceptor Checkoffs</b><br>";
            $totalcheckoff = 0;
         ?>
            <div class="container-fluid ">
               <div class="row">
                  <div class="form-group">
                     <?php if ($startDate && $endDate) { ?>
                        <div class="col-sm-4"><b>Start Date: </b><?php echo date('m/d/Y', strtotime($startDate)); ?> <b>End Date: </b><?php echo date('m/d/Y', strtotime($endDate)); ?><?php if ($rotationId) { ?> <br /> <b>Rotation: </b><?php echo ($mainRotationName); ?><?php } ?></div>
                     <?php } elseif ($startDate) { ?>
                        <div class="col-sm-3"><b>Start Date: </b><?php echo ($startDate); ?></div>
                     <?php } elseif ($endDate) { ?>
                        <div class="col-sm-3"><b>End Date: </b><?php echo ($endDate); ?></div>
                     <?php } elseif ($rotationId) { ?>
                        <div class="col-sm-3"><b>Rotation: </b><?php echo ($mainRotationName); ?></div>
                     <?php } elseif ($location_hidden) { ?>
                        <div class="col-sm-3"><b>Location: </b><?php echo ($location_hidden); ?></div>
                     <?php }  ?>
                     <?php if ($cbosemester != '') { ?>
                        <div class="col-sm-4"><b>Semester Name: </b><?php echo ($semesterName); ?> </div>
                     <?php } ?>
                     <?php if ($individual_student) { ?>
                        <div class="col-sm-3"><b>Student Name: </b><?php echo ($studentFullname); ?></div>
                     <?php }  ?>
                     <?php if ($student_rank) { ?>
                        <div class="col-sm-3"><b>Rank Name: </b><?php echo ($rankName); ?></div>
                     <?php }  ?>
                  </div>
               </div>
               <br>
               <div class="row">
                  <div id="table-scroll" class="table-scroll">
                     <div class="table-wrap">
                        <table id="precheckoff-datatable" class="table table-bordered nowrap table-hover" cellspacing="0" width="100%">
                           <?php
                           $objcheckoff = new clscheckoff();
                           $totalcheckoff = 0;
                           if (
                              isset($_POST['cboreporttype']) || ($_POST['cborotation']) || ($_POST['cboindividualstudent'])
                              || ($_POST['cbostudentrank']) || ($_POST['cboevaluator']) || ($_POST['cbolocation'])
                              || ($_POST['cbohospitalsite']) || ($_POST['startDate']) || ($_POST['endDate']) || ($_POST['checkoffTopic'])
                           ) {
                              if ($individual_student != '') {
                                 $individual_student = count($individual_student) ? serialize($individual_student) : '';
                              }
                              $rowscheckoff = $objcheckoff->GetStudentPreceptorCheckOffDetailsForreport($currentSchoolId, $rotationId, $subcborotation, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $checkoffTopic, $AscDesc, $sordorder, $cbosemester, 0, 0);
                           }
                           if ($rowscheckoff != '') {
                              $totalcheckoff = mysqli_num_rows($rowscheckoff);
                           } else {
                              echo "<b>Preceptor Checkoff Not Found.</b>";
                              exit;
                           }

                           ?>
                           <thead>
                              <tr>
                                 <th style="visibility:visible; vertical-align: middle">First Name</th>
                                 <th style="visibility:visible; vertical-align: middle">Last Name</th>
                                 <th style="vertical-align: middle">Rank</th>
                                 <?php if ($isActiveCheckoff == 2) { ?>
                                    <!-- <th>Instructor</th> -->
                                    <th style="vertical-align: middle">Rotation</th>
                                    <th style="vertical-align: middle">Score</th>
                                    <th style="vertical-align: middle">PEF ID</th>
                                    <th style="vertical-align: middle">PEF Checkoff Title</th>
                                    <th style="vertical-align: middle">Student Signoff Date</th>
                                    <!-- <th style="vertical-align: middle">Instructor Signoff Date</th>
                              <th style="vertical-align: middle">School Signoff Date</th> -->
                                    <!-- <th style="text-align:center ;vertical-align: middle">Topic</th> -->
                                    <th style="vertical-align: middle">Preceptor Name</th>
                                    <th style="vertical-align: middle">SMS Phone</th>
                                    <th style="vertical-align: middle">Status</th>
                                 <?php } else { ?>
                                    <!-- <th style="vertical-align: middle">Evaluator</th> -->
                                    <!-- <th style="vertical-align: middle">Course Topics</th> -->
                                    <th style="vertical-align: middle">Date</th>
                                 <?php }
                                 if ($isActiveCheckoff == 1) { ?>
                                    <!--<th style="vertical-align: middle">Yes</th>   -->
                                    <!--<th style="vertical-align: middle">No</th>   -->
                                    <!--<th style="vertical-align: middle">Comments</th>   -->
                                    <th style="vertical-align: middle">Score</th>
                                    <th style="vertical-align: middle">Rotation</th>
                                    <th style="text-align:center ;vertical-align: middle">Topic</th>
                                    <th style="vertical-align: middle">Student <br> Signoff Date</th>
                                    <th style="vertical-align: middle">Preceptor Name</th>
                                    <th style="vertical-align: middle">SMS Phone</th>
                                    <th style="vertical-align: middle">Status</th>
                                 <?php } elseif ($isActiveCheckoff == 0) { ?>
                                    <th style="vertical-align: middle">Stud</th>
                                    <th style="vertical-align: middle">Precp</th>
                                    <th style="vertical-align: middle">Lab</th>
                                    <th style="vertical-align: middle">Clinical</th>
                                    <th style="vertical-align: middle">Rotation</th>
                                    <th style="text-align:center ;vertical-align: middle">Topic</th>
                                    <th style="vertical-align: middle">Student <br> Signoff Date</th>
                                    <th style="vertical-align: middle">Preceptor Name</th>
                                    <th style="vertical-align: middle">SMS Phone</th>
                                    <th style="vertical-align: middle">Status</th>

                                 <?php } ?>
                              </tr>
                           </thead>
                        </table>
                     </div>
                  </div>
               </div>
            </div>
         <?php
            break;
         case "CheckoffByCourses":

            echo "<b>Checkoff by Courses</b><br>";
            $totalcheckoff = 0;
         ?>
            <div class="container-fluid ">
               <div class="row">
                  <div class="form-group">
                     <?php if ($startDate && $endDate) { ?>
                        <div class="col-sm-4"><b>Start Date: </b><?php echo date('m/d/Y', strtotime($startDate)); ?> <b>End Date: </b><?php echo date('m/d/Y', strtotime($endDate)); ?><?php if ($rotationId) { ?> <br /> <b>Rotation: </b><?php echo ($mainRotationName); ?><?php } ?></div>
                     <?php } elseif ($startDate) { ?>
                        <div class="col-sm-3"><b>Start Date: </b><?php echo ($startDate); ?></div>
                     <?php } elseif ($endDate) { ?>
                        <div class="col-sm-3"><b>End Date: </b><?php echo ($endDate); ?></div>
                     <?php } elseif ($rotationId) { ?>
                        <div class="col-sm-3"><b>Rotation: </b><?php echo ($mainRotationName); ?></div>
                     <?php } elseif ($location_hidden) { ?>
                        <div class="col-sm-3"><b>Location: </b><?php echo ($location_hidden); ?></div>
                     <?php }  ?>
                     <?php if ($cbosemester != '') { ?>
                        <div class="col-sm-4"><b>Semester Name: </b><?php echo ($semesterName); ?> </div>
                     <?php } ?>
                     <?php if ($individual_student) { ?>
                        <div class="col-sm-3"><b>Student Name: </b><?php echo ($studentFullname); ?></div>
                     <?php }  ?>
                     <?php if ($student_rank) { ?>
                        <div class="col-sm-3"><b>Rank Name: </b><?php echo ($rankName); ?></div>
                     <?php }  ?>
                     <?php if ($courseId) { ?>
                        <div class="col-sm-3"><b>Course: </b><?php echo ($courseTitle); ?></div>
                     <?php }  ?>
                  </div>
               </div>
               <br>
               <div class="row mb-5">
                  <div id="table-scroll" class="table-scroll">
                     <div>
                        <table id="checkoff-by-courses-datatable" class="table table-bordered nowrap table-hover" cellspacing="0" width="100%">
                           <?php
                           $objcheckoff = new clscheckoff();
                           $totalcheckoff = 0;
                           if (
                              isset($_POST['cboreporttype']) || ($_POST['cborotation']) || ($_POST['cboindividualstudent'])
                              || ($_POST['cbostudentrank']) || ($_POST['cboevaluator']) || ($_POST['cbolocation'])
                              || ($_POST['cbohospitalsite']) || ($_POST['startDate']) || ($_POST['endDate']) || ($_POST['checkoffTopic'])
                           ) {
                              $rowscheckoff = $objcheckoff->GetCheckOffByCoursesForreport($currentSchoolId, $rotationId, $subcborotation, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $checkoffTopic, $AscDesc, $sordorder, $cbosemester, $courseTopicCompletion, $courseId);
                              // $evaluator = $loggedClinicianId;
                              // $rowscheckoff = $objcheckoff->GetCheckOffByCoursesForClinicianreport($currentSchoolId, $rotationId, $subcborotation, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $checkoffTopic, $AscDesc, $sordorder, $cbosemester, $courseTopicCompletion);
                           }
                           if ($rowscheckoff != '') {
                              $totalcheckoff = mysqli_num_rows($rowscheckoff);
                           } else {
                              echo "<b>PEF Checkoff By Courses Not Found.</b>";
                              exit;
                           }

                           ?>
                           <thead>
                              <tr>
                                 <th>First Name</th>
                                 <th>Last Name</th>
                                 <th>Rotation</th>
                                 <th>Course</th>
                                 <th>Evaluator</th>
                                 <th>Course Topics</th>
                                 <th>Date</th>
                                 <th>Score</th>
                                 <th>Status</th>
                              </tr>
                           </thead>
                           <tbody>
                              <?php if ($totalcheckoff > 0) {
                                 while ($row = mysqli_fetch_array($rowscheckoff)) {

                                    $TimeZone = $_SESSION["loggedClinicianSchoolTimeZone"];

                                    $firstName = $row['firstName'];
                                    $lastName = $row['lastName'];
                                    $courseTitle = $row['courseTitle'];
                                    $rotationTitle = $row['rotationTitle'];
                                    $clinicianfname = $row['clinicianfname'];
                                    $clinicianlname = $row['clinicianlname'];
                                    $clinicianfullname = $clinicianfname . ' ' . $clinicianlname;
                                    $schooltitle = stripslashes($row['schooltitle']);

                                    $checkoffDateTime = stripslashes($row['checkoffDateTime']);
                                    if ($checkoffDateTime) {
                                       $checkoffDateTime = converFromServerTimeZone($checkoffDateTime, $TimeZone);
                                       $checkoffDateTime = date('m/d/Y', strtotime($checkoffDateTime));
                                    }

                                    $TopicStatus = stripslashes($row['Status']);
                                    if ($isActiveCheckoff == 2)
                                       $Score = $row['calculatedUsafScore'];
                                    else
                                       $Score = $row['calculatedScore'];
                              ?>
                                    <tr>
                                       <td><?php echo $firstName; ?></td>
                                       <td><?php echo $lastName; ?></td>
                                       <td><?php echo $rotationTitle; ?></td>
                                       <td><?php echo $courseTitle; ?></td>
                                       <td><?php echo $clinicianfullname; ?></td>
                                       <td><?php echo $schooltitle; ?></td>
                                       <td><?php echo $checkoffDateTime; ?></td>
                                       <td><?php echo $Score; ?></td>
                                       <td><?php echo $TopicStatus; ?></td>
                                    </tr>
                              <?php
                                 }
                              }
                              ?>
                           </tbody>
                        </table>
                     </div>
                  </div>
               </div>
            </div>
         <?php
            break;
         case "CI_Eval":
            echo "<b>CI Evalution</b><br>";
         ?>
            <div class="container-fluid">
               <div class="row">
                  <div class="form-group">
                     <?php if ($startDate && $endDate) { ?>
                        <div class="col-sm-3"><b>Start Date: </b><?php echo date('m/d/Y', strtotime($startDate)); ?><br><b>End Date: </b><?php echo date('m/d/Y', strtotime($endDate)); ?> <?php if ($rotationId > 0) { ?> <br /> <b>Rotation: </b><?php echo ($mainRotationName);
                                                                                                                                                                                                                                                } ?></div>
                     <?php } elseif ($startDate) { ?>
                        <div class="col-sm-3"><b>Start Date: </b><?php echo ($startDate); ?></div>
                     <?php } elseif ($endDate) { ?>
                        <div class="col-sm-3"><b>End Date: </b><?php echo ($endDate); ?></div>
                     <?php } elseif ($rotationId) { ?>
                        <div class="col-sm-3"><b>Rotation: </b><?php echo ($mainRotationName); ?></div>
                     <?php } elseif ($location_hidden) { ?>
                        <div class="col-sm-3"><b>Location: </b><?php echo ($location_hidden); ?></div>
                     <?php }  ?>
                     <?php if ($cbosemester != '') { ?>
                        <div class="col-sm-3"><b>Semester Name: </b><?php echo ($semesterName); ?> </div>
                     <?php } ?>
                     <?php if ($subcborotation != '') { ?>
                        <div class="col-sm-5"><b>Hospital Site: </b><?php echo ($rotationName); ?> </div>
                     <?php } ?>
                     <?php if ($individual_student) { ?>
                        <div class="col-sm-3"><b>Student Name: </b><?php echo ($studentFullname); ?></div>
                     <?php }  ?>
                     <?php if ($student_rank) { ?>
                        <div class="col-sm-3"><b>Rank Name: </b><?php echo ($rankName); ?></div>
                     <?php }  ?>
                  </div>
               </div>
            </div>
            <br>
            <div class="row">
               <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                  <?php
                  $objCIevaluation = new clsCIevaluation();
                  $totalCIevaluation = 0;
                  if (
                     isset($_POST['cboreporttype']) || ($_POST['cborotation']) || ($_POST['cboindividualstudent'])
                     || ($_POST['cbostudentrank']) || ($_POST['cboevaluator']) || ($_POST['cbolocation'])
                     || ($_POST['cbohospitalsite']) || ($_POST['startDate']) || ($_POST['endDate'])
                  ) {

                     $rowsCIevaluation = $objCIevaluation->GetStudentCIevaluationDetailsForreport($currentSchoolId, $rotationId, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder, $cbosemester, $subcborotation);
                  }
                  if ($rowsCIevaluation != '') {
                     $totalCIevaluation = mysqli_num_rows($rowsCIevaluation);
                  } else {
                     echo "<b>CI Evaluation Not Found.</b>";
                     exit;
                  }

                  ?>
                  <thead>
                     <tr>
                        <th>First Name</th>
                        <th>Last Name</th>
                        <th>Ranking</th>
                        <th>Rotation</th>
                        <th>Clinical <br>Instructor</th>
                        <th>Evaluation Date</th>
                        <th>Average Rating</th>
                        <th>Recommend<br>Person</th>
                        <th>Clinical <br>Instructor<br>Strengths</th>
                        <th>Need To <br>Improve</th>
                     </tr>
                  </thead>
                  <tbody>
                     <?php
                     $TotalEvaluationScore = 0;
                     if ($totalCIevaluation > 0) {
                        while ($row = mysqli_fetch_array($rowsCIevaluation)) {

                           $ciEvaluationMasterId = stripslashes($row['ciEvaluationMasterId']);
                           $firstName = stripslashes($row['firstName']);
                           $lastName = stripslashes($row['lastName']);
                           $Rankname = stripslashes($row['Rankname']);
                           $evaluationDate = stripslashes($row['evaluationDate']);
                           $evaluationDate = date('m/d/Y', strtotime($evaluationDate));
                           $Rotationname = stripslashes($row['Rotationname']);
                           $clinicianfname = stripslashes($row['clinicianfname']);
                           $clinicianlname = stripslashes($row['clinicianlname']);
                           $clinicianfullname = $clinicianfname . ' ' . $clinicianlname;
                           $GetCIevaluationScore = $objCIevaluation->GetCIEvaluationScore($ciEvaluationMasterId);
                           $EvaluationScore = $GetCIevaluationScore['EvaluationScore'];

                           $GetRecommendperson = $objCIevaluation->GetRecommendperson($ciEvaluationMasterId);
                           $GetClinicalInstructorStrengths = $objCIevaluation->GetClinicalInstructorStrengths($ciEvaluationMasterId);
                           $GetNeedtoImprove = $objCIevaluation->GetNeedtoImprove($ciEvaluationMasterId);


                           $TotalEvaluationScore += $EvaluationScore;
                     ?>
                           <tr>
                              <td><?php echo ($firstName); ?></td>
                              <td><?php echo ($lastName); ?></td>
                              <td><?php echo ($Rankname); ?></td>
                              <td><?php echo ($Rotationname); ?></td>
                              <td><?php echo ($clinicianfullname); ?></td>
                              <td><?php if ($evaluationDate != '' && $evaluationDate != '01/01/1970' && $evaluationDate != '11/30/-0001') {
                                       echo ($evaluationDate);
                                    } ?></td>
                              <td align="center"><?php echo $EvaluationScore; ?></td>
                              <td><?php echo $GetRecommendperson; ?></td>
                              <td><?php echo $GetClinicalInstructorStrengths; ?></td>
                              <td><?php echo $GetNeedtoImprove; ?></td>
                           </tr>
                     <?php }
                     } ?>
                     <tr>
                        <?php
                        $AvgEvaluationScore = 0;
                        if ($totalCIevaluation > 0)
                           $AvgEvaluationScore = $TotalEvaluationScore / $totalCIevaluation;
                        ?>
                        <td colspan="6" align="right"><b>Total:</b></td>
                        <td align="center">
                           <?php echo number_format((float)$AvgEvaluationScore, 2, '.', ''); ?>
                        </td>
                        <td></td>
                        <td></td>
                        <td></td>
                     <tr>
                  </tbody>
               </table>
            </div>
         <?php
            break;
         case "P_Eval":
            echo "<b>P Evalution</b><br>";
         ?>
            <div class="container-fluid">
               <div class="row">
                  <div class="form-group">
                     <?php if ($startDate && $endDate) { ?>
                        <div class="col-sm-3"><b>Start Date: </b><?php echo date('m/d/Y', strtotime($startDate)); ?><br><b>End Date: </b><?php echo date('m/d/Y', strtotime($endDate)); ?> <?php if ($rotationId > 0) { ?> <br /> <b>Rotation: </b><?php echo ($mainRotationName);
                                                                                                                                                                                                                                                } ?></div>
                     <?php } elseif ($startDate) { ?>
                        <div class="col-sm-3"><b>Start Date: </b><?php echo ($startDate); ?></div>
                     <?php } elseif ($endDate) { ?>
                        <div class="col-sm-3"><b>End Date: </b><?php echo ($endDate); ?></div>
                     <?php } elseif ($rotationId) { ?>
                        <div class="col-sm-3"><b>Rotation: </b><?php echo ($mainRotationName); ?></div>
                     <?php } elseif ($location_hidden) { ?>
                        <div class="col-sm-3"><b>Location: </b><?php echo ($location_hidden); ?></div>
                     <?php }  ?>
                     <?php if ($cbosemester != '') { ?>
                        <div class="col-sm-3"><b>Semester Name: </b><?php echo ($semesterName); ?> </div>
                     <?php } ?>
                     <?php if ($subcborotation != '') { ?>
                        <div class="col-sm-5"><b>Hospital Site: </b><?php echo ($rotationName); ?> </div>
                     <?php } ?>
                     <?php if ($individual_student) { ?>
                        <div class="col-sm-3"><b>Student Name: </b><?php echo ($studentFullname); ?></div>
                     <?php }  ?>
                     <?php if ($student_rank) { ?>
                        <div class="col-sm-3"><b>Rank Name: </b><?php echo ($rankName); ?></div>
                     <?php }  ?>
                  </div>
               </div>
            </div>
            <br>
            <div class="row">
               <table id="pEval-responsive" class="table table-bordered dt-responsive nowrap table-hover responsive-datatable" cellspacing="0" width="100%">
                  <?php
                  $objPevaluation = new clsPEvaluation();
                  $totalPevaluation = 0;
                  if (
                     isset($_POST['cboreporttype']) || ($_POST['cborotation']) || ($_POST['cboindividualstudent'])
                     || ($_POST['cbostudentrank']) || ($_POST['cboevaluator']) || ($_POST['cbolocation'])
                     || ($_POST['cbohospitalsite']) || ($_POST['startDate']) || ($_POST['endDate'])
                  ) {

                     $rowsPevaluation = $objPevaluation->GetStudentPevaluationDetailsForreport($currentSchoolId, $rotationId, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder, $cbosemester, $subcborotation);
                  }
                  if ($rowsPevaluation != '') {
                     $totalPevaluation = mysqli_num_rows($rowsPevaluation);
                  } else {
                     echo "<b>P Evaluation Not Found.</b>";
                     exit;
                  }

                  ?>
                  <thead>
                     <tr>
                        <th>First Name</th>
                        <th>Last Name</th>
                        <th>Ranking</th>
                        <th>Rotation</th>
                        <th>Hospital Site</th>
                        <th>Evaluation Date</th>
                        <th>Average Rating</th>
                        <th style="word-wrap: break-word;">Who was the Preceptor <br>that stood out to <br>you and why?</th>
                        <th style="word-wrap: break-word;">(Optional) If there was a <br> Preceptor who did not <br>meet your expections, <br>who were they and why?</th>
                     </tr>
                  </thead>
                  <tbody>
                     <?php
                     $TotalEvaluationScore = 0;
                     if ($totalPevaluation > 0) {
                        while ($row = mysqli_fetch_array($rowsPevaluation)) {

                           $pEvaluationMasterId = stripslashes($row['pEvaluationMasterId']);
                           $hospitalSiteTitle = $row['hospitalSiteTitle'];
                           $firstName = stripslashes($row['firstName']);
                           $lastName = stripslashes($row['lastName']);
                           $Rankname = stripslashes($row['Rankname']);
                           $evaluationDate = stripslashes($row['evaluationDate']);
                           $evaluationDate = date('m/d/Y', strtotime($evaluationDate));
                           $Rotationname = stripslashes($row['Rotationname']);
                           $clinicianfname = stripslashes($row['clinicianfname']);
                           $clinicianlname = stripslashes($row['clinicianlname']);
                           $clinicianfullname = $clinicianfname . ' ' . $clinicianlname;
                           $whoPreceptorStoodOutToYouQuestion = $objPevaluation->GetWhoPreceptorStoodOutToYouQuestion($pEvaluationMasterId);
                           $WhoDidNotMeetYourExpectionQuestion = $objPevaluation->GetWhoDidNotMeetYourExpectionQuestion($pEvaluationMasterId);
                           $GetCIevaluationScore = $objPevaluation->GetEvaluationScore($pEvaluationMasterId);
                           $EvaluationScore = $GetCIevaluationScore['EvaluationScore'];

                           $TotalEvaluationScore += $EvaluationScore;
                     ?>
                           <tr>
                              <td><?php echo ($firstName); ?></td>
                              <td><?php echo ($lastName); ?></td>
                              <td><?php echo ($Rankname); ?></td>
                              <td><?php echo wordwrap($Rotationname, 15, "<br>\n"); ?></td>
                              <td><?php echo ($hospitalSiteTitle); ?></td>
                              <td><?php if ($evaluationDate != '' && $evaluationDate != '01/01/1970' && $evaluationDate != '11/30/-0001') {
                                       echo ($evaluationDate);
                                    } ?></td>
                              <td align="center"><?php echo $EvaluationScore; ?></td>
                              <td><?php echo wordwrap($whoPreceptorStoodOutToYouQuestion, 15, "<br>\n"); ?></td>
                              <td><?php echo wordwrap($WhoDidNotMeetYourExpectionQuestion, 15, "<br>\n"); ?></td>
                           </tr>
                     <?php }
                     } ?>

                  </tbody>

                  <tfoot>
                     <tr>
                        <?php
                        $AvgEvaluationScore = 0;
                        if ($totalPevaluation > 0)
                           $AvgEvaluationScore = $TotalEvaluationScore / $totalPevaluation;
                        ?>
                        <td colspan=6 scope="row" align="right">Totals</td>
                        <td align="center"><?php echo number_format((float)$AvgEvaluationScore, 2, '.', ''); ?></td>
                        <td></td>
                        <td></td>

                     </tr>
                  </tfoot>
               </table>
            </div>
         <?php
            break;

         case "Dr_Points":
            echo "<b>Dr.Interaction</b><br>";
         ?>
            <div class="container-fluid">
               <div class="row">
                  <div class="form-group">
                     <?php if ($startDate && $endDate) { ?>
                        <div class="col-sm-4"><b>Start Date: </b><?php echo date('m/d/Y', strtotime($startDate)); ?> <br> <b>End Date: </b><?php echo date('m/d/Y', strtotime($endDate)); ?><br /> <b>Rotation: </b><?php echo ($mainRotationName); ?> </div>
                     <?php } elseif ($startDate) { ?>
                        <div class="col-sm-3"><b>Start Date: </b><?php echo ($startDate); ?></div>
                     <?php } elseif ($endDate) { ?>
                        <div class="col-sm-3"><b>End Date: </b><?php echo ($endDate); ?></div>
                     <?php } elseif ($rotationId) { ?>
                        <div class="col-sm-3"><b>Rotation: </b><?php echo ($mainRotationName); ?></div>
                     <?php } elseif ($location_hidden) { ?>
                        <div class="col-sm-3"><b>Location: </b><?php echo ($location_hidden); ?></div>
                     <?php }  ?>
                     <?php if ($cbosemester != '') { ?>
                        <div class="col-sm-4"><b>Semester Name: </b><?php echo ($semesterName); ?> </div>
                     <?php } ?>
                     <?php if ($subcborotation != '') { ?>
                        <div class="col-sm-5"><b>Hospital Site: </b><?php echo ($rotationName); ?> </div>
                     <?php } ?>
                     <?php if ($individual_student) { ?>
                        <div class="col-sm-3"><b>Student Name: </b><?php echo ($studentFullname); ?></div>
                     <?php }  ?>
                     <?php if ($student_rank) { ?>
                        <div class="col-sm-3"><b>Rank Name: </b><?php echo ($rankName); ?></div>
                     <?php }  ?>
                  </div>
               </div>
            </div>
            <br>
            <div class="row">

               <table id="drInteraction" class="table table-bordered dt-responsive nowrap table-hover " cellspacing="0" width="100%">
                  <?php
                  $objInteraction = new clsInteraction();
                  $totalInteraction = 0;
                  if (
                     isset($_POST['cboreporttype']) || ($_POST['cborotation']) || ($_POST['cboindividualstudent'])
                     || ($_POST['cbostudentrank']) || ($_POST['cboevaluator']) || ($_POST['cbolocation'])
                     || ($_POST['cbohospitalsite']) || ($_POST['startDate']) || ($_POST['endDate'])
                  ) {

                     $rowsInteraction = $objInteraction->GetStudentInteractionDetailsForreport($currentSchoolId, $rotationId, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder, $cbosemester, $subcborotation);
                     // $evaluator = $loggedClinicianId;
                     // $rowsInteraction = $objInteraction->GetStudentInteractionDetailsForClinicianreport( $currentSchoolId, $rotationId, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder, $cbosemester, $subcborotation );
                  }
                  if ($rowsInteraction != '') {
                     $totalInteraction = mysqli_num_rows($rowsInteraction);
                  } else {
                     echo "<b>Dr.Interaction Not Found.</b>";
                     exit;
                  }

                  ?>
                  <thead>
                     <tr>
                        <th>First Name</th>
                        <th>Last Name</th>
                        <th>Rank</th>
                        <th>Rotation</th>
                        <th>Clinician</th>
                        <th>Interaction Date</th>
                        <th>School Signed</th>
                        <th>CI Signed</th>
                        <th>Points</th>
                        <th>Time Spent<br>
                           (In Minutes)
                        </th>
                        <th>Student<br>comment</th>
                        <th>Clinican<br>comment</th>
                        <th>School<br>comment</th>
                     </tr>
                  </thead>
                  <tbody>
                     <?php
                     $TotalpointsAwarded = 0;
                     $TotaltimeSpent = 0;
                     $TotatlHours = 0;
                     if ($totalInteraction > 0) {
                        while ($row = mysqli_fetch_array($rowsInteraction)) {

                           $interactionId = stripslashes($row['interactionId']);
                           $firstName = stripslashes($row['firstName']);
                           $lastName = stripslashes($row['lastName']);

                           $pointsAwarded = stripslashes($row['pointsAwarded']);
                           $timeSpent = stripslashes($row['timeSpent']);
                           $timeSpent = (int)($timeSpent);
                           $Rankname = stripslashes($row['rank']);
                           $interactionDate = stripslashes($row['interactionDate']);
                           $interactionSchoolDate = stripslashes($row['interactionSchoolDate']);
                           $interactionSchoolDate = date('m/d/Y', strtotime($interactionSchoolDate));
                           $interactionClinicianDate = stripslashes($row['interactionClinicianDate']);
                           $interactionClinicianDate = date('m/d/Y', strtotime($interactionClinicianDate));
                           $Rotationname = stripslashes($row['rotationname']);
                           $interactionSummary = stripslashes($row['interactionSummary']);
                           $clinicianSummary = stripslashes($row['clinicianSummary']);
                           $schoolSummary = stripslashes($row['schoolSummary']);
                           $clinicianfname = stripslashes($row['clinicianfname']);
                           $clinicianlname = stripslashes($row['clinicianlname']);
                           $clinicianfullname = $clinicianfname . ' ' . $clinicianlname;

                           if ($pointsAwarded > 0) {
                              $TotalpointsAwarded += $pointsAwarded;
                           }

                           if ($timeSpent > 0) {
                              $TotaltimeSpent += $timeSpent;
                              $Totalminutes = $TotaltimeSpent * 60;
                              $TotatlHours = $Totalminutes / 60;
                           }

                     ?>
                           <tr>
                              <td><?php echo ($firstName); ?></td>
                              <td><?php echo ($lastName); ?></td>
                              <td><?php echo ($Rankname); ?></td>
                              <td><?php echo ($Rotationname); ?></td>
                              <td><?php echo ($clinicianfullname); ?></td>
                              <td><?php echo (date('m/d/Y', strtotime($interactionDate))); ?></td>
                              <td><?php if ($interactionSchoolDate != '' && $interactionSchoolDate != '0000-00-00 00:00:00' && $interactionSchoolDate != '11/30/-0001') {
                                       echo ($interactionSchoolDate);
                                    } ?></td>
                              <td><?php if ($interactionClinicianDate != '' && $interactionClinicianDate != '0000-00-00 00:00:00' && $interactionClinicianDate != '11/30/-0001') {
                                       echo ($interactionClinicianDate);
                                    } ?></td>
                              <td align="center"><?php echo ($pointsAwarded); ?></td>
                              <td align="center"><?php echo ($timeSpent); ?></td>
                              <td style="white-space: normal; word-wrap: break-word; background:white"><?php echo $interactionSummary; ?></td>
                              <td style="white-space: normal; word-wrap: break-word; background:white"><?php echo $clinicianSummary; ?></td>
                              <td style="white-space: normal; word-wrap: break-word; background:white"><?php echo $schoolSummary; ?></td>
                           </tr>
                     <?php }
                     } ?>
                     <tr>
                        <?php
                        $AvgTotaltimeSpent = $TotaltimeSpent / 60;
                        ?>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td align="right"><b>Total:</b></td>
                        <td align="center"><?php echo $TotalpointsAwarded; ?></td>
                        <td align="center">
                           <?php echo number_format((float)$TotatlHours, 2, '.', ''); ?>
                        </td>
                        <td></td>
                        <td></td>
                        <td></td>
                     </tr>
                  </tbody>
               </table>
            </div>
         <?php
            break;
         case "ClinicalSiteUnit":
            echo "<b>Clinical Site Unit</b><br>";
         ?>
            <div class="container-fluid">
               <div class="row">
                  <div class="form-group">
                     <?php if ($startDate && $endDate) { ?>
                        <div class="col-sm-4"><b>Start Date: </b><?php echo date('m/d/Y', strtotime($startDate)); ?> <br> <b>End Date: </b><?php echo date('m/d/Y', strtotime($endDate)); ?><br /> <b>Rotation: </b><?php echo ($mainRotationName); ?> </div>
                     <?php } else if ($startDate) { ?>
                        <div class="col-sm-3"><b>Start Date: </b><?php echo ($startDate); ?></div>
                     <?php } elseif ($endDate) { ?>
                        <div class="col-sm-3"><b>End Date: </b><?php echo ($endDate); ?></div>
                     <?php } elseif ($rotationId) { ?>
                        <div class="col-sm-3"><b>Rotation: </b><?php echo ($mainRotationName); ?></div>
                     <?php } elseif ($location_hidden) { ?>
                        <div class="col-sm-3"><b>Location: </b><?php echo ($location_hidden); ?></div>
                     <?php }  ?>
                     <?php if ($individual_student) { ?>
                        <div class="col-sm-3"><b>Student Name: </b><?php echo ($studentFullname); ?></div>
                     <?php }  ?>
                     <?php if ($student_rank) { ?>
                        <div class="col-sm-3"><b>Rank Name: </b><?php echo ($rankName); ?></div>
                     <?php }  ?>
                  </div>
               </div>
            </div>
            <br>
            <div class="row">
               <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                  <?php
                  $objInteraction = new clsInteraction();
                  $totalClinicalSiteUnit = 0;
                  if (
                     isset($_POST['cboreporttype']) || ($_POST['cborotation']) || ($_POST['cboindividualstudent'])
                     || ($_POST['cbostudentrank']) || ($_POST['cboevaluator']) || ($_POST['cbolocation'])
                     || ($_POST['cbohospitalsite']) || ($_POST['startDate']) || ($_POST['endDate'])
                  ) {

                     $rowsInteraction = $objInteraction->GetClinicalSiteUnitDetailsForreport(
                        $currentSchoolId,
                        $rotationId,
                        $individual_student,
                        $student_rank,
                        $evaluator,
                        $school_location,
                        $hospital_site,
                        $startDate,
                        $endDate,
                        $selTopicId,
                        $AscDesc,
                        $sordorder
                     );
                  }
                  if ($rowsInteraction != '') {
                     $totalClinicalSiteUnit = mysqli_num_rows($rowsInteraction);
                  } else {
                     echo "<b>Clinical Site Unit Not Found.</b>";
                     exit;
                  }

                  ?>
                  <thead>
                     <tr>
                        <th>First Name</th>
                        <th>Last Name</th>
                        <th>Interaction Date</th>
                        <th>Clinical Instructor</th>
                        <th>Clinician Signature </th>
                        <th>School Signature</th>
                        <th>Clinical Sites Unit </th>
                        <th>Time Spent</th>
                        <th>Points Awarded</th>
                     </tr>
                  </thead>
                  <tbody>
                     <?php
                     $TotalpointsAwarded = 0;
                     $TotaltimeSpent = 0;
                     if ($totalClinicalSiteUnit > 0) {
                        while ($row = mysqli_fetch_array($rowsInteraction)) {

                           $interactionId = stripslashes($row['interactionId']);
                           $firstName = stripslashes($row['firstName']);
                           $lastName = stripslashes($row['lastName']);

                           $pointsAwarded = stripslashes($row['pointsAwarded']);
                           $timeSpent = stripslashes($row['timeSpent']);
                           $Rankname = stripslashes($row['rank']);
                           $interactionDate = stripslashes($row['interactionDate']);
                           $interactionSchoolDate = stripslashes($row['interactionSchoolDate']);
                           $interactionSchoolDate = date('m/d/Y', strtotime($interactionSchoolDate));
                           $interactionClinicianDate = stripslashes($row['interactionClinicianDate']);
                           $interactionClinicianDate = date('m/d/Y', strtotime($interactionClinicianDate));
                           $hospitalname = stripslashes($row['hospitalname']);
                           $clinicianfname = stripslashes($row['clinicianfname']);
                           $clinicianlname = stripslashes($row['clinicianlname']);
                           $clinicianfullname = $clinicianfname . ' ' . $clinicianlname;

                           if ($pointsAwarded > 0)
                              $TotalpointsAwarded += $pointsAwarded;

                           if ($timeSpent > 0)
                              $TotaltimeSpent += $timeSpent;

                     ?>
                           <tr>
                              <td><?php echo ($firstName); ?></td>
                              <td><?php echo ($lastName); ?></td>
                              <td><?php echo (date('m/d/Y', strtotime($interactionDate))); ?></td>
                              <td><?php echo ($clinicianfullname); ?></td>
                              <td><?php if ($interactionClinicianDate != '01/01/1970' && $interactionClinicianDate != '0000-00-00 00:00:00' && $interactionClinicianDate != '11/30/-0001') {
                                       echo ($interactionClinicianDate);
                                    } ?></td>
                              <td><?php if ($interactionSchoolDate != '01/01/1970' && $interactionSchoolDate != '0000-00-00 00:00:00' && $interactionSchoolDate != '11/30/-0001') {
                                       echo ($interactionSchoolDate);
                                    } ?></td>
                              <td><?php echo ($hospitalname); ?></td>
                              <td align="center"><?php echo ($timeSpent); ?></td>
                              <td align="center"><?php echo ($pointsAwarded); ?></td>
                           </tr>
                     <?php }
                     } ?>
                     <tr>
                        <?php

                        $AvgTotaltimeSpent = $TotaltimeSpent ? $TotaltimeSpent / 60 : 0;
                        ?>
                        <td colspan="7" align="right"><b>Total:</b></td>
                        <td align="center">
                           <?php echo sprintf("%02d:%02d", floor($TotaltimeSpent / 60), $TotaltimeSpent % 60); ?>
                        </td>
                        <td align="center"><?php echo $TotalpointsAwarded; ?></td>
                     <tr>
                  </tbody>
               </table>
            </div>
         <?php
            break;
         case "Equipments":
            echo "<b>Equipments</b><br>";
         ?>
            <div class="container-fluid">
               <div class="row">
                  <div class="form-group">
                     <?php if ($startDate && $endDate) { ?>
                        <div class="col-sm-4"><b>Start Date: </b><?php echo date('m/d/Y', strtotime($startDate)); ?> <br> <b>End Date: </b><?php echo date('m/d/Y', strtotime($endDate)); ?><br /> <b>Rotation: </b><?php echo ($mainRotationName); ?> </div>
                     <?php } elseif ($startDate) { ?>
                        <div class="col-sm-3"><b>Start Date: </b><?php echo ($startDate); ?></div>
                     <?php } elseif ($endDate) { ?>
                        <div class="col-sm-3"><b>End Date: </b><?php echo ($endDate); ?></div>
                     <?php } elseif ($rotationId) { ?>
                        <div class="col-sm-3"><b>Rotation: </b><?php echo ($mainRotationName); ?></div>
                     <?php } elseif ($location_hidden) { ?>
                        <div class="col-sm-3"><b>Location: </b><?php echo ($location_hidden); ?></div>
                     <?php }  ?>
                     <?php if ($cbosemester != '') { ?>
                        <div class="col-sm-4"><b>Semester Name: </b><?php echo ($semesterName); ?> </div>
                     <?php } ?>
                     <?php if ($subcborotation != '') { ?>
                        <div class="col-sm-5"><b>Hospital Site: </b><?php echo ($rotationName); ?> </div>
                     <?php } ?>
                     <?php if ($individual_student) { ?>
                        <div class="col-sm-3"><b>Student Name: </b><?php echo ($studentFullname); ?></div>
                     <?php }  ?>
                     <?php if ($student_rank) { ?>
                        <div class="col-sm-3"><b>Rank Name: </b><?php echo ($rankName); ?></div>
                     <?php }  ?>
                  </div>
               </div>
            </div>
            <br>
            <div class="row">
               <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                  <?php
                  $objEquipment = new clsEquipment();
                  $totalEquipment = 0;
                  if (
                     isset($_POST['cboreporttype']) || ($_POST['cborotation']) || ($_POST['cboindividualstudent'])
                     || ($_POST['cbostudentrank']) || ($_POST['cboevaluator']) || ($_POST['cbolocation'])
                     || ($_POST['cbohospitalsite']) || ($_POST['startDate']) || ($_POST['endDate'])
                  ) {

                     $rowsEquipment = $objEquipment->GetStudentEquipmentDetailsForreport($currentSchoolId, $rotationId, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder, $cbosemester, $subcborotation);
                  }
                  if ($rowsEquipment != '') {
                     $totalEquipment = mysqli_num_rows($rowsEquipment);
                  } else {
                     echo "<b>CI Equipments Not Found.</b>";
                     exit;
                  }

                  ?>
                  <thead>
                     <tr>
                        <th>First Name</th>
                        <th>Last Name</th>
                        <th>Ranking</th>
                        <th>Rotation</th>
                        <th>Supervised By</th>
                        <th>Equipment Used</th>
                        <th>Equipment Usage Date</th>
                     </tr>
                  </thead>
                  <tbody>
                     <?php
                     if ($totalEquipment > 0) {
                        while ($row = mysqli_fetch_array($rowsEquipment)) {

                           $studentEquipmentMasterId = stripslashes($row['studentEquipmentMasterId']);
                           $firstName = stripslashes($row['firstName']);
                           $lastName = stripslashes($row['lastName']);
                           $Rankname = stripslashes($row['Rankname']);
                           $equipmentUsageDate = stripslashes($row['equipmentUsageDate']);
                           $equipmentUsageDate = date('m/d/Y', strtotime($equipmentUsageDate));
                           $Rotationname = stripslashes($row['rotationname']);
                           $clinicianfname = stripslashes($row['clinicianfname']);
                           $clinicianlname = stripslashes($row['clinicianlname']);
                           $clinicianfullname = $clinicianfname . ' ' . $clinicianlname;
                     ?>
                           <tr>
                              <td><?php echo ($firstName); ?></td>
                              <td><?php echo ($lastName); ?></td>
                              <td><?php echo ($Rankname); ?></td>
                              <td><?php echo ($Rotationname); ?></td>
                              <td><?php echo ($clinicianfullname); ?></td>
                              <td><?php echo ('-'); ?></td>
                              <td><?php if ($equipmentUsageDate != '' && $equipmentUsageDate != '01/01/1970' && $equipmentUsageDate != '11/30/-0001') {
                                       echo ($equipmentUsageDate);
                                    } ?></td>
                           </tr>
                     <?php }
                     } ?>
                  </tbody>
               </table>
            </div>
         <?php
            break;
         case "Formative":
            echo "<b>Formative Evaluation</b><br>";
         ?>
            <div class="container-fluid">
               <div class="row">
                  <div class="form-group">
                     <?php if ($startDate && $endDate) { ?>
                        <div class="col-sm-4"><b>Start Date: </b><?php echo date('m/d/Y', strtotime($startDate)); ?> <br> <b>End Date: </b><?php echo date('m/d/Y', strtotime($endDate)); ?><br /> <b>Rotation: </b><?php echo ($mainRotationName); ?> </div>
                     <?php } elseif ($startDate) { ?>
                        <div class="col-sm-3"><b>Start Date: </b><?php echo ($startDate); ?></div>
                     <?php } elseif ($endDate) { ?>
                        <div class="col-sm-3"><b>End Date: </b><?php echo ($endDate); ?></div>
                     <?php } elseif ($rotationId) { ?>
                        <div class="col-sm-3"><b>Rotation: </b><?php echo ($mainRotationName); ?></div>
                     <?php } elseif ($location_hidden) { ?>
                        <div class="col-sm-3"><b>Location: </b><?php echo ($location_hidden); ?></div>
                     <?php }  ?>
                     <?php if ($cbosemester != '') { ?>
                        <div class="col-sm-4"><b>Semester Name: </b><?php echo ($semesterName); ?> </div>
                     <?php } ?>
                     <?php if ($subcborotation != '') { ?>
                        <div class="col-sm-5"><b>Hospital Site: </b><?php echo ($rotationName); ?> </div>
                     <?php } ?>
                     <?php if ($individual_student) { ?>
                        <div class="col-sm-3"><b>Student Name: </b><?php echo ($studentFullname); ?></div>
                     <?php }  ?>
                     <?php if ($student_rank) { ?>
                        <div class="col-sm-3"><b>Rank Name: </b><?php echo ($rankName); ?></div>
                     <?php }  ?>
                  </div>
               </div>
            </div>
            <br>
            <div class="row">
               <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                  <?php
                  $objFormative = new clsFormative();
                  $totalFormative = 0;
                  if (
                     isset($_POST['cboreporttype']) || ($_POST['cborotation']) || ($_POST['cboindividualstudent'])
                     || ($_POST['cbostudentrank']) || ($_POST['cboevaluator']) || ($_POST['cbolocation'])
                     || ($_POST['cbohospitalsite']) || ($_POST['startDate']) || ($_POST['endDate'])
                  ) {

                     $rowsFormative = $objFormative->GetStudentFormativeDetailsForreport($currentSchoolId, $rotationId, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder, $cbosemester, $subcborotation);
                  }
                  if ($rowsFormative != '') {
                     $totalFormative = mysqli_num_rows($rowsFormative);
                  } else {
                     echo "<b>Formative Not Found.</b>";
                     exit;
                  }

                  ?>
                  <thead>
                     <tr>
                        <th>First Name</th>
                        <th>Last Name</th>
                        <th>Ranking</th>
                        <th>Signature</th>
                        <th>Rotation</th>
                        <th>Evaluator</th>
                        <th>Eval Date</th>
                        <th>Phase</th>
                        <th>Unsatisfactory</th>
                        <th>Unsatisfactory Behavior Comments</th>
                        <th>Suggestions</th>
                     </tr>
                  </thead>
                  <tbody>
                     <?php if ($totalFormative > 0) {
                        while ($row = mysqli_fetch_array($rowsFormative)) {
                           $studentId = '';
                           $studentId = $row[0];
                           $studentFormativeMasterId = stripslashes($row['studentFormativeMasterId']);
                           $firstName = stripslashes($row['firstName']);
                           $lastName = stripslashes($row['lastName']);
                           $rank = stripslashes($row['title']);
                           $rotationname = stripslashes($row['rotationname']);
                           $studentSignature = stripslashes($row['dateOfStudentSignature']);
                           if ($studentSignature != '0000-00-00 00:00:00') {
                              $studentSignature = date('m/d/Y', strtotime($studentSignature));
                           } else {
                              $studentSignature = "-";
                           }
                           $clinicianfname = stripslashes($row['clinicianfname']);
                           $clinicianlname = stripslashes($row['clinicianlname']);
                           $clinicianfullname = $clinicianfname . ' ' . $clinicianlname;
                           $evaluationDate = stripslashes($row['evaluationDate']);

                           $rowsFormativeScore = $objFormative->GetFormativeScore($studentFormativeMasterId);
                           $FormativeScore = $rowsFormativeScore['FormativeScore'];

                           $getUnsatisfyBehaviourComments = $objFormative->GetUnsatisfyComments($studentFormativeMasterId);
                           $UnsatisfactoryBehaviorComments = $getUnsatisfyBehaviourComments['schoolFormativeOptionAnswerText'];

                           $getSuggestionsComments = $objFormative->GetSuggestionsComments($studentFormativeMasterId);
                           $SuggestionsComment = $getSuggestionsComments['SuggestionsComment'];
                     ?>
                           <tr>
                              <td><?php echo ($firstName); ?></td>
                              <td><?php echo ($lastName); ?></td>
                              <td><?php echo ($rank); ?></td>
                              <td style="text-align:center"><?php echo ($studentSignature); ?></td>
                              <td><?php echo ($rotationname); ?></td>
                              <td><?php echo ($clinicianfullname); ?></td>
                              <td><?php echo (date('m/d/Y', strtotime($evaluationDate))); ?></td>
                              <td><?php echo number_format((float)$FormativeScore, 2, '.', '');  ?></td>
                              <td><?php  ?></td>
                              <td><?php echo $UnsatisfactoryBehaviorComments; ?></td>
                              <td><?php echo $SuggestionsComment; ?></td>
                           </tr>
                     <?php          }
                     } ?>
                  </tbody>
               </table>
            </div>
         <?php
            break;
         case "Midterm":
            echo "<b>Midterm Evaluation</b><br>";
         ?>
            <div class="container-fluid">
               <div class="row">
                  <div class="form-group">
                     <?php if ($startDate && $endDate) { ?>
                        <div class="col-sm-4"><b>Start Date: </b><?php echo date('m/d/Y', strtotime($startDate)); ?> <br> <b>End Date: </b><?php echo date('m/d/Y', strtotime($endDate)); ?><br /> <b>Rotation: </b><?php echo ($mainRotationName); ?> </div>
                     <?php } elseif ($startDate) { ?>
                        <div class="col-sm-3"><b>Start Date: </b><?php echo ($startDate); ?></div>
                     <?php } elseif ($endDate) { ?>
                        <div class="col-sm-3"><b>End Date: </b><?php echo ($endDate); ?></div>
                     <?php } elseif ($rotationId) { ?>
                        <div class="col-sm-3"><b>Rotation: </b><?php echo ($mainRotationName); ?></div>
                     <?php } elseif ($location_hidden) { ?>
                        <div class="col-sm-3"><b>Location: </b><?php echo ($location_hidden); ?></div>
                     <?php }  ?>
                     <?php if ($cbosemester != '') { ?>
                        <div class="col-sm-4"><b>Semester Name: </b><?php echo ($semesterName); ?> </div>
                     <?php } ?>
                     <?php if ($subcborotation != '') { ?>
                        <div class="col-sm-5"><b>Hospital Site: </b><?php echo ($rotationName); ?> </div>
                     <?php } ?>
                     <?php if ($individual_student) { ?>
                        <div class="col-sm-3"><b>Student Name: </b><?php echo ($studentFullname); ?></div>
                     <?php }  ?>
                     <?php if ($student_rank) { ?>
                        <div class="col-sm-3"><b>Rank Name: </b><?php echo ($rankName); ?></div>
                     <?php }  ?>
                  </div>
               </div>
            </div>
            <br>
            <div class="row">
               <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                  <?php
                  $objMidterm = new clsMidterm();
                  $totalMidterm = 0;
                  if (
                     isset($_POST['cboreporttype']) || ($_POST['cborotation']) || ($_POST['cboindividualstudent'])
                     || ($_POST['cbostudentrank']) || ($_POST['cboevaluator']) || ($_POST['cbolocation'])
                     || ($_POST['cbohospitalsite']) || ($_POST['startDate']) || ($_POST['endDate'])
                  ) {

                     $rowsMidterm = $objMidterm->GetStudentMidtermDetailsForreport($currentSchoolId, $rotationId, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder, $cbosemester, $subcborotation);
                  }
                  if ($rowsMidterm != '') {
                     $totalMidterm = mysqli_num_rows($rowsMidterm);
                  } else {
                     echo "<b>Midterm Not Found.</b>";
                     exit;
                  }

                  ?>
                  <thead>
                     <tr>
                        <th>First Name</th>
                        <th>Last Name</th>
                        <th>Ranking</th>
                        <th>Signature</th>
                        <th>Rotation</th>
                        <th>Evaluator</th>
                        <th>Eval Date</th>
                        <th>Absences</th>
                        <th>Tardy</th>
                        <th>Succeeding</th>
                        <th>Progressing</th>
                        <th>Unsatisfactory</th>
                     </tr>
                  </thead>
                  <tbody>
                     <?php if ($totalMidterm > 0) {
                        while ($row = mysqli_fetch_array($rowsMidterm)) {
                           $studentId = '';
                           $studentId = $row[0];
                           $studentMidtermMasterId = $row['studentMidtermMasterId'];
                           $firstName = stripslashes($row['firstName']);
                           $lastName = stripslashes($row['lastName']);
                           $rank = stripslashes($row['title']);
                           $rotationname = stripslashes($row['rotationname']);
                           $studentSignature = stripslashes($row['dateOfStudentSignature']);
                           $clinicianfname = stripslashes($row['clinicianfname']);
                           $clinicianlname = stripslashes($row['clinicianlname']);
                           $clinicianfullname = $clinicianfname . ' ' . $clinicianlname;
                           $evaluationDate = stripslashes($row['evaluationDate']);
                           $studentSignature = date('m/d/Y', strtotime($studentSignature));
                           $absence = stripslashes($row['absence']);
                           $daysTardy = stripslashes($row['daysTardy']);


                           $rowsMidtermSuccedingCount = $objMidterm->GetSucceedingCount($studentMidtermMasterId);
                           if ($rowsMidtermSuccedingCount != '') {
                              $totalMidtermSuccedingCount = mysqli_num_rows($rowsMidtermSuccedingCount);
                           }

                           $rowsMidtermProgressingCount = $objMidterm->GetProgressingCount($studentMidtermMasterId);
                           if ($rowsMidtermProgressingCount != '') {
                              $totalMidtermProgressingCount = mysqli_num_rows($rowsMidtermProgressingCount);
                           }

                           $rowsMidtermUnsatisfactoryCount = $objMidterm->GetUnsatisfactoryCount($studentMidtermMasterId);
                           if ($rowsMidtermUnsatisfactoryCount != '') {
                              $totalMidtermUnsatisfactoryCount = mysqli_num_rows($rowsMidtermUnsatisfactoryCount);
                           }

                     ?>
                           <tr>
                              <td><?php echo ($firstName); ?></td>
                              <td><?php echo ($lastName); ?></td>
                              <td><?php echo ($rank); ?></td>
                              <td>
                                 <?php
                                 if ($studentSignature != '' && $studentSignature != '01/01/1970' && $studentSignature != '11/30/-0001') {
                                    echo $studentSignature;
                                 }
                                 ?>
                              </td>
                              <td><?php echo ($rotationname); ?></td>
                              <td><?php echo ($clinicianfullname); ?></td>
                              <td><?php echo (date('m/d/Y', strtotime($evaluationDate))); ?></td>
                              <td style="text-align:center"><?php echo ($absence); ?></td>
                              <td style="text-align:center"><?php echo ($daysTardy); ?></td>
                              <td style="text-align:center"><?php echo ($totalMidtermSuccedingCount); ?></td>
                              <td style="text-align:center"><?php echo ($totalMidtermProgressingCount); ?></td>
                              <td style="text-align:center"><?php echo ($totalMidtermUnsatisfactoryCount); ?></td>
                           </tr>
                     <?php          }
                     } ?>
                  </tbody>
               </table>
            </div>
         <?php
            break;
         case "DailyEval":
            echo "<b>Daily/Weekly Evaluation</b><br>";
         ?>
            <div class="container-fluid">
               <div class="row">
                  <div class="form-group">
                     <?php if ($startDate && $endDate) { ?>
                        <div class="col-sm-4"><b>Start Date: </b><?php echo date('m/d/Y', strtotime($startDate)); ?> <br> <b>End Date: </b><?php echo date('m/d/Y', strtotime($endDate)); ?><br /> <b>Rotation: </b><?php echo ($mainRotationName); ?> </div>
                     <?php } elseif ($startDate) { ?>
                        <div class="col-sm-3"><b>Start Date: </b><?php echo ($startDate); ?></div>
                     <?php } elseif ($endDate) { ?>
                        <div class="col-sm-3"><b>End Date: </b><?php echo ($endDate); ?></div>
                     <?php } elseif ($rotationId) { ?>
                        <div class="col-sm-3"><b>Rotation: </b><?php echo ($mainRotationName); ?></div>
                     <?php } elseif ($location_hidden) { ?>
                        <div class="col-sm-3"><b>Location: </b><?php echo ($location_hidden); ?></div>
                     <?php }  ?>
                     <?php if ($cbosemester != '') { ?>
                        <div class="col-sm-4"><b>Semester Name: </b><?php echo ($semesterName); ?> </div>
                     <?php } ?>
                     <?php if ($subcborotation != '') { ?>
                        <div class="col-sm-5"><b>Hospital Site: </b><?php echo ($rotationName); ?> </div>
                     <?php } ?>
                     <?php if ($individual_student) { ?>
                        <div class="col-sm-3"><b>Student Name: </b><?php echo ($studentFullname); ?></div>
                     <?php }  ?>
                     <?php if ($student_rank) { ?>
                        <div class="col-sm-3"><b>Rank Name: </b><?php echo ($rankName); ?></div>
                     <?php }  ?>
                  </div>
               </div>
            </div>
            <br>
            <div class="row">
               <table id="dailyweeklyTable" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                  <?php
                  $objDailyEval = new clsDaily();
                  $totalDailyCount = 0;
                  if (
                     isset($_POST['cboreporttype']) || ($_POST['cborotation']) || ($_POST['cboindividualstudent'])
                     || ($_POST['cbostudentrank']) || ($_POST['cboevaluator']) || ($_POST['cbolocation'])
                     || ($_POST['cbohospitalsite']) || ($_POST['startDate']) || ($_POST['endDate'])
                  ) {

                     $getDailydetails = $objDailyEval->GetAllDailyweeklyEvalForClinicianReport($currentSchoolId, $rotationId, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder, $cbosemester, $subcborotation);
                     // $evaluator = $loggedClinicianId;
                     // $getDailydetails = $objDailyEval->GetAllDailyweeklyEvalForClinicianReport($currentSchoolId, $rotationId, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder, $cbosemester, $subcborotation);
                  }
                  if ($getDailydetails != '') {
                     $totalDailyCount = mysqli_num_rows($getDailydetails);
                  } else {
                     echo "<b>Daily/Weekly Not Found.</b>";
                     exit;
                  }

                  ?>
                  <thead>
                     <tr>
                        <th>First Name</th>
                        <th>Last Name</th>
                        <th>Ranking</th>
                        <th>Student<br>Signature</th>
                        <th>Rotation</th>
                        <th>Evaluator</th>
                        <th>Evaluator<br>Signature</th>
                        <th>Avg<br>Attendance</th>
                        <th>Avg<br>Student<br>Preparation</th>
                        <th>Avg<br>Profe.</th>
                        <th>Avg<br>Knowledge</th>
                        <th>Avg<br>Psychomotor</th>
                        <th>Avg<br>Organization</th>
                        <th>Total<br>Average</th>
                     </tr>
                  </thead>
                  <tbody>
                     <?php if ($totalDailyCount > 0) {
                        while ($row = mysqli_fetch_array($getDailydetails)) {
                           $rotationame = $row['title'];
                           $Ranktitle = $row['Ranktitle'];
                           $firstName = $row['firstName'];
                           $lastName = $row['lastName'];
                           $studentName = $firstName . ' ' . $lastName;
                           $clinicianFirstName = $row['clinicianFirstName'];
                           $clinicianLastName = $row['clinicianLastName'];
                           $clinicianName = $clinicianFirstName . ' ' . $clinicianLastName;
                           $studentDailyMasterId = $row['DailyEvalID'];
                           $evaluationDate = stripslashes($row['evaluationDate']);
                           if ($evaluationDate != '0000-00-00 00:00:00') {
                              $evaluationDate = date("m/d/Y", strtotime($evaluationDate));
                           } else {
                              $evaluationDate = "-";
                           }
                           $dateOfStudentSignature = stripslashes($row['dateOfStudentSignature']);
                           if ($dateOfStudentSignature != '0000-00-00 00:00:00') {
                              $dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $TimeZone);
                              $dateOfStudentSignature = date("m/d/Y", strtotime($dateOfStudentSignature));
                           } else {
                              $dateOfStudentSignature = "-";
                           }
                           $dateOfInstructorSignature = stripslashes($row['dateOfInstructorSignature']);
                           if ($dateOfInstructorSignature != '0000-00-00 00:00:00') {
                              $dateOfInstructorSignature = converFromServerTimeZone($dateOfInstructorSignature, $TimeZone);
                              $dateOfInstructorSignature = date("m/d/Y", strtotime($dateOfInstructorSignature));
                           } else {
                              $dateOfInstructorSignature = "-";
                           }
                           //For Avg
                           $firstSectionAvg = $row['firstSectionAvg'];
                           $secondSectionAvg = $row['secondSectionAvg'];
                           $thirdSectionAvg = $row['thirdSectionAvg'];
                           $fourthSectionAvg = $row['fourthSectionAvg'];
                           $fiveSectionAvg = $row['fiveSectionAvg'];
                           $sixSectionAvg = $row['sixSectionAvg'];
                           $totalAvg = $row['totalAvg'];

                     ?>
                           <tr>
                              <td><?php echo ($firstName); ?></td>
                              <td><?php echo ($lastName); ?></td>
                              <td><?php echo ($Ranktitle); ?></td>
                              <td><?php echo $dateOfStudentSignature; ?></td>
                              <td><?php echo ($rotationame); ?></td>
                              <td><?php echo $clinicianName; ?></td>
                              <td><?php echo $dateOfInstructorSignature; ?></td>
                              <td style="text-align: center"><?php echo number_format((float)$firstSectionAvg, 2, '.', ''); ?></td>
                              <td style="text-align: center"><?php echo number_format((float)$secondSectionAvg, 2, '.', ''); ?></td>
                              <td style="text-align: center"><?php echo number_format((float)$thirdSectionAvg, 2, '.', ''); ?></td>
                              <td style="text-align: center"><?php echo number_format((float)$fourthSectionAvg, 2, '.', ''); ?></td>
                              <td style="text-align: center"><?php echo number_format((float)$fiveSectionAvg, 2, '.', ''); ?></td>
                              <td style="text-align: center"><?php echo number_format((float)$sixSectionAvg, 2, '.', ''); ?></td>
                              <td style="text-align: center"><?php echo number_format((float)$totalAvg, 2, '.', ''); ?></td>
                           </tr>
                     <?php       }
                     }   ?>
                  </tbody>
               </table>
               <br>
            </div>
         <?php
            break;
         case "DailyDetail":
            echo "<b>Daily/Weekly Detail</b><br>";
         ?>
            <div class="container-fluid">
               <div class="row">
                  <div class="form-group">
                     <?php if ($startDate && $endDate) { ?>
                        <div class="col-sm-4"><b>Start Date: </b><?php echo date('m/d/Y', strtotime($startDate)); ?> <br> <b>End Date: </b><?php echo date('m/d/Y', strtotime($endDate)); ?><br /> <b>Rotation: </b><?php echo ($mainRotationName); ?> </div>
                     <?php } elseif ($startDate) { ?>
                        <div class="col-sm-3"><b>Start Date: </b><?php echo ($startDate); ?></div>
                     <?php } elseif ($endDate) { ?>
                        <div class="col-sm-3"><b>End Date: </b><?php echo ($endDate); ?></div>
                     <?php } elseif ($rotationId) { ?>
                        <div class="col-sm-3"><b>Rotation: </b><?php echo ($mainRotationName); ?></div>
                     <?php } elseif ($location_hidden) { ?>
                        <div class="col-sm-3"><b>Location: </b><?php echo ($location_hidden); ?></div>
                     <?php }  ?>
                     <?php if ($cbosemester != '') { ?>
                        <div class="col-sm-4"><b>Semester Name: </b><?php echo ($semesterName); ?> </div>
                     <?php } ?>
                     <?php if ($subcborotation != '') { ?>
                        <div class="col-sm-5"><b>Hospital Site: </b><?php echo ($rotationName); ?> </div>
                     <?php } ?>
                     <?php if ($individual_student) { ?>
                        <div class="col-sm-3"><b>Student Name: </b><?php echo ($studentFullname); ?></div>
                     <?php }  ?>
                     <?php if ($student_rank) { ?>
                        <div class="col-sm-3"><b>Rank Name: </b><?php echo ($rankName); ?></div>
                     <?php }  ?>
                  </div>
               </div>
            </div>
            <br>
            <div class="row margin_bottom_fifty">
               <table id="dailyWeeklyDetailDatatable" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0">
                  <?php
                  $objDailyEval = new clsDaily();
                  $totalDailyCount = 0;
                  if (
                     isset($_POST['cboreporttype']) || ($_POST['cborotation']) || ($_POST['cboindividualstudent'])
                     || ($_POST['cbostudentrank']) || ($_POST['cboevaluator']) || ($_POST['cbolocation'])
                     || ($_POST['cbohospitalsite']) || ($_POST['startDate']) || ($_POST['endDate'])
                  ) {

                     $getDailydetails = $objDailyEval->GetAllDailyweeklyEvalForReport($currentSchoolId, $rotationId, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder, $cbosemester, $subcborotation);
                     // $evaluator = $loggedClinicianId;
                     // $getDailydetails = $objDailyEval->GetAllDailyweeklyEvalForClinicianReport($currentSchoolId, $rotationId, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder, $cbosemester, $subcborotation);

                     $dailyWeeklyQuestions = [];
                     $commentSectionMasterId = 0;
                     $commentQuestionId = 0;
                     $dailyWeeklySections = $objDailyEval->GetSections($currentSchoolId);
                     while ($row = mysqli_fetch_array($dailyWeeklySections)) {
                        $dailyWeeklySectionId = $row['sectionMasterId'];
                        $sectionTitle = $row['title'];

                        $dailyWeeklyQuestionArray = [];
                        $dailyWeeklyQuestionArray['sectionMasterId'] = $dailyWeeklySectionId;
                        $dailyWeeklyQuestionArray['title'] = $sectionTitle;
                        $dailyWeeklyQuestionArray['dailyQuestionId'] = 0;
                        $dailyWeeklyQuestions[] = $dailyWeeklyQuestionArray;

                        $dailyWeeklyQuestionList = $objDailyEval->GetAllDailyQuestionMaster($currentSchoolId, $dailyWeeklySectionId);
                        while ($questionRow = mysqli_fetch_array($dailyWeeklyQuestionList)) {
                           $dailyWeeklyOptionText = $questionRow['optionText'];
                           $dailyWeeklyDailyQuestionId = $questionRow['dailyQuestionId'];
                           $dailyQuestionType = $questionRow['dailyQuestionType'];
                           if ($dailyQuestionType == 5) {
                              $commentSectionMasterId = $questionRow['sectionMasterId'];
                              $commentQuestionId = $questionRow['dailyQuestionId'];
                           }

                           if ($dailyQuestionType != 5) {
                              $dailyWeeklyQuestionArray = [];
                              $dailyWeeklyQuestionArray['sectionMasterId'] = 0;
                              $dailyWeeklyQuestionArray['dailyQuestionId'] = $dailyWeeklyDailyQuestionId;
                              $dailyWeeklyQuestionArray['title'] = $dailyWeeklyOptionText;
                              $dailyWeeklyQuestions[] = $dailyWeeklyQuestionArray;
                           }
                        }
                     }
                  }

                  //Replace comment question id in section array.
                  foreach ($dailyWeeklyQuestions as $key => $value) {
                     $sectionMasterId = $value['sectionMasterId'];
                     if ($sectionMasterId == $commentSectionMasterId)
                        $dailyWeeklyQuestions[$key]['dailyQuestionId'] = $commentQuestionId;
                  }

                  if ($getDailydetails != '') {
                     $totalDailyCount = mysqli_num_rows($getDailydetails);
                  } else {
                     echo "<b>Daily/Weekly Not Found.</b>";
                     exit;
                  }

                  ?>
                  <thead>
                     <tr id="filters">
                        <th class="select-filter">Student Name</th>
                        <th class="select-filter">Ranking</th>
                        <th class="select-filter">Rotation</th>
                        <?php
                        foreach ($dailyWeeklyQuestions as $dailyWeeklyQuestion) {
                           $title = $dailyWeeklyQuestion['title'];
                           if (strlen($title) > 20)
                              $title = substr($title, 0, 17) . '...';

                        ?>
                           <th title="<?php echo $dailyWeeklyQuestion['title']; ?>"><?php echo $title; ?></th>
                        <?php

                        }
                        ?>

                        <th>Avg<br>Attendance</th>
                        <th>Avg<br>Student<br>Preparation</th>
                        <th>Avg<br>Profe.</th>
                        <th>Avg<br>Knowledge</th>
                        <th>Avg<br>Psychomotor</th>
                        <th>Avg<br>Organization</th>
                        <th>Total<br>Average</th>
                     </tr>
                  </thead>
                  <tbody>
                     <?php
                     if ($totalDailyCount > 0) {
                        while ($row = mysqli_fetch_array($getDailydetails)) {
                           $rotationame = $row['title'];
                           $Ranktitle = $row['Ranktitle'];
                           $firstName = $row['firstName'];
                           $lastName = $row['lastName'];
                           $studentName = $firstName . ' ' . $lastName;
                           $clinicianFirstName = $row['clinicianFirstName'];
                           $clinicianLastName = $row['clinicianLastName'];
                           $clinicianName = $clinicianFirstName . ' ' . $clinicianLastName;
                           $studentDailyMasterId = $row['DailyEvalID'];
                           $evaluationDate = stripslashes($row['evaluationDate']);
                           if ($evaluationDate != '0000-00-00 00:00:00')
                              $evaluationDate = date("m/d/Y", strtotime($evaluationDate));
                           else
                              $evaluationDate = "-";

                           $dateOfStudentSignature = stripslashes($row['dateOfStudentSignature']);
                           if ($dateOfStudentSignature != '0000-00-00 00:00:00') {
                              $dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $TimeZone);
                              $dateOfStudentSignature = date("m/d/Y", strtotime($dateOfStudentSignature));
                           } else {
                              $dateOfStudentSignature = "-";
                           }

                           $dateOfInstructorSignature = stripslashes($row['dateOfInstructorSignature']);
                           $dateOfInstructorSignature = converFromServerTimeZone($dateOfInstructorSignature, $TimeZone);
                           $dateOfInstructorSignature = date("m/d/Y", strtotime($dateOfInstructorSignature));

                           //For Avg
                           $firstSectionAvg = $row['firstSectionAvg'];
                           $secondSectionAvg = $row['secondSectionAvg'];
                           $thirdSectionAvg = $row['thirdSectionAvg'];
                           $fourthSectionAvg = $row['fourthSectionAvg'];
                           $fiveSectionAvg = $row['fiveSectionAvg'];
                           $sixSectionAvg = $row['sixSectionAvg'];
                           $totalAvg = $row['totalAvg'];

                     ?>
                           <tr>
                              <td><?php echo ($studentName); ?></td>
                              <td><?php echo ($Ranktitle); ?></td>
                              <td><?php echo ($rotationame); ?></td>
                              <?php
                              foreach ($dailyWeeklyQuestions as $dailyWeeklyQuestion) {
                                 $dailyQuestionId = $dailyWeeklyQuestion['dailyQuestionId'];
                                 $sectionMasterId = $dailyWeeklyQuestion['sectionMasterId'];
                                 $getSelectedQuestionDetail = $objDailyEval->GetStudentDailyWeeklyDetail($studentDailyMasterId, $dailyQuestionId);
                                 $selectedOptionText = isset($getSelectedQuestionDetail['optionText']) ? $getSelectedQuestionDetail['optionText'] : '-';

                                 if ($sectionMasterId && $dailyQuestionId) {
                                    $selectedOptionText = isset($getSelectedQuestionDetail['studentOptionAnswerText']) ? $getSelectedQuestionDetail['studentOptionAnswerText'] : '-';

                                    if (strlen($selectedOptionText) > 50)
                                       $selectedOptionText = substr($selectedOptionText, 0, 47) . '...';
                                 }
                              ?>
                                 <td class="text-center" title="<?php echo $getSelectedQuestionDetail['studentOptionAnswerText']; ?>"><?php echo $selectedOptionText; ?></td>
                              <?php }

                              ?>
                              <td style="text-align: center"><?php echo number_format((float)$firstSectionAvg, 2, '.', ''); ?></td>
                              <td style="text-align: center"><?php echo number_format((float)$secondSectionAvg, 2, '.', ''); ?></td>
                              <td style="text-align: center"><?php echo number_format((float)$thirdSectionAvg, 2, '.', ''); ?></td>
                              <td style="text-align: center"><?php echo number_format((float)$fourthSectionAvg, 2, '.', ''); ?></td>
                              <td style="text-align: center"><?php echo number_format((float)$fiveSectionAvg, 2, '.', ''); ?></td>
                              <td style="text-align: center"><?php echo number_format((float)$sixSectionAvg, 2, '.', ''); ?></td>
                              <td style="text-align: center"><?php echo number_format((float)$totalAvg, 2, '.', ''); ?></td>
                           </tr>
                     <?php       }
                     }   ?>
                  </tbody>
               </table>
            </div>
         <?php
            break;
         case "IRR Report":
         ?>
            <div class="container-fluid">
               <div class="form-group">
                  <?php if ($startDate) { ?>
                     <div class="col-sm-3"><b>Start Date: </b><?php echo ($startDate); ?></div>
                  <?php } elseif ($endDate) { ?>
                     <div class="col-sm-3"><b>End Date: </b><?php echo ($endDate); ?></div>
                  <?php } elseif ($rotationId) { ?>
                     <div class="col-sm-3"><b>Rotation: </b><?php echo ($mainRotationName); ?></div>
                  <?php } elseif ($location_hidden) { ?>
                     <div class="col-sm-3"><b>Location: </b><?php echo ($location_hidden); ?></div>
                  <?php }  ?>
               </div>
            </div>
            <br>
            <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
               <?php
               $objIrr = new clsIrr();
               $totalIrr = 0;
               if (
                  isset($_POST['cboreporttype']) || ($_POST['cborotation']) || ($_POST['cboindividualstudent'])
                  || ($_POST['cbostudentrank']) || ($_POST['cboevaluator']) || ($_POST['cbolocation'])
                  || ($_POST['cbohospitalsite']) || ($_POST['startDate']) || ($_POST['endDate'])
               ) {

                  $rowsIrr = $objIrr->GetStudentJournalDetailsForIRR($currentSchoolId, $rotationId, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder);
               }
               if ($rowsIrr != '') {
                  $totalIrr = mysqli_num_rows($rowsIrr);
               } else {
                  echo "<b>IRR Not Found.</b>";
                  exit;
               }

               ?>
               <thead>
                  <tr>
                     <th>IRR Title</th>
                     <th>IRR Student</th>
                     <th>Rank</th>
                     <th>Course</th>
                     <th>Clinicians</th>
                     <th>Hospital Sites</th>
                     <th>Start Date</th>
                     <th>End Date</th>
                  </tr>
               </thead>
               <tbody>
                  <?php
                  if ($totalIrr > 0) {
                     while ($row = mysqli_fetch_array($rowsIrr)) {
                        $studentId = '';
                        $studentId = $row[0];
                        $irrMasterId = stripslashes($row['irrMasterId']);
                        $irrtitle = stripslashes($row['title']);
                        $firstName = stripslashes($row['firstName']);
                        $lastName = stripslashes($row['lastName']);
                        $CourseName = stripslashes($row['CourseName']);
                        $rank = stripslashes($row['rank']);
                        $schoolname = stripslashes($row['school']);
                        $irrStartDate = stripslashes($row['irrStartDate']);
                        $irrEndDate = stripslashes($row['irrEndDate']);

                        $cliniciancountrow = $objIrr->GetClinicianCount($irrMasterId);
                        $cliniciancount = stripslashes($cliniciancountrow['clinicianId']);
                        $hospitalcountrow = $objIrr->GetHospitalSiteCount($irrMasterId);
                        $hospitalSitecount = stripslashes($hospitalcountrow['hospitalSiteId']);
                        //unset($objIrr);	
                  ?>
                        <tr>
                           <td><?php echo ($irrtitle); ?></td>
                           <td><?php echo ('Student IRR'); ?></td>
                           <td><?php echo ('IRR'); ?></td>
                           <td><?php echo ($CourseName); ?></td>
                           <td><?php echo ($cliniciancount) ?></td>
                           <td><?php echo ($hospitalSitecount); ?></td>
                           <td><?php echo (date('m/d/Y', strtotime($irrStartDate))); ?></td>
                           <td><?php echo (date('m/d/Y', strtotime($irrEndDate))); ?></td>
                        </tr>
                  <?php
                     }
                  }
                  ?>
               </tbody>
            </table>
         <?php
            break;
         case "Journal":
            echo "<b>Journal</b><br>";
         ?>
            <div class="container-fluid">
               <div class="row">
                  <div class="form-group">
                     <?php if ($startDate && $endDate) { ?>
                        <div class="col-sm-4"><b>Start Date: </b><?php echo date('m/d/Y', strtotime($startDate)); ?> <br> <b>End Date: </b><?php echo date('m/d/Y', strtotime($endDate)); ?><br /> <b>Rotation: </b><?php echo ($mainRotationName); ?> </div>
                     <?php } elseif ($startDate) { ?>
                        <div class="col-sm-3"><b>Start Date: </b><?php echo ($startDate); ?></div>
                     <?php } elseif ($endDate) { ?>
                        <div class="col-sm-3"><b>End Date: </b><?php echo ($endDate); ?></div>
                     <?php } elseif ($rotationId) { ?>
                        <div class="col-sm-3"><b>Rotation: </b><?php echo ($mainRotationName); ?></div>
                     <?php } elseif ($location_hidden) { ?>
                        <div class="col-sm-3"><b>Location: </b><?php echo ($location_hidden); ?></div>
                     <?php }  ?>
                     <?php if ($cbosemester != '') { ?>
                        <div class="col-sm-4"><b>Semester Name: </b><?php echo ($semesterName); ?> </div>
                     <?php } ?>
                     <?php if ($subcborotation != '') { ?>
                        <div class="col-sm-5"><b>Hospital Site: </b><?php echo ($rotationName); ?> </div>
                     <?php } ?>
                     <?php if ($individual_student) { ?>
                        <div class="col-sm-3"><b>Student Name: </b><?php echo ($studentFullname); ?></div>
                     <?php }  ?>
                     <?php if ($student_rank) { ?>
                        <div class="col-sm-3"><b>Rank Name: </b><?php echo ($rankName); ?></div>
                     <?php }  ?>
                  </div>
               </div>
            </div>
            <br>
            <div class="row">
               <table id="journal" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                  <?php
                  $objJournal = new clsJournal();
                  $totalJournal = 0;
                  if (
                     isset($_POST['cboreporttype']) || ($_POST['cborotation']) || ($_POST['cboindividualstudent'])
                     || ($_POST['cbostudentrank']) || ($_POST['cboevaluator']) || ($_POST['cbolocation'])
                     || ($_POST['cbohospitalsite']) || ($_POST['startDate']) || ($_POST['endDate'])
                  ) {

                     $rowsJournal = $objJournal->GetStudentJournalDetailsForreport($currentSchoolId, $rotationId, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder, $cbosemester, $subcborotation);
                     // $evaluator = $loggedClinicianId;
                     // $rowsJournal = $objJournal->GetStudentJournalDetailsForClinicianreport($currentSchoolId, $rotationId, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder, $cbosemester, $subcborotation);
                  }
                  if ($rowsJournal != '') {
                     $totalJournal = mysqli_num_rows($rowsJournal);
                  } else {
                     echo "<b>Student Not Found.</b>";
                  }
                  unset($objJournal);
                  ?>
                  <thead>
                     <tr>
                        <th>First Name</th>
                        <th>Last Name</th>
                        <th>Ranking</th>
                        <th>Date</th>
                        <th>Hospital Site</th>
                        <th>Rotation</th>
                        <th>Student Entry</th>
                        <th>School Response</th>
                     </tr>
                  </thead>
                  <tbody>
                     <?php
                     if ($totalJournal > 0) {
                        while ($row = mysqli_fetch_array($rowsJournal)) {
                           $studentId = '';
                           $studentId = $row[0];
                           $firstName = stripslashes($row['firstName']);
                           $lastName = stripslashes($row['lastName']);
                           $fullName = $firstName . ' ' . $lastName;
                           $rank = stripslashes($row['rank']);
                           $schoolname = stripslashes($row['school']);
                           $journalDate = stripslashes($row['journalDate']);
                           $rotationname = stripslashes($row['rotationname']);
                           $hospitalname = stripslashes($row['hospitalname']);
                           $journalSchoolResponse = stripslashes($row['journalSchoolResponse']);
                           $journalStudentEntry = stripslashes($row['journalStudentEntry']);

                     ?>
                           <tr>
                              <td><?php echo ($firstName); ?></td>
                              <td><?php echo ($lastName); ?></td>
                              <td><?php echo ($rank); ?></td>
                              <td><?php echo (date('m/d/Y', strtotime($journalDate))); ?></td>
                              <td><?php echo ($hospitalname); ?></td>
                              <td><?php echo ($rotationname); ?></td>
                              <td style="white-space: normal; word-wrap: break-word; background:white"><?php echo ($journalStudentEntry); ?></td>
                              <td style="white-space: normal; word-wrap: break-word; background:white"><?php echo ($journalSchoolResponse); ?></td>
                           </tr>
                     <?php
                        }
                     }
                     ?>
                  </tbody>
               </table>
            </div><br>
         <?php
            break;
         case "Procidure_Count":
            echo "<b>Procedure Count</b><br>";
         ?>
            <div class="container-fluid">
               <div class="row">
                  <div class="form-group">
                     <?php if ($startDate && $endDate) { ?>
                        <div class="col-sm-4"><b>Start Date: </b><?php echo date('m/d/Y', strtotime($startDate)); ?> <br> <b>End Date: </b><?php echo date('m/d/Y', strtotime($endDate)); ?><?php if ($rotationId > 0) { ?> <br /> <b>Rotation: </b><?php echo ($mainRotationName);
                                                                                                                                                                                                                                                   } ?> </div>
                     <?php }
                     if ($cbosemester != '') { ?>
                        <div class="col-sm-3"><b>Semester Name: </b><?php echo ($semesterName); ?> </div>
                     <?php } ?>
                     <?php if ($subcborotation != '') { ?>
                        <div class="col-sm-5"><b>Hospital Site: </b><?php echo ($rotationName); ?> </div>
                     <?php } ?>
                     <?php if ($individual_student) { ?>
                        <div class="col-sm-4"><b>Student Name: </b><?php echo ($studentFullname); ?></div>
                     <?php }  ?>
                     <?php if ($student_rank) { ?>
                        <div class="col-sm-3"><b>Rank Name: </b><?php echo ($rankName); ?></div>
                     <?php }  ?>
                  </div>
               </div>
            </div>
            <br>
            <div class="row">
               <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                  <?php
                  $objcheckoff = new clscheckoff();
                  $totalprocedure = 0;
                  if (
                     isset($_POST['cboreporttype']) || ($_POST['cborotation']) || ($_POST['cboindividualstudent'])
                     || ($_POST['cbostudentrank']) || ($_POST['cboevaluator']) || ($_POST['cbolocation'])
                     || ($_POST['cbohospitalsite']) || ($_POST['startDate']) || ($_POST['endDate'])
                  ) {

                     if ($isActiveCheckoff == 1) {
                        $rowsprocedure = $objcheckoff->GetStudentProcedureDetailsForreport($currentSchoolId, $rotationId, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder, $cbosemester, $subcborotation);
                     } else if ($isActiveCheckoff == 2) {
                        $rowsprocedure = $objcheckoff->GetStudentProcedureDetailsForreportForUsaf($currentSchoolId, $rotationId, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder, $cbosemester, $subcborotation);
                     } else {
                        $rowsprocedure = $objcheckoff->GetStudentProcedureDetailsForreportForAdvance($currentSchoolId, $rotationId, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder, $cbosemester, $subcborotation);
                     }
                  }
                  if ($rowsprocedure != '') {
                     $totalprocedure = mysqli_num_rows($rowsprocedure);
                  } else {
                     echo "<b>Procidure Count Not Found.</b>";
                     exit;
                  }

                  ?>
                  <thead>
                     <tr>
                        <th>First Name</th>
                        <th>Last Name</th>
                        <th>Rank</th>
                        <th>Rotation</th>
                        <th>Id</th>
                        <th>Procedure Name</th>
                        <!-- <th>Evaluator</th> -->
                        <th>Proc.PointsAss.</th>
                        <th>Proc.PointsObs.</th>
                        <th>Proc.PointsPerf.</th>
                        <th>Procedure Date</th>
                     </tr>
                  </thead>
                  <tbody>
                     <?php
                     $TotalprocedurePointsAssist = 0;
                     $TotalprocedurePointsObserve = 0;
                     $TotalprocedurePointsPerform = 0;
                     $newStudentId = 0;
                     $totalProcedurePointsAssist = 0;
                     if ($totalprocedure > 0) {
                        while ($row = mysqli_fetch_array($rowsprocedure)) {

                           $studentId = stripslashes($row['studentId']);
                           $firstName = stripslashes($row['firstName']);
                           $lastName = stripslashes($row['lastName']);
                           $Rankname = stripslashes($row['Rankname']);
                           //$topic = stripslashes($row['schooltitle']);
                           $proceduteCountName = $row['proceduteCountName'];
                           $procedureCountsCode = $row['procedureCountsCode'];

                           $procedurePointsAssist = stripslashes($row['procedurePointsAssist']);
                           $procedurePointsObserve = stripslashes($row['procedurePointsObserve']);
                           $procedurePointsPerform = stripslashes($row['procedurePointsPerform']);
                           $procedureDate = stripslashes($row['procedureDate']);
                           $procedureDate = date('m/d/Y', strtotime($procedureDate));

                           //$checkoffDateTime = stripslashes($row['checkoffDateTime']);
                           $Rotationname = stripslashes($row['rotationname']);
                           $clinicianfname = stripslashes($row['clinicianfname']);
                           $clinicianlname = stripslashes($row['clinicianlname']);
                           $clinicianfullname = $clinicianfname . ' ' . $clinicianlname;

                           if ($procedurePointsAssist > 0)
                              $TotalprocedurePointsAssist += $procedurePointsAssist;

                           if ($procedurePointsObserve > 0)
                              $TotalprocedurePointsObserve += $procedurePointsObserve;

                           if ($procedurePointsPerform > 0)
                              $TotalprocedurePointsPerform += $procedurePointsPerform;

                           $pname = explode("-", $proceduteCountName);

                           $TotalCount = count($pname);
                           $substrs = $pname[0];

                           if ($isActiveCheckoff == 2) {
                              $shortTitlelen = strlen($proceduteCountName);

                              if ($shortTitlelen > 40) {
                                 $schoolproceduteCountName = substr($proceduteCountName, 0, 40);
                                 $schoolproceduteCountName .= '...';
                              } else {
                                 $schoolproceduteCountName = $proceduteCountName;
                              }
                           } else if ($isActiveCheckoff == 1) {
                              if ($TotalCount == 3) {
                                 $alllow = strtolower($substrs);

                                 $FirstQuestionName = '';
                                 $QuestionName = $pname[0];
                                 if (strpos($alllow, 'foundation') != false) {
                                    $FirstQuestionName = str_replace('PEF Foundation', ' ', $QuestionName);
                                 }
                                 if (strpos($alllow, 'activity ') != false) {
                                    $FirstQuestionName = str_replace('PEF Activity', ' ', $QuestionName);
                                 }
                                 $lastname = $pname[2];
                                 $Procedurename = $FirstQuestionName . '-' . $lastname;
                              } else {
                                 $QuestionName = $pname[0];
                                 if (strpos($alllow, 'foundation') != false) {
                                    $FirstQuestionName = str_replace('PEF Foundation', ' ', $QuestionName);
                                 }
                                 if (strpos($alllow, 'activity ') != false) {
                                    $FirstQuestionName = str_replace('PEF Activity', ' ', $QuestionName);
                                 }
                                 // print_r($pname);
                                 $name = isset($pname[1]) ? $pname[1] : '';
                                 $lastname = isset($pname[3]) ? $pname[3] : '';
                                 $Procedurename = $FirstQuestionName . '-' . $name . '-' . $lastname;
                              }
                           } else {

                              if ($TotalCount == 2) {
                                 $alllow = strtolower($substrs);

                                 $FirstQuestionName = '';
                                 $QuestionName = $pname[0];

                                 $lastname = $pname[1];
                                 $Procedurename = $QuestionName . '-' . $lastname;
                              } else if ($TotalCount == 3) {
                                 if ($proceduteCountName == 'N-VentInit - Neonate Ventilator Initiation' || $proceduteCountName == 'N-VentMon - Neo/Ped Ventilator Monitoring') {
                                    $QuestionName = $pname[0];
                                    $QuestionNameSecond = $pname[1];
                                    $lastname = $pname[2];
                                    $Procedurename = $QuestionName . '-' . $QuestionNameSecond . '-' . $lastname;
                                 } else {
                                    $QuestionName = $pname[0];
                                    $lastname = $pname[2];
                                    $Procedurename = $QuestionName . '-' . $lastname;
                                 }
                              } else {
                                 $QuestionName = $pname[0];
                                 $alllow = strtolower($substrs);
                                 if (strpos($alllow, 'foundation') != false) {
                                    $FirstQuestionName = str_replace('PEF Foundation', ' ', $QuestionName);
                                 }
                                 if (strpos($alllow, 'activity ') != false) {
                                    $FirstQuestionName = str_replace('PEF Activity', ' ', $QuestionName);
                                 }
                                 $name = $pname[1];
                                 $lastname = $pname[3];
                                 $Procedurename = $FirstQuestionName . '-' . $name . '-' . $lastname;
                              }
                           }
                           $shortTitlelen = strlen($Procedurename);
                           if ($shortTitlelen > 40) {
                              $schoolproceduteCountName = substr($Procedurename, 0, 40);
                              $schoolproceduteCountName .= '...';
                           } else {
                              $schoolproceduteCountName = $Procedurename;
                           }



                           $subTotalRow = '';
                           if ($sordorder == 1) {
                              if (!$newStudentId) {
                                 $newStudentId = $studentId;
                              } elseif ($newStudentId != $studentId) {

                                 $totalProcedurePointByStudent = $objcheckoff->GetStudentProcedureTotalForreport($newStudentId, $startDate, $endDate);
                                 $procedurePointsAssistTotal = isset($totalProcedurePointByStudent['procedurePointsAssistTotal']) ? $totalProcedurePointByStudent['procedurePointsAssistTotal'] : 0;
                                 $procedurePointsObserveTotal = isset($totalProcedurePointByStudent['procedurePointsObserveTotal']) ? $totalProcedurePointByStudent['procedurePointsObserveTotal'] : 0;
                                 $procedurePointsPerformTotal = isset($totalProcedurePointByStudent['procedurePointsPerformTotal']) ? $totalProcedurePointByStudent['procedurePointsPerformTotal'] : 0;

                                 $subTotalRow = "<tr><td colspan ='7' style='text-align:right'>Sub Total</td>
                                        <td class='text-center'>" . $procedurePointsAssistTotal . "</td>
                                        <td class='text-center' >" . $procedurePointsObserveTotal . "</td>
                                        <td class='text-center'>" . $procedurePointsPerformTotal . "</td> </tr>";

                                 $newStudentId = 0;
                              }
                           }


                     ?>
                           <!-- For add sub total -->
                           <?php
                           echo $subTotalRow;
                           ?>

                           <tr>
                              <td><?php echo ($firstName); ?></td>
                              <td><?php echo ($lastName); ?></td>
                              <td><?php echo ($Rankname); ?></td>
                              <td><?php echo ($Rotationname); ?></td>
                              <td><?php echo ($procedureCountsCode); ?></td>
                              <td><?php echo ($schoolproceduteCountName); ?></td>
                              <!-- <td><?php //echo ($clinicianfullname); 
                                       ?></td> -->
                              <td style="text-align:center"><?php echo ($procedurePointsAssist); ?></td>
                              <td style="text-align:center"><?php echo ($procedurePointsObserve); ?></td>
                              <td style="text-align:center"><?php echo ($procedurePointsPerform); ?></td>
                              <td><?php if ($procedureDate != '01/01/1970' && $procedureDate != '') echo ($procedureDate);  ?></td>

                           </tr>


                     <?php }
                     } ?>
                     <tr>
                        <td colspan="6" align="right"><b>Total:</b></td>
                        <td style="text-align:center"><?php echo $TotalprocedurePointsAssist; ?></td>
                        <td style="text-align:center"><?php echo $TotalprocedurePointsObserve; ?></td>
                        <td style="text-align:center"><?php echo $TotalprocedurePointsPerform; ?></td>
                        <td></td>
                     </tr>
                  </tbody>
               </table>
            </div>
         <?php
            break;

         case "Procidure_Count_Summary":
            echo "<b>Procedure Count Summary</b><br>";
         ?>
            <div class="container-fluid margin_bottom_ten">
               <div class="row">
                  <div class="form-group">
                     <?php if ($startDate && $endDate) { ?>
                        <div class="col-sm-4"><b>Start Date: </b><?php echo date('m/d/Y', strtotime($startDate)); ?> <br> <b>End Date: </b><?php echo date('m/d/Y', strtotime($endDate)); ?><br /> <?php if ($mainRotationName) { ?> <b>Rotation: </b><?php echo ($mainRotationName);
                                                                                                                                                                                                                                                   } ?> </div>
                     <?php }
                     if ($cbosemester != '') { ?>
                        <div class="col-sm-3"><b>Semester Name: </b><?php echo ($semesterName); ?> </div>
                     <?php } ?>
                     <?php if ($subcborotation != '') { ?>
                        <div class="col-sm-5"><b>Hospital Site: </b><?php echo ($rotationName); ?> </div>
                     <?php } ?>
                     <?php if ($individual_student) { ?>
                        <div class="col-sm-4"><b>Student Name: </b><?php echo ($studentFullname); ?></div>
                     <?php }  ?>
                     <?php if ($student_rank) { ?>
                        <div class="col-sm-3"><b>Rank Name: </b><?php echo ($rankName); ?></div>
                     <?php }  ?>
                  </div>
               </div>
            </div>
            <iframe id="textArea" style="display:none"></iframe>
            <!-- <input type="button" value="Import as Excel" onclick="saveAsExcel('tableToExcel', 'lollipop.xls')"/> -->
            <!-- <button class="btn btn-primary mb-3" onclick="table2csv(this,1)" data-table="example">Export to Excel</button> -->
            <div class="row">
               <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover attendanceTable" style="width: 100%;" cellspacing="0" width="100%">
                  <?php
                  $objcheckoff = new clscheckoff();
                  $totalprocedure = 0;
                  $totalprocedureSummary = 0;
                  if (
                     isset($_POST['cboreporttype']) || ($_POST['cborotation']) || ($_POST['cboindividualstudent'])
                     || ($_POST['cbostudentrank']) || ($_POST['cboevaluator']) || ($_POST['cbolocation'])
                     || ($_POST['cbohospitalsite']) || ($_POST['startDate']) || ($_POST['endDate'])
                  ) {
                     $rowsprocedureSummary = $objcheckoff->GetStudentProcedureSummaryForreport($currentSchoolId, $rotationId, $individual_student, $student_rank, $startDate, $endDate, $AscDesc, $sordorder, $subcborotation);
                  }

                  if ($rowsprocedureSummary != '') {
                     $totalprocedureSummary = mysqli_num_rows($rowsprocedureSummary);
                  } else {
                     echo "<b>Procedure Count Summary Not Found.</b>";
                     exit;
                  }

                  ?>
                  <thead>
                     <tr>
                        <th>First Name</th>
                        <th>Last Name</th>
                        <th>Rank</th>
                        <th>Rotation</th>
                        <th>Id</th>
                        <th>Procedure Name</th>
                        <th>Proc.<br>PointsAss.</th>
                        <th>Proc.<br>PointsObs.</th>
                        <th>Proc.<br>PointsPerf.</th>
                     </tr>
                  </thead>
                  <tbody>
                     <?php
                     $TotalprocedurePointsAssist = 0;
                     $TotalprocedurePointsObserve = 0;
                     $TotalprocedurePointsPerform = 0;
                     if ($totalprocedureSummary > 0) {
                        while ($row = mysqli_fetch_array($rowsprocedureSummary)) {

                           $studentId = stripslashes($row['studentId']);
                           $firstName = stripslashes($row['firstName']);
                           $lastName = stripslashes($row['lastName']);
                           $Rankname = stripslashes($row['Rankname']);
                           $rotationname = stripslashes($row['rotationname']);

                     ?>
                           <tr>
                              <td><b><?php echo ($firstName); ?></b></td>
                              <td><b><?php echo ($lastName); ?><b></td>
                              <td><b><?php echo ($Rankname); ?><b></td>
                              <td><b><?php echo ($rotationname); ?><b></td>
                              <?php
                              if ($isActiveCheckoff == 1)
                                 $rowProcedureNameByStudent = $objcheckoff->GetStandardProceudreNameByStudent($studentId, $startDate, $endDate, $AscDesc, $sordorder);
                              else if ($isActiveCheckoff == 2)
                                 $rowProcedureNameByStudent = $objcheckoff->GetStandardProceudreNameByStudent($studentId, $startDate, $endDate, $AscDesc, $sordorder);
                              else
                                 $rowProcedureNameByStudent = $objcheckoff->GetAdvanceProceudreNameByStudent($studentId, $startDate, $endDate, $AscDesc, $sordorder);

                              $counter = 0;
                              $procedurePointsAssistTotal = 0;
                              $procedurePointsObserveTotal = 0;
                              $procedurePointsPerformTotal = 0;

                              while ($rowProcedureName = mysqli_fetch_array($rowProcedureNameByStudent)) {
                                 $counter++;
                                 $proceduteCountName = $rowProcedureName['proceduteCountName'];
                                 $procedureCountsCode = $rowProcedureName['procedureCountsCode'];
                                 $proceduteCountTopicId = $rowProcedureName['proceduteCountTopicId'];

                                 $rowTotalById = $objcheckoff->GetStudentProcedureTotalById($studentId, $proceduteCountTopicId);

                                 $procedurePointsAssist = stripslashes($rowTotalById['procedurePointsAssist']);
                                 $procedurePointsObserve = stripslashes($rowTotalById['procedurePointsObserve']);
                                 $procedurePointsPerform = stripslashes($rowTotalById['procedurePointsPerform']);

                                 if ($procedurePointsAssist > 0)
                                    $procedurePointsAssistTotal += $procedurePointsAssist;

                                 if ($procedurePointsObserve > 0)
                                    $procedurePointsObserveTotal += $procedurePointsObserve;

                                 if ($procedurePointsPerform > 0)
                                    $procedurePointsPerformTotal += $procedurePointsPerform;

                                 $pname = explode("-", $proceduteCountName);

                                 $TotalCount = count($pname);
                                 $substrs = $pname[0];
                                 //print_r($substrs);

                                 if ($isActiveCheckoff == 2) {

                                    $shortTitlelen = strlen($proceduteCountName);

                                    if ($shortTitlelen > 40) {

                                       $schoolproceduteCountName = substr($proceduteCountName, 0, 40);
                                       $schoolproceduteCountName .= '...';
                                    } else {
                                       $schoolproceduteCountName = $proceduteCountName;
                                    }
                                 } else if ($isActiveCheckoff == 1) {
                                    if ($TotalCount == 3) {
                                       $alllow = strtolower($substrs);

                                       $FirstQuestionName = '';
                                       $QuestionName = $pname[0];
                                       if (strpos($alllow, 'foundation') != false) {
                                          $FirstQuestionName = str_replace('PEF Foundation', ' ', $QuestionName);
                                       }
                                       if (strpos($alllow, 'activity ') != false) {
                                          $FirstQuestionName = str_replace('PEF Activity', ' ', $QuestionName);
                                       }
                                       $lastname = $pname[2];
                                       $Procedurename = $FirstQuestionName . '-' . $lastname;
                                    } else {
                                       $QuestionName = $pname[0];
                                       $FirstQuestionName = '';
                                       if (strpos($alllow, 'foundation') != false) {
                                          $FirstQuestionName = str_replace('PEF Foundation', ' ', $QuestionName);
                                       }
                                       if (strpos($alllow, 'activity ') != false) {
                                          $FirstQuestionName = str_replace('PEF Activity', ' ', $QuestionName);
                                       }
                                       $name = isset($pname[1]) ? $pname[1] : '';
                                       $lastname = isset($pname[3]) ? $pname[3] : '';
                                       $Procedurename = $FirstQuestionName . '-' . $name . '-' . $lastname;
                                    }
                                 } else {

                                    if ($TotalCount == 2) {
                                       $alllow = strtolower($substrs);

                                       $FirstQuestionName = '';
                                       $QuestionName = $pname[0];

                                       $lastname = $pname[1];
                                       $Procedurename = $QuestionName . '-' . $lastname;
                                    } else if ($TotalCount == 3) {
                                       if ($proceduteCountName == 'N-VentInit - Neonate Ventilator Initiation' || $proceduteCountName == 'N-VentMon - Neo/Ped Ventilator Monitoring') {
                                          $QuestionName = $pname[0];
                                          $QuestionNameSecond = $pname[1];
                                          $lastname = $pname[2];
                                          $Procedurename = $QuestionName . '-' . $QuestionNameSecond . '-' . $lastname;
                                       } else {
                                          $QuestionName = $pname[0];
                                          $lastname = $pname[2];
                                          $Procedurename = $QuestionName . '-' . $lastname;
                                       }
                                    }
                                 }
                                 $shortTitlelen = strlen($Procedurename);
                                 if ($shortTitlelen > 40) {
                                    $schoolproceduteCountName = substr($Procedurename, 0, 40);
                                    $schoolproceduteCountName .= '...';
                                 } else {
                                    $schoolproceduteCountName = $Procedurename;
                                 }

                              ?>
                                 <?php if ($counter > 1) { ?>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                 <?php } ?>
                                 <td><?php echo ($procedureCountsCode); ?></td>
                                 <td><?php echo ($schoolproceduteCountName); ?></td>
                                 <td style="text-align:center"><?php echo ($procedurePointsAssist); ?></td>
                                 <td style="text-align:center"><?php echo ($procedurePointsObserve); ?></td>
                                 <td style="text-align:center"><?php echo ($procedurePointsPerform); ?></td>
                           </tr>


                        <?php } ?>
                        <tr>
                           <td style="border-right: none;"></td>
                           <td style="border-right: none;"></td>
                           <td style="border-right: none;"></td>
                           <td style="border-right: none;"></td>
                           <td style="border-right: none;"></td>
                           <td class="text-right"><b>Sub Total:</b></td>
                           <td style="text-align:center"><b><?php echo ($procedurePointsAssistTotal); ?></b></td>
                           <td style="text-align:center"><b><?php echo ($procedurePointsObserveTotal); ?></b></td>
                           <td style="text-align:center"><b><?php echo ($procedurePointsPerformTotal); ?></b></td>
                        </tr>
                     <?php }
                     } else { ?>
                     <tr>
                        <td colspan="8" class="text-center">No data found.</td>
                     </tr>
                  <?php } ?>
                  </tbody>
               </table>
            </div>
         <?php
            break;
         case "Summative":
            echo "<b>Summative Evaluation</b><br>";
         ?>
            <div class="container-fluid">
               <div class="row">
                  <div class="form-group">
                     <?php if ($startDate && $endDate) { ?>
                        <div class="col-sm-4"><b>Start Date: </b><?php echo date('m/d/Y', strtotime($startDate)); ?> <br> <b>End Date: </b><?php echo date('m/d/Y', strtotime($endDate)); ?><br /> <b>Rotation: </b><?php echo ($mainRotationName); ?> </div>
                     <?php } else if ($startDate) { ?>
                        <div class="col-sm-3"><b>Start Date: </b><?php echo ($startDate); ?></div>
                     <?php } elseif ($endDate) { ?>
                        <div class="col-sm-3"><b>End Date: </b><?php echo ($endDate); ?></div>
                     <?php } elseif ($rotationId) { ?>
                        <div class="col-sm-3"><b>Rotation: </b><?php echo ($mainRotationName); ?></div>
                     <?php } elseif ($location_hidden) { ?>
                        <div class="col-sm-3"><b>Location: </b><?php echo ($location_hidden); ?></div>
                     <?php }  ?>
                     <?php if ($cbosemester != '') { ?>
                        <div class="col-sm-4"><b>Semester Name: </b><?php echo ($semesterName); ?> </div>
                     <?php } ?>
                     <?php if ($subcborotation != '') { ?>
                        <div class="col-sm-5"><b>Hospital Site: </b><?php echo ($rotationName); ?> </div>
                     <?php } ?>
                     <?php if ($individual_student) { ?>
                        <div class="col-sm-3"><b>Student Name: </b><?php echo ($studentFullname); ?></div>
                     <?php }  ?>
                     <?php if ($student_rank) { ?>
                        <div class="col-sm-3"><b>Rank Name: </b><?php echo ($rankName); ?></div>
                     <?php }  ?>
                  </div>
               </div>
            </div>
            <br>
            <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
               <?php
               $objSummative = new clsSummative();
               $totalSummative = 0;
               if (
                  isset($_POST['cboreporttype']) || ($_POST['cborotation']) || ($_POST['cboindividualstudent'])
                  || ($_POST['cbostudentrank']) || ($_POST['cboevaluator']) || ($_POST['cbolocation'])
                  || ($_POST['cbohospitalsite']) || ($_POST['startDate']) || ($_POST['endDate'])
               ) {

                  $rowsSummative = $objSummative->GetStudentSummativeDetailsForreport($currentSchoolId, $rotationId, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder, $cbosemester, $subcborotation);
               }
               if ($rowsSummative != '') {
                  $totalSummative = mysqli_num_rows($rowsSummative);
               } else {
                  echo "<b>Summative Not Found.</b>";
                  exit;
               }

               ?>
               <thead>
                  <tr>
                     <th>First Name</th>
                     <th>Last Name</th>
                     <th>Ranking</th>
                     <th>Signature</th>
                     <th>Rotation</th>
                     <th>Evaluator</th>
                     <th>Eval Date</th>
                     <th>Grade</th>
                     <th>Overall Rating</th>
                     <th>Unsatisfactory Behavior Comments</th>
                     <th>Suggestions</th>
                  </tr>
               </thead>
               <tbody>
                  <?php
                  $avgTotalScore = 0;
                  if ($totalSummative > 0) {
                     while ($row = mysqli_fetch_array($rowsSummative)) {
                        $studentId = '';
                        $studentId = $row[0];
                        $studentSummativeMasterId = stripslashes($row['studentSummativeMasterId']);
                        $firstName = stripslashes($row['firstName']);
                        $lastName = stripslashes($row['lastName']);
                        $rank = stripslashes($row['title']);
                        $rotationname = stripslashes($row['rotationname']);
                        $studentSignature = stripslashes($row['dateOfStudentSignature']);
                        $studentSignature = date('m/d/Y', strtotime($studentSignature));
                        $clinicianfname = stripslashes($row['clinicianfname']);
                        $clinicianlname = stripslashes($row['clinicianlname']);
                        $clinicianfullname = $clinicianfname . ' ' . $clinicianlname;
                        $evaluationDate = stripslashes($row['evaluationDate']);

                        $getSummativeScore = $objSummative->GetSummativeScore($studentSummativeMasterId);
                        $avgTotalScore = $getSummativeScore['EvaluationScore'];

                        $getUnsatisfyBehaviourComments = $objSummative->GetUnsatisfyComments($studentSummativeMasterId);
                        $UnsatisfactoryBehaviorComments = $getUnsatisfyBehaviourComments['schoolSummativeOptionAnswerText'];

                        $getSuggestionsComments = $objSummative->GetSuggestionsComments($studentSummativeMasterId);
                        $SuggestionsComment = $getSuggestionsComments['SuggestionsComment'];

                        //Total Eval And Avg
                        $getSelectedOptionList = $objSummative->GetSiteEvaluationSelectedOptionListBySiteEvaluationMasterId($studentSummativeMasterId);
                        $totalSelectedOptionCount = $getSelectedOptionList ? mysqli_num_rows($getSelectedOptionList) : '';
                        $selectedOptionText = [];
                        if ($totalSelectedOptionCount) {
                           while ($optionRow = mysqli_fetch_array($getSelectedOptionList)) {
                              $optionText = $optionRow['optionText'];
                              $optionText = (int) filter_var($optionText, FILTER_SANITIZE_NUMBER_INT);
                              $selectedOptionText[] = $optionText;
                           }
                        }
                        $evalTotal = count($selectedOptionText) ? array_sum($selectedOptionText) : 0;
                        $evalAvg = $evalTotal ? $evalTotal / 24 : 0;
                        $evalAvg = $evalAvg ? (number_format((float)$evalAvg, 1, '.', '')) : 0;

                        //Overall Rating
                        $overallQuestionId = $objSummative->GetSiteEvaluationOverallRotationEvaluationQuestionId($currentSchoolId);
                        $overAllSelectedQuestion = $objSummative->GetSiteEvaluationOverallRotationEvaluationQuestion($studentSummativeMasterId, $overallQuestionId);


                  ?>
                        <tr>
                           <td><?php echo ($firstName); ?></td>
                           <td><?php echo ($lastName); ?></td>
                           <td><?php echo ($rank); ?></td>
                           <td><?php if ($studentSignature != '' && $studentSignature != '01/01/1970' && $studentSignature != '11/30/-0001') echo ($studentSignature); ?></td>
                           <td><?php echo ($rotationname); ?></td>
                           <td><?php echo ($clinicianfullname); ?></td>
                           <td><?php echo (date('m/d/Y', strtotime($evaluationDate))); ?></td>
                           <td><?php echo $evalAvg; ?></td>
                           <td><?php echo ($overAllSelectedQuestion); ?></td>
                           <td><?php echo ($UnsatisfactoryBehaviorComments); ?></td>
                           <td><?php echo ($SuggestionsComment); ?></td>
                        </tr>
                  <?php
                     }
                  }  ?>
               </tbody>
            </table>
         <?php
            break;
         case "Student":
            echo "Student";
         ?>
            <div class="container-fluid">
               <div class="row">
                  <div class="form-group">
                     <?php if ($startDate) { ?>
                        <div class="col-sm-3"><b>Start Date: </b><?php echo ($startDate); ?></div>
                     <?php } elseif ($endDate) { ?>
                        <div class="col-sm-3"><b>End Date: </b><?php echo ($endDate); ?></div>
                     <?php } elseif ($rotationId) { ?>
                        <div class="col-sm-3"><b>Rotation: </b><?php echo ($mainRotationName); ?></div>
                     <?php } elseif ($location_hidden) { ?>
                        <div class="col-sm-3"><b>Location: </b><?php echo ($location_hidden); ?></div>
                     <?php }  ?>
                     <?php if ($student_rank) { ?>
                        <div class="col-sm-3"><b>Rank Name: </b><?php echo ($rankName); ?></div>
                     <?php }  ?>
                  </div>
               </div>
            </div>
            <br>
            <?php
            $objStudents = new clsStudent();
            $totalSchoolStudents = 0;
            if (
               isset($_POST['cboreporttype']) || ($_POST['cborotation']) || ($_POST['cboindividualstudent'])
               || ($_POST['cbostudentrank']) || ($_POST['cbolocation']) || ($_POST['cbohospitalsite']) || ($_POST['sordorder']) || ($_POST['AscDesc']) || ($_POST['cbosemester'])
            ) {
               // echo '$individual_student '.$individual_student;
               $rowsSchoolStudents = $objStudents->GetAllSchoolStudentsForReport($currentSchoolId, $rotationId, $individual_student, $student_rank, $school_location, $hospital_site, $AscDesc, $sordorder, $cbosemester);
            } else {
               $rowsSchoolStudents = $objStudents->GetAllSchoolStudentsForReport($currentSchoolId);
            }
            if ($rowsSchoolStudents != '') {
               $totalSchoolStudents = mysqli_num_rows($rowsSchoolStudents);
            } else {
               echo "<b>Student Not Found.</b>";
               exit;
            }
            unset($objStudents);
            ?>
            <div class="row">
               <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover responsive-datatable" cellspacing="0" width="100%">
                  <thead>
                     <tr>
                        <th>Student Name</th>
                        <th>Ranking</th>
                        <th>Rotation</th>
                        <th>Attendance</th>
                        <th>Procedures</th>
                        <th>Journals</th>
                        <th>Interactions</th>
                        <th>Equipments</th>
                     </tr>
                  </thead>
                  <tbody>
                     <?php
                     $AllProcedureCountTotal = 0;
                     $AllEquipmentsCounts = 0;
                     $AllInteraction = 0;
                     $AllJournalCount = 0;
                     $Totalorignalhours = 0;
                     $totalorignalminstohours = 0;
                     $totalorignalmins = 0;
                     if ($totalSchoolStudents > 0) {
                        while ($row = mysqli_fetch_array($rowsSchoolStudents)) {
                           $studentId = '';

                           $studentId = $row[0];
                           $firstName = stripslashes($row['firstName']);
                           $lastName = stripslashes($row['lastName']);
                           $fullName = $firstName . ' ' . $lastName;
                           $rank = stripslashes($row['rank']);
                           $schoolname = stripslashes($row['school']);
                           $rotationname = stripslashes($row['rotationname']);
                           $ProcedureCountTotal = stripslashes($row['ProcedureCountTotal']);
                           $EquipmentsCounts = stripslashes($row['EquipmentsCounts']);
                           $InteractionCount = stripslashes($row['InteractionCount']);
                           $JournalCount = stripslashes($row['JournalCount']);
                           $orignalhours = (stripslashes($row['orignalhours']) != '') ? stripslashes($row['orignalhours']) : '00:00';

                           $AllJournalCount += $JournalCount ? $JournalCount : 0;
                           $AllInteraction += $InteractionCount ? $InteractionCount : 0;
                           $AllProcedureCountTotal += $ProcedureCountTotal ? $ProcedureCountTotal : 0;
                           $AllEquipmentsCounts += $EquipmentsCounts ? $EquipmentsCounts : 0;
                           // if($orignalhours)
                           // {
                           //    $Totalorignalhours +=$orignalhours;
                           //    echo 'Totalminutesorignalhours'.$Totalminutesorignalhours =$Totalorignalhours * 60;
                           // }
                           $splitorignalhours = explode(":", $orignalhours);
                           // print_r($splitorignalhours);
                           $orignalcount = ($splitorignalhours != '') ? count($splitorignalhours) : 0;

                           if ($orignalcount) {
                              $splitorignalhrs = ($splitorignalhours[0] > 0) ? $splitorignalhours[0] : 00;
                              $splitorignalmin = ($splitorignalhours[1] > 0) ? $splitorignalhours[1] : 00;
                              $totalorignalminstohours += $splitorignalhrs * 60;
                              $totalorignalmins += $splitorignalmin;
                              // $Totalorignalhours = $totalorignalminstohours + $totalorignalmins;
                              $Totalminutesorignalhours = $totalorignalminstohours + $totalorignalmins;
                           }

                     ?>
                           <tr>
                              <td><?php echo ($fullName); ?></td>
                              <td><?php echo ($rank); ?></td>
                              <td><?php echo ($rotationname); ?></td>
                              <td> <?php echo $orignalhours; ?></td>
                              <td><?php echo ($ProcedureCountTotal); ?></td>
                              <td> <?php echo $JournalCount; ?> </td>
                              <td> <?php echo $InteractionCount; ?></td>
                              <td><?php echo $EquipmentsCounts; ?></td>
                           </tr>
                     <?php
                        }
                     }
                     ?>
                     <tr>
                        <?php $TotatlHoursorignalhours = floor($Totalminutesorignalhours / 60) . ':' . ($Totalminutesorignalhours -   floor($Totalminutesorignalhours / 60) * 60);
                        ?>
                        <td colspan="3" align="right"><b>Total:</b></td>
                        <td><?php echo number_format((float)$TotatlHoursorignalhours, 2, '.', ''); ?></td>
                        <td><?php echo $AllProcedureCountTotal; ?></td>
                        <td><?php echo $AllJournalCount; ?></td>
                        <td><?php echo $AllInteraction; ?></td>
                        <td><?php echo $AllEquipmentsCounts; ?></td>
                     </tr>
                  </tbody>
               </table>
            </div>
         <?php
            break;
         case "StudentDetails":
            echo "<b style='margin-top:10px'>Student Portfolio</b><br>";
         ?>
            <div class="container-fluid">
               <div class="row">
                  <div class="form-group">
                     <?php if ($startDate && $endDate) { ?>
                        <div class="col-sm-3"><b>Start Date: </b><?php echo date('m/d/Y', strtotime($startDate)); ?> <br> <b>End Date: </b><?php echo date('m/d/Y', strtotime($endDate)); ?> <?php if ($rotationId) { ?><br /> <b>Rotation: </b><?php echo ($mainRotationName); ?> <?php } ?> </div>
                     <?php } elseif ($startDate) { ?>
                        <div class="col-sm-3"><b>Start Date: </b><?php echo ($startDate); ?></div>
                     <?php } elseif ($endDate) { ?>
                        <div class="col-sm-3"><b>End Date: </b><?php echo ($endDate); ?></div>
                     <?php } elseif ($rotationId) { ?>
                        <div class="col-sm-3"><b>Rotation: </b><?php echo ($mainRotationName); ?></div>
                     <?php } elseif ($location_hidden) { ?>
                        <div class="col-sm-3"><b>Location: </b><?php echo ($location_hidden); ?></div>
                     <?php }  ?>
                     <?php if ($cbosemester != '') { ?>
                        <div class="col-sm-3"><b>Semester Name: </b><?php echo ($semesterName); ?> </div>
                     <?php } ?>
                     <?php if ($subcborotation != '') { ?>
                        <div class="col-sm-5"><b>Hospital Site: </b><?php echo ($rotationName); ?> </div>
                     <?php } ?>
                     <?php if ($individual_student) { ?>
                        <div class="col-sm-3"><b>Student Name: </b><?php echo ($studentFullname); ?></div>
                     <?php }  ?>
                     <?php if ($student_rank) { ?>
                        <div class="col-sm-3"><b>Rank Name: </b><?php echo ($rankName); ?></div>
                     <?php }  ?>
                  </div>
               </div>
            </div>
            <?php
            $objStudents = new clsStudent();
            $totalSchoolStudents = 0;
            if (
               isset($_POST['cboreporttype']) || ($_POST['cborotation']) || ($_POST['cboindividualstudent'])
               || ($_POST['cbostudentrank']) || ($_POST['cbolocation']) || ($_POST['cbohospitalsite']) || ($_POST['sordorder']) || ($_POST['AscDesc']) || ($_POST['cbosemester'])
            ) {
               $rowsSchoolStudents = $objStudents->GetAllSchoolStudentsForReport($currentSchoolId, $rotationId, $individual_student, $student_rank, $school_location, $hospital_site, $AscDesc, $sordorder, $cbosemester);
            } else {
               $rowsSchoolStudents = $objStudents->GetAllSchoolStudents($currentSchoolId, $schoolId);
            }
            if ($rowsSchoolStudents != '') {
               $totalSchoolStudents = mysqli_num_rows($rowsSchoolStudents);
            } else {
               echo "<b>Student Not Found.</b>";
               exit;
            }
            unset($objStudents);
            ?>
            <table id="student-portfolio-datatable" class="table table-bordered dt-responsive nowrap table-hover responsive-datatable" cellspacing="0" width="100%">
               <thead>
                  <tr>
                     <th>First Name</th>
                     <th>Last Name</th>
                     <th>Rank</th>
                     <th style="text-align: center">Action</th>
                  </tr>
               </thead>
               <tbody>
                  <?php
                  if ($totalSchoolStudents > 0) {
                     while ($row = mysqli_fetch_array($rowsSchoolStudents)) {
                        $studentId = '';
                        $studentId = $row[0];
                        $firstName = stripslashes($row['firstName']);
                        $lastName = stripslashes($row['lastName']);
                        $fullName = $firstName . ' ' . $lastName;
                        $rank = stripslashes($row['rank']);
                  ?>
                        <tr>
                           <td> <?php echo ($firstName); ?></td>
                           <td><?php echo ($lastName); ?></td>
                           <td><?php echo ($rank); ?></td>
                           <td style="text-align: center">
                              <a href='studentDetailReport.html?studentId=<?php echo EncodeQueryData($studentId); ?>&reportType=<?php echo $reportType; ?>'>View</a>
                           </td>
                        </tr>
                  <?php
                     }
                  }
                  unset($objStudents);
                  ?>
               </tbody>
            </table>
         <?php
            break;
         case "Site_Eval":
            echo "<b>Site Evaluation</b><br>";
         ?>
            <div class="container-fluid">
               <div class="row">
                  <div class="form-group">
                     <?php if ($startDate && $endDate) { ?>
                        <div class="col-sm-3"><b>Start Date: </b><?php echo date('m/d/Y', strtotime($startDate)); ?> <br> <b>End Date: </b><?php echo date('m/d/Y', strtotime($endDate)); ?><br /> <b>Rotation: </b><?php echo ($mainRotationName); ?> </div>
                     <?php } elseif ($startDate) { ?>
                        <div class="col-sm-3"><b>Start Date: </b><?php echo ($startDate); ?></div>
                     <?php } elseif ($endDate) { ?>
                        <div class="col-sm-3"><b>End Date: </b><?php echo ($endDate); ?></div>
                     <?php } elseif ($rotationId) { ?>
                        <div class="col-sm-3"><b>Rotation: </b><?php echo ($mainRotationName); ?></div>
                     <?php } elseif ($location_hidden) { ?>
                        <div class="col-sm-3"><b>Location: </b><?php echo ($location_hidden); ?></div>
                     <?php }  ?>
                     <?php if ($cbosemester != '') { ?>
                        <div class="col-sm-3"><b>Semester Name: </b><?php echo ($semesterName); ?> </div>
                     <?php } ?>
                     <?php if ($subcborotation != '') { ?>
                        <div class="col-sm-5"><b>Hospital Site: </b><?php echo ($rotationName); ?> </div>
                     <?php } ?>
                     <?php if ($individual_student) { ?>
                        <div class="col-sm-3"><b>Student Name: </b><?php echo ($studentFullname); ?></div>
                     <?php }  ?>
                     <?php if ($student_rank) { ?>
                        <div class="col-sm-3"><b>Rank Name: </b><?php echo ($rankName); ?></div>
                     <?php }  ?>
                     <?php if ($hospital_site) { ?>
                        <div class="col-sm-6"><b>Hospital Side Name: </b><?php echo ($hospitalSideName); ?></div>
                     <?php }  ?>
                  </div>
               </div>
            </div>
            <br>
            <div class="row">
               <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                  <?php
                  $objSiteevaluation = new clsSiteevaluation();
                  $totalSiteevaluation = 0;
                  if (
                     isset($_POST['cboreporttype']) || ($_POST['cborotation']) || ($_POST['cboindividualstudent'])
                     || ($_POST['cbostudentrank']) || ($_POST['cboevaluator']) || ($_POST['cbolocation'])
                     || ($_POST['cbohospitalsite']) || ($_POST['startDate']) || ($_POST['endDate'])
                  ) {

                     $rowsSiteevaluation = $objSiteevaluation->GetStudentSiteEvaluationDetailsForreport($currentSchoolId, $rotationId, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder, $cbosemester, $subcborotation, $selTopicId);
                  }
                  if ($rowsSiteevaluation != '') {
                     $totalSiteevaluation = mysqli_num_rows($rowsSiteevaluation);
                  } else {
                     echo "<b>Site Evaluation Not Found.</b>";
                     exit;
                  }

                  ?>
                  <thead>
                     <tr>
                        <th>First Name</th>
                        <th>Last Name</th>
                        <th>Ranking</th>
                        <th>Rotation</th>
                        <th>Evaluation Date</th>
                        <th>Recommend<br>Clinical<br>Affilate</th>
                        <th>Clinical<br>Location<br>Strengths</th>
                        <th>Clinical<br>Location<br>Need To<br>Improve</th>
                        <th style="text-align: center">Score</th>
                     </tr>
                  </thead>
                  <tbody>
                     <?php
                     $totalScoreSite = 0;
                     $totalEvaluationScore = 0;
                     if ($totalSiteevaluation > 0) {
                        while ($row = mysqli_fetch_array($rowsSiteevaluation)) {

                           $csEvaluationMasterId = stripslashes($row['csEvaluationMasterId']);
                           $firstName = stripslashes($row['firstName']);
                           $lastName = stripslashes($row['lastName']);
                           $Rankname = stripslashes($row['Rankname']);
                           $evaluationDate = stripslashes($row['evaluationDate']);
                           $Rotationname = stripslashes($row['Rotationname']);


                           $GetSiteevaluationScore = $objSiteevaluation->GetSiteEvaluationScore($csEvaluationMasterId);
                           $EvaluationScore = $GetSiteevaluationScore['EvaluationScore'];

                           $GetRecommendClinicalAffilate = $objSiteevaluation->GetRecommendClinicalAffilate($csEvaluationMasterId);
                           $GetClinicalLocationStrengths = $objSiteevaluation->GetClinicalLocationStrengths($csEvaluationMasterId);
                           $GetClinicalLocationNeedToImprove = $objSiteevaluation->GetClinicalLocationNeedToImprove($csEvaluationMasterId);

                           $totalEvaluationScore += $EvaluationScore;

                     ?>
                           <tr>
                              <td><?php echo ($firstName); ?></td>
                              <td><?php echo ($lastName); ?></td>
                              <td><?php echo ($Rankname); ?></td>
                              <td><?php echo ($Rotationname); ?></td>
                              <td><?php echo (date('m/d/Y', strtotime($evaluationDate))); ?></td>
                              <td><?php echo $GetRecommendClinicalAffilate; ?></td>
                              <td><?php echo $GetClinicalLocationStrengths; ?></td>
                              <td><?php echo $GetClinicalLocationNeedToImprove; ?></td>
                              <td style="text-align: center"><?php echo number_format((float)$EvaluationScore, 2, '.', ''); ?></td>
                           </tr>
                     <?php }
                     } ?>
                     <tr>
                        <td colspan="8" align="right"><b>Total:</b></td>
                        <td style="text-align: center"><?php echo number_format((float)$totalEvaluationScore, 2, '.', ''); ?></td>
                     </tr>
                  </tbody>
               </table>
            </div>
         <?php
            break;
         case "Time_Exception":
            echo "<b>Time Exception</b><br>";
         ?>
            <div class="container-fluid">
               <div class="row">
                  <div class="form-group">
                     <?php if ($startDate) { ?>
                        <div class="col-sm-5"><b>Start Date: </b><?php echo date("Y/m/d", strtotime($startDate)); ?>
                           </br><b>End Date: </b><?php echo date("Y/m/d", strtotime($endDate)); ?><br /> <b>Rotation: </b><?php echo ($mainRotationName); ?>
                        </div>
                     <?php } elseif ($rotationId) { ?>
                        <div class="col-sm-3"><b>Rotation: </b><?php echo ($mainRotationName); ?></div>
                     <?php } elseif ($location_hidden) { ?>
                        <div class="col-sm-3"><b>Location: </b><?php echo ($location_hidden); ?></div>
                     <?php }  ?>
                     <?php if ($student_rank) { ?>
                        <div class="col-sm-3"><b>Rank Name: </b><?php echo ($rankName); ?></div>
                     <?php }  ?>
                  </div>
               </div>
            </div>
            <br>
            <div class="row">
               <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                  <?php
                  $objAttendance = new clsAttendance();
                  $totalAttendance = 0;
                  if (
                     isset($_POST['cboreporttype']) || ($_POST['cborotation']) || ($_POST['cboindividualstudent'])
                     || ($_POST['cbostudentrank']) || ($_POST['cboevaluator']) || ($_POST['cbolocation'])
                     || ($_POST['cbohospitalsite']) || ($_POST['startDate']) || ($_POST['endDate'])
                  ) {
                     $subcborotation = '';
                     if (isset($_POST['subcborotation'])) {
                        $subcborotation = $_POST['subcborotation'];

                        //   $subcborotationCount = count($subcborotation);
                        //   if($subcborotationCount>1)
                        //     $subcborotation = implode(" ",$subcborotation);


                     }

                     $rowsAttendance = $objAttendance->GetTimeExceptionDetailsForreport($currentSchoolId, $rotationId, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder, $subcborotation);
                     if (isset($_POST['subcborotation'])) {
                        $subcborotation = $_POST['subcborotation'];

                        $subcborotation = implode(" ", $subcborotation);
                     }
                  }
                  if ($rowsAttendance != '') {
                     $totalAttendance = mysqli_num_rows($rowsAttendance);
                  } else {
                     echo "<b>Time Exception Not Found.</b>";
                     exit;
                  }

                  ?>
                  <thead>
                     <tr>
                        <th>First Name</th>
                        <th>Last Name</th>
                        <th>Rank</th>
                        <th>Rotation</th>
                        <th>Hospital Site</th>
                        <th>Evaluator</th>
                        <th>Date</th>
                        <th>Original</th>
                        <th>Adjusted</th>
                        <th style="text-align:center">Reason</th>
                     </tr>
                  </thead>
                  <tbody>
                     <?php
                     $Totalminutesapprovedhours = 0;
                     $Totalminutesorignalhours = 0;
                     $TotatlHoursorignalhours = 0;
                     $Totalorignalhours = 0;
                     $Totalapprovedhours = 0;
                     $totalmins = 0;
                     if ($totalAttendance > 0) {
                        while ($row = mysqli_fetch_array($rowsAttendance)) {

                           $attendanceId = stripslashes($row['attendanceId']);
                           $firstName = stripslashes($row['firstName']);
                           $lastName = stripslashes($row['lastName']);
                           $Rankname = stripslashes($row['Rankname']);
                           $createdDate = stripslashes($row['updatedDate']);
                           $createdDate = date('m/d/Y', strtotime($createdDate));
                           $orignalhours = stripslashes($row['orignalhours']);
                           $approvedhours = stripslashes($row['approvedhours']);
                           $comment = stripslashes($row['comment']);
                           $Hospitalname = stripslashes($row['Hospitalname']);
                           $rotationname = stripslashes($row['rotationname']);
                           $clinicianfname = stripslashes($row['clinicianfname']);
                           $clinicianlname = stripslashes($row['clinicianlname']);
                           $clinicianfullname = $clinicianfname . ' ' . $clinicianlname;

                           if ($orignalhours) {
                              $Totalorignalhours += $orignalhours;
                              $Totalminutesorignalhours = $Totalorignalhours * 60;
                           }

                           if ($approvedhours) {
                              $Totalapprovedhours += $approvedhours;
                              $Totalminutesapprovedhours = $Totalapprovedhours * 60;
                           }

                     ?>
                           <tr>
                              <td><?php echo ($firstName); ?></td>
                              <td><?php echo ($lastName); ?></td>
                              <td><?php echo ($Rankname); ?></td>
                              <td><?php echo ($rotationname); ?></td>
                              <td><?php echo ($Hospitalname); ?></td>
                              <td><?php echo ($clinicianfullname); ?></td>
                              <td><?php if ($createdDate != '' && $createdDate != '11/30/-0001' && $createdDate != '01/01/1970') {
                                       echo ($createdDate);
                                    } ?></td>
                              <td><?php echo ($orignalhours); ?></td>
                              <td><?php echo ($approvedhours); ?></td>
                              <td style="text-align:center"><?php if ($comment != '') {
                                                               echo ($comment);
                                                            } else {
                                                               echo "-";
                                                            } ?></td>
                           </tr>
                     <?php }
                     } ?>
                     <tr>
                        <?php
                        $TotatlHoursapprovedhours = floor($Totalminutesapprovedhours / 60) . ':' . ($Totalminutesapprovedhours -   floor($Totalminutesapprovedhours / 60) * 60);
                        $TotatlHoursorignalhours = floor($Totalminutesorignalhours / 60) . ':' . ($Totalminutesorignalhours -   floor($Totalminutesorignalhours / 60) * 60);

                        ?>
                        <td colspan="7" align="right"><b>Total:</b></td>
                        <td><?php echo number_format((float)$TotatlHoursorignalhours, 2, '.', ''); ?></td>
                        <td><?php echo number_format((float)$TotatlHoursapprovedhours, 2, '.', ''); ?></td>
                     <tr>
                  </tbody>
               </table>
            </div>
         <?php
            break;
         case "Procedure_Details":
            echo "<b>Procedure Details</b><br>";
         ?>
            <div class="container-fluid">
               <div class="row">
                  <div class="form-group">
                     <?php if ($startDate && $endDate) { ?>
                        <div class="col-sm-5"><b>Start Date: </b><?php echo date('m/d/Y', strtotime($startDate)); ?> <br> <b>End Date: </b><?php echo date('m/d/Y', strtotime($endDate)); ?><br /> <b>Rotation: </b><?php echo ($mainRotationName); ?> </div>
                     <?php } elseif ($startDate) { ?>
                        <div class="col-sm-3"><b>Start Date: </b><?php echo ($startDate); ?></div>
                     <?php } elseif ($endDate) { ?>
                        <div class="col-sm-3"><b>End Date: </b><?php echo ($endDate); ?></div>
                     <?php } elseif ($rotationId) { ?>
                        <div class="col-sm-3"><b>Rotation: </b><?php echo ($mainRotationName); ?></div>
                     <?php } elseif ($location_hidden) { ?>
                        <div class="col-sm-3"><b>Location: </b><?php echo ($location_hidden); ?></div>
                     <?php }  ?>
                     <?php if ($student_rank) { ?>
                        <div class="col-sm-3"><b>Rank Name: </b><?php echo ($rankName); ?></div>
                     <?php }  ?>
                  </div>
               </div>
            </div>
            <br>
            <?php
            $totalProcedure_Details = 0;
            ?>
            <div id="table-scroll" class="table-scroll">
               <div class="table-wrap">
                  <table id="datatable-Procedure" class="main-table table table-bordered dt-responsive nowrap table-hover" cellspacing="0">
                     <?php
                     $objProcedureCount = new clsProcedureCount();
                     $totalProcedure_Details = 0;
                     if (
                        isset($_POST['cboreporttype']) || ($_POST['cborotation']) || ($_POST['cboindividualstudent'])
                        || ($_POST['cbostudentrank']) || ($_POST['cboevaluator']) || ($_POST['cbolocation'])
                        || ($_POST['cbohospitalsite']) || ($_POST['startDate']) || ($_POST['endDate'])
                     ) {

                        $rowsprocedure = $objProcedureCount->GetProcedureDetailsForreport($currentSchoolId, $rotationId, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder);
                     }
                     if ($rowsprocedure != '') {
                        $totalProcedure_Details = mysqli_num_rows($rowsprocedure);
                     } else {
                        echo "<b>Procedure Count Not Found.</b>";
                        exit;
                     }

                     ?>
                     <thead>
                        <tr>
                           <th style="visibility:visible;">Student</th>
                           <th style="visibility:visible;">Hospital</th>
                           <?php
                           if ($isActiveCheckoff == 1)
                              $rowsprocedureName = $objProcedureCount->GetTotalProcedures();
                           else
                              $rowsprocedureName = $objProcedureCount->GetTotalAdvanceProcedures();

                           while ($row = mysqli_fetch_array($rowsprocedureName)) {
                              $proceduteCountId = $row['proceduteCountId'];
                              $procedures = $row['procedures']; ?>
                              <th><?php echo $procedures; ?></th>
                           <?php }
                           ?>
                        </tr>
                     </thead>
                     <tbody>
                        <?php
                        $TotalIsolCount = 0;
                        $TotalEquipCleanCount = 0;
                        $TotalO2TransCount = 0;
                        $TotalHandWshCount = 0;
                        $TotalMedRecCount = 0;
                        $TotalO2AnalyCount = 0;
                        $TotalApicalCount = 0;
                        $TotalPeriphCount = 0;
                        $TotalBPCount = 0;
                        $TotalPtAssessCount = 0;
                        $TotalPtVitalsCount = 0;
                        $TotalO2TxCount = 0;
                        $TotalO2CheckCount = 0;
                        $TotalBlandAerCount = 0;
                        $TotalNTSxCount = 0;
                        $TotalNMTCount = 0;
                        $TotalMDICount = 0;
                        $TotalDPICount = 0;
                        $TotalISCount = 0;
                        $TotalSxInlineCount = 0;

                        $TotalCPTCount = 0;
                        $TotalPEPCount = 0;
                        $TotalIPPBCount = 0;
                        $TotalSxETTCount = 0;
                        $TotalCuffChkCount = 0;
                        $TotalCPRBagCount = 0;
                        $TotalCPRCompCount = 0;
                        $TotalPEFCount = 0;
                        $TotalContAerCount = 0;
                        $TotalSxCSSCount = 0;
                        $TotalTrachCount = 0;
                        $TotalVestTheraCount = 0;
                        $TotalMetaNebCount = 0;
                        $TotalPOCount = 0;
                        $TotalABGCount = 0;
                        $TotalALineCount = 0;
                        $TotalRunCount = 0;
                        $TotalAnalyCount = 0;
                        $TotalParamsCount = 0;
                        $TotalEKGCount = 0;

                        $TotalPFTCount = 0;
                        $TotalCapnogCount = 0;
                        $TotalCXRCount = 0;
                        $TotalBronchCount = 0;
                        $TotalSleepCount = 0;
                        $TotalLungScanCount = 0;
                        $TotalCTScanCount = 0;
                        $TotalMRICount = 0;
                        $TotalCathLabCount = 0;
                        $TotalHolterCount = 0;
                        $TotalEEGCount = 0;
                        $TotalStressTestCount = 0;
                        $TotalVentChkCount = 0;
                        $TotalVentCircChgCount = 0;
                        $TotalVentInitCount = 0;
                        $TotalCPAPCount = 0;
                        $TotalStatCompCount = 0;
                        $TotalCritCareTransCount = 0;
                        $TotalVentWeanCount = 0;
                        $TotalBiPAPNIPPVCount = 0;

                        $TotalExtubCount = 0;
                        $TotalSecureETTCount = 0;
                        $TotalPMWCount = 0;
                        $TotalManualETTCount = 0;
                        $TotalIntubCount = 0;
                        $TotalVentChgCount = 0;
                        $TotalSBTCount = 0;
                        $TotalHFNCCount = 0;
                        $TotalInlineNMTCount = 0;
                        $TotalInlineMDICount = 0;
                        $TotalBiPAPCount = 0;
                        $TotalVentCount = 0;
                        $TotalCBGCount = 0;
                        $TotalOxyhoodCount = 0;
                        $TotalBubCPAPCount = 0;
                        $TotalHFJVCount = 0;
                        $TotalOscillatorCount = 0;
                        $TotalNitricOxCount = 0;
                        $TotalECMOCount = 0;
                        $TotalNHFNCCount = 0;
                        $TotalUACCount = 0;
                        $TotalNABGCount = 0;
                        $TotalNAnalyCount = 0;
                        $TotalNALineCount = 0;
                        $TotalNO2Count = 0;
                        $TotalNBlandAerCount = 0;
                        $TotalNNMTCount = 0;
                        $TotalNNMTBBCount = 0;
                        $TotalNInlineNMTCount = 0;
                        $TotalNInlineMDICount = 0;
                        $TotalNMDICount = 0;
                        $TotalNCPTCount = 0;
                        $TotalNDPICount = 0;
                        $TotalNEquipClnCount = 0;
                        $TotalNPtAssessCount = 0;
                        $TotalPPtAssessCount = 0;
                        $TotalNBPtAssessCount = 0;
                        $TotalNVitalsCount = 0;
                        $TotalNISCount = 0;

                        $TotalNPEPCount = 0;
                        $TotalNTrachCount = 0;
                        $TotalNSecureETTCount = 0;
                        $TotalNNTSCount = 0;
                        $TotalNSxInlineCount = 0;
                        $TotalPCuffChkCount = 0;
                        $TotalNIntubCount = 0;
                        $TotalNExtubCount = 0;
                        $TotalTcO2Count = 0;
                        $TotalTcCO2Count = 0;
                        $TotalSurfAdmCount = 0;
                        $TotalPPDCount = 0;
                        $TotalSGTCount = 0;
                        $TotalFCNCount = 0;
                        $TotalNCPRCount = 0;
                        $TotalPCPRCount = 0;
                        $TotalNBCPRCount = 0;
                        $TotalCPAPCount = 0;
                        $TotalNBiPAPCount = 0;
                        $TotalNVentChkCount = 0;
                        $TotalNVentCircChgCount = 0;
                        $TotalNVentInitCount = 0;
                        $TotalNVentChgCount = 0;
                        $TotalNCritCareTransCount = 0;
                        $TotalPCritCareTransCount = 0;
                        $TotalPSVCount = 0;

                        $TotalOxygenAdministrationCount = 0;
                        $TotalPulseOximetryCount = 0;
                        $TotalMeteredDoseCount = 0;
                        $TotalDryPowderCount = 0;
                        $TotalAirwayClearanceCount = 0;
                        $TotalHumidityCount = 0;
                        $TotalHyperinflationCount = 0;
                        $TotalAerosolizedCount = 0;
                        $TotalTrachealrCount = 0;
                        $TotalNIPPVCount = 0;
                        $TotalVentilatorCount = 0;
                        $TotalAdultVentilatorCount = 0;
                        $TotalAirwayCuffCount = 0;
                        $TotalExtubationCount = 0;
                        $TotalNeonateCount = 0;
                        $TotalNeoPedVentilatorCount = 0;
                        $TotalArterialCount = 0;
                        $TotalEKGCount = 0;
                        $TotalPatientCount = 0;
                        $TotalWeaningCount = 0;

                        if ($totalProcedure_Details > 0) {
                           while ($row = mysqli_fetch_array($rowsprocedure)) {
                              $studentId = $row['studentId'];
                              $firstName = $row['firstName'];
                              $lastName = $row['lastName'];
                              $fullName = $firstName . ' ' . $lastName;
                              $hospitalTitle = $row['hospitalTitle'];

                              if ($isActiveCheckoff == 1) {
                                 $rowTotalIsol = $objProcedureCount->GetTotalProcedureCountForEquipClean($studentId);
                                 $TotalIsol = $rowTotalIsol['TotalIsol'];
                                 $TotalEquipClean = $rowTotalIsol['TotalEquipClean'];
                                 $TotalO2Trans = $rowTotalIsol['TotalO2Trans'];
                                 $TotalHandWsh = $rowTotalIsol['TotalHandWsh'];
                                 $TotalMedRec = $rowTotalIsol['TotalMedRec'];
                                 $TotalO2Analy = $rowTotalIsol['TotalO2Analy'];
                                 $TotalApical = $rowTotalIsol['TotalApical'];
                                 $TotalPeriph = $rowTotalIsol['TotalPeriph'];
                                 $TotalBP = $rowTotalIsol['TotalBP'];
                                 $TotalPtAssess = $rowTotalIsol['TotalPtAssess'];

                                 $TotalIsolCount += $TotalIsol;
                                 $TotalEquipCleanCount += $TotalEquipClean;
                                 $TotalO2TransCount += $TotalO2Trans;
                                 $TotalHandWshCount += $TotalHandWsh;
                                 $TotalMedRecCount += $TotalMedRec;
                                 $TotalO2AnalyCount += $TotalO2Analy;
                                 $TotalApicalCount += $TotalApical;
                                 $TotalPeriphCount += $TotalPeriph;
                                 $TotalBPCount += $TotalBP;
                                 $TotalPtAssessCount += $TotalPtAssess;

                                 $TotalPtVitals = $rowTotalIsol['TotalPtVitals'];
                                 $TotalO2Tx = $rowTotalIsol['TotalO2Tx'];
                                 $TotalO2Check = $rowTotalIsol['TotalO2Check'];
                                 $TotalBlandAer = $rowTotalIsol['TotalBlandAer'];
                                 $TotalNTSx = $rowTotalIsol['TotalNTSx'];
                                 $TotalNMT = $rowTotalIsol['TotalNMT'];
                                 $TotalMDI = $rowTotalIsol['TotalMDI'];
                                 $TotalDPI = $rowTotalIsol['TotalDPI'];
                                 $TotalIS = $rowTotalIsol['TotalIS'];
                                 $TotalSxInline = $rowTotalIsol['TotalSxInline'];

                                 $TotalPtVitalsCount += $TotalPtVitals;
                                 $TotalO2TxCount += $TotalO2Tx;
                                 $TotalO2CheckCount += $TotalO2Check;
                                 $TotalBlandAerCount += $TotalBlandAer;
                                 $TotalNTSxCount += $TotalNTSx;
                                 $TotalNMTCount += $TotalNMT;
                                 $TotalMDICount += $TotalMDI;
                                 $TotalDPICount += $TotalDPI;
                                 $TotalISCount += $TotalIS;
                                 $TotalSxInlineCount += $TotalSxInline;

                                 $TotalCPT = $rowTotalIsol['TotalCPT'];
                                 $TotalPEP = $rowTotalIsol['TotalPEP'];
                                 $TotalIPPB = $rowTotalIsol['TotalIPPB'];
                                 $TotalSxETT = $rowTotalIsol['TotalSxETT'];
                                 $TotalCuffChk = $rowTotalIsol['TotalCuffChk'];
                                 $TotalCPRBag = $rowTotalIsol['TotalCPRBag'];
                                 $TotalCPRComp = $rowTotalIsol['TotalCPRComp'];
                                 $TotalPEF = $rowTotalIsol['TotalPEF'];
                                 $TotalContAer = $rowTotalIsol['TotalContAer'];
                                 $TotalSxCSS = $rowTotalIsol['TotalSxCSS'];

                                 $TotalCPTCount += $TotalCPT;
                                 $TotalPEPCount += $TotalPEP;
                                 $TotalIPPBCount += $TotalIPPB;
                                 $TotalSxETTCount += $TotalSxETT;
                                 $TotalCuffChkCount += $TotalCuffChk;
                                 $TotalCPRBagCount += $TotalCPRBag;
                                 $TotalCPRCompCount += $TotalCPRComp;
                                 $TotalPEFCount += $TotalPEF;
                                 $TotalContAerCount += $TotalContAer;
                                 $TotalSxCSSCount += $TotalSxCSS;


                                 $TotalTrach = $rowTotalIsol['TotalTrach'];
                                 $TotalVestThera = $rowTotalIsol['TotalVestThera'];
                                 $TotalMetaNeb = $rowTotalIsol['TotalMetaNeb'];
                                 $TotalPO = $rowTotalIsol['TotalPO'];
                                 $TotalABG = $rowTotalIsol['TotalABG'];
                                 $TotalALine = $rowTotalIsol['TotalALine'];
                                 $TotalRun = $rowTotalIsol['TotalRun'];
                                 $TotalAnaly = $rowTotalIsol['TotalAnaly'];
                                 $TotalParams = $rowTotalIsol['TotalParams'];
                                 $TotalEKG = $rowTotalIsol['TotalEKG'];

                                 $TotalTrachCount += $TotalTrach;
                                 $TotalVestTheraCount += $TotalVestThera;
                                 $TotalMetaNebCount += $TotalMetaNeb;
                                 $TotalPOCount += $TotalPO;
                                 $TotalABGCount += $TotalABG;
                                 $TotalALineCount += $TotalALine;
                                 $TotalRunCount += $TotalRun;
                                 $TotalAnalyCount += $TotalAnaly;
                                 $TotalParamsCount += $TotalParams;
                                 $TotalEKGCount += $TotalEKG;


                                 $TotalPFT = $rowTotalIsol['TotalPFT'];
                                 $TotalCapnog = $rowTotalIsol['TotalCapnog'];
                                 $TotalCXR = $rowTotalIsol['TotalCXR'];
                                 $TotalBronch = $rowTotalIsol['TotalBronch'];
                                 $TotalSleep = $rowTotalIsol['TotalSleep'];
                                 $TotalLungScan = $rowTotalIsol['TotalLungScan'];
                                 $TotalCTScan = $rowTotalIsol['TotalCTScan'];
                                 $TotalMRI = $rowTotalIsol['TotalMRI'];
                                 $TotalCathLab = $rowTotalIsol['TotalCathLab'];
                                 $TotalHolter = $rowTotalIsol['TotalHolter'];

                                 $TotalPFTCount += $TotalPFT;
                                 $TotalCapnogCount += $TotalCapnog;
                                 $TotalCXRCount += $TotalCXR;
                                 $TotalBronchCount += $TotalBronch;
                                 $TotalSleepCount += $TotalSleep;
                                 $TotalLungScanCount += $TotalLungScan;
                                 $TotalCTScanCount += $TotalCTScan;
                                 $TotalMRICount += $TotalMRI;
                                 $TotalCathLabCount += $TotalCathLab;
                                 $TotalHolterCount += $TotalHolter;


                                 $TotalEEG = $rowTotalIsol['TotalEEG'];
                                 $TotalStressTest = $rowTotalIsol['TotalStressTest'];
                                 $TotalVentChk = $rowTotalIsol['TotalVentChk'];
                                 $TotalVentCircChg = $rowTotalIsol['TotalVentCircChg'];
                                 $TotalVentInit = $rowTotalIsol['TotalVentInit'];
                                 $TotalCPAP = $rowTotalIsol['TotalCPAP'];
                                 $TotalStatComp = $rowTotalIsol['TotalStatComp'];
                                 $TotalCritCareTrans = $rowTotalIsol['TotalCritCareTrans'];
                                 $TotalVentWean = $rowTotalIsol['TotalVentWean'];
                                 $TotalBiPAPNIPPV = $rowTotalIsol['TotalBiPAPNIPPV'];

                                 $TotalEEGCount += $TotalEEG;
                                 $TotalStressTestCount += $TotalStressTest;
                                 $TotalVentChkCount += $TotalVentChk;
                                 $TotalVentCircChgCount += $TotalVentCircChg;
                                 $TotalVentInitCount += $TotalVentInit;
                                 $TotalCPAPCount += $TotalCPAP;
                                 $TotalStatCompCount += $TotalStatComp;
                                 $TotalCritCareTransCount += $TotalCritCareTrans;
                                 $TotalVentWeanCount += $TotalVentWean;
                                 $TotalBiPAPNIPPVCount += $TotalBiPAPNIPPV;


                                 $TotalPSV = $rowTotalIsol['TotalPSV'];
                                 $TotalExtub = $rowTotalIsol['TotalExtub'];
                                 $TotalSecureETT = $rowTotalIsol['TotalSecureETT'];
                                 $TotalPMW = $rowTotalIsol['TotalPMW'];
                                 $TotalManualETT = $rowTotalIsol['TotalManualETT'];
                                 $TotalIntub = $rowTotalIsol['TotalIntub'];
                                 $TotalVentChg = $rowTotalIsol['TotalVentChg'];
                                 $TotalSBT = $rowTotalIsol['TotalSBT'];
                                 $TotalHFNC = $rowTotalIsol['TotalHFNC'];
                                 $TotalInlineNMT = $rowTotalIsol['TotalInlineNMT'];

                                 //Start
                                 $TotalPSVCount += $TotalPSV;
                                 $TotalExtubCount += $TotalExtub;
                                 $TotalSecureETTCount += $TotalSecureETT;
                                 $TotalPMWCount += $TotalPMW;
                                 $TotalManualETTCount += $TotalManualETT;
                                 $TotalIntubCount += $TotalIntub;
                                 $TotalVentChgCount += $TotalVentChg;
                                 $TotalSBTCount += $TotalSBT;
                                 $TotalHFNCCount += $TotalHFNC;
                                 $TotalInlineNMTCount += $TotalInlineNMT;


                                 $TotalInlineMDI = $rowTotalIsol['TotalInlineMDI'];
                                 $TotalBiPAP = $rowTotalIsol['TotalBiPAP'];
                                 $TotalVent = $rowTotalIsol['TotalVent'];
                                 $TotalCBG = $rowTotalIsol['TotalCBG'];
                                 $TotalOxyhood = $rowTotalIsol['TotalOxyhood'];
                                 $TotalBubCPAP = $rowTotalIsol['TotalBubCPAP'];
                                 $TotalHFJV = $rowTotalIsol['TotalHFJV'];
                                 $TotalOscillator = $rowTotalIsol['TotalOscillator'];
                                 $TotalNitricOx = $rowTotalIsol['TotalNitricOx'];
                                 $TotalECMO = $rowTotalIsol['TotalECMO'];

                                 $TotalInlineMDICount += $TotalInlineMDI;
                                 $TotalBiPAPCount += $TotalBiPAP;
                                 $TotalVentCount += $TotalVent;
                                 $TotalCBGCount += $TotalCBG;
                                 $TotalOxyhoodCount += $TotalOxyhood;
                                 $TotalBubCPAPCount += $TotalBubCPAP;
                                 $TotalHFJVCount += $TotalHFJV;
                                 $TotalOscillatorCount += $TotalOscillator;
                                 $TotalNitricOxCount += $TotalNitricOx;
                                 $TotalECMOCount += $TotalECMO;

                                 $TotalNHFNC = $rowTotalIsol['TotalNHFNC'];
                                 $TotalUAC = $rowTotalIsol['TotalUAC'];
                                 $TotalNABG = $rowTotalIsol['TotalNABG'];
                                 $TotalNAnaly = $rowTotalIsol['TotalNAnaly'];
                                 $TotalNALine = $rowTotalIsol['TotalNALine'];
                                 $TotalNO2 = $rowTotalIsol['TotalNO2'];
                                 $TotalNBlandAer = $rowTotalIsol['TotalNBlandAer'];
                                 $TotalNNMT = $rowTotalIsol['TotalNNMT'];
                                 $TotalNNMTBB = $rowTotalIsol['TotalNNMTBB'];
                                 $TotalNInlineNMT = $rowTotalIsol['TotalNInlineNMT'];

                                 $TotalNHFNCCount += $TotalNHFNC;
                                 $TotalUACCount += $TotalUAC;
                                 $TotalNABGCount += $TotalNABG;
                                 $TotalNAnalyCount += $TotalNAnaly;
                                 $TotalNALineCount += $TotalNALine;
                                 $TotalNO2Count += $TotalNO2;
                                 $TotalNBlandAerCount += $TotalNBlandAer;
                                 $TotalNNMTCount += $TotalNNMT;
                                 $TotalNNMTBBCount += $TotalNNMTBB;
                                 $TotalNInlineNMTCount += $TotalNInlineNMT;


                                 $TotalNInlineMDI = $rowTotalIsol['TotalNInlineMDI'];
                                 $TotalNMDI = $rowTotalIsol['TotalNMDI'];
                                 $TotalNCPT = $rowTotalIsol['TotalNCPT'];
                                 $TotalNDPI = $rowTotalIsol['TotalNDPI'];
                                 $TotalNEquipCln = $rowTotalIsol['TotalNEquipCln'];
                                 $TotalNPtAssess = $rowTotalIsol['TotalNPtAssess'];
                                 $TotalPPtAssess = $rowTotalIsol['TotalPPtAssess'];
                                 $TotalNBPtAssess = $rowTotalIsol['TotalNBPtAssess'];
                                 $TotalNVitals = $rowTotalIsol['TotalNVitals'];
                                 $TotalNIS = $rowTotalIsol['TotalNIS'];

                                 $TotalNInlineMDICount += $TotalNInlineMDI;
                                 $TotalNMDICount += $TotalNMDI;
                                 $TotalNCPTCount += $TotalNCPT;
                                 $TotalNDPICount += $TotalNDPI;
                                 $TotalNEquipClnCount += $TotalNEquipCln;
                                 $TotalNPtAssessCount += $TotalNPtAssess;
                                 $TotalPPtAssessCount += $TotalPPtAssess;
                                 $TotalNBPtAssessCount += $TotalNBPtAssess;
                                 $TotalNVitalsCount += $TotalNVitals;
                                 $TotalNISCount += $TotalNIS;

                                 $TotalNPEP = $rowTotalIsol['TotalNPEP'];
                                 $TotalNTrach = $rowTotalIsol['TotalNTrach'];
                                 $TotalNSecureETT = $rowTotalIsol['TotalNSecureETT'];
                                 $TotalNNTS = $rowTotalIsol['TotalNNTS'];
                                 $TotalNSxInline = $rowTotalIsol['TotalNSxInline'];
                                 $TotalPCuffChk = $rowTotalIsol['TotalPCuffChk'];
                                 $TotalNIntub = $rowTotalIsol['TotalNIntub'];
                                 $TotalNExtub = $rowTotalIsol['TotalNExtub'];
                                 $TotalTcO2 = $rowTotalIsol['TotalTcO2'];
                                 $TotalTcCO2 = $rowTotalIsol['TotalTcCO2'];

                                 $TotalNPEPCount += $TotalNPEP;
                                 $TotalNTrachCount += $TotalNTrach;
                                 $TotalNSecureETTCount += $TotalNSecureETT;
                                 $TotalNNTSCount += $TotalNNTS;
                                 $TotalNSxInlineCount += $TotalNSxInline;
                                 $TotalPCuffChkCount += $TotalPCuffChk;
                                 $TotalNIntubCount += $TotalNIntub;
                                 $TotalNExtubCount += $TotalNExtub;
                                 $TotalTcO2Count += $TotalTcO2;
                                 $TotalTcCO2Count += $TotalTcCO2;

                                 $TotalSurfAdm = $rowTotalIsol['TotalSurfAdm'];
                                 $TotalPPD = $rowTotalIsol['TotalPPD'];
                                 $TotalSGT = $rowTotalIsol['TotalSGT'];
                                 $TotalFCN = $rowTotalIsol['TotalFCN'];
                                 $TotalNCPR = $rowTotalIsol['TotalNCPR'];
                                 $TotalPCPR = $rowTotalIsol['TotalPCPR'];
                                 $TotalNBCPR = $rowTotalIsol['TotalNBCPR'];
                                 $TotalCPAP = $rowTotalIsol['TotalCPAP'];
                                 $TotalNBiPAP = $rowTotalIsol['TotalNBiPAP'];
                                 $TotalNVentChk = $rowTotalIsol['TotalNVentChk'];

                                 $TotalSurfAdmCount += $TotalSurfAdm;
                                 $TotalPPDCount += $TotalPPD;
                                 $TotalSGTCount += $TotalSGT;
                                 $TotalFCNCount += $TotalFCN;
                                 $TotalNCPRCount += $TotalNCPR;
                                 $TotalPCPRCount += $TotalNCPR;
                                 $TotalNBCPRCount += $TotalNBCPR;
                                 $TotalCPAPCount += $TotalCPAP;
                                 $TotalNBiPAPCount += $TotalNBiPAP;
                                 $TotalNVentChkCount += $TotalNVentChk;

                                 $TotalNVentCircChg = $rowTotalIsol['TotalNVentCircChg'];
                                 $TotalNVentInit = $rowTotalIsol['TotalNVentInit'];
                                 $TotalNVentChg = $rowTotalIsol['TotalNVentChg'];
                                 $TotalNCritCareTrans = $rowTotalIsol['TotalNCritCareTrans'];
                                 $TotalPCritCareTrans = $rowTotalIsol['TotalPCritCareTrans'];

                                 $TotalNVentCircChgCount += $TotalNVentCircChg;
                                 $TotalNVentInitCount += $TotalNVentInit;
                                 $TotalNVentChgCount += $TotalNVentChg;
                                 $TotalNCritCareTransCount += $TotalNCritCareTrans;
                                 $TotalPCritCareTransCount += $TotalPCritCareTrans;
                              } else {
                                 $rowAdvanceProcedureTotal = $objProcedureCount->GetTotalAdvanceProcedureCount($studentId);
                                 $TotalOxygenAdministration = $rowAdvanceProcedureTotal['TotalOxygenAdministration'];
                                 $TotalPulseOximetry = $rowAdvanceProcedureTotal['TotalPulseOximetry'];
                                 $TotalMeteredDose = $rowAdvanceProcedureTotal['TotalMeteredDose'];
                                 $TotalDryPowder = $rowAdvanceProcedureTotal['TotalDryPowder'];
                                 $TotalAirwayClearance = $rowAdvanceProcedureTotal['TotalAirwayClearance'];

                                 $TotalOxygenAdministrationCount += $TotalOxygenAdministration;
                                 $TotalPulseOximetryCount += $TotalPulseOximetry;
                                 $TotalMeteredDoseCount += $TotalMeteredDose;
                                 $TotalDryPowderCount += $TotalDryPowder;
                                 $TotalAirwayClearanceCount += $TotalAirwayClearance;


                                 $TotalHumidity = $rowAdvanceProcedureTotal['TotalHumidity'];
                                 $TotalHyperinflation = $rowAdvanceProcedureTotal['TotalHyperinflation'];
                                 $TotalAerosolized = $rowAdvanceProcedureTotal['TotalAerosolized'];
                                 $TotalTracheal = $rowAdvanceProcedureTotal['TotalTracheal'];
                                 $TotalNIPPV = $rowAdvanceProcedureTotal['TotalNIPPV'];

                                 $TotalHumidityCount += $TotalHumidity;
                                 $TotalHyperinflationCount += $TotalHyperinflation;
                                 $TotalAerosolizedCount += $TotalAerosolized;
                                 $TotalTrachealrCount += $TotalTracheal;
                                 $TotalNIPPVCount += $TotalNIPPV;

                                 $TotalVentilator = $rowAdvanceProcedureTotal['TotalVentilator'];
                                 $TotalAdultVentilator = $rowAdvanceProcedureTotal['TotalAdultVentilator'];
                                 $TotalAirwayCuff = $rowAdvanceProcedureTotal['TotalAirwayCuff'];
                                 $TotalWeaning = $rowAdvanceProcedureTotal['TotalWeaning'];
                                 $TotalExtubation = $rowAdvanceProcedureTotal['TotalExtubation'];

                                 $TotalVentilatorCount += $TotalVentilator;
                                 $TotalAdultVentilatorCount += $TotalAdultVentilator;
                                 $TotalAirwayCuffCount += $TotalAirwayCuff;
                                 $TotalWeaningCount += $TotalWeaning;
                                 $TotalExtubationCount += $TotalExtubation;

                                 $TotalNeonate = $rowAdvanceProcedureTotal['TotalNeonate'];
                                 $TotalNeoPedVentilator = $rowAdvanceProcedureTotal['TotalNeoPedVentilator'];
                                 $TotalArterial = $rowAdvanceProcedureTotal['TotalArterial'];
                                 $TotalEKG = $rowAdvanceProcedureTotal['TotalEKG'];
                                 $TotalPatient = $rowAdvanceProcedureTotal['TotalPatient'];

                                 $TotalNeonateCount += $TotalNeonate;
                                 $TotalNeoPedVentilatorCount += $TotalNeoPedVentilator;
                                 $TotalArterialCount += $TotalArterial;
                                 $TotalEKGCount += $TotalEKG;
                                 $TotalPatientCount += $TotalPatient;
                              }
                        ?>
                              <tr>
                                 <td style="visibility:visible;"><?php echo $fullName; ?></td>
                                 <td style="visibility:visible;"><?php echo $hospitalTitle; ?></td>
                                 <?php if ($isActiveCheckoff == 1) { ?>
                                    <td style="text-align:center"><?php echo $TotalABG; ?></td>
                                    <td style="text-align:center"></td>
                                    <td style="text-align:center"><?php echo $TotalALine; ?></td>
                                    <td style="text-align:center"><?php echo $TotalAnaly; ?></td>
                                    <td style="text-align:center"><?php echo $TotalApical; ?></td>
                                    <td style="text-align:center"><?php echo $TotalBiPAP; ?></td>
                                    <td style="text-align:center"><?php echo $TotalBiPAPNIPPV; ?></td>
                                    <td style="text-align:center"><?php echo $TotalBlandAer; ?></td>
                                    <td style="text-align:center"><?php echo $TotalBP; ?></td>
                                    <td style="text-align:center"><?php echo $TotalBronch; ?></td>
                                    <td style="text-align:center"><?php echo $TotalBubCPAP; ?></td>
                                    <td style="text-align:center"><?php echo $TotalCapnog; ?></td>
                                    <td style="text-align:center"><?php echo $TotalCathLab; ?></td>
                                    <td style="text-align:center"><?php echo $TotalCBG; ?></td>
                                    <td style="text-align:center"></td>
                                    <td style="text-align:center"></td>
                                    <td style="text-align:center"></td>
                                    <td style="text-align:center"><?php echo $TotalContAer; ?></td>
                                    <td style="text-align:center"></td>
                                    <td style="text-align:center"><?php echo $TotalCPAP; ?></td>
                                    <td style="text-align:center"><?php echo $TotalCPAP; ?></td>
                                    <td style="text-align:center"><?php echo $TotalCPRBag; ?></td>
                                    <td style="text-align:center"><?php echo $TotalCPRComp; ?></td>
                                    <td style="text-align:center"><?php echo $TotalCPT; ?></td>
                                    <td style="text-align:center"><?php echo $TotalCritCareTrans; ?></td>
                                    <td style="text-align:center"><?php echo $TotalCTScan; ?></td>
                                    <td style="text-align:center"><?php echo $TotalCuffChk; ?></td>
                                    <td style="text-align:center"><?php echo $TotalCXR; ?></td>
                                    <td style="text-align:center"><?php echo $TotalDPI; ?></td>
                                    <td style="text-align:center"><?php echo $TotalECMO; ?></td>
                                    <td style="text-align:center"><?php echo $TotalEEG; ?></td>
                                    <td style="text-align:center"><?php echo $TotalEKG; ?></td>
                                    <td style="text-align:center"><?php echo $TotalEquipClean; ?></td>
                                    <td style="text-align:center"><?php echo $TotalExtub; ?></td>
                                    <td style="text-align:center"><?php echo $TotalFCN; ?></td>
                                    <td style="text-align:center"><?php echo $TotalHandWsh; ?></td>
                                    <td style="text-align:center"><?php echo $TotalHFJV; ?></td>
                                    <td style="text-align:center"><?php echo $TotalHFNC; ?></td>
                                    <td style="text-align:center"><?php echo $TotalHolter; ?></td>
                                    <td style="text-align:center"></td>
                                    <td style="text-align:center"><?php echo $TotalInlineMDI; ?></td>
                                    <td style="text-align:center"><?php echo $TotalInlineNMT; ?></td>
                                    <td style="text-align:center"><?php echo $TotalIntub; ?></td>
                                    <td style="text-align:center"><?php echo $TotalIPPB; ?></td>
                                    <td style="text-align:center"><?php echo $TotalIS; ?></td>
                                    <td style="text-align:center"><?php echo $TotalIsol; ?></td>
                                    <td style="text-align:center"><?php echo $TotalLungScan; ?></td>
                                    <td style="text-align:center"><?php echo $TotalManualETT; ?></td>
                                    <td style="text-align:center"></td>
                                    <td style="text-align:center"><?php echo $TotalMDI; ?></td>
                                    <td style="text-align:center"><?php echo $TotalMedRec; ?></td>
                                    <td style="text-align:center"><?php echo $TotalMetaNeb; ?></td>
                                    <td style="text-align:center"><?php echo $TotalMRI; ?></td>
                                    <td style="text-align:center"><?php echo $TotalNABG; ?></td>
                                    <td style="text-align:center"><?php echo $TotalNALine; ?></td>
                                    <td style="text-align:center"><?php echo $TotalNAnaly; ?></td>
                                    <td style="text-align:center"><?php echo $TotalNBiPAP; ?></td>
                                    <td style="text-align:center"><?php echo $TotalNBlandAer; ?></td>
                                    <td style="text-align:center"><?php echo $TotalNCPR; ?></td>
                                    <td style="text-align:center"><?php echo $TotalNCPT; ?></td>
                                    <td style="text-align:center"><?php echo $TotalNCritCareTrans; ?></td>
                                    <td style="text-align:center"><?php echo $TotalNDPI; ?></td>
                                    <td style="text-align:center"><?php echo $TotalNEquipCln; ?></td>
                                    <td style="text-align:center"><?php echo $TotalNExtub; ?></td>
                                    <td style="text-align:center"><?php echo $TotalNHFNC; ?></td>
                                    <td style="text-align:center"><?php echo $TotalNInlineMDI; ?></td>
                                    <td style="text-align:center"><?php echo $TotalNInlineNMT; ?></td>
                                    <td style="text-align:center"><?php echo $TotalNIntub; ?></td>
                                    <td style="text-align:center"><?php echo $TotalNIS; ?></td>
                                    <td style="text-align:center"><?php echo $TotalNMDI; ?></td>
                                    <td style="text-align:center"><?php echo $TotalNNMT; ?></td>
                                    <td style="text-align:center"><?php echo $TotalNNMTBB; ?></td>
                                    <td style="text-align:center"><?php echo $TotalNNTS; ?></td>
                                    <td style="text-align:center"><?php echo $TotalNO2; ?></td>
                                    <td style="text-align:center"><?php echo $TotalNPEP; ?></td>
                                    <td style="text-align:center"><?php echo $TotalNPtAssess; ?></td>
                                    <td style="text-align:center"><?php echo $TotalNSecureETT; ?></td>
                                    <td style="text-align:center"><?php echo $TotalNSxInline; ?></td>
                                    <td style="text-align:center"><?php echo $TotalNTrach; ?></td>
                                    <td style="text-align:center"><?php echo $TotalNVentChg; ?></td>
                                    <td style="text-align:center"><?php echo $TotalNVentChk; ?></td>
                                    <td style="text-align:center"><?php echo $TotalNVentCircChg; ?></td>
                                    <td style="text-align:center"><?php echo $TotalNVentInit; ?></td>
                                    <td style="text-align:center"><?php echo $TotalNVitals; ?></td>
                                    <td style="text-align:center"><?php echo $TotalNBCPR; ?></td>
                                    <td style="text-align:center"><?php echo $TotalNBPtAssess; ?></td>
                                    <td style="text-align:center"></td>
                                    <td style="text-align:center"><?php echo $TotalNitricOx; ?></td>
                                    <td style="text-align:center"><?php echo $TotalNMT; ?></td>
                                    <td style="text-align:center"><?php echo $TotalNTSx; ?></td>
                                    <td style="text-align:center"><?php echo $TotalO2Analy; ?></td>
                                    <td style="text-align:center"><?php echo $TotalO2Check; ?></td>
                                    <td style="text-align:center"><?php echo $TotalO2Trans; ?></td>
                                    <td style="text-align:center"><?php echo $TotalO2Tx; ?></td>
                                    <td style="text-align:center"></td>
                                    <td style="text-align:center"><?php echo $TotalOscillator; ?></td>
                                    <td style="text-align:center"><?php echo $TotalOxyhood; ?></td>
                                    <td style="text-align:center"><?php echo $TotalPCPR; ?></td>
                                    <td style="text-align:center"><?php echo $TotalPCritCareTrans; ?></td>
                                    <td style="text-align:center"><?php echo $TotalPCuffChk; ?></td>
                                    <td style="text-align:center"><?php echo $TotalPPtAssess; ?></td>
                                    <td style="text-align:center"><?php echo $TotalParams; ?></td>
                                    <td style="text-align:center"><?php echo $TotalPEF; ?></td>
                                    <td style="text-align:center"><?php echo $TotalPEP; ?></td>
                                    <td style="text-align:center"><?php echo $TotalPeriph; ?></td>
                                    <td style="text-align:center"><?php echo $TotalPFT; ?></td>
                                    <td style="text-align:center"><?php echo $TotalPMW; ?></td>
                                    <td style="text-align:center"><?php echo $TotalPO; ?></td>
                                    <td style="text-align:center"><?php echo $TotalPPD; ?></td>
                                    <td style="text-align:center"><?php echo $TotalPSV; ?></td>
                                    <td style="text-align:center"><?php echo $TotalPtAssess; ?></td>
                                    <td style="text-align:center"></td>
                                    <td style="text-align:center"><?php echo $TotalPtVitals; ?></td>
                                    <td style="text-align:center"><?php echo $TotalRun; ?></td>
                                    <td style="text-align:center"><?php echo $TotalSBT; ?></td>
                                    <td style="text-align:center"><?php echo $TotalSecureETT; ?></td>
                                    <td style="text-align:center"><?php echo $TotalSGT; ?></td>
                                    <td style="text-align:center"><?php echo $TotalSleep; ?></td>
                                    <td style="text-align:center"><?php echo $TotalStatComp; ?></td>
                                    <td style="text-align:center"><?php echo $TotalStressTest; ?></td>
                                    <td style="text-align:center"><?php echo $TotalSurfAdm; ?></td>
                                    <td style="text-align:center"><?php echo $TotalSxCSS; ?></td>
                                    <td style="text-align:center"><?php echo $TotalSxETT; ?></td>
                                    <td style="text-align:center"><?php echo $TotalSxInline; ?></td>
                                    <td style="text-align:center"><?php echo $TotalTcCO2; ?></td>
                                    <td style="text-align:center"><?php echo $TotalTcO2; ?></td>
                                    <td style="text-align:center"><?php echo $TotalTrach; ?></td>
                                    <td style="text-align:center"><?php echo $TotalUAC; ?></td>
                                    <td style="text-align:center"><?php echo $TotalVent; ?></td>
                                    <td style="text-align:center"><?php echo $TotalVentChg; ?></td>
                                    <td style="text-align:center"><?php echo $TotalVentChk; ?></td>
                                    <td style="text-align:center"><?php echo $TotalVentCircChg; ?></td>
                                    <td style="text-align:center"><?php echo $TotalVentInit; ?></td>
                                    <td style="text-align:center"><?php echo $TotalVentWean; ?></td>
                                    <td style="text-align:center"><?php echo $TotalVestThera; ?></td>
                                 <?php } else { ?>
                                    <td style="text-align:center"><?php echo $TotalArterial; ?></td>
                                    <td style="text-align:center"><?php echo $TotalAerosolized; ?></td>
                                    <td style="text-align:center"><?php echo $TotalAirwayCuff; ?></td>
                                    <td style="text-align:center"><?php echo $TotalDryPowder; ?></td>
                                    <td style="text-align:center"><?php echo $TotalEKG; ?></td>
                                    <td style="text-align:center"><?php echo $TotalExtubation; ?></td>
                                    <td style="text-align:center"><?php echo $TotalHumidity; ?></td>
                                    <td style="text-align:center"><?php echo $TotalHyperinflation; ?></td>
                                    <td style="text-align:center"><?php echo $TotalMeteredDose; ?></td>
                                    <td style="text-align:center"><?php echo $TotalNeonate; ?></td>
                                    <td style="text-align:center"><?php echo $TotalNeoPedVentilator; ?></td>
                                    <td style="text-align:center"><?php echo $TotalNIPPV; ?></td>
                                    <td style="text-align:center"><?php echo $TotalTracheal; ?></td>
                                    <td style="text-align:center"><?php echo $TotalOxygenAdministration; ?></td>
                                    <td style="text-align:center"><?php echo $TotalPatient; ?></td>
                                    <td style="text-align:center"><?php echo $TotalPulseOximetry; ?></td>
                                    <td style="text-align:center"><?php echo $TotalAirwayClearance; ?></td>
                                    <td style="text-align:center"><?php echo $TotalVentilator; ?></td>
                                    <td style="text-align:center"><?php echo $TotalAdultVentilator; ?></td>
                                    <td style="text-align:center"><?php echo $TotalWeaning; ?></td>
                                 <?php } ?>
                              </tr>
                        <?php }
                        }
                        ?>
                        <tr>
                           <td colspan="2" align="right" style="visibility:visible;"><b>Total:</b></td>
                           <?php if ($isActiveCheckoff == 1) { ?>
                              <td style="text-align:center"><?php echo $TotalABGCount; ?></td>
                              <td style="text-align:center"></td>
                              <td style="text-align:center"><?php echo $TotalALineCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalAnalyCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalApicalCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalBiPAPCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalBiPAPNIPPVCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalBlandAerCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalBPCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalBronchCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalBubCPAPCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalCapnogCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalCathLabCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalCBGCount; ?></td>
                              <td style="text-align:center"></td>
                              <td style="text-align:center"></td>
                              <td style="text-align:center"></td>
                              <td style="text-align:center"><?php echo $TotalContAerCount; ?></td>
                              <td style="text-align:center"></td>
                              <td style="text-align:center"><?php echo $TotalCPAPCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalCPAPCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalCPRBagCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalCPRCompCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalCPTCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalCritCareTransCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalCTScanCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalCuffChkCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalCXRCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalDPICount; ?></td>
                              <td style="text-align:center"><?php echo $TotalECMOCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalEEGCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalEKGCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalEquipCleanCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalExtubCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalFCNCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalHandWshCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalHFJVCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalHFNCCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalHolterCount; ?></td>
                              <td style="text-align:center"></td>
                              <td style="text-align:center"><?php echo $TotalInlineMDICount; ?></td>
                              <td style="text-align:center"><?php echo $TotalInlineNMTCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalIntubCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalIPPBCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalISCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalIsolCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalLungScanCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalManualETTCount; ?></td>
                              <td style="text-align:center"></td>
                              <td style="text-align:center"><?php echo $TotalMDICount; ?></td>
                              <td style="text-align:center"><?php echo $TotalMedRec; ?></td>
                              <td style="text-align:center"><?php echo $TotalMetaNebCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalMRICount; ?></td>
                              <td style="text-align:center"><?php echo $TotalNABGCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalNALineCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalNAnalyCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalNBiPAPCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalNBlandAerCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalNCPRCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalNCPTCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalNCritCareTransCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalNDPICount; ?></td>
                              <td style="text-align:center"><?php echo $TotalNEquipClnCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalNExtubCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalNHFNCCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalNInlineMDICount; ?></td>
                              <td style="text-align:center"><?php echo $TotalNInlineNMTCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalNIntubCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalNISCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalNMDICount; ?></td>
                              <td style="text-align:center"><?php echo $TotalNNMTCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalNNMTBBCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalNNTSCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalNO2Count; ?></td>
                              <td style="text-align:center"><?php echo $TotalNPEPCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalNPtAssessCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalNSecureETTCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalNSxInlineCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalNTrachCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalNVentChgCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalNVentChkCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalNVentCircChgCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalNVentInitCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalNVitalsCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalNBCPRCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalNBPtAssessCount; ?></td>
                              <td style="text-align:center"></td>
                              <td style="text-align:center"><?php echo $TotalNitricOxCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalNMTCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalNTSxCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalO2AnalyCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalO2CheckCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalO2TransCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalO2TxCount; ?></td>
                              <td style="text-align:center"></td>
                              <td style="text-align:center"><?php echo $TotalOscillatorCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalOxyhoodCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalPCPRCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalPCritCareTransCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalPCuffChkCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalPPtAssessCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalParamsCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalPEFCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalPEPCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalPeriphCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalPFTCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalPMWCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalPOCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalPPDCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalPSVCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalPtAssessCount; ?></td>
                              <td style="text-align:center"></td>
                              <td style="text-align:center"><?php echo $TotalPtVitalsCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalRunCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalSBTCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalSecureETTCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalSGTCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalSleepCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalStatCompCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalStressTestCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalSurfAdmCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalSxCSSCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalSxETTCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalSxInlineCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalTcCO2Count; ?></td>
                              <td style="text-align:center"><?php echo $TotalTcO2Count; ?></td>
                              <td style="text-align:center"><?php echo $TotalTrachCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalUACCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalVentCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalVentChgCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalVentChkCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalVentCircChgCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalVentInitCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalVentWeanCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalVestTheraCount; ?></td>
                           <?php } else { ?>
                              <td style="text-align:center"><?php echo $TotalArterialCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalAerosolizedCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalAirwayCuffCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalDryPowderCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalEKGCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalExtubationCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalHumidityCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalHyperinflationCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalMeteredDoseCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalNeonateCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalNeoPedVentilatorCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalNIPPVCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalTrachealrCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalOxygenAdministrationCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalPatientCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalPulseOximetryCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalAirwayClearanceCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalVentilatorCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalAdultVentilatorCount; ?></td>
                              <td style="text-align:center"><?php echo $TotalWeaningCount; ?></td>
                           <?php } ?>
                        </tr>
                     </tbody>
                  </table>
               <?php
               break;
            case "PEF_I_Evaluation":
               echo "<b>PEF I Evaluation</b><br>";
               ?>
                  <div class="container-fluid">
                     <div class="row">
                        <div class="form-group">
                           <?php if ($startDate && $endDate) { ?>
                              <div class="col-sm-4"><b>Start Date: </b><?php echo date('m/d/Y', strtotime($startDate)); ?> <br> <b>End Date: </b><?php echo date('m/d/Y', strtotime($endDate)); ?><br /> <b>Rotation: </b><?php echo ($mainRotationName); ?> </div>
                           <?php } elseif ($startDate) { ?>
                              <div class="col-sm-3"><b>Start Date: </b><?php echo ($startDate); ?></div>
                           <?php } elseif ($endDate) { ?>
                              <div class="col-sm-3"><b>End Date: </b><?php echo ($endDate); ?></div>
                           <?php } elseif ($rotationId) { ?>
                              <div class="col-sm-3"><b>Rotation: </b><?php echo ($mainRotationName); ?></div>
                           <?php } elseif ($location_hidden) { ?>
                              <div class="col-sm-3"><b>Location: </b><?php echo ($location_hidden); ?></div>
                           <?php }  ?>
                           <?php if ($cbosemester != '') { ?>
                              <div class="col-sm-4"><b>Semester Name: </b><?php echo ($semesterName); ?> </div>
                           <?php } ?>
                           <?php if ($subcborotation != '') { ?>
                              <div class="col-sm-5"><b>Hospital Site: </b><?php echo ($rotationName); ?> </div>
                           <?php } ?>
                           <?php if ($individual_student) { ?>
                              <div class="col-sm-3"><b>Student Name: </b><?php echo ($studentFullname); ?></div>
                           <?php }  ?>
                           <?php if ($student_rank) { ?>
                              <div class="col-sm-3"><b>Rank Name: </b><?php echo ($rankName); ?></div>
                           <?php }  ?>
                        </div>
                     </div>
                  </div>
                  <br>
                  <div class="row">
                     <table id="PEF_I_EvaluationTable1" class="table table-bordered dt-responsive nowrap table-hover responsive-datatable" cellspacing="0" width="100%">
                        <?php
                        $objPEF = new clsPEF();

                        $totalDailyCount = 0;
                        if (
                           isset($_POST['cboreporttype']) || ($_POST['cborotation']) || ($_POST['cboindividualstudent'])
                           || ($_POST['cbostudentrank']) || ($_POST['cboevaluator']) || ($_POST['cbolocation'])
                           || ($_POST['cbohospitalsite']) || ($_POST['startDate']) || ($_POST['endDate'])
                        ) {
                           $studentId = 0;
                           $isType = 'pef1';
                           $getPEFIdetails = $objPEF->GetAllPEF($currentSchoolId, $rotationId, $studentId, $isType, $startDate, $endDate, $AscDesc, $sordorder);
                        }
                        if ($getPEFIdetails != '') {
                           $totalPEFIdetailsCount = mysqli_num_rows($getPEFIdetails);
                        } else {
                           echo "<b>PEF I Evaluation Not Found.</b>";
                           exit;
                        }

                        ?>




                        <thead>
                           <tr>
                              <th>First Name</th>
                              <th>Last Name</th>
                              <th>Rotation</th>
                              <th>Date</th>
                              <th class="text-center">Student Signture Date</th>
                              <th class="text-center">Score</th>
                              <th class="text-center">Status</th>
                           </tr>
                        </thead>
                        <tbody>
                           <?php if ($totalPEFIdetailsCount > 0) {
                              while ($row = mysqli_fetch_array($getPEFIdetails)) {
                                 $DBStudentId = $row['studentId'];
                                 $isStatus = $row['isStatus'];
                                 $score = $row['score'];
                                 $studentPEFMasterId = $row['studentPEFMasterId'];
                                 $studentfirstName = $row['studentfirstName'];
                                 $studentlastName = $row['studentlastName'];
                                 $studentfullName = $studentfirstName . ' ' . $studentlastName;

                                 $evaluationDate = $row['evaluationDate'];
                                 $evaluationDate = converFromServerTimeZone($evaluationDate, $TimeZone);
                                 $evaluationDate = date("m/d/Y", strtotime($evaluationDate));

                                 $dateOfStudentSignature = $row['dateOfStudentSignature'];
                                 if ($dateOfStudentSignature != '0000-00-00 00:00:00') {
                                    $dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $TimeZone);
                                    $dateOfStudentSignature = date("m/d/Y", strtotime($dateOfStudentSignature));
                                 } else {
                                    $dateOfStudentSignature = "-";
                                 }

                                 $rotationName = $row['rotationName'];
                           ?>
                                 <tr>
                                    <td><?php echo ($studentfirstName); ?></td>
                                    <td><?php echo ($studentlastName); ?></td>
                                    <td><?php echo ($rotationName); ?></td>
                                    <td><?php echo ($evaluationDate); ?></td>
                                    <td style="text-align: center"><?php echo $dateOfStudentSignature; ?></td>
                                    <td style="text-align: center"><?php echo ($score); ?></td>
                                    <td style="text-align: center"><?php echo $isStatus; ?></td>
                                 </tr>
                           <?php       }
                           }   ?>
                        </tbody>
                     </table>
                     <br>
                  </div>
               <?php
               break;
            case "PEF_II_Evaluation":
               echo "<b>PEF II Evaluation</b><br>";
               ?>
                  <div class="container-fluid">
                     <div class="row">
                        <div class="form-group">
                           <?php if ($startDate && $endDate) { ?>
                              <div class="col-sm-4"><b>Start Date: </b><?php echo date('m/d/Y', strtotime($startDate)); ?> <br> <b>End Date: </b><?php echo date('m/d/Y', strtotime($endDate)); ?><br /> <b>Rotation: </b><?php echo ($mainRotationName); ?> </div>
                           <?php } elseif ($startDate) { ?>
                              <div class="col-sm-3"><b>Start Date: </b><?php echo ($startDate); ?></div>
                           <?php } elseif ($endDate) { ?>
                              <div class="col-sm-3"><b>End Date: </b><?php echo ($endDate); ?></div>
                           <?php } elseif ($rotationId) { ?>
                              <div class="col-sm-3"><b>Rotation: </b><?php echo ($mainRotationName); ?></div>
                           <?php } elseif ($location_hidden) { ?>
                              <div class="col-sm-3"><b>Location: </b><?php echo ($location_hidden); ?></div>
                           <?php }  ?>
                           <?php if ($cbosemester != '') { ?>
                              <div class="col-sm-4"><b>Semester Name: </b><?php echo ($semesterName); ?> </div>
                           <?php } ?>
                           <?php if ($subcborotation != '') { ?>
                              <div class="col-sm-5"><b>Hospital Site: </b><?php echo ($rotationName); ?> </div>
                           <?php } ?>
                           <?php if ($individual_student) { ?>
                              <div class="col-sm-3"><b>Student Name: </b><?php echo ($studentFullname); ?></div>
                           <?php }  ?>
                           <?php if ($student_rank) { ?>
                              <div class="col-sm-3"><b>Rank Name: </b><?php echo ($rankName); ?></div>
                           <?php }  ?>
                        </div>
                     </div>
                  </div>
                  <br>
                  <div class="row">
                     <table id="PEF_II_EvaluationTable" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                        <?php
                        $objPEF = new clsPEF();

                        $totalDailyCount = 0;
                        if (
                           isset($_POST['cboreporttype']) || ($_POST['cborotation']) || ($_POST['cboindividualstudent'])
                           || ($_POST['cbostudentrank']) || ($_POST['cboevaluator']) || ($_POST['cbolocation'])
                           || ($_POST['cbohospitalsite']) || ($_POST['startDate']) || ($_POST['endDate'])
                        ) {
                           $studentId = 0;
                           $isType = 'pef2';
                           $getPEFIIdetails = $objPEF->GetAllPEF($currentSchoolId, $rotationId, $studentId, $isType, $startDate, $endDate);
                        }
                        if ($getPEFIIdetails != '') {
                           $totalPEFIIdetailsCount = mysqli_num_rows($getPEFIIdetails);
                        } else {
                           echo "<b>PEF II Evaluation Not Found.</b>";
                           exit;
                        }

                        ?>




                        <thead>
                           <tr>
                              <th>First Name</th>
                              <th>Last Name</th>
                              <th>Rotation</th>
                              <th>Date</th>
                              <th class="text-center">Student Signture Date</th>
                              <th class="text-center">Score</th>
                              <th class="text-center">Status</th>
                           </tr>
                        </thead>
                        <tbody>
                           <?php if ($totalPEFIIdetailsCount > 0) {
                              while ($row = mysqli_fetch_array($getPEFIIdetails)) {
                                 $DBStudentId = $row['studentId'];
                                 $isStatus = $row['isStatus'];
                                 $score = $row['score'];
                                 $studentPEFMasterId = $row['studentPEFMasterId'];
                                 $studentfirstName = $row['studentfirstName'];
                                 $studentlastName = $row['studentlastName'];
                                 $studentfullName = $studentfirstName . ' ' . $studentlastName;

                                 $evaluationDate = $row['evaluationDate'];
                                 $evaluationDate = converFromServerTimeZone($evaluationDate, $TimeZone);
                                 $evaluationDate = date("m/d/Y", strtotime($evaluationDate));

                                 $dateOfStudentSignature = $row['dateOfStudentSignature'];
                                 if ($dateOfStudentSignature != '0000-00-00 00:00:00') {
                                    $dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $TimeZone);
                                    $dateOfStudentSignature = date("m/d/Y", strtotime($dateOfStudentSignature));
                                 } else {
                                    $dateOfStudentSignature = "-";
                                 }

                                 $rotationName = $row['rotationName'];
                           ?>
                                 <tr>
                                    <td><?php echo ($studentfirstName); ?></td>
                                    <td><?php echo ($studentlastName); ?></td>
                                    <td><?php echo ($rotationName); ?></td>
                                    <td><?php echo ($evaluationDate); ?></td>
                                    <td style="text-align: center"><?php echo $dateOfStudentSignature; ?></td>
                                    <td style="text-align: center"><?php echo ($score); ?></td>
                                    <td style="text-align: center"><?php echo $isStatus; ?></td>
                                 </tr>
                           <?php       }
                           }   ?>
                        </tbody>
                     </table>
                     <br>
                  </div>
               <?php
               break;
            case "Mastery":
               echo "<b>Mastery Evaluation</b><br>";
               ?>
                  <div class="container-fluid">
                     <div class="row">
                        <div class="form-group">
                           <?php if ($startDate && $endDate) { ?>
                              <div class="col-sm-4"><b>Start Date: </b><?php echo date('m/d/Y', strtotime($startDate)); ?> <br> <b>End Date: </b><?php echo date('m/d/Y', strtotime($endDate)); ?><br /> <b>Rotation: </b><?php echo ($mainRotationName); ?> </div>
                           <?php } elseif ($startDate) { ?>
                              <div class="col-sm-3"><b>Start Date: </b><?php echo ($startDate); ?></div>
                           <?php } elseif ($endDate) { ?>
                              <div class="col-sm-3"><b>End Date: </b><?php echo ($endDate); ?></div>
                           <?php } elseif ($rotationId) { ?>
                              <div class="col-sm-3"><b>Rotation: </b><?php echo ($mainRotationName); ?></div>
                           <?php } elseif ($location_hidden) { ?>
                              <div class="col-sm-3"><b>Location: </b><?php echo ($location_hidden); ?></div>
                           <?php }  ?>
                           <?php if ($cbosemester != '') { ?>
                              <div class="col-sm-4"><b>Semester Name: </b><?php echo ($semesterName); ?> </div>
                           <?php } ?>
                           <?php if ($subcborotation != '') { ?>
                              <div class="col-sm-5"><b>Hospital Site: </b><?php echo ($rotationName); ?> </div>
                           <?php } ?>
                           <?php if ($individual_student) { ?>
                              <div class="col-sm-3"><b>Student Name: </b><?php echo ($studentFullname); ?></div>
                           <?php }  ?>
                           <?php if ($student_rank) { ?>
                              <div class="col-sm-3"><b>Rank Name: </b><?php echo ($rankName); ?></div>
                           <?php }  ?>
                        </div>
                     </div>
                  </div>
                  <br>
                  <div class="row">
                     <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                        <?php
                        $objMasteryEval = new clsMasteryEval();
                        $totalJournal = 0;
                        if (
                           isset($_POST['cboreporttype']) || ($_POST['cborotation']) || ($_POST['cboindividualstudent'])
                           || ($_POST['cbostudentrank']) || ($_POST['cboevaluator']) || ($_POST['cbolocation'])
                           || ($_POST['cbohospitalsite']) || ($_POST['startDate']) || ($_POST['endDate'])
                        ) {

                           $rowsMastery = $objMasteryEval->GetAllMasteryEvalForReport($currentSchoolId, $rotationId, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder, $cbosemester, $subcborotation);
                        }
                        if ($rowsMastery != '') {
                           $totalMastery = mysqli_num_rows($rowsMastery);
                        } else {
                           echo "<b>Student Not Found.</b>";
                        }
                        unset($objMasteryEval);
                        ?>
                        <thead>
                           <tr>
                              <th>First Name</th>
                              <th>Last Name</th>
                              <th>Ranking</th>
                              <th>Student Signature</th>
                              <th>Rotation</th>
                              <th>Evaluator</th>
                              <th>Evaluator Signature</th>
                              <th>CPAP</th>
                              <th>Delivery Of Neonate</th>
                              <th>HFOV</th>
                              <th>Tracheostomy Care</th>
                              <th>Total average</th>
                              <th>Action</th>
                           </tr>
                        </thead>
                        <tbody>
                           <?php
                           if ($totalMastery > 0) {
                              while ($row = mysqli_fetch_array($rowsMastery)) {
                                 $studentId = '';
                                 $studentId = $row[0];
                                 $firstName = stripslashes($row['firstName']);
                                 $lastName = stripslashes($row['lastName']);
                                 $fullName = $firstName . ' ' . $lastName;
                                 $rank = stripslashes($row['Ranktitle']);
                                 $rotationname = stripslashes($row['rotationname']);
                                 $dateOfStudentSignature = stripslashes($row['dateOfStudentSignature']);
                                 $evaluationDate = stripslashes($row['evaluationDate']);

                                 $clinicianFirstName = stripslashes($row['clinicianFirstName']);
                                 $clinicianLastName = stripslashes($row['clinicianLastName']);
                                 $evaluator = $clinicianFirstName . ' ' . $clinicianLastName;
                                 $firstSectionAvg = stripslashes($row['firstSectionAvg']);
                                 $secondSectionAvg = stripslashes($row['secondSectionAvg']);
                                 $thirdSectionAvg = stripslashes($row['thirdSectionAvg']);
                                 $fourthSectionAvg = stripslashes($row['fourthSectionAvg']);
                                 $totalAvg = stripslashes($row['totalAvg']);

                           ?>
                                 <tr>
                                    <td><?php echo ($firstName); ?></td>
                                    <td><?php echo ($lastName); ?></td>
                                    <td><?php echo ($rank); ?></td>
                                    <td><?php echo (date('m/d/Y', strtotime($dateOfStudentSignature))); ?></td>
                                    <td><?php echo ($rotationname); ?></td>
                                    <td><?php echo ($evaluator); ?></td>
                                    <td><?php echo (date('m/d/Y', strtotime($evaluationDate))); ?></td>
                                    <td><?php echo ($firstSectionAvg); ?></td>
                                    <td><?php echo ($secondSectionAvg); ?></td>
                                    <td><?php echo ($thirdSectionAvg); ?></td>
                                    <td><?php echo ($fourthSectionAvg); ?></td>
                                    <td><?php echo ($totalAvg); ?></td>
                                    <td><?php echo ($totalAvg); ?></td>
                                 </tr>
                           <?php
                              }
                           }
                           ?>
                        </tbody>
                     </table>
                  </div>
               <?php
               break;

            // late clock in report
            case "Late_clockIn":
               echo "<b>Late Clock In Report</b><br>";
               ?>
                  <div class="container-fluid">
                     <div class="row">
                        <div class="form-group">
                           <?php if ($startDate && $endDate) { ?>
                              <div class="col-sm-4"><b>Start Date: </b><?php echo date('m/d/Y', strtotime($startDate)); ?> <br> <b>End Date: </b><?php echo date('m/d/Y', strtotime($endDate)); ?><br /> <b>Rotation: </b><?php echo ($mainRotationName); ?> </div>
                           <?php } elseif ($startDate) { ?>
                              <div class="col-sm-3"><b>Start Date: </b><?php echo ($startDate); ?></div>
                           <?php } elseif ($endDate) { ?>
                              <div class="col-sm-3"><b>End Date: </b><?php echo ($endDate); ?></div>
                           <?php } elseif ($rotationId) { ?>
                              <div class="col-sm-3"><b>Rotation: </b><?php echo ($mainRotationName); ?></div>
                           <?php } elseif ($location_hidden) { ?>
                              <div class="col-sm-3"><b>Location: </b><?php echo ($location_hidden); ?></div>
                           <?php }  ?>
                           <?php if ($cbosemester != '') { ?>
                              <div class="col-sm-4"><b>Semester Name: </b><?php echo ($semesterName); ?> </div>
                           <?php } ?>
                           <?php if ($subcborotation != '') { ?>
                              <div class="col-sm-5"><b>Hospital Site: </b><?php echo ($rotationName); ?> </div>
                           <?php } ?>
                           <?php if ($individual_student) { ?>
                              <div class="col-sm-3"><b>Student Name: </b><?php echo ($studentFullname); ?></div>
                           <?php }  ?>
                           <?php if ($student_rank) { ?>
                              <div class="col-sm-3"><b>Rank Name: </b><?php echo ($rankName); ?></div>
                           <?php }  ?>
                        </div>
                     </div>
                  </div>
                  <br>
                  <div class="row">
                     <table id="lateClockIn" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                        <?php
                        $objAttendance = new clsAttendance();

                        $objMasteryEval = new clsMasteryEval();
                        $totalJournal = 0;
                        if (
                           isset($_POST['cboreporttype']) || ($_POST['cborotation']) || ($_POST['cboindividualstudent'])
                           || ($_POST['cbostudentrank']) || ($_POST['cboevaluator']) || ($_POST['cbolocation'])
                           || ($_POST['cbohospitalsite']) || ($_POST['startDate']) || ($_POST['endDate'])
                        ) {

                           //Late Clock In
                           $evaluator = $loggedClinicianId;
                           $lateClockIn = $objAttendance->LateClockInDetailsForReportForClinician($currentSchoolId, $rotationId, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder, $cbosemester, $subcborotation);
                           $TotalLateClockIn = 0;
                           if ($lateClockIn != '') {
                              $TotalLateClockIn = mysqli_num_rows($lateClockIn);
                           } else {
                              echo "<b>Late ClockIn Not Found.</b>";
                           }
                        }


                        ?>
                        <thead>
                           <tr>
                              <th>First Name</th>
                              <th>Last Name</th>
                              <th>Ranking</th>
                              <th>Rotation</th>
                              <th>Clock In Date</th>
                              <th>Clock In Time</th>
                              <th>Student In Time</th>

                           </tr>
                        </thead>
                        <tbody>
                           <?php
                           if ($TotalLateClockIn > 0) {
                              while ($row = mysqli_fetch_array($lateClockIn)) {
                                 $firstName = stripslashes($row['firstName']);
                                 $lastName = stripslashes($row['lastName']);
                                 $Rankname = stripslashes($row['Rankname']);
                                 $fullname = $firstName . ' ' . $lastName;
                                 $title = stripslashes($row['title']);

                                 $startDate = stripslashes($row['startDate']);
                                 $startDate = converFromServerTimeZone($startDate, $TimeZone);
                                 $rotationStartDate = date('m/d/Y', strtotime($startDate));
                                 $rotationStartTime = date('h:i A', strtotime($startDate));

                                 $clockInDateTime = stripslashes($row['clockInDateTime']);
                                 $clockInDateTime = converFromServerTimeZone($clockInDateTime, $TimeZone);
                                 $clockInDate = date('m/d/Y', strtotime($clockInDateTime));
                                 $clockInTime = date('h:i A', strtotime($clockInDateTime));

                           ?>

                                 <tr>
                                    <td><?php echo ($firstName); ?></td>
                                    <td><?php echo ($lastName); ?></td>
                                    <td><?php echo ($Rankname); ?></td>
                                    <td><?php echo ($title); ?></td>

                                    <td><?php echo ($clockInDate); ?></td>
                                    <td><?php echo ($rotationStartTime); ?></td>
                                    <td><?php echo ($clockInTime); ?></td>


                                 </tr>
                           <?php
                              }
                           }
                           ?>
                        </tbody>
                     </table>
                  </div>
                  <br>
               <?php
               break;

            // Early clock out report
            case "Early_clockOut":
               echo "<b>Early Clock Out Report</b><br>";
               ?>
                  <div class="container-fluid">
                     <div class="row">
                        <div class="form-group">
                           <?php if ($startDate && $endDate) { ?>
                              <div class="col-sm-4"><b>Start Date: </b><?php echo date('m/d/Y', strtotime($startDate)); ?> <br> <b>End Date: </b><?php echo date('m/d/Y', strtotime($endDate)); ?><br /> <b>Rotation: </b><?php echo ($mainRotationName); ?> </div>
                           <?php } elseif ($startDate) { ?>
                              <div class="col-sm-3"><b>Start Date: </b><?php echo ($startDate); ?></div>
                           <?php } elseif ($endDate) { ?>
                              <div class="col-sm-3"><b>End Date: </b><?php echo ($endDate); ?></div>
                           <?php } elseif ($rotationId) { ?>
                              <div class="col-sm-3"><b>Rotation: </b><?php echo ($mainRotationName); ?></div>
                           <?php } elseif ($location_hidden) { ?>
                              <div class="col-sm-3"><b>Location: </b><?php echo ($location_hidden); ?></div>
                           <?php }  ?>
                           <?php if ($cbosemester != '') { ?>
                              <div class="col-sm-4"><b>Semester Name: </b><?php echo ($semesterName); ?> </div>
                           <?php } ?>
                           <?php if ($subcborotation != '') { ?>
                              <div class="col-sm-5"><b>Hospital Site: </b><?php echo ($rotationName); ?> </div>
                           <?php } ?>
                           <?php if ($individual_student) { ?>
                              <div class="col-sm-3"><b>Student Name: </b><?php echo ($studentFullname); ?></div>
                           <?php }  ?>
                           <?php if ($student_rank) { ?>
                              <div class="col-sm-3"><b>Rank Name: </b><?php echo ($rankName); ?></div>
                           <?php }  ?>
                        </div>
                     </div>
                  </div>
                  <br>
                  <div class="row">
                     <table id="earlyClockOut" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                        <?php
                        $objAttendance = new clsAttendance();

                        $objMasteryEval = new clsMasteryEval();
                        $totalJournal = 0;
                        if (
                           isset($_POST['cboreporttype']) || ($_POST['cborotation']) || ($_POST['cboindividualstudent'])
                           || ($_POST['cbostudentrank']) || ($_POST['cboevaluator']) || ($_POST['cbolocation'])
                           || ($_POST['cbohospitalsite']) || ($_POST['startDate']) || ($_POST['endDate'])
                        ) {

                           //Early Clock out
                           $evaluator = $loggedClinicianId;
                           $earlyClockOut = $objAttendance->noClockOutDetailsForClinicianReport($currentSchoolId, $rotationId, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder, $cbosemester, $subcborotation);

                           // $lateClockIn=$objAttendance->LateClockInDetailsForReport($currentSchoolId,$rotationId,$individual_student,$student_rank,$evaluator,$school_location,$hospital_site,$startDate,$endDate,$AscDesc,$sordorder,$cbosemester,$subcborotation);
                           $TotalEarlryClockOut = 0;
                           if ($earlyClockOut != '') {
                              $TotalEarlryClockOut = mysqli_num_rows($earlyClockOut);
                           } else {
                              echo "<b>Early Clock Out Not Found.</b>";
                           }
                        }


                        ?>
                        <thead>
                           <tr>
                              <th>First Name</th>
                              <th>Last Name</th>
                              <th>Ranking</th>
                              <th>Rotation</th>
                              <th>Clock Out Date</th>
                              <th>Clock Out Time</th>
                              <th>Student Out Time</th>
                           </tr>
                        </thead>
                        <tbody>

                           <?php
                           if ($TotalEarlryClockOut > 0) {
                              while ($row = mysqli_fetch_array($earlyClockOut)) {
                                 $firstName = stripslashes($row['firstName']);
                                 $lastName = stripslashes($row['lastName']);
                                 $fullname = $firstName . ' ' . $lastName;
                                 $title = stripslashes($row['title']);
                                 $Rankname = stripslashes($row['Rankname']);

                                 $endDate = stripslashes($row['endDate']);
                                 $endDate = converFromServerTimeZone($endDate, $TimeZone);
                                 $rotationEndDate = date('m/d/Y', strtotime($endDate));
                                 $rotationEndTime = date('h:i A', strtotime($endDate));

                                 $clockOutDateTime = stripslashes($row['clockOutDateTime']);
                                 $clockOutDateTime = converFromServerTimeZone($clockOutDateTime, $TimeZone);
                                 $clockOutDate = date('m/d/Y', strtotime($clockOutDateTime));
                                 $clockOutTime = date('h:i A', strtotime($clockOutDateTime));
                           ?>
                                 <tr><?php
                                       if ($clockOutDate != '' && $clockOutDate != '0000-00-00' && $clockOutDate != '01/01/1970') {

                                       ?>

                                       <td><?php echo ($firstName); ?></td>
                                       <td><?php echo ($lastName); ?></td>
                                       <td><?php echo ($Rankname); ?></td>
                                       <td><?php echo ($title); ?></td>

                                       <td><?php echo ($clockOutDate); ?></td>
                                       <td><?php echo ($rotationEndTime); ?></td>
                                       <td><?php echo ($clockOutTime); ?></td>
                                    <?php } ?>
                                 </tr>
                              <?php }
                           } else { ?>
                              <tr>
                                 <td colspan="5" align="center">No record(s) available</td>
                              <tr>
                              <?php } ?>


                        </tbody>
                     </table>
                  </div>
                  <br>
               <?php
               break;

            // No clock out report
            case "No_clockout":
               echo "<b>No Clock Out Report</b><br>";
               ?>
                  <div class="container-fluid">
                     <div class="row">
                        <div class="form-group">
                           <?php if ($startDate && $endDate) { ?>
                              <div class="col-sm-4"><b>Start Date: </b><?php echo date('m/d/Y', strtotime($startDate)); ?> <br> <b>End Date: </b><?php echo date('m/d/Y', strtotime($endDate)); ?><br /> <b>Rotation: </b><?php echo ($mainRotationName); ?> </div>
                           <?php } elseif ($startDate) { ?>
                              <div class="col-sm-3"><b>Start Date: </b><?php echo ($startDate); ?></div>
                           <?php } elseif ($endDate) { ?>
                              <div class="col-sm-3"><b>End Date: </b><?php echo ($endDate); ?></div>
                           <?php } elseif ($rotationId) { ?>
                              <div class="col-sm-3"><b>Rotation: </b><?php echo ($mainRotationName); ?></div>
                           <?php } elseif ($location_hidden) { ?>
                              <div class="col-sm-3"><b>Location: </b><?php echo ($location_hidden); ?></div>
                           <?php }  ?>
                           <?php if ($cbosemester != '') { ?>
                              <div class="col-sm-4"><b>Semester Name: </b><?php echo ($semesterName); ?> </div>
                           <?php } ?>
                           <?php if ($subcborotation != '') { ?>
                              <div class="col-sm-5"><b>Hospital Site: </b><?php echo ($rotationName); ?> </div>
                           <?php } ?>
                           <?php if ($individual_student) { ?>
                              <div class="col-sm-3"><b>Student Name: </b><?php echo ($studentFullname); ?></div>
                           <?php }  ?>
                           <?php if ($student_rank) { ?>
                              <div class="col-sm-3"><b>Rank Name: </b><?php echo ($rankName); ?></div>
                           <?php }  ?>
                        </div>
                     </div>
                  </div>
                  <br>
                  <div class="row">
                     <table id="noClockOut" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                        <?php
                        $objAttendance = new clsAttendance();

                        $objMasteryEval = new clsMasteryEval();
                        $totalJournal = 0;
                        if (
                           isset($_POST['cboreporttype']) || ($_POST['cborotation']) || ($_POST['cboindividualstudent'])
                           || ($_POST['cbostudentrank']) || ($_POST['cboevaluator']) || ($_POST['cbolocation'])
                           || ($_POST['cbohospitalsite']) || ($_POST['startDate']) || ($_POST['endDate'])
                        ) {

                           //No clock out
                           $evaluator = $loggedClinicianId;
                           $noClockOut = $objAttendance->noClockOutDetailsForClinicianReport($currentSchoolId, $rotationId, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder, $cbosemester, $subcborotation);
                           $TotalNoClockOut = 0;
                           if ($noClockOut != '') {
                              $TotalNoClockOut = mysqli_num_rows($noClockOut);
                           } else {
                              echo "<b>No Clock Out Not Found.</b>";
                           }
                        }


                        ?>
                        <thead>
                           <tr>
                              <th>First Name</th>
                              <th>Last Name</th>
                              <th>Ranking</th>
                              <th>Rotation</th>
                              <th>Clock In Date</th>
                              <th>Clock In Time</th>

                           </tr>
                        </thead>
                        <tbody>
                           <?php
                           if ($TotalNoClockOut > 0) {
                              while ($row = mysqli_fetch_array($noClockOut)) {
                                 $firstName = stripslashes($row['firstName']);
                                 $lastName = stripslashes($row['lastName']);
                                 $Rankname = stripslashes($row['Rankname']);
                                 $fullname = $firstName . ' ' . $lastName;
                                 $title = stripslashes($row['title']);

                                 $startDate = stripslashes($row['startDate']);
                                 $startDate = converFromServerTimeZone($startDate, $TimeZone);
                                 $rotationStartDate = date('m/d/Y', strtotime($startDate));
                                 $rotationStartTime = date('h:i A', strtotime($startDate));

                                 $clockInDateTime = stripslashes($row['clockInDateTime']);
                                 $clockInDateTime = converFromServerTimeZone($clockInDateTime, $TimeZone);
                                 $clockInDate = date('m/d/Y', strtotime($clockInDateTime));
                                 $clockInTime = date('h:i A', strtotime($clockInDateTime));

                           ?>

                                 <tr>
                                    <td><?php echo ($firstName); ?></td>
                                    <td><?php echo ($lastName); ?></td>
                                    <td><?php echo ($Rankname); ?></td>
                                    <td><?php echo ($title); ?></td>

                                    <td><?php echo ($clockInDate); ?></td>
                                    <td><?php echo ($clockInTime); ?></td>


                                 </tr>
                           <?php
                              }
                           }
                           ?>
                        </tbody>
                     </table>
                  </div>
                   <br>
                  <?php
               break;

            //Soap Note
            case "SoapNote":
               echo "<b>Soap Note</b><br>";
               ?>
                  <div class="container-fluid">
                     <div class="row">
                        <div class="form-group">
                        <?php if ($startDate && $endDate) { ?>
                           <div class="col-sm-5"><b>Start Date: </b><?php echo date('m/d/Y', strtotime($startDate)); ?> <br><b>End Date: </b><?php echo date('m/d/Y', strtotime($endDate)); ?> <?php if ($rotationId) { ?><br /> <b>Rotation: </b><?php echo ($mainRotationName); ?> <?php } ?></div>
                        <?php } elseif ($startDate) { ?>
                           <div class="col-sm-3"><b>Start Date: </b><?php echo ($startDate); ?></div>
                        <?php } elseif ($endDate) { ?>
                           <div class="col-sm-3"><b>End Date: </b><?php echo ($endDate); ?></div>
                        <?php } elseif ($rotationId) { ?>
                           <div class="col-sm-3"><b>Rotation: </b><?php echo ($mainRotationName); ?></div>
                        <?php } ?>

                        <?php if ($subcborotation != '') { ?>
                           <div class="col-sm-5"><b>Hospital Site: </b><?php echo ($rotationName); ?> </div>
                        <?php } ?>
                        <?php if ($individual_student) { ?>
                           <div class="col-sm-4"><b>Student Name: </b><?php echo ($studentFullname); ?></div>
                        <?php }  ?>

                     </div>
                     </div>
                  </div>
                  <br>

                  <div class="row">
                     <table id="SoapNote" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                        <?php
                        $objSoapNote = new clsSoapNote();

                        $totalSoapNote = 0;
                        if (
                           isset($_POST['cboreporttype']) || ($_POST['cborotation']) || ($_POST['cboindividualstudent'])
                           || ($_POST['cbohospitalsite']) || ($_POST['startDate']) || ($_POST['endDate'])
                        ) {

                           $rowsSoapNote = $objSoapNote->GetAllSoapNoteForReport($currentSchoolId, $rotationId, $subcborotation, $individual_student, $evaluator, $startDate, $endDate, $AscDesc, $sordorder);
                        }

                        if ($rowsSoapNote != '') {
                           $totalSoapNote = mysqli_num_rows($rowsSoapNote);
                        } else {
                           echo "<b>Soap Note is Not Found.</b>";
                           exit;
                        }


                        ?>
                        <thead>
                           <tr>
                              <th>Date</th>
                              <th>Student Name</th>
                              <th>Rotation</th>
                              <th style="text-align: center">Clinician/Preceptor Details</th>
                              <th>Clinician/Preceptor <br>Sign Date </th>
                              <th>Student Sign Date </th>

                           </tr>
                        </thead>
                        <tbody>
                           <?php

                           if ($totalSoapNote > 0) {
                              while ($row = mysqli_fetch_array($rowsSoapNote)) {
                                 $soapNoteId = ($row['soapNoteId']);
                                 $soapNoteDate = stripslashes($row['soapNoteDate']);
                                 $rotationName = stripslashes($row['rotationName']);
                                 $isSendToPreceptor = stripslashes($row['isSendToPreceptor']);
                                 $externalPreceptorId = stripslashes($row['preceptorId']);
                                 $clinicianName = stripslashes($row['clinicianName']);
                                 $studentName = stripslashes($row['studentName']);
                                 $preceptorStatus = stripslashes($row['status']);
                                 $rotationId = $row['rotationId'];
                                 $courselocationId = $_POST['locationId'] ?? null;
                                 $parentRotationId = $_POST['parentRotationId'] ?? null;
                                 $rotationLocationId = $_POST['rotationLocationId'] ?? null;
                                 $studentSignatureDate = stripslashes($row['studentSignatureDate']);

                                 $soapNoteDate = date("m/d/Y", strtotime($soapNoteDate));
                                 if ($soapNoteDate != '12/31/1969' && $soapNoteDate != '' && $soapNoteDate != '11/30/-0001' && $soapNoteDate != '0000-00-00') {

                                    $soapNoteDate = date("m/d/Y", strtotime($soapNoteDate));
                                 } else
                                    $soapNoteDate = '';

                                 $isSendToPreceptor = stripslashes($row['isSendToPreceptor']);

                                 if ($isSendToPreceptor == 0) {
                                    $clinicianSignatureDate = stripslashes($row['clinicianSignatureDate']);
                                    if ($clinicianSignatureDate != '12/31/1969' && $clinicianSignatureDate != '' && $clinicianSignatureDate != '11/30/-0001' && $clinicianSignatureDate != '0000-00-00') {

                                       $clinicianSignatureDate = date("m/d/Y", strtotime($clinicianSignatureDate));
                                    } else
                                       $clinicianSignatureDate = '-';
                                 } else {


                                    $clinicianSignatureDate = stripslashes($row['signatureDate']); //preceptor signature date
                                    if ($clinicianSignatureDate != '12/31/1969' && $clinicianSignatureDate != '' && $clinicianSignatureDate != '11/30/-0001' && $clinicianSignatureDate != '0000-00-00') {

                                       $clinicianSignatureDate = date("m/d/Y", strtotime($clinicianSignatureDate));
                                    } else
                                       $clinicianSignatureDate = '-';
                                 }


                                 $studentSignatureDate  = stripslashes($row['studentSignatureDate']);
                                 if ($studentSignatureDate  != '12/31/1969' && $studentSignatureDate  != '' && $studentSignatureDate  != '11/30/-0001' && $studentSignatureDate  != '0000-00-00') {

                                    $studentSignatureDate  = date("m/d/Y", strtotime($studentSignatureDate));
                                 } else
                                    $studentSignatureDate  = '-';

                                 $preceptorId = stripslashes($row['preceptorId']);
                                 $isCompletedStatus = '';
                                 $preceptorHours = '-';
                                 if ($preceptorId > 0) {
                                    $objExternalPreceptors = new clsExternalPreceptors();

                                    $externalPreceptorDetail = $objExternalPreceptors->GetExternalPreceptorDetail($preceptorId);
                                    $preceptorFirstName = isset($externalPreceptorDetail['firstName']) ? $externalPreceptorDetail['firstName'] : '';
                                    $preceptorLastName = isset($externalPreceptorDetail['lastName']) ? $externalPreceptorDetail['lastName'] : '';
                                    $preceptorNum = isset($externalPreceptorDetail['mobile_num']) ? $externalPreceptorDetail['mobile_num'] : '';
                                    $preceptorFullName = $preceptorFirstName . ' ' . $preceptorLastName;
                                    $preceptorInfo = ' Name: ' . $preceptorFullName . ' </br>Phone: ' . $preceptorNum;

                                    // $isCompletedStatus = $preceptorStatus ? 'Completed' : 'Pending';
                                    if ($preceptorStatus)
                                       $preceptorInfo .= ' </br>Status: Completed';
                                    else
                                       $preceptorInfo .= ' </br>Status: Pending';

                                    $preceptorHours = isset($externalPreceptorDetail['preceptorhours']) ? $externalPreceptorDetail['preceptorhours'] : '';
                                    if (!empty($preceptorHours) && $preceptorHours != '00:00:00') {
                                       // Create a DateTime object from the preceptor hours
                                       $time = DateTime::createFromFormat('H:i:s', $preceptorHours);

                                       // Extract hours and minutes
                                       $hours = (int) $time->format('H');
                                       $minutes = (int) $time->format('i');

                                       // Format as HH:MM
                                       $formattedPreceptorHours = sprintf('%02d:%02d', $hours, $minutes);
                                       $preceptorHours = $formattedPreceptorHours;
                                    } else {
                                       $preceptorHours = '-'; // Default if the value is 0 or empty
                                    }
                                    $objDB = new clsDB();
                                    unset($objExternalPreceptors);
                                 }

                           ?>

                                 <tr>
                                    <td>
                                       <?php echo ($soapNoteDate);
                                       ?>
                                    </td>
                                    <td>
                                       <?php echo ($studentName);
                                       ?>
                                    </td>
                                    <td>
                                       <?php echo ($rotationName);
                                       ?>
                                    </td>
                                    <td>
                                       <?php if ($preceptorId)
                                          echo ($preceptorInfo);
                                       else
                                          echo ($clinicianName); ?>
                                    </td>
                                    <td align="center">
                                       <?php echo ($clinicianSignatureDate); ?>
                                    </td>
                                    <td align="center">
                                       <?php echo ($studentSignatureDate); ?>
                                    </td>

                                 </tr>
                           <?php
                              }
                           }
                           ?>
                        </tbody>
                     </table>
                  </div>
                  <br>
            <?php
               break;

            default:
               echo "<b>Accreditation Tracking Information Start/End Date</b>";
               break;
         }
            ?>
               </div>
            </div>
   </div>
   <?php include('includes/footer.php'); ?>
   <?php include("includes/datatablejs.php") ?>
   <script src="https://cdn.datatables.net/buttons/1.2.2/js/dataTables.buttons.min.js"></script>
   <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/2.5.0/jszip.min.js"></script>
   <script src="https://cdn.rawgit.com/bpampuch/pdfmake/0.1.18/build/pdfmake.min.js"></script>
   <script src="https://cdn.rawgit.com/bpampuch/pdfmake/0.1.18/build/vfs_fonts.js"></script>
   <script src="https://cdn.datatables.net/buttons/1.2.2/js/buttons.html5.min.js"></script>
   <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
   <!-- <script src="<?php //echo ($dynamicOrgUrl); 
                     ?>/assets/js/exportTables/table2csv.min.js"></script> -->
   <script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/fixedcolumns/3.2.4/js/dataTables.fixedColumns.min.js"></script>



   <script type="text/javascript">
      alertify.defaults.transition = "slide";
      alertify.defaults.theme.ok = "btn btn-success";
      alertify.defaults.theme.cancel = "btn btn-danger";
      alertify.defaults.theme.input = "form-control";


      $(window).load(function() {
         $("#divTopLoading").addClass('hide');
         $("#loading-div-background").hide();



      });


      jQuery(document).ready(function() {
         jQuery(".main-table").clone(true).appendTo('#table-scroll').addClass('clone');
      });


      // var studentPortfolioDatatable = $("#student-portfolio-datatable").DataTable({
      // "paging":   false,
      // "ordering": false,
      // "info":     false
      // });	


      var currentSchoolId = '<?php echo ($currentSchoolId); ?>';
      var rotationId = '<?php echo ($rotationId); ?>';
      var isActiveCheckoff = '<?php echo ($isActiveCheckoff); ?>';
      var individual_student = '<?php echo ($individualStudentArray); ?>';
      var student_rank = '<?php echo ($student_rank); ?>';
      var evaluator = '<?php echo ($evaluator); ?>';
      var school_location = '<?php echo ($school_location); ?>';
      var hospital_site = '<?php echo ($hospital_site); ?>';
      var startDate = '<?php echo ($startDate); ?>';
      var endDate = '<?php echo ($endDate); ?>';
      var checkoffTopic = '<?php echo ($checkoffTopic); ?>';
      var AscDesc = '<?php echo ($AscDesc); ?>';
      var sordorder = '<?php echo ($sordorder); ?>';
      var cbosemester = '<?php echo ($cbosemester); ?>';
      var studentIdArr = '<?php echo ($studentIdArr); ?>';
      var subcborotation = '<?php echo ($subcborotation); ?>';
      var cboreporttype = '<?php echo ($reportType); ?>';
      var clinicianId = '<?php echo ($clinicianId); ?>';

      var current_datatable = $("#checkoff-datatable").DataTable({
         "searching": false,
         "ordering": false,
         "processing": true,
         drawCallback: noOfRows,
         "bServerSide": true,
         "paging": false,
         "ajax": {
            url: "../ajax/ajax_get_student_checkoff_to_report.html", // json datasource
            type: "POST", // method  , by default get
            data: {
               'currentSchoolId': currentSchoolId,
               'isActiveCheckoff': isActiveCheckoff,
               'rotationId': rotationId,
               'subcborotation': subcborotation,
               'individual_student': individual_student,
               'studentIdArr': studentIdArr,
               'student_rank': student_rank,
               'evaluator': evaluator,
               'clinicianId': clinicianId,
               'school_location': school_location,
               'hospital_site': hospital_site,
               'startDate': startDate,
               'endDate': endDate,
               'checkoffTopic': checkoffTopic,
               'AscDesc': AscDesc,
               'sordorder': sordorder,
               'cbosemester': cbosemester,
               'isClinician': 1
            },
            error: function() { // error handling
               $(".employee-grid-error").html("");
               $("#checkoff-datatable").append('<tbody class="employee-grid-error"><tr><th colspan="3">No data found in the server</th></tr></tbody>');
               $("#employee-grid_processing").css("display", "none");
            }
         },
         dom: 'Bfrtip',
         buttons: [{
            extend: 'excelHtml5',
            text: 'Export to Excel',
            message: "Checkoffs Report",
            title: "Checkoffs Report",
            className: 'btn-primary '
         }]
      });

      var current_datatable = $("#precheckoff-datatable").DataTable({
         "searching": false,
         "ordering": false,
         "processing": true,
         "bServerSide": true,
         "paging": false,
         "ajax": {
            url: "../ajax/ajax_get_student_Preceptor_checkoff_to_report.html", // json datasource
            type: "POST", // method  , by default get
            data: {
               'currentSchoolId': currentSchoolId,
               'isActiveCheckoff': isActiveCheckoff,
               'rotationId': rotationId,
               'subcborotation': subcborotation,
               'individual_student': individual_student,
               'studentIdArr': studentIdArr,
               'student_rank': student_rank,
               'evaluator': evaluator,
               'school_location': school_location,
               'hospital_site': hospital_site,
               'startDate': startDate,
               'endDate': endDate,
               'checkoffTopic': checkoffTopic,
               'AscDesc': AscDesc,
               'sordorder': sordorder,
               'cbosemester': cbosemester,
            },
            error: function() { // error handling
               $(".employee-grid-error").html("");
               $("#checkoff-datatable").append('<tbody class="employee-grid-error"><tr><th colspan="3">No data found in the server</th></tr></tbody>');
               $("#employee-grid_processing").css("display", "none");
            }
         },
         dom: 'Bfrtip',
         buttons: [{
               extend: 'excelHtml5',
               text: 'Export to Excel',
               message: "Preceptor Checkoffs Report",
               title: "Preceptor Checkoffs Report",
               className: 'btn-primary '
            }


         ]
      });

      $('.notes').change(function(e) {

         var attendanceid = $(this).attr('attendanceid');
         var loggedId = $(this).attr('loggedId');
         var notes = $(this).val();

         $.ajax({
            type: "POST",
            url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_send_notes.html",
            data: {
               id: attendanceid,
               notes: notes,
               loggedId: loggedId
            }
         });

      });

      $(document).ready(function() {
         // document.onreadystatechange = function(e)
         // {
         //    if(document.readyState=="interactive")
         //    {
         //       var all = document.getElementsByTagName("*");
         //       for (var i=0, max=all.length; i < max; i++) 
         //       {
         //          set_ele(all[i]);
         //       }
         //    }
         // }

      });


      function check_element(ele) {
         var all = document.getElementsByTagName("*");
         var totalele = all.length;
         var per_inc = 100 / all.length;

         if ($(ele).on()) {

            var prog_width = per_inc + Number(document.getElementById("progress_width").value);
            document.getElementById("progress_width").value = prog_width;
            $("#bar1").animate({
               width: prog_width + "%"
            }, 10, function() {
               if (document.getElementById("bar1").style.width == "100%") {
                  $(".progress").fadeOut("slow");
                  $("#loading-div-background").hide();
               }
            });
         } else {
            set_ele(ele);
         }
      }

      function set_ele(set_element) {
         check_element(set_element);
      }

      // var dailyWeeklyDetailDatatable = $("#dailyWeeklyDetailDatatable").DataTable({


      // });

      var responsiveDatatable = $(".responsive-datatable").DataTable({
         "autoWidth": true,
         // "sScrollX" : true,
         "ordering": false,
         "paging": false,
         responsive: true,
         dom: 'Bfrtip',
         buttons: [{
               extend: 'excelHtml5',
               text: 'Export to Excel',
               className: 'btn-primary'
            },
            // {
            //    extend: 'pdfHtml5',
            //    text: 'Export to PDF',
            //    className: 'btn-primary'   
            // }
         ]
      });

      var dailyWeeklyDetailDatatable = $("#dailyWeeklyDetailDatatable").DataTable({
         responsive: false,
         "sScrollX": true,
         "ordering": false,
         "paging": false,
         "info": false,
         drawCallback: noOfRows,
         scrollCollapse: true,
         scrollY: "500px",
         scrollX: true,
         // fixedColumns: {
         //    leftColumns: 3
         // },
         "ordering": false,
         "paging": false,
         dom: 'Bfrtip',
         buttons: [{
               extend: 'excelHtml5',
               text: 'Export to Excel',
               message: "Daily/Weekly Details Report",
               title: "Daily/Weekly Details Report",
               className: 'btn-primary'
            }

         ]
      });
      var dailyweeklyTable = $("#dailyweeklyTable").DataTable({
         responsive: false,
         "sScrollX": true,
         "ordering": false,
         "paging": false,
         "info": false,
         drawCallback: noOfRows,
         scrollCollapse: true,
         scrollY: "500px",
         scrollX: true,
         fixedColumns: {
            leftColumns: 3
         },
         "ordering": false,
         "paging": false,
         dom: 'Bfrtip',
         buttons: [{
               extend: 'excelHtml5',
               text: 'Export to Excel',
               message: "Daily/Weekly Evaluation Report",
               title: "Daily/Weekly Evaluation Report",
               className: 'btn-primary'
            }

         ]

      });
      // PEF_I_EvaluationTable 
      var pef1evl = $("#PEF_I_EvaluationTable").DataTable({
         responsive: false,
         "sScrollX": true,
         "ordering": false,
         "paging": false,

         scrollCollapse: true,
         scrollY: "500px",
         scrollX: true,
         // fixedColumns: {
         //    leftColumns: 3
         // },

         dom: 'Bfrtip',
         buttons: [{
               extend: 'excelHtml5',
               text: 'Export to Excel',
               message: "PEF I Evaluation Report",
               title: "PEF I Evaluation Report",
               className: 'btn-primary pef1evl'
            }


         ]
      });

      var procedure_count_summery = $(".attendanceTable").DataTable({
         responsive: false,
         "bSort": false,
         "ordering": false,
         drawCallback: noOfRows,
         "paging": false,
         // scrollX: true,           
         dom: 'Bfrtip',
         buttons: [{
               extend: 'excelHtml5',
               text: 'Export to Excel',
               message: "Procedure Count Summery Report",
               title: "Procedure Count Summery Report",
               className: 'btn-primary'
            }


         ]
      });

      var attendanceTable = $("#attendanceTable").DataTable({
         responsive: false,
         "bSort": false,
         "ordering": false,
         "paging": false,
         drawCallback: noOfRows,
         scrollX: true,
         fixedColumns: {
            leftColumns: 4 // Set the number of columns you want to fix on the left side
         },
         // "ajax":{
         //       url :"../ajax/ajax_get_reports.html", // json datasource
         //       type: "POST",  // method  , by default get
         //       data: {
         //             'currentSchoolId': currentSchoolId,
         //             'isActiveCheckoff':isActiveCheckoff,
         //             'rotationId': rotationId,
         //             'subcborotation': subcborotation,
         //             'individual_student': individual_student,
         //             'studentIdArr': studentIdArr,
         //             'student_rank': student_rank,
         //             'evaluator': evaluator,
         //             'school_location': school_location,
         //             'hospital_site': hospital_site,
         //             'startDate': startDate,
         //             'endDate': endDate,
         //             'checkoffTopic': checkoffTopic,
         //             'AscDesc': AscDesc,
         //             'sordorder': sordorder,
         //             'cbosemester': cbosemester,
         //             'reportType': cboreporttype,
         //       },
         //       error: function(){  // error handling
         //             $(".employee-grid-error").html("");
         //             $("#checkoff-datatable").append('<tbody class="employee-grid-error"><tr><th colspan="3">No data found in the server</th></tr></tbody>');
         //             $("#employee-grid_processing").css("display","none");
         //       }
         //    },         
         dom: 'Bfrtip',
         buttons: [{
               extend: 'excelHtml5',
               text: 'Export to Excel',
               message: "Attendance Details Report",
               title: "Attendance Details Report",
               className: 'btn-primary'
            }


         ]
      });
      // Sum of column values
      // //       var table = $('#example').DataTable(); // replace 'example' with your table id
      //       var totalHours = 0;
      //       var ApprovedHours = 0;

      //       var totalMinutes = 0;

      //       attendanceTable.column(11).data().each(function (value, index) {
      //          var time = value.split(':');
      //          var hours = parseFloat(time[0]);
      //          var minutes = parseFloat(time[1]);
      //          if (!isNaN(hours) && !isNaN(minutes)) {
      //             totalMinutes += (hours * 60) + minutes;
      //          }
      //       });

      //       var hours = Math.floor(totalMinutes / 60);
      //       var minutes = totalMinutes % 60;

      //       var formattedResult = hours.toString().padStart(2, '0') + ':' + minutes.toString().padStart(2, '0');
      //       // console.log('Total Time:', formattedResult);
      //       // attendanceTable.column(12).data().each(function (value, index) {
      //       //     var hours = parseFloat(value);
      //       //     if (!isNaN(hours)) {
      //       //       ApprovedHours += hours;
      //       //     }
      //       // });

      //       // console.log('Total Hours:', totalHours);
      // // console.log('Total Hours:', ApprovedHours);

      var attendanceDailyWeekly = $("#attendanceDailyWeekly").DataTable({
         responsive: false,
         "bSort": false,
         "ordering": false,
         "paging": false,
         drawCallback: noOfRows,
         scrollX: true,
         dom: 'Bfrtip',
         buttons: [{
               extend: 'excelHtml5',
               text: 'Export to Excel',
               message: "Attendance Daily/Weekly Report",
               title: "Attendance Daily/Weekly Report",
               className: 'btn-primary'
            }


         ]
      });

      var checkoffBycourses = $("#checkoff-by-courses-datatable").DataTable({
         responsive: false,
         "bSort": false,
         "ordering": false,
         "paging": false,
         "sScrollX": true,
         scrollX: true,
         fixedColumns: {
            leftColumns: 4 // Set the number of columns you want to fix on the left side
         },
         drawCallback: noOfRows,
         dom: 'Bfrtip',
         buttons: [{
               extend: 'excelHtml5',
               text: 'Export to Excel',
               message: " Checkoff by Courses Report",
               title: " Checkoff by Courses Report",
               className: 'btn-primary'
            }


         ]
      });

      // attendance-Summery
      var attendanceSummery = $("#attendance-Summery").DataTable({
         responsive: false,
         "bSort": false,
         "ordering": false,
         "paging": false,
         "info": false,
         scrollX: true,
         dom: 'Bfrtip',
         drawCallback: noOfRows,
         buttons: [{
               extend: 'excelHtml5',
               text: 'Export to Excel',
               message: " Attendance Summary Report",
               title: " Attendance Summary Report",
               className: 'btn-primary'
            }


         ]
      });

      // drInteraction
      var drInteraction = $("#drInteraction").DataTable({
         responsive: false,
         "autoWidth": true,
         "bSort": false,
         "ordering": false,
         "paging": false,
         drawCallback: noOfRows,
         scrollX: true,
         fixedColumns: {
            leftColumns: 4
         },
         dom: 'Bfrtip',
         buttons: [{
               extend: 'excelHtml5',
               text: 'Export to Excel',
               message: " Dr Interaction Report",
               title: " Dr Interaction Report",
               className: 'btn-primary'
            }


         ]
      });

      // Jouranal
      var journal = $("#journal").DataTable({
         responsive: false,
         "autoWidth": true,
         "bSort": false,
         "ordering": false,
         "paging": false,
         scrollX: true,
         drawCallback: noOfRows,
         "info": false,
         fixedColumns: {
            leftColumns: 4
         },
         dom: 'Bfrtip',
         buttons: [{
               extend: 'excelHtml5',
               text: 'Export to Excel',
               message: " Journal Report",
               title: " Journal Report",
               className: 'btn-primary'
            }


         ]
      });
      // PEF_II_EvaluationTable
      var pef2evl = $("#PEF_II_EvaluationTable").DataTable({
         responsive: false,
         "sScrollX": true,
         "ordering": false,
         "paging": false,

         scrollCollapse: true,
         scrollY: "500px",
         scrollX: true,
         fixedColumns: {
            leftColumns: 3
         },

         dom: 'Bfrtip',
         buttons: [{
               extend: 'excelHtml5',
               text: 'Export to Excel',
               message: "PEF II Evaluation Report",
               title: "PEF II Evaluation Report",
               className: 'btn-primary pef2evl'
            }


         ]
      });

      // late clock in 
      var lateClockIn = $("#lateClockIn").DataTable({
         responsive: false,
         "sScrollX": true,
         "ordering": false,
         "paging": false,

         // scrollCollapse: true,
         // scrollY:        "500px",
         // scrollX: true,


         dom: 'Bfrtip',
         buttons: [{
               extend: 'excelHtml5',
               text: 'Export to Excel',
               message: "Late Clock In Report",
               title: "'Late Clock In Report",
               className: 'btn-primary pef2evl'
            }


         ]
      });

      // early Clock Out
      var earlyClockOut = $("#earlyClockOut").DataTable({
         responsive: false,
         "sScrollX": true,
         "ordering": false,
         "paging": false,

         // scrollCollapse: true,
         // scrollY:        "500px",
         // scrollX: true,


         dom: 'Bfrtip',
         buttons: [{
               extend: 'excelHtml5',
               text: 'Export to Excel',
               message: "Early Clock Out Report",
               title: "Early Clock Out Report",
               className: 'btn-primary pef2evl'
            }


         ]
      });

      var totalPEFIIdetailsCount = '<?php echo $totalPEFIIdetailsCount; ?>';
      var totalPEFIdetailsCount = '<?php echo $totalPEFIdetailsCount; ?>';
      // if(totalPEFIdetailsCount == 0 )
      //    $('.pef1evl').hide();

      // if(totalPEFIIdetailsCount == 0)
      //    $('.pef2evl').hide();
      function noOfRows() {
         // var a = $(table).attr('id');
         var id = $(this).attr('id');
         // console.log( 'Table redrawn '+id );
         // Get a reference to the DataTable instance
         var table = $('#' + id).DataTable();

         // Get the number of rows in the table
         var numRows = table.rows().count();
         if (numRows == 1 && (id == 'attendanceTable' || id == 'attendanceSummery' || id == 'drInteraction')) {
            $('.btn-primary').hide();
         } else if (numRows == 0) {
            $('.btn-primary').hide();
         }
         // Log the number of rows to the console
         // console.log(numRows);

      };

      // Case Study Report
      $('#casestudy-datatable').DataTable({
         responsive: false,
         "sScrollX": false,
         "ordering": false,
         "paging": false,
         dom: 'Bfrtip',
         buttons: [{
               extend: 'excelHtml5',
               text: 'Export to Excel',
               message: "Case Study Report",
               title: "Case Study Report",
               className: 'btn-primary pef2evl'
            }


         ]
      });

      // no Clock Out
      var noClockOut = $("#noClockOut").DataTable({
         responsive: false,
         "sScrollX": true,
         "ordering": false,
         "paging": false,

         // scrollCollapse: true,
         // scrollY:        "500px",
         // scrollX: true,


         dom: 'Bfrtip',
         buttons: [{
               extend: 'excelHtml5',
               text: 'Export to Excel',
               message: "No Clock Out Report",
               title: "No Clock Out Report",
               className: 'btn-primary pef2evl'
            }


         ]
      });

      //Soap Note
      var SoapNote = $("#SoapNote").DataTable({
         responsive: false,
         "sScrollX": true,
         "ordering": false,
         "paging": false,

         // scrollCollapse: true,
         // scrollY:        "500px",
         // scrollX: true,


         dom: 'Bfrtip',
         buttons: [{
               extend: 'excelHtml5',
               text: 'Export to Excel',
               message: "Soap Note Report",
               title: "Soap Note Report",
               className: 'btn-primary pef2evl'
            }


         ]
      });
     
   </script>
</body>

</html>

</html>