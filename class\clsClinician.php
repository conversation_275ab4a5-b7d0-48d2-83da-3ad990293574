<?php
class clsClinician
{
	var $clinicianId = 0;
	var $schoolId = '';
	var $profilePic = '';
	var $firstName = '';
	var $middleName = '';
	var $lastName = '';
	var $address1 = '';
	var $address2 = '';
	var $city = '';
	var $stateId = '';
	var $zip = '';
	var $phone = '';
	var $cellPhone = '';
	var $email = '';
	var $username = '';
	var $passwordHash = '';
	var $isActive = '';
	var $createdBy = '';
	var $createdDate = '';
	var $updatedBy = '';
	var $updatedDate = '';
	var $locationId = '';
	var $clinicianRoleId = '';
	var $emailtopassword = 0;



	function SaveClinician($clinicianId = 0)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($clinicianId > 0) {
			$sql = "UPDATE clinician SET  
						firstName = '" . addslashes($this->firstName) . "',
						middleName = '" . addslashes($this->middleName) . "',
						lastName = '" . addslashes($this->lastName) . "',
						address1 = '" . addslashes($this->address1) . "',
						address2 = '" . addslashes($this->address2) . "',
						email = '" . addslashes($this->email) . "',
						city = '" . addslashes($this->city) . "',
						stateId = '" . addslashes($this->stateId) . "',
						locationId = '" . ($this->locationId) . "',						
						clinicianRoleId = '" . ($this->clinicianRoleId) . "',						
						zip = '" . addslashes($this->zip) . "',
						phone = '" . addslashes($this->phone) . "',
						cellPhone = '" . addslashes($this->cellPhone) . "',
						username = '" . addslashes($this->username) . "',
						updatedBy = '" . addslashes($this->createdBy) . "',
						updatedDate = '" . (date("Y-m-d h:i:s")) . "',
						isEmailPassword = '" . addslashes($this->emailtopassword) . "'
						Where clinicianId=" . $clinicianId;

			// echo $sql;
			// exit;
			$objDB->ExecuteQuery($sql);
		} else {
			$sql = "INSERT INTO clinician (firstName, middleName, lastName,
											schoolId, locationId, clinicianRoleId,  address1, address2, city,  stateId, zip, 
										 email, phone, cellPhone, username, passwordHash, isActive,
										 createdBy, createdDate, isEmailPassword)
								VALUES  ('" . addslashes($this->firstName) . "',
											'" . addslashes($this->middleName) . "',
											'" . addslashes($this->lastName) . "',
											'" . ($this->schoolId) . "',
											'" . ($this->locationId) . "',
											'" . ($this->clinicianRoleId) . "',
											'" . addslashes($this->address1) . "',
											'" . addslashes($this->address2) . "',											
											'" . ($this->city) . "',
											'" . ($this->stateId) . "',
											'" . ($this->zip) . "',											
											'" . ($this->email) . "',
											'" . ($this->phone) . "',
											'" . ($this->cellPhone) . "',
											'" . ($this->username) . "',
											'" . ($this->passwordHash) . "',
											'" . ($this->isActive) . "',
											'" . ($this->createdBy) . "',
											'" . (date("Y-m-d h:i:s")) . "',
											'" . ($this->emailtopassword) . "'
											)";

			// echo $sql;
			// exit;
			$clinicianId = $objDB->ExecuteInsertQuery($sql);
		}

		unset($objDB);
		return $clinicianId;
	}

	function UpdateClinicianProfile($clinicianId)
	{
		if ($clinicianId > 0) {
			$objDB = new clsDB();
			$sql = "UPDATE clinician SET 										 
										firstName = '" . addslashes($this->firstName) . "', 
										lastName = '" . addslashes($this->lastName) . "',
										email = '" . addslashes($this->email) . "',
										username = '" . addslashes($this->username) . "',										
										updatedBy = '" . addslashes($this->updatedBy) . "',
										updatedDate = '" . addslashes($this->updatedDate) . "'
										Where clinicianId= " . $clinicianId;
			$objDB->ExecuteQuery($sql);
			unset($objDB);
		}
		return $clinicianId;
	}

	function DeleteClinician($clinicianId, $SchoolId)
	{
		$result = "";
		if ($clinicianId > 0) {

			$schoolDir = ROOT_PATH . "/upload/schools/" . $SchoolId . "/clinician/" . $clinicianId;
			delete_directory($schoolDir);
			$objDB = new clsDB();
			$sql = "DELETE FROM clinician WHERE clinicianId = " . $clinicianId;

			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
		return $result;
	}

	function GetAllClinicians()
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM clinician";
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	function GetAllIntractionClinicians($schoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM `clinician` WHERE schoolId =" . $schoolId . " AND clinician.isActive=1 ORDER BY clinician.firstName ASC";
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	function GetAllCliniciansWithoutPreceptor($schoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT clinician.*,clinicianrolemaster.type FROM `clinician` 
		INNER JOIN clinicianrolemaster ON clinician.clinicianRoleId =clinicianrolemaster.clinicianRoleId
		WHERE clinician.schoolId =" . $schoolId . "  AND clinician.isActive = 1 AND clinician.isBlocked = 0  AND clinicianrolemaster.type !='P' ";

		$sql .= " ORDER BY clinician.firstName ASC";
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllSchoolClinicians($schoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
				clinician.clinicianId,
				clinician.firstName,
				clinician.lastName,
				clinician.email,
				clinician.phone,
				clinician.cellPhone,
				clinician.username,							
				clinician.isActive,							
				clinician.isBlocked,							
				IFNULL(location.title,'-') as location, clinicianrolemaster.title as role FROM clinician 
			  LEFT JOIN location on clinician.locationId = location.locationId 
			  LEFT JOIN clinicianrolemaster on clinician.clinicianRoleId = clinicianrolemaster.clinicianRoleId 
			    WHERE clinician.schoolId=" . $schoolId . " ORDER BY clinician.firstName ASC";



		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	/* 10052021 */
	function GetAllSchoolCliniciansActive($schoolId, $clinicianRoleId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
				clinician.clinicianId,
				clinician.firstName,
				clinician.lastName,
				clinician.email,
				clinician.phone,
				clinician.cellPhone,
				clinician.username,							
				clinician.isActive,							
				clinician.isBlocked,
				clinician.clinicianRoleId,							
				IFNULL(location.title,'-') as location, clinicianrolemaster.title as role FROM clinician 
			  LEFT JOIN location on clinician.locationId = location.locationId 
			  LEFT JOIN clinicianrolemaster on clinician.clinicianRoleId = clinicianrolemaster.clinicianRoleId 
			    WHERE clinician.schoolId=" . $schoolId . " AND clinician.isActive = 1";
		if ($clinicianRoleId)
			$sql .=	" AND clinicianrolemaster.clinicianRoleId = $clinicianRoleId ";
		$sql .=	" ORDER BY clinician.firstName ASC";
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	/* 10052021 */

	function GetClinicianDetails($clinicianId)
	{
		$objDB = new clsDB();
		$sql = "SELECT clinician.*, schools.isActiveCheckoffForStudent AS isActiveCheckoff,schools.isDefaultCiEval,schools.schoolId,schools.displayName,schools.logoSmallName,
				timezonemaster.timezone,clinicianrolemaster.type AS loggedClinicianType,clinicianrolemaster.title
				FROM  clinician 
				INNER JOIN schools ON clinician.schoolId=schools.schoolId
				INNER JOIN timezonemaster ON schools.timeZoneId=timezonemaster.timeZoneId
				INNER JOIN clinicianrolemaster ON clinician.clinicianRoleId=clinicianrolemaster.clinicianRoleId
				WHERE clinicianId=" . $clinicianId;
		// echo $sql;exit;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}

	function GetClinicianTimeZoneByClinicianId($clinicianId)
	{
		//Get User Time Zone
		$sql = "SELECT timezonemaster.timezone
				FROM clinician
				INNER JOIN schools ON clinician.schoolId=schools.schoolId
				INNER JOIN  timezonemaster ON  timezonemaster.timeZoneId=schools.timeZoneId
				WHERE clinician.clinicianId = " . $clinicianId;

		$objDB = new clsDB();
		$timezone = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		$timezone = $timezone ? $timezone : SERVER_TIMEZONE;
		return $timezone;
	}

	function CheckLogin($schoolId, $username)
	{
		$systemUser = "0";
		$objDB = new clsDB();
		$sql = "SELECT clinician.*, timezonemaster.timezone,schools.isActiveCheckoffForStudent,schools.isDefaultCiEval,
				clinicianrolemaster.type AS loggedClinicianType
				FROM clinician 
				INNER JOIN schools ON clinician.schoolId=schools.schoolId
				INNER JOIN  timezonemaster ON  timezonemaster.timeZoneId=schools.timeZoneId
				INNER JOIN clinicianrolemaster ON clinician.clinicianRoleId=clinicianrolemaster.clinicianRoleId
				WHERE clinician.schoolId = " . $schoolId . " AND clinician.username = '" . $username . "'";
		// 		echo $sql;exit;
		$systemUser = $objDB->GetDataRow($sql);

		unset($objDB);
		return $systemUser;
	}

	function UpdateClinicianpassword($currentSchoolId, $clinicianId, $Confirmpassword, $isPasswordUpdated = 1, $checkText = 1)
	{
		$objDB = new clsDB();
		$sql = "Update clinician Set 
				passwordHash ='" . addslashes($Confirmpassword) . "',
				isPasswordUpdated =" . $isPasswordUpdated . ",	
				isChecked =" . $checkText . "			
				Where clinicianId=" . $clinicianId . " AND schoolId=" . $currentSchoolId;

		//echo $sql;exit;
		$objDB->ExecuteQuery($sql);
		unset($objDB);
	}

	function ValidateClinicianEmail($email, $currentSchoolId)
	{
		$clinicianId = "0";
		$objDB = new clsDB();
		$sql = "select clinicianId from clinician where email = '" . addslashes($email) . "' AND schoolId=" . $currentSchoolId;
		$retUserId = $objDB->GetDataRow($sql);
		unset($objDB);
		return $retUserId;
	}

	function ValidateApiClinicianEmail($email, $schoolCode)
	{
		$sql = "SELECT clinician.*,schools.displayName,schools.logoSmallName
				FROM clinician
				INNER JOIN schools ON clinician.schoolId=schools.schoolId
				WHERE clinician.email =" . "'$email' AND code='" . $schoolCode . "'";
		// echo $sql;
		$objDB = new clsDB();
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}

	function UpdateClinicianPhotosFileName($clinicianId, $smallProfilePic, $profilePic)
	{
		if ($clinicianId > 0) {
			$objDB = new clsDB();
			$sql = "Update clinician set smallProfilePic = '" . $smallProfilePic . "',
			profilePic = '" . $profilePic . "'  Where clinicianId = " . $clinicianId;

			// echo $sql;exit;
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}	

		return $clinicianId;

	}

	function SetClinicianStatus($clinicianId, $status, $schoolId)
	{
		if ($clinicianId > 0) {
			$objDB = new clsDB();
			$sql = "Update clinician set isActive = " . $status . " Where clinicianId = " . $clinicianId .
				" AND schoolId=" . $schoolId;
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);

			return $clinicianId;
		}
	}
	function IsDuplicateUserName($clinicianId, $userName, $schoolId)
	{
		$sql = '';
		$retUserId = 0;
		$objDB = new clsDB();

		$sql = "select clinicianId from clinician where LOWER(username) = '" . strtolower($userName) . "' and schoolId=" . $schoolId;
		if ($clinicianId)
			$sql .= " and clinicianId != '" . $clinicianId;
		//echo $sql; exit;
		$retUserId = $objDB->GetSingleFieldValue($sql);
		unset($objDB);


		if ($retUserId > 0) {

			return 1;
		} else {

			return 0;
		}
	}


	function GetClinicianCounts($schoolId)
	{
		$returnCounts = '';
		$objDB = new clsDB();
		$sql = "SELECT A.TotalCount, B.ActiveCount  FROM (SELECT COUNT(*) As TotalCount FROM `clinician` WHERE schoolId=" . $schoolId . ") As A,
				(SELECT COUNT(*) As ActiveCount FROM `clinician` WHERE isActive=1 AND schoolId=" . $schoolId . ") As B";
		$returnCounts = $objDB->GetDataRow($sql);
		// echo $sql;
		unset($objDB);
		return $returnCounts;
	}

	function SaveFailedLogin($clinicianId, $currentSchoolId)
	{
		$lastLoginFailedTime = date('Y-m-d H:i:s');
		//Save Failed login to DB 		
		//------------------------------------------------------------ 	
		$objDB = new clsDB();
		$sql = "Insert Into clinicialloginlogs(clinicianId)
	Values('" . $clinicianId . "')";
		$objDB->ExecuteInsertQuery($sql);  		//Make Auto Lock Records 
		$sql = "Select COUNT(*) From clinicialloginlogs Where DATE_ADD(LastFailedLoginTime, INTERVAL 15 MINUTE)
	>= NOW() and clinicianId=" . $clinicianId;

		$totalFailedCount = $objDB->GetSingleFieldValue($sql);
		if ($totalFailedCount > 4) //lockout after 5 failed login attempts within 15 minutes 
		{ 			//Make Blocked 			
			$sql = "Update clinician SET isPasswordUpdated = 1, isBlocked=1 
	Where clinicianId = " . $clinicianId;
			$objDB->ExecuteQuery($sql);
			//Clear All Records 			
			$this->ClearFailedLogin($clinicianId);  			//Notify Email to Clinician and DCE - For Reset the password 	
			$objSendEmails = new clsSendEmails($currentSchoolId);
			$objSendEmails->NotifyToClinicianAndDCEClinicianLockedOut($clinicianId);
			unset($objSendEmails);
		}
		unset($objDB);
	}

	function ClearFailedLogin($clinicianId)
	{
		$objDB = new clsDB();
		$sql = "Delete from clinicialloginlogs Where clinicianId = " . $clinicianId;
		$objDB->ExecuteQuery($sql);
		unset($objDB);
	}

	function SetClinicianBlockUnblock($clinicianId, $block, $schoolId)
	{
		if ($clinicianId > 0) {
			$objDB = new clsDB();
			$sql = "Update clinician set isBlocked = " . $block . " Where clinicianId = " . $clinicianId .
				" AND schoolId=" . $schoolId;
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
	}

	function GetClinicianByRotation($schoolId, $rotationId)
	{
		$objDB = new clsDB();
		$sql = "SELECT  clinician.clinicianId,clinician.firstName,clinician.lastName,clinician.schoolId,
				rotation.hospitalSiteId,rotation.rotationId,
				clinicianhospitalsite.clinicianId,clinicianhospitalsite.hospitalSiteId,
				schools.schoolId
				FROM  clinician
				INNER JOIN schools ON clinician.schoolId=schools.schoolId
				LEFT JOIN clinicianhospitalsite ON clinician.clinicianId =clinicianhospitalsite.clinicianId
				LEFT JOIN rotation ON clinicianhospitalsite.hospitalSiteId = rotation.hospitalSiteId";
		$sql .= " WHERE schools.schoolId =" . $schoolId . " AND clinician.isActive = 1";
		if ($rotationId > 0) {
			$sql .= " AND rotation.rotationId =" . $rotationId;
		}
		$sql .= " group by clinician.clinicianId ORDER BY firstName ASC";
		// echo $sql;exit; 
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}

	function GetClinicianByRotationsHospitalSite($schoolId, $rotationId)
	{
		$objDB = new clsDB();
		$sql = "SELECT  clinician.clinicianId,clinician.firstName,clinician.lastName,clinician.schoolId,
				rotation.hospitalSiteId,rotation.rotationId,
				clinicianhospitalsite.clinicianId,clinicianhospitalsite.hospitalSiteId,
				schools.schoolId
				FROM  clinician
				INNER JOIN schools ON clinician.schoolId=schools.schoolId
				INNER JOIN clinicianhospitalsite ON clinician.clinicianId =clinicianhospitalsite.clinicianId
				LEFT JOIN rotation ON clinicianhospitalsite.hospitalSiteId = rotation.hospitalSiteId";
		$sql .= " WHERE schools.schoolId =" . $schoolId;
		if ($rotationId > 0) {
			$sql .= " AND rotation.rotationId =" . $rotationId;
		}
		$sql .= " AND clinician.isActive=1 group by clinician.clinicianId ORDER BY firstName ASC";
		// echo $sql;exit; 
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}

	//Created By Sunil
	function GetClinicianBySchool($schoolId)
	{
		$objDB = new clsDB();
		$sql = "SELECT  clinician.clinicianId,clinician.firstName,clinician.lastName,clinician.schoolId,
				rotation.hospitalSiteId,rotation.rotationId,
				clinicianhospitalsite.clinicianId,clinicianhospitalsite.hospitalSiteId,
				schools.schoolId
				FROM  clinician
				INNER JOIN schools ON clinician.schoolId=schools.schoolId
				LEFT JOIN clinicianhospitalsite ON clinician.clinicianId =clinicianhospitalsite.clinicianId
				LEFT JOIN rotation ON clinicianhospitalsite.hospitalSiteId = rotation.hospitalSiteId";
		$sql .= " WHERE schools.schoolId =" . $schoolId;
		$sql .= " group by clinician.clinicianId";
		//echo $sql; 
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}



	function GetClinicianForCheckoffByRotation($schoolId, $rotationId, $checkoffId = 0)
	{
		$objDB = new clsDB();
		$sql = "SELECT  clinician.clinicianId,clinician.firstName,clinician.lastName
				FROM  clinician
				INNER JOIN schools ON clinician.schoolId=schools.schoolId
				INNER JOIN checkoff ON clinician.clinicianId=checkoff.clinicianId";
		$sql .= " WHERE schools.schoolId =" . $schoolId;
		if ($rotationId > 0) {
			$sql .= " AND checkoff.rotationId =" . $rotationId;
		}
		if ($checkoffId > 0) {
			$sql .= " AND checkoff.checkoffId =" . $checkoffId;
		}
		$sql .= " group by clinician.clinicianId";
		// echo $sql; 
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}
	function GetClinicianByStudent($studentId)
	{
		$objDB = new clsDB();
		$sql = "SELECT clinician.clinicianId,clinician.firstName,clinician.lastName,rotation.hospitalSiteId,
				rotationdetails.studentId, clinicianhospitalsite.clinicianId,clinicianhospitalsite.hospitalSiteId, 
				student.studentId 
				FROM clinician 
				LEFT JOIN clinicianhospitalsite ON clinician.clinicianId =clinicianhospitalsite.clinicianId
				 LEFT JOIN rotation ON clinicianhospitalsite.hospitalSiteId = rotation.hospitalSiteId
				 LEFT JOIN  rotationdetails ON rotation.rotationId=rotationdetails.rotationId
				 LEFT JOIN student ON rotationdetails.studentId=student.studentId
				 WHERE rotationdetails.studentId =" . $studentId . " 
				group by clinician.clinicianId";

		//echo $sql;
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}

	function GetAllClinicianByCourse($SchoolId)
	{
		$objDB = new clsDB();
		$sql = "SELECT rotation.hospitalSiteId, rotation.courseId,
				hospitalsites.hospitalSiteId,hospitalsites.title AS Hospitalname,
				clinicianhospitalsite.hospitalSiteId,clinicianhospitalsite.clinicianId,
				clinician.clinicianId,clinician.firstName,clinician.lastName,
				courses.courseId,courses.title
				 FROM rotation 
				 INNER JOIN courses ON rotation.courseId = courses.courseId 
				 LEFT JOIN hospitalsites ON rotation.hospitalSiteId = hospitalsites.hospitalSiteId
				 LEFT JOIN clinicianhospitalsite ON hospitalsites.hospitalSiteId = clinicianhospitalsite.hospitalSiteId 
				 INNER  JOIN clinician ON clinicianhospitalsite.clinicianId = clinician.clinicianId 
				 WHERE   courses.schoolId=" . $SchoolId . "   
				 GROUP BY clinician.clinicianId ORDER BY clinician.firstName,clinician.lastName";
		//echo $sql;
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}

	function GetAllClinicianBySchool($SchoolId)
	{
		$objDB = new clsDB();
		$sql = "SELECT 
				hospitalsites.hospitalSiteId,hospitalsites.title AS Hospitalname,
				clinicianhospitalsite.hospitalSiteId,clinicianhospitalsite.clinicianId,
				clinician.clinicianId,clinician.firstName,clinician.lastName
			
				 FROM clinician 
				INNER  JOIN clinicianhospitalsite ON  clinician.clinicianId =clinicianhospitalsite.clinicianId 
				LEFT JOIN hospitalsites ON hospitalsites.hospitalSiteId = clinicianhospitalsite.hospitalSiteId 
				 WHERE   clinician.schoolId=" . $SchoolId . "   
				 GROUP BY clinician.clinicianId 
				 ORDER BY clinician.firstName,clinician.lastName";
		//echo $sql;
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}
	function GetClinicianByHospital($HospitalId)
	{
		$objDB = new clsDB();
		$sql = "SELECT clinician.* ,clinicianhospitalsite.*
				FROM clinician
				INNER JOIN clinicianhospitalsite ON clinician.clinicianId=
													clinicianhospitalsite.clinicianId
				WHERE clinicianhospitalsite.hospitalSiteId=" . $HospitalId;

		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}
	function DeleteSchoolClinician($schoolId)
	{
		$schoolDir = ROOT_PATH . "/upload/schools/" . $SchoolId . "/clinician";
		delete_directory($schoolDir);
		$objDB = new clsDB();
		$sql = "DELETE FROM clinician WHERE schoolId  = " . $schoolId;
		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}
	function GetIrrClinician($schoolId, $irrMasterId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT clinician.clinicianId,clinician.firstName,clinician.lastName 
				FROM clinician 
				INNER JOIN irrdetails ON clinician.clinicianId=irrdetails.clinicianId
		        WHERE clinician.schoolId=" . $schoolId;
		if ($irrMasterId)
			$sql .= " AND irrdetails.irrMasterId = " . $irrMasterId;

		$sql .= "  GROUP BY clinician.clinicianId ORDER BY firstName ";
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	function GetPersonnelClinician($schoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT clinician.clinicianId,clinician.	firstName,clinician.lastName,clinicianrolemaster.title
				FROM clinician 
				INNER JOIN clinicianrolemaster ON clinician.clinicianRoleId =  clinicianrolemaster.clinicianRoleId
		        WHERE clinician.schoolId=" . $schoolId . "
				 ORDER BY clinician.firstName";
		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllSchoolClinicianForPersonnelCoarc($schoolId, $surveyId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT clinician.clinicianId,clinician.firstName,clinician.lastName,clinicianrolemaster.clinicianRoleId,clinicianrolemaster.title as Role 
				,coarcsurveymaster.title as surveyTitle,coarcsurveydetails.coarcSurveyMasterId,coarcsurveymaster.isDelivery
				FROM clinician 
				INNER JOIN clinicianrolemaster ON clinicianrolemaster.clinicianRoleId = clinician.clinicianRoleId 
				INNER JOIN coarcsurveydetails ON clinician.clinicianId=coarcsurveydetails.clinicianId 
				INNER JOIN coarcsurveymaster ON coarcsurveydetails.coarcSurveyMasterId=coarcsurveymaster.coarcSurveyMasterId 
				INNER JOIN coarcsurveytype ON coarcsurveymaster.coarctype=coarcsurveytype.coarcSurveyTypeId 
				WHERE clinician.schoolId=" . $schoolId . " AND coarcsurveymaster.coarctype='2' ";

		if ($surveyId > 0) {
			$sql .= " AND coarcsurveydetails.coarcSurveyMasterId=" . $surveyId;
		}

		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllSchoolClinicianForPersonnelCoarcList($schoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT coarcsurveymaster.title as surveyTitle,coarcsurveydetails.coarcSurveyMasterId
				FROM clinician 
				INNER JOIN clinicianrolemaster ON clinicianrolemaster.clinicianRoleId = clinician.clinicianRoleId 
				INNER JOIN coarcsurveydetails ON clinician.clinicianId=coarcsurveydetails.clinicianId 
				INNER JOIN coarcsurveymaster ON coarcsurveydetails.coarcSurveyMasterId=coarcsurveymaster.coarcSurveyMasterId 
				INNER JOIN coarcsurveytype ON coarcsurveymaster.coarctype=coarcsurveytype.coarcSurveyTypeId 
				WHERE clinician.schoolId=" . $schoolId . " AND coarcsurveymaster.coarctype='2' ";
		$sql .= "Group By coarcsurveydetails.coarcSurveyMasterId order by coarcsurveymaster.title";
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function clinicianDocumentDetails($clinicianId, $Type)
	{
		$objDB = new clsDB();
		$sql = '';
		$sql = "select * from cliniciandocuments
		where clinicianId = " . $clinicianId;
		$sql .= " AND documetype = '" . $Type . "'";
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	function getClinicianFile($clinicianId, $fileTitle)
	{
		$sql = '';
		$objDB = new clsDB();
		$sql = "select * from cliniciandocuments where clinicianId = " . $clinicianId . " AND fileTitle ='" . $fileTitle . "'";
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}

	function saveClinicianDocument()
	{
		$objDB = new clsDB();
		$sql = '';
		$sql = "INSERT INTO cliniciandocuments (uploadedDate,clinicianId,clinicianIdImmunizationId,fileTitle,fileName,uploadedBy,documetype,recordid) 
				VALUES  ('" . (date("Y-m-d h:i:s")) . "',
					'" . ($this->clinicianId) . "',
					'" . ($this->clinicianIdImmunizationId) . "',
					'" . addslashes($this->fileTitle) . "',
					'" . addslashes($this->fileName) . "',
					'" . ($this->uploadedBy) . "',
					'" . addslashes($this->documetype) . "',
					'" . ($this->recordid) . "'
					)";

		$clinicianDocumentId =$objDB->ExecuteInsertQuery($sql);
		//echo $sql;exit;
		unset($objDB);
		return $clinicianDocumentId;
	}

	function clinicianSingleDocument($clinicianDocumentId)
	{
		$sql = '';
		$objDB = new clsDB();
		$sql = "select * from cliniciandocuments where clinicianDocumentId = " . $clinicianDocumentId;

		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}
	function DeleteClinicianDocuments($clinicianDocumentId)
	{
		$sql = '';
		$objDB = new clsDB();
		$sql = "delete from cliniciandocuments where clinicianDocumentId = " . $clinicianDocumentId;

		$row = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $row;
	}
	function GetRotationAssignedtoclinician($clinicianId)
	{
		$sql = '';
		$objDB = new clsDB();
		$sql = "SELECT  clinician.clinicianId,rotation.hospitalSiteId,rotation.rotationId
		FROM  clinician
		LEFT JOIN clinicianhospitalsite ON clinician.clinicianId =clinicianhospitalsite.clinicianId
		LEFT JOIN rotation ON clinicianhospitalsite.hospitalSiteId = rotation.hospitalSiteId
		WHERE  clinician.clinicianId = " . $clinicianId;
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}

	function GetClinicianDetailForSuperAdmin($searchForFilter, $schoolIds = '')
	{
		$sql = '';
		$objDB = new clsDB();
		$sql = " SELECT clinician.*,clinicianrolemaster.title,schools.title as schoolName,schools.slug FROM clinician 
		inner join clinicianrolemaster on clinicianrolemaster.clinicianRoleId = clinician.clinicianRoleId
		inner join schools on schools.schoolId = clinician.schoolId
		WHERE (clinician.firstName like '%$searchForFilter%' 
		OR clinician.lastName like '%$searchForFilter%' 
		OR clinician.email like '%$searchForFilter%' 
		OR clinician.username like '%$searchForFilter%' 
		OR CONCAT(clinician.firstName,' ',clinician.lastName) like '%$searchForFilter%' 
		OR clinician.phone like '%$searchForFilter%' )";

		if ($schoolIds != '')
			$sql .= " AND schools.schoolId IN ( $schoolIds ) ";

		// echo $sql;
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}

	function GetClinicianDetailToAll($searchForFilter)
	{
		$sql = '';
		$objDB = new clsDB();
		$sql = " SELECT clinician.*,clinicianrolemaster.title,schools.title as schoolName,schools.slug FROM clinician 
		inner join clinicianrolemaster on clinicianrolemaster.clinicianRoleId = clinician.clinicianRoleId
		inner join schools on schools.schoolId = clinician.schoolId
		WHERE clinician.email like '$searchForFilter'  and clinician.isActive !=0 ";

		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}

	function GetActiveClinician()
	{
		$row = '';
		$objDB = new clsDB();
		$sql = "SELECT CONCAT(clinician.firstName,' ',clinician.lastName) as Fullname ,createdDate,clinicianRoleId,email,phone,cellPhone,schoolId FROM `clinician` WHERE isActive=1 ORDER BY createdDate";
		//echo $sql;exit;
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}

	function GetClinicianForCheckoffByStandardSchoolRotation($schoolId, $rotationId, $clinicianId)
	{
		$objDB = new clsDB();
		$sql = "SELECT  clinician.clinicianId,clinician.firstName,clinician.lastName
				FROM  clinician
				INNER JOIN schools ON clinician.schoolId=schools.schoolId
				INNER JOIN checkoff ON clinician.clinicianId=checkoff.clinicianId";
		$sql .= " WHERE schools.schoolId =" . $schoolId;
		if ($rotationId > 0) {
			$sql .= " AND checkoff.rotationId =" . $rotationId;
		}

		if ($clinicianId > 0) {
			$sql .= " AND checkoff.clinicianId =" . $clinicianId;
		}

		$sql .= " group by clinician.clinicianId";
		//-echo $sql; 
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}

	function GetCliniciansMail($schoolIds)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT `email` FROM `clinician` ";
		if ($schoolIds > 0)
			$sql .= " WHERE `schoolId` IN ($schoolIds)";
		// ECHO $sql;EXIT;
		$rows = $objDB->GetResultset($sql);

		unset($objDB);
		//print_r($rows);exit;
		return $rows;
	}
	function GetClinicianNameById($clinicianId)
	{
		$objDB = new clsDB();
		$sql = "SELECT concat(firstName,' ',lastName) as clinicianName
				FROM  clinician WHERE clinician.clinicianId=" . $clinicianId;
		$row = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $row;
	}

	function CheckLoginDetail($SchoolCode, $UserName)
	{
		$row = '';
		$objDB = new clsDB();
		// SELECT *,schools.isActive as schoolIsActive
		$sql = "SELECT clinician.*,schools.isActive as schoolIsActive,schools.displayName,schools.isActiveCheckoffForStudent AS isActiveCheckoff,clinicianrolemaster.title,clinicianrolemaster.type
				FROM clinician 
				INNER JOIN schools ON clinician.schoolId=schools.schoolId
				INNER JOIN clinicianrolemaster ON clinician.clinicianRoleId=clinicianrolemaster.clinicianRoleId	
				WHERE username =" . "'$UserName'" . " AND schools.code=" . $SchoolCode;
		// echo $sql;exit;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}

	function GetAllSchoolCliniciansForAdmin($schoolId, $roleId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
				clinician.clinicianId,
				clinician.firstName,
				clinician.lastName,
				clinician.email,
				clinician.phone,
				clinician.cellPhone,
				clinician.username,							
				clinician.isActive,							
				clinician.isBlocked,							
				IFNULL(location.title,'-') as location, clinicianrolemaster.title as role FROM clinician 
			  LEFT JOIN location on clinician.locationId = location.locationId 
			  LEFT JOIN clinicianrolemaster on clinician.clinicianRoleId = clinicianrolemaster.clinicianRoleId 
			    WHERE clinician.schoolId=" . $schoolId;

		if ($roleId > 0) {
			$sql .= " AND clinician.clinicianRoleId =" . $roleId;
		}
		$sql .= " ORDER BY clinician.firstName ASC";

		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	function GetClinicianDetailForSuperAdminBySchoolIds($searchForFilter, $schoolIds = '')
	{
		$sql = '';
		$objDB = new clsDB();
		$sql = " SELECT clinician.*,clinicianrolemaster.title,schools.title as schoolName,schools.slug FROM clinician 
		inner join clinicianrolemaster on clinicianrolemaster.clinicianRoleId = clinician.clinicianRoleId
		inner join schools on schools.schoolId = clinician.schoolId
		WHERE (clinician.firstName like '%$searchForFilter%' 
		OR clinician.lastName like '%$searchForFilter%' 
		OR clinician.email like '%$searchForFilter%' 
		OR clinician.username like '%$searchForFilter%' 
		OR CONCAT(clinician.firstName,' ',clinician.lastName) like '%$searchForFilter%' 
		OR clinician.phone like '%$searchForFilter%' )";

		// if ($schoolIds != '')
		$sql .= " AND schools.schoolId IN ( $schoolIds ) ";

		// echo $sql;
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}
	function GetSingleClinician($schoolId, $clinicianId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT clinician.firstName ,clinicianrolemaster.title ,
				clinician.lastName,schools.schoolId,schools.displayName,
				clinician.email,clinician.phone
				FROM clinician
				INNER JOIN schools ON clinician.schoolId=schools.schoolId
				INNER JOIN clinicianrolemaster ON clinicianrolemaster.clinicianRoleId=clinician.clinicianRoleId
				WHERE clinician.clinicianId=" . $clinicianId;
		// echo '<hr>'.$sql.'<hr>';exit;
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}

	/**
	 * Retrieves Clinician details for a given Clinician ID and school ID.
	 *
	 * @param int $clinicianId The ID of the Clinician.
	 * @param int $schoolId The ID of the school. If not provided, all schools will be considered.
	 *
	 * @return array An array containing the log data, row data, and additional data.
	 */
	function GetAllClinicianDetailsForLogs($clinicianId, $schoolId = 0)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT clinician.*,CONCAT(clinician.firstName, ' ', clinician.lastName) AS userName ,clinicianrolemaster.title,schools.schoolId,schools.displayName as schoolName,
				clinician.email,clinician.phone
				FROM clinician
				INNER JOIN schools ON clinician.schoolId=schools.schoolId
				INNER JOIN clinicianrolemaster ON clinicianrolemaster.clinicianRoleId=clinician.clinicianRoleId
				WHERE clinician.clinicianId=" . $clinicianId;
		// echo '<hr>'.$sql.'<hr>';exit;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}


	/**
	 * Retrieves Clinician Document details for a given clinicianDocument ID and school ID.
	 *
	 * @param int $clinicianDocumentId The ID of the Clinician.
	 * @param int $Type The ID of the school. If not provided, all schools will be considered.
	 *
	 * @return array An array containing the log data, row data, and additional data.
	 */
	function GetAllClinicianDocumentDetailsForLogs($clinicianDocumentId, $Type = '')
	{

		$objDB = new clsDB();
		$sql = '';
		$sql = "SELECT cliniciandocuments.*,CONCAT(clinician.firstName, ' ', clinician.lastName) AS userName ,clinicianrolemaster.title,schools.schoolId,schools.displayName as schoolName,
				clinician.email,clinician.phone from cliniciandocuments
				INNER JOIN clinician ON clinician.clinicianId=cliniciandocuments.clinicianId
				INNER JOIN schools ON clinician.schoolId=schools.schoolId
				INNER JOIN clinicianrolemaster ON clinicianrolemaster.clinicianRoleId=clinician.clinicianRoleId
				WHERE cliniciandocuments.clinicianDocumentId = " . $clinicianDocumentId;
		if($Type){
			$sql .= " AND documetype = '" . $Type . "'";
		}
		// echo $sql;
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;

	}

	/**
	 * This function creates a log entry for a CI evaluation action.
	 *
	 * @param int $id The ID of the CI evaluation.
	 * @param string $action The action performed (Add, Edit, Delete, Signoff).
	 * @param int $userId The ID of the user performing the action.
	 * @param string $userType The type of user performing the action (Student, Preceptor, Admin).
	 *
	 * @return array An array containing the log data, row data, and additional data.
	 */
	function createClinicianLog($id, $action, $userId, $userType,$type,$backUserId)
	{
			// echo "type ".$type;exit;
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$objClinician = new clsClinician(); // Assuming `Attendance` class is used for `prepareAttendanceLogData`

		// Get user details based on user type
		$userDetails = getUserDetails($userId, $userType);

		if($type == 'Document'){

			$rowData = $objClinician->GetAllClinicianDocumentDetailsForLogs($id);
		}
		else{

			// For Login AS User Details
			if($backUserId > 0){
				include('clsSystemUser.php');
				$objSystemUser = new clsSystemUser();
				$backUserData = $objSystemUser->GetSchoolAdminDetailsForLogs($backUserId);
			}

				$rowData = $objClinician->GetAllClinicianDetailsForLogs($id);			
		}

		// echo "<pre>";
		// print_r($rowData);exit;

		if ($rowData != '')
			$logData = $objLog->generateLogData($rowData, $userType);

		$additionalData = '';
		// Populate log data with user details
		$logData['userId'] = $userDetails['userId'];
		$logData['userName'] = isset($backUserData) ? $backUserData['userName'] : $userDetails['userName'];
		$logMessage = '';

		if($type == 'Document'){
			if ($action == 'Add') {
				$logMessage = $logData['userName'] . ' uploaded Documents for ' . $rowData['userName'] . ' Clinician ';
			} else if ($action == 'Delete') {
				$logMessage = $logData['userName'] . ' deleted Documents of ' . $rowData['userName'] . ' Clinician .';
			} 

		} else {

			if ($action == 'Add') {
				$logMessage = $logData['userName'] . ' added the new Clinician';
			} else if ($action == 'Edit') {
				$msg = ($userType == 'Admin') ? $logData['userName'] . ' updated the ' . $rowData['userName'] . ' Clinician Profile details.' : $logData['userName'] . ' updated his Profile.';
				$logMessage = $msg;
			}  else if ($action == 'Delete') {
				$logMessage = $logData['userName'] . ' deleted the ' . $rowData['userName'] . ' Clinician.';
			} else if ($action == 'Active') {
				$logMessage = $logData['userName'] . ' activate the ' . $rowData['userName'] . ' Clinician.';
			} else if ($action == 'Inactive') {
				$logMessage = $logData['userName'] . ' deactivate the ' . $rowData['userName'] . ' Clinician.';
			}else if ($action == 'Lock') {
				$logMessage = $logData['userName'] . ' Locked the ' . $rowData['userName'] . ' Clinician.';
			} else if ($action == 'Unlocked') {
				$logMessage = $logData['userName'] . ' Unlocked the ' . $rowData['userName'] . ' Clinician.';
			} else if ($action == 'ChangePassword') {
				$logMessage =  $logData['userName'] .' Changed Password.';
			} else if ($action == 'LogIn') {
				$logMessage = $logData['userName'] . ' logged in.';
			} else if ($action == 'Login As') {
				$logMessage = $logData['userName'] . ' logged in as ' . $rowData['userName'] . ' Clinician.';
			} else if ($action == 'LogOut' && $backUserId > 0) {
				$logMessage = $backUserData['userName'] . ' logged out As ' . $rowData['userName'];
			} else if ($action == 'LogOut') {
				$logMessage =  $logData['userName'] .' Logged Out.';
			}



		}


		

		// Construct log message
		$logData['message'] = $logMessage;

		// Return the data
		return [$logData, $rowData, $additionalData];
	}

	/**
	 * Saves audit log for CI evaluation actions
	 *
	 * @param int $id ID of the CI evaluation master record
	 * @param int $userId User ID of the user performing the action
	 * @param string $userType Type of user (e.g. student, clinician, etc.)
	 * @param string $action Action type (Add, Edit, Delete, Signoff)
	 * @param int $isMobile Indicates if the action was performed from a mobile device (0 = false, 1 = true)
	 *
	 * @return bool Returns true if successful
	 */
	function saveClinicianAuditLog($id, $userId, $userType, $action, $isMobile = 0,$SchoolId =0,$type ='',$backUserId = 0)
	{
		// echo "type ".$type;exit;
		// Instantiate the Logger and Checkoff classes
		$objLog = new clsLogger();
			$objClinician = new clsClinician();
	

		// Prepare log data
		[$logData, $rowData, $additionalData] = $objClinician->createClinicianLog($id, $action, $userId, $userType,$type,$backUserId);
		$logData['isMobile'] = $isMobile;

		if ($action == 'Delete') {

			$additionalData = '';
			if($type == 'Document')
				$objClinician->DeleteClinicianDocuments($id);	
			else
				$objClinician->DeleteClinician($id,$SchoolId);
			
		}
		// Save the log details
		$objLog->saveLogs($logData, $action, $id, 'clinician '.$type, $rowData, $additionalData);

		// Clean up
		unset($objLog);
		unset($objCIevaluation);

		return true; // Return success or handle further actions as needed
	}
}
