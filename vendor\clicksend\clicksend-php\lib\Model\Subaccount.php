<?php
/**
 * Subaccount
 *
 * PHP version 5
 *
 * @category Class
 * @package  ClickSend
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * ClickSend v3 API
 *
 * This is an official SDK for [ClickSend](https://clicksend.com)  Below you will find a current list of the available methods for clicksend.  *NOTE: You will need to create a free account to use the API. You can register [here](https://dashboard.clicksend.com/#/signup/step1/)..*
 *
 * OpenAPI spec version: 3.1
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * ClickSend Codegen version: 3.0.51
 */
/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace ClickSend\Model;

use \ArrayAccess;
use \ClickSend\ObjectSerializer;

/**
 * Subaccount Class Doc Comment
 *
 * @category Class
 * @description Accounts that are maintained under a main account
 * @package  ClickSend
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */
class Subaccount implements ModelInterface, ArrayAccess
{
    const DISCRIMINATOR = 'class_type';

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $swaggerModelName = 'Subaccount';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerTypes = [
        'api_username' => 'string',
        'password' => 'string',
        'email' => 'string',
        'phone_number' => 'string',
        'first_name' => 'string',
        'last_name' => 'string',
        'access_users' => 'int',
        'access_billing' => 'int',
        'access_reporting' => 'int',
        'access_contacts' => 'int',
        'access_settings' => 'int'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerFormats = [
        'api_username' => null,
        'password' => null,
        'email' => null,
        'phone_number' => null,
        'first_name' => null,
        'last_name' => null,
        'access_users' => 'int32',
        'access_billing' => 'int32',
        'access_reporting' => 'int32',
        'access_contacts' => 'int32',
        'access_settings' => 'int32'
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerTypes()
    {
        return self::$swaggerTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerFormats()
    {
        return self::$swaggerFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'api_username' => 'api_username',
        'password' => 'password',
        'email' => 'email',
        'phone_number' => 'phone_number',
        'first_name' => 'first_name',
        'last_name' => 'last_name',
        'access_users' => 'access_users',
        'access_billing' => 'access_billing',
        'access_reporting' => 'access_reporting',
        'access_contacts' => 'access_contacts',
        'access_settings' => 'access_settings'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'api_username' => 'setApiUsername',
        'password' => 'setPassword',
        'email' => 'setEmail',
        'phone_number' => 'setPhoneNumber',
        'first_name' => 'setFirstName',
        'last_name' => 'setLastName',
        'access_users' => 'setAccessUsers',
        'access_billing' => 'setAccessBilling',
        'access_reporting' => 'setAccessReporting',
        'access_contacts' => 'setAccessContacts',
        'access_settings' => 'setAccessSettings'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'api_username' => 'getApiUsername',
        'password' => 'getPassword',
        'email' => 'getEmail',
        'phone_number' => 'getPhoneNumber',
        'first_name' => 'getFirstName',
        'last_name' => 'getLastName',
        'access_users' => 'getAccessUsers',
        'access_billing' => 'getAccessBilling',
        'access_reporting' => 'getAccessReporting',
        'access_contacts' => 'getAccessContacts',
        'access_settings' => 'getAccessSettings'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$swaggerModelName;
    }



    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['api_username'] = isset($data['api_username']) ? $data['api_username'] : null;
        $this->container['password'] = isset($data['password']) ? $data['password'] : null;
        $this->container['email'] = isset($data['email']) ? $data['email'] : null;
        $this->container['phone_number'] = isset($data['phone_number']) ? $data['phone_number'] : null;
        $this->container['first_name'] = isset($data['first_name']) ? $data['first_name'] : null;
        $this->container['last_name'] = isset($data['last_name']) ? $data['last_name'] : null;
        $this->container['access_users'] = isset($data['access_users']) ? $data['access_users'] : 1;
        $this->container['access_billing'] = isset($data['access_billing']) ? $data['access_billing'] : 1;
        $this->container['access_reporting'] = isset($data['access_reporting']) ? $data['access_reporting'] : 1;
        $this->container['access_contacts'] = isset($data['access_contacts']) ? $data['access_contacts'] : 0;
        $this->container['access_settings'] = isset($data['access_settings']) ? $data['access_settings'] : 1;

        // Initialize discriminator property with the model name.
        $discriminator = array_search('class_type', self::$attributeMap, true);
        $this->container[$discriminator] = static::$swaggerModelName;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if ($this->container['api_username'] === null) {
            $invalidProperties[] = "'api_username' can't be null";
        }
        if ($this->container['password'] === null) {
            $invalidProperties[] = "'password' can't be null";
        }
        if ($this->container['email'] === null) {
            $invalidProperties[] = "'email' can't be null";
        }
        if ($this->container['phone_number'] === null) {
            $invalidProperties[] = "'phone_number' can't be null";
        }
        if ($this->container['first_name'] === null) {
            $invalidProperties[] = "'first_name' can't be null";
        }
        if ($this->container['last_name'] === null) {
            $invalidProperties[] = "'last_name' can't be null";
        }
        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets api_username
     *
     * @return string
     */
    public function getApiUsername()
    {
        return $this->container['api_username'];
    }

    /**
     * Sets api_username
     *
     * @param string $api_username Your new api username.
     *
     * @return $this
     */
    public function setApiUsername($api_username)
    {
        $this->container['api_username'] = $api_username;

        return $this;
    }

    /**
     * Gets password
     *
     * @return string
     */
    public function getPassword()
    {
        return $this->container['password'];
    }

    /**
     * Sets password
     *
     * @param string $password Your new password
     *
     * @return $this
     */
    public function setPassword($password)
    {
        $this->container['password'] = $password;

        return $this;
    }

    /**
     * Gets email
     *
     * @return string
     */
    public function getEmail()
    {
        return $this->container['email'];
    }

    /**
     * Sets email
     *
     * @param string $email Your new email.
     *
     * @return $this
     */
    public function setEmail($email)
    {
        $this->container['email'] = $email;

        return $this;
    }

    /**
     * Gets phone_number
     *
     * @return string
     */
    public function getPhoneNumber()
    {
        return $this->container['phone_number'];
    }

    /**
     * Sets phone_number
     *
     * @param string $phone_number Your phone number in E.164 format.
     *
     * @return $this
     */
    public function setPhoneNumber($phone_number)
    {
        $this->container['phone_number'] = $phone_number;

        return $this;
    }

    /**
     * Gets first_name
     *
     * @return string
     */
    public function getFirstName()
    {
        return $this->container['first_name'];
    }

    /**
     * Sets first_name
     *
     * @param string $first_name Your firstname
     *
     * @return $this
     */
    public function setFirstName($first_name)
    {
        $this->container['first_name'] = $first_name;

        return $this;
    }

    /**
     * Gets last_name
     *
     * @return string
     */
    public function getLastName()
    {
        return $this->container['last_name'];
    }

    /**
     * Sets last_name
     *
     * @param string $last_name Your lastname
     *
     * @return $this
     */
    public function setLastName($last_name)
    {
        $this->container['last_name'] = $last_name;

        return $this;
    }

    /**
     * Gets access_users
     *
     * @return int
     */
    public function getAccessUsers()
    {
        return $this->container['access_users'];
    }

    /**
     * Sets access_users
     *
     * @param int $access_users Your access users flag value, must be 1 or 0.
     *
     * @return $this
     */
    public function setAccessUsers($access_users)
    {
        $this->container['access_users'] = $access_users;

        return $this;
    }

    /**
     * Gets access_billing
     *
     * @return int
     */
    public function getAccessBilling()
    {
        return $this->container['access_billing'];
    }

    /**
     * Sets access_billing
     *
     * @param int $access_billing Your access billing flag value, must be 1 or 0.
     *
     * @return $this
     */
    public function setAccessBilling($access_billing)
    {
        $this->container['access_billing'] = $access_billing;

        return $this;
    }

    /**
     * Gets access_reporting
     *
     * @return int
     */
    public function getAccessReporting()
    {
        return $this->container['access_reporting'];
    }

    /**
     * Sets access_reporting
     *
     * @param int $access_reporting Your access reporting flag value, must be 1 or 0.
     *
     * @return $this
     */
    public function setAccessReporting($access_reporting)
    {
        $this->container['access_reporting'] = $access_reporting;

        return $this;
    }

    /**
     * Gets access_contacts
     *
     * @return int
     */
    public function getAccessContacts()
    {
        return $this->container['access_contacts'];
    }

    /**
     * Sets access_contacts
     *
     * @param int $access_contacts Your access contacts flag value, must be 1 or 0.
     *
     * @return $this
     */
    public function setAccessContacts($access_contacts)
    {
        $this->container['access_contacts'] = $access_contacts;

        return $this;
    }

    /**
     * Gets access_settings
     *
     * @return int
     */
    public function getAccessSettings()
    {
        return $this->container['access_settings'];
    }

    /**
     * Sets access_settings
     *
     * @param int $access_settings Your access settings flag value, must be 1 or 0.
     *
     * @return $this
     */
    public function setAccessSettings($access_settings)
    {
        $this->container['access_settings'] = $access_settings;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    #[\ReturnTypeWillChange]
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return isset($this->container[$offset]) ? $this->container[$offset] : null;
    }

    /**
     * Sets value based on offset.
     *
     * @param integer $offset Offset
     * @param mixed   $value  Value to be set
     *
     * @return void
     */
    #[\ReturnTypeWillChange]
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    #[\ReturnTypeWillChange]
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        if (defined('JSON_PRETTY_PRINT')) { // use JSON pretty print
            return json_encode(
                ObjectSerializer::sanitizeForSerialization($this),
                JSON_PRETTY_PRINT
            );
        }

        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}
