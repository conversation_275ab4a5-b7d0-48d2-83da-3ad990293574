<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsRotation.php');
include('../class/clsLocations.php');
include('../class/clsStudent.php');
include('../class/clsFormative.php');
include('../setRequest.php');
include('../class/clsQuestionOption.php');
include('../class/clsSectionStudentName.php');
include('../class/clsClinician.php');
include('../class/clsFloorTherapyAndICUEvaluation.php');
include('../class/clsExternalPreceptors.php');
include('../class/clsSystemUser.php');

$isEvalType = isset($_GET['isEvalType']) ? DecodeQueryData($_GET['isEvalType']) : '';

$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];
// print_r($_SESSION);
$schoolId = 0;
$rotationId = 0;
$studentId = 0;
$currentstudentId = 0;
$studentMasterId = 0;
$clinicianId = 0;
$rotationId = 0;
$dateOfStudentSignature = '';
$evaluationDate = '';
$display_to_date = date('m/d/Y');
$schoolId = $currentSchoolId;
$DBRotationId = 0;
$objRotation = new clsRotation();
$objEvaluation = new clsFloorTherapyAndICUEvaluation();

//For Student	
$currentstudentId = isset($_GET['studentId']) ? DecodeQueryData($_GET['studentId']) : 0;
$rotationId = isset($_GET['rotationId']) ? DecodeQueryData($_GET['rotationId']) : 0;
if($isEvalType == 1){
    $activebtn = 'icu';
}else{
    $activebtn = 'floor';
}
$DBRotationId = isset($_GET['rotationId']) ? DecodeQueryData($_GET['rotationId']) : 0;
//For Edit Formative
if (isset($_GET['studentMasterId']) && ($_GET['rotationId'])) {

    $schoolId = $currentSchoolId;

    if ($isEvalType == 1)
        $page_title = "Edit ICU Evaluation ";
    else
        $page_title = "Edit Floor Therapy ";

    $bedCrumTitle = 'View';

    //For Formative Details
    $objEvaluation = new clsFloorTherapyAndICUEvaluation();
    $studentMasterId = DecodeQueryData($_GET['studentMasterId']);
    $rowFormative = $objEvaluation->GetStudentPEFDetails($studentMasterId);

    unset($objEvaluation);
    // if($rowFormative=='')
    // {
    //     header('location:floorTherapyAndICUEvaluationlist.html');
    //     exit;
    // }
    $studentComment = stripslashes($rowFormative['studentComment']);
    $studentComment = strip_tags($rowFormative['studentComment']);
    $DBRotationId = ($rowFormative['rotationId']);
    $clinicianId = ($rowFormative['clinicianId']);
    $evaluationDate = ($rowFormative['evaluationDate']);
    $courselocationId = $rowFormative['locationId'];
    $parentRotationId = stripslashes($rowFormative['parentRotationId']);
    $rotationLocationId = stripslashes($rowFormative['rotationLocationId']);
    $aprovedBy = stripslashes($rowFormative['aprovedBy']);
    $rotationGrade = ($rowFormative['rotationGrade']);

    $aprovedloggedUserRoleId = isset($_SESSION['loggedUserRoleId'])  ? $_SESSION['loggedUserRoleId'] : 0;
    $objDB = new clsDB();
    $isdefaultFloorAndIcuEval = $objDB->GetSingleColumnValueFromTable('schools', 'isdefaultFloorAndIcuEval', 'schoolId', $currentSchoolId);
    unset($objDB);
    $isAprovalId = ($aprovedloggedUserRoleId == 536 || $aprovedloggedUserRoleId == 537 || $aprovedloggedUserRoleId == 245 || $aprovedloggedUserRoleId == 246 || $aprovedloggedUserRoleId == 249 || $aprovedloggedUserRoleId == 540) ? $aprovedloggedUserRoleId : 0;

    if ($aprovedBy == 0 && $isAprovalId)
        $aprovedBy = $_SESSION['loggedUserId'];
    // echo 'aprovedBy'.$aprovedBy;    
    $aprovedDate = ($rowFormative['aprovedDate']);
    $isPreceptorCompletedStatus = $rowFormative['isPreceptorCompletedStatus'];
    $bedCrumTitle = ($isPreceptorCompletedStatus == 1 && $aprovedDate == '')  ? 'Edit' : 'View';
    $aprovedDisabled = ($aprovedBy && $aprovedDate != '')  ? 'disabled' : '';

    $locationId = 0;
    if ($rotationLocationId != $courselocationId && $parentRotationId > 0) {
        if ($parentRotationId > 0) {
            if (!$rotationLocationId)
                $locationId = $objRotation->GetLocationByRotation($DBRotationId);
            else
                $locationId  = $rotationLocationId;
        }
    } else {
        $locationId  = $courselocationId;
    }

    //Get Time Zone By Rotation 
    $objLocation = new clsLocations();
    $TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
    unset($objLocation);
    if ($TimeZone == '')
        $TimeZone = $_SESSION["loggedUserSchoolTimeZone"];
    $evaluationDate = converFromServerTimeZone($evaluationDate, $TimeZone);
    $evaluationDate = (date('m/d/Y', strtotime($evaluationDate)));
    $studentId = ($rowFormative['studentId']);
    $studentSignature = isset($rowFormative['studentsigniture']) ? $rowFormative['studentsigniture'] : '';
    // echo $studentSignature;exit;
    $dateOfStudentSignature = ($rowFormative['dateOfStudentSignature']);
    if ($dateOfStudentSignature != '' && $dateOfStudentSignature != '0000-00-00 00:00:00') {
        $dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $TimeZone);
        $dateOfStudentSignature = (date('m/d/Y', strtotime($dateOfStudentSignature)));
    } else
        $dateOfStudentSignature = '';

    if ($aprovedDate != '' && $aprovedDate != '0000-00-00 00:00:00') {
        $aprovedDate = converFromServerTimeZone($aprovedDate, $TimeZone);
        $aprovedDate = (date('m/d/Y', strtotime($aprovedDate)));
    }
    else{
        $aprovedDate = '';
    }

    $preceptorId = ($rowFormative['preceptorId']);
    $preceptorFullName = '';
    if ($preceptorId > 0) {
        $objExternalPreceptors = new clsExternalPreceptors();
        $externalPreceptorDetail = $objExternalPreceptors->GetExternalPreceptorDetail($preceptorId);
        $preceptorFirstName = isset($externalPreceptorDetail['firstName']) ? $externalPreceptorDetail['firstName'] : '';
        $preceptorLastName = isset($externalPreceptorDetail['lastName']) ? $externalPreceptorDetail['lastName'] : '';
        $preceptorFullName = $preceptorFirstName . ' ' . $preceptorLastName;
    }

    //For Rotation List
    $rowstudentrotation = $objRotation->GetRotationByStudent($schoolId, $currentstudentId);
} else {
    if ($isEvalType == 1)
        $page_title = "Add PEF II";
    else
        $page_title = "Add PEF I";

    $bedCrumTitle = 'Add';
    //For Rotation List
    $rowstudentrotation = $objRotation->GetCurrentRotationByStudent($schoolId, $currentstudentId);
}


//For Clinician Name
$objClinician = new clsClinician();
$Clinician = $objClinician->GetClinicianByRotation($currentSchoolId, $rotationId);
unset($objClinician);

//For Student Name
$objStudent = new clsStudent();
$Student = $objStudent->GetStudentsByRotation($currentSchoolId, $rotationId);
unset($objStudent);

$objSectionStudentName = new clsSectionStudentName();
$getStudentName = $objSectionStudentName->GetSectionStudentName($schoolId, $rotationId);
unset($objSectionStudentName);


$totalSection = 0;
$objEvaluation = new clsFloorTherapyAndICUEvaluation();

if ($activebtn == 'floor')
    $evalSection = $objEvaluation->GetSections('floor');
elseif ($activebtn == 'icu')
    $evalSection = $objEvaluation->GetSections('icu');

$totalSection = ($evalSection != '') ? mysqli_num_rows($evalSection) : 0;


//For Rotation Name
$RotationName = $objRotation->GetrotationDetails($rotationId, $schoolId);
$rotationtitle = isset($RotationName['title']) ? $RotationName['title'] : '';
unset($objRotation);

//For Student Full Name
$objStudent = new clsStudent();
$Rowstudent = $objStudent->GetSingleStudent($schoolId, $currentstudentId);
$studentfullname = ($Rowstudent) ? $Rowstudent['firstName'] . ' ' . $Rowstudent['lastName'] : '';
unset($objStudent);

if ($isEvalType == 1)
    $breadCrubTitle = 'ICU / ABG Rotation Evaluation';
else
    $breadCrubTitle = 'Floor Therapy Evaluation';

//For School System Users
$totalSchoolUser = 0;
$objSystemUsers = new clsSystemUser();
$rowsSchoolUsers = $objSystemUsers->GetSchoolSystemUsers($schoolId);

if ($rowsSchoolUsers != '') {
    $totalSchoolUser = mysqli_num_rows($rowsSchoolUsers);
}

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($page_title); ?></title>
    <?php include('includes/headercss.php'); ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />

    <style type="text/css">
        .some-class {
            float: left;
            clear: none;
        }

        .form-group {
            margin-bottom: 10px !important;
        }

        .form-control {
            height: 45px;
        }

        /* Style for the collapsible content */
        .panel-collapse {
            display: none;
            /* Hidden by default */
            /* padding: 10px;
            border: 1px solid #ddd;
            background-color: #f9f9f9; */
        }

        /* Style for the collapsible button */
        .collapsible {
            /* background-color: #3498db;
            color: white;
            padding: 10px;
            border: none; */
            width: 100%;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            /* Align content horizontally */
        }

        .panel-heading {
            width: 100%;
        }

        /* Style for the arrow icons */
        .arrow-icon {
            transition: transform 0.3s;
        }

        .collapsible.expanded .arrow-icon i {
            transform: rotate(180deg);
        }
    </style>

</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <?php if ($currentstudentId > 0) { ?>
                        <li><a href="clinical.html">Clinical</a></li>
                        <li><a href="clinical.html"><?php echo ($studentfullname); ?></a></li>
                        <li><a href="floorTherapyAndICUEvaluationlist.html?studentId=<?php echo EncodeQueryData($currentstudentId); ?>"><?php echo $breadCrubTitle; ?></a></li>
                    <?php } else { ?>
                        <li><a href="rotations.html">Rotations</a></li>
                        <li><a href="rotations.html"><?php echo ($rotationtitle); ?></a></li>
                        <li><a href="floorTherapyAndICUEvaluationlist.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>"><?php echo $breadCrubTitle; ?></a></li>
                    <?php } ?>
                    <li class="active"><?php echo ($bedCrumTitle); ?></li>
                </ol>
            </div>

        </div>
    </div>

    <div class="container">

        <form id="frmformative" data-parsley-validate class="form-horizontal" method="POST" action="addFloorTherapyAndICUEvaluationSubmit.html?studentMasterId=<?php echo (EncodeQueryData($studentMasterId)); ?>
		 &rotationId=<?php echo (EncodeQueryData($rotationId)); ?>&type=1">
            <input type="hidden" name="isEvalType" id="" value="<?php echo $isEvalType; ?>">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="evaluationDate">Evaluation Date</label>
                        <div class="col-md-12">
                            <div class='input-group date w-full' id='evaluationDate' style="position: relative;">

                                <input type='text' name="evaluationDate" id="evaluationDate" class="form-control input-md required-input rotation_date" value="<?php echo ($evaluationDate); ?>" disabled data-parsley-errors-container="#error-evaluationDate" placeholder="MM-DD-YYYY" />
                                <span class="input-group-addon calender-icon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                            <div id="error-evaluationDate"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <?php if ($preceptorId) { ?>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="cboclinician">Preceptor</label>
                            <div class="col-md-12">
                                <input type="text" name="" id="" value="<?php echo $preceptorFullName; ?>" class="form-control" disabled>
                            </div>
                        </div>
                    </div>
                <?php } else { ?>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="cboclinician">Clinician</label>
                            <div class="col-md-12">
                                <select id="cboclinician" name="cboclinician" class="form-control input-md required-input select2_single" disabled required data-parsley-errors-container="#error-cboclinician">
                                    <option value="" selected>Select</option>
                                    <?php
                                    if ($Clinician != "") {
                                        while ($row = mysqli_fetch_assoc($Clinician)) {
                                            $selClinicianId  = $row['clinicianId'];
                                            $name  = stripslashes($row['firstName']);
                                            $lastName  = stripslashes($row['lastName']);
                                            $fullName = $name . ' ' . $lastName;
                                    ?>
                                            <option value="<?php echo ($selClinicianId); ?>" <?php if ($clinicianId == $selClinicianId) { ?> selected="true" <?php } ?>><?php echo ($fullName); ?></option>

                                    <?php

                                        }
                                    }
                                    ?>
                                </select>
                                <div id="error-cboclinician"></div>
                            </div>
                        </div>
                    </div>
                <?php } ?>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="cborotation">Rotation</label>
                        <div class="col-md-12">
                            <select id="cborotation" name="cborotation" class="form-control input-md required-input select2_single" disabled required data-parsley-errors-container="#error-cborotation">
                                <option value="" selected>Select</option>
                                <?php
                                if ($rowstudentrotation != "") {
                                    while ($row = mysqli_fetch_assoc($rowstudentrotation)) {
                                        $selrotationId  = $row['rotationId'];
                                        $title  = stripslashes($row['title']);
                                ?>
                                        <option value="<?php echo ($selrotationId); ?>" <?php if ($DBRotationId == $selrotationId) { ?> selected="true" <?php } ?>><?php echo ($title); ?></option>
                                <?php }
                                }
                                ?>
                            </select>
                            <div id="error-cborotation"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="cbostudent">Student</label>
                        <div class="col-md-12">
                            <select id="cbostudent" name="cbostudent" class="form-control input-md required-input select2_single" disabled required data-parsley-errors-container="#error-cbostudent">
                                <option value="" selected>Select</option>
                                <?php
                                if ($Student != "") {
                                    while ($row = mysqli_fetch_assoc($Student)) {
                                        $selstudentId  = $row['studentId'];
                                        $firstName  = stripslashes($row['firstName']);
                                        $lastName  = stripslashes($row['lastName']);
                                        $name =    $firstName . ' ' . $lastName;
                                        if ($currentstudentId > 0) { ?>
                                            <option value="<?php echo ($selstudentId); ?>" <?php if ($currentstudentId == $selstudentId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                                        <?php } else { ?>
                                            <option value="<?php echo ($selstudentId); ?>" <?php if ($studentId == $selstudentId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                                        <?php }  ?>
                                <?php

                                    }
                                }
                                ?>
                            </select>
                            <div id="error-cbostudent"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="studentsignitureDate">Date of Student Signature</label>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <div class='input-group date w-full' id='studentsignitureDate' style="position: relative;">

                                <input type='text' name="studentsignitureDate" readonly id="studentsignitureDate" class="form-control input-md required-input rotation_date" value="<?php echo ($dateOfStudentSignature);  ?>" data-parsley-errors-container="#error-studentsignitureDate" placeholder="MM-DD-YYYY" />
                                <span class="input-group-addon calender-icon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                            <div id="error-studentsignitureDate"></div>
                        </div>
                    </div>
                </div>
            </div>
            <?php if ($preceptorId && $isAprovalId && $isdefaultFloorAndIcuEval) { ?>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="aprovalId">Aproval</label>
                            <div class="col-md-12">
                                <input type="hidden" name="myselect" value="<?php echo ($aprovedBy); ?>" />
                                <select id="aprovalId" name="aprovalId" class="form-control input-md required-input select2_single" disabled <?php //echo $aprovedDisabled;
                                                                                                                                                ?> required data-parsley-errors-container="#error-aprovalId">
                                    <option value="" selected>Select</option>
                                    <?php
                                    if ($totalSchoolUser != "") {
                                        while ($row = mysqli_fetch_assoc($rowsSchoolUsers)) {
                                            // print_r($row);
                                            $systemUserMasterId  = $row['systemUserMasterId'];
                                            $systemUserfirstName  = stripslashes($row['firstName']);
                                            $systemUserlastName  = stripslashes($row['lastName']);
                                            $systemUsername =    $systemUserfirstName . ' ' . $systemUserlastName;
                                    ?>
                                            <option value="<?php echo ($systemUserMasterId); ?>" <?php if ($aprovedBy == $systemUserMasterId) { ?> selected="true" <?php } ?>><?php echo ($systemUsername); ?></option>

                                    <?php

                                        }
                                    }
                                    ?>
                                </select>
                                <div id="error-aprovalId"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-4 control-label" for="aprovalDate">Date of Aproval Signature</label>
                            <div class="col-md-8 col-sm-4 col-xs-12">
                                <div class='input-group date w-full' id='aprovalDate' style="position: relative;">

                                    <input type='text' name="aprovalDate" id="aprovalDate" required class="form-control input-md required-input rotation_date" value="<?php echo ($aprovedDate);  ?>" <?php echo $aprovedDisabled; ?> data-parsley-errors-container="#error-aprovalDate" placeholder="MM-DD-YYYY" />
                                    <span class="input-group-addon calender-icon">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>
                                </div>
                                <div id="error-aprovalDate"></div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php } ?>
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="studentcomment">Student Comments</label>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <textarea name="studentcomment" id="studentcomment" disabled class="form-control input-md" rows="4" cols="100"><?php if (isset($_GET['studentMasterId']))  echo ($studentComment); ?></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <!-- <label class="col-md-12 control-label" for="instructions:"></label> -->
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <div class="panel panel-default border-14">
                                <div class="panel-body">
                                    <ul class="list-unstyled text-center">
                                        <li>This evaluation is based upon a <b>SATISFACTORY / NEEDS IMPROVEMENT / UNSATISFACTORY </b> scale</li>
                                        <li>
                                            <h4 class="margin_top_five margin_bottom_five"><b>For each objective below, please mark “S” “NI” or “U”.</b></h4>
                                        </li>
                                        <li>If “NI” or “U” is marked, please give an explanation in the comment section.</li>
                                        <?php if ($isEvalType == 1) { ?>
                                            <li>If the objective was not covered during this rotation, please input “N/A”</li>
                                        <?php } ?>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <!-- <label class="col-md-2 control-label" for="instructions:"></label> -->
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <div class="panel-group" id="posts">
                                <?php

                                if ($evalSection) {
                                    while ($row = mysqli_fetch_array($evalSection)) {
                                        $sectionMasterId = $row['sectionMasterId'];
                                        $sectionDescription = isset($row['description']) ? $row['description'] : '';
                                        $title = $row['title'];
                                        //$firstName = $row['firstName'];
                                ?>

                                        <div class="panel panel-default">
                                            <a class="collapsible" style="color: #000; text-decoration: none;" href="#<?php echo $sectionMasterId; ?>" data-toggle="collapse" data-parent="#posts" id="collapse-link">
                                                <div class="panel-heading" style="display: flex; justify-content: space-between; align-items: center;">
                                                    <h4 class="panel-title">
                                                        <?php echo  $title;
                                                        if ($sectionDescription) {
                                                            echo $sectionDescription;
                                                        } ?>
                                                    </h4>
                                                    <span class="arrow-icon"> <i class="fa fa-angle-down" aria-hidden="true" id="collapse-icon" style="font-size: 19px;"></i></span>
                                                </div>
                                            </a>

                                            <div id="<?php echo $sectionMasterId; ?>" class="panel-collapse collapse">
                                                <?php
                                                // for question
                                                $pefquestion = $objEvaluation->GetAllQuestionMaster($sectionMasterId);
                                                $totalPEF = ($pefquestion != '') ? mysqli_num_rows($pefquestion) : 0;

                                                if ($totalPEF > 0) {
                                                    while ($row = mysqli_fetch_array($pefquestion)) {
                                                        if (isset($_GET['studentMasterId']))
                                                            $studentMasterId = DecodeQueryData($_GET['studentMasterId']);
                                                        else
                                                            $studentMasterId = 0;

                                                        $questionId = $row['questionId'];
                                                        $schoolPEFQuestionTitle = $row['optionText'];
                                                        $questionType = $row['questionType'];
                                                        $description = $row['description'];
                                                        $qhtml = GetFloorTherapyAndICUEvaluationQuestionHtml($questionId, $questionType, $studentMasterId, $currentSchoolId);

                                                ?>
                                                        <div class="panel-body isAllRadioButton">
                                                            <b class="questionDiv"><?php echo ($schoolPEFQuestionTitle); ?> </b><br /><br />
                                                            <?php echo $qhtml; ?>
                                                            <?php if ($description != '') { ?>
                                                                <br>
                                                                <div class="text-left margin_top_five margin_bottom_five" style="left: 5%;position: absolute;"><?php echo $description; ?></div>
                                                                <br><br>
                                                            <?php } ?>
                                                        </div>
                                                <?php
                                                    }
                                                }
                                                ?>
                                            </div>
                                        </div>
                                <?php
                                    }
                                }

                                ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <?php //if($isEvalType == 1) { 
            ?>
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <!-- <label class="col-md-2 control-label" for="instructions:"></label> -->
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <div style="border-collapse: collapse;border-color: #ddd;border: 2px solid black;padding: 7px;" class="border border-primary border-14">
                                <div class=" padding_top_five padding_bottom_five border-14" style="border: 1px solid black;">

                                    <div class="text-center">
                                        <p><b><u> SATISFACTORY’ / ‘NEEDS IMPROVEMENT’ / ‘UNSATISFACTORY’ grading scale.</u></b></p>
                                    </div>
                                    <p class="margin_left_ten"><b>All “S” results in a grade of 100% for rotation. Each “NI” results in 10 points off the rotation’s overall grade/ Each “U” results in 25 points off the rotation’s overall grade. </b></p>
                                    <p class="margin_left_ten">Receipt of an “NI” or “U” for any objective will result in a mandatory counseling session with the DCE and/or Program Director. Repeated “NI” ratings will be reviewed by the DCE and/or Program Director. At their discretion, the grade may be changed to a “U” designation.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- <div class="row">
                
                <label class="col-md-2 control-label"></label>
                <div class="col-md-10 padding_left_zero">
                    <div class="form-group">
                        <label class="col-md-2 control-label">Rotation Grade: </label>
                        <div class="col-md-2 padding_zero">
                            <input type="text" name="rotationGrade" id="rotationGrade" class="form-control" readonly> 
                        </div>
                    </div>
                    
                </div>
            </div> -->

            <div class="row">
                <div class="col-md-12 mb-20 mt-5">
                    <div class="grid-layout">
                        <div class="item card-body">
                            <div style="display: flex; align-items: center;">
                                <div class="card-image-section" style="background: #EEF5FF;">
                                    <img src="<?php echo ($dynamicOrgUrl); ?>/student/assets/images/award.png" alt="award">
                                </div>
                                <p class="card-title">
                                    Rotation Grade
                                </p>
                            </div>
                            <div>
                                <p class="card-count" style="font-size: 18px; font-family: 600;">
                                    <!-- <span class="card-count-span">3.5</span> of -->
                                    <?php echo $rotationGrade; ?>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php //} 
            ?>
            <div class="row">
                <div class="form-group m-0">
                    <!-- <label class="col-md-2 control-label"></label> -->
                    <div class="col-md-12" style="display: flex; justify-content: center; margin: 0 0 20px 0;gap: 15px;">
                        <?php if ($aprovedDate == '' && $isPreceptorCompletedStatus && $isAprovalId && $isdefaultFloorAndIcuEval) { ?>
                            <button id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
                        <?php }  ?>
                        <?php if ($currentstudentId > 0) { ?>
                            <a type="button" href="floorTherapyAndICUEvaluationlist.html?studentId=<?php echo EncodeQueryData($currentstudentId); ?>" class="btn btn-default margin_left_ten">Cancel</a>
                        <?php } else { ?>
                            <a type="button" href="floorTherapyAndICUEvaluationlist.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>" class="btn btn-default margin_left_ten">Cancel</a>
                        <?php } ?>
                    </div>
                </div>
            </div>
    </div>
    </div>
    </form>
    <?php include('includes/footer.php'); ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
    <script src="https://cdn.ckeditor.com/ckeditor5/35.0.1/classic/ckeditor.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script type="text/javascript">
        // ClassicEditor
        // 	.create(document.querySelector('#studentcomment'))
        // 	.catch(error => {
        // 		console.error(error);
        // 	});	
        $(window).load(function() {

            $('.isAllRadioButton').trigger('click');
            $(".isAllRadioButton input[type=radio]").prop('disabled', true);
            $("#textarea").prop('disabled', true);
            $("input[type=checkbox]").prop('disabled', true);

            $('#frmformative').parsley().on('field:validated', function() {
                    var ok = $('.parsley-error').length === 0;
                })
                .on('form:submit', function() {
                    ShowProgressAnimation();
                    return true; // Don't submit form for this demo
                });

            $('#evaluationDate').datetimepicker({
                format: 'MM/DD/YYYY'
            });
            $('#studentsignitureDate').datetimepicker({
                format: 'MM/DD/YYYY'
            });
            $('#InstructorDate').datetimepicker({
                format: 'MM/DD/YYYY'
            });
            $('#aprovalDate').datetimepicker({
                format: 'MM/DD/YYYY'
            });

            //for searching dropdown
            $(".select2_single").select2();
            $('#select2-cborotation-container').addClass('required-select2');
            $('#select2-cboclinician-container').addClass('required-select2');
            $('#select2-cbostudent-container').addClass('required-select2');
            $('#select2-aprovalId-container').addClass('required-select2');

            <?php if ($currentstudentId > 0) { ?>
                $('#cbostudent').prop('disabled', true);
            <?php }
            if ($rotationId > 0) { ?>
                document.getElementById("cborotation").required = false;
            <?php } ?>

        });

        //For Fourth Section
        $(document).ready(function() {
                    $(".isAllRadioButton").click(function() {
                                var isEvalType = "<?php echo $isEvalType; ?>";

                                var sumCheckedButton = 0;
                                var isTotal = 0;
                                var isNiTotal = 0;
                                var isUTotal = 0;
                                var isSSeleted = 0;
                                $(".isAllRadioButton input[type=radio]:checked").each(function() {

                                    var checkedRadio = $.trim($(this).parent().text());
                                    if (checkedRadio == 'NI')
                                        isNiTotal = 10 + parseInt(isNiTotal);
                                    if (checkedRadio == 'U')
                                        isUTotal = 25 + parseInt(isUTotal);
                                    if (checkedRadio == 'S')
                                        isSSeleted = 100;

                                    $('.isAllRadioButton').trigger('click');
                                    $(".isAllRadioButton input[type=radio]").prop('disabled', true);
                                    $("#textarea").prop('disabled', true);
                                    $("input[type=checkbox]").prop('disabled', true);

                                    $('#frmformative').parsley().on('field:validated', function() {
                                            var ok = $('.parsley-error').length === 0;
                                        })
                                        .on('form:submit', function() {
                                            ShowProgressAnimation();
                                            return true; // Don't submit form for this demo
                                        });

                                    $('#evaluationDate').datetimepicker({
                                        format: 'MM/DD/YYYY'
                                    });
                                    $('#studentsignitureDate').datetimepicker({
                                        format: 'MM/DD/YYYY'
                                    });
                                    $('#InstructorDate').datetimepicker({
                                        format: 'MM/DD/YYYY'
                                    });
                                    $('#aprovalDate').datetimepicker({
                                        format: 'MM/DD/YYYY'
                                    });

                                    //for searching dropdown
                                    $(".select2_single").select2();
                                    $('#select2-cborotation-container').addClass('required-select2');
                                    $('#select2-cboclinician-container').addClass('required-select2');
                                    $('#select2-cbostudent-container').addClass('required-select2');
                                    $('#select2-aprovalId-container').addClass('required-select2');

                                    <?php if ($currentstudentId > 0) { ?>
                                        $('#cbostudent').prop('disabled', true);
                                    <?php }
                                    if ($rotationId > 0) { ?>
                                        document.getElementById("cborotation").required = false;
                                    <?php } ?>

                                });

                                //For Fourth Section
                                $(document).ready(function() {
                                    $(".isAllRadioButton").click(function() {
                                        var isEvalType = "<?php echo $isEvalType; ?>";

                                        var sumCheckedButton = 0;
                                        var isTotal = 0;
                                        var isNiTotal = 0;
                                        var isUTotal = 0;
                                        var isSSeleted = 0;
                                        $(".isAllRadioButton input[type=radio]:checked").each(function() {

                                            var checkedRadio = $.trim($(this).parent().text());
                                            if (checkedRadio == 'NI')
                                                isNiTotal = 10 + parseInt(isNiTotal);
                                            if (checkedRadio == 'U')
                                                isUTotal = 25 + parseInt(isUTotal);
                                            if (checkedRadio == 'S')
                                                isSSeleted = 100;

                                        });

                                        // if(isEvalType == 1)
                                        // {
                                        isTotal = parseInt(isUTotal) + parseInt(isNiTotal);

                                        $("#rotationGrade").val('');
                                        // console.log(isTotal);
                                        if (isTotal > 0)
                                            $("#rotationGrade").val(100 - isTotal);
                                        else if (isSSeleted > 0)
                                            $("#rotationGrade").val(100);
                                        // }
                                    });

                                });
    </script>
    <script>
        // Get all collapsible button elements
        var buttons = document.querySelectorAll(".collapsible");
        var contents = document.querySelectorAll(".panel-collapse");

        // Add click event listeners to all buttons
        buttons.forEach(function(button, index) {
            button.addEventListener("click", function() {
                // Check if the content is currently expanded
                var isExpanded = contents[index].style.display === "block";

                // Close all sections
                contents.forEach(function(content) {
                    content.style.display = "none";
                });

                // Reset the "expanded" class for all buttons
                buttons.forEach(function(btn) {
                    btn.classList.remove("expanded");
                });

                // Toggle the content for the clicked section
                if (!isExpanded) {
                    contents[index].style.display = "block";
                    button.classList.add("expanded");
                }
            });
        });
    </script>
</body>

</html>