<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsSystemUser.php');
include('../class/clsSchool.php');
include('../setRequest.php');

$title = "Edit Profile";
$objSystemUser = new clsSystemUser();
$row =  $objSystemUser->GetSystemUserDetails($_SESSION['loggedUserId']);
unset($objSystemUser);

$firstName  = stripslashes($row['firstName']);
$lastName  = stripslashes($row['lastName']);
$email  = stripslashes($row['email']);
$userName  = stripslashes($row['username']);
$profilePic = stripslashes($row['profilePic']);
$defaultprofilePic = GetUserImagePath($_SESSION['loggedUserId'], $currentSchoolId, $profilePic);

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($title); ?></title>
    <?php include('includes/headercss.php'); ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/css/cropper.css">

    <style>
        .form-control {
            height: 45px;
        }

        .input-group {
            width: 100%;
        }

        .required-input {
            border-left: 3px solid red !important;
        }

        .input-group-addon {
            background: transparent;
        }

        .formSubHeading {
            border-bottom: 2px solid #d9d6d657;
            padding: 3px 0;
            position: relative;
        }

        .school-logo-section {
            display: flex;
            align-items: center;
        }

        .logo-image-section {
            margin-right: 20px
        }

        .img-thumbnail {
            width: 120px;
            max-width: 120px;
            border-radius: 10px;
            padding: 6px;
        }

        input[type="file"] {
            background-color: #fff !important;
        }

        @media screen and (max-width: 500px) {

            .school-logo-section {
                align-items: start;
                flex-direction: column;
            }

            .logo-image-section {
                margin-right: 0;
                margin-bottom: 20px;
            }

       
        }
    </style>
</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li class="active"><?php echo ($title); ?></li>
                </ol>
            </div>

        </div>
    </div>

    <div class="container">

        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "InvalidFile") {
        ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Please check image file extension.
                </div>

            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>

            <?php
            } else if ($_GET["status"] == "Updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Profile updated successfully.
                </div>
        <?php
            }
        }
        ?>

        <form id="frmEditProfile" data-parsley-validate class="form-horizontal" method="POST" action="editprofilesubmit.html" enctype="multipart/form-data">

            <div class="row">
                <div class="col-md-12">

                    <div class="formSubHeading">User Information</div>
                    <!-- Text input-->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtFirstName">First Name</label>
                                <div class="col-md-12">
                                    <input id="txtFirstName" data-parsley-pattern="^[a-zA-Z]+$" name="txtFirstName" value="<?php echo ($firstName); ?>" required type="text" placeholder="" class="form-control input-md required-input" required="">

                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <!-- Select Basic -->
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtLastName">Last Name</label>
                                <div class="col-md-12">
                                    <input id="txtLastName" data-parsley-pattern="^[a-zA-Z]+$" name="txtLastName" value="<?php echo ($lastName); ?>" required type="text" placeholder="" class="form-control input-md required-input" required="">

                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <!-- Text input-->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtEmail">Email</label>
                                <div class="col-md-12">
                                    <input id="txtEmail" name="txtEmail" value="<?php echo ($email); ?>" required type="email" placeholder="" class="form-control input-md required-input">

                                </div>
                            </div>
                        </div>
                        <!-- Text input-->
                        <!---div class="form-group">
                        <label class="col-md-4 control-label" for="cboGender">Gender</label>
                        <div class="col-md-8">
                            <select id="cboGender" name="cboGender" class="form-control select2_single" required>
                                <option value="">Select</option>
                                <option value="M" <?php //if($gender=="M"){
                                                    ?> selected <?php // } 
                                                                ?> >Male</option>
                                <option value="F" <?php //if($gender=="F"){
                                                    ?> selected <?php // } 
                                                                ?> >Female</option>
                            </select>
                        </div>
                    </div---->

                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtUsername">User Name</label>
                                <div class="col-md-12">
                                    <input id="txtUsername" name="txtUsername" value="<?php echo ($userName); ?>" required type="text" placeholder="" class="form-control input-md required-input">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="col-md-12">
                    <div class="formSubHeading">Other Information</div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="filePhoto">Upload Photo</label>
                                <div class="col-md-12 school-logo-section">
                                    <!-- Hidden field to store cropped image data -->
                                    <input type="hidden" id="fileLogo" name="fileLogo" value="">
                                    <input type="hidden" id="hasCroppedImage" name="hasCroppedImage" value="0">
                                    
                                    <section class="upload-section" id="uploadSection">
                                        <div class="upload-area" id="uploadArea">
                                            <input style="visibility: hidden;" type="file" id="fileInput" accept="image/*" hidden>
                                            <div class="upload-content">
                                                <?php if ($defaultprofilePic != '') { ?>
                                                    <div style="display: flex; flex-direction: column; align-items: center; gap: 10px;">
                                                        <img src="<?php echo ($defaultprofilePic . "?randId=" . $randormRefreshId) ?>" 
                                                            style="max-width: 150px; max-height: 150px; border-radius: 8px;  object-fit: contain; box-shadow: 0 4px 8px rgba(0,0,0,0.1);" 
                                                            alt="Profile Picture Preview">
                                                        <div style="text-align: center;">
                                                            <p style="color: green; font-weight: bold; margin: 5px 0; font-size: 14px;">✓ Current profile picture</p>
                                                            <span class="file-types" style="color: #666; font-size: 12px;">Click to select a different image if needed</span>
                                                        </div>
                                                    </div>
                                                <?php } else { ?>
                                                    <svg class="upload-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                                        <polyline points="7,10 12,15 17,10"></polyline>
                                                        <line x1="12" y1="15" x2="12" y2="3"></line>
                                                    </svg>
                                                    <p>Click to browse or drag and drop an image</p>
                                                    <span class="file-types">Supports: JPG, PNG, GIF, WebP</span>
                                                <?php } ?>
                                            </div>
                                        </div>
                                    </section>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Cropper Section -->
            <section class="cropper-section" id="cropperSection" style="display: none;">
                <div class="controls">
                    <div class="control-group">
                        <div class="mode-buttons">
                            <button type="button" class="mode-btn active" data-mode="rectangle">Rectangle</button>
                            <button type="button" class="mode-btn" data-mode="square">Square</button>
                        </div>
                    </div>
                    <div class="control-group">
                        <button type="button" class="action-btn" id="resetBtn">Reset</button>
                        <button type="button" class="action-btn" id="backBtn">Back</button>
                        <button type="button" class="action-btn primary" id="cropBtn">Crop & Save</button>
                    </div>
                    <div class="control-group">
                        <label>Zoom:</label>
                        <div class="zoom-controls">
                            <button type="button" class="zoom-btn" id="zoomOut">-</button>
                            <span class="zoom-level" id="zoomLevel">100%</span>
                            <button type="button" class="zoom-btn" id="zoomIn">+</button>
                            <input type="range" id="zoomSlider" min="20" max="300" value="100" class="zoom-slider">
                        </div>
                    </div>
                </div>
                <div style="display: flex; gap: 20px; align-items: flex-start;">
                    <div class="image-container" id="imageContainer">
                        <img id="previewImage" src="" alt="Preview">
                        <div class="crop-overlay" id="cropOverlay">
                            <div class="crop-selection" id="cropSelection"></div>
                        </div>
                    </div>

                    <div class="preview-section">
                        <h4>Cropped Preview</h4>
                        <canvas id="previewCanvas"></canvas>
                    </div>
                </div>
            </section>
            <div class="form-group">
                <label class="col-md-2 control-label"></label>
                <div class="col-md-12" style="display: flex; justify-content: center; margin: 20px 0;gap: 15px;">
                    <button id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
                    <a type="button" href="dashboard.html" class="btn btn-default">Cancel</a>
                </div>

            </div>






        </form>


    </div>

    <?php include('includes/footer.php'); ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/cropper.js"></script>
    
    <script type="text/javascript">
        // Initialize the image cropper when the page loads
        $(document).ready(function() {
            new ImageCropper();
            
            // Check if default profile image exists and display it
            const defaultImagePath = '<?php echo $defaultprofilePic; ?>';
            if (defaultImagePath && defaultImagePath !== '') {
                const $uploadArea = $('#uploadArea');
                
                // Add cropped state class to indicate we have an image
                $uploadArea.addClass('cropped-state');
            }
        });

        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        $(window).load(function() {
            $(".select2_single").select2();
            $('#select2-cboGender-container').addClass('required-select2');

            $('.image-preview').magnificPopup({
                type: 'image'
            });

            $('#frmEditProfile').parsley().on('field:validated', function() {
                    var ok = $('.parsley-error').length === 0;
                })
                .on('form:submit', function() {
                    ShowProgressAnimation();
                    
                    // Check if we have a cropped image
                    var hasCroppedImage = $('#hasCroppedImage').val() === '1';
                    
                    return true;
                });

            $("#txtUsername").change(function() {
                var currentUsername = $(this).val();
                $.ajax({
                    type: "POST",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_check_duplicate_username.html",
                    data: {
                        userId: '<?php echo (EncodeQueryData($_SESSION['loggedUserId'])); ?>',
                        userName: currentUsername,
                        type: 'user',
                        schoolId: '<?php echo (EncodeQueryData($currentSchoolId)); ?>'
                    },
                    success: function(responseData) {
                        if (responseData == 1) {
                            alertify.error("Username not available.");
                            $("#txtUsername").val('').focus();
                        }
                    }
                });
            });
        });
    </script>





</body>

</html>
