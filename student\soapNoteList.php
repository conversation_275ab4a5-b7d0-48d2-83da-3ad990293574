<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');
include('../class/clsSoapNote.php');
include('../class/clsRotation.php');
include('../class/clsLocations.php');
include('../setRequest.php');
include('../class/clsPreceptorDetails.php');
include('../class/clsExternalPreceptors.php');

$selrotationId = 0;
$rotationId = 0;
$studentSignatureDate = 0;
$TimeZone =  isset($_SESSION["loggedStudentSchoolTimeZone"]) ? $_SESSION["loggedStudentSchoolTimeZone"] : '';

if (isset($_GET['rotationId'])) {
    $DefaultrotationId = $_GET['rotationId'];
    $rotationId = DecodeQueryData($DefaultrotationId);
}

$studentId = $_SESSION["loggedStudentId"];
if (isset($_GET['studentId'])) {
    $currentstudentId = DecodeQueryData($_GET['studentId']);
    $studentId = DecodeQueryData($_GET['studentId']);
}

//Get All Soap Note List
$objSoapNote = new clsSoapNote();
$rowsSoapNoteData = $objSoapNote->GetAllSoapNote($currentSchoolId, $studentId, $rotationId);
$totalSoapNoteCount = 0;
if ($rowsSoapNoteData != '') {
    $totalSoapNoteCount = mysqli_num_rows($rowsSoapNoteData);
}
unset($objSoapNote);

$status = isset($_GET["status"]) ? $_GET["status"] : '';
$rotationStatus = checkRotationStatus($rotationId);

$objDB = new clsDB();
$loggedStudentEmailId = $objDB->GetSingleColumnValueFromTable('student', 'email', 'studentId', $studentId);
unset($objDB);

//rotation
$objrotation = new clsRotation();
$bedCrumTitle = 'Rotation';
$rotationtitle = '';
//Get Rotation Name 
$rowsrotation = $objrotation->GetrotationTitleForInteraction($rotationId, $currentSchoolId);
$rotationtitle = isset($rowsrotation['title']) ? $rowsrotation['title'] : '';

?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>Soap Note</title>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <?php include('includes/headercss.php'); ?>
    <?php include("includes/datatablecss.php") ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">

    <style>
        .input-group-addon {
            position: absolute;
            right: 7px;
            /* width: 100%; */
            z-index: 99;
            width: 35px;
            margin: auto;
            top: 5px;
            border-left: 1px solid #ccc;
            border-radius: 50% !important;
            padding: 10px -2px;
            height: 35px;
            /* background: #01A750; */
            /* color: #fff; */
            color: #555;
            background: #f6f9f9;
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .panel-default>.panel-heading {
            background-color: transparent !important;
        }

        .btn-success,
        .btn-default {
            padding: 8px 25px;
            border-radius: 10px;
        }

        .panel {
            border-radius: 14px !important;
        }

        .input-group-addon {
            position: absolute;
            right: 7px;
            /* width: 100%; */
            z-index: 99;
            width: 35px;
            margin: auto;
            top: 5px;
            border-left: 1px solid #ccc;
            border-radius: 50% !important;
            padding: 10px -2px;
            height: 35px;
            /* background: #01A750; */
            /* color: #fff; */
            color: #555;
            background: #f6f9f9;
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .panel-default>.panel-heading {
            background-color: transparent !important;
        }

        .btn-success,
        .btn-default {
            padding: 8px 25px;
            border-radius: 10px;
        }

        .panel {
            border-radius: 14px !important;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 45px !important;
        }

        .required-select2 {
            border-left: solid 3px red !important;
            border-radius: 12px !important;
        }

        .select2-container--default .select2-selection--single {
            background-color: #f6f9f9 !important;
            cursor: default !important;
            height: 45px !important;
            border-radius: 10px !important;
        }

        .select2-container--default .select2-selection--single {
            border: none !important;
        }

        .panel,
        .form-group {
            margin-bottom: 10px;
        }

        .bootstrap-datetimepicker-widget {
            border-radius: 12px !important;
        }

        .form-control {
            height: 45px;
        }

        .input-group {
            width: 100%;
        }

        /* .form-horizontal .form-group {
                margin-right: 0;
                margin-left: 0;
            } */

        @media screen and (max-width: 500px) {
            .panel-body ol {
                padding-left: 20px;
            }

            .panel-default>.panel-heading {
                padding-left: 5px;
            }

            /* .container-zero{
                    padding: 0;
                } */

            div.dataTables_wrapper div.dataTables_length {
                text-align: left !important;
                margin-top: 10px;
            }
        }
    </style>
</head>

<body>

    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <?php if ($rotationId > 0) { ?>
                        <li class="active"><a href="rotations.html?active=1"><?php echo $bedCrumTitle; ?></a></li>
                        <li class="active"><a href="rotations.html?active=1"><?php echo ($rotationtitle); ?></a></li>
                    <?php } ?>
                    <li class="active">Soap Note</li>
                </ol>
            </div>
            <?php if ($rotationStatus == 0) { ?>
                <div class="pull-right" style="margin-top:5px">
                    <a href="addSoapNote.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>">Add</a>
                </div>
            <?php } ?>
        </div>
    </div>

    <div class="container">
        <?php
        if (isset($_GET["status"])) {
            $message = "";
            $alertType = "alert-success";

            switch ($_GET["status"]) {
                case "Added":
                    $message = "Soap Note added successfully.";
                    break;
                case "Updated":
                    $message = "Soap Note updated successfully.";
                    break;
                case "SignOff":
                    $message = "Soap Note sign off successfully.";
                    break;
                case "Error":
                    $message = "Error occurred.";
                    $alertType = "alert-danger";
                    break;
            }

            if ($message) {
                echo '<div class="alert ' . $alertType . ' alert-dismissible fade in" role="alert">';
                echo '<button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>';
                echo $message;
                echo '</div>';
            }
        }
        ?>

        <div id="divTopLoading">Loading...</div>

        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Rotation</th>
                    <th>Clinician/Preceptor Details</th>
                    <th>Clinician/Preceptor <br>Sign Date </th>
                    <th>Student Sign Date </th>
                    <th style="text-align: center">Action</th>
                </tr>
            </thead>
            <tbody>
                <?php
                if ($totalSoapNoteCount > 0) {
                    while ($row = mysqli_fetch_array($rowsSoapNoteData)) {

                        $soapNoteId = ($row['soapNoteId']);
                        $soapNoteDate = stripslashes($row['soapNoteDate']);
                        $rotationName = stripslashes($row['rotationName']);
                        $isSendToPreceptor = stripslashes($row['isSendToPreceptor']);
                        $externalPreceptorId = stripslashes($row['preceptorId']);
                        $clinicianName = stripslashes($row['clinicianName']);
                        $preceptorStatus = stripslashes($row['status']);
                        $rotationId = $row['rotationId'];
                        $courselocationId = $_POST['locationId'] ?? null;
                        $parentRotationId = $_POST['parentRotationId'] ?? null;
                        $rotationLocationId = $_POST['rotationLocationId'] ?? null;
                        $studentSignatureDate = stripslashes($row['studentSignatureDate']);

                        $soapNoteDate = date("m/d/Y", strtotime($soapNoteDate));
                        if ($soapNoteDate != '12/31/1969' && $soapNoteDate != '' && $soapNoteDate != '11/30/-0001' && $soapNoteDate != '0000-00-00') {
                            $soapNoteDate = converFromServerTimeZone($soapNoteDate, $TimeZone);
                            $soapNoteDate = date("m/d/Y", strtotime($soapNoteDate));
                        } else
                            $soapNoteDate = '';

                        $isSendToPreceptor = stripslashes($row['isSendToPreceptor']);

                        if ($isSendToPreceptor == 0) {
                            $clinicianSignatureDate = stripslashes($row['clinicianSignatureDate']); //clinician signature date
                            if ($clinicianSignatureDate != '12/31/1969' && $clinicianSignatureDate != '' && $clinicianSignatureDate != '11/30/-0001' && $clinicianSignatureDate != '0000-00-00') {
                                $clinicianSignatureDate = converFromServerTimeZone($clinicianSignatureDate, $TimeZone);
                                $clinicianSignatureDate = date("m/d/Y", strtotime($clinicianSignatureDate));
                            } else
                                $clinicianSignatureDate = '-';
                        } else {
                            $clinicianSignatureDate = stripslashes($row['signatureDate']); //preceptor signature date
                            if ($clinicianSignatureDate != '12/31/1969' && $clinicianSignatureDate != '' && $clinicianSignatureDate != '11/30/-0001' && $clinicianSignatureDate != '0000-00-00') {
                                $clinicianSignatureDate = converFromServerTimeZone($clinicianSignatureDate, $TimeZone);
                                $clinicianSignatureDate = date("m/d/Y", strtotime($clinicianSignatureDate));
                            } else
                                $clinicianSignatureDate = '-';
                        }

                        $studentSignatureDate  = stripslashes($row['studentSignatureDate']); //student signature date
                        if ($studentSignatureDate  != '12/31/1969' && $studentSignatureDate  != '' && $studentSignatureDate  != '11/30/-0001' && $studentSignatureDate  != '0000-00-00') {
                            $studentSignatureDate = converFromServerTimeZone($studentSignatureDate, $TimeZone);
                            $studentSignatureDate  = date("m/d/Y", strtotime($studentSignatureDate));
                        } else
                            $studentSignatureDate  = '-';

                        $isCompletedStatus = '';
                        $objExternalPreceptors = new clsExternalPreceptors();

                        $externalPreceptorDetail = $objExternalPreceptors->GetExternalPreceptorDetail($externalPreceptorId);
                        $preceptorId = isset($externalPreceptorDetail['id']) ? $externalPreceptorDetail['id'] : 0;
                        $preceptorFirstName = isset($externalPreceptorDetail['firstName']) ? $externalPreceptorDetail['firstName'] : '';
                        $preceptorLastName = isset($externalPreceptorDetail['lastName']) ? $externalPreceptorDetail['lastName'] : '';
                        $preceptorFullName = $preceptorFirstName . ' ' . $preceptorLastName;
                        $preceptorMobileNum = isset($externalPreceptorDetail['mobile_num']) ? $externalPreceptorDetail['mobile_num'] : '';
                        $formatted_number = '';
                        if ($preceptorMobileNum != '') {
                            $mobileNum = str_replace("-", "", $preceptorMobileNum);

                            $masked_number = substr_replace($mobileNum, "XXXXXX", 0, 6);
                            $formatted_number = substr($masked_number, 0, 3) . "-" . substr($masked_number, 3, 3) . "-" . substr($mobileNum, 6);
                        }
                        $isCompletedStatus = $preceptorStatus ? 'Completed' : 'Pending';
                        $objDB = new clsDB();

                ?>
                        <tr>
                            <td>
                                <?php echo ($soapNoteDate);
                                ?>
                            </td>
                            <td>
                                <?php echo ($rotationName);
                                ?>
                            </td>
                            <td class="<?php if ($preceptorMobileNum == 0) {
                                            echo 'text-center';
                                        }  ?>">
                                <?php if ($isSendToPreceptor) { ?>
                                    Name: <?php echo $preceptorFullName; ?> <br>
                                    Phone: <?php echo $formatted_number; ?> <br>
                                    Status: <?php echo $isCompletedStatus; ?> <br>
                                    <?php if ($preceptorStatus != 1) {
                                    ?>
                                        <a href="javascript:void(0);" soapNoteId="<?php echo $soapNoteId; ?>" preceptorMobileNum="<?php echo $preceptorMobileNum; ?>" preceptorId=<?php echo EncodeQueryData($preceptorId); ?> rotationId="<?php echo $rotationId ?>" class="reSendrequest" data-toggle="modal" data-target="#resendModal" title="Resend Activtiy Sheet" onclick="ShowEvaluationDetails(this)">Resend SMS</a>
                                        | <a href="javascript:void(0);" soapNoteId="<?php echo $soapNoteId; ?>" preceptorMobileNum="<?php echo $preceptorMobileNum; ?>" preceptorId=<?php echo EncodeQueryData($preceptorId); ?> rotationId="<?php echo $rotationId ?>" class="copyLink" evaluationType='soapnote' onclick="copyLinkUrl(this)">Click to Copy URL</a>
                                        <br><a href="javascript:void(0);" soapNoteId="<?php echo $soapNoteId; ?>" evaluationType="soapnote" preceptorMobileNum="<?php echo $preceptorMobileNum; ?>" preceptorId=<?php echo EncodeQueryData($preceptorId); ?> rotationId="<?php echo $rotationId ?>" class="reSendEmailrequest" data-toggle="modal" email="<?php echo $loggedStudentEmailId; ?>" data-target="#resendEmailModal" onclick="ShowEvaluationDetailsForEmail(this)">Send URL to Email</a>
                                    <?php }
                                } else { ?>
                                    <?php echo $clinicianName; ?>
                                <?php } ?>
                            </td>
                            <td align="center">
                                <?php echo ($clinicianSignatureDate); ?>
                            </td>
                            <td align="center">
                                <?php echo ($studentSignatureDate); ?>
                            </td>
                            <td style="text-align: center">
                                <?php
                                $rotationStatus = checkRotationStatus($rotationId);
                               if($rotationStatus == 0){
                                   if ($studentSignatureDate == '-' && $clinicianSignatureDate == '-') {
                                    // Neither signed - show Edit
                                ?>
                                    <a href="addSoapNote.html?editsoapNoteId=<?php echo EncodeQueryData($soapNoteId); ?>">Edit</a>
                                <?php
                                } else if ($studentSignatureDate == '-' && $clinicianSignatureDate != '-') {
                                    // Clinician signed but student hasn't - show Signoff
                                ?>
                                    <a href="addSoapNote.html?editsoapNoteId=<?php echo EncodeQueryData($soapNoteId); ?>&isSignoff=1">Signoff</a>
                                <?php
                                } else if (($studentSignatureDate != '-' && $clinicianSignatureDate != '-') || $rotatinStatus) {
                                    // Both signed or rotation is completed - show View
                                ?>
                                    <a href="addSoapNote.html?editsoapNoteId=<?php echo EncodeQueryData($soapNoteId); ?>&view=V">View</a>
                                <?php
                                }
                                ?>
                               <?php }else{ ?>
                                        <a href="addSoapNote.html?editsoapNoteId=<?php echo EncodeQueryData($soapNoteId); ?>&view=V">View</a>
                           <?php    } ?>
                              
                            </td>

                        </tr>
                <?php
                    }
                }
                unset($objrotation);
                ?>
            </tbody>

        </table>
    </div>

    <?php include('includes/resendSms.php'); ?>
    <?php include('includes/resendLinkToEmail.php'); ?>
    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>

    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        $(window).load(function() {
            $("#divTopLoading").addClass('hide');
            $(".select2_single").select2();

            $(function() {
                $("#fromDate").datetimepicker({
                    format: "MM/DD/YYYY",
                    useCurrent: false
                });
            });

            $(function() {
                $("#toDate").datetimepicker({
                    format: "MM/DD/YYYY",
                    useCurrent: false
                });
            });

            $("#cborotation").change(function() {
                var rotationId = $(this).val();
                if (rotationId) {
                    window.location.href = "soapNoteList.html?rotationId=" + rotationId;
                } else {
                    window.location.href = "soapNoteList.html";
                }
            });

        });
        var current_datatable = $("#datatable-responsive").DataTable({
            "scrollX": true,
            "responsive": false,
            "ordering": true,
            "order": [
                [0, "desc"]
            ],

            "aoColumns": [{
                    "sWidth": "20%"
                },
                {
                    "sWidth": "25%",
                    "sClass": "alignCenter"
                }, 
                {
                    "sWidth": "25%",
                    "sClass": "alignCenter"
                },
                {
                    "sWidth": "20%",
                    "sClass": "alignCenter"
                }, {
                    "sWidth": "20%",
                    "sClass": "alignCenter"
                }, {
                    "sWidth": "20%",
                    "sClass": "alignCenter",
                }
            ]
        });

        function ShowEvaluationDetails(eleObj) {
            var preceptorId = $(eleObj).attr('preceptorId');
            var preceptorNum = $(eleObj).attr('preceptorMobileNum');
            var soapNoteId = $(eleObj).attr('soapNoteId');
            var rotationId = $(eleObj).attr('rotationId');
            var title = $(eleObj).attr('title');

            $('#preceptorId').val(preceptorId);
            $('#preceptorNum').val(preceptorNum);
            $('#soapNoteId').val(soapNoteId);
            $('#rotationId').val(rotationId);
            $('#evaluationType').val('soapnote');
            $('#title').text(title);
        }

        $(document).on('click', '.btnSendSms', function() {
            var data = $('#resendForm').serialize();
            var mobileNo = $('#preceptorNum').val();
            var rsmobileNo = mobileNo.replace(/[_-]/g, '');
            var mobileNoLength = rsmobileNo.length;

            if (mobileNoLength != 10) {
                alertify.error('Invalid Mob Number')
                return false;
            }

            $.ajax({
                type: "POST",
                url: "../ajax/send_evaluation_sms_to_preceptor.html",
                data: {
                    data
                },
                success: function(data) {
                    alertify.success('Sent');
                    // console.log(data);
                    // window.location.reload();
                    setTimeout(function() {
                        window.location.reload();
                    }, 2000);
                    // history.go(0);
                }
            });
        });
    </script>
</body>

</html>