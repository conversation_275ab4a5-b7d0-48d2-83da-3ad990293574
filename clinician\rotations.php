<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsRotation.php');
include('../class/clsStudent.php');
include('../class/clsLocations.php');
include('../class/clsHospitalSite.php');
include('../setRequest.php');

// echo '<pre>';
// print_r($_POST);
// print_r($_GET);

$schoolId = 0;
$subratoncount = 0;
$skipParentRotations = 0;
$rotationId = 0;
$locationId = 0;
$clinicianId = 0;
$loggedClinicianType = '';
$from_date = '';
$to_date = '';
$TimeZone = $_SESSION["loggedClinicianSchoolTimeZone"];
$transchooldisplayName = '';
$loggedClinicianType = $_SESSION['loggedClinicianType'];
$studentId = 0;
$hospitalsiteId = 0;
$totalHospitalSite = 0;
$rankId = 0;
//For Hospital Site List
$isActiveHospital = 2;  //Set By Default All tab selected 

if (isset($_GET['active']))
    $isActiveHospital = DecodeQueryData($_GET['active']);

if ($isActiveHospital == 2)
    $clsBtnActiveAll = "active";
elseif ($isActiveHospital == 0)
    $clsBtnActive = "active";
elseif ($isActiveHospital == 1) {
    $clsBtnInActive = "active";
} else
    $clsBtnActive = "active";

$display_from_date = '';
$display_to_date = '';

if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSearch'])) {
    $display_from_date = $_POST['fromDate'];
    $from_date = date("Y-m-d", strtotime($display_from_date));
    $display_to_date = $_POST['toDate'];
    $to_date = date("Y-m-d", strtotime($display_to_date));
}


if (isset($_GET['rotationId'])) //Edit Mode
{
    $rotationId = $_GET['rotationId'];
    $rotationId = DecodeQueryData($rotationId);
}
if (isset($_GET['schoolId'])) //Edit Mode
{
    $schoolId = $_GET['schoolId'];
    $schoolId = DecodeQueryData($schoolId);
} else {
    $schoolId = $currentSchoolId;
    $transchooldisplayName = $currenschoolDisplayname;
}
$clinicianId = $_SESSION["loggedClinicianId"];
$title = "Rotations  ";

//Filter by Student
if (isset($_GET['studentId'])) {
    $studentId = $_GET['studentId'];
    $studentId = DecodeQueryData($studentId);
}

//Filter by Hospitalsite
if (isset($_GET['hospitalsiteId'])) {
    $hospitalsiteId = $_GET['hospitalsiteId'];
    $hospitalsiteId = DecodeQueryData($hospitalsiteId);
}

//Soap Note Module - Enabled/Disabled through superadmin -> admin 
$objDB = new clsDB();
$soapNote = $objDB->GetSingleColumnValueFromTable('schoolsettings', 'status', 'schoolId', $schoolId, 'type', 'soapNote');
unset($objDB);


//For Rotation List
$objRotation = new clsRotation();
$totalRotations = 0;
$courseId = 0;
$locationId = $_SESSION['loggedClinicianLocationId'];
$activeRecords = [];
$inactiveRecords = [];
if ($loggedClinicianType == 'p' || $loggedClinicianType == 'P' || $loggedClinicianType == 'C' || $loggedClinicianType == 'c') {

    if ($isActiveHospital == 0) {
        $rowsRotations = $objRotation->GetAllActiveRotationsByClinicianRoleForApp($currentSchoolId, $clinicianId, $rotationId, $from_date, $to_date, $courseId, $studentId, $hospitalsiteId);
    } elseif ($isActiveHospital == 1) {
        $rowsRotations = $objRotation->GetInactiveRotationsByClinicianRoleForApp($currentSchoolId,  $clinicianId, $rotationId, $from_date, $to_date, $courseId, $studentId, $hospitalsiteId);
    } else {
        $rowsRotations = $objRotation->GetRotationByClinicianRole($currentSchoolId, $clinicianId, $rotationId, $from_date, $to_date, $studentId, $hospitalsiteId);
    }
} else {

    if ($isActiveHospital == 0) {
        $rowsRotations = $objRotation->GetAllActiveRotationsBySchool($currentSchoolId, $rotationId, $from_date, $to_date, $courseId,  $studentId, $hospitalsiteId);
    } elseif ($isActiveHospital == 1) {
        $rowsRotations = $objRotation->GetInactiveRotationsBySchool($currentSchoolId, $rotationId, $from_date, $to_date, $courseId, $studentId, $hospitalsiteId);
    } else {
        $rowsRotations = $objRotation->GetRotationForClinicianBySchoolId($currentSchoolId, $rotationId, $from_date, $to_date, $studentId, $hospitalsiteId);
    }
}

if ($rowsRotations != '') {
    $totalRotations = mysqli_num_rows($rowsRotations);
}

$isActiveCheckoff = '';
$isActiveCheckoff = $_SESSION["isActiveCheckoff"];

//For All Student List
$totalStudentCount = 0;
$objStudent = new clsStudent();

if ($loggedClinicianType == 'p' || $loggedClinicianType == 'P' || $loggedClinicianType == 'C' || $loggedClinicianType == 'c') {
    $studentList = $objStudent->GetClinicianStudentsByHospital($clinicianId, $rankId);
} else {
    $studentList = $objStudent->GetAllSchoolStudents($schoolId);
}

if ($studentList != '') {
    $totalStudentCount = mysqli_num_rows($studentList);
}
unset($objStudent);

//For All Hospitalsite List
$totalHospitalsiteCount = 0;
$objHospitalsite = new clsHospitalSite();

if ($loggedClinicianType == 'p' || $loggedClinicianType == 'P' || $loggedClinicianType == 'C' || $loggedClinicianType == 'c') {
    $hospitalsiteList = $objHospitalsite->GetAllHospitalSiteByRole($schoolId, $clinicianId);
} else {
    $hospitalsiteList = $objHospitalsite->GetAllHospitalSites($schoolId);
}

if ($hospitalsiteList != '') {
    $totalHospitalsiteCount = mysqli_num_rows($hospitalsiteList);
}
unset($clsHospitalSite);
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($title); ?></title>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <?php include('includes/headercss.php'); ?>
    <?php include("includes/datatablecss.php") ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/fullcalendar/fullcalendar_main.css" />

    <style>
        .some-class {
            float: left;
            clear: none;
        }

        .modalPosition {
            position: unset !important;
            top: 20px;
            margin-left: 10px;
            width: auto;
        }

        .input-group-addon {
            position: absolute;
            right: 7px;
            /* width: 100%; */
            z-index: 99;
            width: 35px;
            margin: auto;
            top: 5px;
            border-left: 1px solid #ccc;
            border-radius: 50% !important;
            padding: 10px -2px;
            height: 35px;
            /* background: #01A750; */
            /* color: #fff; */
            color: #555;
            background: #f6f9f9;
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .panel-default>.panel-heading {
            background-color: transparent !important;
        }

        .btn-success,
        .btn-default {
            padding: 8px 25px;
            border-radius: 10px;
        }

        .panel {
            border-radius: 14px !important;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 45px !important;
        }

        .required-select2 {
            border-left: 3px solid red !important;
            border-radius: 12px !important;
        }

        .select2-container--default .select2-selection--single {
            background-color: #f6f9f9 !important;
            cursor: default !important;
            height: 45px !important;
            border-radius: 10px !important;
        }

        .select2-container--default .select2-selection--single {
            border: none !important;
        }

        .panel,
        .form-group {
            margin-bottom: 10px;
        }

        .bootstrap-datetimepicker-widget {
            border-radius: 12px !important;
        }

        .form-control {
            height: 45px;
        }


        .input-group {
            width: 100%;
        }

        .required-input {
            border-left: 3px solid red !important;
        }

        .input-group-addon {
            background: transparent;
        }

        #datatable-responsive_wrapper {
            overflow-x: auto;
        }

        div.dataTables_wrapper div.dataTables_length select {
            height: 45px;
        }

        @media screen and (max-width: 500px) {
            .panel-body ol {
                padding-left: 20px;
            }

            .panel-default>.panel-heading {
                padding-left: 5px;
            }
        }


        a {
            text-decoration: none !important;
        }

        .fc-direction-ltr {
            border: 1px solid #8080802b;
            padding: 8px;
            border-radius: 10px;
            margin-top: 20px;
            box-shadow: rgba(17, 17, 26, 0.05) 0px 1px 0px, rgba(17, 17, 26, 0.1) 0px 0px 8px;
        }

        .fc {
            font-size: 1em;
        }

        .fc .fc-toolbar-title {
            font-size: 1.2em;
            margin: 0;
            color: rgb(45, 105, 182);
            font-weight: 600;
        }

        .fc .fc-button-primary:disabled,
        .fc .fc-button {
            color: #fff;
            background-color: rgb(45, 105, 182) !important;
            border-color: rgb(45, 105, 182);
        }

        .fc .fc-button-primary:hover {
            border: none;
        }

        .fc .fc-button {
            font-weight: 400;
            padding: 0.4em 0.65em;
            font-size: .8em;
            line-height: 1.5;
            border-radius: 0.25em;
            background: transparent;
        }

        .fc-theme-standard td,
        .fc-theme-standard th,
        .fc-theme-standard .fc-scrollgrid {
            border: none;
        }

        .fc th {
            /* text-align: left; */
            text-align: center;
        }

        .fc-toolbar-chunk:nth-child(3) {
            display: flex;
        }

        .fc .fc-scrollgrid-section-body table,
        .fc .fc-scrollgrid-section-footer table,
        .fc .fc-daygrid-body,
        .fc .fc-scrollgrid-section table {
            width: 100% !important;
        }

        .fc .fc-view-harness {
            height: 162.074px !important;
        }

        .fc-direction-ltr {
            background: #ffffff !important;
        }

        .fc .fc-button-primary {
            border: none;
        }

        .fc .fc-bg-event,
        .fc .fc-non-business,
        .fc .fc-highlight {
            left: 4px;
        }

        .fc .fc-col-header-cell-cushion {
            color: #337ab7;
            background-color: transparent;
            padding: 2px 0px;
            font-weight: 600;
        }

        .fc .fc-daygrid-day-number {
            position: relative;
            z-index: 4;
            padding: 4px;
        }

        .fc .fc-bg-event {
            background: rgb(143, 223, 130);
            background: var(--fc-bg-event-color, rgb(143, 223, 130));
            opacity: 0.3;
            /* / opacity: var(--fc-bg-event-opacity, 0.3); / */
            color: #ffffff;
            border-radius: 50%;
            width: 28px;
            height: 28px;
            /* / display: flex; /
            / justify-content: end; / */
            float: right;
        }

        .fc .fc-scroller-liquid-absolute,
        .fc-scroller {
            overflow: hidden !important;
        }

        .fc .fc-daygrid-day-frame {
            position: relative;
            min-height: 100%;
            height: 28px;
        }

        .fc .fc-daygrid-day-top {
            display: flex;
            flex-direction: row;
            justify-content: center;
        }

        .hover-trigger {
            cursor: pointer;
            background: transparent;
            outline: none;
            border: none;
        }

        .fc-today-button {
            margin-left: 4px !important;
        }

        .table-flex {
            display: flex;
            justify-content: space-between;
        }

        .display-none {
            display: none;
        }

        .display-flex {
            display: flex;
        }

        .calendar-container {
            position: absolute;
            right: 5px;
            top: 10px;
        }

        .position-relative {
            position: relative;
        }
    </style>
</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li class="active">Rotations</li>
                </ol>
            </div>
        </div>
    </div>

    <div class="custom-container">

        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "Added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Rotation added successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Rotation updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Deleted") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Rotation deleted successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
        <?php
            }
        }
        ?>

        <div id="divTopLoading">Loading...</div>

        <div class="row margin_bottom_ten">
            <div class="btn-group pull-right" style="margin-right: 14px;" role="group" aria-label="First group">
                <a role="button" class="btn btn-primary <?php echo $clsBtnActive; ?>" href="rotations.html?schoolId=<?php echo EncodeQueryData($currentSchoolId); ?>&active=<?php echo EncodeQueryData(0); ?>">Active</a>
                <a role="button" class="btn btn-primary  <?php echo $clsBtnInActive; ?>" href="rotations.html?schoolId=<?php echo EncodeQueryData($currentSchoolId); ?>&active=<?php echo EncodeQueryData(1); ?>">Inactive</a>
                <a role="button" class="btn btn-primary <?php echo $clsBtnActiveAll; ?>" href="rotations.html?schoolId=<?php echo EncodeQueryData($currentSchoolId); ?>&active=<?php echo EncodeQueryData(2); ?>">All</a>
            </div>
        </div>
        <div class="row">
            <div class="col-md-2 pull-right padding_right_zero" style="margin-right: 10px;">
                <select id="cboStudent" name="cboStudent" class="form-control input-md select2_single redirectUrl">
                    <option value="" selected>Select</option>
                    <?php
                    if ($studentList != "") {
                        while ($row = mysqli_fetch_assoc($studentList)) {

                            $studentsId  = $row['studentId'];
                            $firstName = $row['firstName'];
                            $lastName = $row['lastName'];
                            $fullName  = $firstName . ' ' . $lastName;

                    ?>
                            <option value="<?php echo EncodeQueryData($studentsId); ?>" <?php if ($studentId == $studentsId) { ?> selected="true" <?php } ?>><?php echo ($fullName); ?></option>
                    <?php
                        }
                    }
                    ?>
                </select>
            </div>
            <label style="margin-top: 10px;" class=" control-label  pull-right " for="cboStudent">Student :</label>

            <div class="col-md-2 pull-right padding_right_zero" style="margin-right: 20px;">
                <select id="cboHospitalsite" name="cboHospitalsite" class="form-control input-md select2_single redirectUrl">
                    <option value="" selected>Select</option>
                    <?php
                    if ($hospitalsiteList != "") {
                        while ($row = mysqli_fetch_assoc($hospitalsiteList)) {

                            $hospitalsitesId  = $row['hospitalSiteId'];
                            $hospitalName = $row['title'];
                    ?>
                            <option value="<?php echo EncodeQueryData($hospitalsitesId); ?>" <?php if ($hospitalsiteId == $hospitalsitesId) { ?> selected="true" <?php } ?>><?php echo ($hospitalName); ?></option>
                    <?php
                        }
                    }
                    ?>
                </select>
            </div>
            <label style="margin-top: 10px;" class=" control-label  pull-right " for="cboHospitalsite">Hospital Site :</label>
        </div>
        <div class="row" style="margin: 0 !important;">
            <?php

            $url = "rotations.html?active=" . EncodeQueryData($isActiveHospital);
            if ($hospitalsiteId) {
                $url .= "&hospitalsiteId=" . EncodeQueryData($hospitalsiteId);
            }
            if ($studentId) {
                $url .= "&studentId=" . EncodeQueryData($studentId);
            }
            ?>
            <form name="rotationlist" id="rotationlist" method="POST" action="<?php echo $url; ?>" style="display: flex;align-items: end;">
                <div class="col-md-3  margin_bottom_ten">
                    <div class="form-group">
                        <label class="col-md-4 control-label p-0" for="fromDate" style="margin-top:8px;text-align:end">Start Date</label>

                        <div class='input-group date' name="fromDate1" id='fromDate1'>
                            <input type='text' name="fromDate" id="fromDate" value="<?php echo ($display_from_date);
                                                                                    ?>" class="form-control input-md  rotation_date dateInputFormat" data-parsley-errors-container="#error-txtDate" placeholder="MM/DD/YYYY" />
                            <span class="input-group-addon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                        <div id="error-txtDate"></div>
                    </div>
                </div>

                <div class="col-md-3  margin_bottom_ten">
                    <div class="form-group">
                        <label class="col-md-4 control-label" for="toDate" style="margin-top:10px;text-align:end">End Date</label>
                        <div class='input-group date' id='toDate1'>
                            <input type='text' name="toDate" id="toDate" class="form-control input-md  rotation_date dateInputFormat" value="<?php echo ($display_to_date); ?>" data-parsley-errors-container="#error-txtDate" placeholder="MM/DD/YYYY" />
                            <span class="input-group-addon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                        <div id="error-txtDate"></div>
                    </div>
                </div>
                <div class="col-md-2  margin_bottom_ten">
                    <div class="form-group">
                        <button id="btnSearch" name="btnSearch" class="btn btn-success">Search</button>
                    </div>
                </div>
            </form>
        </div>

        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr>
                    <th style="text-align: center">Rotation</th>
                    <th style="text-align: center">Course</th>
                    <th style="text-align: center">Start Date</th>
                    <th style="text-align: center">End Date</th>
                    <th style="text-align: center">Duration</th>
                    <th style="text-align: center">Students</th>
                    <th style="text-align: center">Action</th>
                </tr>
            </thead>
            <tbody>
                <?php
                $scheduledatesArray = array();

                if ($totalRotations > 0) {
                    while ($row = mysqli_fetch_array($rowsRotations)) {
                        $rotationId = $row['rotationId'];
                        $courseTitle = $row['courseTitle'];
                        $locationTitle = $row['locationTitle'];
                        //$studentId = $row['studentId'];					
                        $startDate = $row['startDate'];
                        $courselocationId = $row['locationId'];
                        $parentRotationId = stripslashes($row['parentRotationId']);
                        $rotationLocationId = stripslashes($row['rotationLocationId']);
                        $locationId = 0;
                        $courseHours = $row['courseHours'];

                        if ($rotationLocationId != $courselocationId && $parentRotationId > 0) {
                            if (!$rotationLocationId)
                                $locationId = $objRotation->GetLocationByRotation($rotationId);
                            else
                                $locationId  = $rotationLocationId;
                        } else {
                            $locationId  = $courselocationId;
                        }

                        //Get Time Zone By Rotation 
                        $objLocation = new clsLocations();
                        $TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
                        unset($objLocation);
                        if ($TimeZone == '')
                            $TimeZone = $_SESSION["loggedClinicianSchoolTimeZone"];

                        $startDate = converFromServerTimeZone($startDate, $TimeZone);
                        $endDate = $row['endDate'];
                        $endDate = converFromServerTimeZone($endDate, $TimeZone);
                        $title = stripslashes($row['rotationName']);
                        $duration = $row['duration'];
                        $rotationStudentCount = $objRotation->GetCountRotationByStudent($rotationId);
                        if ($loggedClinicianType != 'p' && $loggedClinicianType != 'P') {

                            $subratoncount = $row['subratoncount'];
                        }
                        $startDateTimestamp = strtotime($startDate);
                        $endDateTimestamp = strtotime($endDate);

                        $hospitalSite = $row['hospitalTitle'];
                        $hospitalSiteDispName = strlen($hospitalSite) > 30 ? (substr($hospitalSite, 0, 27)) . '...' : $hospitalSite;
                        $today = date("m/d/Y");
                        $date_convert = date('m/d/Y', $startDateTimestamp);

                        $rotationLocation = $objRotation->GetLocationNameBySubRotationId($rotationId);

                        //check rotation is schedule or not
                        $objDB = new clsDB();
                        $isSchedule = $objDB->GetSingleColumnValueFromTable('rotation', 'isSchedule', 'rotationId', $rotationId);
                        unset($objDB);
                        if ($isSchedule) {
                            $rotationScheduleDates = $objRotation->GetScheduleDateById($rotationId);
                            if ($rotationScheduleDates != '') {
                                $scheduledates = explode(',', $rotationScheduleDates);
                                $scheduledatesArray["calendar_" . $rotationId] = $scheduledates;
                            }
                        }
                        if ($rotationStudentCount > 0) {
                ?>
                            <tr>
                                <td class="table-flex">
                                    <div>
                                        <?php echo ($title) . '<br><div><small><label title="' . $hospitalSite . '" style="margin-bottom: 0 !important;" >Hospital: ' . $hospitalSiteDispName . '</label></small></div>';

                                        if ($subratoncount > 0) {
                                        ?>
                                            <a title='View Hospital Sites' href="addsubrotation.html?rotationId=<?php echo (EncodeQueryData($rotationId)); ?>">Hospital Sites</a>
                                        <?php
                                        }
                                        if ($rotationLocation != '') {
                                            echo '<div><small><label title="' . $rotationLocation . '">Location: ' . $rotationLocation . '</label></small></div>';
                                        }
                                        ?>
                                    </div>
                                </td>
                                <td style="text-align: left">
                                    <?php echo $courseTitle; ?><br>
                                    <small>Location: </small><?php echo '<small>' . $locationTitle . '</small>'; ?>
                                    <?php if ($courseHours != '') { ?>
                                        <br><small>Required Hours: </small><?php echo '<small>' . $courseHours . 'Hrs </small>'; ?>
                                    <?php } ?>
                                </td>

                                <td class="position-relative">
                                    <div class="flex" style="padding-right: 20px;">
                                        <?php echo (date('m/d/Y', $startDateTimestamp)); ?>
                                    </div>
                                    <?php if ($isSchedule) { ?>
                                        <div class="calendar-container">
                                            <div class="hover-container" style="position: relative;">
                                                <button class="hover-trigger" style="cursor: pointer;">
                                                    <span class="glyphicon glyphicon-calendar"></span>
                                                </button>
                                                <div class="calendar display-none" style="min-width: 255px; position: absolute; z-index: 9999; background: #fff; border: 1px solid #ccc; padding: 10px;top: 2px;" id="calendar_<?php echo $rotationId; ?>" isScheduleDates='<?php echo $rotationScheduleDates; ?>'>
                                                </div>
                                            </div>
                                        </div>
                                    <?php } ?>

                                </td>
                                <td style="text-align: center">
                                    <?php echo (date('m/d/Y', $endDateTimestamp)); ?>
                                </td>
                                <td style="text-align: center">
                                    <small>From </small> <?php echo (date('h:i A', $startDateTimestamp)); ?>
                                    <small> To </small> <?php echo (date('h:i A', $endDateTimestamp)); ?>
                                </td>
                                <td style="text-align: center">
                                    <?php
                                    if ($rotationStudentCount > 0) { ?>
                                        <a title='View students' href="rotationstudents.html?rotationId=<?php echo (EncodeQueryData($rotationId)); ?>"><?php echo $rotationStudentCount; ?></a>
                                    <?php } else {
                                        echo '-NA-';
                                    } ?>
                                </td>
                                <td>
                                    <?php
                                    if ($subratoncount > 0) {
                                        if (strtotime($date_convert) <= strtotime($today)) {
                                            echo '-';
                                        } else {
                                            echo 'Rotation has not started';
                                        }
                                    } else {

                                        if (strtotime($date_convert) <= strtotime($today)) { ?>
                                            <a href="Attendance.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>">Attendance</a> |
                                            <a href="journal.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>">Daily Journal</a> |
                                            <a class="showactive  " href="interaction.html?rotationId=<?php echo (EncodeQueryData($rotationId)); ?>">Dr.Interaction</a> |
                                            <?php if ($isActiveCheckoff == 1) { ?>
                                                <a class="showactive  " href="checkoff.html?rotationId=<?php echo (EncodeQueryData($rotationId)); ?>&Type=R">Checkoff</a> |
                                            <?php } else if ($isActiveCheckoff == 2) { ?>
                                                <a class="showactive  " href="checkoffusaf.html?rotationId=<?php echo (EncodeQueryData($rotationId)); ?>&Type=R">Checkoff</a> |
                                            <?php } else { ?>
                                                <a href='checkoffs.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>&Type=R'>Checkoff</a> |
                                            <?php } ?>
                                            <a class="showactive  " href="dailyEvalList.html?rotationId=<?php echo (EncodeQueryData($rotationId)); ?>&Type=A">Daily/Weekly</a> |
                                            <a class="showactive  " href="incident.html?rotationId=<?php echo (EncodeQueryData($rotationId)); ?>">Incident</a> |
                                            <a href="formativelist.html?formativerotationid=<?php echo EncodeQueryData($rotationId); ?>">Formative</a>
                                            |<a href="midtermlist.html?midtermrotationid=<?php echo EncodeQueryData($rotationId); ?>">Midterm</a> |
                                            <a href="summativelist.html?summativerotationid=<?php echo EncodeQueryData($rotationId); ?>">Summative</a>|
                                            <a href="admincievaluationlist.html?admincievaluationrotationid=<?php echo EncodeQueryData($rotationId); ?>">DCE CI Evaluation</a>|

                                            <?php if ($isActiveCheckoff == 0) { ?>
                                                <a href="masteryList.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>">Mastery </a> |
                                            <?php } ?>

                                            <?php if ($isActiveCheckoff == 1) { ?>
                                                <a href="volunteerEvalList.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>">Volunteer</a> |
                                            <?php } ?>
                                            <?php if ($currentSchoolId == 75 || $currentSchoolId == 121) { ?>
                                                <a href="pefList.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>">PEF Evaluation</a> |
                                            <?php } ?>
                                            <?php if ($currentSchoolId == 75 || $currentSchoolId == 127) { ?>
                                                <a href="floorTherapyAndICUEvaluationlist.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>">Floor Therapy And ICU Evaluation</a> |
                                            <?php } ?>
                                            <a href="equipmentlist.html?equipmentrotationid=<?php echo EncodeQueryData($rotationId); ?>">Equipment</a>

                                            | <a href="procedurecounts.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>">Procedure Count</a>
                                             <?php if ($soapNote) { ?>
                                                | <a href="soapNoteList.html?type=soapnote&rotationId=<?php echo EncodeQueryData($rotationId) ?>">Soap Note</a> 
                                            <?php } ?>
                                           
                                        <?php
                                        } else { ?>
                                            <p>Rotation has not started</p>
                                    <?php }
                                        $calendarIds = array_keys($scheduledatesArray);
                                    } ?>
                                </td>

                            </tr>
                <?php
                        }
                    }
                }

                ?>
            </tbody>
        </table>
    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/fullcalendar/fullcalendar_main.js"></script>


    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        $('.addCommentpopup').magnificPopup({
            'type': 'ajax',
        });
        $(document).ready(function() {
            $(".select2_single").select2();
        });

        $(window).load(function() {
            $("#divTopLoading").addClass('hide');

            $("#fromDate").datetimepicker({
                format: "MM/DD/YYYY"
            });

            $("#toDate").datetimepicker({
                format: "MM/DD/YYYY"
            });

            $(".redirectUrl").change(function() {

                var selectedStudentId = $('#cboStudent').val();
                var hospitalsiteId = $('#cboHospitalsite').val();
                // var rotationId = $('#cborotation').val();
                var active = "<?php echo EncodeQueryData($isActiveHospital) ?>";
                // // console.log( selectedStudentId);
                Url = "rotations.html?active=" + active;
                if (selectedStudentId != '') {
                    Url = Url + "&studentId=" + selectedStudentId;
                }
                if (hospitalsiteId != '') {
                    Url = Url + "&hospitalsiteId=" + hospitalsiteId;
                }

                window.location.href = Url;

            });
        });

        var current_datatable = $("#datatable-responsive").DataTable({
            "ordering": true,
            "order": [
                [2, "desc"]
            ],

            "aoColumns": [{
                    "sWidth": "30%"
                }, {
                    "sWidth": "15%",
                    "sClass": "alignCenter"
                }, {
                    "sWidth": "10%",
                    "sClass": "alignCenter",
                    "bSortable": false
                }, {
                    "sWidth": "10%",
                    "sClass": "alignCenter",
                    "bSortable": false
                },
                {
                    "sWidth": "20%",
                    "sClass": "alignCenter",
                    "bSortable": false
                }, {
                    "sWidth": "10%",
                    "sClass": "alignCenter"
                }, {
                    "sWidth": "10%",
                    "sClass": "alignCenter",
                    "bSortable": false
                }
            ]
        });


        // Wait for the DOM content to be loaded before executing the script
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize the calendars for the first time
            initializeCalendars();

            // Reinitialize calendars after DataTable redraws
            $('#datatable-responsive').on('draw.dt', function() {
                // console.log('DataTable redrawn. Reinitializing calendars...');
                initializeCalendars();
            });
        });

        function initializeCalendars() {
            setTimeout(function() {
                // Find all elements with IDs starting with "calendar_"
                const calendarElements = document.querySelectorAll('[id^="calendar_"]');
                // console.log('Found calendars:', calendarElements.length);

                calendarElements.forEach(function(calendarEl) {
                    const id = calendarEl.id;
                    const dates = $('#' + id).attr('isScheduleDates');
                    // console.log(`Processing calendar: ${id} | Dates: ${dates}`);

                    // Destroy any existing calendar instance
                    if (calendarEl._fullCalendar) {
                        // console.log(`Destroying existing calendar for: ${id}`);
                        calendarEl._fullCalendar.destroy();
                    }

                    const eventData = [];

                    if (dates && dates.trim() !== '') {
                        const datesList = dates.split(',');

                        // Prepare event data
                        datesList.forEach(function(date) {
                            const parts = date.split('/');
                            const isoDate = `${parts[2]}-${parts[0].padStart(2, '0')}-${parts[1].padStart(2, '0')}`;
                            eventData.push({
                                start: isoDate
                            });
                        });
                    }

                    // Create a new FullCalendar instance
                    const calendar = new FullCalendar.Calendar(calendarEl, {
                        initialView: 'dayGridMonth',
                        events: eventData,
                        eventDisplay: 'background',
                        eventBackgroundColor: '#5cb85c',
                        selectable: false,
                        headerToolbar: {
                            start: 'prev,today',
                            center: 'title',
                            end: 'next'
                        },
                        titleFormat: {
                            month: 'short',
                            year: 'numeric'
                        },
                    });

                    calendar.render();
                    calendarEl._fullCalendar = calendar; // Store the calendar instance for cleanup
                });
            }, 100); // Slight delay to ensure all elements are ready
        }

        // Handle calendar visibility toggle
        $(document).on('click', '.hover-container', function(e) {
            e.stopPropagation(); // Prevent event bubbling to document

            // Find the specific calendar div within this hover-container
            const $calendar = $(this).find('.calendar');

            // Hide all other calendars
            $('.calendar').not($calendar).hide();

            // Toggle the visibility of the current calendar
            $calendar.toggle();
        });

        // Prevent hiding the calendar when clicking inside it
        $(document).on('click', '.calendar', function(e) {
            e.stopPropagation();
        });

        // Hide calendars when clicking outside any hover-container or calendar
        $(document).on('click', function() {
            $('.calendar').hide();
        });
    </script>


</body>

</html>