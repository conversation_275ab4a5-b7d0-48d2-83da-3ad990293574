$(document).on('change', '.docUploadLimit', function () {

    var fileExtension = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'pdf', 'PDF',
        'jpeg', 'jpg', 'png', 'gif', 'bmp', 'JPEG', 'JPG', 'PNG', 'GIF', 'BMP'
    ];
    if ($.inArray($(this).val().split('.').pop().toLowerCase(), fileExtension) == -1) {
        alert("Only formats are allowed : " + fileExtension.join(', '));
        $(this).val("");
        return false;
    }

    var selectedfile = this.files[0];
    var fileSize = selectedfile.size;
    if (fileSize > 1048576) {
        alert('Maximum allowed size for uploaded file 1MB.');
        $(this).val("");
        return false;
    }

});

$(document).on('change', '.imageUploadLimit', function () {

    var fileExtension = ['jpeg', 'jpg', 'png', 'gif', 'bmp', 'JPEG', 'JPG', 'PNG', 'GIF', 'BMP'];
    if ($.inArray($(this).val().split('.').pop().toLowerCase(), fileExtension) == -1) {
        alert("Only formats are allowed : " + fileExtension.join(', '));
        $(this).val("");
        return false;
    }

    var selectedfile = this.files[0];
    var fileSize = selectedfile.size;
    if (fileSize > 1048576) {
        alert('Maximum allowed size for uploaded file 1MB.');
        $(this).val("");
        return false;
    }

});

$(document).on('change', '.pdfUploadLimit', function () {

    var fileExtension = ['pdf', 'PDF'];
    if ($.inArray($(this).val().split('.').pop().toLowerCase(), fileExtension) == -1) {
        alert("Only formats are allowed : " + fileExtension.join(', '));
        $(this).val("");
        return false;
    }

    var selectedfile = this.files[0];
    var fileSize = selectedfile.size;
    if (fileSize > 10485760) {
        alert('Maximum allowed size for uploaded file 10MB.');
        $(this).val("");
        return false;
    }

});

$(document).on('change', '.videoUploadLimit', function () {

    var fileExtension = ['mp4', 'MP4'];
    if ($.inArray($(this).val().split('.').pop().toLowerCase(), fileExtension) == -1) {
        alert("Only formats are allowed : " + fileExtension.join(', '));
        $(this).val("");
        return false;
    }

    var selectedfile = this.files[0];
    var fileSize = selectedfile.size;
    if (fileSize > 524288000) {
        alert('Maximum allowed size for uploaded file 500MB.');
        $(this).val("");
        return false;
    }

});

function loadingText() {
    return 'Please wait...<i class="fa fa-circle-o-notch fa-spin" aria-hidden="true"></i>';
}

const inputFields = document.querySelectorAll('input');

inputFields.forEach((input) => {
    input.setAttribute('autocomplete', 'off');
});

$(window).load(function () {
    $("#green-loader").addClass('hide');
    $(".hidden-body").removeClass('hidden-body');
});


function loadScriptOnce(src) {
    if (!document.querySelector(`script[src="${src}"]`)) {
        const script = document.createElement("script");
        script.src = src;
        document.head.appendChild(script);
    }
}
