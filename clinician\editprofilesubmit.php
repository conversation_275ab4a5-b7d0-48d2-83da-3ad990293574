<?php

include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsClinician.php');
include('../class/clsSchool.php');
include('../setRequest.php');
include('../class/Zebra_Image.php');
include('../class/clsChatApp.php');


if (isset($_POST['btnSubmit'])) {
	$objClinician = new clsClinician();
	// Initialize Chat and Database objects
	$objChatApp = new clsChatApp();
	$objDB = new clsDB();

	$userId = $_SESSION['loggedClinicianId'];

	$firstName = $_POST['txtFirstName'];
	$lastName = $_POST['txtLastName'];
	$email = $_POST['txtEmail'];
	$userName = $_POST['txtUsername'];

	$objClinician->firstName = $firstName;
	$objClinician->lastName = $lastName;
	$objClinician->email = $email;
	$objClinician->username = $userName;
	$objClinician->updatedBy = $userId;
	$objClinician->updatedDate = date('Y-m-d h:i:s');
	$retUserId = $objClinician->UpdateClinicianProfile($userId);

	if ($retUserId > 0) {

		//Set Update Session
		$_SESSION["loggedClinicianName"] = $userName;
		$_SESSION["loggedClinicianFirstName"] = $firstName;
		$_SESSION["loggedClinicianLastName"] = $lastName;

		$role_id = 3;
		// $retUserId = $userId;
		$profileImagePath = '';
		$address1 ='';

		
		// Fetch user role management ID and profile image name from the database
		$userRoleManagementId = $objDB->GetSingleColumnValueFromTable('userrolemanagement', 'id', 'userId', $retUserId, 'role_Id', $role_id);
		$phone = $objDB->GetSingleColumnValueFromTable('clinician', 'phone', 'clinicianId', $retUserId, 'schoolId', $currentSchoolId);
		$userRoleManagementId = $userRoleManagementId ?: 0;

		// Define a common function to set ChatApp user properties and save the user data
		$retUserRoleManagementId = $objChatApp->setAndSaveChatAppUser($objChatApp, $retUserId, $firstName, $lastName, $email, $phone, $address1, $profileImagePath, $currenschoolDisplayname, $currentSchoolId, $role_id, $userRoleManagementId);

		if ($retUserRoleManagementId) {
			$objChatApp->prepareAndSendUserData($objChatApp, $retUserId, $firstName, $lastName, $email, $phone, $profileImagePath, $address1, $role_id, $currenschoolDisplayname, $currentSchoolId, $userRoleManagementId);
		}

		// Check if we have a cropped image from the new cropper
		$coverImage = isset($_POST['fileLogo']) ? $_POST['fileLogo'] : '';
		if($coverImage != '') {
			$ext = getFileExtensionFromBase64($coverImage);
			$image_array_1 = explode(";", $coverImage);
			$image_array_2 = explode(",", $image_array_1[1]);
			$coverImage = base64_decode($image_array_2[1]);

			// File Name
			$smallFilename = 'PROFILE_SMALL_' . $retUserId . '.' . $ext;
			$largeFilename = 'PROFILE_LARGE_' . $retUserId . '.' . $ext;
			
			// Check User Directory
			$uploaddir = "../upload/schools/" . $currentSchoolId;
			if (!file_exists($uploaddir)) {
				mkdir($uploaddir);
			}

			$uploaddir .= "/clinician";
			if (!file_exists($uploaddir)) {
				mkdir($uploaddir);
			}

			$uploaddir .= "/" . $retUserId . "/";
			if (!file_exists($uploaddir)) {
				mkdir($uploaddir);
			}

			// Save small image
			$UploadSmallFilePath = $uploaddir . $smallFilename;
			file_put_contents($UploadSmallFilePath, $coverImage);
			
			// Save large image
			$UploadLargeFilePath = $uploaddir . $largeFilename;
			file_put_contents($UploadLargeFilePath, $coverImage);
			
			// Update File Name to DB
			$objClinician->UpdateClinicianPhotosFileName($retUserId, $smallFilename, $largeFilename);
			
			// Update profile image path
			$profileImageName = $objDB->GetSingleColumnValueFromTable('clinician', 'profilePic', 'clinicianId', $retUserId);
			if (!empty($profileImageName)) {
				$profileImagePath = BASE_PATH . '/upload/schools/' . $currentSchoolId . '/clinician/' . $retUserId . '/' . $profileImageName . '?id=' . rand(1, 10000);
				$objDB->UpdateSingleColumnValueToTable('userrolemanagement', 'profileImagePath', $profileImagePath, 'id', $userRoleManagementId);
				$objChatApp->UpdateUserProfilePicTochat($profileImagePath, $retUserId, $role_id);
			}
			
			// Set session variables for profile images
			$_SESSION["loggedClinicianProfileImagePath"] = GetClinicianImagePath($retUserId, $currentSchoolId, $smallFilename);
			$_SESSION["loggedClinicianProfileLargeImagePath"] = GetClinicianImagePath($retUserId, $currentSchoolId, $largeFilename);
			
			
			
			header('location:editprofile.html?status=Updated');
		}
		//Here we are going to upload logo using traditional file upload if no cropped image
		else if (isset($_FILES['filePhoto'])) {
			$Image = $_FILES['filePhoto']['name'];
			if ($Image) {

				$ext = strtolower(pathinfo($_FILES['filePhoto']['name'], PATHINFO_EXTENSION));
				if ($ext != "png" && $ext != "jpg" && $ext != "jpeg" && $ext != "gif") {
					header('location:editprofile.html?status=InvalidFile');
					exit();
				}

				//Check User Directory


				$uploaddir = "../upload/schools/" . $currentSchoolId;
				if (!file_exists($uploaddir)) {
					mkdir($uploaddir);
				}

				$uploaddir .= "/clinician";
				if (!file_exists($uploaddir)) {
					mkdir($uploaddir);
				}

				$uploaddir .= "/" . $retUserId . "/";

				if (!file_exists($uploaddir)) {
					mkdir($uploaddir);
				}


				//Save SMALL Path
				$smallFilename = 'PROFILE_SMALL_' . $retUserId . '.' . $ext;
				$UploadSmallFilePath = $uploaddir . $smallFilename;
				copy($_FILES['filePhoto']['tmp_name'], $UploadSmallFilePath);


				//Save LARGE Path
				$largeFilename = 'PROFILE_LARGE_' . $retUserId . '.' . $ext;
				$UploadLargeFilePath = $uploaddir . $largeFilename;
				copy($_FILES['filePhoto']['tmp_name'], $UploadLargeFilePath);
				if (isset($_POST['chkAutoCrop'])) {
					$image = new Zebra_Image();
					$image->source_path = $UploadSmallFilePath;
					$image->target_path = $UploadSmallFilePath;
					$image->jpeg_quality = 85;
					$image->preserve_aspect_ratio = true;
					$image->enlarge_smaller_images = true;
					$image->preserve_time = true;
					$image->resize(50, 50, ZEBRA_IMAGE_CROP_CENTER, '-1');
					unset($image);
					//-----------------------------------
					//Crop and Resize Image
					//-----------------------------------
					$image = new Zebra_Image();
					$image->source_path = $UploadLargeFilePath;
					$image->target_path = $UploadLargeFilePath;
					$image->jpeg_quality = 85;
					$image->preserve_aspect_ratio = true;
					$image->enlarge_smaller_images = true;
					$image->preserve_time = true;
					$image->resize(200, 200, ZEBRA_IMAGE_CROP_CENTER, '-1');
					unset($image);
					//-----------------------------------
					$_SESSION["loggedClinicianProfileImagePath"] = $defaultProfileImagePath = GetClinicianImagePath($retUserId, $currentSchoolId, $smallFilename);
					$_SESSION["loggedClinicianProfileLargeImagePath"] = GetClinicianImagePath($retUserId, $currentSchoolId, $largeFilename);
				}
				//Update File Name to DB
				//-----------------------------------
				$objClinician->UpdateClinicianPhotosFileName($retUserId, $smallFilename, $largeFilename);
				//-----------------------------------
				$profileImageName = $objDB->GetSingleColumnValueFromTable('clinician', 'profilePic', 'clinicianId', $retUserId);
				// echo $profileImageName;exit;
				// Check if profile image exists, then set its path
				if (!empty($profileImageName)) {
					$profileImagePath = BASE_PATH . '/upload/schools/' . $currentSchoolId . '/clinician/' . $retUserId . '/' . $profileImageName . '?id=' . rand(1, 10000);
					$objDB->UpdateSingleColumnValueToTable('userrolemanagement', 'profileImagePath', $profileImagePath, 'id', $userRoleManagementId);
					$objChatApp->UpdateUserProfilePicTochat($profileImagePath, $retUserId, $role_id);
				}
			}
		}	

			//Audit Log Start
			// Instantiate the Logger class
			$objLog = new clsLogger();

			// Determine the action type (EDIT or ADD) based on the presence of a journal ID
			$action = $objLog::EDIT;
			$type = 'Profile';
			$userType = isset($_SESSION['loggedUserId']) ?  $objLog::ADMIN : $objLog::CLINICIAN; // User type is set to ADMIN
			$backUserId = isset($_SESSION['loggedUserId']) ? $_SESSION['loggedUserId'] : 0;
			$IsMobile = 0;

			$objClinician = new clsClinician();
			$objClinician->saveClinicianAuditLog($retUserId, $_SESSION['loggedClinicianId'], $userType, $action, $IsMobile,0,$type,$backUserId);

			unset($objLog);
			//Audit Log End
			
			unset($objClinician);
			header('location:editprofile.html?status=Updated');
		
	} else {
		header('location:editprofile.html?status=Error');
	}
} else {
	header('location:editprofile.html');
}

// Function to get file extension from base64 string
// function getFileExtensionFromBase64($base64String) {
//     // Extract the MIME type from the base64 string
//     $mime = explode(';', $base64String)[0];
//     $mime = explode(':', $mime)[1];
    
//     // Map MIME types to file extensions
//     $mimeToExt = [
//         'image/jpeg' => 'jpg',
//         'image/jpg' => 'jpg',
//         'image/png' => 'png',
//         'image/gif' => 'gif',
//         'image/webp' => 'webp'
//     ];
    
//     return isset($mimeToExt[$mime]) ? $mimeToExt[$mime] : 'jpg';
// }
