<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php');     
    include('../class/clsMasteryEval.php');     

	include('../setRequest.php'); 

    $schoolId = 0;	
  
	$schoolId= $currentSchoolId;
	$title ="Add Site Evaluation Section - ".$currenschoolDisplayname;   
    $page_title ="Add Site Evaluation Section";
    $schoolSectionId = 0;
    $title = '';
    $sortOrder  = '';
	$TopicTitleId ='';
    $bedCrumTitle = 'Add';

    $sortOrder = '';
    $isActiveCheckoff=$_SESSION['isActiveCheckoff'];
    
    if(isset($_GET['editid'])) //Edit Mode
	{
        $schoolSectionId = DecodeQueryData($_GET['editid']);
	    $page_title ="Edit Site Evaluation Section";
        $bedCrumTitle = 'Edit';

        //For Checkoff Topic Details
         $objMasteryEval = new clsMasteryEval();
        
        if($isCurrentSchoolSuperAdmin == 1) // For superadmin
		    $row = $objMasteryEval->GetDefaultMasteryEvaluationSectionDetail($schoolSectionId);
        else
		    $row = $objMasteryEval->GetMasterySchoolEvaluationSectionDetail($schoolSectionId);
        unset($objMasteryEval);
       
        $title  = stripslashes($row['title']); 
        $sortOrder  = stripslashes($row['sortOrder']);    
           
    }
?>    
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo($page_title); ?></title>
    <?php include('includes/headercss.php');?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
	<link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
    <link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">

</head>

<body>
    <?php include('includes/header.php');?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
					 <li><a href="settings.html">Settings</a></li>                   
                    <li><a href="masteryEvaluationSectionList.html">Mastery Evaluation Section</a></li>
                    <li class="active"><?php echo($bedCrumTitle);?></li>
                </ol>
            </div>

        </div>
    </div>

    <div class="container">

         <form id="frmcheckoff" data-parsley-validate class="form-horizontal" method="POST" action="addmasteryEvaluationSectionSubmit.html?editid=<?php echo(EncodeQueryData($schoolSectionId)); ?>" >

            <div class="row">
                <div class="col-md-6">

                    <!-- Text input-->
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtsection"> Section</label>
                        <div class="col-md-12">
                            <input  id="txtsection"  name="txtsection" value="<?php echo($title); ?>"  type="text"  class="form-control input-md required-input" required>

                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtsortorder">Section Number</label>
                        <div class="col-md-12">
                            <input  id="txtsortorder"  name="txtsortorder" value="<?php echo($sortOrder); ?>"  type="text"  class="form-control input-md required-input" required>

                        </div>
                    </div>
					
                </div>
            </div>
            <div class="form-group">
            <!-- <label class="col-md-2 control-label"></label> -->
            <div class="col-md-12" style="display: flex; justify-content: center; margin: 20px 0;gap: 15px;">
            <button id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
                <a type="button" href="masteryEvaluationSectionList.html" class="btn btn-default">Cancel</a>
            </div>
            </div>
        </form>

    </div>

    <?php include('includes/footer.php');?>
   
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
	<script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
	<script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
	<script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="https://cdn.ckeditor.com/ckeditor5/35.0.1/classic/ckeditor.js"></script>
    <script type="text/javascript">
        $(".select2_tags").select2({'placeholder':'Select'}); //for multiple selection
        CKEDITOR.replace('txtdescription');	
        $(window).load(function(){

             $('#frmcheckoff').parsley().on('field:validated', function() {
			    var ok = $('.parsley-error').length === 0;
            })
            .on('form:submit', function() {
                ShowProgressAnimation();									  
                return true; 
            });
		
        });
    </script>

</body>

</html>