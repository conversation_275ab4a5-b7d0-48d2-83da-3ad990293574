<?php
/**
 * InboundSMSRuleTest
 *
 * PHP version 5
 *
 * @category Class
 * @package  ClickSend
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * ClickSend v3 API
 *
 * This is an official SDK for [ClickSend](https://clicksend.com)  Below you will find a current list of the available methods for clicksend.  *NOTE: You will need to create a free account to use the API. You can register [here](https://dashboard.clicksend.com/#/signup/step1/)..*
 *
 * OpenAPI spec version: 3.1
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * ClickSend Codegen version: 3.0.51
 */
/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Please update the test case below to test the model.
 */

namespace ClickSend;

use PHPUnit\Framework\TestCase;

/**
 * InboundSMSRuleTest Class Doc Comment
 *
 * @category    Class
 * @description Model for Inbound SMS Rules
 * @package     ClickSend
 * <AUTHOR> Codegen team
 * @link        https://github.com/swagger-api/swagger-codegen
 */
class InboundSMSRuleTest extends TestCase
{

    /**
     * Setup before running any test case
     */
    public static function setUpBeforeClass(): void
    {
    }

    /**
     * Setup before running each test case
     */
    public function setUp(): void
    {
    }

    /**
     * Clean up after running each test case
     */
    public function tearDown(): void
    {
    }

    /**
     * Clean up after running all test cases
     */
    public static function tearDownAfterClass(): void
    {
    }

    /**
     * Test "InboundSMSRule"
     */
    public function testInboundSMSRule()
    {
    }

    /**
     * Test attribute "dedicated_number"
     */
    public function testPropertyDedicatedNumber()
    {
    }

    /**
     * Test attribute "rule_name"
     */
    public function testPropertyRuleName()
    {
    }

    /**
     * Test attribute "message_search_type"
     */
    public function testPropertyMessageSearchType()
    {
    }

    /**
     * Test attribute "message_search_term"
     */
    public function testPropertyMessageSearchTerm()
    {
    }

    /**
     * Test attribute "action"
     */
    public function testPropertyAction()
    {
    }

    /**
     * Test attribute "action_address"
     */
    public function testPropertyActionAddress()
    {
    }

    /**
     * Test attribute "enabled"
     */
    public function testPropertyEnabled()
    {
    }

    /**
     * Test attribute "webhook_type"
     */
    public function testPropertyWebhookType()
    {
    }
}
