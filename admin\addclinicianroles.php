<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsClinicianRoleMaster.php');
include('../setRequest.php');

$schoolId = 0;
if (isset($_GET['schoolId'])) //Edit Mode
{
    $schoolId = $_GET['schoolId'];
    $schoolId = DecodeQueryData($schoolId);
} else {
    $schoolId = $currentSchoolId;
    $title = "Add Clinician Role - " . $currenschoolDisplayname;
}

//Soap Note Module - Enabled/Disabled through superadmin -> admin 
$objDB = new clsDB();
$soapNote = $objDB->GetSingleColumnValueFromTable('schoolsettings', 'status', 'schoolId', $schoolId, 'type', 'soapNote');
unset($objDB);

$page_title = "Add Clinician Role";
$clinicianRoleId = 0;
$title = '';
$sortOrder = '';
$Roletype = '';
$bedCrumTitle = 'Add';
$isReportAccess  = 0;
if (isset($_GET['id'])) //Edit Mode
{
    $clinicianRoleId = DecodeQueryData($_GET['id']);
    $page_title = "Edit Clinician Role";
    $bedCrumTitle = 'Edit';

    //For Clinician Role Details
    $objClinicianRoleMaster = new clsClinicianRoleMaster();
    $row = $objClinicianRoleMaster->GetClinicianRoleDetails($clinicianRoleId, $schoolId);

    //For Clinician Reports
    $rsClinicianReports = $objClinicianRoleMaster->GetClinicianReports($clinicianRoleId, $schoolId);

    unset($objClinicianRoleMaster);
    if ($row == '') {
        header('location:addclinicianroles.html');
        exit;
    }

    $title  = stripslashes($row['title']);
    $sortOrder  = stripslashes($row['sortOrder']);
    $Roletype  = stripslashes($row['type']);
    $isReportAccess  = stripslashes($row['isReportAccess']);
}

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($page_title); ?></title>
    <?php include('includes/headercss.php'); ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <style>
        .form-control {
            height: 45px;
        }

        .select2-container--default.select2-container--disabled .select2-selection--multiple {
            background-color: #eee;
            cursor: default;
            height: fit-content;
            min-height: 45px;
            border: none;
            border-radius: 12px;
        }

        input:read-only {
            background-color: transparent !important;
        }

        .select2-container--default.select2-container--disabled .select2-selection--multiple {
            background-color: #eee !important;
            cursor: default;
        }

        .select2-container--default .select2-selection--multiple {
            min-height: 45px;
            background-color: #f6f9f9 !important;
            border-radius: 12px !important;
            box-shadow: none !important;
            border: none !important;
        }

        .select2-container--default .select2-selection--multiple .select2-selection__rendered li {
            text-wrap: wrap;
        }

        .select2-hidden-accessible~.select2-container--disabled {
            min-height: 45px !important;
            height: fit-content !important;
        }
    </style>
</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="settings.html?id=<?php echo (EncodeQueryData($currentSchoolId)); ?>">Settings</a></li>
                    <li><a href="viewclinicianroles.html?schoolId=<?php echo (EncodeQueryData($currentSchoolId)); ?>">Clinician Roles</a></li>
                    <li class="active"><?php echo ($bedCrumTitle); ?></li>
                </ol>
            </div>

        </div>
    </div>

    <div class="container">

        <form id="frmClinicianRole" data-parsley-validate class="form-horizontal" method="POST" action="addclinicianrolesubmit.html?id=<?php echo (EncodeQueryData($clinicianRoleId)); ?>">

            <div class="row">
                <div class="col-md-6">
                    <!-- Text input-->
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtRole">Role</label>
                        <div class="col-md-12">
                            <input id="txtRole" name="txtRole" value="<?php echo ($title); ?>" required type="text" placeholder="" class="form-control input-md required-input" required="">

                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtRoletype">Role Type</label>
                        <div class="col-md-12">
                            <input id="txtRoletype" name="txtRoletype" value="<?php echo ($Roletype); ?>" required type="text" placeholder="e.g.Faculty : F, Preceptor  : P" class="form-control input-md required-input" required="">

                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtOrder">Sort Order</label>
                        <div class="col-md-12">
                            <input id="txtOrder" name="txtOrder" value="<?php echo ($sortOrder); ?>" required type="text" placeholder="" class="form-control input-md required-input" required="">

                        </div>
                    </div>
                </div>
                <div class="col-md-6"></div>
            </div>
            <div class="row">
                <div class="col-md-6 px-0">
                    <div class="col-md-12 mt-5">
                        <div class="form-group m-0" style="margin-bottom: 0;">
                            <input type="checkbox" name="isReportAccess" id="isReportAccess" value="1" <?php if ($isReportAccess > 0) {
                                                                                                            echo 'checked';
                                                                                                        } ?>>
                            <label class="control-label margin_left_two" for="txtOrder">Ability to show reports</label>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 px-0">
                    <input type="hidden" name="hiddenReportValues" id="hiddenReportValues" value="">
                    <input type="hidden" name="hiddenReportTexts" id="hiddenReportTexts" value="">
                    <input type="hidden" name="hiddenReportTypesValue" id="hiddenReportTypesValue" value="<?php echo $rsClinicianReports; ?>">
                    <?php if ($isClinicianReports) { ?>
                        <!-- Text input-->
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="cboreporttype">Report Type</label>
                            <div class="col-md-12">
                                <select id="cboreporttype" name="cboreporttype[]" class="form-control input-md select2_single input-required" multiple data-parsley-errors-container="#error-cboreporttype" required>
                                    <!-- <option value="">Select Report</option> -->
                                    <!-- <option value="Absence">Absence</option>
                                    <option value="Accreditation" id="Accreditation">Accreditation Tracking Information</option>
                                    <option value="AdditionalContactInformation">Additional Contact Information</option> -->
                                    <option value="Attendance">Attendance Detail</option>
                                    <option value="Attendance_Daily_Weekly">Attendance Daily/Weekly</option>
                                    <option value="AttendanceSummary">Attendance Summary</option>
                                    <!-- <option value="Certification">Certification Log</option> -->
                                    <option value="CaseStudy">Case Study Report</option>
                                    <option value="Checkoff">Checkoff</option>
                                    <option value="CheckoffByCourses">Checkoff by Courses</option>
                                    <!-- <option value="CI_Eval">CI Evaluation</option>
                                    <option value="ClinicalSiteUnit">Clinical Site Unit</option> -->
                                    <option value="DailyEval">Daily/Weekly Evaluation</option>
                                    <option value="DailyDetail">Daily/Weekly Detail Report</option>
                                    <option value="Dr_Points">Dr.Interaction</option>
                                    <option value="Early_clockOut">Early Clock Out Report</option>
                                    <!-- <option value="Equipments">Equipments</option>
                                    <option value="Formative">Formative Evaluation</option> -->
                                    <option value="Journal">Journal</option>
                                    <option value="No_clockout">No Clock Out Report</option>
                                    <?php if ($soapNote) { ?>
                                        <option value="SoapNote" id="SoapNote">Soap Note</option>
                                    <?php } ?>
                                    <!-- <option value="Late_clockIn">Late Clock In Report</option> -->
                                    <!-- <option value="Midterm">Midterm Evaluation</option>
                                    <option value="Mastery">Mastery Evaluation</option>
                                    <option value="P_Eval">P Evaluation</option>
                                    <option value="Preceptor_Checkoff">Preceptor Checkoff Report</option>
                                    <option value="Procidure_Count">Procedure Count</option>
                                    <option value="Procidure_Count_Summary">Procedure Count Summary</option>
                                    <?php //if ($schoolId == 75 || $schoolId == 123) { 
                                    ?>
                                        <option value="PEF_I_Evaluation">PEF I Evaluation</option>
                                        <option value="PEF_II_Evaluation">PEF II Evaluation</option>
                                    <?php //} 
                                    ?>
                                    <option value="Procedure_Details">Procedure Details</option>
                                    <option value="Site_Eval">Site Evaluation</option>
                                    <option value="Student">Student </option>
                                    <option value="StudentDetails">Student Portfolio</option>
                                    <option value="Summative">Summative Evaluation</option>
                                    <option value="Time_Exception">Time Exception</option> -->
                                    <!--option value="IRR Report">IRR Report</option-->
                                </select>
                                <div id="error-cboreporttype"></div>
                            </div>


                        </div>
                    <?php } ?>

                    <!---/// End 1st dd ///-------->
                    <!---/// End 2nd dd ///-------->
                </div>
            </div>

            <div class="form-group">
                <!-- <label class="col-md-2 control-label"></label> -->
                <div class="col-md-12" style="display: flex; justify-content: center;margin: 20px 0;gap: 15px;">

                    <button id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
                    <a href="viewclinicianroles.html" id="btnCancel" name="btnCancel" class="btn btn-default">Cancel</a>

                </div>
            </div>
        </form>


    </div>

    <?php include('includes/footer.php'); ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script type="text/javascript">
        $(document).ready(function() {
            $('#isReportAccess').change(function() {
                if ($(this).is(':checked')) {
                    $('#cboreporttype').prop('disabled', false)
                    $('#cboreporttype').prop('required', true)
                } else {
                    $('#cboreporttype').prop('disabled', true)
                    $('#cboreporttype').prop('required', false)
                }
            });

            $('#isReportAccess').trigger('change');
        });

        $(window).load(function() {

            $(".select2_single").select2();
            $('#select2-cboreporttype-container').addClass('required-select2');
            $(".select2_single").select2({
                'placeholder': 'Select Reports'
            });

            function test() {
                const selectElement = $('#cboreporttype');
                const selectedValues = selectElement.val();
                const selectedTexts = [];
                $.each(selectedValues, function(index, value) {
                    selectedTexts.push(selectElement.find('option[value="' + value + '"]').text());
                });
                // var selectedValues = $('select').val();
                // var selectedTexts = $('select option:selected').map(function() {
                // return $(this).text();
                // }).get();

                // const hiddenReportValues = selectedValues.reduce((acc, current, index) => {
                // return [...acc, [current, selectedTexts[index]]]
                // }, [])
                console.log(selectedTexts);
                $('#hiddenReportValues').val(selectedValues);
                $('#hiddenReportTexts').val(selectedTexts);

            }


            var select2Control = $('#cboreporttype');
            // Next, set the values you want to select as an array
            var selectedValues = $('#hiddenReportTypesValue').val();
            if (selectedValues != '') {
                selectedValues = selectedValues.split(",");
                // Finally, use the .val() method to set the selected values for the select2 control
                select2Control.val(selectedValues).trigger('change');
            }







            $('#frmClinicianRole').parsley().on('field:validated', function() {
                    var ok = $('.parsley-error').length === 0;
                })
                .on('form:submit', function() {
                    test();
                    ShowProgressAnimation();
                    return true; // Don't submit form for this demo
                });
        });
    </script>
</body>

</html>