<?php

include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../class/clsSystemUser.php');
include('../class/clsSchool.php');
include('../class/clsFormative.php');
include('../class/clsMidterm.php');
include('../class/clsSiteevaluation.php');
include('../class/clsCIevaluation.php');
include('../class/clsPEvaluation.php');
include('../class/clsAdminCIevaluation.php');
include('../class/clsSummative.php');
include('../class/clsPEF.php');
include('../class/clsFloorTherapyAndICUEvaluation.php');
include('../includes/commonfun.php');
include('../setRequest.php');


if (isset($_GET['id'])) //Check for Updates Status
{
	$sectionMasterId = DecodeQueryData($_GET['id']);
	$type = $_GET['type'];
	$isEvalType = $_GET['isEvalType'];
	if ($type == 'status') {
		if ($isEvalType == 'F') {
			//Create object
			$objFormative = new clsFormative();
			$newStatus = $_GET['newStatus'];
			if ($isCurrentSchoolSuperAdmin == 1) // For superadmin
				$objFormative->SetDefaultFormativeEvaluationStatus($sectionMasterId, $newStatus);
			else // For School Admin 
				$objFormative->SetFormativeEvaluationStatus($sectionMasterId, $newStatus);
			unset($objFormative);

			//Audit Log Start
			// Instantiate the Logger class
			$objLog = new clsLogger();

			// Determine the action type (EDIT or ADD) based on the presence of a journal ID
			$logAction = ($newStatus == 0) ? $objLog::INACTIVE : $objLog::ACTIVE;
			$userId = isset($_GET['userId']) ? ($_GET['userId']) : 0;
			$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;
			$type = "section";
			$isSuperAdmin = ($isCurrentSchoolSuperAdmin == 1) ? 1 : 0;
			$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN; // User type is set to Admin
			$objFormative = new clsFormative();
			$objFormative->saveFormativeAuditLog($sectionMasterId, $sectionMasterId, $userId, $userType, $logAction, $IsMobile = 0, $type, $isSuperAdmin);

			unset($objFormative);
			unset($objLog);


			if ($isCurrentSchoolSuperAdmin == 1) // For superadmin
				header('location:formativeEvaluationSectionList.html?status=StatusUpdated');
			else // For School Admin 
				header('location:formativeEvaluationSectionList.html?status=StatusUpdated&schoolId=' . $_GET['schoolId'] . '');
			exit();
		}
		if ($isEvalType == 'M') {
			//Create object
			$objMidterm = new clsMidterm();
			$newStatus = $_GET['newStatus'];
			if ($isCurrentSchoolSuperAdmin == 1) // For superadmin
				$objMidterm->SetDefaultMidtermEvaluationStatus($sectionMasterId, $newStatus);
			else // For School Admin
				$objMidterm->SetMidtermEvaluationStatus($sectionMasterId, $newStatus);

			unset($objMidterm);

			//Audit Log Start
			// Instantiate the Logger class
			$objLog = new clsLogger();

			// Determine the action type (EDIT or ADD) based on the presence of a journal ID
			$logAction = ($newStatus == 0) ? $objLog::INACTIVE : $objLog::ACTIVE;
			$userId = isset($_GET['userId']) ? ($_GET['userId']) : 0;
			$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;
			$type = "section";
			$isSuperAdmin = ($isCurrentSchoolSuperAdmin == 1) ? 1 : 0;
			$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN; // User type is set to Admin
			$objMidterm = new clsMidterm();
			$objMidterm->saveMidtermEvalAuditLog($sectionMasterId, $sectionMasterId, $userId, $userType, $logAction, $IsMobile = 0, $type, $isSuperAdmin);
			unset($objMidterm);

			unset($objLog);
			//Audit Log End

			header('location:midtermEvaluationSectionList.html?status=StatusUpdated&schoolId=' . $_GET['schoolId'] . '');
			exit();
		}
		if ($isEvalType == 'site') {
			//Create object
			$objSiteevaluation = new clsSiteevaluation();
			$objDB = new clsDB();
			$newStatus = $_GET['newStatus'];

			if ($isCurrentSchoolSuperAdmin == 1) // For superadmin
			{
				$objDB->UpdateSingleColumnValueToTable('defaultsitesectionmaster', 'isActive', $newStatus, 'sectionMasterId', $sectionMasterId);
				// header('location:siteEvaluationSectionList.html?status=StatusUpdated');s
			} else {
				$objSiteevaluation->SetSiteEvaluationStatus($sectionMasterId, $newStatus);
				// header('location:siteEvaluationSectionList.html?status=StatusUpdated&schoolId=' . $_GET['schoolId'] . '');
			}

			//Audit Log Start
			// Instantiate the Logger class
			$objLog = new clsLogger();

			// Determine the action type (EDIT or ADD) based on the presence of a journal ID
			$logAction = ($newStatus == 0) ? $objLog::INACTIVE : $objLog::ACTIVE;
			$userId = isset($_GET['userId']) ? ($_GET['userId']) : 0;
			$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;
			$type = "section";
			$isSuperAdmin = ($isCurrentSchoolSuperAdmin == 1) ? 1 : 0;
			$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN; // User type is set to Admin
			$objSiteevaluation = new clsSiteevaluation();
			$objSiteevaluation->saveSiteEvalAuditLog($sectionMasterId, $sectionMasterId, $userId, $userType, $logAction, $IsMobile = 0, $type, $isSuperAdmin);

			unset($objSiteevaluation);
			unset($objLog);


			if ($isCurrentSchoolSuperAdmin == 1) // For superadmin
				header('location:siteEvaluationSectionList.html?status=StatusUpdated');
			else
				header('location:siteEvaluationSectionList.html?status=StatusUpdated&schoolId=' . $_GET['schoolId'] . '');
			exit();
		}
		if ($isEvalType == 'cie') {

			//Create object
			$objCIevaluation = new clsCIevaluation();
			$newStatus = $_GET['newStatus'];
			if ($isCurrentSchoolSuperAdmin == 1) // For superadmin
				$objCIevaluation->SetDefaultCIEvaluationStatus($sectionMasterId, $newStatus);
			else // For School Admin 
				$objCIevaluation->SetCleEvaluationStatus($sectionMasterId, $newStatus);


			$userId = isset($_GET['userId']) ? ($_GET['userId']) : 0;
			$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

			// $isUser 1 for Admin and 2 for Clinician and 3 for Student
			// Instantiate the Logger class
			$objLog = new clsLogger();
			$logAction = ($newStatus == 0) ? $objLog::INACTIVE : $objLog::ACTIVE;
			$type = "section";
			$isSuperAdmin = ($isCurrentSchoolSuperAdmin == 1) ? 1 : 0;
			$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN; // User type is set to Admin

			$objCIevaluation->saveCIEvaluationAuditLog($sectionMasterId, $userId, $userType, $logAction, 0, $type, $isSuperAdmin);

			unset($objCIevaluation);

			header('location:ciEvaluationSectionList.html?status=StatusUpdated&schoolId=' . $_GET['schoolId'] . '');
			exit();
		}
		if ($isEvalType == 'sum') {
			//Create object
			$objSummative = new clsSummative();
			$objDB = new clsDB();

			$newStatus = $_GET['newStatus'];
			if ($isCurrentSchoolSuperAdmin == 1) // For superadmin
			{
				$objDB->UpdateSingleColumnValueToTable('defaultsummativesectionmaster', 'isActive', $newStatus, 'sectionMasterId', $sectionMasterId);
			} else {
				$objSummative->SetSummativeEvaluationStatus($sectionMasterId, $newStatus);
			}

			//Audit Log Start
			// Instantiate the Logger class
			$objLog = new clsLogger();

			// Determine the action type (EDIT or ADD) based on the presence of a journal ID
			$logAction = ($newStatus == 0) ? $objLog::INACTIVE : $objLog::ACTIVE;
			$userId = isset($_GET['userId']) ? ($_GET['userId']) : 0;
			$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;
			$type = "section";
			$isSuperAdmin = ($isCurrentSchoolSuperAdmin == 1) ? 1 : 0;
			$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN; // User type is set to Admin		
			$objSummative->saveSummativeEvalAuditLog($sectionMasterId, $sectionMasterId, $userId, $userType, $logAction, $IsMobile = 0, $type, $isSuperAdmin);
			unset($objSummative);
			unset($objDB);
			//Audit Log End
			header('location:summativeEvaluationSectionList.html?status=StatusUpdated&schoolId=' . $_GET['schoolId'] . '');
			exit();
		}
		if ($isEvalType == 'dceci') {

			//Create object
			$objCIevaluation = new clsAdminCIevaluation();
			$newStatus = $_GET['newStatus'];
			if ($isCurrentSchoolSuperAdmin == 1) // For superadmin
				$objCIevaluation->SetDefaultCIEvaluationStatus($sectionMasterId, $newStatus);
			else // For School Admin 
				$objCIevaluation->SetCleEvaluationStatus($sectionMasterId, $newStatus);

			//Audit Log Start
			// Instantiate the Logger class
			$objLog = new clsLogger();

			// Determine the action type (EDIT or ADD) based on the presence of a journal ID
			$logAction = ($newStatus == 0) ? $objLog::INACTIVE : $objLog::ACTIVE;
			$userId = isset($_GET['userId']) ? ($_GET['userId']) : 0;
			$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;
			$type = "section";
			$isSuperAdmin = ($isCurrentSchoolSuperAdmin == 1) ? 1 : 0;
			$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN; // User type is set to Admin
			$objCIevaluation->saveDCECIEvaluationAuditLog($sectionMasterId, $userId, $userType, $logAction, $IsMobile = 0, $type, $isSuperAdmin);
			unset($objCIevaluation);
			unset($objDB);
			//Audit Log End
			header('location:adminciEvaluationSectionList.html?status=StatusUpdated&schoolId=' . $_GET['schoolId'] . '');
			exit();
		}
		if ($isEvalType == 'pEval') {
			//Create object
			$objPevaluation = new clsPEvaluation();
			$objDB = new clsDB();
			$newStatus = $_GET['newStatus'];

			if ($isCurrentSchoolSuperAdmin == 1) // For superadmin
			{
				$objDB->UpdateSingleColumnValueToTable('defaultpsectionmaster', 'isActive', $newStatus, 'sectionMasterId', $sectionMasterId);
				// header('location:siteEvaluationSectionList.html?status=StatusUpdated');s
			} else {
				$objPevaluation->SetPEvaluationStatus($sectionMasterId, $newStatus);
				// header('location:siteEvaluationSectionList.html?status=StatusUpdated&schoolId=' . $_GET['schoolId'] . '');
			}

			//Audit Log Start
			// Instantiate the Logger class
			$objLog = new clsLogger();

			// Determine the action type (EDIT or ADD) based on the presence of a journal ID
			$logAction = ($newStatus == 0) ? $objLog::INACTIVE : $objLog::ACTIVE;
			$userId = isset($_GET['userId']) ? ($_GET['userId']) : 0;
			$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;
			$type = "section";
			$isSuperAdmin = ($isCurrentSchoolSuperAdmin == 1) ? 1 : 0;
			$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN; // User type is set to Admin
			// $objPevaluation->savePEvalAuditLog($sectionMasterId, $sectionMasterId, $userId, $userType, $action);

			$objPevaluation->savePEvalAuditLog($sectionMasterId, $sectionMasterId, $userId, $userType, $logAction, $IsMobile = 0, $type, $isSuperAdmin);

			unset($objPevaluation);
			unset($objLog);


			if ($isCurrentSchoolSuperAdmin == 1) // For superadmin
				header('location:PEvaluationSectionList.html?status=StatusUpdated');
			else
				header('location:PEvaluationSectionList.html?status=StatusUpdated&schoolId=' . $_GET['schoolId'] . '');
			exit();
		}
		if ($isEvalType == 'pef') {
			//Create object
			$objpef = new clsPEF();
			$newStatus = $_GET['newStatus'];
			$objpef->SetPEFEvaluationStatus($sectionMasterId, $newStatus);


			//Audit Log Start
			// Instantiate the Logger class

			// Determine the action type (EDIT or ADD) based on the presence of a journal ID
			$userId = isset($_GET['userId']) ? ($_GET['userId']) : 0;
			$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;
			$isPEFType = isset($_GET['isPEFType']) ? ($_GET['isPEFType']) : 0;

			$objLog = new clsLogger();
			$logAction = ($newStatus == 0) ? $objLog::INACTIVE : $objLog::ACTIVE;
			$type = "section";

			$userType = $objLog::SUPERADMIN; // User type is set to Admin
			$objpef = new clsPEF();
			$objpef->savePEFEvalAuditLog($sectionMasterId, $sectionMasterId, $userId, $userType, $logAction, $IsMobile = 0, $type);

			unset($objpef);

			$status = 'StatusUpdated';
			header('Location: pefEvaluationSectionList.html?active=' . ($isPEFType == 1 ? 'pef2' : 'pef1') . '&status=' . $status);
			exit();
		}
		if ($isEvalType == 'flooricu') {
			//Create object
			$objFloorTherapy = new clsFloorTherapyAndICUEvaluation();
			$newStatus = $_GET['newStatus'];

			$objFloorTherapy->SetFloorIcuEvaluationStatus($sectionMasterId, $newStatus);

			//Audit Log Start
			// Instantiate the Logger class
			$objLog = new clsLogger();
			$logAction = ($newStatus == 0) ? $objLog::INACTIVE : $objLog::ACTIVE;
			$userId = isset($_GET['userId']) ? ($_GET['userId']) : 0;
			$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;
			$isFloorIcuType = isset($_GET['isFloorIcuType']) ? ($_GET['isFloorIcuType']) : 0;

			$type = "section";

			$userType = $objLog::SUPERADMIN; // User type is set to Admin
			$objFloorTherapy = new clsFloorTherapyAndICUEvaluation();
			$objFloorTherapy->saveFloorAndIcuEvaluationAuditLog($sectionMasterId, $userId, $userType, $logAction, $type, $IsMobile = 0);

			unset($objFloorTherapy);
			unset($objLog);
			$status = 'StatusUpdated';
			header('Location: floorIcuEvaluationSectionList.html?active=' . ($isFloorIcuType == 1 ? 'icu' : 'floor') . '&status=' . $status);
			exit();
		}
	}
} else {
	header('location:formativeEvaluationSectionList.html');
	exit();
}
