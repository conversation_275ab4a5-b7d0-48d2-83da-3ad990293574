<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php');  
    include('../class/clsRotation.php');
    include('../class/clsHospitalSite.php');
    include('../class/clsStudent.php');
    include('../class/clsFormative.php');
	include('../setRequest.php'); 
	include('../class/clsQuestionOption.php');
	include('../class/clsSectionStudentName.php');
	include('../class/clsClinician.php');
	include('../class/clsPEvaluation.php');
	include('../class/clsschoolclinicalsiteunit.php');
    include('../class/clsLocations.php');	
	
    $schoolId = 0;
    $rotationId = 0;
    $studentId=0;    
	$pEvaluationMasterId=0;
	$clinicianId =0;
	$pevaluationrotationid=0;
	$hospitalSiteId=0;
	$display_to_date= date('m/d/Y');
	$clinicianId=$_SESSION["loggedClinicianId"];
	$loggedClinicianType = $_SESSION['loggedClinicianType'];
	$TimeZone= $_SESSION["loggedClinicianSchoolTimeZone"];
	
	//object
	$objRotation=new clsRotation();

	if(isset($_GET['pevaluationrotationid']))
	{
		$pevaluationrotationid= DecodeQueryData($_GET['pevaluationrotationid']);		
	}

	//For Edit CI Evaluation
	if(isset($_GET['pEvaluationMasterId']))
	{
		$pEvaluationMasterId= DecodeQueryData($_GET['pEvaluationMasterId']);
		$schoolId= $currentSchoolId;
		$page_title ="View P Evaluation " ;
		$bedCrumTitle = 'View';

		//For CI Evalution Details
		$objCIevaluation = new clsPEvaluation();	
		$rowCIevaluation=$objCIevaluation->GetEvaluationDetails($pEvaluationMasterId);
		
		if($rowCIevaluation=='')
		{
			header('location:pevaluationlist.html');
			exit;
		}
		$rotationId = ($rowCIevaluation['rotationId']);
		$clinicianId = ($rowCIevaluation['clinicianId']);
		$hospitalSiteId = ($rowCIevaluation['schoolClinicalSiteUnitId']);
		$evaluationDate = ($rowCIevaluation['evaluationDate']);
		$courselocationId = $rowCIevaluation['locationId'];
		$parentRotationId = stripslashes($rowCIevaluation['parentRotationId']);
		$rotationLocationId = stripslashes($rowCIevaluation['rotationLocationId']);
		$locationId = 0;
	
		if($rotationLocationId != $courselocationId && $parentRotationId > 0)
		{
				if(!$rotationLocationId)
					$locationId = $objRotation->GetLocationByRotation($rotationId);
				else
					$locationId  = $rotationLocationId;
		}
		else
		{	
				$locationId  = $courselocationId;
		}
				
		//Get Time Zone By Rotation 
		$objLocation = new clsLocations();
		$TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
		unset($objLocation);
		if($TimeZone == '')
			$TimeZone = $_SESSION["loggedClinicianSchoolTimeZone"];
		$evaluationDate = converFromServerTimeZone($evaluationDate,$TimeZone);
		$studentId = ($rowCIevaluation['studentId']);				
							
	}

	//For Student Name
	$objStudent = new clsStudent();
	$Student = $objStudent->GetStudentsByRotation($schoolId,$pevaluationrotationid=0);
	unset($objStudent);

	
	//Get CI Evalution Section
	$objCIevaluation = new clsPEvaluation();
	$totalSection=0;
	$PevaluationSection = $objCIevaluation->GetSections($schoolId,1);
	if($PevaluationSection !='')
	{       
		$totalSection =mysqli_num_rows($PevaluationSection);  
	}
	
	//For Hospital Site
	$objHospitalSite = new clsHospitalSite();
	$hospitalSite = $objHospitalSite->GetAllHospitalSite($currentSchoolId);
	unset($objHospitalSite);

	//For Rotation Name
	if($loggedClinicianType == 'p' || $loggedClinicianType == 'P')
		$rowsRotations = $objRotation->GetRotationBySchool($currentSchoolId);
	else
		$rowsRotations = $objRotation->GetRotationBySchool($currentSchoolId);
	
	unset($objRotation);
		
?>    
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo($page_title); ?></title>
    <?php include('includes/headercss.php');?>
	<link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
	<link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
	
	<style type="text/css">
		.some-class {
		float: left;
		clear: none;
		}

	</style>

	</head>

<body>
    <?php include('includes/header.php');?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>                    
                    <li><a href="pevaluationlist.html">CI Evaluation</a></li>
                    <li class="active"><?php echo($bedCrumTitle);?></li>
                </ol>
            </div>

        </div>
    </div>

    <div class="container">

         <form id="frmcievaluation" data-parsley-validate class="form-horizontal" method="POST" action="cievaluationsubmit.html?pEvaluationMasterId=<?php echo(EncodeQueryData($pEvaluationMasterId)); ?>
																									&pevaluationrotationid=<?php echo (EncodeQueryData($pevaluationrotationid));?>" >

            <div class="row">                
				<div class="col-md-6">
				
				<div class="form-group">
                        <label class="col-md-4 control-label" for="cborotation">Rotation</label>
                        <div class="col-md-8">
                            <select id="cborotation" name="cborotation"  
									class="form-control input-md required-input select2_single"   required>
                            <option value="" selected>Select</option>
								<?php
                                if($rowsRotations!="")
                                {
                                    while($row = mysqli_fetch_assoc($rowsRotations))
                                    {
                                         $selrotationId  = $row['rotationId'];
                                         $name  = stripslashes($row['title']);
										?>
										<option value="<?php echo($selrotationId); ?>" <?php if($rotationId==$selrotationId){ ?>  selected="true" <?php } ?>><?php echo($name); ?></option>
                                        
										<?php

                                    }
                                }
                            ?>
                            </select>
                        </div>
                    </div>
					
				</div>
				<div class="col-md-6">
				
				<div class="form-group">
                        <label class="col-md-4 control-label" for="cbostudent">Student</label>
                        <div class="col-md-8">
                            <select id="cbostudent" name="cbostudent" class="form-control input-md required-input select2_single" disabled >
                            <option value="" selected>Select</option>
								<?php
                                if($Student!="")
                                {
                                    while($row = mysqli_fetch_assoc($Student))
                                    {
                                         $selstudentId  = $row['studentId'];
                                         $firstName  = stripslashes($row['firstName']);
                                         $lastName  = stripslashes($row['lastName']);
										 $name=	$firstName . ' ' . $lastName;
                                         ?>
										<option value="<?php echo($selstudentId); ?>" <?php if($studentId==$selstudentId){ ?>  selected="true" <?php } ?>><?php echo($name); ?></option>
                                        
										<?php

                                    }
                                }
                            ?>
                            </select>
                        </div>
                    </div>
					<!--- ROTATION DD END---->
				</div>
			</div>	
				
           
			
			<div class="row">
				<div class="col-md-6">
					<div class="form-group">					
							<label class="col-md-4 control-label" for="evaluationDate">Evaluation Date</label>
								<div class="col-md-8 col-sm-4 col-xs-12">	
									<div class='input-group date' id='evaluationDate'>
									
										<input readonly type='text' name="evaluationDate"  id="evaluationDate" class="form-control input-md required-input rotation_date" value="<?php  echo ($display_to_date); ?>" required data-parsley-errors-container="#error-txtDate"/>
											<span class="input-group-addon">										
												<span class="glyphicon glyphicon-calendar"></span>
											</span>
									</div>
										<div id="error-txtDate"></div>
								</div>		
					</div>	
				</div>
				<div class="col-md-6">
						<div class="form-group">
								<label class="col-md-4 control-label" for="cbohospitalsites">Hospital Sites</label>
								<div class="col-md-8">
									<select id="cbohospitalsites"   name="cbohospitalsites"   class="form-control input-md required-input select2_single"  required="true" >
									<option value="" selected>Select</option>
										<?php
										if($hospitalSite!="")
										{
											while($row = mysqli_fetch_assoc($hospitalSite))
											{
												 $selhospitalSiteId  = $row['hospitalSiteId'];
												 $name  = stripslashes($row['title']);

												 ?>
												  <option value="<?php echo($selhospitalSiteId); ?>" <?php if($hospitalSiteId==$selhospitalSiteId){ ?> selected="true" <?php } ?> ><?php echo($name); ?></option>
												 <?php

											}
										}
									?>
									</select>
									<p id="hospitalerror" style="color:#E74C3C; border: 1px solid #E8544;"></p>
								</div>
							</div>
				</div>
			</div>	
			
			<div class="row">
				<div class="col-md-12">
					<div class="form-group">
						<label class="col-md-2 control-label" for="instructions:"></label>
							<div class="col-md-10 col-sm-6 col-xs-12">
								<div class="panel panel-default">
									<div class="panel-body">
										<p>The preceptor's ratings are based on the following categories. The rating scale for each category is<br>
										<b>1-Strongly Disagree, 2-Disagree, 3-Neutral/Acceptable, 4-Agree, 5-Strongly Agree.</b></p>
									</div>
								</div>
							</div>						
					</div>
				</div>
			</div>
					
			<!---- 1st SECTION div start -------->
		<div class="row">	
				<div class="col-md-12">
					<div class="form-group">
							<label class="col-md-2 control-label" for="instructions:"></label>
							<div class="col-md-10 col-sm-6 col-xs-12">	
									<div class="panel-group" id="posts"> 
											<div class="panel panel-default">  
													<div class="panel-heading">  
															<h4 class="panel-title">  
																<a href="#PevaluationSection" data-toggle="collapse" data-parent="#posts"><b>Student Preceptor Evaluation</b></a>  
															</h4>  
													</div>
														<div id="PevaluationSection" class="panel-collapse collapse in">
															<?php  	while($row = mysqli_fetch_array($PevaluationSection))
																		{
																			$sectionMasterId = $row['sectionMasterId'];
																			$title = $row['title'];
																			?>
																				<div class="panel-body"><?php echo '<b>'. $title .'</b>'; ?></div>
																			<?php   // for question
																				$totalCIevaluation = 0;
																				$Pevaluationquestion = $objCIevaluation->GetAllEvaluationQuestionMaster($schoolId,$sectionMasterId);  
																				$totalCIevaluation = ($Pevaluationquestion !='') ? mysqli_num_rows($Pevaluationquestion) : 0;  
																				
																				if($Pevaluationquestion)
																				{
																					while($row = mysqli_fetch_array($Pevaluationquestion))
																					{													
																						if(isset($_GET['pEvaluationMasterId']))
																							$pEvaluationMasterId= DecodeQueryData($_GET['pEvaluationMasterId']);
																						else 
																							$pEvaluationMasterId=0;
																							
																						$schoolPEvaluationQuestionId = $row['schoolPEvaluationQuestionId'];
																						$questionText = $row['questionText'];
																						$schoolPEvaluationQuestionType = $row['schoolPEvaluationQuestionType'];													
																						$qhtml=GetPEvaluationQuestionHtml($schoolPEvaluationQuestionId,$schoolPEvaluationQuestionType,$pEvaluationMasterId,$currentSchoolId);
																						?>																									
																						<div class="panel-body"> 							   
																						<b><?php echo ($questionText); ?></b> <br/><br/>
																								<?php echo $qhtml; ?>					   
																						</div>
																						<?php  
																					} 
																				}	
																		}				
																?>
														</div>													
				
										</div>  				
									</div> 
											
						</div>
					</div>
				</div>
		</div>
		
		<div class="row">
			<div class="form-group">
				<label class="col-md-2 control-label"></label>
				<div class="col-md-10">
					<a type="button" href="pevaluationlist.html" class="btn btn-default">Cancel</a>
				</div>
			</div>
		</div>
			
        </form>


    </div>

    <?php include('includes/footer.php');?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
	<script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
	<script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
	<script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>

	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/jquery.cascadingdropdown.js"></script>
    



    <script type="text/javascript">
 
        $(window).load(function(){
			
		
            $('#frmcievaluation').parsley().on('field:validated', function() {
			    var ok = $('.parsley-error').length === 0;
            })
			
			
				
            .on('form:submit', function() {
                ShowProgressAnimation();									  
                return true; // Don't submit form for this demo
            });
			
					$('#evaluationDate').datetimepicker
					({
						format: 'MM/DD/YYYY',
						maxDate: new Date()
					});
					$('#studentsignitureDate').datetimepicker
					({
						format: 'MM/DD/YYYY',
						maxDate: new Date()
					});
			
			//for searching dropdown
			$(".select2_single").select2();
			$('#select2-cbostudent-container').addClass('required-select2');			
			$('#select2-cborotation-container').addClass('required-select2');
			$('#select2-cbohospitalsites-container').addClass('required-select2');
			
			<?php if(isset($_GET['pEvaluationMasterId']) && ($_GET['pevaluationrotationid'])) { ?>
			$('#cbostudent').prop('disabled', true);
		<?php } ?>
			
        });
			
				$('#cborotation').prop('disabled', true);
				$('#cbohospitalsites').prop('disabled', true);
				$("input[type=radio]").attr('disabled', true);
				$("textarea").attr('disabled', true);
	
    </script>
</body>

</html>