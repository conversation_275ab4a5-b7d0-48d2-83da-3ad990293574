<?php
/**
 * EmailDeliveryReceiptRulesApiTest
 * PHP version 5
 *
 * @category Class
 * @package  ClickSend
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * ClickSend v3 API
 *
 * This is an official SDK for [ClickSend](https://clicksend.com)  Below you will find a current list of the available methods for clicksend.  *NOTE: You will need to create a free account to use the API. You can register [here](https://dashboard.clicksend.com/#/signup/step1/)..*
 *
 * OpenAPI spec version: 3.1
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * ClickSend Codegen version: 3.0.51
 */
/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Please update the test case below to test the endpoint.
 */

namespace ClickSend;

use <PERSON><PERSON>Send\Configuration;
use ClickSend\ApiException;
use <PERSON>lickSend\ObjectSerializer;
use PHPUnit\Framework\TestCase;

/**
 * EmailDeliveryReceiptRulesApiTest Class Doc Comment
 *
 * @category Class
 * @package  ClickSend
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */
class EmailDeliveryReceiptRulesApiTest extends TestCase
{

    /**
     * Setup before running any test cases
     */
    public static function setUpBeforeClass(): void
    {
    }

    /**
     * Setup before running each test case
     */
    public function setUp(): void
    {
    }

    /**
     * Clean up after running each test case
     */
    public function tearDown(): void
    {
    }

    /**
     * Clean up after running all test cases
     */
    public static function tearDownAfterClass(): void
    {
    }

    /**
     * Test case for emailDeliveryReceiptAutomationDelete
     *
     * Delete email delivery receipt automation.
     *
     */
    public function testEmailDeliveryReceiptAutomationDelete()
    {
    }

    /**
     * Test case for emailDeliveryReceiptAutomationGet
     *
     * Get specific email delivery receipt automation.
     *
     */
    public function testEmailDeliveryReceiptAutomationGet()
    {
    }

    /**
     * Test case for emailDeliveryReceiptAutomationPost
     *
     * Create email delivery receipt automations.
     *
     */
    public function testEmailDeliveryReceiptAutomationPost()
    {
    }

    /**
     * Test case for emailDeliveryReceiptAutomationPut
     *
     * Update email delivery receipt automation.
     *
     */
    public function testEmailDeliveryReceiptAutomationPut()
    {
    }

    /**
     * Test case for emailDeliveryReceiptAutomationsGet
     *
     * Get all email delivery receipt automations.
     *
     */
    public function testEmailDeliveryReceiptAutomationsGet()
    {
    }
}
