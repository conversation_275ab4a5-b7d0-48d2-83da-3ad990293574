<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsFloorTherapyAndICUEvaluation.php');
include('../setRequest.php');

$isFloorIcuType = isset($_GET['isFloorIcuType']) ? DecodeQueryData($_GET['isFloorIcuType']) : 0;
$sectionMasterId = isset($_GET['sectionMasterId']) ? (int)DecodeQueryData($_GET['sectionMasterId']) : 0;

$title = "Add Floor Therapy and ICU/ABG Evaluation Section  ";
if ($isFloorIcuType == 1)
    $page_title = "Add ICU/ABG ";
else
    $page_title = "Add Floor Therapy";

$bedCrumTitle = 'Add';
$PEFSectionId = 0;
$title = '';
$sortOrder  = '';
$TopicTitleId = '';
$view = '';
$bedCrumTitle = 'Add';

$sortOrder = '';
$isActiveCheckoff = $_SESSION['isActiveCheckoff'];
if ($isFloorIcuType == 0) {
    $activeType = 'floor';
} else {
    $activeType = 'icu';
}
$view = isset($_GET['view']) ? $_GET['view'] : '';

if (isset($_GET['editid'])) //Edit Mode
{
    $PEFSectionId = DecodeQueryData($_GET['editid']);


    if ($view == 1) {
        if ($isFloorIcuType == 1)
            $page_title = "View ICU/ABG Evaluation";
        else
            $page_title = "View Floor Therapy ";
    } else {
        if ($isFloorIcuType == 1)
            $page_title = "Edit ICU/ABG Evaluation";
        else
            $page_title = "Edit Floor Therapy ";
    }

    if ($view == '1') {
        $bedCrumTitle = 'view';
    } else {
        $bedCrumTitle = 'Edit';
    }


    //For Checkoff Topic Details
    $objFloorTherapy = new clsFloorTherapyAndICUEvaluation();
    $row = $objFloorTherapy->GetFloorIcuEvaluationSectionDetail($PEFSectionId, $isFloorIcuType);
    unset($objFloorTherapy);

    $title  = stripslashes($row['title']);
    $sortOrder  = stripslashes($row['sortOrder']);
}



?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($page_title); ?></title>
    <?php include('includes/headercss.php'); ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">

</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="settings.html">Settings</a></li>
                    <li>
                        <a href="floorIcuEvaluationSectionList.html?active=<?php echo ($isFloorIcuType == 1 ? 'icu' : 'floor'); ?>&isFloorIcuType=<?php echo EncodeQueryData($isFloorIcuType); ?>">
                            <?php echo ($isFloorIcuType == 1) ? "ICU/ABG Evaluation Section" : "Floor Therapy Evaluation Section"; ?>
                        </a>
                    </li>
                    <li class="active"><?php echo ($bedCrumTitle); ?></li>
                </ol>
            </div>

        </div>
    </div>

    <div class="container">

        <form id="frmcheckoff" data-parsley-validate class="form-horizontal" method="POST" action="floorIcuEvaluationSectionSubmit.html?editid=<?php echo (EncodeQueryData($PEFSectionId)); ?>&isFloorIcuType=<?php echo (EncodeQueryData($isFloorIcuType)); ?>">
            <input type="hidden" name="isFloorIcuType" id="" value="<?php echo $isFloorIcuType; ?>">

            <div class="row">
                <div class="col-md-6">

                    <!-- Text input-->
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtsection"> Section</label>
                        <div class="col-md-12">
                            <input id="txtsection" name="txtsection" value="<?php echo ($title); ?>" type="text" class="form-control input-md required-input" required>

                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtsortorder">Section Number</label>
                        <div class="col-md-12">
                            <input id="txtsortorder" name="txtsortorder" value="<?php echo ($sortOrder); ?>" type="text" class="form-control input-md required-input" required>

                        </div>
                    </div>

                </div>
            </div>
            <div class="form-group">
                <!-- <label class="col-md-2 control-label"></label> -->
                <div class="col-md-12" style="display: flex; justify-content: center; margin: 20px 0; gap: 15px;">
                    <?php if ($view == 1) { ?>
                        <a type="button" href="floorIcuEvaluationSectionList.html?sectionMasterId=<?php echo (EncodeQueryData($sectionMasterId)); ?>&active=<?php echo ($isFloorIcuType == 1 ? 'icu' : 'floor'); ?>" class="btn btn-default">Cancel</a>
                    <?php } else { ?>
                        <button id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
                        <a type="button" href="floorIcuEvaluationSectionList.html?active=<?php echo ($isFloorIcuType == 1 ? 'icu' : 'floor'); ?>"
                            class="btn btn-default">Cancel</a>
                    <?php } ?>
                </div>

            </div>
        </form>

    </div>

    <?php include('includes/footer.php'); ?>

    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="https://cdn.ckeditor.com/ckeditor5/35.0.1/classic/ckeditor.js"></script>
    <script type="text/javascript">
        $(".select2_tags").select2({
            'placeholder': 'Select'
        }); //for multiple selection
        // ClassicEditor
        // 	.create(document.querySelector('#txtdescription'))
        // 	.catch(error => {
        // 		console.error(error);
        // 	});	
        $(window).load(function() {

            $('#frmcheckoff').parsley().on('field:validated', function() {
                    var ok = $('.parsley-error').length === 0;
                })
                .on('form:submit', function() {
                    ShowProgressAnimation();
                    return true;
                });

        });
    </script>
    <?php if ($view == 1): ?>
        <script>
            document.addEventListener("DOMContentLoaded", function() {
                // Disable all form fields inside the form only
                document.querySelectorAll('#frmcheckoff input, #frmcheckoff select, #frmcheckoff textarea, #frmcheckoff button').forEach(function(el) {
                    el.setAttribute('disabled', true);
                });

                // Optional: enable Cancel button so user can go back
                document.querySelectorAll('.btn-default').forEach(function(el) {
                    el.removeAttribute('disabled');
                });
            });
        </script>
    <?php endif; ?>

</body>

</html>