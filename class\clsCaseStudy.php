<?php

class clsCaseStudy
{
    function SaveCaseStudyFloor($caseStudyId)
    {

        $objDB = new clsDB();
        $sql = '';
        if ($caseStudyId > 0) {

            $sql = "UPDATE casestudyfloor SET 
						 rotationId = '" . addslashes($this->rotationId) . "',						
						 caseStudydate = '" . addslashes($this->caseStudydate) . "',  
						 floorCohort = '" . addslashes($this->floorCohort) . "',						
						 floorAdmissionDate = '" . addslashes($this->floorAdmissionDate) . "',
                         floorPtAge = '" . addslashes($this->floorPtAge) . "',						
						 floorSex = '" . addslashes($this->floorSex) . "',  
						 floorHt = '" . addslashes($this->floorHt) . "', 
						 floorSmoking = '" . addslashes($this->floorSmoking) . "',
						 floorChiefComplaint = '" . addslashes($this->floorChiefComplaint) . "',						
						 floorRespiratory = '" . addslashes($this->floorRespiratory) . "',
                         floorPastMedicalHx = '" . addslashes($this->floorPastMedicalHx) . "',						
						 floorOtherPulmonaryProblems = '" . addslashes($this->floorOtherPulmonaryProblems) . "',  
						 floorHR = '" . addslashes($this->floorHR) . "',
						 floorSpontRR = '" . addslashes($this->floorSpontRR) . "',						
						 floorBP = '" . addslashes($this->floorBP) . "',
                         floorTemp = '" . addslashes($this->floorTemp) . "',
                         
                         floorSpO2 = '" . addslashes($this->floorSpO2) . "',						
						 floorIO = '" . addslashes($this->floorIO) . "',  
						 floorBreathSounds = '" . addslashes($this->floorBreathSounds) . "',
						 floorLevelofActivity = '" . addslashes($this->floorLevelofActivity) . "',						
						 floorNa = '" . addslashes($this->floorNa) . "',
                         floorK = '" . addslashes($this->floorK) . "',						
						 floorCl = '" . addslashes($this->floorCl) . "',  
						 floorWBC = '" . addslashes($this->floorWBC) . "',
						 floorHgb = '" . addslashes($this->floorHgb) . "',						
						 floorHct = '" . addslashes($this->floorHct) . "',
                         floorCO2 = '" . addslashes($this->floorCO2) . "',						
						 floorBUN = '" . addslashes($this->floorBUN) . "',  
						 floorGlucose = '" . addslashes($this->floorGlucose) . "',
						 floorPlatelets = '" . addslashes($this->floorPlatelets) . "',						
						 floorINR = '" . addslashes($this->floorINR) . "',
                         floorSputumCult = '" . addslashes($this->floorSputumCult) . "',						
						 floorCreatinine = '" . addslashes($this->floorCreatinine) . "',  
						 floorLabInterpretation = '" . addslashes($this->floorLabInterpretation) . "',
						 floorXRayInterpretation = '" . addslashes($this->floorXRayInterpretation) . "',						
						 floorEKG = '" . addslashes($this->floorEKG) . "',
                         floorTrachSize = '" . addslashes($this->floorTrachSize) . "',						
						 floorTrachType = '" . addslashes($this->floorTrachType) . "',  
						 floorCuffPressure = '" . addslashes($this->floorCuffPressure) . "',
						 floorpH = '" . addslashes($this->floorpH) . "',						
						 floorPaCO2 = '" . addslashes($this->floorPaCO2) . "',
                         floorHCO3 = '" . addslashes($this->floorHCO3) . "',						
						 floorFVC = '" . addslashes($this->floorFVC) . "',  
						 floorFEF25 = '" . addslashes($this->floorFEF25) . "',
						 floorFEF1 = '" . addslashes($this->floorFEF1) . "',						
						 floorPaO2 = '" . addslashes($this->floorPaO2) . "',
                         floorSaO2 = '" . addslashes($this->floorSaO2) . "',						
						 floorFiO2 = '" . addslashes($this->floorFiO2) . "',  
						 floorPEFR = '" . addslashes($this->floorPEFR) . "',
						 floorFEV1 = '" . addslashes($this->floorFEV1) . "',						
						 floorDateBloodGas = '" . addslashes($this->floorDateBloodGas) . "',
                         floorLungValues = '" . addslashes($this->floorLungValues) . "',						
						 floorInterpretationABG = '" . addslashes($this->floorInterpretationABG) . "',  
						 floorInterpretationPFT = '" . addslashes($this->floorInterpretationPFT) . "',
						 floorInterpretationPAO2 = '" . addslashes($this->floorInterpretationPAO2) . "',						
						 floorInterpretationAO2 = '" . addslashes($this->floorInterpretationAO2) . "',
                        floorInterpretationCaO2 = '" . addslashes($this->floorInterpretationCaO2) . "',
						 floorInterpretationPFRatio = '" . addslashes($this->floorInterpretationPFRatio) . "',						
						 floorIPAP = '" . addslashes($this->floorIPAP) . "',
                         floorEPAP = '" . addslashes($this->floorEPAP) . "',						
						 floorRate = '" . addslashes($this->floorRate) . "', 
                         floorFiO2Setting = '" . addslashes($this->floorFiO2Setting) . "', 
						 floorItime = '" . addslashes($this->floorItime) . "',
						 floorRise = '" . addslashes($this->floorRise) . "',						
						 floorRamp = '" . addslashes($this->floorRamp) . "',
                          floorHumidityTemp = '" . addslashes($this->floorHumidityTemp) . "',
						 floorSuction = '" . addslashes($this->floorSuction) . "',						
						 floorCough = '" . addslashes($this->floorCough) . "',
                         floorSputumAmount = '" . addslashes($this->floorSputumAmount) . "',						
						 flooMedicationsUse1 = '" . addslashes($this->flooMedicationsUse1) . "',  
						 floorModificationCarePlan1 = '" . addslashes($this->floorModificationCarePlan1) . "',
						 flooMedicationsUse2 = '" . addslashes($this->flooMedicationsUse2) . "',						
						 floorModificationCarePlan2 = '" . addslashes($this->floorModificationCarePlan2) . "',
                         flooMedicationsUse3 = '" . addslashes($this->flooMedicationsUse3) . "',  
						 floorModificationCarePlan3 = '" . addslashes($this->floorModificationCarePlan3) . "',
						 flooMedicationsUse4 = '" . addslashes($this->flooMedicationsUse4) . "',						
						 floorModificationCarePlan4 = '" . addslashes($this->floorModificationCarePlan4) . "',
                         flooMedicationsUse5 = '" . addslashes($this->flooMedicationsUse5) . "',  
						 floorModificationCarePlan5 = '" . addslashes($this->floorModificationCarePlan5) . "',
						 flooMedicationsUse6 = '" . addslashes($this->flooMedicationsUse6) . "',						
						 floorModificationCarePlan6 = '" . addslashes($this->floorModificationCarePlan6) . "',
                         flooMedicationsUse7 = '" . addslashes($this->flooMedicationsUse7) . "',  
						 floorModificationCarePlan7 = '" . addslashes($this->floorModificationCarePlan7) . "',
						 flooMedicationsUse8 = '" . addslashes($this->flooMedicationsUse8) . "',						
						 floorModificationCarePlan8 = '" . addslashes($this->floorModificationCarePlan8) . "',
                         flooMedicationsUse9 = '" . addslashes($this->flooMedicationsUse9) . "',
                         floorModificationCarePlan9 = '" . addslashes($this->floorModificationCarePlan9) . "',
                         flooMedicationsUse10 = '" . addslashes($this->flooMedicationsUse10) . "',  
						 floorModificationCarePlan10 = '" . addslashes($this->floorModificationCarePlan10) . "',
                         flooMedicationsUse11 = '" . addslashes($this->flooMedicationsUse11) . "',  
						 floorModificationCarePlan11 = '" . addslashes($this->floorModificationCarePlan11) . "',
                         flooMedicationsUse12 = '" . addslashes($this->flooMedicationsUse12) . "',  
						 floorModificationCarePlan12 = '" . addslashes($this->floorModificationCarePlan12) . "',
                         flooMedicationsUse13 = '" . addslashes($this->flooMedicationsUse13) . "',  
						 floorModificationCarePlan13 = '" . addslashes($this->floorModificationCarePlan13) . "',
                         flooMedicationsUse14 = '" . addslashes($this->flooMedicationsUse14) . "',  
						 floorModificationCarePlan14 = '" . addslashes($this->floorModificationCarePlan14) . "',
                         flooMedicationsUse15 = '" . addslashes($this->flooMedicationsUse15) . "',  
						 floorModificationCarePlan15 = '" . addslashes($this->floorModificationCarePlan15) . "',
						 flooMedicationsUseList = '" . addslashes($this->flooMedicationsUseList) . "',
						 floorModificationCarePlanList = '" . addslashes($this->floorModificationCarePlanList) . "',
						 studentcomments = '" . addslashes($this->studentcomments) . "',
						 school_comments = '" . addslashes($this->school_comments) . "',
						 clinician_comments = '" . addslashes($this->clinician_comments) . "'
						 Where caseStudyFloorId= " . $caseStudyId;
            // echo 'update->'.$sql;exit;
            $objDB->ExecuteQuery($sql);
        } else {
            $sql = "INSERT INTO casestudyfloor (schoolId,studentId, rotationId,caseStudydate, type, floorCohort, floorAdmissionDate,
                                    floorPtAge, floorSex, floorHt, floorSmoking, floorChiefComplaint, floorRespiratory, floorPastMedicalHx, floorOtherPulmonaryProblems,
                                    floorHR, floorSpontRR, floorBP, floorTemp, floorSpO2, floorIO, floorBreathSounds, floorLevelofActivity, floorNa, floorK, floorCl,
                                    floorWBC, floorHgb, floorHct, floorCO2, floorBUN, floorGlucose, floorPlatelets, floorINR, floorSputumCult, floorCreatinine, floorLabInterpretation, floorXRayInterpretation,
                                    floorEKG, floorTrachSize, floorTrachType, floorCuffPressure, floorpH, floorPaCO2,  floorHCO3, floorFVC, floorFEF25, floorFEF1,
                                    floorPaO2, floorSaO2, floorFiO2, floorPEFR, floorFEV1,  floorDateBloodGas,  floorLungValues, floorInterpretationABG, floorInterpretationPFT, floorInterpretationPAO2,
                                    floorInterpretationAO2,  floorInterpretationCaO2, floorInterpretationPFRatio, floorIPAP, floorEPAP, floorRate, floorFiO2Setting, floorItime,  floorRise, floorRamp, floorHumidityTemp,
                                     floorSuction, floorCough,
                                    floorSputumAmount, flooMedicationsUse1, floorModificationCarePlan1, flooMedicationsUse2,
                                    floorModificationCarePlan2, flooMedicationsUse3, floorModificationCarePlan3, flooMedicationsUse4, floorModificationCarePlan4, flooMedicationsUse5, floorModificationCarePlan5, flooMedicationsUse6, floorModificationCarePlan6,
                                    flooMedicationsUse7, floorModificationCarePlan7, flooMedicationsUse8, floorModificationCarePlan8, flooMedicationsUse9, floorModificationCarePlan9,  flooMedicationsUse10, floorModificationCarePlan10,flooMedicationsUse11, floorModificationCarePlan11, flooMedicationsUse12, floorModificationCarePlan12,
                                     flooMedicationsUse13, floorModificationCarePlan13, flooMedicationsUse14, floorModificationCarePlan14, flooMedicationsUse15, floorModificationCarePlan15,flooMedicationsUseList,floorModificationCarePlanList,studentcomments,school_comments,clinician_comments ) 
                    VALUES ('" . addslashes($this->schoolId) . "',
                            '" . addslashes($this->studentId) . "',
                            '" . addslashes($this->rotationId) . "',
                            '" . addslashes($this->caseStudydate) . "',	
                            '" . addslashes($this->type) . "',
                            '" . addslashes($this->floorCohort) . "',						
                            '" . addslashes($this->floorAdmissionDate) . "',
                            '" . addslashes($this->floorPtAge) . "',
                            '" . addslashes($this->floorSex) . "',
                            '" . addslashes($this->floorHt) . "',
                            '" . addslashes($this->floorSmoking) . "',	
                            '" . addslashes($this->floorChiefComplaint) . "',	
                            '" . addslashes($this->floorRespiratory) . "',
                            '" . addslashes($this->floorPastMedicalHx) . "',						
                            '" . addslashes($this->floorOtherPulmonaryProblems) . "',
                            '" . addslashes($this->floorHR) . "',
                            '" . addslashes($this->floorSpontRR) . "',
                            '" . addslashes($this->floorBP) . "',
                             '" . addslashes($this->floorTemp) . "',
                            '" . addslashes($this->floorSpO2) . "',	
                            '" . addslashes($this->floorIO) . "',
                            '" . addslashes($this->floorBreathSounds) . "',						
                            '" . addslashes($this->floorLevelofActivity) . "',
                            '" . addslashes($this->floorNa) . "',
                            '" . addslashes($this->floorK) . "',
                            '" . addslashes($this->floorCl) . "',	
                            '" . addslashes($this->floorWBC) . "',	
                            '" . addslashes($this->floorHgb) . "',
                            '" . addslashes($this->floorHct) . "',						
                            '" . addslashes($this->floorCO2) . "',
                            '" . addslashes($this->floorBUN) . "',
                            '" . addslashes($this->floorGlucose) . "',
                            '" . addslashes($this->floorPlatelets) . "',	
                            '" . addslashes($this->floorINR) . "',	
                            '" . addslashes($this->floorSputumCult) . "',
                            '" . addslashes($this->floorCreatinine) . "',						
                            '" . addslashes($this->floorLabInterpretation) . "',
                            '" . addslashes($this->floorXRayInterpretation) . "',
                            '" . addslashes($this->floorEKG) . "',
                            '" . addslashes($this->floorTrachSize) . "',	
                            '" . addslashes($this->floorTrachType) . "',	
                            '" . addslashes($this->floorCuffPressure) . "',
                            '" . addslashes($this->floorpH) . "',						
                            '" . addslashes($this->floorPaCO2) . "',
                            '" . addslashes($this->floorHCO3) . "',
                            '" . addslashes($this->floorFVC) . "',
                            '" . addslashes($this->floorFEF25) . "',	
                            '" . addslashes($this->floorFEF1) . "',	
                            '" . addslashes($this->floorPaO2) . "',
                            '" . addslashes($this->floorSaO2) . "',						
                            '" . addslashes($this->floorFiO2) . "',

                            '" . addslashes($this->floorPEFR) . "',
                            '" . addslashes($this->floorFEV1) . "',
                            '" . addslashes($this->floorDateBloodGas) . "',	
                            '" . addslashes($this->floorLungValues) . "',	
                            '" . addslashes($this->floorInterpretationABG) . "',
                            '" . addslashes($this->floorInterpretationPFT) . "',						
                            '" . addslashes($this->floorInterpretationPAO2) . "',
                            '" . addslashes($this->floorInterpretationAO2) . "',
                            '" . addslashes($this->floorInterpretationCaO2) . "',
                            '" . addslashes($this->floorInterpretationPFRatio) . "',	
                            '" . addslashes($this->floorIPAP) . "',	
                            '" . addslashes($this->floorEPAP) . "',
                            '" . addslashes($this->floorRate) . "',	
                             '" . addslashes($this->floorFiO2Setting) . "',	
                            '" . addslashes($this->floorItime) . "',
                            '" . addslashes($this->floorRise) . "',
                            '" . addslashes($this->floorRamp) . "',	
                            '" . addslashes($this->floorHumidityTemp) . "',	
                            '" . addslashes($this->floorSuction) . "',
                            '" . addslashes($this->floorCough) . "',						
                            '" . addslashes($this->floorSputumAmount) . "',
                            '" . addslashes($this->flooMedicationsUse1) . "',	
                            '" . addslashes($this->floorModificationCarePlan1) . "',
                            '" . addslashes($this->flooMedicationsUse2) . "',						
                            '" . addslashes($this->floorModificationCarePlan2) . "'	,
                            '" . addslashes($this->flooMedicationsUse3) . "',	
                            '" . addslashes($this->floorModificationCarePlan3) . "',
                            '" . addslashes($this->flooMedicationsUse4) . "',						
                            '" . addslashes($this->floorModificationCarePlan4) . "'	,
                            '" . addslashes($this->flooMedicationsUse5) . "',	
                            '" . addslashes($this->floorModificationCarePlan5) . "',
                            '" . addslashes($this->flooMedicationsUse6) . "',						
                            '" . addslashes($this->floorModificationCarePlan6) . "'	,
                            '" . addslashes($this->flooMedicationsUse7) . "',	
                            '" . addslashes($this->floorModificationCarePlan7) . "',
                            '" . addslashes($this->flooMedicationsUse8) . "',						
                            '" . addslashes($this->floorModificationCarePlan8) . "'	,
                            '" . addslashes($this->flooMedicationsUse9) . "',	
                            '" . addslashes($this->floorModificationCarePlan9) . "',
                            '" . addslashes($this->flooMedicationsUse10) . "',						
                            '" . addslashes($this->floorModificationCarePlan10) . "',
                            '" . addslashes($this->flooMedicationsUse11) . "',						
                            '" . addslashes($this->floorModificationCarePlan11) . "',
                            '" . addslashes($this->flooMedicationsUse12) . "',						
                            '" . addslashes($this->floorModificationCarePlan12) . "',
                            '" . addslashes($this->flooMedicationsUse13) . "',						
                            '" . addslashes($this->floorModificationCarePlan13) . "',
                            '" . addslashes($this->flooMedicationsUse14) . "',						
                            '" . addslashes($this->floorModificationCarePlan14) . "',
                            '" . addslashes($this->flooMedicationsUse15) . "',						
                            '" . addslashes($this->floorModificationCarePlan15) . "',
                            '" . addslashes($this->flooMedicationsUseList) . "',
                            '" . addslashes($this->floorModificationCarePlanList) . "',
                            '" . addslashes($this->studentcomments) . "',
                            '" . addslashes($this->school_comments) . "',
                            '" . addslashes($this->clinician_comments) . "'	
                                                    
                            )";
            // echo 'Insert->'.$sql;exit;
            $caseStudyId = $objDB->ExecuteInsertQuery($sql);
        }

        unset($objDB);
        return $caseStudyId;
    }

    function GetAllCaseStudy($SchoolId, $rotationId, $studentId, $canvasStatus = '', $casestudySettings = '')
    {
        // print_r($casestudySettings);
        $sql = '';
        $casestudySettings = explode(",", $casestudySettings);
        $objDB = new clsDB();
        $rows = "";
        //check value in array is exist
        //PACR CASE STUDY
        if (in_array("1", $casestudySettings)) {
            $sql .= "SELECT 
            CONCAT('CC - ', LEFT(pacrCC, 70), '<br> DX - ', LEFT(pacrDx, 70)) AS ChiefComplaint,
            casestudypacr.caseStudyPacrId as caseStudyId, 
            casestudypacr.caseStudydate  ,
            casestudypacr.ClinicianDate , 
            casestudypacr.schoolDate ,
            casestudypacr.clinician_comments, 
            casestudypacr.school_comments, 
            casestudypacr.isSendToCanvas, 
            casestudypacr.studentId, 
            rotation.rotationId,
            rotation.title as rotationname,
            student.firstName as studentfirstName,
            rankmaster.title as rankTitle,
            student.lastName as studentlastName ,
            casestudypacr.type,
            casestudypacr.clinician_comments,
            casestudypacr.school_comments,
            casestudypacr.status,
            rotation.parentRotationId,rotation.locationId as rotationLocationId, courses.title AS Coursename,courses.locationId
            FROM casestudypacr 
            LEFT JOIN student ON student.studentId = casestudypacr.studentId 
            LEFT JOIN rotation ON casestudypacr.rotationId=rotation.rotationId 
            INNER JOIN rankmaster ON rankmaster.rankId=student.rankId
            LEFT JOIN courses ON rotation.courseId=courses.courseId
            where casestudypacr.schoolId=" . $SchoolId;

            if ($rotationId)
                $sql .= "  AND  casestudypacr.rotationId=" . $rotationId;
            if ($studentId > 0)
                $sql .= "  AND casestudypacr.studentId=" . $studentId;
            if ($canvasStatus != '')
                $sql .= "  AND casestudypacr.isSendToCanvas=" . $canvasStatus;
        }
        //check value in array is exist
        //FLOOR CASE STUDY
        if (in_array("2", $casestudySettings)) {
            //get position in array
            $key2 = array_search('2', $casestudySettings);
            if ($key2)
                $sql .= " union ";

            $sql .= "SELECT casestudyfloor.floorChiefComplaint as ChiefComplaint,
            casestudyfloor.caseStudyFloorId as caseStudyId, 
            casestudyfloor.caseStudydate,
            casestudyfloor.ClinicianDate, 
            casestudyfloor.schoolDate, 
            casestudyfloor.clinician_comments, 
            casestudyfloor.school_comments, 
            casestudyfloor.isSendToCanvas, 
            casestudyfloor.studentId, 
            rotation.rotationId,
            rotation.title as rotationname, 
            student.firstName as studentfirstName,
            rankmaster.title as rank,
            student.lastName as studentlastName ,
            casestudyfloor.type,
            casestudyfloor.clinician_comments,
            casestudyfloor.school_comments,
            casestudyfloor.status as status,
            rotation.parentRotationId,rotation.locationId as rotationLocationId, courses.title AS Coursename,courses.locationId
            FROM casestudyfloor 
            LEFT JOIN student ON student.studentId = casestudyfloor.studentId 
            LEFT JOIN rotation ON casestudyfloor.rotationId=rotation.rotationId 
            LEFT JOIN courses ON rotation.courseId=courses.courseId
            INNER JOIN rankmaster ON rankmaster.rankId=student.rankId 
            where casestudyfloor.schoolId=" . $SchoolId;

            if ($rotationId)
                $sql .= "  AND  casestudyfloor.rotationId=" . $rotationId;
            if ($studentId > 0)
                $sql .= "  AND casestudyfloor.studentId=" . $studentId;
            if ($canvasStatus != '')
                $sql .= "  AND casestudyfloor.isSendToCanvas=" . $canvasStatus;
        }
        //check value in array is exist
        //ADULT CASE STUDY
        if (in_array("3", $casestudySettings)) {
            //get position in array
            $key3 = array_search('3', $casestudySettings);
            if ($key3)
                $sql .= " union ";

            $sql .= "SELECT 
                casestudyadult.adultChiefComplaint as ChiefComplaint,
                casestudyadult.caseStudyAdultId as caseStudyId,
                casestudyadult.caseStudydate  ,
                casestudyadult.ClinicianDate , 
                casestudyadult.schoolDate ,
                casestudyadult.clinician_comments, 
                casestudyadult.school_comments,
                casestudyadult.isSendToCanvas,
                casestudyadult.studentId,
                rotation.rotationId,
                rotation.title as rotationname,
                student.firstName as studentfirstName,
                rankmaster.title as rank,
                student.lastName as studentlastName ,
                casestudyadult.type,
                casestudyadult.clinician_comments,
                casestudyadult.school_comments,
                casestudyadult.status,
                rotation.parentRotationId,rotation.locationId as rotationLocationId, courses.title AS Coursename,courses.locationId

                FROM casestudyadult 
                LEFT JOIN student ON student.studentId = casestudyadult.studentId 
                LEFT JOIN rotation ON casestudyadult.rotationId=rotation.rotationId 
                LEFT JOIN courses ON rotation.courseId=courses.courseId
                INNER JOIN rankmaster ON rankmaster.rankId=student.rankId
				where casestudyadult.schoolId=" . $SchoolId;

            if ($rotationId)
                $sql .= "  AND  casestudyadult.rotationId=" . $rotationId;
            if ($studentId > 0)
                $sql .= "  AND casestudyadult.studentId=" . $studentId;
            if ($canvasStatus != '')
                $sql .= "  AND casestudyadult.isSendToCanvas=" . $canvasStatus;
        }

        // echo 'SELECT->'.$sql;exit;
        $rows = $objDB->GetResultset($sql);
        unset($objDB);
        return $rows;
    }

    function GetAllFloorCaseStudy($SchoolId, $rotationId, $studentId, $canvasStatus = '', $generateLimitString = "", $searchText = "")
    {
        $objDB = new clsDB();
        $rows = "";
        $sql = "SELECT casestudyfloor.floorChiefComplaint as ChiefComplaint,
                casestudyfloor.caseStudyFloorId as caseStudyId, 
                casestudyfloor.caseStudydate,
                casestudyfloor.ClinicianDate, 
                casestudyfloor.schoolDate, 
                casestudyfloor.clinician_comments, 
                casestudyfloor.school_comments, 
                casestudyfloor.isSendToCanvas, 
                casestudyfloor.studentId, 
                rotation.rotationId,
                rotation.title as rotationname, 
                student.studentId,
                student.firstName as studentfirstName,
                rankmaster.title as rankTitle,
                student.lastName as studentlastName ,
                casestudyfloor.type,
                casestudyfloor.status as status,
				rotation.parentRotationId,rotation.locationId as rotationLocationId, courses.title AS Coursename,courses.locationId
                FROM casestudyfloor 
                LEFT JOIN student ON student.studentId = casestudyfloor.studentId 
                LEFT JOIN rotation ON casestudyfloor.rotationId=rotation.rotationId 
                INNER JOIN rankmaster ON rankmaster.rankId=student.rankId 
				LEFT JOIN courses ON rotation.courseId=courses.courseId
				where casestudyfloor.schoolId=" . $SchoolId;

        if ($rotationId)
            $sql .= "  AND  casestudyfloor.rotationId=" . $rotationId;
        if ($studentId > 0)
            $sql .= "  AND casestudyfloor.studentId=" . $studentId;
        if ($canvasStatus != '')
            $sql .= "  AND casestudyfloor.isSendToCanvas=" . $canvasStatus;

        if ($searchText != "") {
            $sql .= " AND ( rotation.title LIKE '%" . $searchText . "%' OR casestudyfloor.type  LIKE '%" . $searchText . "%' OR CONCAT(student.firstName, ' ', student.lastName) LIKE '%" . $searchText . "%')";
        }
        $sql .= " ORDER BY casestudyfloor.caseStudydate DESC";
        $sql .= $generateLimitString;
        //echo 'SELECT->'.$sql;
        $rows = $objDB->GetResultset($sql);
        unset($objDB);
        return $rows;
    }
    function GetAllAdultCaseStudy($SchoolId, $rotationId, $studentId, $canvasStatus = '', $generateLimitString = "", $searchText = "")
    {
        $objDB = new clsDB();
        $rows = "";
        $sql = "SELECT 
                casestudyadult.adultChiefComplaint as ChiefComplaint,
                casestudyadult.caseStudyAdultId as caseStudyId,
                casestudyadult.caseStudydate  ,
                casestudyadult.ClinicianDate , 
                casestudyadult.schoolDate ,
                casestudyadult.clinician_comments, 
                casestudyadult.school_comments, 
                casestudyadult.isSendToCanvas, 
                casestudyadult.studentId, 
                rotation.rotationId,
                rotation.title as rotationname,
                student.studentId,
                student.firstName as studentfirstName,
                rankmaster.title as rankTitle,
                student.lastName as studentlastName ,
                casestudyadult.type,
                casestudyadult.status,
				rotation.parentRotationId,rotation.locationId as rotationLocationId, courses.title AS Coursename,courses.locationId
                FROM casestudyadult 
                LEFT JOIN student ON student.studentId = casestudyadult.studentId 
                LEFT JOIN rotation ON casestudyadult.rotationId=rotation.rotationId 
                INNER JOIN rankmaster ON rankmaster.rankId=student.rankId
				LEFT JOIN courses ON rotation.courseId=courses.courseId
				where casestudyadult.schoolId=" . $SchoolId;

        if ($rotationId)
            $sql .= "  AND  casestudyadult.rotationId=" . $rotationId;
        if ($studentId > 0)
            $sql .= "  AND casestudyadult.studentId=" . $studentId;
        if ($canvasStatus != '')
            $sql .= "  AND casestudyadult.isSendToCanvas=" . $canvasStatus;

        if ($searchText != "") {
            $sql .= " AND ( rotation.title LIKE '%" . $searchText . "%' OR casestudyadult.type  LIKE '%" . $searchText . "%' OR CONCAT(student.firstName, ' ', student.lastName) LIKE '%" . $searchText . "%')";
        }

        $sql .= " ORDER BY casestudyadult.caseStudydate DESC";
        $sql .=  $generateLimitString;
        // $sql .=" ORDER BY casestudyfloor.caseStudydate DESC";
        // echo 'SELECT->'.$sql;exit;
        $rows = $objDB->GetResultset($sql);
        unset($objDB);
        return $rows;
    }

    function SaveCaseStudyAdult($caseStudyId)
    {

        $objDB = new clsDB();
        $sql = '';
        if ($caseStudyId > 0) {

            $sql = "  UPDATE casestudyadult SET    
                            adultCohort ='" . addslashes($this->adultCohort) . "',
                            caseStudydate = '" . addslashes($this->caseStudydate) . "',  
                            adultAdmissionDate='" . addslashes($this->adultAdmissionDate) . "',
                            adultPtAge ='" . addslashes($this->adultPtAge) . "',
                            adultAPGAR='" . addslashes($this->adultAPGAR) . "',
                            gestationalAOB = '" . addslashes($this->gestationalAOB) . "',
                            studentDOB ='" . addslashes($this->studentDOB) . "',
                            adultMomsPARA = '" . addslashes($this->adultMomsPARA) . "',
                            adultParentSmokingHx ='" . addslashes($this->adultParentSmokingHx) . "',
                            adultSex='" . addslashes($this->adultSex) . "',
                            adultHt = '" . addslashes($this->adultHt) . "',
                            adultSmoking ='" . addslashes($this->adultSmoking) . "',
                            adultChiefComplaint='" . addslashes($this->adultChiefComplaint) . "',
                            adultPastMedicalHx='" . addslashes($this->adultPastMedicalHx) . "',
                            adultRespiratory ='" . addslashes($this->adultRespiratory) . "',
                            adultOtherPulmonaryProblems='" . addslashes($this->adultOtherPulmonaryProblems) . "',
                            adultHR ='" . addslashes($this->adultHR) . "',
                            adultSpontRR='" . addslashes($this->adultSpontRR) . "',
                            adultBP ='" . addslashes($this->adultBP) . "',
                            adultTemp='" . addslashes($this->adultTemp) . "',
                            adultSpO2 ='" . addslashes($this->adultSpO2) . "',
                            adultIO='" . addslashes($this->adultIO) . "',
                            adultBreathSounds ='" . addslashes($this->adultBreathSounds) . "',
                            adultLevelofActivity='" . addslashes($this->adultLevelofActivity) . "',
                            adultNa ='" . addslashes($this->adultNa) . "',
                            adultK='" . addslashes($this->adultK) . "',
                            adultCl ='" . addslashes($this->adultCl) . "',
                            adultWBC='" . addslashes($this->adultWBC) . "',
                            adultHgb ='" . addslashes($this->adultHgb) . "',
                            adultHct='" . addslashes($this->adultHct) . "',
                            adultCO2 ='" . addslashes($this->adultCO2) . "',
                            adultBUN='" . addslashes($this->adultBUN) . "',
                            adultGlucose ='" . addslashes($this->adultGlucose) . "',
                            adultPlatelets='" . addslashes($this->adultPlatelets) . "',
                            adultINR ='" . addslashes($this->adultINR) . "',
                            adultSputumCult='" . addslashes($this->adultSputumCult) . "',
                            adultCreatinine ='" . addslashes($this->adultCreatinine) . "',
                            adultLabInterpretation='" . addslashes($this->adultLabInterpretation) . "',
                            adultXRayInterpretation ='" . addslashes($this->adultXRayInterpretation) . "',
                            adultETT  = '" . addslashes($this->adultETT) . "',
                            adultPosition  = '" . addslashes($this->adultPosition) . "',
                            adultTrachType  = '" . addslashes($this->adultTrachType) . "',
                            adult1  = '" . addslashes($this->adult1) . "',
                            adultCuffPressure  = '" . addslashes($this->adultCuffPressure) . "',
                            
                            adultpH  = '" . addslashes($this->adultpH) . "',
                            adultPaCO2  = '" . addslashes($this->adultPaCO2) . "',
                            adultHCO3  = '" . addslashes($this->adultHCO3) . "',
                            adultSvO2  = '" . addslashes($this->adultSvO2) . "',
                            

                            adultCO  = '" . addslashes($this->adultCO) . "',
                            adultPAP  = '" . addslashes($this->adultPAP) . "',
                            adultPaO2  = '" . addslashes($this->adultPaO2) . "',
                            adultSaO2  = '" . addslashes($this->adultSaO2) . "',
                            adultFiO2Lpm  = '" . addslashes($this->adultFiO2Lpm) . "',
                            adultCVP  = '" . addslashes($this->adultCVP) . "',
                            adultPCWP  = '" . addslashes($this->adultPCWP) . "',
                            adultICP  = '" . addslashes($this->adultICP) . "',
                            adultDateBloodGas  = '" . addslashes($this->adultDateBloodGas) . "',
                            adultInterpretationHemodynamics  = '" . addslashes($this->adultInterpretationHemodynamics) . "',

                            adultInterpretationABG  = '" . addslashes($this->adultInterpretationABG) . "',
                            adultEKGResults  = '" . addslashes($this->adultEKGResults) . "',
                            adultInterpretationPAO2  = '" . addslashes($this->adultInterpretationPAO2) . "',
                            adultInterpretationAO2  = '" . addslashes($this->adultInterpretationAO2) . "',
                            adultInterpretationCaO2  = '" . addslashes($this->adultInterpretationCaO2) . "',
                            adultInterpretationPFRatio  = '" . addslashes($this->adultInterpretationPFRatio) . "',
                            adultVentilator  = '" . addslashes($this->adultVentilator) . "',
                            adultFiO2  = '" . addslashes($this->adultFiO2) . "',
                            adultPiP  = '" . addslashes($this->adultPiP) . "',
                            adultPlat  = '" . addslashes($this->adultPlat) . "',
                            adultRR  = '" . addslashes($this->adultRR) . "',
                            adultMode  = '" . addslashes($this->adultMode) . "',
                            adultPSupport  = '" . addslashes($this->adultPSupport) . "',
                            adultMAP  = '" . addslashes($this->adultMAP) . "',
                            adultVE  = '" . addslashes($this->adultVE) . "',
                            adultSetRate  = '" . addslashes($this->adultSetRate) . "',
                            adultMaxFlow  = '" . addslashes($this->adultMaxFlow) . "',
                            adultSpontVt  = '" . addslashes($this->adultSpontVt) . "',
                            adultIE  = '" . addslashes($this->adultIE) . "',
                            adultSetVt  = '" . addslashes($this->adultSetVt) . "',
                            adultFlowSens  = '" . addslashes($this->adultFlowSens) . "',
                            adultCsta  = '" . addslashes($this->adultCsta) . "',
                            adultRaw  = '" . addslashes($this->adultRaw) . "',
                            adultVte  = '" . addslashes($this->adultVte) . "',
                            adultIBWVt  = '" . addslashes($this->adultIBWVt) . "',
                            adultItime  = '" . addslashes($this->adultItime) . "',
                            adultPEEPCPAP  = '" . addslashes($this->adultPEEPCPAP) . "',
                            adultHumidityTemp  = '" . addslashes($this->adultHumidityTemp) . "',
                            adultSputumAmount  = '" . addslashes($this->adultSputumAmount) . "',
                            adultOtherSettings  = '" . addslashes($this->adultOtherSettings) . "',
                            adultRecommendations  = '" . addslashes($this->adultRecommendations) . "',
                            adultLowHiPiP  = '" . addslashes($this->adultLowHiPiP) . "',
                            adultLowHiVte  = '" . addslashes($this->adultLowHiVte) . "',
                            adultLowHiVe  = '" . addslashes($this->adultLowHiVe) . "',
                            adultLowHiRR  = '" . addslashes($this->adultLowHiRR) . "',
                            adultApneaAlert  = '" . addslashes($this->adultApneaAlert) . "',
                            adultOtherAlarms  = '" . addslashes($this->adultOtherAlarms) . "',
                            adultIPAP  = '" . addslashes($this->adultIPAP) . "',
                            adultEPAP  = '" . addslashes($this->adultEPAP) . "',
                            adultRate  = '" . addslashes($this->adultRate) . "',
                            adultFiO21  = '" . addslashes($this->adultFiO21) . "',
                            adultItimeSetting  = '" . addslashes($this->adultItimeSetting) . "',
                            adultRise  = '" . addslashes($this->adultRise) . "',
                            adultRamp  = '" . addslashes($this->adultRamp) . "',
                            adultFVC  = '" . addslashes($this->adultFVC) . "',
                            adultFEF25  = '" . addslashes($this->adultFEF25) . "',
                            adultFEV1  = '" . addslashes($this->adultFEV1) . "',
                            adultPEFR  = '" . addslashes($this->adultPEFR) . "',
                            adultFEV1FVC  = '" . addslashes($this->adultFEV1FVC) . "',
                            adultLungVolumes  = '" . addslashes($this->adultLungVolumes) . "',
                            adultInterpretationPFT  = '" . addslashes($this->adultInterpretationPFT) . "',
                            adultMedicationsUse1  = '" . addslashes($this->adultMedicationsUse1) . "',
                            adultModificationCarePlan1  = '" . addslashes($this->adultModificationCarePlan1) . "',
                            adultMedicationsUse2  = '" . addslashes($this->adultMedicationsUse2) . "',
                            adultModificationCarePlan2  = '" . addslashes($this->adultModificationCarePlan2) . "',
                            adultMedicationsUse3 = '" . addslashes($this->adultMedicationsUse3) . "',  
                            adultModificationCarePlan3 = '" . addslashes($this->adultModificationCarePlan3) . "',
                            adultMedicationsUse4 = '" . addslashes($this->adultMedicationsUse4) . "',						
                            adultModificationCarePlan4 = '" . addslashes($this->adultModificationCarePlan4) . "',
                            adultMedicationsUse5 = '" . addslashes($this->adultMedicationsUse5) . "',  
                            adultModificationCarePlan5 = '" . addslashes($this->adultModificationCarePlan5) . "',
                            adultMedicationsUse6 = '" . addslashes($this->adultMedicationsUse6) . "',						
                            adultModificationCarePlan6 = '" . addslashes($this->adultModificationCarePlan6) . "',
                            adultMedicationsUse7 = '" . addslashes($this->adultMedicationsUse7) . "',  
                            adultModificationCarePlan7 = '" . addslashes($this->adultModificationCarePlan7) . "',
                            adultMedicationsUse8 = '" . addslashes($this->adultMedicationsUse8) . "',						
                            adultModificationCarePlan8 = '" . addslashes($this->adultModificationCarePlan8) . "',
                            adultMedicationsUse9 = '" . addslashes($this->adultMedicationsUse9) . "',
                            adultModificationCarePlan9 = '" . addslashes($this->adultModificationCarePlan9) . "',
                            adultMedicationsUse10 = '" . addslashes($this->adultMedicationsUse10) . "',  
                            adultModificationCarePlan10 = '" . addslashes($this->adultModificationCarePlan10) . "',
                            adultMedicationsUse11 = '" . addslashes($this->adultMedicationsUse11) . "',  
                            adultModificationCarePlan11 = '" . addslashes($this->adultModificationCarePlan11) . "',
                            adultMedicationsUse12 = '" . addslashes($this->adultMedicationsUse12) . "',  
                            adultModificationCarePlan12 = '" . addslashes($this->adultModificationCarePlan12) . "',
                            adultMedicationsUse13 = '" . addslashes($this->adultMedicationsUse13) . "',  
                            adultModificationCarePlan13 = '" . addslashes($this->adultModificationCarePlan13) . "',
                            adultMedicationsUse14 = '" . addslashes($this->adultMedicationsUse14) . "',  
                            adultModificationCarePlan14 = '" . addslashes($this->adultModificationCarePlan14) . "',
                            adultMedicationsUse15 = '" . addslashes($this->adultMedicationsUse15) . "',  
                            adultModificationCarePlan15 = '" . addslashes($this->adultModificationCarePlan15) . "',
                            adultMedicationsUseList = '" . addslashes($this->adultMedicationsUseList) . "',
                            adultModificationCarePlanList = '" . addslashes($this->adultModificationCarePlanList) . "',
                             adultSuction  = '" . addslashes($this->adultSuction) . "',
                              adultCough  = '" . addslashes($this->adultCough) . "',
                              studentcomments  = '" . addslashes($this->studentcomments) . "'
                            where caseStudyAdultId =" . $caseStudyId;
            // echo $sql;exit;
            $objDB->ExecuteQuery($sql);
        } else {
            $sql = "INSERT INTO casestudyadult (schoolId,studentId, rotationId,caseStudydate, type, adultCohort, adultAdmissionDate,
                                    adultPtAge, adultSex, adultHt, adultSmoking, adultChiefComplaint, adultAPGAR, gestationalAOB, studentDOB, adultMomsPARA, adultParentSmokingHx, adultRespiratory, adultPastMedicalHx, adultOtherPulmonaryProblems,
                                    adultHR, adultSpontRR, adultBP, adultTemp,adultSpO2, adultIO, adultBreathSounds, adultLevelofActivity, adultNa, adultK, adultCl,
                                    adultWBC, adultHgb, adultHct, adultCO2, adultBUN, adultGlucose, adultPlatelets, adultINR, adultSputumCult, adultCreatinine, adultLabInterpretation, adultXRayInterpretation,
                                    adultETT, adultPosition, adultTrachType, adult1, adultCuffPressure, adultpH, adultPaCO2, adultHCO3, adultSvO2, adultCO, adultPAP, adultPaO2, adultSaO2,adultFiO2Lpm,
                                    adultCVP, adultPCWP, adultICP, adultDateBloodGas, adultInterpretationHemodynamics, adultInterpretationABG, adultEKGResults, adultInterpretationPAO2, adultInterpretationAO2,
                                    adultInterpretationCaO2, adultInterpretationPFRatio, adultVentilator, adultFiO2, adultPiP, adultPlat, adultRR, adultMode, adultPSupport, adultMAP,
                                    adultVE, adultSetRate, adultMaxFlow, adultSpontVt, adultIE, adultSetVt, adultFlowSens, adultCsta, adultRaw, adultVte, adultIBWVt, adultItime, adultPEEPCPAP,
                                    adultHumidityTemp, adultSputumAmount, adultOtherSettings, adultRecommendations, adultLowHiPiP, adultLowHiVte, adultLowHiVe, adultLowHiRR, adultApneaAlert, adultOtherAlarms, adultIPAP, adultEPAP, adultRate, adultFiO21, adultItimeSetting, adultRise, 
                                    adultRamp, adultFVC, adultFEF25, adultFEV1, adultPEFR, adultFEV1FVC, adultLungVolumes, adultInterpretationPFT, adultMedicationsUse1, adultModificationCarePlan1, adultMedicationsUse2, adultModificationCarePlan2,
                                    adultMedicationsUse3, adultModificationCarePlan3, adultMedicationsUse4, adultModificationCarePlan4, adultMedicationsUse5, adultModificationCarePlan5, adultMedicationsUse6, adultModificationCarePlan6,
                                    adultMedicationsUse7, adultModificationCarePlan7, adultMedicationsUse8, adultModificationCarePlan8, adultMedicationsUse9, adultModificationCarePlan9,  adultMedicationsUse10, adultModificationCarePlan10,adultMedicationsUse11, adultModificationCarePlan11, adultMedicationsUse12, adultModificationCarePlan12,
                                     adultMedicationsUse13, adultModificationCarePlan13, adultMedicationsUse14, adultModificationCarePlan14, adultMedicationsUse15, adultModificationCarePlan15,adultMedicationsUseList,adultModificationCarePlanList,
                                    adultCough, adultSuction,studentcomments) 
                    VALUES ('" . addslashes($this->schoolId) . "',
                            '" . addslashes($this->studentId) . "',
                            '" . addslashes($this->rotationId) . "',
                            '" . addslashes($this->caseStudydate) . "',	
                            '" . addslashes($this->type) . "',	                            
                            '" . addslashes($this->adultCohort) . "',						
                            '" . addslashes($this->adultAdmissionDate) . "',
                            '" . addslashes($this->adultPtAge) . "',
                            '" . addslashes($this->adultSex) . "',
                            '" . addslashes($this->adultHt) . "',
                            '" . addslashes($this->adultSmoking) . "',	
                            '" . addslashes($this->adultChiefComplaint) . "',	
                            '" . addslashes($this->adultAPGAR) . "',
                            '" . addslashes($this->gestationalAOB) . "',
                            '" . addslashes($this->studentDOB) . "',
                            '" . addslashes($this->adultMomsPARA) . "',
                            '" . addslashes($this->adultParentSmokingHx) . "',
                            '" . addslashes($this->adultRespiratory) . "',
                            '" . addslashes($this->adultPastMedicalHx) . "',						
                            '" . addslashes($this->adultOtherPulmonaryProblems) . "',
                            '" . addslashes($this->adultHR) . "',
                            '" . addslashes($this->adultSpontRR) . "',
                            '" . addslashes($this->adultBP) . "',
                            '" . addslashes($this->adultTemp) . "',	
                            '" . addslashes($this->adultSpO2) . "',	
                            '" . addslashes($this->adultIO) . "',
                            '" . addslashes($this->adultBreathSounds) . "',						
                            '" . addslashes($this->adultLevelofActivity) . "',
                            '" . addslashes($this->adultNa) . "',
                            '" . addslashes($this->adultK) . "',
                            '" . addslashes($this->adultCl) . "',	
                            '" . addslashes($this->adultWBC) . "',	
                            '" . addslashes($this->adultHgb) . "',
                            '" . addslashes($this->adultHct) . "',						
                            '" . addslashes($this->adultCO2) . "',
                            '" . addslashes($this->adultBUN) . "',
                            '" . addslashes($this->adultGlucose) . "',
                            '" . addslashes($this->adultPlatelets) . "',	
                            '" . addslashes($this->adultINR) . "',	
                            '" . addslashes($this->adultSputumCult) . "',
                            '" . addslashes($this->adultCreatinine) . "',						
                            '" . addslashes($this->adultLabInterpretation) . "',
                            '" . addslashes($this->adultXRayInterpretation) . "',
                            '" . addslashes($this->adultETT) . "',
                            '" . addslashes($this->adultPosition) . "',	
                            '" . addslashes($this->adultTrachType) . "',	
                            '" . addslashes($this->adult1) . "',
                            '" . addslashes($this->adultCuffPressure) . "',						
                            '" . addslashes($this->adultpH) . "',
                            '" . addslashes($this->adultPaCO2) . "',
                            '" . addslashes($this->adultHCO3) . "',
                            '" . addslashes($this->adultSvO2) . "',	
                            '" . addslashes($this->adultCO) . "',	
                            '" . addslashes($this->adultPAP) . "',
                            '" . addslashes($this->adultPaO2) . "',						
                            '" . addslashes($this->adultSaO2) . "',
                            '" . addslashes($this->adultFiO2Lpm) . "',
                            '" . addslashes($this->adultCVP) . "',
                            '" . addslashes($this->adultPCWP) . "',	
                            '" . addslashes($this->adultICP) . "',	
                            '" . addslashes($this->adultDateBloodGas) . "',
                            '" . addslashes($this->adultInterpretationHemodynamics) . "',						
                            '" . addslashes($this->adultInterpretationABG) . "',
                            '" . addslashes($this->adultEKGResults) . "',
                            '" . addslashes($this->adultInterpretationPAO2) . "',
                            '" . addslashes($this->adultInterpretationAO2) . "',	
                            '" . addslashes($this->adultInterpretationCaO2) . "',	
                            '" . addslashes($this->adultInterpretationPFRatio) . "',
                            '" . addslashes($this->adultVentilator) . "',						
                            '" . addslashes($this->adultFiO2) . "',
                            '" . addslashes($this->adultPiP) . "',
                            '" . addslashes($this->adultPlat) . "',	
                            '" . addslashes($this->adultRR) . "',	
                            '" . addslashes($this->adultMode) . "',
                            '" . addslashes($this->adultPSupport) . "',						
                            '" . addslashes($this->adultMAP) . "',
                            '" . addslashes($this->adultVE) . "',	
                            '" . addslashes($this->adultSetRate) . "',
                            '" . addslashes($this->adultMaxFlow) . "',						
                            '" . addslashes($this->adultSpontVt) . "'	,
                             '" . addslashes($this->adultIE) . "',
                            '" . addslashes($this->adultSetVt) . "',						
                            '" . addslashes($this->adultFlowSens) . "',
                            '" . addslashes($this->adultCsta) . "',
                            '" . addslashes($this->adultRaw) . "',	
                            '" . addslashes($this->adultVte) . "',	
                            '" . addslashes($this->adultIBWVt) . "',
                            '" . addslashes($this->adultItime) . "',						
                            '" . addslashes($this->adultPEEPCPAP) . "',
                            '" . addslashes($this->adultHumidityTemp) . "',	
                            '" . addslashes($this->adultSputumAmount) . "',
                            '" . addslashes($this->adultOtherSettings) . "',
                            '" . addslashes($this->adultRecommendations) . "',
                            '" . addslashes($this->adultLowHiPiP) . "',						
                            '" . addslashes($this->adultLowHiVte) . "',
                             '" . addslashes($this->adultLowHiVe) . "',
                            '" . addslashes($this->adultLowHiRR) . "',						
                            '" . addslashes($this->adultApneaAlert) . "',
                            '" . addslashes($this->adultOtherAlarms) . "',
                            '" . addslashes($this->adultIPAP) . "',
                            '" . addslashes($this->adultEPAP) . "',	
                            '" . addslashes($this->adultRate) . "',	
                            '" . addslashes($this->adultFiO21) . "',
                             '" . addslashes($this->adultItimeSetting) . "',
                            '" . addslashes($this->adultRise) . "',						
                            '" . addslashes($this->adultRamp) . "',
                            '" . addslashes($this->adultFVC) . "',	
                            '" . addslashes($this->adultFEF25) . "',
                            '" . addslashes($this->adultFEV1) . "',						
                            '" . addslashes($this->adultPEFR) . "',
                            '" . addslashes($this->adultFEV1FVC) . "',
                            '" . addslashes($this->adultLungVolumes) . "',						
                            '" . addslashes($this->adultInterpretationPFT) . "',
                            '" . addslashes($this->adultMedicationsUse1) . "',	
                            '" . addslashes($this->adultModificationCarePlan1) . "',
                            '" . addslashes($this->adultMedicationsUse2) . "',						
                            '" . addslashes($this->adultModificationCarePlan2) . "',
                            '" . addslashes($this->adultMedicationsUse3) . "',	
                            '" . addslashes($this->adultModificationCarePlan3) . "',
                            '" . addslashes($this->adultMedicationsUse4) . "',						
                            '" . addslashes($this->adultModificationCarePlan4) . "'	,
                            '" . addslashes($this->adultMedicationsUse5) . "',	
                            '" . addslashes($this->adultModificationCarePlan5) . "',
                            '" . addslashes($this->adultMedicationsUse6) . "',						
                            '" . addslashes($this->adultModificationCarePlan6) . "'	,
                            '" . addslashes($this->adultMedicationsUse7) . "',	
                            '" . addslashes($this->adultModificationCarePlan7) . "',
                            '" . addslashes($this->adultMedicationsUse8) . "',						
                            '" . addslashes($this->adultModificationCarePlan8) . "'	,
                            '" . addslashes($this->adultMedicationsUse9) . "',	
                            '" . addslashes($this->adultModificationCarePlan9) . "',
                            '" . addslashes($this->adultMedicationsUse10) . "',						
                            '" . addslashes($this->adultModificationCarePlan10) . "',
                            '" . addslashes($this->adultMedicationsUse11) . "',						
                            '" . addslashes($this->adultModificationCarePlan11) . "',
                            '" . addslashes($this->adultMedicationsUse12) . "',						
                            '" . addslashes($this->adultModificationCarePlan12) . "',
                            '" . addslashes($this->adultMedicationsUse13) . "',						
                            '" . addslashes($this->adultModificationCarePlan13) . "',
                            '" . addslashes($this->adultMedicationsUse14) . "',						
                            '" . addslashes($this->adultModificationCarePlan14) . "',
                            '" . addslashes($this->adultMedicationsUse15) . "',						
                            '" . addslashes($this->adultModificationCarePlan15) . "',
                            '" . addslashes($this->adultMedicationsUseList) . "',
                            '" . addslashes($this->adultModificationCarePlanList) . "',
                             '" . addslashes($this->adultCough) . "',
                              '" . addslashes($this->adultSuction) . "',
                              '" . addslashes($this->studentcomments) . "'
                            
                                                    
                            )";
            // echo 'Insert->'.$sql;exit;
            $caseStudyId = $objDB->ExecuteInsertQuery($sql);
        }

        unset($objDB);
        return $caseStudyId;
    }

    function GetFloorCaseStudyForStudent($caseStudyId)
    {
        $objDB = new clsDB();
        $rows = "";
        $sql = "SELECT casestudyfloor.*,student.firstName,student.lastName,rotation.parentRotationId,rotation.locationId as rotationLocationId, courses.title AS Coursename,courses.locationId FROM `casestudyfloor` 
        inner Join student on student.studentId = casestudyfloor.studentId  
		LEFT JOIN rotation ON casestudyfloor.`rotationId` = rotation.`rotationId` 
		LEFT JOIN courses ON rotation.courseId=courses.courseId
        WHERE caseStudyFloorId =" . $caseStudyId;
        //echo $sql;
        $rows = $objDB->GetDataRow($sql);
        unset($objDB);
        return $rows;
    }

    function GetflooMedicationsUseForStudent($caseStudyId)
    {
        $objDB = new clsDB();
        $rows = "";
        $sql = "SELECT caseStudyFloorId,flooMedicationsUse1,flooMedicationsUse2,flooMedicationsUse3,flooMedicationsUse4,flooMedicationsUse5,flooMedicationsUse6,flooMedicationsUse7,flooMedicationsUse8,flooMedicationsUse9,flooMedicationsUse10,flooMedicationsUse11,flooMedicationsUse12,flooMedicationsUse13,flooMedicationsUse14,flooMedicationsUse15,floorModificationCarePlan1,floorModificationCarePlan2,floorModificationCarePlan3,floorModificationCarePlan4,floorModificationCarePlan5,floorModificationCarePlan6,floorModificationCarePlan7,floorModificationCarePlan8,floorModificationCarePlan9,floorModificationCarePlan10,floorModificationCarePlan11,floorModificationCarePlan12,floorModificationCarePlan13,floorModificationCarePlan14,floorModificationCarePlan15  FROM `casestudyfloor` ";
        // echo $sql;exit;
        $rows = $objDB->GetResultSet($sql);
        unset($objDB);
        return $rows;
    }
    function GetAdultMedicationsUseForStudent($caseStudyId)
    {
        $objDB = new clsDB();
        $rows = "";
        $sql = "SELECT caseStudyAdultId,adultMedicationsUse1,adultMedicationsUse2,adultMedicationsUse3,adultMedicationsUse4,adultMedicationsUse5,adultMedicationsUse6,adultMedicationsUse7,adultMedicationsUse8,adultMedicationsUse9,adultMedicationsUse10,adultMedicationsUse11,adultMedicationsUse12,adultMedicationsUse13,adultMedicationsUse14,adultMedicationsUse15,adultModificationCarePlan1,adultModificationCarePlan2,adultModificationCarePlan3,adultModificationCarePlan4,adultModificationCarePlan5,adultModificationCarePlan6,adultModificationCarePlan7,adultModificationCarePlan8,adultModificationCarePlan9,adultModificationCarePlan10,adultModificationCarePlan11,adultModificationCarePlan12,adultModificationCarePlan13,adultModificationCarePlan14,adultModificationCarePlan15  FROM `casestudyadult` ";
        // echo $sql;exit;
        $rows = $objDB->GetResultSet($sql);
        unset($objDB);
        return $rows;
    }

    function GetAdultCaseStudyForStudent($caseStudyId)
    {
        $objDB = new clsDB();
        $rows = "";
        $sql = "SELECT casestudyadult.*,student.firstName,student.lastName,rotation.parentRotationId,rotation.locationId as rotationLocationId, courses.title AS Coursename,courses.locationId FROM `casestudyadult`
        inner Join student on student.studentId = casestudyadult.studentId 
		LEFT JOIN rotation ON casestudyadult.`rotationId` = rotation.`rotationId` 
		LEFT JOIN courses ON rotation.courseId=courses.courseId
         WHERE caseStudyAdultId =" . $caseStudyId;

        //echo $sql;
        $rows = $objDB->GetDataRow($sql);
        unset($objDB);
        return $rows;
    }

    function DeleteFloorCaseStudy($caseStudyId)
    {
        $objDB = new clsDB();
        $sql = "DELETE  FROM casestudyfloor 
					WHERE caseStudyFloorId=" . $caseStudyId;
        //ECHO 'DELETE->'.$sql;EXIT;
        $result = $objDB->ExecuteQuery($sql);
        unset($objDB);
        return $result;
    }

    function DeleteAdultCaseStudy($caseStudyId)
    {
        $objDB = new clsDB();
        $sql = "DELETE  FROM casestudyadult 
					WHERE caseStudyAdultId=" . $caseStudyId;
        //ECHO 'DELETE->'.$sql;EXIT;
        $result = $objDB->ExecuteQuery($sql);
        unset($objDB);
        return $result;
    }

    function SaveClinicianCaseStudyFloor($caseStudyId)
    {
        $objDB = new clsDB();
        $sql = '';
        if ($caseStudyId > 0) {
            // echo $this->clinician_comments;;exit;
            $sql = "UPDATE casestudyfloor SET 
                        clinicianId = '" . addslashes($this->clinicianId) . "',						
                        ClinicianDate = '" . addslashes($this->ClinicianDate) . "',
                        clinician_comments = '" . addslashes($this->clinician_comments) . "'
                        Where caseStudyFloorId= " . $caseStudyId;
            // echo 'update->'.$sql;exit;
            $objDB->ExecuteQuery($sql);
        }

        unset($objDB);
        return $caseStudyId;
    }

    function SaveClinicianCaseStudyAdult($caseStudyId)
    {
        $objDB = new clsDB();
        $sql = '';
        if ($caseStudyId > 0) {

            $sql = "UPDATE casestudyadult SET 
                        clinicianId = '" . addslashes($this->clinicianId) . "',						
                        ClinicianDate = '" . addslashes($this->ClinicianDate) . "',
                        clinician_comments = '" . addslashes($this->clinician_comments) . "'
                        Where caseStudyAdultId= " . $caseStudyId;
            //echo 'update->'.$sql;exit;
            $objDB->ExecuteQuery($sql);
        }

        unset($objDB);
        return $caseStudyId;
    }

    function SaveAdminCaseStudyAdult($caseStudyId)
    {
        $objDB = new clsDB();
        $sql = '';
        if ($caseStudyId > 0) {

            $sql = "UPDATE casestudyadult SET 					
                        schoolDate = '" . addslashes($this->schoolDate) . "',
                        school_comments = '" . addslashes($this->school_comments) . "'
                        Where caseStudyAdultId= " . $caseStudyId;
            //echo 'update->'.$sql;exit;
            $objDB->ExecuteQuery($sql);
        }

        unset($objDB);
        return $caseStudyId;
    }

    function SaveAdminCaseStudyFloor($caseStudyId)
    {
        $objDB = new clsDB();
        $sql = '';
        if ($caseStudyId > 0) {

            $sql = "UPDATE casestudyfloor SET 					
                        schoolDate = '" . addslashes($this->schoolDate) . "',
                        school_comments = '" . addslashes($this->school_comments) . "'
                        Where caseStudyFloorId= " . $caseStudyId;
            // echo 'update->'.$sql;exit;
            $objDB->ExecuteQuery($sql);
        }

        unset($objDB);
        return $caseStudyId;
    }

    // PACR Case Study
    function SaveCaseStudyPACR($caseStudyId)
    {

        $objDB = new clsDB();
        $sql = '';
        if ($caseStudyId > 0) {

            $sql = "  UPDATE casestudypacr SET  
                        rotationId =    '" . addslashes($this->rotationId) . "',
                        hospitalSiteId = '" . addslashes($this->hospitalSiteId) . "',
                        hospitalSiteUnitId = '" . addslashes($this->hospitalSiteUnitId) . "',
                        caseStudydate ='" . addslashes($this->caseStudydate) . "', 
                        pacrCohort = '" . addslashes($this->pacrCohort) . "',
                        pacrWeek = '" . addslashes($this->pacrWeek) . "',
                        pacrAdmissionDate = '" . addslashes($this->pacrAdmissionDate) . "',
                        pacrPtAge = '" . addslashes($this->pacrPtAge) . "',	
                        pacrSex = '" . addslashes($this->pacrSex) . "',
                        pacrCC = '" . addslashes($this->pacrCC) . "',
                        pacrDx = '" . addslashes($this->pacrDx) . "',
                        pacrHx = '" . addslashes($this->pacrHx) . "',	
                        pacrPhysicalExam = '" . addslashes($this->pacrPhysicalExam) . "',
                        pacrBreathSounds = '" . addslashes($this->pacrBreathSounds) . "',
                        pacrVitals = '" . addslashes($this->pacrVitals) . "',
                        pacrCBCDetails = '" . addslashes($this->pacrCBCDetails) . "',	
                        pacrCBCCheckValues = '" . addslashes($this->pacrCBCCheckValues) . "',
                        pacrInterpretPatient = '" . addslashes($this->pacrInterpretPatient) . "',
                        pacrInterpretABGStatus = '" . addslashes($this->pacrInterpretABGStatus) . "',
                        pacrAaFormula = '" . addslashes($this->pacrAaFormula) . "',	

                        pacrAaNormalvalue = '" . addslashes($this->pacrAaNormalvalue) . "',
                        pacrAaIsNormal = '" . addslashes($this->pacrAaIsNormal) . "',
                        pacrAaAbnormal = '" . addslashes($this->pacrAaAbnormal) . "',
                        pacrCaO2 = '" . addslashes($this->pacrCaO2) . "',	
                        pacrNormalCaO2= '" . addslashes($this->pacrNormalCaO2) . "',
                        pacrCaO2Isnormal = '" . addslashes($this->pacrCaO2Isnormal) . "',
                        pacrCaO2Abnormal = '" . addslashes($this->pacrCaO2Abnormal) . "',
                        pacrProcedures = '" . addslashes($this->pacrProcedures) . "',
                        pacrPFTList = '" . addslashes($this->pacrPFTList) . "',	
                        pacrCXRList = '" . addslashes($this->pacrCXRList) . "',
                        pacrMedications = '" . addslashes($this->pacrMedications) . "',
                        pacrClassifications = '" . addslashes($this->pacrClassifications) . "',
                        pacrModeOfctions = '" . addslashes($this->pacrModeOfctions) . "',	

                        pacrDosage = '" . addslashes($this->pacrDosage) . "',
                        pacrRespiratoryOrders = '" . addslashes($this->pacrRespiratoryOrders) . "',
                        pacrIndications = '" . addslashes($this->pacrIndications) . "',
                        pacrGoals = '" . addslashes($this->pacrGoals) . "',	


                        pacrRTOrder= '" . addslashes($this->pacrRTOrder) . "',
                        pacrVentilator = '" . addslashes($this->pacrVentilator) . "',
                        pacrABGResults = '" . addslashes($this->pacrABGResults) . "',
                        pacrHemodynamicsList = '" . addslashes($this->pacrHemodynamicsList) . "',	
                        pacrHemodynamicCheckvalues = '" . addslashes($this->pacrHemodynamicCheckvalues) . "',
                        pacrSummery = '" . addslashes($this->pacrSummery) . "',
                        studentcomments = '" . addslashes($this->studentcomments) . "',
                        updatedBy = '" . addslashes($this->updatedBy) . "',
                        updatedDate = '" . addslashes($this->updatedDate) . "'
                        where caseStudyPacrId =" . $caseStudyId;

            // echo $sql;exit;
            $objDB->ExecuteQuery($sql);
        } else {
            $sql = "INSERT INTO casestudypacr 
                (`schoolId`, `studentId`, `rotationId`, `hospitalSiteId`, `hospitalSiteUnitId`,  `caseStudydate`, `type`, `pacrCohort`, `pacrWeek`, `pacrAdmissionDate`, `pacrPtAge`, `pacrSex`, `pacrCC`, `pacrDx`, `pacrHx`, `pacrPhysicalExam`, `pacrBreathSounds`, `pacrVitals`, `pacrCBCDetails`, `pacrCBCCheckValues`, `pacrInterpretPatient`, `pacrInterpretABGStatus`, `pacrAaFormula`, `pacrAaNormalvalue`, `pacrAaIsNormal`, `pacrAaAbnormal`, `pacrCaO2`, `pacrNormalCaO2`, `pacrCaO2Isnormal`, `pacrCaO2Abnormal`, `pacrProcedures`, `pacrPFTList`, `pacrCXRList`, `pacrMedications`, `pacrClassifications`, `pacrModeOfctions`, `pacrDosage`, `pacrRespiratoryOrders`, `pacrIndications`, `pacrGoals`, `pacrRTOrder`, `pacrVentilator`, `pacrABGResults`, `pacrHemodynamicsList`, `pacrHemodynamicCheckvalues`, `pacrSummery`, `studentcomments`,  `createdBy`, `createdDate`) 
                    VALUES ('" . addslashes($this->schoolId) . "',
                            '" . addslashes($this->studentId) . "',
                            '" . addslashes($this->rotationId) . "',
                            '" . addslashes($this->hospitalSiteId) . "',
                            '" . addslashes($this->hospitalSiteUnitId) . "',

                            '" . addslashes($this->caseStudydate) . "', 
                            '" . addslashes($this->type) . "',                              
                            '" . addslashes($this->pacrCohort) . "',                        
                            '" . addslashes($this->pacrWeek) . "',
                            '" . addslashes($this->pacrAdmissionDate) . "',
                            '" . addslashes($this->pacrPtAge) . "',
                            '" . addslashes($this->pacrSex) . "',
                            '" . addslashes($this->pacrCC) . "',    
                            '" . addslashes($this->pacrDx) . "',    
                            '" . addslashes($this->pacrHx) . "',
                            '" . addslashes($this->pacrPhysicalExam) . "',
                            '" . addslashes($this->pacrBreathSounds) . "',
                            '" . addslashes($this->pacrVitals) . "',
                            '" . addslashes($this->pacrCBCDetails) . "',
                            '" . addslashes($this->pacrCBCCheckValues) . "',
                            '" . addslashes($this->pacrInterpretPatient) . "',                      
                            '" . addslashes($this->pacrInterpretABGStatus) . "',
                            '" . addslashes($this->pacrAaFormula) . "',
                            '" . addslashes($this->pacrAaNormalvalue) . "',
                            '" . addslashes($this->pacrAaIsNormal) . "',
                            '" . addslashes($this->pacrAaAbnormal) . "',    
                            '" . addslashes($this->pacrCaO2) . "',    
                            '" . addslashes($this->pacrNormalCaO2) . "',    
                            '" . addslashes($this->pacrCaO2Isnormal) . "',
                            '" . addslashes($this->pacrCaO2Abnormal) . "',
                            '" . addslashes($this->pacrProcedures) . "',                        
                            '" . addslashes($this->pacrPFTList) . "',
                            '" . addslashes($this->pacrCXRList) . "',
                            '" . addslashes($this->pacrMedications) . "',
                            '" . addslashes($this->pacrClassifications) . "',   
                            '" . addslashes($this->pacrModeOfctions) . "',  
                            '" . addslashes($this->pacrDosage) . "',
                            '" . addslashes($this->pacrRespiratoryOrders) . "',                     
                            '" . addslashes($this->pacrIndications) . "',
                            '" . addslashes($this->pacrGoals) . "',
                            '" . addslashes($this->pacrRTOrder) . "',
                            '" . addslashes($this->pacrVentilator) . "',    
                            '" . addslashes($this->pacrABGResults) . "',    
                            '" . addslashes($this->pacrHemodynamicsList) . "',
                            '" . addslashes($this->pacrHemodynamicCheckvalues) . "',                        
                            '" . addslashes($this->pacrSummery) . "',
                            '" . addslashes($this->studentcomments) . "',
                            '" . addslashes($this->createdBy) . "',
                            '" . addslashes($this->createdDate) . "'    
                            
                            
                                                    
                            )";
            // echo 'Insert->'.$sql;exit;
            $caseStudyId = $objDB->ExecuteInsertQuery($sql);
        }

        unset($objDB);
        return $caseStudyId;
    }

    function GetAllPACRCaseStudy($SchoolId, $rotationId, $studentId, $canvasStatus = '', $generateLimitString = "", $searchText = "")
    {
        $objDB = new clsDB();
        $rows = "";
        $sql = "SELECT 
                CONCAT('CC - ', LEFT(pacrCC, 70), '</br>DX - ', LEFT(pacrDx, 70)) AS ChiefComplaint,
                casestudypacr.caseStudyPacrId as caseStudyId, 
                casestudypacr.caseStudydate  ,
                casestudypacr.ClinicianDate , 
                casestudypacr.schoolDate ,
                casestudypacr.clinician_comments, 
                casestudypacr.school_comments, 
                casestudypacr.isSendToCanvas, 
                casestudypacr.studentId, 
                rotation.rotationId,
                rotation.title as rotationname,
                student.studentId,
                student.firstName as studentfirstName,
                rankmaster.title as rankTitle,
                student.lastName as studentlastName ,
                casestudypacr.type,
                casestudypacr.status,
				rotation.parentRotationId,rotation.locationId as rotationLocationId, courses.title AS Coursename,courses.locationId
                FROM casestudypacr 
                LEFT JOIN student ON student.studentId = casestudypacr.studentId 
                LEFT JOIN rotation ON casestudypacr.rotationId=rotation.rotationId 
                INNER JOIN rankmaster ON rankmaster.rankId=student.rankId
				LEFT JOIN courses ON rotation.courseId=courses.courseId
				where casestudypacr.schoolId=" . $SchoolId;

        if ($rotationId)
            $sql .= "  AND  casestudypacr.rotationId=" . $rotationId;
        if ($studentId > 0)
            $sql .= "  AND casestudypacr.studentId=" . $studentId;
        if ($canvasStatus != '')
            $sql .= "  AND casestudypacr.isSendToCanvas=" . $canvasStatus;

        if ($searchText != "") {
            $sql .= " AND ( rotation.title LIKE '%" . $searchText . "%' OR casestudypacr.type  LIKE '%" . $searchText . "%' OR CONCAT(student.firstName, ' ', student.lastName) LIKE '%" . $searchText . "%')";
        }

        $sql .= " ORDER BY casestudypacr.caseStudydate DESC";
        $sql .= $generateLimitString;
        //$sql .=" ORDER BY casestudyfloor.caseStudydate DESC";
        // echo 'SELECT->'.$sql;
        $rows = $objDB->GetResultset($sql);
        unset($objDB);
        return $rows;
    }

    function DeletePACRCaseStudy($caseStudyId)
    {
        $objDB = new clsDB();
        $sql = "DELETE  FROM casestudypacr 
					WHERE caseStudyPacrId=" . $caseStudyId;
        //ECHO 'DELETE->'.$sql;EXIT;
        $result = $objDB->ExecuteQuery($sql);
        unset($objDB);
        return $result;
    }

    function GetPACRCaseStudyForStudent($caseStudyId)
    {
        $objDB = new clsDB();
        $rows = "";
        $sql = "SELECT casestudypacr.*,student.firstName,student.lastName,rotation.parentRotationId,rotation.locationId as rotationLocationId, courses.title AS Coursename,courses.locationId FROM `casestudypacr`
        inner Join student on student.studentId = casestudypacr.studentId 
		LEFT JOIN rotation ON casestudypacr.`rotationId` = rotation.`rotationId` 
		LEFT JOIN courses ON rotation.courseId=courses.courseId
         WHERE caseStudyPacrId =" . $caseStudyId;

        //echo $sql;
        $rows = $objDB->GetDataRow($sql);
        unset($objDB);
        return $rows;
    }

    function SaveAdminCaseStudyPACR($caseStudyId)
    {
        $objDB = new clsDB();
        $sql = '';
        if ($caseStudyId > 0) {

            $sql = "UPDATE casestudypacr SET 					
                        schoolDate = '" . addslashes($this->schoolDate) . "',
                        school_comments = '" . addslashes($this->school_comments) . "'
                        Where caseStudyPacrId= " . $caseStudyId;
            // echo 'update->'.$sql;exit;
            $objDB->ExecuteQuery($sql);
        }

        unset($objDB);
        return $caseStudyId;
    }

    function SaveClinicianCaseStudyPACR($caseStudyId)
    {
        $objDB = new clsDB();
        $sql = '';
        if ($caseStudyId > 0) {

            $sql = "UPDATE casestudypacr SET 
                        clinicianId = '" . addslashes($this->clinicianId) . "',						
                        ClinicianDate = '" . addslashes($this->ClinicianDate) . "',
                        clinician_comments = '" . addslashes($this->clinician_comments) . "'
                        
                        Where caseStudyPacrId= " . $caseStudyId;
            // echo 'update->'.$sql;exit;
            $objDB->ExecuteQuery($sql);
        }

        unset($objDB);
        return $caseStudyId;
    }

    // Clinician reviews
    function SaveClinicianReviews($clinicianReviewId)
    {
        //echo '$schoolSectionId->'.$schoolSectionId;

        $objDB = new clsDB();
        $sql = '';
        if ($clinicianReviewId > 0) {
            $sql = "UPDATE clinicianreviews SET 
				clinicianReview = '" . addslashes($this->clinicianReview) . "',
				reviewDate = '" . addslashes($this->reviewDate) . "',
				casestudyId = '" . addslashes($this->casestudyId) . "',
				clinicianId = '" . addslashes($this->clinicianId) . "'
				Where clinicianReviewId= " . $clinicianReviewId;
            $objDB->ExecuteQuery($sql);
            //  echo $sql;exit;
        } else {
            $sql = "INSERT INTO `clinicianreviews` (`clinicianReview`, `reviewDate`, `casestudyId`, `clinicianId`)
				VALUES ('" . addslashes($this->clinicianReview) . "', 
						'" . ($this->reviewDate) . "',
						'" . ($this->casestudyId) . "',
						'" . ($this->clinicianId) . "'
						)";
            // echo $sql;exit;
            $clinicianReviewId = $objDB->ExecuteInsertQuery($sql);
        }


        unset($objDB);
        return $clinicianReviewId;
    }

    function GetClinicianReviews($caseStudyId)
    {
        $objDB = new clsDB();
        $rows = "";
        $sql = "SELECT clinicianreviews.*  , CONCAT( clinician.firstName,  ' ', clinician.lastName ) as clinicianName FROM `clinicianreviews` INNER JOIN clinician on clinician.clinicianId = clinicianreviews.clinicianId WHERE `casestudyId` = " . $caseStudyId;

        // echo $sql;
        $rows = $objDB->GetResultSet($sql);
        unset($objDB);
        return $rows;
    }
    function GetMaxClinicianReviews($caseStudyId, $clinicianId = 0)
    {
        $objDB = new clsDB();
        $rows = "";
        $sql = "SELECT clinicianreviews.* , CONCAT( clinician.firstName, ' ', clinician.lastName ) as clinicianName FROM `clinicianreviews` INNER JOIN clinician on clinician.clinicianId = clinicianreviews.clinicianId WHERE `casestudyId` = $caseStudyId ";
        if ($clinicianId)
            $sql .= " AND clinicianreviews.clinicianId = $clinicianId ";

        $sql .= " ORDER by createdDate DESC ";
        // echo $sql;
        $rows = $objDB->GetDataRow($sql);
        unset($objDB);
        return $rows;
    }

    function DeleteClinicianReview($clinicianReviewId)
    {
        $objDB = new clsDB();
        $sql = "DELETE  FROM clinicianreviews 
					WHERE clinicianReviewId=" . $clinicianReviewId;
        // ECHO 'DELETE->'.$sql;EXIT;
        $result = $objDB->ExecuteQuery($sql);
        unset($objDB);
        return $result;
    }

    // Casestudy Report for Admin
    function GetStudentCaseStudyForreport($SchoolId, $rotationId = 0, $subcborotation = 0, $studentId = '', $rankId = 0, $evaluator = 0, $locationId = 0, $hospitalSiteId = 0, $startdate = '', $endtdate = '', $ascdesc, $sordorder, $cbosemester)
    {
        $cbosemester = str_replace(" ", ",", $cbosemester);
        $subcborotation = str_replace(" ", ",", $subcborotation);

        $studentIds =  $studentId ? implode(',', $studentId) : '';
        $sql = '';
        $objDB = new clsDB();
        $rows = "";

        //PACR CASE STUDY
        $sql .= "SELECT cc.ChiefComplaint, cc.caseStudyId, cc.caseStudydate, cc.ClinicianDate, cc.schoolDate, cc.clinician_comments, cc.school_comments, cc.isSendToCanvas, cc.studentId, cc.rotationId, cc.rotationname, s.firstName as studentfirstName, rm.title as rankTitle, s.lastName as studentlastName, cc.type, cc.parentRotationId, cc.rotationLocationId, c.title AS Coursename, c.locationId, cc.schoolId , cc.hospitalSiteId

         FROM 
         
         ( SELECT casestudypacr.caseStudyPacrId as caseStudyId, concat('CC - ',pacrCC, '. DX - ',pacrDx) as ChiefComplaint, casestudypacr.caseStudydate , casestudypacr.ClinicianDate , casestudypacr.schoolDate , casestudypacr.clinician_comments, casestudypacr.school_comments, casestudypacr.isSendToCanvas, casestudypacr.studentId, casestudypacr.rotationId, rotation.title as rotationname, rotation.parentRotationId, rotation.locationId as rotationLocationId, casestudypacr.type, rotation.courseId, casestudypacr.schoolId,rotation.hospitalSiteId FROM casestudypacr INNER JOIN rotation ON casestudypacr.rotationId = rotation.rotationId
          
         UNION ALL 
          
         SELECT casestudyfloor.caseStudyFloorId as caseStudyId, casestudyfloor.floorChiefComplaint as ChiefComplaint, casestudyfloor.caseStudydate, casestudyfloor.ClinicianDate, casestudyfloor.schoolDate, casestudyfloor.clinician_comments, casestudyfloor.school_comments, casestudyfloor.isSendToCanvas, casestudyfloor.studentId, casestudyfloor.rotationId, rotation.title as rotationname, rotation.parentRotationId, rotation.locationId as rotationLocationId, casestudyfloor.type, rotation.courseId, casestudyfloor.schoolId ,rotation.hospitalSiteId FROM casestudyfloor INNER JOIN rotation ON casestudyfloor.rotationId = rotation.rotationId 
          
          UNION ALL 
          
         SELECT casestudyadult.caseStudyAdultId as caseStudyId, casestudyadult.adultChiefComplaint as ChiefComplaint, casestudyadult.caseStudydate , casestudyadult.ClinicianDate , casestudyadult.schoolDate , casestudyadult.clinician_comments, casestudyadult.school_comments, casestudyadult.isSendToCanvas, casestudyadult.studentId, casestudyadult.rotationId, rotation.title as rotationname, rotation.parentRotationId, rotation.locationId as rotationLocationId, casestudyadult.type, rotation.courseId, casestudyadult.schoolId,rotation.hospitalSiteId FROM casestudyadult INNER JOIN rotation ON casestudyadult.rotationId = rotation.rotationId ) as cc 
         
         INNER JOIN student s ON cc.studentId = s.studentId 
         INNER JOIN rankmaster rm ON s.rankId = rm.rankId 
         INNER JOIN courses c ON cc.courseId = c.courseId 
         LEFT JOIN semestermaster ON c.`semesterId` = semestermaster.`semesterId` 
         LEFT JOIN hospitalsites h ON h.hospitalSiteId = cc.hospitalSiteId
         WHERE cc.schoolId = $SchoolId";

        if ($cbosemester > 0)
            $sql .= " AND semestermaster.semesterId IN ($cbosemester)";

        if ($subcborotation > 0)
            $sql .= " AND (cc.rotationId IN ($subcborotation) OR cc.rotationId=" . $rotationId . " )";
        else if ($rotationId > 0)
            $sql .= " AND cc.rotationId=" . $rotationId;

        if ($studentIds > 0)
            $sql .= " AND s.studentId IN ($studentIds)";
        if ($rankId > 0)
            $sql .= " AND rm.rankId=" . $rankId;
        // if ($clinicianId > 0)
        // 	$sql .= " AND clinician.clinicianId=" . $clinicianId;
        if ($locationId > 0)
            $sql .= " AND c.locationId=" . $locationId;
        if ($hospitalSiteId > 0)
            $sql .= " AND h.hospitalSiteId=" . $hospitalSiteId;

        if ($startdate > 0 || $endtdate > 0)
            $sql .= " AND date(cc.caseStudydate) >= '" . $startdate . "' and date(cc.caseStudydate) <= '" . $endtdate . "'";
        if ($ascdesc && $sordorder == 2)
            $sql .= "  ORDER BY rm.title " . $ascdesc;

        else if ($ascdesc && $sordorder == 5)
            $sql .= "  ORDER BY cc.rotationname " . $ascdesc;

        else if ($ascdesc && ($sordorder == 10 || $sordorder == 8))
            $sql .= "  ORDER BY h.title " . $ascdesc;

        else if ($ascdesc && $sordorder == 14)
            $sql .= "  ORDER BY s.firstName " . $ascdesc;

        else if ($ascdesc && $sordorder == 15)
            $sql .= "  ORDER BY s.lastName " . $ascdesc;
        else
            $sql .= "  ORDER BY s.firstName " . $ascdesc . ", s.lastName " . $ascdesc . " ,cc.rotationname " . $ascdesc;

        // echo 'SELECT->'.$sql;
        $rows = $objDB->GetResultset($sql);
        unset($objDB);
        return $rows;
    }

    // Casestudy Report for Clinician
    function GetStudentCaseStudyForClinicianReport($SchoolId, $rotationId = 0, $subcborotation = 0, $studentId = '', $rankId = 0, $clinicianId = 0, $locationId = 0, $hospitalSiteId = 0, $startdate = '', $endtdate = '', $ascdesc, $sordorder, $cbosemester)
    {
        $cbosemester = str_replace(" ", ",", $cbosemester);
        $subcborotation = str_replace(" ", ",", $subcborotation);

        $studentIds =  $studentId ? implode(',', $studentId) : '';
        $sql = '';
        $objDB = new clsDB();
        $rows = "";

        //PACR CASE STUDY
        $sql .= "SELECT cc.ChiefComplaint, cc.caseStudyId, cc.caseStudydate, cc.ClinicianDate, cc.schoolDate, cc.clinician_comments, cc.school_comments, cc.isSendToCanvas, cc.studentId, cc.rotationId, cc.rotationname, s.firstName as studentfirstName, rm.title as rankTitle, s.lastName as studentlastName, cc.type, cc.parentRotationId, cc.rotationLocationId, c.title AS Coursename, c.locationId, cc.schoolId , cc.hospitalSiteId

         FROM 
         
         ( SELECT casestudypacr.caseStudyPacrId as caseStudyId, concat('CC - ',pacrCC, '. DX - ',pacrDx) as ChiefComplaint, casestudypacr.caseStudydate , casestudypacr.ClinicianDate , casestudypacr.schoolDate , casestudypacr.clinician_comments, casestudypacr.school_comments, casestudypacr.isSendToCanvas, casestudypacr.studentId, casestudypacr.rotationId, rotation.title as rotationname, rotation.parentRotationId, rotation.locationId as rotationLocationId, casestudypacr.type, rotation.courseId, casestudypacr.schoolId,rotation.hospitalSiteId FROM casestudypacr INNER JOIN rotation ON casestudypacr.rotationId = rotation.rotationId
          
         UNION ALL 
          
         SELECT casestudyfloor.caseStudyFloorId as caseStudyId, casestudyfloor.floorChiefComplaint as ChiefComplaint, casestudyfloor.caseStudydate, casestudyfloor.ClinicianDate, casestudyfloor.schoolDate, casestudyfloor.clinician_comments, casestudyfloor.school_comments, casestudyfloor.isSendToCanvas, casestudyfloor.studentId, casestudyfloor.rotationId, rotation.title as rotationname, rotation.parentRotationId, rotation.locationId as rotationLocationId, casestudyfloor.type, rotation.courseId, casestudyfloor.schoolId ,rotation.hospitalSiteId FROM casestudyfloor INNER JOIN rotation ON casestudyfloor.rotationId = rotation.rotationId 
          
          UNION ALL 
          
         SELECT casestudyadult.caseStudyAdultId as caseStudyId, casestudyadult.adultChiefComplaint as ChiefComplaint, casestudyadult.caseStudydate , casestudyadult.ClinicianDate , casestudyadult.schoolDate , casestudyadult.clinician_comments, casestudyadult.school_comments, casestudyadult.isSendToCanvas, casestudyadult.studentId, casestudyadult.rotationId, rotation.title as rotationname, rotation.parentRotationId, rotation.locationId as rotationLocationId, casestudyadult.type, rotation.courseId, casestudyadult.schoolId,rotation.hospitalSiteId FROM casestudyadult INNER JOIN rotation ON casestudyadult.rotationId = rotation.rotationId ) as cc 
         
         INNER JOIN student s ON cc.studentId = s.studentId 
         INNER JOIN rankmaster rm ON s.rankId = rm.rankId 
         INNER JOIN courses c ON cc.courseId = c.courseId 
         LEFT JOIN semestermaster ON c.`semesterId` = semestermaster.`semesterId` 
         LEFT JOIN hospitalsites h ON h.hospitalSiteId = cc.hospitalSiteId
		 INNER join clinicianhospitalsite on clinicianhospitalsite.hospitalSiteId = cc.hospitalSiteId AND clinicianhospitalsite.clinicianId = " . $clinicianId . "
         WHERE cc.schoolId = " . $SchoolId . " AND (rm.title !='Graduate' AND rm.title NOT like '%Graduate%') and rm.title !='Dropout'";

        if ($cbosemester > 0)
            $sql .= " AND semestermaster.semesterId IN ($cbosemester)";

        if ($subcborotation > 0)
            $sql .= " AND (cc.rotationId IN ($subcborotation) OR cc.rotationId=" . $rotationId . " )";
        else if ($rotationId > 0)
            $sql .= " AND cc.rotationId=" . $rotationId;

        if ($studentIds > 0)
            $sql .= " AND s.studentId IN ($studentIds)";
        if ($rankId > 0)
            $sql .= " AND rm.rankId=" . $rankId;
        // if ($clinicianId > 0)
        // 	$sql .= " AND clinician.clinicianId=" . $clinicianId;
        if ($locationId > 0)
            $sql .= " AND c.locationId=" . $locationId;
        if ($hospitalSiteId > 0)
            $sql .= " AND h.hospitalSiteId=" . $hospitalSiteId;

        if ($startdate > 0 || $endtdate > 0)
            $sql .= " AND date(cc.caseStudydate) >= '" . $startdate . "' and date(cc.caseStudydate) <= '" . $endtdate . "'";
        if ($ascdesc && $sordorder == 2)
            $sql .= "  ORDER BY rankmaster.title " . $ascdesc;

        else if ($ascdesc && $sordorder == 5)
            $sql .= "  ORDER BY cc.rotationname " . $ascdesc;

        else if ($ascdesc && ($sordorder == 10 || $sordorder == 8))
            $sql .= "  ORDER BY h.title " . $ascdesc;

        else if ($ascdesc && $sordorder == 14)
            $sql .= "  ORDER BY s.firstName " . $ascdesc;

        else if ($ascdesc && $sordorder == 15)
            $sql .= "  ORDER BY s.lastName " . $ascdesc;
        else
            $sql .= "  ORDER BY s.firstName " . $ascdesc . ", s.lastName " . $ascdesc . " ,cc.rotationname " . $ascdesc;

        // echo 'SELECT->'.$sql;
        $rows = $objDB->GetResultset($sql);
        unset($objDB);
        return $rows;
    }


    // function GetAllCaseStudyForApp($SchoolId, $rotationId, $studentId, $casestudySettings = '', $generateLimitString, $searchText)
    // {
    //     // print_r($casestudySettings);
    //     $sql = '';
    //     $casestudySettings = explode(",", $casestudySettings);
    //     $objDB = new clsDB();
    //     $rows = "";
    //     //check value in array is exist
    //     //PACR CASE STUDY
    //     if (in_array("1", $casestudySettings)) {
    //         $sql .= "SELECT
    //         concat('CC - ',LEFT(pacrCC, 70), '</br>DX - ',LEFT(pacrDx, 70)) as ChiefComplaint,
    //         casestudypacr.caseStudyPacrId as caseStudyId,
    //         casestudypacr.caseStudydate  ,             
    //         casestudypacr.studentId,
    //         rotation.rotationId,
    //         rotation.title as rotationname,
    //         casestudypacr.type,
    //         casestudypacr.ClinicianDate, casestudypacr.schoolDate
    //         FROM casestudypacr
    //         LEFT JOIN rotation ON casestudypacr.rotationId=rotation.rotationId

    //         where casestudypacr.schoolId=" . $SchoolId;

    //         if ($rotationId)
    //             $sql .= "  AND  casestudypacr.rotationId=" . $rotationId;
    //         if ($studentId > 0)
    //             $sql .= "  AND casestudypacr.studentId=" . $studentId;

    //         if ($searchText != "") {
    //             $sql .= " AND ( rotation.title LIKE '%" . $searchText . "%' OR casestudypacr.type  LIKE '%" . $searchText . "%' )";
    //         }
    //         // if($canvasStatus !='')
    //         // $sql .="  AND casestudypacr.isSendToCanvas=".$canvasStatus;
    //     }
    //     //check value in array is exist
    //     //FLOOR CASE STUDY
    //     if (in_array("2", $casestudySettings)) {
    //         //get position in array
    //         $key2 = array_search('2', $casestudySettings);
    //         if ($key2)
    //             $sql .= " union ";

    //         $sql .= "SELECT casestudyfloor.floorChiefComplaint as ChiefComplaint,
    //         casestudyfloor.caseStudyFloorId as caseStudyId, 
    //         casestudyfloor.caseStudydate,            
    //         casestudyfloor.studentId,
    //         rotation.rotationId,
    //         rotation.title as rotationname,
    //         casestudyfloor.type,
    //         casestudyfloor.ClinicianDate, casestudyfloor.schoolDate
    //         FROM casestudyfloor
    //         LEFT JOIN rotation ON casestudyfloor.rotationId=rotation.rotationId

    //         where casestudyfloor.schoolId=" . $SchoolId;

    //         if ($rotationId)
    //             $sql .= "  AND  casestudyfloor.rotationId=" . $rotationId;
    //         if ($studentId > 0)
    //             $sql .= "  AND casestudyfloor.studentId=" . $studentId;
    //         if ($searchText != "") {
    //             $sql .= " AND ( rotation.title LIKE '%" . $searchText . "%' OR casestudyfloor.type  LIKE '%" . $searchText . "%')";
    //         }
    //         // if($canvasStatus !='')
    //         //     $sql .="  AND casestudyfloor.isSendToCanvas=".$canvasStatus;
    //     }
    //     //check value in array is exist
    //     //ADULT CASE STUDY
    //     if (in_array("3", $casestudySettings)) {
    //         //get position in array
    //         $key3 = array_search('3', $casestudySettings);
    //         if ($key3)
    //             $sql .= " union ";

    //         $sql .= "SELECT 
    //                 casestudyadult.adultChiefComplaint as ChiefComplaint,
    //                 casestudyadult.caseStudyAdultId as caseStudyId,
    //                 casestudyadult.caseStudydate,            
    //                 casestudyadult.studentId,
    //                 rotation.rotationId,
    //                 rotation.title as rotationname,
    //                 casestudyadult.type,
    //                 casestudyadult.ClinicianDate, casestudyadult.schoolDate
    //                 FROM casestudyadult
    //                 LEFT JOIN rotation ON casestudyadult.rotationId=rotation.rotationId
    //                 where casestudyadult.schoolId=" . $SchoolId;

    //         if ($rotationId)
    //             $sql .= "  AND  casestudyadult.rotationId=" . $rotationId;
    //         if ($studentId > 0)
    //             $sql .= "  AND casestudyadult.studentId=" . $studentId;
    //         if ($searchText != "") {
    //             $sql .= " AND ( rotation.title LIKE '%" . $searchText . "%' OR casestudyadult.type  LIKE '%" . $searchText . "%')";
    //         }
    //         // if($canvasStatus !='')
    //         //     $sql .="  AND casestudyadult.isSendToCanvas=".$canvasStatus;
    //     }

    //     $sql .= $generateLimitString;

    //     // echo 'SELECT->' . $sql;
    //     $rows = $objDB->GetResultset($sql);
    //     unset($objDB);
    //     return $rows;
    // }

    function GetAllCaseStudyForApp($SchoolId, $rotationId, $studentId, $casestudySettings = '', $generateLimitString, $searchText)
    {
        $sql = '';
        $casestudySettings = explode(",", $casestudySettings);
        $objDB = new clsDB();
        $rows = "";

        // PACR CASE STUDY
        if (in_array("1", $casestudySettings)) {
            $sql .= "(SELECT
            concat('CC - ',LEFT(pacrCC, 70), '</br>DX - ',LEFT(pacrDx, 70)) as ChiefComplaint,
            casestudypacr.caseStudyPacrId as caseStudyId,
            casestudypacr.caseStudydate,             
            casestudypacr.studentId,
            rotation.rotationId,
            rotation.title as rotationname,
            casestudypacr.type,
            casestudypacr.ClinicianDate,
            casestudypacr.schoolDate,
            casestudypacr.clinician_comments, 
            casestudypacr.school_comments, 
            student.firstName as studentfirstName,
            student.lastName as studentlastName,
            casestudypacr.status
            FROM casestudypacr
            LEFT JOIN rotation ON casestudypacr.rotationId=rotation.rotationId
            LEFT JOIN student ON casestudypacr.studentId=student.studentId
           
            WHERE casestudypacr.schoolId=" . $SchoolId;

            if ($rotationId)
                $sql .= " AND casestudypacr.rotationId=" . $rotationId;
            if ($studentId > 0)
                $sql .= " AND casestudypacr.studentId=" . $studentId;

            if ($searchText != "") {
                $sql .= " AND (rotation.title LIKE '%" . $searchText . "%' OR casestudypacr.type LIKE '%" . $searchText . "%' OR CONCAT(student.firstName, ' ', student.lastName) LIKE '%" . $searchText . "%')";
            }
            $sql .= " )";

            // $sql .= " ORDER BY casestudypacr.caseStudydate DESC)"; // Order by casestudydate in descending order
        }

        // FLOOR CASE STUDY
        if (in_array("2", $casestudySettings)) {
            $key2 = array_search('2', $casestudySettings);
            if ($key2)
                $sql .= " UNION ";

            $sql .= "(SELECT casestudyfloor.floorChiefComplaint as ChiefComplaint,
            casestudyfloor.caseStudyFloorId as caseStudyId, 
            casestudyfloor.caseStudydate,            
            casestudyfloor.studentId,
            rotation.rotationId,
            rotation.title as rotationname,
            casestudyfloor.type,
            casestudyfloor.ClinicianDate, 
            casestudyfloor.schoolDate,
            casestudyfloor.clinician_comments, 
            casestudyfloor.school_comments,
            student.firstName as studentfirstName,
            student.lastName as studentlastName,
            casestudyfloor.status
            FROM casestudyfloor
            LEFT JOIN rotation ON casestudyfloor.rotationId=rotation.rotationId
            LEFT JOIN student ON casestudyfloor.studentId=student.studentId
           
            WHERE casestudyfloor.schoolId=" . $SchoolId;

            if ($rotationId)
                $sql .= " AND casestudyfloor.rotationId=" . $rotationId;
            if ($studentId > 0)
                $sql .= " AND casestudyfloor.studentId=" . $studentId;
            if ($searchText != "") {
                $sql .= " AND (rotation.title LIKE '%" . $searchText . "%' OR casestudyfloor.type LIKE '%" . $searchText . "%' OR CONCAT(student.firstName, ' ', student.lastName) LIKE '%" . $searchText . "%')";
            }
            $sql .= " )";

            // $sql .= " ORDER BY casestudyfloor.caseStudydate DESC)"; // Order by casestudydate in descending order
        }

        // ADULT CASE STUDY
        if (in_array("3", $casestudySettings)) {
            $key3 = array_search('3', $casestudySettings);
            if ($key3)
                $sql .= " UNION ";

            $sql .= "(SELECT 
                casestudyadult.adultChiefComplaint as ChiefComplaint,
                casestudyadult.caseStudyAdultId as caseStudyId,
                casestudyadult.caseStudydate,            
                casestudyadult.studentId,
                rotation.rotationId,
                rotation.title as rotationname,
                casestudyadult.type,
                casestudyadult.ClinicianDate, 
                casestudyadult.schoolDate,
                casestudyadult.clinician_comments, 
                casestudyadult.school_comments,
                student.firstName as studentfirstName,
                student.lastName as studentlastName,
                casestudyadult.status 
                FROM casestudyadult
                LEFT JOIN rotation ON casestudyadult.rotationId=rotation.rotationId
                LEFT JOIN student ON casestudyadult.studentId=student.studentId

                WHERE casestudyadult.schoolId=" . $SchoolId;

            if ($rotationId)
                $sql .= " AND casestudyadult.rotationId=" . $rotationId;
            if ($studentId > 0)
                $sql .= " AND casestudyadult.studentId=" . $studentId;
            if ($searchText != "") {
                $sql .= " AND (rotation.title LIKE '%" . $searchText . "%' OR casestudyadult.type LIKE '%" . $searchText . "%' OR CONCAT(student.firstName, ' ', student.lastName) LIKE '%" . $searchText . "%')";
            }
            $sql .= " )";
            // $sql .= " ORDER BY casestudyadult.caseStudydate DESC)"; // Order by casestudydate in descending order
        }

        // Apply pagination and the final ORDER BY clause
        $sql .= " ORDER BY caseStudydate DESC";
        $sql .= $generateLimitString;

        // Debugging: Output the generated SQL query
        // echo 'SELECT->' . $sql;
        // exit;

        $rows = $objDB->GetResultset($sql);
        unset($objDB);
        return $rows;
    }

    function GetCaseStudySettingForApp($SchoolId)
    {
        $objDB = new clsDB();
        $sql = "SELECT  casestudySettings FROM schools WHERE schoolId = " . $SchoolId;
        //    echo $sql;exit;
        $result = $objDB->GetResultset($sql);
        unset($objDB);
        return $result;
    }

    function GetAllPACRCaseStudyForClinician($SchoolId, $rotationId, $studentId, $canvasStatus = '', $generateLimitString = "", $searchText = "", $clinicianId = 0)
    {
        $objDB = new clsDB();
        $rows = "";
        $sql = "SELECT 
                CONCAT('CC - ', LEFT(pacrCC, 70), '</br>DX - ', LEFT(pacrDx, 70)) AS ChiefComplaint,
                casestudypacr.caseStudyPacrId as caseStudyId, 
                casestudypacr.caseStudydate  ,
                casestudypacr.ClinicianDate , 
                casestudypacr.schoolDate ,
                casestudypacr.clinician_comments, 
                casestudypacr.school_comments, 
                casestudypacr.isSendToCanvas, 
                casestudypacr.studentId, 
                rotation.rotationId,
                rotation.title as rotationname,
                student.firstName as studentfirstName,
                rankmaster.title as rankTitle,
                student.lastName as studentlastName ,
                casestudypacr.type,
                casestudypacr.status,
				rotation.parentRotationId,rotation.locationId as rotationLocationId, courses.title AS Coursename,courses.locationId
                FROM casestudypacr 
                LEFT JOIN student ON student.studentId = casestudypacr.studentId 
                LEFT JOIN rotation ON casestudypacr.rotationId=rotation.rotationId 
                INNER JOIN rankmaster ON rankmaster.rankId=student.rankId
				LEFT JOIN courses ON rotation.courseId=courses.courseId ";

        if ($clinicianId) {
            $sql .= " INNER JOIN clinicianhospitalsite ON rotation.hospitalSiteId=clinicianhospitalsite.hospitalSiteId AND clinicianhospitalsite.clinicianId = $clinicianId
                INNER JOIN clinician ON clinicianhospitalsite.clinicianId=clinician.clinicianId
                LEFT JOIN hospitalsites ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId";
        }

        $sql .= "	where casestudypacr.schoolId=" . $SchoolId;

        if ($rotationId)
            $sql .= "  AND  casestudypacr.rotationId=" . $rotationId;
        if ($studentId > 0)
            $sql .= "  AND casestudypacr.studentId=" . $studentId;
        if ($canvasStatus != '')
            $sql .= "  AND casestudypacr.isSendToCanvas=" . $canvasStatus;

        $sql .= " AND rotation.isDelete = 0 AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)
            AND (
                (CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
                OR
                (rotation.parentRotationId > 0
                AND EXISTS (
                    SELECT 1
                    FROM rotation AS parent
                    WHERE parent.rotationId = rotation.parentRotationId
                    AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
                    AND rotation.isSchedule = 1 
                )
                )
            )
		";
        if ($searchText != "") {
            $sql .= " AND ( CONCAT(student.firstName, ' ', student.lastName) LIKE '%" . $searchText . "%' OR rotation.title LIKE '%" . $searchText . "%' OR casestudypacr.type  LIKE '%" . $searchText . "%' )";
        }

        $sql .= " ORDER BY casestudypacr.caseStudydate DESC";
        $sql .= $generateLimitString;

        // echo 'SELECT->' . $sql;exit;
        $rows = $objDB->GetResultset($sql);
        unset($objDB);
        return $rows;
    }

    function GetAllFloorCaseStudyForClinician($SchoolId, $rotationId, $studentId, $canvasStatus = '', $generateLimitString = "", $searchText = "", $clinicianId = 0)
    {
        $objDB = new clsDB();
        $rows = "";
        $sql = "SELECT casestudyfloor.floorChiefComplaint as ChiefComplaint,
                casestudyfloor.caseStudyFloorId as caseStudyId, 
                casestudyfloor.caseStudydate,
                casestudyfloor.ClinicianDate, 
                casestudyfloor.schoolDate, 
                casestudyfloor.clinician_comments, 
                casestudyfloor.school_comments, 
                casestudyfloor.isSendToCanvas, 
                casestudyfloor.studentId, 
                rotation.rotationId,
                rotation.title as rotationname, 
                student.firstName as studentfirstName,
                rankmaster.title as rankTitle,
                student.lastName as studentlastName ,
                casestudyfloor.type,
                casestudyfloor.status as status,
				rotation.parentRotationId,rotation.locationId as rotationLocationId, courses.title AS Coursename,courses.locationId
                FROM casestudyfloor 
                LEFT JOIN student ON student.studentId = casestudyfloor.studentId 
                LEFT JOIN rotation ON casestudyfloor.rotationId=rotation.rotationId 
                INNER JOIN rankmaster ON rankmaster.rankId=student.rankId 
				LEFT JOIN courses ON rotation.courseId=courses.courseId";

        if ($clinicianId) {
            $sql .= " INNER JOIN clinicianhospitalsite ON rotation.hospitalSiteId=clinicianhospitalsite.hospitalSiteId AND clinicianhospitalsite.clinicianId = $clinicianId
                INNER JOIN clinician ON clinicianhospitalsite.clinicianId=clinician.clinicianId
                LEFT JOIN hospitalsites ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId";
        }

        $sql .= "	where casestudyfloor.schoolId=" . $SchoolId;


        if ($rotationId)
            $sql .= "  AND  casestudyfloor.rotationId=" . $rotationId;
        if ($studentId > 0)
            $sql .= "  AND casestudyfloor.studentId=" . $studentId;
        if ($canvasStatus != '')
            $sql .= "  AND casestudyfloor.isSendToCanvas=" . $canvasStatus;

        $sql .= " AND rotation.isDelete = 0 AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)
            AND (
                (CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
                OR
                (rotation.parentRotationId > 0
                AND EXISTS (
                    SELECT 1
                    FROM rotation AS parent
                    WHERE parent.rotationId = rotation.parentRotationId
                    AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
                    AND rotation.isSchedule = 1 
                )
                )
            )
		";
        if ($searchText != "") {
            $sql .= " AND ( CONCAT(student.firstName, ' ', student.lastName) LIKE '%" . $searchText . "%' OR rotation.title LIKE '%" . $searchText . "%' OR casestudyfloor.type  LIKE '%" . $searchText . "%')";
        }
        $sql .= " ORDER BY casestudyfloor.caseStudydate DESC";
        $sql .= $generateLimitString;

        //echo 'SELECT->'.$sql;
        $rows = $objDB->GetResultset($sql);
        unset($objDB);
        return $rows;
    }
    function GetAllAdultCaseStudyForClinician($SchoolId, $rotationId, $studentId, $canvasStatus = '', $generateLimitString = "", $searchText = "", $clinicianId = 0)
    {
        $objDB = new clsDB();
        $rows = "";
        $sql = "SELECT 
                casestudyadult.adultChiefComplaint as ChiefComplaint,
                casestudyadult.caseStudyAdultId as caseStudyId,
                casestudyadult.caseStudydate  ,
                casestudyadult.ClinicianDate , 
                casestudyadult.schoolDate ,
                casestudyadult.clinician_comments, 
                casestudyadult.school_comments, 
                casestudyadult.isSendToCanvas, 
                casestudyadult.studentId, 
                rotation.rotationId,
                rotation.title as rotationname,
                student.firstName as studentfirstName,
                rankmaster.title as rankTitle,
                student.lastName as studentlastName ,
                casestudyadult.type,
                casestudyadult.status,
				rotation.parentRotationId,rotation.locationId as rotationLocationId, courses.title AS Coursename,courses.locationId
                FROM casestudyadult 
                LEFT JOIN student ON student.studentId = casestudyadult.studentId 
                LEFT JOIN rotation ON casestudyadult.rotationId=rotation.rotationId 
                INNER JOIN rankmaster ON rankmaster.rankId=student.rankId
				LEFT JOIN courses ON rotation.courseId=courses.courseId";

        if ($clinicianId) {
            $sql .= " INNER JOIN clinicianhospitalsite ON rotation.hospitalSiteId=clinicianhospitalsite.hospitalSiteId AND clinicianhospitalsite.clinicianId = $clinicianId
                INNER JOIN clinician ON clinicianhospitalsite.clinicianId=clinician.clinicianId
                LEFT JOIN hospitalsites ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId";
        }

        $sql .= "	where casestudyadult.schoolId=" . $SchoolId;

        if ($rotationId)
            $sql .= "  AND  casestudyadult.rotationId=" . $rotationId;
        if ($studentId > 0)
            $sql .= "  AND casestudyadult.studentId=" . $studentId;
        if ($canvasStatus != '')
            $sql .= "  AND casestudyadult.isSendToCanvas=" . $canvasStatus;

        $sql .= " AND rotation.isDelete = 0 AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)
            AND (
                (CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
                OR
                (rotation.parentRotationId > 0
                AND EXISTS (
                    SELECT 1
                    FROM rotation AS parent
                    WHERE parent.rotationId = rotation.parentRotationId
                    AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
                    AND rotation.isSchedule = 1 
                )
                )
            )
		";
        if ($searchText != "") {
            $sql .= " AND ( CONCAT(student.firstName, ' ', student.lastName) LIKE '%" . $searchText . "%' OR rotation.title LIKE '%" . $searchText . "%' OR casestudyadult.type  LIKE '%" . $searchText . "%')";
        }
        $sql .= " ORDER BY casestudyadult.caseStudydate DESC";
        $sql .=  $generateLimitString;

        // echo 'SELECT->' . $sql;
        $rows = $objDB->GetResultset($sql);
        unset($objDB);
        return $rows;
    }

    function GetAllCaseStudyForClinician($SchoolId, $rotationId, $studentId = 0, $casestudySettings = '', $generateLimitString = '', $searchText = '', $clinicianId = 0)
    {
        // print_r($casestudySettings);
        $sql = '';
        $casestudySettings = explode(",", $casestudySettings);
        $objDB = new clsDB();
        $rows = "";
        //check value in array is exist
        //PACR CASE STUDY
        if (in_array("1", $casestudySettings)) {
            $sql .= "(SELECT
            CONCAT('CC - ', LEFT(pacrCC, 70), '</br>DX - ', LEFT(pacrDx, 70)) AS ChiefComplaint,
                casestudypacr.caseStudyPacrId as caseStudyId, 
                casestudypacr.caseStudydate  ,
                casestudypacr.ClinicianDate , 
                casestudypacr.schoolDate ,
                casestudypacr.clinician_comments, 
                casestudypacr.school_comments, 
                casestudypacr.isSendToCanvas, 
                casestudypacr.studentId, 
                rotation.rotationId,
                rotation.title as rotationname,
                student.firstName as studentfirstName,
                rankmaster.title as rankTitle,
                student.lastName as studentlastName ,
                casestudypacr.type,
                casestudypacr.status,
				rotation.parentRotationId,rotation.locationId as rotationLocationId, courses.title AS Coursename,courses.locationId
                FROM casestudypacr 
                LEFT JOIN student ON student.studentId = casestudypacr.studentId 
                LEFT JOIN rotation ON casestudypacr.rotationId=rotation.rotationId 
                INNER JOIN rankmaster ON rankmaster.rankId=student.rankId
				LEFT JOIN courses ON rotation.courseId=courses.courseId
            ";

            if ($clinicianId) {
                $sql .= " INNER JOIN clinicianhospitalsite ON rotation.hospitalSiteId=clinicianhospitalsite.hospitalSiteId AND clinicianhospitalsite.clinicianId = $clinicianId
                        INNER JOIN clinician ON clinicianhospitalsite.clinicianId=clinician.clinicianId
                        LEFT JOIN hospitalsites ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId";
            }

            $sql .= "	where casestudypacr.schoolId=" . $SchoolId;


            if ($rotationId)
                $sql .= "  AND  casestudypacr.rotationId=" . $rotationId;
            if ($studentId > 0)
                $sql .= "  AND casestudypacr.studentId=" . $studentId;

            $sql .= " AND rotation.isDelete = 0 AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)
                AND (
                    (CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
                    OR
                    (rotation.parentRotationId > 0
                    AND EXISTS (
                        SELECT 1
                        FROM rotation AS parent
                        WHERE parent.rotationId = rotation.parentRotationId
                        AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
                        AND rotation.isSchedule = 1 
                    )
                    )
                )
            ";

            if ($searchText != "") {
                $sql .= " AND ( CONCAT(student.firstName, ' ', student.lastName) LIKE '%" . $searchText . "%' OR rotation.title LIKE '%" . $searchText . "%' OR casestudypacr.type  LIKE '%" . $searchText . "%' )";
            }

            $sql .= " ORDER BY casestudypacr.caseStudydate DESC)"; // Order by casestudydate in descending order

            // if($canvasStatus !='')
            // $sql .="  AND casestudypacr.isSendToCanvas=".$canvasStatus;
        }
        //check value in array is exist
        //FLOOR CASE STUDY
        if (in_array("2", $casestudySettings)) {
            //get position in array
            $key2 = array_search('2', $casestudySettings);
            if ($key2)
                $sql .= " union ";

            $sql .= "(SELECT casestudyfloor.floorChiefComplaint as ChiefComplaint,
            casestudyfloor.caseStudyFloorId as caseStudyId, 
            casestudyfloor.caseStudydate,
            casestudyfloor.ClinicianDate, 
            casestudyfloor.schoolDate, 
            casestudyfloor.clinician_comments, 
            casestudyfloor.school_comments, 
            casestudyfloor.isSendToCanvas, 
            casestudyfloor.studentId, 
            rotation.rotationId,
            rotation.title as rotationname, 
            student.firstName as studentfirstName,
            rankmaster.title as rankTitle,
            student.lastName as studentlastName ,
            casestudyfloor.type,
            casestudyfloor.status as status,
            rotation.parentRotationId,rotation.locationId as rotationLocationId, courses.title AS Coursename,courses.locationId
            FROM casestudyfloor 
            LEFT JOIN student ON student.studentId = casestudyfloor.studentId 
            LEFT JOIN rotation ON casestudyfloor.rotationId=rotation.rotationId 
            INNER JOIN rankmaster ON rankmaster.rankId=student.rankId 
            LEFT JOIN courses ON rotation.courseId=courses.courseId";

            if ($clinicianId) {
                $sql .= " INNER JOIN clinicianhospitalsite ON rotation.hospitalSiteId=clinicianhospitalsite.hospitalSiteId AND clinicianhospitalsite.clinicianId = $clinicianId
                        INNER JOIN clinician ON clinicianhospitalsite.clinicianId=clinician.clinicianId
                        LEFT JOIN hospitalsites ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId";
            }

            $sql .= "	where casestudyfloor.schoolId=" . $SchoolId;

            if ($rotationId)
                $sql .= "  AND  casestudyfloor.rotationId=" . $rotationId;
            if ($studentId > 0)
                $sql .= "  AND casestudyfloor.studentId=" . $studentId;

            $sql .= " AND rotation.isDelete = 0 AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)
                AND (
                    (CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
                    OR
                    (rotation.parentRotationId > 0
                    AND EXISTS (
                        SELECT 1
                        FROM rotation AS parent
                        WHERE parent.rotationId = rotation.parentRotationId
                        AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
                        AND rotation.isSchedule = 1 
                    )
                    )
                )
            ";
            if ($searchText != "") {
                $sql .= " AND ( rotation.title LIKE '%" . $searchText . "%' OR casestudyfloor.type  LIKE '%" . $searchText . "%')";
            }

            $sql .= " ORDER BY casestudyfloor.caseStudydate DESC)"; // Order by casestudydate in descending order

            // if($canvasStatus !='')
            //     $sql .="  AND casestudyfloor.isSendToCanvas=".$canvasStatus;
        }
        //check value in array is exist
        //ADULT CASE STUDY
        if (in_array("3", $casestudySettings)) {
            //get position in array
            $key3 = array_search('3', $casestudySettings);
            if ($key3)
                $sql .= " union ";

            $sql .= "(SELECT 
                    casestudyadult.adultChiefComplaint as ChiefComplaint,
                casestudyadult.caseStudyAdultId as caseStudyId,
                casestudyadult.caseStudydate  ,
                casestudyadult.ClinicianDate , 
                casestudyadult.schoolDate ,
                casestudyadult.clinician_comments, 
                casestudyadult.school_comments, 
                casestudyadult.isSendToCanvas, 
                casestudyadult.studentId, 
                rotation.rotationId,
                rotation.title as rotationname,
                student.firstName as studentfirstName,
                rankmaster.title as rankTitle,
                student.lastName as studentlastName ,
                casestudyadult.type,
                casestudyadult.status,
				rotation.parentRotationId,rotation.locationId as rotationLocationId, courses.title AS Coursename,courses.locationId
                FROM casestudyadult 
                LEFT JOIN student ON student.studentId = casestudyadult.studentId 
                LEFT JOIN rotation ON casestudyadult.rotationId=rotation.rotationId 
                INNER JOIN rankmaster ON rankmaster.rankId=student.rankId
				LEFT JOIN courses ON rotation.courseId=courses.courseId";

            if ($clinicianId) {
                $sql .= " INNER JOIN clinicianhospitalsite ON rotation.hospitalSiteId=clinicianhospitalsite.hospitalSiteId AND clinicianhospitalsite.clinicianId = $clinicianId
                        INNER JOIN clinician ON clinicianhospitalsite.clinicianId=clinician.clinicianId
                        LEFT JOIN hospitalsites ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId";
            }

            $sql .= "	where casestudyadult.schoolId=" . $SchoolId;

            if ($rotationId)
                $sql .= "  AND  casestudyadult.rotationId=" . $rotationId;
            if ($studentId > 0)
                $sql .= "  AND casestudyadult.studentId=" . $studentId;

            $sql .= " AND rotation.isDelete = 0 AND 0 = (select count(*) from rotation r where r.parentRotationId = rotation.rotationId)
                AND (
                    (CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
                    OR
                    (rotation.parentRotationId > 0
                    AND EXISTS (
                        SELECT 1
                        FROM rotation AS parent
                        WHERE parent.rotationId = rotation.parentRotationId
                        AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
                        AND rotation.isSchedule = 1 
                    )
                    )
                )
            ";
            if ($searchText != "") {
                $sql .= " AND ( rotation.title LIKE '%" . $searchText . "%' OR casestudyadult.type  LIKE '%" . $searchText . "%')";
            }

            $sql .= " ORDER BY casestudyadult.caseStudydate DESC)"; // Order by casestudydate in descending order

            // if($canvasStatus !='')
            //     $sql .="  AND casestudyadult.isSendToCanvas=".$canvasStatus;
        }
        $sql .= " ORDER BY caseStudydate DESC";
        $sql .= $generateLimitString;

        // echo 'SELECT->' . $sql;exit;
        $rows = $objDB->GetResultset($sql);
        unset($objDB);
        return $rows;
    }

    function GetFloorCaseStudyForStudentForPdf($caseStudyId)
    {
        $objDB = new clsDB();
        $rows = "";
        $sql = "SELECT casestudyfloor.*,student.firstName,student.lastName,concat(rotation.title,' (',hospitalsites.title,')') as rotationName,CONCAT(student.firstName, ' ', student.lastName) AS studentFullName,rotation.parentRotationId,rotation.locationId as rotationLocationId, courses.title AS Coursename,courses.locationId,hospitalsites.title AS hospitalSiteName FROM `casestudyfloor` 
        inner Join student on student.studentId = casestudyfloor.studentId  
		LEFT JOIN rotation ON casestudyfloor.`rotationId` = rotation.`rotationId` 
		LEFT JOIN courses ON rotation.courseId=courses.courseId
        INNER JOIN hospitalsites ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId

        WHERE caseStudyFloorId =" . $caseStudyId;
        // echo $sql;
        $rows = $objDB->GetDataRow($sql);
        unset($objDB);
        return $rows;
    }
    function GetAdultCaseStudyForStudentForPdf($caseStudyId)
    {
        $objDB = new clsDB();
        $rows = "";
        $sql = "SELECT casestudyadult.*,student.firstName,student.lastName,concat(rotation.title,' (',hospitalsites.title,')') as rotationName,CONCAT(student.firstName, ' ', student.lastName) AS studentFullName,rotation.parentRotationId,rotation.locationId as rotationLocationId, courses.title AS Coursename,courses.locationId,hospitalsites.title AS hospitalSiteName FROM `casestudyadult`
        inner Join student on student.studentId = casestudyadult.studentId 
		LEFT JOIN rotation ON casestudyadult.`rotationId` = rotation.`rotationId` 
		LEFT JOIN courses ON rotation.courseId=courses.courseId
        INNER JOIN hospitalsites ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
         WHERE caseStudyAdultId =" . $caseStudyId;

        // echo $sql;
        $rows = $objDB->GetDataRow($sql);
        unset($objDB);
        return $rows;
    }
    function GetPACRCaseStudyForStudentForPdf($caseStudyId)
    {
        $objDB = new clsDB();
        $rows = "";
        $sql = "SELECT casestudypacr.*,student.firstName,student.lastName,concat(rotation.title,' (',hospitalsites.title,')') as rotationName,CONCAT(student.firstName, ' ', student.lastName) AS studentFullName,rotation.parentRotationId,rotation.locationId as rotationLocationId, courses.title AS Coursename,courses.locationId,hospitalsites.title AS hospitalSiteName,schoolclinicalsiteunit.title As hospitalSiteUnitTitle FROM `casestudypacr`
        inner Join student on student.studentId = casestudypacr.studentId 
		LEFT JOIN rotation ON casestudypacr.`rotationId` = rotation.`rotationId` 
		LEFT JOIN courses ON rotation.courseId=courses.courseId
        INNER JOIN hospitalsites ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
        LEFT JOIN schoolclinicalsiteunit ON schoolclinicalsiteunit.schoolClinicalSiteUnitId = casestudypacr.hospitalSiteUnitId

         WHERE caseStudyPacrId =" . $caseStudyId;

        // echo $sql;
        $rows = $objDB->GetDataRow($sql);
        unset($objDB);
        return $rows;
    }



    /**
     * Retrieves detailed information for a specific case study from the database.
     *
     * @param string $tableName The name of the table containing the case study details.
     * @param string $primaryKey The primary key column name of the table.
     * @param int $casestudyId The ID of the case study to retrieve details for.
     * @param int $schoolId Optional. The ID of the school for filtering results.
     *                       Defaults to 0, which means no filtering by school.
     * @return array|false An associative array containing the case study details, or false if not found.
     */

    function GetAllCasestudyDetailsForLogs($tableName, $primaryKey, $casestudyId, $schoolId = 0)
    {
        $objDB = new clsDB();

        $sql = "SELECT $tableName.*, 
                   rotation.title as rotationName, 
                   $tableName.studentId as userId, 
                   CONCAT(student.firstName, ' ', student.lastName) AS userName, 
                   CONCAT(clinician.firstName, ' ', clinician.lastName) AS clinicianName, 
                   hospitalsites.title AS hospitalSiteName, 
                   schools.displayName as schoolName 
            FROM `$tableName` 
            INNER JOIN rotation ON $tableName.`rotationId` = rotation.`rotationId` 
            INNER JOIN student ON student.studentId = $tableName.studentId
            INNER JOIN hospitalsites ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
            INNER JOIN schools ON schools.schoolId = $tableName.schoolId
            LEFT JOIN clinician ON clinician.clinicianId = $tableName.clinicianId
            WHERE $primaryKey = " . $casestudyId;

        if ($schoolId) {
            $sql .= " AND $tableName.schoolId = " . $schoolId;
        }

        $row = $objDB->GetDataRow($sql);
        unset($objDB);

        return $row;
    }

    function GetAllClinicianReviewsForLogs($id, $schoolId = 0)
    {
        $objDB = new clsDB();

        $sql = "SELECT clinicianreviews.*,CONCAT(clinician.firstName, ' ', clinician.lastName) AS clinicianName, schools.displayName as schoolName FROM `clinicianreviews` 
		
        INNER JOIN clinician ON clinician.clinicianId = clinicianreviews.clinicianId
        INNER JOIN schools ON schools.schoolId = clinician.schoolId
		WHERE clinicianreviewId =" . $id;

        // echo $sql;exit;
        $row = $objDB->GetDataRow($sql);
        unset($objDB);
        return $row;
    }


    /**
     * Creates a log entry for a case study action
     *
     * @param int $caseStudyId ID of the case study
     * @param string $action Action type (Add, Edit, Delete)
     * @param int $userId User ID of the user performing the action
     * @param string $userType Type of user (e.g. student, clinician, etc.)
     * @param string $caseStudyType Type of case study (floor, Adult, PACR)
     *
     * @return array An array containing the log data, the original case study data, and any additional data
     */
    function createCaseStudyLog($caseStudyId, $action, $userId, $userType, $caseStudyType)
    {
        // Instantiate the Logger class
        $objLog = new clsLogger();
        $objCaseStudy = new clsCaseStudy(); // Assuming `Attendance` class is used for `prepareAttendanceLogData`

        // Get user details based on user type
        $userDetails = getUserDetails($userId, $userType);

        if ($caseStudyType == 'floor') {
            $tableName = 'casestudyfloor';
            $primaryKey = 'caseStudyFloorId';
        } else if ($caseStudyType == 'Adult') {
            $tableName = 'casestudyadult';
            $primaryKey = 'caseStudyAdultId';
        } else if ($caseStudyType == 'PACR') {
            $tableName = 'casestudypacr';
            $primaryKey = 'caseStudyPacrId';
        }
        // Retrieve checkoff details for the given checkoff ID and school ID
        if ($caseStudyType == 'clinician review')
            $rowData = $objCaseStudy->GetAllClinicianReviewsForLogs($caseStudyId); // caseStudyId = clinicianReviewId
        else
            $rowData = $objCaseStudy->GetAllCasestudyDetailsForLogs($tableName, $primaryKey, $caseStudyId);

        if ($rowData != '')
            $logData = $objLog->generateLogData($rowData, $userType);

        $additionalData = '';

        // Populate log data with user details
        $logData['userId'] = $userDetails['userId'];
        $logData['userName'] = $userDetails['userName'];
        $logMessage = '';
        if ($caseStudyType == 'clinician review') {
            $logMessage = $logData['userName'] . ' added ' . ucfirst($caseStudyType) . ' for PACR case study.';
        } else {
            if ($action == 'Add') {
                $logMessage = $logData['userName'] . ' added ' . ucfirst($caseStudyType) . ' case study for ' . $logData['rotationName'] . ' rotation.';
            } else if ($action == 'Edit') {
                $logMessage = $logData['userName'] . ' updated ' . ucfirst($caseStudyType) . ' case study from ' . $logData['rotationName'] . ' rotation.';
            } else if ($action == 'Delete' && $userType != 'Student') {
                $logMessage = $logData['userName'] . ' deleted ' . $rowData['userName'] . ' ' . ucfirst($caseStudyType) . ' case study from ' . $logData['rotationName'] . ' rotation.';
            } else if ($action == 'Delete' && $userType == 'Student') {
                $logMessage = $logData['userName'] . ' deleted ' . ucfirst($caseStudyType) . ' case study from ' . $logData['rotationName'] . ' rotation.';
            }
        }

        // Construct log message
        $logData['message'] = $logMessage;

        // Return the data
        return [$logData, $rowData, $additionalData];
    }


    /**
     * Saves the audit log for a case study action
     *
     * @param int $caseStudyId The ID of the case study
     * @param string $caseStudyType The type of the case study (floor, Adult, PACR)
     * @param int $clinicianId The ID of the clinician performing the action
     * @param string $userType The type of user performing the action (Student, Clinician, School)
     * @param string $action The action being performed (Add, Edit, Delete)
     * @param int $isMobile (Optional) Whether the action was performed on a mobile device
     *
     * @return bool Returns true on success
     */
    function saveCaseStudyAuditLog($caseStudyId, $caseStudyType, $userId, $userType, $action, $isMobile = 0)
    {
        // Instantiate the Logger and Checkoff classes
        $objLog = new clsLogger();
        $objCaseStudy = new clsCaseStudy();

        // Prepare log data
        [$logData, $rowData, $additionalData] = $objCaseStudy->createCaseStudyLog($caseStudyId, $action, $userId, $userType, $caseStudyType);
        $logData['isMobile'] = $isMobile;

        if ($action == 'Delete') {

            $objCaseStudy = new clsCaseStudy();
            if ($caseStudyType == 'floor')
                $objCaseStudy->DeleteFloorCaseStudy($caseStudyId);
            else if ($caseStudyType == 'Adult')
                $objCaseStudy->DeleteAdultCaseStudy($caseStudyId);
            else if ($caseStudyType == 'PACR')
                $objCaseStudy->DeletePACRCaseStudy($caseStudyId);
            unset($objCaseStudy);
        }
        // Save the log details
        $objLog->saveLogs($logData, $action, $caseStudyId, 'Case study', $rowData, $additionalData);

        // Clean up
        unset($objLog);
        unset($objCaseStudy);

        return true; // Return success or handle further actions as needed
    }

    function SaveCaseStudyPACRDetailsSection($caseStudyId)
    {

        $objDB = new clsDB();
        $sql = '';
        if ($caseStudyId > 0) {

            $sql = "  UPDATE casestudypacr SET  
                        rotationId =    '" . addslashes($this->rotationId) . "',
                        hospitalSiteId = '" . addslashes($this->hospitalSiteId) . "',
                        hospitalSiteUnitId = '" . addslashes($this->hospitalSiteUnitId) . "',
                        caseStudydate ='" . addslashes($this->caseStudydate) . "', 
                        pacrCohort = '" . addslashes($this->pacrCohort) . "',
                        pacrWeek = '" . addslashes($this->pacrWeek) . "',
                        
                        updatedBy = '" . addslashes($this->updatedBy) . "',
                        updatedDate = '" . addslashes($this->updatedDate) . "'
                        where caseStudyPacrId =" . $caseStudyId;

            // echo $sql;exit;
            $objDB->ExecuteQuery($sql);
        } else {
            $sql = "INSERT INTO casestudypacr 
                (`schoolId`, `studentId`, `rotationId`, `hospitalSiteId`, `hospitalSiteUnitId`,  `caseStudydate`, `type`, `pacrCohort`, `pacrWeek`,  `createdBy`, `createdDate`) 
                    VALUES ('" . addslashes($this->schoolId) . "',
                            '" . addslashes($this->studentId) . "',
                            '" . addslashes($this->rotationId) . "',
                            '" . addslashes($this->hospitalSiteId) . "',
                            '" . addslashes($this->hospitalSiteUnitId) . "',

                            '" . addslashes($this->caseStudydate) . "', 
                            '" . addslashes($this->type) . "',                              
                            '" . addslashes($this->pacrCohort) . "',                        
                            '" . addslashes($this->pacrWeek) . "',
                            '" . addslashes($this->createdBy) . "',
                            '" . addslashes($this->createdDate) . "'    
                            
                            
                                                    
                            )";
            // echo 'Insert->'.$sql;exit;
            $caseStudyId = $objDB->ExecuteInsertQuery($sql);
        }

        unset($objDB);
        return $caseStudyId;
    }

    function SaveCaseStudyPACRDemographicsSection($caseStudyId)
    {

        $objDB = new clsDB();
        $sql = '';
        if ($caseStudyId > 0) {

            $sql = "  UPDATE casestudypacr SET  
                        pacrAdmissionDate = '" . addslashes($this->pacrAdmissionDate) . "',
                        pacrPtAge = '" . addslashes($this->pacrPtAge) . "',	
                        pacrSex = '" . addslashes($this->pacrSex) . "',
                        pacrCC = '" . addslashes($this->pacrCC) . "',
                        pacrDx = '" . addslashes($this->pacrDx) . "',
                        pacrHx = '" . addslashes($this->pacrHx) . "',	
                        pacrPhysicalExam = '" . addslashes($this->pacrPhysicalExam) . "',
                        pacrBreathSounds = '" . addslashes($this->pacrBreathSounds) . "',
                        
                        updatedBy = '" . addslashes($this->updatedBy) . "',
                        updatedDate = '" . addslashes($this->updatedDate) . "'
                        where caseStudyPacrId =" . $caseStudyId;

            // echo $sql;exit;
            $objDB->ExecuteQuery($sql);
        }
        unset($objDB);
        return $caseStudyId;
    }

    function SaveCaseStudyPACRMedicationsSection($caseStudyId)
    {

        $objDB = new clsDB();
        $sql = '';
        if ($caseStudyId > 0) {

            $sql = "  UPDATE casestudypacr SET  
                        pacrMedications = '" . addslashes($this->pacrMedications) . "',
                        pacrClassifications = '" . addslashes($this->pacrClassifications) . "',
                        pacrModeOfctions = '" . addslashes($this->pacrModeOfctions) . "',	
                        pacrDosage = '" . addslashes($this->pacrDosage) . "',
                        
                        pacrRespiratoryOrders = '" . addslashes($this->pacrRespiratoryOrders) . "',
                        pacrIndications = '" . addslashes($this->pacrIndications) . "',
                        pacrGoals = '" . addslashes($this->pacrGoals) . "',	


                        pacrRTOrder= '" . addslashes($this->pacrRTOrder) . "',
                        pacrVentilator = '" . addslashes($this->pacrVentilator) . "',
                        pacrABGResults = '" . addslashes($this->pacrABGResults) . "',
                        pacrHemodynamicsList = '" . addslashes($this->pacrHemodynamicsList) . "',	
                        pacrHemodynamicCheckvalues = '" . addslashes($this->pacrHemodynamicCheckvalues) . "',
                        pacrCBCCheckValues = '" . addslashes($this->pacrCBCCheckValues) . "',
                        pacrSummery = '" . addslashes($this->pacrSummery) . "',
                        
                        updatedBy = '" . addslashes($this->updatedBy) . "',
                        updatedDate = '" . addslashes($this->updatedDate) . "'
                        where caseStudyPacrId =" . $caseStudyId;

            // echo $sql;exit;
            $objDB->ExecuteQuery($sql);
        }
        unset($objDB);
        return $caseStudyId;
    }

    function SaveCaseStudyPACRCbcSection($caseStudyId)
    {

        $objDB = new clsDB();
        $sql = '';
        if ($caseStudyId > 0) {

            $sql = "  UPDATE casestudypacr SET  
                        pacrCBCDetails = '" . addslashes($this->pacrCBCDetails) . "',
                        pacrCBCCheckValues = '" . addslashes($this->pacrCBCCheckValues) . "',
                        pacrInterpretPatient = '" . addslashes($this->pacrInterpretPatient) . "',
                        pacrInterpretABGStatus = '" . addslashes($this->pacrInterpretABGStatus) . "',
                        pacrAaFormula = '" . addslashes($this->pacrAaFormula) . "',	

                        pacrAaNormalvalue = '" . addslashes($this->pacrAaNormalvalue) . "',
                        pacrAaIsNormal = '" . addslashes($this->pacrAaIsNormal) . "',
                        pacrAaAbnormal = '" . addslashes($this->pacrAaAbnormal) . "',
                        pacrCaO2 = '" . addslashes($this->pacrCaO2) . "',	
                        pacrNormalCaO2= '" . addslashes($this->pacrNormalCaO2) . "',
                        pacrCaO2Isnormal = '" . addslashes($this->pacrCaO2Isnormal) . "',
                        pacrCaO2Abnormal = '" . addslashes($this->pacrCaO2Abnormal) . "',
                        pacrProcedures = '" . addslashes($this->pacrProcedures) . "',
                        pacrPFTList = '" . addslashes($this->pacrPFTList) . "',	
                        pacrCXRList = '" . addslashes($this->pacrCXRList) . "',
                        
                        updatedBy = '" . addslashes($this->updatedBy) . "',
                        updatedDate = '" . addslashes($this->updatedDate) . "'
                        where caseStudyPacrId =" . $caseStudyId;

            // echo $sql;exit;
            $objDB->ExecuteQuery($sql);
        }
        unset($objDB);
        return $caseStudyId;
    }

    function SaveCaseStudyFloorDetailsSection($caseStudyId)
    {

        $objDB = new clsDB();
        $sql = '';
        if ($caseStudyId > 0) {

            $sql = "  UPDATE casestudyfloor SET 
						rotationId = '" . addslashes($this->rotationId) . "',						
						caseStudydate = '" . addslashes($this->caseStudydate) . "',  
						floorCohort = '" . addslashes($this->floorCohort) . "',						
						floorAdmissionDate = '" . addslashes($this->floorAdmissionDate) . "',
                        floorPtAge = '" . addslashes($this->floorPtAge) . "',						
						floorSex = '" . addslashes($this->floorSex) . "',  
						floorHt = '" . addslashes($this->floorHt) . "', 
						floorSmoking = '" . addslashes($this->floorSmoking) . "',
						floorChiefComplaint = '" . addslashes($this->floorChiefComplaint) . "',						
						floorRespiratory = '" . addslashes($this->floorRespiratory) . "',
                        floorPastMedicalHx = '" . addslashes($this->floorPastMedicalHx) . "',						
						floorOtherPulmonaryProblems = '" . addslashes($this->floorOtherPulmonaryProblems) . "'                        
                        
                        where caseStudyFloorId =" . $caseStudyId;

            // echo $sql;
            // exit;
            $objDB->ExecuteQuery($sql);
        } else {
            $sql = "INSERT INTO casestudyfloor 
                (schoolId,studentId, rotationId,caseStudydate, type, floorCohort, floorAdmissionDate,
                                    floorPtAge, floorSex, floorHt, floorSmoking, floorChiefComplaint, floorRespiratory, floorPastMedicalHx, floorOtherPulmonaryProblems) 
                    VALUES ('" . addslashes($this->schoolId) . "',
                            '" . addslashes($this->studentId) . "',
                            '" . addslashes($this->rotationId) . "',
                            '" . addslashes($this->caseStudydate) . "',	
                            '" . addslashes($this->type) . "',
                            '" . addslashes($this->floorCohort) . "',						
                            '" . addslashes($this->floorAdmissionDate) . "',
                            '" . addslashes($this->floorPtAge) . "',
                            '" . addslashes($this->floorSex) . "',
                            '" . addslashes($this->floorHt) . "',
                            '" . addslashes($this->floorSmoking) . "',	
                            '" . addslashes($this->floorChiefComplaint) . "',	
                            '" . addslashes($this->floorRespiratory) . "',
                            '" . addslashes($this->floorPastMedicalHx) . "',						
                            '" . addslashes($this->floorOtherPulmonaryProblems) . "'
                            )";
            // echo 'Insert->'.$sql;exit;
            $caseStudyId = $objDB->ExecuteInsertQuery($sql);
        }

        unset($objDB);
        return $caseStudyId;
    }

    function SaveCaseStudyFloorVitalsSection($caseStudyId)
    {

        $objDB = new clsDB();
        $sql = '';
        if ($caseStudyId > 0) {

            $sql = "  UPDATE casestudyfloor SET  
                        floorHR = '" . addslashes($this->floorHR) . "',
						 floorSpontRR = '" . addslashes($this->floorSpontRR) . "',						
						 floorBP = '" . addslashes($this->floorBP) . "',
                         floorTemp = '" . addslashes($this->floorTemp) . "',
                         
                         floorSpO2 = '" . addslashes($this->floorSpO2) . "',						
						 floorIO = '" . addslashes($this->floorIO) . "',  
						 floorBreathSounds = '" . addslashes($this->floorBreathSounds) . "',
						 floorLevelofActivity = '" . addslashes($this->floorLevelofActivity) . "'
                        
                        where caseStudyFloorId =" . $caseStudyId;

            // echo $sql;exit;
            $objDB->ExecuteQuery($sql);
        }
        unset($objDB);
        return $caseStudyId;
    }

    function SaveCaseStudyFloorReadingsSection($caseStudyId)
    {

        $objDB = new clsDB();
        $sql = '';
        if ($caseStudyId > 0) {

            $sql = "  UPDATE casestudyfloor SET  
                        floorNa = '" . addslashes($this->floorNa) . "',
                        floorK = '" . addslashes($this->floorK) . "',						
                        floorCl = '" . addslashes($this->floorCl) . "',  
                        floorWBC = '" . addslashes($this->floorWBC) . "',
                        floorHgb = '" . addslashes($this->floorHgb) . "',						
                        floorHct = '" . addslashes($this->floorHct) . "',
                        floorCO2 = '" . addslashes($this->floorCO2) . "',						
                        floorBUN = '" . addslashes($this->floorBUN) . "',  
                        floorGlucose = '" . addslashes($this->floorGlucose) . "',
                        floorPlatelets = '" . addslashes($this->floorPlatelets) . "',						
                        floorINR = '" . addslashes($this->floorINR) . "',
                        floorSputumCult = '" . addslashes($this->floorSputumCult) . "',						
                        floorCreatinine = '" . addslashes($this->floorCreatinine) . "',  
                        floorLabInterpretation = '" . addslashes($this->floorLabInterpretation) . "',
                        floorXRayInterpretation = '" . addslashes($this->floorXRayInterpretation) . "',						
                        floorEKG = '" . addslashes($this->floorEKG) . "',
                        floorTrachSize = '" . addslashes($this->floorTrachSize) . "',						
                        floorTrachType = '" . addslashes($this->floorTrachType) . "',  
                        floorCuffPressure = '" . addslashes($this->floorCuffPressure) . "',
                        floorpH = '" . addslashes($this->floorpH) . "',						
                        floorPaCO2 = '" . addslashes($this->floorPaCO2) . "',
                        floorHCO3 = '" . addslashes($this->floorHCO3) . "',						
                        floorFVC = '" . addslashes($this->floorFVC) . "',  
                        floorFEF25 = '" . addslashes($this->floorFEF25) . "',
                        floorFEF1 = '" . addslashes($this->floorFEF1) . "',						
                        floorPaO2 = '" . addslashes($this->floorPaO2) . "',
                        floorSaO2 = '" . addslashes($this->floorSaO2) . "',						
                        floorFiO2 = '" . addslashes($this->floorFiO2) . "',  
                        floorPEFR = '" . addslashes($this->floorPEFR) . "',
                        floorFEV1 = '" . addslashes($this->floorFEV1) . "',						
                        floorDateBloodGas = '" . addslashes($this->floorDateBloodGas) . "',
                        floorLungValues = '" . addslashes($this->floorLungValues) . "',						
                        floorInterpretationABG = '" . addslashes($this->floorInterpretationABG) . "',  
                        floorInterpretationPFT = '" . addslashes($this->floorInterpretationPFT) . "',
                        floorInterpretationPAO2 = '" . addslashes($this->floorInterpretationPAO2) . "',						
                        floorInterpretationAO2 = '" . addslashes($this->floorInterpretationAO2) . "',
                        floorInterpretationCaO2 = '" . addslashes($this->floorInterpretationCaO2) . "',
                        floorInterpretationPFRatio = '" . addslashes($this->floorInterpretationPFRatio) . "',						
                        floorIPAP = '" . addslashes($this->floorIPAP) . "',
                        floorEPAP = '" . addslashes($this->floorEPAP) . "',						
                        floorRate = '" . addslashes($this->floorRate) . "', 
                        floorFiO2Setting = '" . addslashes($this->floorFiO2Setting) . "', 
                        floorItime = '" . addslashes($this->floorItime) . "',
                        floorRise = '" . addslashes($this->floorRise) . "',						
                        floorRamp = '" . addslashes($this->floorRamp) . "',
                        floorHumidityTemp = '" . addslashes($this->floorHumidityTemp) . "',
                        floorSuction = '" . addslashes($this->floorSuction) . "',						
                        floorCough = '" . addslashes($this->floorCough) . "',
                        floorSputumAmount = '" . addslashes($this->floorSputumAmount) . "'			

                        
                        where caseStudyFloorId =" . $caseStudyId;

            // echo $sql;exit;
            $objDB->ExecuteQuery($sql);
        }
        unset($objDB);
        return $caseStudyId;
    }

    function SaveCaseStudyAdultDetailsSection($caseStudyId)
    {

        $objDB = new clsDB();
        $sql = '';
        if ($caseStudyId > 0) {

            $sql = "  UPDATE casestudyadult SET    
                            adultCohort ='" . addslashes($this->adultCohort) . "',
                            caseStudydate = '" . addslashes($this->caseStudydate) . "',  
                            adultAdmissionDate='" . addslashes($this->adultAdmissionDate) . "',
                            adultPtAge ='" . addslashes($this->adultPtAge) . "',
                            adultAPGAR='" . addslashes($this->adultAPGAR) . "',
                            gestationalAOB = '" . addslashes($this->gestationalAOB) . "',
                            studentDOB ='" . addslashes($this->studentDOB) . "',
                            adultMomsPARA = '" . addslashes($this->adultMomsPARA) . "',
                            adultParentSmokingHx ='" . addslashes($this->adultParentSmokingHx) . "',
                            adultSex='" . addslashes($this->adultSex) . "',
                            adultHt = '" . addslashes($this->adultHt) . "',
                            adultSmoking ='" . addslashes($this->adultSmoking) . "',
                            adultChiefComplaint='" . addslashes($this->adultChiefComplaint) . "',
                            adultPastMedicalHx='" . addslashes($this->adultPastMedicalHx) . "',
                            adultRespiratory ='" . addslashes($this->adultRespiratory) . "',
                            adultOtherPulmonaryProblems='" . addslashes($this->adultOtherPulmonaryProblems) . "'                     
                        
                        where caseStudyAdultId =" . $caseStudyId;

            // echo $sql;
            // exit;
            $objDB->ExecuteQuery($sql);
        } else {
            $sql = "INSERT INTO casestudyadult 
                (schoolId,studentId, rotationId,caseStudydate, type, adultCohort, adultAdmissionDate,
                                    adultPtAge, adultSex, adultHt, adultSmoking, adultChiefComplaint, adultAPGAR, gestationalAOB, studentDOB, adultMomsPARA, adultParentSmokingHx, adultRespiratory, adultPastMedicalHx, adultOtherPulmonaryProblems) 
                    VALUES ('" . addslashes($this->schoolId) . "',
                            '" . addslashes($this->studentId) . "',
                            '" . addslashes($this->rotationId) . "',
                            '" . addslashes($this->caseStudydate) . "',	
                            '" . addslashes($this->type) . "',	                            
                            '" . addslashes($this->adultCohort) . "',						
                            '" . addslashes($this->adultAdmissionDate) . "',
                            '" . addslashes($this->adultPtAge) . "',
                            '" . addslashes($this->adultSex) . "',
                            '" . addslashes($this->adultHt) . "',
                            '" . addslashes($this->adultSmoking) . "',	
                            '" . addslashes($this->adultChiefComplaint) . "',	
                            '" . addslashes($this->adultAPGAR) . "',
                            '" . addslashes($this->gestationalAOB) . "',
                            '" . addslashes($this->studentDOB) . "',
                            '" . addslashes($this->adultMomsPARA) . "',
                            '" . addslashes($this->adultParentSmokingHx) . "',
                            '" . addslashes($this->adultRespiratory) . "',
                            '" . addslashes($this->adultPastMedicalHx) . "',						
                            '" . addslashes($this->adultOtherPulmonaryProblems) . "'
                            )";
            // echo 'Insert->'.$sql;exit;
            $caseStudyId = $objDB->ExecuteInsertQuery($sql);
        }

        unset($objDB);
        return $caseStudyId;
    }

    function SaveCaseStudyAdultVitalsSection($caseStudyId)
    {

        $objDB = new clsDB();
        $sql = '';
        if ($caseStudyId > 0) {

            $sql = "  UPDATE casestudyadult SET  
                        adultHR = '" . addslashes($this->adultHR) . "',
                        adultSpontRR = '" . addslashes($this->adultSpontRR) . "',
                        adultBP = '" . addslashes($this->adultBP) . "',
                        adultTemp = '" . addslashes($this->adultTemp) . "',
                        adultSpO2 = '" . addslashes($this->adultSpO2) . "',
                        adultIO = '" . addslashes($this->adultIO) . "',
                        adultBreathSounds = '" . addslashes($this->adultBreathSounds) . "',
                        adultLevelofActivity = '" . addslashes($this->adultLevelofActivity) . "'                     
                        
                        where caseStudyAdultId =" . $caseStudyId;

            // echo $sql;exit;
            $objDB->ExecuteQuery($sql);
        }
        unset($objDB);
        return $caseStudyId;
    }

    function SaveCaseStudyAdultReadingsSection($caseStudyId)
    {

        $objDB = new clsDB();
        $sql = '';
        if ($caseStudyId > 0) {

            $sql = "  UPDATE casestudyadult SET  
                        adultNa ='" . addslashes($this->adultNa) . "',
                            adultK='" . addslashes($this->adultK) . "',
                            adultCl ='" . addslashes($this->adultCl) . "',
                            adultWBC='" . addslashes($this->adultWBC) . "',
                            adultHgb ='" . addslashes($this->adultHgb) . "',
                            adultHct='" . addslashes($this->adultHct) . "',
                            adultCO2 ='" . addslashes($this->adultCO2) . "',
                            adultBUN='" . addslashes($this->adultBUN) . "',
                            adultGlucose ='" . addslashes($this->adultGlucose) . "',
                            adultPlatelets='" . addslashes($this->adultPlatelets) . "',
                            adultINR ='" . addslashes($this->adultINR) . "',
                            adultSputumCult='" . addslashes($this->adultSputumCult) . "',
                            adultCreatinine ='" . addslashes($this->adultCreatinine) . "',
                            adultLabInterpretation='" . addslashes($this->adultLabInterpretation) . "',
                            adultXRayInterpretation ='" . addslashes($this->adultXRayInterpretation) . "',
                            adultETT  = '" . addslashes($this->adultETT) . "',
                            adultPosition  = '" . addslashes($this->adultPosition) . "',
                            adultTrachType  = '" . addslashes($this->adultTrachType) . "',
                            adult1  = '" . addslashes($this->adult1) . "',
                            adultCuffPressure  = '" . addslashes($this->adultCuffPressure) . "',
                            
                            adultpH  = '" . addslashes($this->adultpH) . "',
                            adultPaCO2  = '" . addslashes($this->adultPaCO2) . "',
                            adultHCO3  = '" . addslashes($this->adultHCO3) . "',
                            adultSvO2  = '" . addslashes($this->adultSvO2) . "',
                            

                            adultCO  = '" . addslashes($this->adultCO) . "',
                            adultPAP  = '" . addslashes($this->adultPAP) . "',
                            adultPaO2  = '" . addslashes($this->adultPaO2) . "',
                            adultSaO2  = '" . addslashes($this->adultSaO2) . "',
                            adultFiO2Lpm  = '" . addslashes($this->adultFiO2Lpm) . "',
                            adultCVP  = '" . addslashes($this->adultCVP) . "',
                            adultPCWP  = '" . addslashes($this->adultPCWP) . "',
                            adultICP  = '" . addslashes($this->adultICP) . "',
                            adultDateBloodGas  = '" . addslashes($this->adultDateBloodGas) . "',
                            adultInterpretationHemodynamics  = '" . addslashes($this->adultInterpretationHemodynamics) . "',

                            adultInterpretationABG  = '" . addslashes($this->adultInterpretationABG) . "',
                            adultEKGResults  = '" . addslashes($this->adultEKGResults) . "',
                            adultInterpretationPAO2  = '" . addslashes($this->adultInterpretationPAO2) . "',
                            adultInterpretationAO2  = '" . addslashes($this->adultInterpretationAO2) . "',
                            adultInterpretationCaO2  = '" . addslashes($this->adultInterpretationCaO2) . "',
                            adultInterpretationPFRatio  = '" . addslashes($this->adultInterpretationPFRatio) . "'        
                        
                            where caseStudyAdultId =" . $caseStudyId;

            // echo $sql;exit;
            $objDB->ExecuteQuery($sql);
        }
        unset($objDB);
        return $caseStudyId;
    }

    function SaveCaseStudyAdultMedicationsSection($caseStudyId)
    {

        $objDB = new clsDB();
        $sql = '';
        if ($caseStudyId > 0) {

            $sql = "  UPDATE casestudyadult SET  
                            adultVentilator  = '" . addslashes($this->adultVentilator) . "',
                            adultFiO2  = '" . addslashes($this->adultFiO2) . "',
                            adultPiP  = '" . addslashes($this->adultPiP) . "',
                            adultPlat  = '" . addslashes($this->adultPlat) . "',
                            adultRR  = '" . addslashes($this->adultRR) . "',
                            adultMode  = '" . addslashes($this->adultMode) . "',
                            adultPSupport  = '" . addslashes($this->adultPSupport) . "',
                            adultMAP  = '" . addslashes($this->adultMAP) . "',
                            adultVE  = '" . addslashes($this->adultVE) . "',
                            adultSetRate  = '" . addslashes($this->adultSetRate) . "',
                            adultMaxFlow  = '" . addslashes($this->adultMaxFlow) . "',
                            adultSpontVt  = '" . addslashes($this->adultSpontVt) . "',
                            adultIE  = '" . addslashes($this->adultIE) . "',
                            adultSetVt  = '" . addslashes($this->adultSetVt) . "',
                            adultFlowSens  = '" . addslashes($this->adultFlowSens) . "',
                            adultCsta  = '" . addslashes($this->adultCsta) . "',
                            adultRaw  = '" . addslashes($this->adultRaw) . "',
                            adultVte  = '" . addslashes($this->adultVte) . "',
                            adultIBWVt  = '" . addslashes($this->adultIBWVt) . "',
                            adultItime  = '" . addslashes($this->adultItime) . "',
                            adultPEEPCPAP  = '" . addslashes($this->adultPEEPCPAP) . "',
                            adultHumidityTemp  = '" . addslashes($this->adultHumidityTemp) . "',
                            adultSputumAmount  = '" . addslashes($this->adultSputumAmount) . "',
                            adultOtherSettings  = '" . addslashes($this->adultOtherSettings) . "',
                            adultSuction  = '" . addslashes($this->adultSuction) . "',
                            adultCough  = '" . addslashes($this->adultCough) . "',
                            adultRecommendations  = '" . addslashes($this->adultRecommendations) . "',
                            adultLowHiPiP  = '" . addslashes($this->adultLowHiPiP) . "',
                            adultLowHiVte  = '" . addslashes($this->adultLowHiVte) . "',
                            adultLowHiVe  = '" . addslashes($this->adultLowHiVe) . "',
                            adultLowHiRR  = '" . addslashes($this->adultLowHiRR) . "',
                            adultApneaAlert  = '" . addslashes($this->adultApneaAlert) . "',
                            adultOtherAlarms  = '" . addslashes($this->adultOtherAlarms) . "',
                            adultIPAP  = '" . addslashes($this->adultIPAP) . "',
                            adultEPAP  = '" . addslashes($this->adultEPAP) . "',
                            adultRate  = '" . addslashes($this->adultRate) . "',
                            adultFiO21  = '" . addslashes($this->adultFiO21) . "',
                            adultItimeSetting  = '" . addslashes($this->adultItimeSetting) . "',
                            adultRise  = '" . addslashes($this->adultRise) . "',
                            adultRamp  = '" . addslashes($this->adultRamp) . "',
                            adultFVC  = '" . addslashes($this->adultFVC) . "',
                            adultFEF25  = '" . addslashes($this->adultFEF25) . "',
                            adultFEV1  = '" . addslashes($this->adultFEV1) . "',
                            adultPEFR  = '" . addslashes($this->adultPEFR) . "',
                            adultFEV1FVC  = '" . addslashes($this->adultFEV1FVC) . "',
                            adultLungVolumes  = '" . addslashes($this->adultLungVolumes) . "',
                            adultInterpretationPFT  = '" . addslashes($this->adultInterpretationPFT) . "', 
                            adultMedicationsUseList = '" . addslashes($this->adultMedicationsUseList) . "',
                            adultModificationCarePlanList = '" . addslashes($this->adultModificationCarePlanList) . "'     
                        
                            where caseStudyAdultId =" . $caseStudyId;

            // echo $sql;exit;
            $objDB->ExecuteQuery($sql);
        }
        unset($objDB);
        return $caseStudyId;
    }
}
