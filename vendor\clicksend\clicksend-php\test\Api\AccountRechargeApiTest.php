<?php
/**
 * AccountRechargeApiTest
 * PHP version 5
 *
 * @category Class
 * @package  ClickSend
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * ClickSend v3 API
 *
 * This is an official SDK for [ClickSend](https://clicksend.com)  Below you will find a current list of the available methods for clicksend.  *NOTE: You will need to create a free account to use the API. You can register [here](https://dashboard.clicksend.com/#/signup/step1/)..*
 *
 * OpenAPI spec version: 3.1
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * ClickSend Codegen version: 3.0.51
 */
/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Please update the test case below to test the endpoint.
 */

namespace ClickSend;

use ClickSend\Configuration;
use ClickSend\ApiException;
use <PERSON>lickSend\ObjectSerializer;
use PHPUnit\Framework\TestCase;

/**
 * AccountRechargeApiTest Class Doc Comment
 *
 * @category Class
 * @package  ClickSend
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */
class AccountRechargeApiTest extends TestCase
{

    /**
     * Setup before running any test cases
     */
    public static function setUpBeforeClass(): void
    {
    }

    /**
     * Setup before running each test case
     */
    public function setUp(): void
    {
    }

    /**
     * Clean up after running each test case
     */
    public function tearDown(): void
    {
    }

    /**
     * Clean up after running all test cases
     */
    public static function tearDownAfterClass(): void
    {
    }

    /**
     * Test case for rechargeCreditCardGet
     *
     * Get Credit Card info.
     *
     */
    public function testRechargeCreditCardGet()
    {
    }

    /**
     * Test case for rechargeCreditCardPut
     *
     * Update credit card info.
     *
     */
    public function testRechargeCreditCardPut()
    {
    }

    /**
     * Test case for rechargePackagesGet
     *
     * Get list of all packages.
     *
     */
    public function testRechargePackagesGet()
    {
    }

    /**
     * Test case for rechargePurchaseByPackageIdPut
     *
     * Purchase a package.
     *
     */
    public function testRechargePurchaseByPackageIdPut()
    {
    }

    /**
     * Test case for rechargeTransactionsByTransactionIdGet
     *
     * Get specific Transaction.
     *
     */
    public function testRechargeTransactionsByTransactionIdGet()
    {
    }

    /**
     * Test case for rechargeTransactionsGet
     *
     * Purchase a package.
     *
     */
    public function testRechargeTransactionsGet()
    {
    }
}
