<?php

include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsStudent.php');
include('../class/clsSchool.php');
include('../setRequest.php');
include('../class/Zebra_Image.php');
include('../class/clsChatApp.php');

if ($_SERVER['REQUEST_METHOD'] == "POST") {
    $objStudent = new clsStudent();
    $userId = $_SESSION['loggedStudentId'];

    $recordIdNumber = $_POST['recordIdNumber'];
    $firstName = $_POST['txtFirstName'];
    $lastName = $_POST['txtLastName'];
    $email = $_POST['txtEmail'];
    $address1 = trim($_POST['txtAddress1']);
    $address2 = trim($_POST['txtAddress2']);
    $cboCountry = trim($_POST['cboCountry']);
    $city = trim($_POST['txtCity']);
    $state = trim($_POST['cboState']);
    $zipCode = trim($_POST['txtZipCode']);
    $userName = $_POST['txtUsername'];

    $objStudent->recordIdNumber = $recordIdNumber;
    $objStudent->firstName = $firstName;
    $objStudent->lastName = $lastName;
    $objStudent->email = $email;
    $objStudent->address1 = $address1;
    $objStudent->address2 = $address2;
    $objStudent->cboCountry = $cboCountry;
    $objStudent->city = $city;
    $objStudent->stateId = $state;
    $objStudent->zip = $zipCode;
    $objStudent->username = $userName;
    $objStudent->updatedBy = $userId;
    $objStudent->updatedDate = date('Y-m-d H:i:s');
    $retUserId = $objStudent->UpdateStudentProfile($userId);

    if ($retUserId > 0) {
        $_SESSION["loggedStudentName"] = $userName;
        $_SESSION["loggedStudentFirstName"] = $firstName;
        $_SESSION["loggedStudentLastName"] = $lastName;

        $objChatApp = new clsChatApp();
        $objDB = new clsDB();
        $role_id = 4;
        $retstudentId = $userId;
        $profileImagePath = '';

        $userRoleManagementId = $objDB->GetSingleColumnValueFromTable('userrolemanagement', 'id', 'userId', $retstudentId, 'role_Id', $role_id);
        $phone = $objDB->GetSingleColumnValueFromTable('student', 'phone', 'studentId', $retstudentId, 'schoolId', $currentSchoolId);
        $userRoleManagementId = $userRoleManagementId ?: 0;

        $currenschoolDisplayname = $objDB->GetSingleColumnValueFromTable('schools', 'displayName', 'schoolId', $currentSchoolId);

        $retUserRoleManagementId = $objChatApp->setAndSaveChatAppUser($objChatApp, $retstudentId, $firstName, $lastName, $email, $phone, $address1, $profileImagePath, $currenschoolDisplayname, $currentSchoolId, $role_id, $userRoleManagementId);

        if ($retUserRoleManagementId) {
            $objChatApp->prepareAndSendUserData($objChatApp, $retstudentId, $firstName, $lastName, $email, $phone, $profileImagePath, $address1, $role_id, $currenschoolDisplayname, $currentSchoolId, $userRoleManagementId);
        }

        // Profile image upload/cropper
        $coverImage = $_POST['fileLogo'] ?? '';
        if ($coverImage != '') {
            $ext = getFileExtensionFromBase64($coverImage);
            $image_array = explode(",", explode(";", $coverImage)[1]);
            $coverImage = base64_decode($image_array[1]);

            $smallFilename = 'PROFILE_SMALL_' . $retUserId . '.' . $ext;
            $largeFilename = 'PROFILE_LARGE_' . $retUserId . '.' . $ext;

            $uploadPath = "../upload/schools/$currentSchoolId/student/$retUserId/";
            if (!file_exists($uploadPath)) mkdir($uploadPath, 0755, true);

            file_put_contents($uploadPath . $smallFilename, $coverImage);
            file_put_contents($uploadPath . $largeFilename, $coverImage);

            $objStudent->UpdateStudentPhotosFileName($retUserId, $smallFilename, $largeFilename);

            $profileImageName = $objDB->GetSingleColumnValueFromTable('student', 'profilePic', 'studentId', $retUserId);
            if (!empty($profileImageName)) {
                $profileImagePath = BASE_PATH . "/upload/schools/$currentSchoolId/student/$retUserId/$profileImageName?id=" . rand(1, 10000);
                $objDB->UpdateSingleColumnValueToTable('userrolemanagement', 'profileImagePath', $profileImagePath, 'id', $userRoleManagementId);
                $objChatApp->UpdateUserProfilePicTochat($profileImagePath, $retUserId, $role_id);
            }

            $_SESSION["loggedStudentProfileImagePath"] = GetStudentImagePath($retUserId, $currentSchoolId, $smallFilename);
            $_SESSION["loggedStudentProfileLargeImagePath"] = GetStudentImagePath($retUserId, $currentSchoolId, $largeFilename);
        }

        // Fallback: traditional upload
        else if (isset($_FILES['filePhoto']) && $_FILES['filePhoto']['name']) {
            $Image = $_FILES['filePhoto']['name'];
            $ext = strtolower(pathinfo($Image, PATHINFO_EXTENSION));
            if (!in_array($ext, ['jpg', 'jpeg', 'png', 'gif'])) {
                header('location:editprofile.html?status=InvalidFile');
                exit;
            }

            $uploadPath = "../upload/schools/$currentSchoolId/student/$retUserId/";
            if (!file_exists($uploadPath)) mkdir($uploadPath, 0755, true);

            $smallFilename = 'PROFILE_SMALL_' . $retUserId . '.' . $ext;
            $largeFilename = 'PROFILE_LARGE_' . $retUserId . '.' . $ext;

            copy($_FILES['filePhoto']['tmp_name'], $uploadPath . $smallFilename);
            copy($_FILES['filePhoto']['tmp_name'], $uploadPath . $largeFilename);

            if (isset($_POST['chkAutoCrop'])) {
                $img = new Zebra_Image();
                $img->source_path = $uploadPath . $smallFilename;
                $img->target_path = $uploadPath . $smallFilename;
                $img->resize(50, 50, ZEBRA_IMAGE_CROP_CENTER, -1);

                $img->source_path = $uploadPath . $largeFilename;
                $img->target_path = $uploadPath . $largeFilename;
                $img->resize(200, 200, ZEBRA_IMAGE_CROP_CENTER, -1);
            }

            $objStudent->UpdateStudentPhotosFileName($retUserId, $smallFilename, $largeFilename);
        }
            $profileImageName = $objDB->GetSingleColumnValueFromTable('student', 'profilePic', 'studentId', $retUserId);

            if (!empty($profileImageName)) {
                $profileImagePath = BASE_PATH . "/upload/schools/$currentSchoolId/student/$retUserId/$profileImageName?id=" . rand(1, 10000);
                $objDB->UpdateSingleColumnValueToTable('userrolemanagement', 'profileImagePath', $profileImagePath, 'id', $userRoleManagementId);
                $objChatApp->UpdateUserProfilePicTochat($profileImagePath, $retUserId, $role_id);
            }

            $_SESSION["loggedStudentProfileImagePath"] = GetStudentImagePath($retUserId, $currentSchoolId, $smallFilename);
            $_SESSION["loggedStudentProfileLargeImagePath"] = GetStudentImagePath($retUserId, $currentSchoolId, $largeFilename);
        

        // ✅ AUDIT LOG
         $objLog = new clsLogger();
        $action = $objLog::EDIT;
        $type = 'Profile';
        $userType = isset($_SESSION['loggedUserId']) ? $objLog::ADMIN : $objLog::STUDENT;
        $backUserId = $_SESSION['loggedUserId'] ?? 0;

        $objStudent->saveStudentAuditLog(
            $retUserId,
            $_SESSION['loggedStudentId'],
            $userType,
            $action,
            0,
            0,
            $type,
            0,
            $backUserId
        );

        header('location:editprofile.html?status=Updated');
    } else {
        header('location:editprofile.html?status=Error');
    }
} else {
    header('location:editprofile.html');
}
