<?php
// Redirect from mobile
$IsMobile = isset($_GET['IsMobile']) ? ($_GET['IsMobile']) : 0;
include('../includes/config.php');
include('includes/validateUserLogin.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsRotation.php');
include('../class/clsCaseStudy.php');
include('../class/clsLocations.php');
include('../setRequest.php');

$schoolId = 0;
$schoolId = $currentSchoolId;
$page_title = "Add Case Study";
$bedCrumTitle = 'Add';
$caseStudyId = '';
$rotationId = 0;
$adultCohort = '';
$adultAdmissionDate = '';
$adultPtAge = '';
$adultSex = '';
$adultHt = '';
$adultSmoking = '';
$adultChiefComplaint = '';
$adultRespiratory = '';
$adultOtherPulmonaryProblems = '';
$adultHR = '';
$adultSpontRR = '';
$adultBP = '';
$adultTemp = '';
$adultSpO2 = '';
$adultIO = '';
$adultBreathSounds = '';
$adultLevelofActivity = '';
$adultNa = '';
$adultK = '';
$adultCl = '';
$adultWBC = '';
$adultHgb = '';
$adultHct = '';
$adultCO2 = '';
$adultBUN = '';
$adultGlucose = '';
$adultPlatelets = '';
$adultINR = '';
$adultSputumCult = '';
$adultCreatinine = '';
$adultLabInterpretation = '';
$adultXRayInterpretation = '';
$adultETT  = '';
$adultPosition  = '';
$adultTrachType  = '';
$adult1  = '';
$adultCuffPressure  = '';
$caseStudydate = '';
$schoolDate = '';
$clinicianDate = '';
$adultPastMedicalHx = '';
$adultpH  = '';
$adultPaCO2  = '';
$adultHCO3  = '';
$adultSvO2  = '';
$adultOtherSettings = '';
$adultRecommendations = '';
$adultOtherAlarms = '';
$studentcomments  = 'Assessment: &#013;&#010;&#010;Plan of Care:&#013;&#010;&#010;Critical Thinking Questions:&#013;&#010;&#010;';
/* 21042021 */
$clinician_comments = '';
$school_comments = '';
// $clinician_comments = 'No';
// $school_comments = 'No';
/* 21042021 */

$adultCO  = '';
$adultPAP  = '';
$adultPaO2  = '';
$adultSaO2  = '';
$adultFiO2Lpm  = '';
$adultCVP  = '';
$adultPCWP  = '';
$adultICP  = '';
$adultDateBloodGas  = '';
$adultInterpretationHemodynamics  = '';

$adultInterpretationABG  = '';
$adultEKGResults  = '';
$adultInterpretationPAO2  = '';
$adultInterpretationAO2  = '';
$adultInterpretationCaO2  = '';

$adultInterpretationPFRatio  = '';
$adultVentilator  = '';
$adultFiO2  = '';
$adultPiP  = '';
$adultPlat  = '';

$adultRR  = '';
$adultMode  = '';
$adultPSupport  = '';
$adultMAP  = '';
$adultVE  = '';
$adultSetRate  = '';
$adultMaxFlow  = '';
$adultSpontVt  = '';
$adultIE  = '';
$adultSetVt  = '';

$adultFlowSens  = '';
$adultCsta  = '';
$adultRaw  = '';
$adultVte  = '';
$adultIBWVt  = '';
$adultItime  = '';
$adultPEEPCPAP  = '';
$adultHumidityTemp  = '';
$adultSputumAmount  = '';
$adultLowHiPiP  = '';

$adultLowHiVte  = '';
$adultLowHiVe  = '';
$adultLowHiRR  = '';
$adultApneaAlert  = '';
$adultIPAP  = '';
$adultEPAP  = '';
$adultRate  = '';
$adultFiO21  = '';

$adultSpontVt  = '';
$adultItimeSetting  = '';
$adultRise  = '';

$adultRamp  = '';
$adultFVC  = '';
$adultFEF25  = '';
$adultFEV1  = '';
$adultPEFR  = '';
$adultFEV1FVC  = '';
$adultLungVolumes  = '';
$adultInterpretationPFT  = '';
$adultMedicationsUse1  = '';
$adultModificationCarePlan1  = '';

$adultMedicationsUse2  = '';
$adultModificationCarePlan2  = '';
$adultMedicationsUse3  = '';
$adultModificationCarePlan3  = '';
$adultMedicationsUse4  = '';
$adultModificationCarePlan4  = '';
$adultMedicationsUse5  = '';
$adultModificationCarePlan5  = '';
$adultMedicationsUse6  = '';
$adultModificationCarePlan6  = '';
$adultMedicationsUse7  = '';
$adultModificationCarePlan7  = '';
$adultMedicationsUse8  = '';
$adultModificationCarePlan8  = '';
$adultMedicationsUse9  = '';
$adultModificationCarePlan9  = '';
$adultMedicationsUse10  = '';
$adultModificationCarePlan10  = '';
$adultMedicationsUse11  = '';
$adultModificationCarePlan11  = '';
$adultMedicationsUse12  = '';
$adultModificationCarePlan12  = '';
$adultMedicationsUse13  = '';
$adultModificationCarePlan13  = '';
$adultMedicationsUse14  = '';
$adultModificationCarePlan14  = '';
$adultMedicationsUse15  = '';
$adultModificationCarePlan15  = '';
$adultMedicationsUseList  = '';
$adultModificationCarePlanList  = '';
$caseStudydate = '';
$adultSuction = '';
$adultCough = '';
$view = '';
$adultAPGAR = '';
$gestationalAOB = '';
$studentDOB = '';
$adultMomsPARA = '';
$adultParentSmokingHx = '';
$studentId = $_SESSION["loggedStudentId"];
$TimeZone = $_SESSION["loggedStudentSchoolTimeZone"];

if (isset($_GET['rotationId'])) {
    $DefaultrotationId = $_GET['rotationId'];
    $rotationId = DecodeQueryData($DefaultrotationId);
}

if (isset($_GET['view'])) {
    $view = $_GET['view'];
}
$objRotation = new clsRotation();
//Get Active Rotations
$rotation = $objRotation->GetAllActiveRotationForDropdown($schoolId, $studentId);

if (isset($_GET['caseStudyId']) && DecodeQueryData($_GET['caseStudyId']) > 0) //Edit Mode
{
    //Get All Rotations
    if ($view == 'V') {
        $rotation = $objRotation->GetAllRotationForDropdown($schoolId, $studentId);
    }

    $caseStudyId = $_GET['caseStudyId'];
    $caseStudyId = DecodeQueryData($caseStudyId);
    $page_title = "Edit Case Study";
    $bedCrumTitle = 'Edit';
    // echo 'hi';
    $objCaseStudy = new clsCaseStudy();
    $rowMedications = $objCaseStudy->GetAdultMedicationsUseForStudent($caseStudyId);
    if ($rowMedications != '') {
        $aa = 0;
        while ($row1 = mysqli_fetch_assoc($rowMedications)) {
            // print_r($row);
            $shedules = [];

            $caseStudyAdultId = stripslashes($row1['caseStudyAdultId']);
            $adultMedicationsUse1 = stripslashes($row1['adultMedicationsUse1']);
            $adultMedicationsUse2 = stripslashes($row1['adultMedicationsUse2']);
            $adultMedicationsUse3 = stripslashes($row1['adultMedicationsUse3']);
            $adultMedicationsUse4 = stripslashes($row1['adultMedicationsUse4']);
            $adultMedicationsUse5 = stripslashes($row1['adultMedicationsUse5']);
            $adultMedicationsUse6 = stripslashes($row1['adultMedicationsUse6']);
            $adultMedicationsUse7 = stripslashes($row1['adultMedicationsUse7']);
            $adultMedicationsUse8 = stripslashes($row1['adultMedicationsUse8']);
            $adultMedicationsUse9 = stripslashes($row1['adultMedicationsUse9']);
            $adultMedicationsUse10 = stripslashes($row1['adultMedicationsUse10']);
            $adultMedicationsUse11 = stripslashes($row1['adultMedicationsUse11']);
            $adultMedicationsUse12 = stripslashes($row1['adultMedicationsUse12']);
            $adultMedicationsUse13 = stripslashes($row1['adultMedicationsUse13']);
            $adultMedicationsUse14 = stripslashes($row1['adultMedicationsUse14']);
            $adultMedicationsUse15 = stripslashes($row1['adultMedicationsUse15']);

            $adultModificationCarePlan1 = stripslashes($row1['adultModificationCarePlan1']);
            $adultModificationCarePlan2 = stripslashes($row1['adultModificationCarePlan2']);
            $adultModificationCarePlan3 = stripslashes($row1['adultModificationCarePlan3']);
            $adultModificationCarePlan4 = stripslashes($row1['adultModificationCarePlan4']);
            $adultModificationCarePlan5 = stripslashes($row1['adultModificationCarePlan5']);
            $adultModificationCarePlan6 = stripslashes($row1['adultModificationCarePlan6']);
            $adultModificationCarePlan7 = stripslashes($row1['adultModificationCarePlan7']);
            $adultModificationCarePlan8 = stripslashes($row1['adultModificationCarePlan8']);
            $adultModificationCarePlan9 = stripslashes($row1['adultModificationCarePlan9']);
            $adultModificationCarePlan10 = stripslashes($row1['adultModificationCarePlan10']);
            $adultModificationCarePlan11 = stripslashes($row1['adultModificationCarePlan11']);
            $adultModificationCarePlan12 = stripslashes($row1['adultModificationCarePlan12']);
            $adultModificationCarePlan13 = stripslashes($row1['adultModificationCarePlan13']);
            $adultModificationCarePlan14 = stripslashes($row1['adultModificationCarePlan14']);
            $adultModificationCarePlan15 = stripslashes($row1['adultModificationCarePlan15']);
            $shedules = [];
            $j = 0;
            $adultMedicationsUseArray = [];
            $adultModificationCarePlanArray = [];
            $adultMedicationsUseArray[$j = $j + 1][] = $adultMedicationsUse1;
            $adultModificationCarePlanArray[$j][] = $adultModificationCarePlan1;
            $adultMedicationsUseArray[$j = $j + 1][] = $adultMedicationsUse2;
            $adultModificationCarePlanArray[$j][] = $adultModificationCarePlan2;
            $adultMedicationsUseArray[$j = $j + 1][] = $adultMedicationsUse3;
            $adultModificationCarePlanArray[$j][] = $adultModificationCarePlan3;
            $adultMedicationsUseArray[$j = $j + 1][] = $adultMedicationsUse4;
            $adultModificationCarePlanArray[$j][] = $adultModificationCarePlan4;
            $adultMedicationsUseArray[$j = $j + 1][] = $adultMedicationsUse5;
            $adultModificationCarePlanArray[$j][] = $adultModificationCarePlan5;


            if ($adultMedicationsUse6  || $adultModificationCarePlan6) {
                $adultMedicationsUseArray[$j = $j + 1] = $adultMedicationsUse6;
                $adultModificationCarePlanArray[$j] = $adultModificationCarePlan6;
            }
            if ($adultMedicationsUse7  || $adultModificationCarePlan7) {
                $adultMedicationsUseArray[$j = $j + 1][] = $adultMedicationsUse7;
                $adultModificationCarePlanArray[$j][] = $adultModificationCarePlan7;
            }
            if ($adultMedicationsUse8  || $adultModificationCarePlan8) {
                $adultMedicationsUseArray[$j = $j + 1][] = $adultMedicationsUse8;
                $adultModificationCarePlanArray[$j][] = $adultModificationCarePlan8;
            }
            if ($adultMedicationsUse9  || $adultModificationCarePlan9) {
                $adultMedicationsUseArray[$j = $j + 1][] = $adultMedicationsUse9;
                $adultModificationCarePlanArray[$j][] = $adultModificationCarePlan9;
            }
            if ($adultMedicationsUse10 || $adultModificationCarePlan10) {
                $adultMedicationsUseArray[$j = $j + 1][] = $adultMedicationsUse10;
                $adultModificationCarePlanArray[$j][] = $adultModificationCarePlan10;
            }
            if ($adultMedicationsUse11 || $adultModificationCarePlan11) {
                $adultMedicationsUseArray[$j = $j + 1][] = $adultMedicationsUse11;
                $adultModificationCarePlanArray[$j][] = $adultModificationCarePlan11;
            }
            if ($adultMedicationsUse12 || $adultModificationCarePlan12) {
                $adultMedicationsUseArray[$j = $j + 1][] = $adultMedicationsUse12;
                $adultModificationCarePlanArray[$j][] = $adultModificationCarePlan12;
            }
            if ($adultMedicationsUse13 || $adultModificationCarePlan13) {
                $adultMedicationsUseArray[$j = $j + 1][] = $adultMedicationsUse13;
                $adultModificationCarePlanArray[$j][] = $adultModificationCarePlan13;
            }
            if ($adultMedicationsUse14 || $adultModificationCarePlan14) {
                $adultMedicationsUseArray[$j = $j + 1][] = $adultMedicationsUse14;
                $adultModificationCarePlanArray[$j][] = $adultModificationCarePlan14;
            }
            if ($adultMedicationsUse15 || $adultModificationCarePlan15) {
                $adultMedicationsUseArray[$j = $j + 1][] = $adultMedicationsUse15;
                $adultModificationCarePlanArray[$j][] = $adultModificationCarePlan15;
            }



            // $adultMedicationsUses[] = $adultMedicationsUseArray;
            // $adultModificationCarePlan[] = $adultModificationCarePlanArray;
            // print_r($adultMedicationsUses);
            $adultMedicationsUseList = json_encode($adultMedicationsUseArray);
            $adultModificationCarePlanList = json_encode($adultModificationCarePlanArray);
            // 	$objDB = new clsDB();

            // $resultadultMedicationsUses = $objDB->UpdateSingleColumnValueToTable('casestudyadult', 'adultMedicationsUseList',$adultMedicationsUseList,'caseStudyAdultId',$caseStudyAdultId);
            // $resultadultModificationCarePlan = $objDB->UpdateSingleColumnValueToTable('casestudyadult', 'adultModificationCarePlanList',$adultModificationCarePlanList,'caseStudyAdultId',$caseStudyAdultId);
            // unset($objDB);
            $aa++;
            // echo '<pre>';
            // print_r($shedules);

            // exit;
        }
    }
    $row = $objCaseStudy->GetAdultCaseStudyForStudent($caseStudyId);
    unset($objCaseStudy);
    if ($row == '') {
        header('location:caseStudyList.html');
    }
    $courselocationId = $row['locationId'];
    $parentRotationId = stripslashes($row['parentRotationId']);
    $rotationLocationId = stripslashes($row['rotationLocationId']);

    $locationId = 0;
    if ($rotationLocationId > 0) {
        if ($rotationLocationId != $courselocationId && $parentRotationId > 0) {
            if ($parentRotationId > 0) {
                if (!$rotationLocationId)
                    $locationId = $objRotation->GetLocationByRotation($rotationId);
                else
                    $locationId  = $rotationLocationId;
            }
        } else {
            $locationId  = $courselocationId;
        }
    }
    //Get Time Zone By Rotation 
    $objLocation = new clsLocations();
    $TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
    unset($objLocation);
    if ($TimeZone == '')
        $TimeZone = $_SESSION["loggedStudentSchoolTimeZone"];

    $rotationId  = stripslashes($row['rotationId']);
    $schoolDate  = ($row['schoolDate']);
    if ($schoolDate) {
        $schoolDate = converFromServerTimeZone($schoolDate, $TimeZone);
        $schoolDate = date('m/d/Y', strtotime($schoolDate));
    }
    $caseStudydate  = ($row['caseStudydate']);
    if ($caseStudydate) {
        $caseStudydate = converFromServerTimeZone($caseStudydate, $TimeZone);
        $caseStudydate = date('m/d/Y', strtotime($caseStudydate));
    }
    $clinicianDate  = stripslashes($row['ClinicianDate']);
    if ($clinicianDate) {
        $clinicianDate = converFromServerTimeZone($clinicianDate, $TimeZone);
        $clinicianDate = date('m/d/Y', strtotime($clinicianDate));
    }
    $adultCohort  = ($row['adultCohort']);
    $adultAdmissionDate  = ($row['adultAdmissionDate']);
    $adultPtAge  = ($row['adultPtAge']);
    $adultSex  = ($row['adultSex']);
    $adultHt  = ($row['adultHt']);
    $adultSmoking  = ($row['adultSmoking']);
    $adultChiefComplaint  = ($row['adultChiefComplaint']);
    $adultRespiratory  = ($row['adultRespiratory']);
    $adultPastMedicalHx  = ($row['adultPastMedicalHx']);
    $adultOtherPulmonaryProblems  = ($row['adultOtherPulmonaryProblems']);
    $adultHR  = ($row['adultHR']);
    $adultSpontRR  = ($row['adultSpontRR']);
    $adultBP  = ($row['adultBP']);
    $adultTemp  = ($row['adultTemp']);
    $adultSpO2  = ($row['adultSpO2']);
    $adultIO  = ($row['adultIO']);
    $adultBreathSounds  = ($row['adultBreathSounds']);
    $adultLevelofActivity  = ($row['adultLevelofActivity']);
    $adultNa  = ($row['adultNa']);
    $adultK  = ($row['adultK']);
    $studentcomments  = ($row['studentcomments']);

    /* 21042021 */
    $clinician_comments = stripslashes($row['clinician_comments']);
    // if ($clinician_comments != '') {
    //     $clinician_comments = $clinician_comments;
    // } else {
    //     $clinician_comments = 'No';
    // }
    $school_comments  = stripslashes($row['school_comments']);
    // if ($school_comments != '') {
    //     $school_comments = $school_comments;
    // } else {
    //     $school_comments = 'No';
    // }
    /* 21042021 */

    $adultCl  = ($row['adultCl']);
    $adultWBC  = ($row['adultWBC']);
    $adultHgb  = ($row['adultHgb']);
    $adultHct  = ($row['adultHct']);
    $adultCO2  = ($row['adultCO2']);
    $adultBUN  = ($row['adultBUN']);
    $adultGlucose  = ($row['adultGlucose']);
    $adultPlatelets  = ($row['adultPlatelets']);
    $adultINR  = ($row['adultINR']);
    $adultSputumCult  = ($row['adultSputumCult']);
    $adultCreatinine  = ($row['adultCreatinine']);
    $adultLabInterpretation  = ($row['adultLabInterpretation']);
    $adultXRayInterpretation  = ($row['adultXRayInterpretation']);
    $adultETT  = ($row['adultETT']);
    $adultPosition  = ($row['adultPosition']);
    $adultTrachType  = ($row['adultTrachType']);
    $adult1  = ($row['adult1']);
    $adultCuffPressure  = ($row['adultCuffPressure']);

    $adultpH  = ($row['adultpH']);
    $adultPaCO2  = ($row['adultPaCO2']);
    $adultHCO3  = ($row['adultHCO3']);
    $adultSvO2  = ($row['adultSvO2']);

    $adultCO  = ($row['adultCO']);
    $adultPAP  = ($row['adultPAP']);
    $adultPaO2  = ($row['adultPaO2']);
    $adultSaO2  = ($row['adultSaO2']);
    $adultFiO2Lpm  = ($row['adultFiO2Lpm']);
    $adultCVP  = ($row['adultCVP']);
    $adultPCWP  = ($row['adultPCWP']);
    $adultICP  = ($row['adultICP']);
    $adultDateBloodGas  = ($row['adultDateBloodGas']);
    $adultInterpretationHemodynamics  = ($row['adultInterpretationHemodynamics']);

    $adultInterpretationABG  = ($row['adultInterpretationABG']);
    $adultEKGResults  = ($row['adultEKGResults']);
    $adultInterpretationPAO2  = ($row['adultInterpretationPAO2']);
    $adultInterpretationAO2  = ($row['adultInterpretationAO2']);
    $adultInterpretationCaO2  = ($row['adultInterpretationCaO2']);

    $adultInterpretationPFRatio  = ($row['adultInterpretationPFRatio']);
    $adultVentilator  = ($row['adultVentilator']);
    $adultFiO2  = ($row['adultFiO2']);
    $adultPiP  = ($row['adultPiP']);
    $adultPlat  = ($row['adultPlat']);

    $adultRR  = ($row['adultRR']);
    $adultMode  = ($row['adultMode']);
    $adultPSupport  = ($row['adultPSupport']);
    $adultMAP  = ($row['adultMAP']);
    $adultVE  = ($row['adultVE']);
    $adultSetRate  = ($row['adultSetRate']);
    $adultMaxFlow  = ($row['adultMaxFlow']);
    $adultSpontVt  = ($row['adultSpontVt']);
    $adultIE  = ($row['adultIE']);
    $adultSetVt  = ($row['adultSetVt']);

    $adultFlowSens  = ($row['adultFlowSens']);
    $adultCsta  = ($row['adultCsta']);
    $adultRaw  = ($row['adultRaw']);
    $adultVte  = ($row['adultVte']);
    $adultIBWVt  = ($row['adultIBWVt']);
    $adultItime  = ($row['adultItime']);
    $adultPEEPCPAP  = ($row['adultPEEPCPAP']);
    $adultHumidityTemp  = ($row['adultHumidityTemp']);
    $adultSputumAmount  = ($row['adultSputumAmount']);
    $adultOtherSettings  = ($row['adultOtherSettings']);
    $adultRecommendations  = ($row['adultRecommendations']);

    $adultLowHiPiP  = ($row['adultLowHiPiP']);
    $adultLowHiVte  = ($row['adultLowHiVte']);
    $adultLowHiVe  = ($row['adultLowHiVe']);
    $adultLowHiRR  = ($row['adultLowHiRR']);
    $adultApneaAlert  = ($row['adultApneaAlert']);
    $adultOtherAlarms  = ($row['adultOtherAlarms']);

    $adultIPAP  = ($row['adultIPAP']);
    $adultEPAP  = ($row['adultEPAP']);
    $adultRate  = ($row['adultRate']);
    $adultFiO21  = ($row['adultFiO21']);

    $adultSpontVt  = ($row['adultSpontVt']);
    $adultItimeSetting  = ($row['adultItimeSetting']);
    $adultRise  = ($row['adultRise']);

    $adultRamp  = ($row['adultRamp']);
    $adultFVC  = ($row['adultFVC']);
    $adultFEF25  = ($row['adultFEF25']);
    $adultFEV1  = ($row['adultFEV1']);
    $adultPEFR  = ($row['adultPEFR']);
    $adultFEV1FVC  = ($row['adultFEV1FVC']);
    $adultLungVolumes  = ($row['adultLungVolumes']);
    $adultInterpretationPFT  = ($row['adultInterpretationPFT']);
    $adultMedicationsUse1  = ($row['adultMedicationsUse1']);
    $adultModificationCarePlan1  = ($row['adultModificationCarePlan1']);

    $adultMedicationsUse2  = ($row['adultMedicationsUse2']);
    $adultModificationCarePlan2  = ($row['adultModificationCarePlan2']);

    $adultMedicationsUse3  = ($row['adultMedicationsUse3']);
    $adultModificationCarePlan3  = ($row['adultModificationCarePlan3']);
    $adultMedicationsUse4  = ($row['adultMedicationsUse4']);
    $adultModificationCarePlan4  = ($row['adultModificationCarePlan4']);
    $adultMedicationsUse5  = ($row['adultMedicationsUse5']);
    $adultModificationCarePlan5  = ($row['adultModificationCarePlan5']);
    $adultMedicationsUse6  = ($row['adultMedicationsUse6']);
    $adultModificationCarePlan6  = ($row['adultModificationCarePlan6']);
    $adultMedicationsUse7  = ($row['adultMedicationsUse7']);
    $adultModificationCarePlan7  = ($row['adultModificationCarePlan7']);
    $adultMedicationsUse8  = ($row['adultMedicationsUse8']);
    $adultModificationCarePlan8  = ($row['adultModificationCarePlan8']);
    $adultMedicationsUse9  = ($row['adultMedicationsUse9']);
    $adultModificationCarePlan9  = ($row['adultModificationCarePlan9']);
    $adultMedicationsUse10  = ($row['adultMedicationsUse10']);
    $adultModificationCarePlan10  = ($row['adultModificationCarePlan10']);
    $adultMedicationsUse11  = ($row['adultMedicationsUse11']);
    $adultModificationCarePlan11  = ($row['adultModificationCarePlan11']);
    $adultMedicationsUse12  = ($row['adultMedicationsUse12']);
    $adultModificationCarePlan12  = ($row['adultModificationCarePlan12']);
    $adultMedicationsUse13  = ($row['adultMedicationsUse13']);
    $adultModificationCarePlan13  = ($row['adultModificationCarePlan13']);
    $adultMedicationsUse14  = ($row['adultMedicationsUse14']);
    $adultModificationCarePlan14  = ($row['adultModificationCarePlan14']);
    $adultMedicationsUse15  = ($row['adultMedicationsUse15']);
    $adultModificationCarePlan15  = ($row['adultModificationCarePlan15']);
    $adultMedicationsUseList  = ($row['adultMedicationsUseList']);
    $adultModificationCarePlanList  = ($row['adultModificationCarePlanList']);
    $adultCough  = ($row['adultCough']);
    $adultSuction  = ($row['adultSuction']);
    $adultAPGAR  = ($row['adultAPGAR']);
    $gestationalAOB  = ($row['gestationalAOB']);
    // if($gestationalAOB)
    // {
    // 	$gestationalAOB = converFromServerTimeZone($gestationalAOB,$TimeZone);		
    // 	$gestationalAOB = date('m/d/Y', strtotime($gestationalAOB));
    // }
    $studentDOB  = ($row['studentDOB']);
    if ($studentDOB) {
        $studentDOB = converFromServerTimeZone($studentDOB, $TimeZone);
        $studentDOB = date('m/d/Y', strtotime($studentDOB));
    }
    $adultMomsPARA  = ($row['adultMomsPARA']);
    $adultParentSmokingHx  = ($row['adultParentSmokingHx']);
}

$readonly = ($caseStudydate != '') ? 'readonly' : '';


?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content
        must come *after* these tags -->
    <title>
        <?php echo ($page_title); ?>
    </title>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/stepper.css">
    <?php include('includes/headercss.php'); ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">

    <script type="module" src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.esm.js"></script>
    <script nomodule src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.js"></script>
    <style>
        .d-flex {
            display: flex;
        }

        .margin-right {
            margin-right: 7px;
        }

        .checkboxStyle {
            margin: 0 !important;
            width: 50%;
            border: 1px solid #ccc;
        }

        .alignCenter {
            align-items: center !important;
        }

        .abnormal {
            color: red !important;
        }

        .input-group-addon {
            position: absolute;
            right: 7px;
            /* width: 100%; */
            z-index: 99;
            width: 35px;
            margin: auto;
            top: 5px;
            border-left: 1px solid #ccc;
            border-radius: 50% !important;
            padding: 10px -2px;
            height: 35px;
            /* background: #01A750; */
            /* color: #fff; */
            color: #555;
            background: #f6f9f9;
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .panel-default>.panel-heading {
            background-color: transparent !important;
        }

        .btn-success,
        .btn-default {
            padding: 8px 25px;
            border-radius: 10px;
        }

        .panel {
            border-radius: 14px !important;
        }

        .input-group-addon {
            position: absolute;
            right: 7px;
            /* width: 100%; */
            z-index: 99;
            width: 35px;
            margin: auto;
            top: 5px;
            border-left: 1px solid #ccc;
            border-radius: 50% !important;
            padding: 10px -2px;
            height: 35px;
            /* background: #01A750; */
            /* color: #fff; */
            color: #555;
            background: #f6f9f9;
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .panel-default>.panel-heading {
            background-color: transparent !important;
        }

        .btn-success,
        .btn-default {
            padding: 8px 25px;
            border-radius: 10px;
        }

        .panel {
            border-radius: 14px !important;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 45px !important;
        }

        .required-select2 {
            border-left: solid 3px red !important;
            border-radius: 12px !important;
        }

        .select2-container--default .select2-selection--single {
            background-color: #f6f9f9 !important;
            cursor: default !important;
            height: 45px !important;
            border-radius: 10px !important;
        }

        .select2-container--default .select2-selection--single {
            border: none !important;
        }

        .panel,
        .form-group {
            margin-bottom: 10px;
        }

        .bootstrap-datetimepicker-widget {
            border-radius: 12px !important;
        }

        .form-control {
            height: 45px;
        }

        textarea.form-control {
            height: auto;
        }

        .pl-0 {
            padding-left: 0;
        }

        .blood-pressure-input {
            width: 80px;
            display: inline-block;
            margin: 5px 10px;
        }

        .abnormal-value-checkbox {
            padding: 0;
            width: 37px;
            height: 37px;
            margin-left: 10px;
        }

        .row-delete-icon {
            font-size: 18px;
            padding-left: 6px;
        }

        .mobile-block {
            display: flex;
            align-items: center;
        }

        /* .form-horizontal .form-group {
            margin-right: 0;
            margin-left: 0;
        } */


        .select2-container {
            width: 100% !important;
        }

        ion-icon {
            pointer-events: none;
        }

        .icon-inner,
        .ionicon,
        svg {
            color: #01A750;
        }

        @media screen and (max-width: 500px) {
            .panel-body ol {
                padding-left: 20px;
            }

            .panel-default>.panel-heading {
                padding-left: 5px;
            }

            .blood-pressure-input {
                width: 100%;
                margin: 5px 0;
            }

            /* .container-zero{
            padding: 0;
         } */

            .mobile-gray-border {
                border: 1px solid #ececec;
            }

            .collapsible {
                cursor: pointer;
                padding: 15px;
                /* border: 1px solid #181818; */
                /* background-color: #f9f9f9; */
                display: flex;
                justify-content: space-between;
                align-items: center;
                /* border-radius: 14px; */
            }

            .collapsible p {
                margin: 0;
            }

            .collapsible-arrow {
                font-size: 18px;
                transition: transform 0.3s ease;
            }

            .content {
                display: none;
                padding: 10px 0;
                /* border-top: 1px solid #ececec; */
            }

            .content.active {
                display: block;
            }

            .active.collapsible-arrow {
                transform: rotate(180deg);
            }

            .row-delete-icon {
                position: absolute;
                top: -82px;
                right: 20px;
            }

            .mobile-block {
                display: block;
            }

            .form-stepper-horizontal li {
                min-width: 90px;
            }

            .form-step {
                padding: 2rem 1rem;
            }

            .pt-0 {
                padding-top: 0 !important;
            }
        }
    </style>
</head>

<body>
    <?php if ($IsMobile == 0) { ?>

        <?php include('includes/header.php'); ?>
        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
                        <li>
                            <a href="dashboard.html">Home</a>
                        </li>
                        <li>
                            <a href="caseStudyList.html?active=adult">Case Study</a>
                        </li>
                        <li>
                            <a href="caseStudyList.html?active=adult">Adult ICU & NICU/PICU</a>
                        </li>
                        <li class="active">
                            <?php echo ($bedCrumTitle); ?>
                        </li>
                    </ol>
                </div>
            </div>
        </div>
    <?php  } else  ?>

    <div class="container mb-15 mobile-padding-4">
        <!-- stepper start -->
        <div>
            <div id="multi-step-form-container">
                <!-- Form Steps / Progress Bar -->
                <ul class="form-stepper form-stepper-horizontal text-center mx-auto">
                    <!-- Step 1 -->
                    <li class="form-stepper-active text-center form-stepper-list" style="" step="1">
                        <a class="mx-2">
                            <span class="form-stepper-circle">
                                <span>1</span>
                            </span>
                            <div class="label stepper-label" style="">Details</div>
                        </a>
                    </li>
                    <!-- Step 2 -->
                    <li class="form-stepper-unfinished text-center form-stepper-list" step="2">
                        <a class="mx-2">
                            <span class="form-stepper-circle text-muted">
                                <span>2</span>
                            </span>
                            <div class="label text-muted stepper-label">Vitals</div>
                        </a>
                    </li>
                    <!-- Step 3 -->
                    <li class="form-stepper-unfinished text-center form-stepper-list" step="3">
                        <a class="mx-2">
                            <span class="form-stepper-circle text-muted">
                                <span>3</span>
                            </span>
                            <div class="label text-muted stepper-label">Readings</div>
                        </a>
                    </li>
                    <!-- Step 4 -->
                    <li class="form-stepper-unfinished text-center form-stepper-list" step="4">
                        <a class="mx-2">
                            <span class="form-stepper-circle text-muted">
                                <span>4</span>
                            </span>
                            <div class="label text-muted stepper-label">Medications</div>
                        </a>
                    </li>
                    <!-- Step 5 -->
                    <li class="form-stepper-unfinished text-center form-stepper-list" step="5">
                        <a class="mx-2">
                            <span class="form-stepper-circle text-muted">
                                <span>5</span>
                            </span>
                            <div class="label text-muted stepper-label">Comments</div>
                        </a>
                    </li>
                    <!-- Step 6 -->
                    <!-- <li class="form-stepper-unfinished text-center form-stepper-list" step="6">
                            <a class="mx-2">
                                <span class="form-stepper-circle text-muted">
                                    <span>6</span>
                                </span>
                                <div class="label text-muted stepper-label">Comment</div>
                            </a>
                        </li> -->
                </ul>
            </div>
        </div>
        <form id="formCaseStudy" data-parsley-validate="data-parsley-validate" class="form-horizontal" method="POST">
            <!-- Mobile redirect -->
            <input type="hidden" name="IsMobile" id="IsMobile" value="<?php echo $IsMobile; ?>">
            <input type="hidden" name="hiddenCaseStudyId" id="hiddenCaseStudyId" value="<?php echo $caseStudyId; ?>">
            <input type="hidden" name="hiddenStudentId" id="hiddenStudentId" value="<?php echo $studentId; ?>">
            <input type="hidden" name="hiddenType" id="hiddenType" value="Adult">
            <input type="hidden" name="hiddenView" id="hiddenView" value="<?php echo $view; ?>">

            <section id="step-1" class="form-step pt-0" data-parsley-validate>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="caseStudydate">
                                Date</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <div class='input-group date w-full' id='caseStudydate' style="position: relative;">
                                    <input type='text' name="caseStudydate" id="caseStudydate" class="form-control input-md required-input rotation_date dateInputFormat" value="<?php echo $caseStudydate; ?>" <?php echo $readonly; ?> required="required" data-parsley-errors-container="#error-txtDate" />
                                    <span class="input-group-addon calender-icon">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>
                                </div>
                                <div id="error-txtDate"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="cborotation">Rotation</label>
                            <div class="col-md-12">
                                <select id="cborotation" name="cborotation" class="form-control input-md  select2_single required-input" data-parsley-errors-container="#error-txtRotation" required>
                                    <option value="" selected="selected">Select</option>
                                    <?php
                                    if ($rotation != "") {
                                        while ($row = mysqli_fetch_assoc($rotation)) {
                                            $selrotationId  = $row['rotationId'];
                                            $name  = stripslashes($row['title']);
                                    ?>
                                            <option value="<?php echo EncodeQueryData($selrotationId); ?>" <?php if ($rotationId == $selrotationId) { ?> selected="true" <?php } ?>>
                                                <?php echo ($name); ?>
                                            </option>
                                    <?php
                                        }
                                    }
                                    unset($objRotation);
                                    ?>
                                </select>
                                <div id="error-txtRotation"></div>

                            </div>
                        </div>
                    </div>
                </div>
                <?php if ($caseStudyId) { ?>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="ClinicianDate">Clinician Signature
                                    <?php echo ($clinician_comments != '') ? '<span class="text-danger"><b>*</b></span>' : ''; ?>
                                </label>
                                <div class="col-md-12 col-sm-12 col-xs-12">
                                    <div class='input-group date w-full' id='ClinicianDate' style="position: relative;">
                                        <input type='text' name="ClinicianDate" id="ClinicianDate" class="form-control input-md dateInputFormat" value="<?php echo $clinicianDate; ?>" readonly="readonly" />
                                        <span class="input-group-addon calender-icon">
                                            <span class="glyphicon glyphicon-calendar"></span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="schoolDate">School Signature
                                    <?php echo ($school_comments != '') ? '<span class="text-danger"><b>*</b></span>' : ''; ?>
                                </label>
                                <div class="col-md-12 col-sm-12 col-xs-12">
                                    <div class='input-group date w-full' id='schoolDate' style="position: relative;">
                                        <input type='text' name="schoolDate" id="schoolDate" class="form-control input-md dateInputFormat" value="<?php echo $schoolDate; ?>" readonly="readonly" />
                                        <span class="input-group-addon calender-icon">
                                            <span class="glyphicon glyphicon-calendar"></span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php } ?>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="adultCohort">Cohort</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <input type="text" class="form-control input-md" value="<?php echo $adultCohort; ?>" name="adultCohort" id="adultCohort">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="adultAdmissionDate">Admission Date</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <div class='input-group date w-full' id='adultAdmissionDate' style="position: relative;">
                                    <input type='text' name="adultAdmissionDate" id="adultAdmissionDate" class="form-control input-md dateInputFormat " value="<?php echo $adultAdmissionDate; ?>" />
                                    <span class="input-group-addon calender-icon">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 p-0">
                        <div class=" col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="adultPtAge">Pt Age</label>
                                <div class="col-md-12 col-sm-12 col-xs-12">
                                    <input type="text" class="form-control input-md" value="<?php echo $adultPtAge; ?>" name="adultPtAge" id="adultPtAge">
                                </div>
                            </div>
                        </div>
                        <div class=" col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="Sex">Sex</label>
                                <div class="col-md-12 col-sm-12 col-xs-12">
                                    <input type="text" class="form-control input-md" value="<?php echo $adultSex; ?>" name="adultSex" id="adultSex">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 p-0">
                        <div class=" col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="adultHt">Ht</label>
                                <div class="col-md-12 col-sm-12 col-xs-12">
                                    <input type="text" class="form-control input-md" value="<?php echo $adultHt; ?>" name="adultHt" id="adultHt">
                                </div>
                            </div>
                        </div>
                        <div class=" col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="adultSmoking">Smoking: (Pack/Yrs)</label>
                                <div class="col-md-12 col-sm-12 col-xs-12">
                                    <input type="text" class="form-control input-md" value="<?php echo $adultSmoking; ?>" name="adultSmoking" id="adultSmoking">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="gestationalAOB">Gestational Age at Birth</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <input type='text' name="gestationalAOB" id="gestationalAOB" class="form-control input-md " value="<?php echo $gestationalAOB; ?>" />
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="studentDOB">Date of Birth</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <div class='input-group date w-full' id='studentDOB' style="position: relative;">
                                    <input type='text' name="studentDOB" id="studentDOB" class="form-control input-md dateInputFormat" value="<?php echo $studentDOB; ?>" />
                                    <span class="input-group-addon calender-icon">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="adultAPGAR">APGAR</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <input type="text" class="form-control input-md" value="<?php echo $adultAPGAR; ?>" name="adultAPGAR" id="adultAPGAR">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="adultRespiratory">Respiratory Rx(s)</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <input type="text" class="form-control input-md" value="<?php echo $adultRespiratory; ?>" name="adultRespiratory" id="adultRespiratory">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="adultChiefComplaint">Chief Complaint/Admitting Diagnosis</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <textarea type="text" class="form-control input-md" name="adultChiefComplaint" id="adultChiefComplaint" rows="3"><?php echo $adultChiefComplaint; ?></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="adultPastMedicalHx">Past Medical Hx</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <textarea type="text" class="form-control input-md" name="adultPastMedicalHx" id="adultPastMedicalHx" rows="3"><?php echo $adultPastMedicalHx; ?></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="adultMomsPARA">
                                Mom’s PARA</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <input type="text" class="form-control input-md" value="<?php echo $adultMomsPARA; ?>" name="adultMomsPARA" id="adultMomsPARA">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="adultParentSmokingHx">Parent/Guardian Smoking Hx</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <input type="text" class="form-control input-md" value="<?php echo $adultParentSmokingHx; ?>" name="adultParentSmokingHx" id="adultParentSmokingHx">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="adultOtherPulmonaryProblems">Other Pulmonary Problems</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <input type="text" class="form-control input-md" value="<?php echo $adultOtherPulmonaryProblems; ?>" name="adultOtherPulmonaryProblems" id="adultOtherPulmonaryProblems">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-3 flex-end">
                    <button class="button next btn-navigate-form-step" type="button" step_number="2">Save & Next</button>
                </div>
            </section>
            <!-- Step 2 Content, default hidden on page load. -->
            <section id="step-2" class="form-step d-none pt-0" data-parsley-validate>
                <div class="row padding_top_ten margin_bottom_ten w-full" style="margin: 10px auto;">
                    <!-- <div class="row text-center margin_bottom_ten">
                            <label>
                                <b>Vital Signs</b>
                            </label>
                        </div> -->
                    <div class="row ">
                        <div class="col-md-6 p-0">
                            <div class=" col-md-4">
                                <div class="form-group">
                                    <label class="col-md-12 control-label" for="adultHR">HR</label>
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <input type="text" class="form-control input-md" value="<?php echo $adultHR; ?>" name="adultHR" id="adultHR">
                                    </div>
                                </div>
                            </div>
                            <div class=" col-md-4">
                                <div class="form-group">
                                    <label class="col-md-12 control-label" for="adultSpontRR">Spont. RR</label>
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <input type="text" class="form-control input-md" value="<?php echo $adultSpontRR; ?>" name="adultSpontRR" id="adultSpontRR">
                                    </div>
                                </div>
                            </div>
                            <div class=" col-md-4">
                                <div class="form-group">
                                    <label class="col-md-12 control-label" for="adultBP">BP</label>
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <input type="text" class="form-control input-md" value="<?php echo $adultBP; ?>" name="adultBP" id="adultBP">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 p-0">
                            <div class=" col-md-4">
                                <div class="form-group">
                                    <label class="col-md-12 control-label" for="adultTemp">Temp</label>
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <input type="text" class="form-control input-md" value="<?php echo $adultTemp; ?>" name="adultTemp" id="adultTemp">
                                    </div>
                                </div>
                            </div>
                            <div class=" col-md-4">
                                <div class="form-group">
                                    <label class="col-md-12 control-label" for="adultSpO2">SpO2</label>
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <input type="text" class="form-control input-md" value="<?php echo $adultSpO2; ?>" name="adultSpO2" id="adultSpO2">
                                    </div>
                                </div>
                            </div>
                            <div class=" col-md-4">
                                <div class="form-group">
                                    <label class="col-md-12 control-label" for="adultIO">I/O</label>
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <input type="text" class="form-control input-md" value="<?php echo $adultIO; ?>" name="adultIO" id="adultIO">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="adultBreathSounds">Breath Sounds</label>
                                <div class="col-md-12 col-sm-12 col-xs-12">
                                    <input type="text" class="form-control input-md" value="<?php echo $adultBreathSounds; ?>" name="adultBreathSounds" id="adultBreathSounds">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="adultLevelofActivity">Level of Activity/Cooperation</label>
                                <div class="col-md-12 col-sm-12 col-xs-12">
                                    <input type="text" class="form-control input-md" value="<?php echo $adultLevelofActivity; ?>" name="adultLevelofActivity" id="adultLevelofActivity">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-3 flex-end">
                    <button class="button btn-navigate-form-step button-prev" type="button" step_number="1">Prev</button>
                    <button class="button next btn-navigate-form-step" type="button" step_number="3">Save & Next</button>
                </div>
            </section>
            <!-- Step 3 Content, default hidden on page load. -->
            <section id="step-3" class="form-step d-none pt-0" data-parsley-validate>

                <div class="row padding_top_ten margin_bottom_ten w-full" style="margin: 10px auto;">
                    <!-- <div class="row">
                            <div class="col-md-6 text-center mobile-collaps-view">
                                <label>
                                    <b>Chemistry Panel</b>
                                </label>
                            </div>
                            <div class="col-md-6 text-center">
                                <label>
                                    <b>Hematology Panel</b>
                                </label>
                            </div>
                        </div> -->
                    <div class="row">
                        <div class="col-md-12 desktop-px-zero">
                            <div class="col-md-6 p-0 mb-10 mobile-padding-zero mobile-collaps-view">
                                <div class="collapsible">
                                    <p style="text-align: center;"> <b> Chemistry Panel </b></p>
                                    <i class="fa fa-chevron-down desktop-hide" aria-hidden="true"></i>
                                </div>
                                <div class="content">
                                    <div class="col-md-12 p-0">
                                        <div class=" col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultNa">Na+
                                                </label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultNa; ?>" name="adultNa" id="adultNa">
                                                </div>
                                            </div>
                                        </div>
                                        <div class=" col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultK">K+</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultK; ?>" name="adultK" id="adultK">
                                                </div>
                                            </div>
                                        </div>
                                        <div class=" col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultCl">Cl+</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultCl; ?>" name="adultCl" id="adultCl">
                                                </div>
                                            </div>
                                        </div>
                                        <div class=" col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultCO2">CO2
                                                </label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultCO2; ?>" name="adultCO2" id="adultCO2">
                                                </div>
                                            </div>
                                        </div>
                                        <div class=" col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultBUN">BUN</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultBUN; ?>" name="adultBUN" id="adultBUN">
                                                </div>
                                            </div>
                                        </div>
                                        <div class=" col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultGlucose">Glucose</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultGlucose; ?>" name="adultGlucose" id="adultGlucose">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultCreatinine">Creatinine
                                                </label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultCreatinine; ?>" name="adultCreatinine" id="adultCreatinine">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 p-0 mb-10 mobile-padding-zero mobile-collaps-view">
                                <div class="collapsible">
                                    <p style="text-align: center;"><b> Hematology Panel </b></p>
                                    <i class="fa fa-chevron-down desktop-hide" aria-hidden="true"></i>
                                </div>
                                <div class="content">
                                    <div class="col-md-12 p-0">

                                        <div class=" col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultWBC">WBC</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultWBC; ?>" name="adultWBC" id="adultWBC">
                                                </div>
                                            </div>
                                        </div>
                                        <div class=" col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultHgb">Hgb</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultHgb; ?>" name="adultHgb" id="adultHgb">
                                                </div>
                                            </div>
                                        </div>
                                        <div class=" col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultHct">Hct</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultHct; ?>" name="adultHct" id="adultHct">
                                                </div>
                                            </div>
                                        </div>
                                        <div class=" col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultPlatelets">Platelets</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultPlatelets; ?>" name="adultPlatelets" id="adultPlatelets">
                                                </div>
                                            </div>
                                        </div>
                                        <div class=" col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultINR">INR</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultINR; ?>" name="adultINR" id="adultINR">
                                                </div>
                                            </div>
                                        </div>
                                        <div class=" col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultSputumCult">Sputum Cult</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultSputumCult; ?>" name="adultSputumCult" id="adultSputumCult">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultLabInterpretation">Lab Interpretation
                                                </label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <textarea type="text" class="form-control input-md" name="adultLabInterpretation" id="adultLabInterpretation" rows="2"><?php echo $adultLabInterpretation; ?></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- <div class="row padding_top_ten margin_bottom_ten"> -->
                            <!-- <div class="row">
                                            <div class="col-md-12 text-center">
                                                <label>
                                                    <b>X-ray</b>
                                                </label>
                                            </div>
                                        </div> -->
                            <!-- <div class="row"> -->
                            <div class="col-md-12 p-0 mb-10 mobile-padding-zero mobile-collaps-view">
                                <div class="collapsible">
                                    <p style="text-align: center;"><b> X-Ray </b></p>
                                    <i class="fa fa-chevron-down desktop-hide" aria-hidden="true"></i>
                                </div>
                                <div class="content">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultXRayInterpretation">X-Ray Interpretation
                                            </label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <textarea type="text" class="form-control input-md" name="adultXRayInterpretation" id="adultXRayInterpretation" rows="3"><?php echo $adultXRayInterpretation; ?></textarea>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- <div class="row"> -->
                                    <!-- <div class="col-md-6"> -->
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultETT">ETT or Trach size</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultETT; ?>" name="adultETT" id="adultETT">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultTrachType">Trach Type</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultTrachType; ?>" name="adultTrachType" id="adultTrachType">
                                            </div>
                                        </div>
                                    </div>
                                    <!-- </div> -->
                                    <!-- <div class="col-md-6"> -->
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultPosition">Position</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultPosition; ?>" name="adultPosition" id="adultPosition">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adult@">@</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adult1; ?>" name="adult1" id="adult1">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultCuffPressure">Cuff Pressure</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultCuffPressure; ?>" name="adultCuffPressure" id="adultCuffPressure">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- </div> -->
                                <!-- </div> -->
                            </div>
                            <!-- </div> -->

                            <!-- </div> -->

                            <!-- <div class="row padding_top_ten margin_bottom_ten padding_right_ten"> -->
                            <!-- <div class="row">
                                    <div class="col-md-6 text-center">
                                        <label>
                                            <b>ABG</b>
                                        </label>
                                    </div>
                                    <div class="col-md-6 text-center">
                                        <label>
                                            <b>Hemodynamics</b>
                                        </label>
                                    </div>
                                </div> -->
                            <!-- <div class="row"> -->
                            <div class="col-md-6 p-0 mb-10 mobile-padding-zero mobile-collaps-view">
                                <div class="collapsible">
                                    <p style="text-align: center;"> <b> ABG </b></p>
                                    <i class="fa fa-chevron-down desktop-hide" aria-hidden="true"></i>
                                </div>
                                <div class="content">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultpH">pH</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultpH; ?>" name="adultpH" id="adultpH">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultPaCO2">PaCO2</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultPaCO2; ?>" name="adultPaCO2" id="adultPaCO2">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultHCO3">HCO3</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultHCO3; ?>" name="adultHCO3" id="adultHCO3">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultPaO2">PaO2</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultPaO2; ?>" name="adultPaO2" id="adultPaO2">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultSaO2">SaO2</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultSaO2; ?>" name="adultSaO2" id="adultSaO2">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultFiO2Lpm">FiO2/Lpm</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultFiO2Lpm; ?>" name="adultFiO2Lpm" id="adultFiO2Lpm">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultDateBloodGas">Date/Time Blood Gas Obtained</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultDateBloodGas; ?>" name="adultDateBloodGas" id="adultDateBloodGas">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultInterpretationABG">Interpretation</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <textarea type="text" class="form-control input-md" name="adultInterpretationABG" id="adultInterpretationABG" rows="3"><?php echo $adultInterpretationABG; ?></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- </div> -->
                            <!-- <div class="row"> -->
                            <div class="col-md-6 p-0 mb-10 mobile-padding-zero mobile-collaps-view">
                                <div class="collapsible">
                                    <p style="text-align: center;"> <b> Hemodynamics </b></p>
                                    <i class="fa fa-chevron-down desktop-hide" aria-hidden="true"></i>
                                </div>
                                <div class="content">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultSvO2">SvO2</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultSvO2; ?>" name="adultSvO2" id="adultSvO2">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultCO">CO</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultCO; ?>" name="adultCO" id="adultCO">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultPAP">PAP</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultPAP; ?>" name="adultPAP" id="adultPAP">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultCVP">CVP</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultCVP; ?>" name="adultCVP" id="adultCVP">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultPCWP">PCWP</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultPCWP; ?>" name="adultPCWP" id="adultPCWP">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultICP">ICP</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultICP; ?>" name="adultICP" id="adultICP">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultInterpretationHemodynamics">Interpretation</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <textarea type="text" class="form-control input-md" name="adultInterpretationHemodynamics" id="adultInterpretationHemodynamics" rows="2"><?php echo $adultInterpretationHemodynamics; ?></textarea>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultEKGResults">EKG Results</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <textarea type="text" class="form-control input-md" name="adultEKGResults" id="adultEKGResults" rows="3"><?php echo $adultEKGResults; ?></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- </div> -->
                            <div class="col-md-12 p-0 mb-10 mobile-padding-zero mobile-collaps-view">
                                <div class="collapsible">
                                    <p style="text-align: center;"><b> Interpretation </b></p>
                                    <i class="fa fa-chevron-down desktop-hide" aria-hidden="true"></i>
                                </div>
                                <div class="content">
                                    <div class="col-md-12">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label class="col-md-12 control-label" for="adultInterpretationPAO2">PAO2</label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                                        <textarea type="text" class="form-control input-md" name="adultInterpretationPAO2" id="adultInterpretationPAO2" rows="2"><?php echo $adultInterpretationPAO2; ?></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label class="col-md-12 control-label" for="adultInterpretationAO2">(A-a)O2</label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                                        <textarea type="text" class="form-control input-md" name="adultInterpretationAO2" id="adultInterpretationAO2" rows="2"><?php echo $adultInterpretationAO2; ?></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label class="col-md-12 control-label" for="adultInterpretationCaO2">CaO2</label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                                        <textarea type="text" class="form-control input-md" name="adultInterpretationCaO2" id="adultInterpretationCaO2" rows="2"><?php echo $adultInterpretationCaO2; ?></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label class="col-md-12 control-label" for="adultInterpretationPFRatio">P/F Ratio</label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                                        <textarea type="text" class="form-control input-md" name="adultInterpretationPFRatio" id="adultInterpretationPFRatio" rows="2"><?php echo $adultInterpretationPFRatio; ?></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-3 flex-end">
                    <button class="button btn-navigate-form-step button-prev" type="button" step_number="2">Prev</button>
                    <button class="button next btn-navigate-form-step" type="button" step_number="4">Save & Next</button>
                </div>

            </section>

            <!-- Step 4 Content, default hidden on page load. -->
            <section id="step-4" class="form-step d-none pt-0" data-parsley-validate>
                <!-- <div class="row text-center ">
                    <label class="control-label" for="">
                        <b>Invasive / Non-Invasive Ventilation</b>
                    </label>
                </div> -->
                <div class="row padding_top_ten margin_bottom_ten w-full" style="margin: 10px auto;">
                    <div class="row">
                        <div class="col-md-12 desktop-px-zero">
                            <div class="col-md-6 p-0 mb-10 mobile-padding-zero mobile-collaps-view">
                                <!-- <div class="col-md-6 text-center"> -->
                                <!-- <label>
                                    <b>Ordered Settings</b>
                                </label> -->
                                <div class="collapsible">
                                    <p style="text-align: center;"><b> Orderd Settings</b></p>
                                    <i class="fa fa-chevron-down desktop-hide" aria-hidden="true"></i>
                                </div>
                                <div class="content">
                                    <div class="col-md-12 p-0">
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultVentilator">Ventilator</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultVentilator; ?>" name="adultVentilator" id="adultVentilator">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultFiO2">FiO2:</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultFiO2; ?>" name="adultFiO2" id="adultFiO2">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultMode">Mode</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultMode; ?>" name="adultMode" id="adultMode">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultPSupport">P Support</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultPSupport; ?>" name="adultPSupport" id="adultPSupport">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultSetRate">Set Rate</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultSetRate; ?>" name="adultSetRate" id="adultSetRate">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultMaxFlow">Max Flow</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultMaxFlow; ?>" name="adultMaxFlow" id="adultMaxFlow">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultSetVt">Set Vt</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultSetVt; ?>" name="adultSetVt" id="adultSetVt">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultFlowSens">Flow Sens</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultFlowSens; ?>" name="adultFlowSens" id="adultFlowSens">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultIBWVt">IBWVt</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultIBWVt; ?>" name="adultIBWVt" id="adultIBWVt">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultItime">I-Time</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultItime; ?>" name="adultItime" id="adultItime">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultPEEPCPAP">PEEP/CPAP</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultPEEPCPAP; ?>" name="adultPEEPCPAP" id="adultPEEPCPAP">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultHumidityTemp">Humidity Temp</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultHumidityTemp; ?>" name="adultHumidityTemp" id="adultHumidityTemp">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultOtherSettings">Other Settings</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <textarea type="text" class="form-control input-md" name="adultOtherSettings" id="adultOtherSettings" rows="2"><?php echo $adultOtherSettings; ?></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- </div> -->
                            </div>
                            <div class="col-md-6 p-0 mb-10 mobile-padding-zero mobile-collaps-view">
                                <!-- <div class="col-md-6 text-center"> -->
                                <!-- <label>
                                        <b>Patient Parameters</b>
                                    </label> -->
                                <div class="collapsible">
                                    <p style="text-align: center;"><b>Patient Parameters </b></p>
                                    <i class="fa fa-chevron-down desktop-hide" aria-hidden="true"></i>
                                </div>
                                <div class="content">
                                    <div class="col-md-12 p-0">
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultPiP">PiP</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultPiP; ?>" name="adultPiP" id="adultPiP">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultPlat">Plat</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultPlat; ?>" name="adultPlat" id="adultPlat">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultRR">RR</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultRR; ?>" name="adultRR" id="adultRR">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultMAP">MAP</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultMAP; ?>" name="adultMAP" id="adultMAP">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultVE">VE</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultVE; ?>" name="adultVE" id="adultVE">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultSpontVt">Spont Vt</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultSpontVt; ?>" name="adultSpontVt" id="adultSpontVt">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultIE">I:E</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultIE; ?>" name="adultIE" id="adultIE">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultCsta">Csta</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <textarea type="text" class="form-control input-md" name="adultCsta" id="adultCsta" rows="2"><?php echo $adultCsta; ?></textarea>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultRaw">Raw</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <textarea type="text" class="form-control input-md" name="adultRaw" id="adultRaw" rows="2"><?php echo $adultRaw; ?></textarea>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultVte">Vte</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultVte; ?>" name="adultVte" id="adultVte">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">

                                            <label class="control-label" for="adultSuction" style="margin-right: 3px;">Suction</label>
                                            <label class="radio-inline control-label"><input type="radio" class=" input-md" value="Yes" name="adultSuction" <?php echo ($adultSuction == 'Yes') ? "checked" : ""; ?>>Yes</label>
                                            <label class="radio-inline control-label"><input type="radio" class=" input-md" value="No" name="adultSuction" <?php echo ($adultSuction == 'No') ? "checked" : ""; ?>>No</label>

                                        </div>
                                        <div class="col-md-6">
                                            <label class="control-label" for="adultCough" style="margin-right: 3px;">Cough</label>
                                            <label class="radio-inline control-label"><input type="radio" class=" input-md" value="Yes" name="adultCough" <?php echo ($adultCough == 'Yes') ? "checked" : ""; ?>>Yes</label>
                                            <label class="radio-inline control-label"><input type="radio" class="input-md" value="No" name="adultCough" <?php echo ($adultCough == 'No') ? "checked" : ""; ?>>
                                                No</label>

                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultSputumAmount">Sputum Amount/Color</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultSputumAmount; ?>" name="adultSputumAmount" id="adultSputumAmount">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultRecommendations">Student Recommendations</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <textarea type="text" class="form-control input-md" name="adultRecommendations" id="adultRecommendations" rows="2"><?php echo $adultRecommendations; ?></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- </div> -->
                            </div>

                            <div class="col-md-12 p-0 mb-10 mobile-padding-zero mobile-collaps-view">
                                <!-- <div class="row text-center margin_bottom_ten"> -->
                                <div class="collapsible">
                                    <!-- <label class="control-label" for="">
                                            <b>Alarms</b>
                                        </label> -->
                                    <p style="text-align: center;"><b>Alarms </b></p>
                                    <i class="fa fa-chevron-down desktop-hide" aria-hidden="true"></i>
                                </div>
                                <!-- </div> -->
                                <div class="content">
                                    <!-- <div class="row"> -->
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultLowHiPiP">Low/Hi PiP</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultLowHiPiP; ?>" name="adultLowHiPiP" id="adultLowHiPiP">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultLowHiVte">Low/Hi Vte</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultLowHiVte; ?>" name="adultLowHiVte" id="adultLowHiVte">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultLowHiVe">Low/Hi Ve</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultLowHiVe; ?>" name="adultLowHiVe" id="adultLowHiVe">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultLowHiRR">Low/Hi RR</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultLowHiRR; ?>" name="adultLowHiRR" id="adultLowHiRR">
                                            </div>
                                        </div>
                                    </div>
                                    <!-- </div> -->
                                    <!-- <div class="row "> -->
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultApneaAlert">Apnea Alert</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultApneaAlert; ?>" name="adultApneaAlert" id="adultApneaAlert">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-9">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultOtherAlarms">Other Alarms</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <textarea type="text" class="form-control input-md" name="adultOtherAlarms" id="adultOtherAlarms" rows="2"><?php echo $adultOtherAlarms; ?></textarea>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- </div> -->
                                </div>
                            </div>

                            <!-- BiPAP / O2 via NC settings section start -->
                            <div class="col-md-12 p-0 mb-10 mobile-padding-zero mobile-collaps-view">
                                <!-- <div class="row text-center margin_bottom_ten"> -->
                                <div class="collapsible">
                                    <!-- <label class="control-label" for="">
                                            <b>BiPAP / O2 via NC settings</b>
                                        </label> -->
                                    <p style="text-align: center;"><b>Orderd Settings </b></p>
                                    <i class="fa fa-chevron-down desktop-hide" aria-hidden="true"></i>
                                </div>
                                <!-- </div> -->
                                <div class="content">
                                    <!-- <div class="row"> -->
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultIPAP">IPAP</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultIPAP; ?>" name="adultIPAP" id="adultIPAP">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultEPAP">EPAP</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultEPAP; ?>" name="adultEPAP" id="adultEPAP">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultRate">Rate</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultRate; ?>" name="adultRate" id="adultRate">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultFiO21">FiO2</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultFiO21; ?>" name="adultFiO21" id="adultFiO21">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultItimeSetting">I-Time</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultItimeSetting; ?>" name="adultItimeSetting" id="adultItimeSetting">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultRise">Rise</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultRise; ?>" name="adultRise" id="adultRise">
                                            </div>
                                        </div>
                                    </div>
                                    <!-- </div> -->

                                    <!-- <div class="row"> -->
                                    <div class="col-md-2">
                                        <div class="form-group ">
                                            <label class="col-md-12 control-label" for="adultRamp">Ramp</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <select id="adultRamp" name="adultRamp" class="form-control input-md  select2_single ">
                                                    <option value="">Select</option>
                                                    <option value="Yes" <?php if ($adultRamp == "Yes") { ?> selected="true" <?php } ?>>Yes</option>
                                                    <option value="No" <?php if ($adultRamp == "No") { ?> selected="true" <?php } ?>>No</option>

                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- </div> -->
                                </div>
                            </div>

                            <!-- PFT section start -->
                            <div class="col-md-12 p-0 mb-10 mobile-padding-zero mobile-collaps-view">
                                <!-- <div class="row text-center margin_bottom_ten"> -->
                                <div class="collapsible">
                                    <!-- <label class="control-label" for="">
                                        <b>PFT</b>
                                    </label> -->
                                    <p style="text-align: center;"><b>PFT </b></p>
                                    <i class="fa fa-chevron-down desktop-hide" aria-hidden="true"></i>
                                </div>
                                <!-- </div> -->
                                <div class="content">
                                    <!-- <div class="row"> -->
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultFVC">FVC</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultFVC; ?>" name="adultFVC" id="adultFVC">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultFEF25">FEF 25-75</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultFEF25; ?>" name="adultFEF25" id="adultFEF25">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultFEV1">FEV1</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultFEV1; ?>" name="adultFEV1" id="adultFEV1">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultPEFR">PEFR</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultPEFR; ?>" name="adultPEFR" id="adultPEFR">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultFEV1FVC">FEV1/FVC</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultFEV1FVC; ?>" name="adultFEV1FVC" id="adultFEV1FVC">
                                            </div>
                                        </div>
                                    </div>
                                    <!-- </div> -->

                                    <!-- <div class="row"> -->
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultLungVolumes">Lung Volumes and % Predicted</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultLungVolumes; ?>" name="adultLungVolumes" id="adultLungVolumes">
                                            </div>
                                        </div>
                                    </div>
                                    <!-- </div> -->
                                    <!-- <div class="row"> -->
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultInterpretationPFT">Interpretation</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12" style="padding-right: 30px;">
                                                <textarea type="text" class="form-control input-md" name="adultInterpretationPFT" id="adultInterpretationPFT" rows="2"><?php echo $adultInterpretationPFT; ?></textarea>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- </div> -->
                                </div>
                            </div>
                            <div class="col-md-12 p-0 mb-10 mobile-padding-zero mobile-collaps-view">
                                <!-- <div class="row"> -->
                                <div class="col-md-6 mobile-padding-4 text-center">
                                    <div class="form-group mx-0">
                                        <label class="control-label" for="">
                                            <b>List ALL Medications w/Indications for use
                                            </b>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6 mobile-padding-4 text-center">
                                    <div class="form-group mx-0">
                                        <label class="control-label" for="">
                                            <b>Recommended Modifications to Care Plan: (if required)</b>
                                        </label>
                                    </div>
                                </div>
                                <!-- </div> -->
                                <div class="" id="MedicationDiv" style="margin-right: 5px;">
                                    <?php
                                    if ($caseStudyId && ($adultMedicationsUseList != '' || $adultModificationCarePlanList != '')) {
                                        // DECODE JSON FOR PRINT DATA
                                        $adultMedicationsUseList = json_decode($adultMedicationsUseList, true);
                                        $adultModificationCarePlanList = json_decode($adultModificationCarePlanList, true);
                                        $result = '';
                                        //MEARGE ARRAY 
                                        if ($adultMedicationsUseList && $adultModificationCarePlanList)
                                            $result = array_map('array_merge', $adultMedicationsUseList, $adultModificationCarePlanList);
                                        $listId = 0;

                                        if ($result) {
                                            foreach ($result as $key => $value) {
                                                $listId = $key + 1;
                                                $value = ($value); // convert object to array
                                                // PRINT HTML
                                                $divHtml = '<div class="col-md-12 p-0" id="row_' . $listId . '" isrow="' . $listId . '"> <div class="col-md-6"> <div class="form-group">  <div class="col-md-12    col-sm-12 col-xs-12 mobile-block"><label class="control-label labelcount" for="adultMedicationsUse' . $listId . '">' . $listId . '</label> <input type="text" class="form-control input-md" value="' . $value[0] . '" name="adultMedicationsUseList[' . $listId . '][]" id="adultMedicationsUse' . $listId . '"> </div> </div> </div> <div class="col-md-6"> <div class="form-group" style="display: flex;align-items: center;"> <div class="col-md-11 col-sm-12 col-xs-12"> <textarea type="text" class="form-control input-md" rows="2" cols="5" name="adultModificationCarePlanList[' . $listId . '][]" id="adultModificationCarePlan' . $listId . '">' . $value[1] . '</textarea> </div> <div> <a  href="javascript:void(0);" class="deleteRow" deleteRowId="' . $listId . '" ><i class="fa fa-trash-o row-delete-icon" aria-hidden="true" style="font-size: 18px;padding-left: 6px;"></i></a> </div> </div> </div> </div>';
                                                echo $divHtml;
                                            }
                                        }

                                    ?>

                                    <?php } else {
                                        $listId = 5; ?>

                                        <div class="col-md-12 p-0" id="row_1" isrow="1">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <div class="col-md-12 col-sm-12 col-xs-12 mobile-block">
                                                        <label class="control-label margin-right" for="adultMedicationsUse1">1</label>
                                                        <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse1; ?>" name="adultMedicationsUseList[1][]" id="adultMedicationsUse1" placeholder="Enter Medications w/Indications ">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <div class="col-md-11 col-sm-12 col-xs-12">
                                                        <textarea type="text" class="form-control input-md" rows="2" cols="5" name="adultModificationCarePlanList[1][]" id="adultModificationCarePlan1" placeholder="Enter Modifications to Care Plan "><?php echo $adultModificationCarePlan1; ?></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-12 p-0" id="row_2" isrow="2">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <div class="col-md-12 col-sm-12 col-xs-12 mobile-block">
                                                        <label class="control-label margin-right" for="adultMedicationsUse2">2</label>
                                                        <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse2; ?>" name="adultMedicationsUseList[2][]" id="adultMedicationsUse2" placeholder="Enter Medications w/Indications ">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <div class="col-md-11 col-sm-12 col-xs-12">
                                                        <textarea type="text" class="form-control input-md" rows="2" cols="5" name="adultModificationCarePlanList[2][]" id="adultModificationCarePlan2" placeholder="Enter Modifications to Care Plan "><?php echo $adultModificationCarePlan2; ?></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-12 p-0" id="row_3" isrow="3">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <div class="col-md-12 col-sm-12 col-xs-12 mobile-block">
                                                        <label class="control-label labelcount margin-right" for="adultMedicationsUse3">3</label>
                                                        <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse3; ?>" name="adultMedicationsUseList[3][]" id="adultMedicationsUse3" placeholder="Enter Medications w/Indications ">
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <div class="col-md-11 col-sm-12 col-xs-12">
                                                        <textarea type="text" class="form-control input-md" rows="2" cols="5" name="adultModificationCarePlanList[3][]" id="adultModificationCarePlan3" placeholder="Enter Modifications to Care Plan "><?php echo $adultModificationCarePlan3; ?></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-12 p-0" id="row_4" isrow="4">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <div class="col-md-12 col-sm-12 col-xs-12 mobile-block">
                                                        <label class="control-label labelcount margin-right" for="adultMedicationsUse4">4</label>
                                                        <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse4; ?>" name="adultMedicationsUseList[4][]" id="adultMedicationsUse4" placeholder="Enter Medications w/Indications ">
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <div class="col-md-11 col-sm-12 col-xs-12">
                                                        <textarea type="text" class="form-control input-md" rows="2" cols="5" name="adultModificationCarePlanList[4][]" id="adultModificationCarePlan4" placeholder="Enter Modifications to Care Plan "><?php echo $adultModificationCarePlan4; ?></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-12 p-0" id="row_5" isrow="5">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <div class="col-md-12 col-sm-12 col-xs-12 mobile-block">
                                                        <label class="control-label labelcount margin-right" for="adultMedicationsUse5">5</label>
                                                        <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse5; ?>" name="adultMedicationsUseList[5][]" id="adultMedicationsUse5" placeholder="Enter Medications w/Indications ">
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <div class="col-md-11 col-sm-12 col-xs-12">
                                                        <textarea type="text" class="form-control input-md" rows="2" cols="5" name="adultModificationCarePlanList[5][]" id="adultModificationCarePlan5" placeholder="Enter Modifications to Care Plan "><?php echo $adultModificationCarePlan5; ?></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php }

                                    ?>
                                </div>
                                <!-- ----------------------------Show More---------------------------------------->
                                <div class="row">
                                    <div class="col-md-12 form-group justify-content-center" style="margin: 7px auto; width: 100%; display:flex; justify-content: center;">
                                        <input type="button" style="width: 164px;" class="form-control input-md showMore_6_10" value="Add More Rows" islast='<?php echo $listId; ?>'>
                                    </div>
                                </div>
                                <!-- ------------------------------------------------------------------ -->
                                <div class="row hidecol_6 hide">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="col-md-12    col-sm-12 col-xs-12 mobile-block">
                                                <label class="control-label labelcount margin-right" for="adultMedicationsUse6">6</label>
                                                <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse6; ?>" name="adultMedicationsUse6" id="adultMedicationsUse6">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <textarea type="text" class="form-control input-md" rows="2" cols="5" name="adultModificationCarePlan6" id="adultModificationCarePlan6"><?php echo $adultModificationCarePlan6; ?></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row hidecol_6 hide">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="col-md-12    col-sm-12 col-xs-12 mobile-block">
                                                <label class="control-label labelcount margin-right" for="adultMedicationsUse7">7</label>
                                                <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse7; ?>" name="adultMedicationsUse7" id="adultMedicationsUse7">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <textarea type="text" class="form-control input-md" rows="2" cols="5" name="adultModificationCarePlan7" id="adultModificationCarePlan7"><?php echo $adultModificationCarePlan7; ?></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row hidecol_6 hide">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="col-md-12    col-sm-12 col-xs-12 mobile-block">
                                                <label class="control-label labelcount margin-right" for="adultMedicationsUse8">8</label>
                                                <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse8; ?>" name="adultMedicationsUse8" id="adultMedicationsUse8">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <textarea type="text" class="form-control input-md" rows="2" cols="5" name="adultModificationCarePlan8" id="adultModificationCarePlan8"><?php echo $adultModificationCarePlan8; ?></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row hidecol_6 hide">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="col-md-12    col-sm-12 col-xs-12 mobile-block">
                                                <label class="control-label labelcount margin-right" for="adultMedicationsUse9">9</label>
                                                <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse9; ?>" name="adultMedicationsUse9" id="adultMedicationsUse9">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <textarea type="text" class="form-control input-md" rows="2" cols="5" name="adultModificationCarePlan9" id="adultModificationCarePlan9"><?php echo $adultModificationCarePlan9; ?></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row hidecol_6 hide">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="col-md-12    col-sm-12 col-xs-12 mobile-block">
                                                <label class="control-label labelcount margin-right" for="adultMedicationsUse10">10</label>
                                                <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse10; ?>" name="adultMedicationsUse10" id="adultMedicationsUse10">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <textarea type="text" class="form-control input-md" rows="2" cols="5" name="adultModificationCarePlan10" id="adultModificationCarePlan10"><?php echo $adultModificationCarePlan10; ?></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- ----------------------------Show More---------------------------------------->
                                <div class="row showLess_1 hide">
                                    <div class="col-md-6 form-group justify-content-center" style="width: 158px;margin-left: 446px;">
                                        <input type="button" class="form-control input-md showMore_10_15" value="Add More Rows">
                                    </div>
                                    <div class="col-md-6 form-group justify-content-center" style="width: 160px;margin-left: -4px;">
                                        <input type="button" class="form-control input-md showLess_10_6" value="Show Less Rows">
                                    </div>
                                </div>
                                <!-- ------------------------------------------------------------------ -->
                                <div class="row hidecol_10 hide">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="col-md-12    col-sm-12 col-xs-12 mobile-block">
                                                <label class="control-label labelcount margin-right" for="adultMedicationsUse10">11</label>
                                                <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse11; ?>" name="adultMedicationsUse11" id="adultMedicationsUse11">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <textarea type="text" class="form-control input-md" rows="2" cols="5" name="adultModificationCarePlan11" id="adultModificationCarePlan11"><?php echo $adultModificationCarePlan11; ?></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row hidecol_10 hide">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="col-md-12    col-sm-12 col-xs-12 mobile-block">
                                                <label class="control-label labelcount margin-right" for="adultMedicationsUse10">12</label>
                                                <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse12; ?>" name="adultMedicationsUse12" id="adultMedicationsUse12">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <textarea type="text" class="form-control input-md" rows="2" cols="5" name="adultModificationCarePlan12" id="adultModificationCarePlan12"><?php echo $adultModificationCarePlan12; ?></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row  hidecol_10 hide">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="col-md-12    col-sm-12 col-xs-12 mobile-block">
                                                <label class="control-label labelcount margin-right" for="adultMedicationsUse13">13</label>
                                                <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse13; ?>" name="adultMedicationsUse13" id="adultMedicationsUse13">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <textarea type="text" class="form-control input-md" rows="2" cols="5" name="adultModificationCarePlan13" id="adultModificationCarePlan13"><?php echo $adultModificationCarePlan13; ?></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row  hidecol_10 hide">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="col-md-12    col-sm-12 col-xs-12 mobile-block">
                                                <label class="control-label labelcount margin-right" for="adultMedicationsUse14">14</label>
                                                <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse14; ?>" name="adultMedicationsUse14" id="adultMedicationsUse14">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <textarea type="text" class="form-control input-md" rows="2" cols="5" name="adultModificationCarePlan14" id="adultModificationCarePlan14"><?php echo $adultModificationCarePlan14; ?></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row  hidecol_10 hide">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="col-md-12    col-sm-12 col-xs-12 mobile-block">
                                                <label class="control-label labelcount margin-right" for="adultMedicationsUse15">15</label>
                                                <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse15; ?>" name="adultMedicationsUse15" id="adultMedicationsUse15">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <textarea type="text" class="form-control input-md" rows="2" cols="5" name="adultModificationCarePlan15" id="adultModificationCarePlan15"><?php echo $adultModificationCarePlan15; ?></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- ----------------------------Show Less---------------------------------------->
                                <div class="row showLess_2 hide">
                                    <div class="col-md-12 form-group justify-content-center" style="width: 168px;margin: 7px;padding-left: 23px;margin-left: 503px;margin-top: -6px;">
                                        <input type="button" class="form-control input-md showLess_15_10" value="Show Less Rows">
                                    </div>

                                </div>
                                <!-- ------------------------------------------------------------------ -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-3 flex-end">
                    <button class="button btn-navigate-form-step button-prev" type="button" step_number="3">Prev</button>
                    <button class="button next btn-navigate-form-step" type="button" step_number="5">Save & Next</button>
                </div>
            </section>




            <!-- Step 5 Content, default hidden on page load. -->
            <section id="step-5" class="form-step d-none pt-0" data-parsley-validate>
                <div class="row margin_bottom_ten w-full" style="margin: 10px auto;">
                    <!-- student comments 21042021-->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group m-0">
                                <label class="control-label" for="">
                                    <b>Clinician Comment
                                    </b>
                                </label>
                            </div>
                        </div>
                        <div class="col-md-12 text-left" style="margin-top: 5px;">
                            <div class="form-group">
                                <?php //if ($clinician_comments != '') { 
                                ?>
                                <div class="col-md-12 col-md-12">
                                    <textarea type="text" class="form-control input-md" name="clinician_comments" id="clinician_comments" rows="5" readonly="readonly"><?php echo $clinician_comments; ?></textarea>
                                </div>
                                <?php //} else {
                                ?>
                                <!-- <div class="col-md-12 col-md-12">
                                        <textarea type="text" class="form-control input-md" name="clinician_comments" id="clinician_comments" rows="5" readonly="readonly"><?php echo $clinician_comments; ?></textarea>
                                    </div> -->
                                <?php //} 
                                ?>
                            </div>
                        </div>
                    </div>
                    <!-- student comments 21042021-->
                    <!-- school comments 21042021-->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group m-0">
                                <label class="control-label" for="">
                                    <b>School Comment
                                    </b>
                                </label>
                            </div>
                        </div>
                        <div class="col-md-12 text-left" style="margin-top: 5px;">
                            <div class="form-group">
                                <?php //if ($school_comments != '') { 
                                ?>
                                <div class="col-md-12 col-md-12">
                                    <textarea type="text" class="form-control input-md" name="school_comments" id="school_comments" rows="5" readonly="readonly"><?php echo $school_comments; ?></textarea>
                                </div>
                                <?php //} else {
                                ?>
                                <!-- <div class="col-md-12 col-md-12">
                                        <textarea type="text" class="form-control input-md" name="school_comments" id="school_comments" rows="5" readonly="readonly"><?php echo $school_comments; ?></textarea>
                                    </div> -->
                                <?php //} 
                                ?>
                            </div>
                        </div>
                    </div>
                    <!-- school comments 21042021-->

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group m-0">
                                <label class="control-label" for="">
                                    <b>Student Comment
                                    </b>
                                </label>
                            </div>
                        </div>
                        <div class="col-md-12 text-left">
                            <div class="form-group">
                                <div class="col-md-12">
                                    <textarea name="studentcomments" rows="6" id="studentcomments" class="form-control"><?php echo ($studentcomments); ?></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group" style="display: flex;margin-left: 0;">
                            <button class="button btn-navigate-form-step button-prev" type="button" step_number="4">Prev</button>
                            <!-- <label class="col-md-2 control-label"></label> -->
                            <div class="col-md-12 col-sm-12 col-xs-12 flex-end">
                                <?php if ($view != 'V') { ?>
                                    <button id="btnSave" name="btnSave" class="btn btn-success mr-15" type="button">Submit</button>
                                <?php } ?>
                                <?php if ($IsMobile) { ?>
                                    <a type="button" href="<?php echo BASE_PATH ?>/webRedirect.html?status=Cancel&type=adult" class="btn btn-default">Cancel</a>
                                <?php } else { ?>
                                    <a href="caseStudyList.html" class="btn btn-default">Cancel</a>
                                <?php } ?>

                            </div>
                        </div>
                    </div>
                </div>
                <!-- <div class="mt-3 flex-end" style="">
                    <button class="button btn-navigate-form-step button-prev" type="button" step_number="4">Prev</button>
                    <button class="button submit-btn" type="submit">Save</button>
                </div> -->
            </section>

        </form>

    </div>

    <?php include('includes/footer.php'); ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script src="https://cdn.ckeditor.com/ckeditor5/35.0.1/classic/ckeditor.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>

    <script>
        // load common.js
        loadScriptOnce("<?php echo ($dynamicOrgUrl); ?>/assets/js/common.js");

        $('.next').on('click', function() {
            event.preventDefault();

            var isValid = $('#step-1').parsley().validate();

            if (!isValid) {
                return false;
            }
        });
        /**
         * Define a function to navigate betweens form steps.
         * It accepts one parameter. That is - step number.
         */
        const navigateToFormStep = (stepNumber) => {
            /**
             * Hide all form steps.
             */
            document.querySelectorAll(".form-step").forEach((formStepElement) => {
                formStepElement.classList.add("d-none");
            });
            /**
             * Mark all form steps as unfinished.
             */
            document.querySelectorAll(".form-stepper-list").forEach((formStepHeader) => {
                formStepHeader.classList.add("form-stepper-unfinished");
                formStepHeader.classList.remove("form-stepper-active", "form-stepper-completed");
            });
            /**
             * Show the current form step (as passed to the function).
             */
            document.querySelector("#step-" + stepNumber).classList.remove("d-none");
            /**
             * Select the form step circle (progress bar).
             */
            const formStepCircle = document.querySelector('li[step="' + stepNumber + '"]');
            /**
             * Mark the current form step as active.
             */
            formStepCircle.classList.remove("form-stepper-unfinished", "form-stepper-completed");
            formStepCircle.classList.add("form-stepper-active");
            /**
             * Loop through each form step circles.
             * This loop will continue up to the current step number.
             * Example: If the current step is 3,
             * then the loop will perform operations for step 1 and 2.
             */
            for (let index = 0; index < stepNumber; index++) {
                /**
                 * Select the form step circle (progress bar).
                 */
                const formStepCircle = document.querySelector('li[step="' + index + '"]');
                /**
                 * Check if the element exist. If yes, then proceed.
                 */
                if (formStepCircle) {
                    /**
                     * Mark the form step as completed.
                     */
                    formStepCircle.classList.remove("form-stepper-unfinished", "form-stepper-active");
                    formStepCircle.classList.add("form-stepper-completed");
                }
            }
        };
        /**
         * Select all form navigation buttons, and loop through them.
         */
        // document.querySelectorAll(".btn-navigate-form-step").forEach((formNavigationBtn) => {
        //     /**
        //      * Add a click event listener to the button.
        //      */
        //     formNavigationBtn.addEventListener("click", () => {
        //         /**
        //          * Get the value of the step.
        //          */
        //         const stepNumber = parseInt(formNavigationBtn.getAttribute("step_number"));
        //         /**
        //          * Call the function to navigate to the target form step.
        //          */
        //         navigateToFormStep(stepNumber);
        //     });
        // });

        $(document).ready(function() {
            $('.btn-navigate-form-step').on('click', function() {
                const $btn = $(this);
                const stepNumber = parseInt($btn.attr('step_number'));
                const isNextButton = $btn.hasClass('next');
                var view = $('#hiddenView').val();
                if (view == 'V') {
                    navigateToFormStep(stepNumber);
                } else {
                    if (stepNumber === 2) {
                        const isValidStep1 = $('#step-1').parsley().validate();
                        if (!isValidStep1) return false;

                        if (isNextButton) {
                            saveCaseStudyDetails('details', 1, function() {
                                navigateToFormStep(stepNumber);
                            });
                            return; // prevent immediate navigation
                        }
                    }

                    if (stepNumber === 3) {
                        const isValidStep3 = $('#step-2').parsley().validate();
                        if (!isValidStep3) return false;

                        if (isNextButton) {
                            saveCaseStudyDetails('vitals', 2, function() {
                                navigateToFormStep(stepNumber);
                            });
                            return;
                        }
                    }

                    if (stepNumber === 4) {
                        const isValidStep4 = $('#step-3').parsley().validate();
                        if (!isValidStep4) return false;

                        if (isNextButton) {
                            saveCaseStudyDetails('readings', 3, function() {
                                navigateToFormStep(stepNumber);
                            });
                            return;
                        }
                    }

                    if (stepNumber === 5) {
                        const isValidStep5 = $('#step-4').parsley().validate();
                        if (!isValidStep5) return false;

                        if (isNextButton) {
                            saveCaseStudyDetails('medications', 4, function() {
                                navigateToFormStep(stepNumber);
                            });
                            return;
                        }
                    }
                }
                // For "Previous" buttons or non-saving steps, allow navigation directly
                navigateToFormStep(stepNumber);
            });

        });

        $(document).on('click', '#btnSave', function() {
            const isValidStep6 = $('#step-5').parsley().validate();
            if (!isValidStep6) return false;

            saveCaseStudyDetails('comments', 5, function() {
                // navigateToFormStep(stepNumber);
            });
            return;
        });

        $('.form-stepper-list').click(function() {
            var view = '<?php echo $view; ?>';
            stepId = $(this).attr('step');
            stepNextId = $('.form-stepper-active').attr('step');
            if (view == 'V') {
                navigateToFormStep(stepId);
            } else {
                if (stepId < stepNextId)
                    navigateToFormStep(stepId);
            }

        });
    </script>

    <!-- collapsible start -->
    <script>
        const collapsibles = document.querySelectorAll('.collapsible');

        collapsibles.forEach(collapsible => {
            collapsible.addEventListener('click', () => {
                const content = collapsible.nextElementSibling;
                const icon = collapsible.querySelector('i.fa-chevron-down');

                // Check if the content is currently visible
                const isActive = content.classList.contains('active');

                // Close all content sections and reset arrows
                document.querySelectorAll('.content').forEach(item => {
                    item.classList.remove('active');
                });

                document.querySelectorAll('i.fa-chevron-down').forEach(item => {
                    item.classList.remove('rotate');
                });

                // Toggle the content's active state and arrow rotation
                content.classList.toggle('active', !isActive);
                icon.classList.toggle('rotate', !isActive);
            });
        });
    </script>
    <!-- collapsible end -->


    <script type="text/javascript">
        $(window).load(function() {
            $(".select2_single").select2();
            $('#select2-cborotation-container').addClass('required-select2');

            // $('#formCaseStudy')
            //     .parsley()
            //     .on('field:validated', function() {
            //         var ok = $('.parsley-error').length === 0;
            //     })
            //     .on('form:submit', function() {
            //         ShowProgressAnimation();
            //         return true; // Don't submit form for this demo
            //     });

            var caseStudydate = '<?php echo $caseStudydate; ?>';
            // console.log(caseStudydate);
            if (caseStudydate != '') {
                $('#caseStudydate').datetimepicker({
                    format: 'MM/DD/YYYY'
                });
                $('#caseStudydate').val(caseStudydate);
            } else {
                $('#caseStudydate').datetimepicker({
                    format: 'MM/DD/YYYY',
                    maxDate: moment()
                });
            }

            $('#ClinicianDate').datetimepicker({
                format: 'MM/DD/YYYY'
            });
            $('#schoolDate').datetimepicker({
                format: 'MM/DD/YYYY'
            });
            $('#adultAdmissionDate').datetimepicker({
                format: 'MM/DD/YYYY'
            });

            // $('#gestationalAOB').datetimepicker({     format: 'MM/DD/YYYY' });

            $('#studentDOB').datetimepicker({
                format: 'MM/DD/YYYY'
            });


            var view = $('#hiddenView').val();
            if (view == 'V') {
                $('.next').text('Next');
            }

        });
        $('.showMore_6_10').click(function() {
            var islast = $(this).attr('islast');
            var listId = parseInt(islast) + 1;
            $(this).attr('islast', listId);
            var divHtml = '<div class="col-md-12 p-0" id="row_' + listId + '" isrow="' + listId + '"> <div class="col-md-6"> <div class="form-group"> <div class="col-md-12    col-sm-12 col-xs-12 mobile-block"><label class="control-label labelcount margin-right" for="adultMedicationsUse' + listId + '">' + listId + '</label>  <input type="text" class="form-control input-md" value="" name="adultMedicationsUseList[' + listId + '][]" id="adultMedicationsUse' + listId + '" placeholder="Enter Medications w/Indications "> </div> </div> </div> <div class="col-md-6"> <div class="form-group" style="display: flex;align-items: center;"> <div class="col-md-11 col-sm-12 col-xs-12"> <textarea type="text" class="form-control input-md" rows="2" cols="5" name="adultModificationCarePlanList[' + listId + '][]" id="adultModificationCarePlan' + listId + '" placeholder="Enter Modifications to Care Plan "></textarea> </div> <div> <a  href="javascript:void(0);" class="deleteRow row-delete-icon" style="padding-left:0;" deleteRowId="' + listId + '" ><i class="fa fa-trash-o" aria-hidden="true" style="font-size: 18px;"></i></ion-icon></a> </div> </div> </div> </div>';
            $('#MedicationDiv').append(divHtml);
        });
        $(document).on('click', '.deleteRow', function() {
            var lastrowId = $('#MedicationDiv').children().last().attr('isrow');
            var deleteRowId = $(this).attr('deleteRowId');
            $('#row_' + deleteRowId).remove();
            if (deleteRowId == lastrowId)
                var lastrowId = parseInt(lastrowId) - 1;

            $('.showMore_6_10').attr('islast', lastrowId);
            var islast = $('.showMore_6_10').attr('islast');
        });

        // $('.showMore_6_10').click(function () {

        //     $('.hidecol_6').removeClass('hide');
        //     $(this).addClass('hide');
        //     $('.showLess_1').removeClass('hide');
        // });
        // $('.showLess_10_6').click(function () {
        //     $('.hidecol_6').addClass('hide');
        //     $('.showMore_6_10').removeClass('hide');
        //     $('.showLess_1').addClass('hide');
        // });

        // $('.showMore_10_15').click(function () {
        //     $('.hidecol_6, .hidecol_10').removeClass('hide');
        //     $('.showLess_1').addClass('hide');
        //     $('.showLess_2').removeClass('hide');
        //     $('.showLess_15_10').removeClass('hide');
        // });
        // $('.showLess_15_10').click(function () {

        //     $('.hidecol_10').addClass('hide');
        //     $('.showLess_1').removeClass('hide');
        //     $('.showLess_10').removeClass('hide');
        //     $(this).addClass('hide');
        // });
    </script>
</body>

</html>