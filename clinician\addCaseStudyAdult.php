<?php
$IsMobile = isset($_GET['IsMobile']) ? ($_GET['IsMobile']) : 0;

include('../includes/config.php');
include('includes/validateUserLogin.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsRotation.php');
include('../class/clsLocations.php');
include('../class/clsCaseStudy.php');
include('../setRequest.php');

$schoolId = 0;
$schoolId = $currentSchoolId;
$page_title = "Add Case Study";
$bedCrumTitle = 'Add';
$caseStudyId = '';
$rotationId = 0;
$adultCohort = '';
$adultAdmissionDate = '';
$adultPtAge = '';
$adultSex = '';
$adultHt = '';
$adultSmoking = '';
$adultChiefComplaint = '';
$adultRespiratory = '';
$adultOtherPulmonaryProblems = '';
$adultHR = '';
$adultSpontRR = '';
$adultBP = '';
$adultTemp = '';
$adultSpO2 = '';
$adultIO = '';
$adultBreathSounds = '';
$adultLevelofActivity = '';
$adultNa = '';
$adultK = '';
$adultCl = '';
$adultWBC = '';
$adultHgb = '';
$adultHct = '';
$adultCO2 = '';
$adultBUN = '';
$adultGlucose = '';
$adultPlatelets = '';
$adultINR = '';
$adultSputumCult = '';
$adultCreatinine = '';
$adultLabInterpretation = '';
$adultXRayInterpretation = '';
$adultETT  = '';
$adultPosition  = '';
$adultTrachType  = '';
$adult1  = '';
$adultCuffPressure  = '';
$caseStudydate = '';
$schoolDate = '';
$clinicianDate = '';
$adultPastMedicalHx = '';
$adultpH  = '';
$adultPaCO2  = '';
$adultHCO3  = '';
$adultSvO2  = '';
$adultOtherSettings = '';
$adultRecommendations = '';
$adultOtherAlarms = '';
// $studentcomments  = '';
$studentcomments  = 'Assessment: &#013;&#010;&#010;Plan of care:&#013;&#010;&#010;Critical Thinking Questions:&#013;&#010;&#010;';
/* 21042021 */
$clinician_comments = '';
$school_comments = '';
// $school_comments = 'No';
/* 21042021 */

$adultCO  = '';
$adultPAP  = '';
$adultPaO2  = '';
$adultSaO2  = '';
$adultFiO2Lpm  = '';
$adultCVP  = '';
$adultPCWP  = '';
$adultICP  = '';
$adultDateBloodGas  = '';
$adultInterpretationHemodynamics  = '';

$adultInterpretationABG  = '';
$adultEKGResults  = '';
$adultInterpretationPAO2  = '';
$adultInterpretationAO2  = '';
$adultInterpretationCaO2  = '';

$adultInterpretationPFRatio  = '';
$adultVentilator  = '';
$adultFiO2  = '';
$adultPiP  = '';
$adultPlat  = '';

$adultRR  = '';
$adultMode  = '';
$adultPSupport  = '';
$adultMAP  = '';
$adultVE  = '';
$adultSetRate  = '';
$adultMaxFlow  = '';
$adultSpontVt  = '';
$adultIE  = '';
$adultSetVt  = '';

$adultFlowSens  = '';
$adultCsta  = '';
$adultRaw  = '';
$adultVte  = '';
$adultIBWVt  = '';
$adultItime  = '';
$adultPEEPCPAP  = '';
$adultHumidityTemp  = '';
$adultSputumAmount  = '';
$adultLowHiPiP  = '';

$adultLowHiVte  = '';
$adultLowHiVe  = '';
$adultLowHiRR  = '';
$adultApneaAlert  = '';
$adultIPAP  = '';
$adultEPAP  = '';
$adultRate  = '';
$adultFiO21  = '';

$adultSpontVt  = '';
$adultItimeSetting  = '';
$adultRise  = '';

$adultRamp  = '';
$adultFVC  = '';
$adultFEF25  = '';
$adultFEV1  = '';
$adultPEFR  = '';
$adultFEV1FVC  = '';
$adultLungVolumes  = '';
$adultInterpretationPFT  = '';
$adultMedicationsUse1  = '';
$adultModificationCarePlan1  = '';

$adultMedicationsUse2  = '';
$adultModificationCarePlan2  = '';
$adultMedicationsUse3  = '';
$adultModificationCarePlan3  = '';
$adultMedicationsUse4  = '';
$adultModificationCarePlan4  = '';
$adultMedicationsUse5  = '';
$adultModificationCarePlan5  = '';
$adultMedicationsUse6  = '';
$adultModificationCarePlan6  = '';
$adultMedicationsUse7  = '';
$adultModificationCarePlan7  = '';
$adultMedicationsUse8  = '';
$adultModificationCarePlan8  = '';
$adultMedicationsUse9  = '';
$adultModificationCarePlan9  = '';
$adultMedicationsUse10  = '';
$adultModificationCarePlan10  = '';
$adultMedicationsUse11  = '';
$adultModificationCarePlan11  = '';
$adultMedicationsUse12  = '';
$adultModificationCarePlan12  = '';
$adultMedicationsUse13  = '';
$adultModificationCarePlan13  = '';
$adultMedicationsUse14  = '';
$adultModificationCarePlan14  = '';
$adultMedicationsUse15  = '';
$adultModificationCarePlan15  = '';
$adultMedicationsUseList  = '';
$adultModificationCarePlanList  = '';
$adultSuction = '';
$adultCough = '';
$studentId = 0;
$isClinicianSignoff = 0;
$firstName = '';
$lastName = '';
$fullName = '';
$adultAPGAR = '';
$gestationalAOB = '';
$studentDOB = '';
$adultMomsPARA = '';
$adultParentSmokingHx = '';
$clinicianId = $_SESSION['loggedClinicianId'];
$loggedClinicianType = $_SESSION['loggedClinicianType'];
$TimeZone = $_SESSION["loggedClinicianSchoolTimeZone"];
$view = '';

//object
$objRotation = new clsRotation();

if (isset($_GET['rotationId'])) {
    $DefaultrotationId = $_GET['rotationId'];
    $rotationId = DecodeQueryData($DefaultrotationId);
}

if (isset($_GET['view'])) {
    $view = $_GET['view'];
}

if (isset($_GET['caseStudyId'])) //Edit Mode
{
    $caseStudyId = $_GET['caseStudyId'];
    $caseStudyId = DecodeQueryData($caseStudyId);
    $page_title = "Edit Case Study";
    $bedCrumTitle = 'Edit';

    $objCaseStudy = new clsCaseStudy();
    $row = $objCaseStudy->GetAdultCaseStudyForStudent($caseStudyId);
    unset($objCaseStudy);
    if ($row == '') {
        header('location:caseStudyList.html');
    }

    $rotationId  = stripslashes($row['rotationId']);
    $schoolDate  = ($row['schoolDate']);
    $courselocationId = $row['locationId'];
    $parentRotationId = stripslashes($row['parentRotationId']);
    $rotationLocationId = stripslashes($row['rotationLocationId']);
    $locationId = 0;

    if ($rotationLocationId != $courselocationId && $parentRotationId > 0) {
        if (!$rotationLocationId)
            $locationId = $objRotation->GetLocationByRotation($rotationId);
        else
            $locationId  = $rotationLocationId;
    } else {
        $locationId  = $courselocationId;
    }

    //Get Time Zone By Rotation 
    $objLocation = new clsLocations();
    $TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
    unset($objLocation);
    if ($TimeZone == '')
        $TimeZone = $_SESSION["loggedClinicianSchoolTimeZone"];
    if ($schoolDate) {
        $schoolDate = converFromServerTimeZone($schoolDate, $TimeZone);
        $schoolDate = date('m/d/Y', strtotime($schoolDate));
    }
    $caseStudydate  = ($row['caseStudydate']);
    if ($caseStudydate) {
        $caseStudydate = converFromServerTimeZone($caseStudydate, $TimeZone);
        $caseStudydate = date('m/d/Y', strtotime($caseStudydate));
    }

    $clinicianDate  = stripslashes($row['ClinicianDate']);
    if ($clinicianDate) {
        $isClinicianSignoff = 1;
        $clinicianDate = converFromServerTimeZone($clinicianDate, $TimeZone);
        $clinicianDate = date('m/d/Y', strtotime($clinicianDate));
    }
    $firstName  = ($row['firstName']);
    $lastName  = ($row['lastName']);
    $fullName = $firstName . ' ' . $lastName;
    $adultCohort  = ($row['adultCohort']);
    $adultAdmissionDate  = ($row['adultAdmissionDate']);
    $adultPtAge  = ($row['adultPtAge']);
    $adultSex  = ($row['adultSex']);
    $adultHt  = ($row['adultHt']);
    $adultSmoking  = ($row['adultSmoking']);
    $adultChiefComplaint  = ($row['adultChiefComplaint']);
    $adultRespiratory  = ($row['adultRespiratory']);
    $adultPastMedicalHx  = ($row['adultPastMedicalHx']);
    $adultOtherPulmonaryProblems  = ($row['adultOtherPulmonaryProblems']);
    $adultHR  = ($row['adultHR']);
    $adultSpontRR  = ($row['adultSpontRR']);
    $adultBP  = ($row['adultBP']);
    $adultTemp  = ($row['adultTemp']);
    $adultSpO2  = ($row['adultSpO2']);
    $adultIO  = ($row['adultIO']);
    $adultBreathSounds  = ($row['adultBreathSounds']);
    $adultLevelofActivity  = ($row['adultLevelofActivity']);
    $adultNa  = ($row['adultNa']);
    $adultK  = ($row['adultK']);

    $adultCl  = ($row['adultCl']);
    $adultWBC  = ($row['adultWBC']);
    $adultHgb  = ($row['adultHgb']);
    $adultHct  = ($row['adultHct']);
    $adultCO2  = ($row['adultCO2']);
    $adultBUN  = ($row['adultBUN']);
    $adultGlucose  = ($row['adultGlucose']);
    $adultPlatelets  = ($row['adultPlatelets']);
    $adultINR  = ($row['adultINR']);
    $adultSputumCult  = ($row['adultSputumCult']);
    $adultCreatinine  = ($row['adultCreatinine']);
    $adultLabInterpretation  = ($row['adultLabInterpretation']);
    $adultXRayInterpretation  = ($row['adultXRayInterpretation']);
    $adultETT  = ($row['adultETT']);
    $adultPosition  = ($row['adultPosition']);
    $adultTrachType  = ($row['adultTrachType']);
    $adult1  = ($row['adult1']);
    $adultCuffPressure  = ($row['adultCuffPressure']);

    $adultpH  = ($row['adultpH']);
    $adultPaCO2  = ($row['adultPaCO2']);
    $adultHCO3  = ($row['adultHCO3']);
    $adultSvO2  = ($row['adultSvO2']);

    $adultCO  = ($row['adultCO']);
    $adultPAP  = ($row['adultPAP']);
    $adultPaO2  = ($row['adultPaO2']);
    $adultSaO2  = ($row['adultSaO2']);
    $adultFiO2Lpm  = ($row['adultFiO2Lpm']);
    $adultCVP  = ($row['adultCVP']);
    $adultPCWP  = ($row['adultPCWP']);
    $adultICP  = ($row['adultICP']);
    $adultDateBloodGas  = ($row['adultDateBloodGas']);
    $adultInterpretationHemodynamics  = ($row['adultInterpretationHemodynamics']);

    $studentcomments  = stripslashes($row['studentcomments']);
    // if ($studentcomments == '')
    //     $studentcomments = 'No';

    /* 21042021 */
    $clinician_comments = stripslashes($row['clinician_comments']);
    $school_comments  = stripslashes($row['school_comments']);
    // if ($school_comments != '') {
    //     $school_comments = 'Yes';
    // } else {
    //     $school_comments = 'No';
    // }
    /* 21042021 */

    $adultInterpretationABG  = ($row['adultInterpretationABG']);
    $adultEKGResults  = ($row['adultEKGResults']);
    $adultInterpretationPAO2  = ($row['adultInterpretationPAO2']);
    $adultInterpretationAO2  = ($row['adultInterpretationAO2']);
    $adultInterpretationCaO2  = ($row['adultInterpretationCaO2']);

    $adultInterpretationPFRatio  = ($row['adultInterpretationPFRatio']);
    $adultVentilator  = ($row['adultVentilator']);
    $adultFiO2  = ($row['adultFiO2']);
    $adultPiP  = ($row['adultPiP']);
    $adultPlat  = ($row['adultPlat']);

    $adultRR  = ($row['adultRR']);
    $adultMode  = ($row['adultMode']);
    $adultPSupport  = ($row['adultPSupport']);
    $adultMAP  = ($row['adultMAP']);
    $adultVE  = ($row['adultVE']);
    $adultSetRate  = ($row['adultSetRate']);
    $adultMaxFlow  = ($row['adultMaxFlow']);
    $adultSpontVt  = ($row['adultSpontVt']);
    $adultIE  = ($row['adultIE']);
    $adultSetVt  = ($row['adultSetVt']);

    $adultFlowSens  = ($row['adultFlowSens']);
    $adultCsta  = ($row['adultCsta']);
    $adultRaw  = ($row['adultRaw']);
    $adultVte  = ($row['adultVte']);
    $adultIBWVt  = ($row['adultIBWVt']);
    $adultItime  = ($row['adultItime']);
    $adultPEEPCPAP  = ($row['adultPEEPCPAP']);
    $adultHumidityTemp  = ($row['adultHumidityTemp']);
    $adultSputumAmount  = ($row['adultSputumAmount']);
    $adultOtherSettings  = ($row['adultOtherSettings']);
    $adultRecommendations  = ($row['adultRecommendations']);
    $adultLowHiPiP  = ($row['adultLowHiPiP']);

    $adultLowHiVte  = ($row['adultLowHiVte']);
    $adultLowHiVe  = ($row['adultLowHiVe']);
    $adultLowHiRR  = ($row['adultLowHiRR']);
    $adultApneaAlert  = ($row['adultApneaAlert']);
    $adultOtherAlarms  = ($row['adultOtherAlarms']);

    $adultIPAP  = ($row['adultIPAP']);
    $adultEPAP  = ($row['adultEPAP']);
    $adultRate  = ($row['adultRate']);
    $adultFiO21  = ($row['adultFiO21']);

    $adultSpontVt  = ($row['adultSpontVt']);
    $adultItimeSetting  = ($row['adultItimeSetting']);
    $adultRise  = ($row['adultRise']);

    $adultRamp  = ($row['adultRamp']);
    $adultFVC  = ($row['adultFVC']);
    $adultFEF25  = ($row['adultFEF25']);
    $adultFEV1  = ($row['adultFEV1']);
    $adultPEFR  = ($row['adultPEFR']);
    $adultFEV1FVC  = ($row['adultFEV1FVC']);
    $adultLungVolumes  = ($row['adultLungVolumes']);
    $adultInterpretationPFT  = ($row['adultInterpretationPFT']);
    $adultMedicationsUse1  = ($row['adultMedicationsUse1']);
    $adultModificationCarePlan1  = ($row['adultModificationCarePlan1']);

    $adultMedicationsUse2  = ($row['adultMedicationsUse2']);
    $adultModificationCarePlan2  = ($row['adultModificationCarePlan2']);
    $adultMedicationsUse3  = ($row['adultMedicationsUse3']);
    $adultModificationCarePlan3  = ($row['adultModificationCarePlan3']);
    $adultMedicationsUse4  = ($row['adultMedicationsUse4']);
    $adultModificationCarePlan4  = ($row['adultModificationCarePlan4']);
    $adultMedicationsUse5  = ($row['adultMedicationsUse5']);
    $adultModificationCarePlan5  = ($row['adultModificationCarePlan5']);
    $adultMedicationsUse6  = ($row['adultMedicationsUse6']);
    $adultModificationCarePlan6  = ($row['adultModificationCarePlan6']);
    $adultMedicationsUse7  = ($row['adultMedicationsUse7']);
    $adultModificationCarePlan7  = ($row['adultModificationCarePlan7']);
    $adultMedicationsUse8  = ($row['adultMedicationsUse8']);
    $adultModificationCarePlan8  = ($row['adultModificationCarePlan8']);
    $adultMedicationsUse9  = ($row['adultMedicationsUse9']);
    $adultModificationCarePlan9  = ($row['adultModificationCarePlan9']);
    $adultMedicationsUse10  = ($row['adultMedicationsUse10']);
    $adultModificationCarePlan10  = ($row['adultModificationCarePlan10']);
    $adultMedicationsUse11  = ($row['adultMedicationsUse11']);
    $adultModificationCarePlan11  = ($row['adultModificationCarePlan11']);
    $adultMedicationsUse12  = ($row['adultMedicationsUse12']);
    $adultModificationCarePlan12  = ($row['adultModificationCarePlan12']);
    $adultMedicationsUse13  = ($row['adultMedicationsUse13']);
    $adultModificationCarePlan13  = ($row['adultModificationCarePlan13']);
    $adultMedicationsUse14  = ($row['adultMedicationsUse14']);
    $adultModificationCarePlan14  = ($row['adultModificationCarePlan14']);
    $adultMedicationsUse15  = ($row['adultMedicationsUse15']);
    $adultModificationCarePlan15  = ($row['adultModificationCarePlan15']);
    $adultMedicationsUseList  = ($row['adultMedicationsUseList']);
    $adultModificationCarePlanList  = ($row['adultModificationCarePlanList']);
    $adultCough  = ($row['adultCough']);
    $adultSuction  = ($row['adultSuction']);
    $adultAPGAR  = ($row['adultAPGAR']);
    $gestationalAOB  = ($row['gestationalAOB']);
    // if ($gestationalAOB) {
    //     $gestationalAOB = converFromServerTimeZone($gestationalAOB, $TimeZone);
    //     $gestationalAOB = date('m/d/Y', strtotime($gestationalAOB));
    // }
    $studentDOB  = ($row['studentDOB']);
    if ($studentDOB) {
        $studentDOB = converFromServerTimeZone($studentDOB, $TimeZone);
        $studentDOB = date('m/d/Y', strtotime($studentDOB));
    }
    $adultMomsPARA  = ($row['adultMomsPARA']);
    $adultParentSmokingHx  = ($row['adultParentSmokingHx']);
}

//$rotation=$objRotation->GetRotationByStudent($schoolId,$studentId=0);
$rotation = $objRotation->GetAllActiveRotation($schoolId, $studentId);
unset($objRotation);

$clinicianDate = ($clinicianDate != '') ? $clinicianDate : date('m/d/Y');

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title>
        <?php echo ($page_title); ?>
    </title>
    <?php include('includes/headercss.php'); ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/stepper.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">

    <style>
        .d-flex {
            display: flex;
        }

        .margin-right {
            margin-right: 7px;
        }

        .checkboxStyle {
            margin: 0 !important;
            width: 50%;
            border: 1px solid #ccc;
        }

        .alignCenter {
            align-items: center !important;
        }

        .abnormal {
            color: red !important;
        }

        .input-group-addon {
            position: absolute;
            right: 7px;
            /* width: 100%; */
            z-index: 99;
            width: 35px;
            margin: auto;
            top: 5px;
            border-left: 1px solid #ccc;
            border-radius: 50% !important;
            padding: 10px -2px;
            height: 35px;
            /* background: #01A750; */
            /* color: #fff; */
            color: #555;
            /* background: #f6f9f9; */
            background: transparent;
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .panel-default>.panel-heading {
            background-color: transparent !important;
        }

        .btn-success,
        .btn-default {
            padding: 8px 25px;
            border-radius: 10px;
        }

        .panel {
            border-radius: 14px !important;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 45px !important;
        }

        .required-select2 {
            border-left: solid 3px red !important;
            border-radius: 12px !important;
        }

        .select2-container--default .select2-selection--single {
            background-color: #f6f9f9 !important;
            cursor: default !important;
            height: 45px !important;
            border-radius: 10px !important;
        }

        .select2-container--default .select2-selection--single {
            border: none !important;
        }

        .panel,
        .form-group {
            margin-bottom: 10px;
        }

        .bootstrap-datetimepicker-widget {
            border-radius: 12px !important;
        }

        .form-control {
            height: 45px;
        }

        textarea.form-control {
            height: auto;
        }

        .pl-0 {
            padding-left: 0;
        }

        .blood-pressure-input {
            width: 80px;
            display: inline-block;
            margin: 5px 10px;
        }

        .abnormal-value-checkbox {
            padding: 0;
            width: 37px;
            height: 37px;
            margin-left: 10px;
        }

        .row-delete-icon {
            font-size: 18px;
            padding-left: 6px;
        }

        .mobile-block {
            display: flex;
            align-items: center;
        }

        /* .form-horizontal .form-group {
            margin-right: 0;
            margin-left: 0;
        } */


        .select2-container {
            width: 100% !important;
        }

        ion-icon {
            pointer-events: none;
        }

        .icon-inner,
        .ionicon,
        svg {
            color: #01A750;
        }

        @media screen and (max-width: 500px) {
            .panel-body ol {
                padding-left: 20px;
            }

            .panel-default>.panel-heading {
                padding-left: 5px;
            }

            .blood-pressure-input {
                width: 100%;
                margin: 5px 0;
            }

            /* .container-zero{
            padding: 0;
         } */

            .mobile-gray-border {
                border: 1px solid #ececec;
            }

            .collapsible {
                cursor: pointer;
                padding: 15px;
                /* border: 1px solid #181818; */
                /* background-color: #f9f9f9; */
                display: flex;
                justify-content: space-between;
                align-items: center;
                /* border-radius: 14px; */
            }

            .collapsible p {
                margin: 0;
            }

            .collapsible-arrow {
                font-size: 18px;
                transition: transform 0.3s ease;
            }

            .content {
                display: none;
                padding: 10px 0;
                /* border-top: 1px solid #ececec; */
            }

            .content.active {
                display: block;
            }

            .active.collapsible-arrow {
                transform: rotate(180deg);
            }

            .row-delete-icon {
                position: absolute;
                top: -82px;
                right: 20px;
            }

            .mobile-block {
                display: block;
            }

            .form-stepper-horizontal li {
                min-width: 90px;
            }

            .form-step {
                padding: 2rem 1rem;
            }

            .pt-0 {
                padding-top: 0 !important;
            }
        }
    </style>
</head>

<body>
<?php if ($IsMobile == 0) { ?>

    <?php include('includes/header.php'); ?>
    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="caseStudyList.html?active=adult">Case Study</a></li>
                    <li><a href="caseStudyList.html?active=adult">Adult ICU & NICU/PICU</a></li>
                    <li class="active">
                        <?php echo ($bedCrumTitle); ?>
                    </li>
                </ol>
            </div>

        </div>
    </div>
    <?php  }  ?>

    <div class="container mb-15 mobile-padding-4">
        <!-- stepper start -->
        <div>
            <div id="multi-step-form-container">
                <!-- Form Steps / Progress Bar -->
                <ul class="form-stepper form-stepper-horizontal text-center mx-auto">
                    <!-- Step 1 -->
                    <li class="form-stepper-active text-center form-stepper-list" style="" step="1">
                        <a class="mx-2">
                            <span class="form-stepper-circle">
                                <span>1</span>
                            </span>
                            <div class="label stepper-label" style="">Details</div>
                        </a>
                    </li>
                    <!-- Step 2 -->
                    <li class="form-stepper-unfinished text-center form-stepper-list" step="2">
                        <a class="mx-2">
                            <span class="form-stepper-circle text-muted">
                                <span>2</span>
                            </span>
                            <div class="label text-muted stepper-label">Vitals</div>
                        </a>
                    </li>
                    <!-- Step 3 -->
                    <li class="form-stepper-unfinished text-center form-stepper-list" step="3">
                        <a class="mx-2">
                            <span class="form-stepper-circle text-muted">
                                <span>3</span>
                            </span>
                            <div class="label text-muted stepper-label">Readings</div>
                        </a>
                    </li>
                    <!-- Step 4 -->
                    <li class="form-stepper-unfinished text-center form-stepper-list" step="4">
                        <a class="mx-2">
                            <span class="form-stepper-circle text-muted">
                                <span>4</span>
                            </span>
                            <div class="label text-muted stepper-label">Medications</div>
                        </a>
                    </li>
                    <!-- Step 5 -->
                    <li class="form-stepper-unfinished text-center form-stepper-list" step="5">
                        <a class="mx-2">
                            <span class="form-stepper-circle text-muted">
                                <span>5</span>
                            </span>
                            <div class="label text-muted stepper-label">Comments</div>
                        </a>
                    </li>
                    <!-- Step 6 -->
                    <!-- <li class="form-stepper-unfinished text-center form-stepper-list" step="6">
                            <a class="mx-2">
                                <span class="form-stepper-circle text-muted">
                                    <span>6</span>
                                </span>
                                <div class="label text-muted stepper-label">Comment</div>
                            </a>
                        </li> -->
                </ul>
            </div>
        </div>


        <form id="formCaseStudy" data-parsley-validate class="form-horizontal" method="POST" action="addCaseStudyAdultSubmit.html?caseStudyId=<?php echo encodeQueryData($caseStudyId) ?>">
            <input type="hidden" name="IsMobile" id="IsMobile" value="<?php echo $IsMobile; ?>">

            <section id="step-1" class="form-step pt-0" data-parsley-validate>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="caseStudydate"> Date</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <div class='input-group date w-full' id='caseStudydate' style="position: relative;">
                                    <input type='text' name="caseStudydate" id="caseStudydate" class="form-control input-md required-input rotation_date dateInputFormat" value="<?php echo $caseStudydate; ?>" required data-parsley-errors-container="#error-txtDate" readonly placeholder="MM-DD-YYYY" />
                                    <span class="input-group-addon calender-icon">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>
                                </div>
                                <div id="error-txtDate"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="cborotation">Rotation</label>
                            <div class="col-md-12">
                                <select id="cborotation" name="cborotation" class="form-control input-md  select2_single required-input" required data-parsley-errors-container="#error-txtRotation" disabled>
                                    <option value="" selected>Select</option>
                                    <?php
                                    if ($rotation != "") {
                                        while ($row = mysqli_fetch_assoc($rotation)) {
                                            $selrotationId  = $row['rotationId'];
                                            $name  = stripslashes($row['title']);
                                    ?>
                                            <option value="<?php echo EncodeQueryData($selrotationId); ?>" <?php if ($rotationId == $selrotationId) { ?> selected="true" <?php } ?>>
                                                <?php echo ($name); ?>
                                            </option>
                                    <?php
                                        }
                                    }
                                    ?>
                                </select>
                                <div id="error-txtRotation"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="studentName">Student Name</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <input type='text' name="studentName" class="form-control input-md " value="<?php echo $fullName; ?>" readonly />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="ClinicianDate">Clinician Signature</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <div class='input-group date w-full' id='ClinicianDate' style="position: relative;">
                                    <input type='text' name="ClinicianDate" id="ClinicianDate" class="form-control input-md required-input dateInputFormat" value="<?php echo $clinicianDate; ?>" data-parsley-errors-container="#error-txtClinicianDate" placeholder="MM-DD-YYYY" />
                                    <span class="input-group-addon calender-icon">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>

                                </div>
                                <div id="error-txtClinicianDate"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="schoolDate">School Signature</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <div class='input-group date w-full' id='schoolDate' style="position: relative;">
                                    <input type='text' name="schoolDate" id="schoolDate" class="form-control input-md dateInputFormat " value="<?php echo $schoolDate; ?>" readonly placeholder="MM-DD-YYYY" />
                                    <span class="input-group-addon calender-icon">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="adultCohort">Cohort</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <input type="text" class="form-control input-md" value="<?php echo $adultCohort; ?>" name="adultCohort" id="adultCohort">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="adultAdmissionDate">Admission Date</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <div class='input-group date w-full' id='adultAdmissionDate' style="position: relative;">
                                    <input type='text' name="adultAdmissionDate" id="adultAdmissionDate" class="form-control input-md dateInputFormat" value="<?php echo $adultAdmissionDate; ?>" placeholder="MM-DD-YYYY" />
                                    <span class="input-group-addon calender-icon">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 p-0">
                        <div class=" col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="adultPtAge">Pt Age</label>
                                <div class="col-md-12 col-sm-12 col-xs-12">
                                    <input type="text" class="form-control input-md" value="<?php echo $adultPtAge; ?>" name="adultPtAge" id="adultPtAge">
                                </div>
                            </div>
                        </div>
                        <div class=" col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="Sex">Sex</label>
                                <div class="col-md-12 col-sm-12 col-xs-12 padding_right_zero">
                                    <input type="text" class="form-control input-md" value="<?php echo $adultSex; ?>" name="adultSex" id="adultSex">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 p-0">
                        <div class=" col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="adultHt">Ht</label>
                                <div class="col-md-12 col-sm-12 col-xs-12">
                                    <input type="text" class="form-control input-md" value="<?php echo $adultHt; ?>" name="adultHt" id="adultHt">
                                </div>
                            </div>
                        </div>
                        <div class=" col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="adultSmoking padding_right_zero">Smoking: (Pack/Yrs)</label>
                                <div class="col-md-12 col-sm-12 col-xs-12">
                                    <input type="text" class="form-control input-md" value="<?php echo $adultSmoking; ?>" name="adultSmoking" id="adultSmoking">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="gestationalAOB">Gestational Age at Birth</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <input type='text' name="gestationalAOB" id="gestationalAOB" class="form-control input-md " value="<?php echo $gestationalAOB; ?>" />
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="studentDOB">Date of Birth</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <div class='input-group date w-full' id='studentDOB' style="position: relative;">
                                    <input type='text' name="studentDOB" id="studentDOB" class="form-control input-md " value="<?php echo $studentDOB; ?>" placeholder="MM-DD-YYYY" />
                                    <span class="input-group-addon calender-icon">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="adultAPGAR">APGAR</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <input type="text" class="form-control input-md" value="<?php echo $adultAPGAR; ?>" name="adultAPGAR" id="adultAPGAR">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="adultRespiratory">Respiratory Rx(s)</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <input type="text" class="form-control input-md" value="<?php echo $adultRespiratory; ?>" name="adultRespiratory" id="adultRespiratory">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="adultChiefComplaint">Chief Complaint / Admitting Diagnosis</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <textarea type="text" class="form-control input-md" name="adultChiefComplaint" id="adultChiefComplaint" rows="3"><?php echo $adultChiefComplaint; ?></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="adultPastMedicalHx">Past Medical Hx</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <textarea type="text" class="form-control input-md" name="adultPastMedicalHx" id="adultPastMedicalHx" rows="3"><?php echo $adultPastMedicalHx; ?></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="adultMomsPARA"> Mom’s PARA</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <input type="text" class="form-control input-md" value="<?php echo $adultMomsPARA; ?>" name="adultMomsPARA" id="adultMomsPARA">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="adultParentSmokingHx">Parent/Guardian Smoking Hx</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <input type="text" class="form-control input-md" value="<?php echo $adultParentSmokingHx; ?>" name="adultParentSmokingHx" id="adultParentSmokingHx">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="adultOtherPulmonaryProblems">Other Pulmonary Problems:</label>
                            <div class="col-md-12 col-sm-12 col-xs-12">
                                <input type="text" class="form-control input-md" value="<?php echo $adultOtherPulmonaryProblems; ?>" name="adultOtherPulmonaryProblems" id="adultOtherPulmonaryProblems">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-3 flex-end">
                    <button class="button next btn-navigate-form-step" type="button" step_number="2">Next</button>
                </div>
            </section>
            <!-- Step 2 Content, default hidden on page load. -->
            <section id="step-2" class="form-step d-none pt-0" data-parsley-validate>
                <div class="row padding_top_ten margin_bottom_ten w-full" style="margin: 10px auto;">
                    <!-- <div class="row text-center margin_bottom_ten">
                        <label><b>Vital Signs</b></label>
                    </div> -->
                    <div class="row">
                        <div class="col-md-6 p-0">
                            <div class=" col-md-4">
                                <div class="form-group">
                                    <label class="col-md-12 control-label" for="adultHR">HR</label>
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <input type="text" class="form-control input-md" value="<?php echo $adultHR; ?>" name="adultHR" id="adultHR" readonly>
                                    </div>
                                </div>
                            </div>
                            <div class=" col-md-4">
                                <div class="form-group">
                                    <label class="col-md-12 control-label" for="adultSpontRR">Spont. RR</label>
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <input type="text" class="form-control input-md" value="<?php echo $adultSpontRR; ?>" name="adultSpontRR" id="adultSpontRR" readonly>
                                    </div>
                                </div>
                            </div>
                            <div class=" col-md-4">
                                <div class="form-group">
                                    <label class="col-md-12 control-label" for="adultBP">BP</label>
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <input type="text" class="form-control input-md" value="<?php echo $adultBP; ?>" name="adultBP" id="adultBP" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 p-0">
                            <div class=" col-md-4">
                                <div class="form-group">
                                    <label class="col-md-12 control-label" for="adultTemp">Temp</label>
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <input type="text" class="form-control input-md" value="<?php echo $adultTemp; ?>" name="adultTemp" id="adultTemp" readonly>
                                    </div>
                                </div>
                            </div>
                            <div class=" col-md-4">
                                <div class="form-group">
                                    <label class="col-md-12 control-label" for="adultSpO2">SpO2</label>
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <input type="text" class="form-control input-md" value="<?php echo $adultSpO2; ?>" name="adultSpO2" id="adultSpO2" readonly>
                                    </div>
                                </div>
                            </div>
                            <div class=" col-md-4">
                                <div class="form-group">
                                    <label class="col-md-12 control-label" for="adultIO">I/O</label>
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <input type="text" class="form-control input-md" value="<?php echo $adultIO; ?>" name="adultIO" id="adultIO" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="adultBreathSounds">Breath Sounds</label>
                                <div class="col-md-12 col-sm-12 col-xs-12">
                                    <input type="text" class="form-control input-md" value="<?php echo $adultBreathSounds; ?>" name="adultBreathSounds" id="adultBreathSounds" readonly>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="adultLevelofActivity">Level of Activity/Cooperation</label>
                                <div class="col-md-12 col-sm-12 col-xs-12">
                                    <input type="text" class="form-control input-md" value="<?php echo $adultLevelofActivity; ?>" name="adultLevelofActivity" id="adultLevelofActivity" readonly>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-3 flex-end">
                    <button class="button btn-navigate-form-step button-prev" type="button" step_number="1">Prev</button>
                    <button class="button next btn-navigate-form-step" type="button" step_number="3">Next</button>
                </div>
            </section>
            <!-- Step 3 Content, default hidden on page load. -->
            <section id="step-3" class="form-step d-none pt-0" data-parsley-validate>
                <div class="row padding_top_ten margin_bottom_ten w-full" style="margin: 10px auto;">
                    <!-- <div class="row margin_bottom_ten">
                        <div class="col-md-6 text-center"> <label><b>Chemistry Panel</b></label> </div>
                        <div class="col-md-6 text-center"> <label><b>Hematology Panel</b></label> </div>
                    </div> -->
                    <div class="row">
                        <div class="col-md-12 desktop-px-zero">
                            <div class="col-md-6 p-0 mb-10 mobile-padding-zero mobile-collaps-view">
                                <div class="collapsible">
                                    <p style="text-align: center;"> <b> Chemistry Panel </b></p>
                                    <i class="fa fa-chevron-down desktop-hide" aria-hidden="true"></i>
                                </div>
                                <div class="content">
                                    <div class="col-md-12 p-0">
                                        <div class=" col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultNa">Na+ </label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultNa; ?>" name="adultNa" id="adultNa" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class=" col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultK">K+</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultK; ?>" name="adultK" id="adultK" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class=" col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultCl">Cl+</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultCl; ?>" name="adultCl" id="adultCl" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class=" col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultCO2">CO2 </label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultCO2; ?>" name="adultCO2" id="adultCO2" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class=" col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultBUN">BUN</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultBUN; ?>" name="adultBUN" id="adultBUN" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class=" col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultGlucose">Glucose</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultGlucose; ?>" name="adultGlucose" id="adultGlucose" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultCreatinine">Creatinine </label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultCreatinine; ?>" name="adultCreatinine" id="adultCreatinine" readonly>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 p-0 mb-10 mobile-padding-zero mobile-collaps-view">
                                <div class="collapsible">
                                    <p style="text-align: center;"><b> Hematology Panel </b></p>
                                    <i class="fa fa-chevron-down desktop-hide" aria-hidden="true"></i>
                                </div>
                                <div class="content">
                                    <div class="col-md-12 p-0">
                                        <div class=" col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultWBC">WBC</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultWBC; ?>" name="adultWBC" id="adultWBC" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class=" col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultHgb">Hgb</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultHgb; ?>" name="adultHgb" id="adultHgb" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class=" col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultHct">Hct</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultHct; ?>" name="adultHct" id="adultHct" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class=" col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultPlatelets">Platelets</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultPlatelets; ?>" name="adultPlatelets" id="adultPlatelets" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class=" col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultINR">INR</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultINR; ?>" name="adultINR" id="adultINR" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class=" col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultSputumCult">Sputum Cult</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultSputumCult; ?>" name="adultSputumCult" id="adultSputumCult" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultLabInterpretation">Lab Interpretation </label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <textarea type="text" class="form-control input-md" name="adultLabInterpretation" id="adultLabInterpretation" rows="2" readonly><?php echo $adultLabInterpretation; ?></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- <div class="row padding_top_ten margin_bottom_ten padding_right_ten formBorder"> -->
                            <!-- <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="col-md-2 control-label" for="adultXRayInterpretation">X-Ray Interpretation </label>
                                            <div class="col-md-10 col-sm-4 col-xs-12">
                                                <textarea type="text" class="form-control input-md" name="adultXRayInterpretation" id="adultXRayInterpretation" rows="1" readonly><?php echo $adultXRayInterpretation; ?></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div> -->
                            <div class="col-md-12 p-0 mb-10 mobile-padding-zero mobile-collaps-view">
                                <div class="collapsible">
                                    <p style="text-align: center;"><b> X-Ray </b></p>
                                    <i class="fa fa-chevron-down desktop-hide" aria-hidden="true"></i>
                                </div>
                                <div class="content">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultXRayInterpretation">X-Ray Interpretation </label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <textarea type="text" class="form-control input-md" name="adultXRayInterpretation" id="adultXRayInterpretation" rows="3" readonly><?php echo $adultXRayInterpretation; ?></textarea>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- <div class="col-md-6"> -->
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultETT">ETT or Trach size</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultETT; ?>" name="adultETT" id="adultETT" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultTrachType">Trach Type</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultTrachType; ?>" name="adultTrachType" id="adultTrachType" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- </div> -->
                                    <!-- <div class="col-md-6"> -->
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultPosition">Position</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultPosition; ?>" name="adultPosition" id="adultPosition" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adult@">@</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adult1; ?>" name="adult1" id="adult1" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultCuffPressure">Cuff Pressure</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultCuffPressure; ?>" name="adultCuffPressure" id="adultCuffPressure" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- </div> -->
                                </div>
                            </div>
                            <div class="col-md-6 p-0 mb-10 mobile-padding-zero mobile-collaps-view">
                                <div class="collapsible">
                                    <p style="text-align: center;"> <b> ABG </b></p>
                                    <i class="fa fa-chevron-down desktop-hide" aria-hidden="true"></i>
                                </div>
                                <div class="content">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultpH">pH</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultpH; ?>" name="adultpH" id="adultpH" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultPaCO2">PaCO2</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultPaCO2; ?>" name="adultPaCO2" id="adultPaCO2" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultHCO3">HCO3</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultHCO3; ?>" name="adultHCO3" id="adultHCO3" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultSvO2">SvO2</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultSvO2; ?>" name="adultSvO2" id="adultSvO2" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultCO">CO</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultCO; ?>" name="adultCO" id="adultCO" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultPAP">PAP</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultPAP; ?>" name="adultPAP" id="adultPAP" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultDateBloodGas">Date / Time Blood Gas Obtained</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultDateBloodGas; ?>" name="adultDateBloodGas" id="adultDateBloodGas" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultInterpretationABG">Interpretation</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <textarea type="text" class="form-control input-md" name="adultInterpretationABG" id="adultInterpretationABG" rows="3" readonly><?php echo $adultInterpretationABG; ?></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 p-0 mb-10 mobile-padding-zero mobile-collaps-view">
                                <div class="collapsible">
                                    <p style="text-align: center;"> <b> Hemodynamics </b></p>
                                    <i class="fa fa-chevron-down desktop-hide" aria-hidden="true"></i>
                                </div>
                                <div class="content">

                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultPaO2">PaO2</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultPaO2; ?>" name="adultPaO2" id="adultPaO2" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultSaO2">SaO2</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultSaO2; ?>" name="adultSaO2" id="adultSaO2" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultFiO2Lpm">FiO2 / Lpm</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultFiO2Lpm; ?>" name="adultFiO2Lpm" id="adultFiO2Lpm" readonly>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultCVP">CVP</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultCVP; ?>" name="adultCVP" id="adultCVP" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultPCWP">PCWP</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultPCWP; ?>" name="adultPCWP" id="adultPCWP" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultICP">ICP</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultICP; ?>" name="adultICP" id="adultICP" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultInterpretationHemodynamics">Interpretation</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <textarea type="text" class="form-control input-md" name="adultInterpretationHemodynamics" id="adultInterpretationHemodynamics" rows="3" readonly><?php echo $adultInterpretationHemodynamics; ?></textarea>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultEKGResults">EKG Results</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <textarea type="text" class="form-control input-md" name="adultEKGResults" id="adultEKGResults" rows="3" readonly><?php echo $adultEKGResults; ?></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12 p-0 mb-10 mobile-padding-zero mobile-collaps-view">
                                <div class="collapsible">
                                    <p style="text-align: center;"><b> Interpretation </b></p>
                                    <i class="fa fa-chevron-down desktop-hide" aria-hidden="true"></i>
                                </div>
                                <div class="content">
                                    <div class="col-md-12">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label class="col-md-12 control-label" for="adultInterpretationPAO2">PAO2</label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                                        <textarea type="text" class="form-control input-md" name="adultInterpretationPAO2" id="adultInterpretationPAO2" rows="2" readonly><?php echo $adultInterpretationPAO2; ?></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label class="col-md-12 control-label" for="adultInterpretationAO2">(A-a)O2 </label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                                        <textarea type="text" class="form-control input-md" name="adultInterpretationAO2" id="adultInterpretationAO2" rows="2" readonly><?php echo $adultInterpretationAO2; ?></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label class="col-md-12 control-label" for="adultInterpretationCaO2">CaO2</label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                                        <textarea type="text" class="form-control input-md" name="adultInterpretationCaO2" id="adultInterpretationCaO2" rows="2" readonly><?php echo $adultInterpretationCaO2; ?></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label class="col-md-12 control-label" for="adultInterpretationPFRatio">P/F Ratio</label>
                                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                                        <textarea type="text" class="form-control input-md" name="adultInterpretationPFRatio" id="adultInterpretationPFRatio" rows="2" readonly><?php echo $adultInterpretationPFRatio; ?></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-3 flex-end">
                        <button class="button btn-navigate-form-step button-prev" type="button" step_number="2">Prev</button>
                        <button class="button next btn-navigate-form-step" type="button" step_number="4">Next</button>
                    </div>
                </div>
            </section>

            <!-- Step 4 Content, default hidden on page load. -->
            <section id="step-4" class="form-step d-none pt-0" data-parsley-validate>
                <!-- <div class="row text-center margin_bottom_ten">
                    <label class="control-label" for=""><b>Invasive / Non-Invasive Ventilation</b></label>
                </div> -->
                <div class="row padding_top_ten margin_bottom_ten w-full" style="margin: 10px auto;">
                    <div class="row">
                        <div class="col-md-12 desktop-px-zero">
                            <div class="col-md-6 p-0 mb-10 mobile-padding-zero mobile-collaps-view">
                                <!-- <div class="col-md-6 text-center"> -->
                                <!-- <label>
                                    <b>Ordered Settings</b>
                                </label> -->
                                <div class="collapsible">
                                    <p style="text-align: center;"><b> Orderd Settings</b></p>
                                    <i class="fa fa-chevron-down desktop-hide" aria-hidden="true"></i>
                                </div>
                                <div class="content">
                                    <div class="col-md-12 p-0">
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultVentilator">Ventilator</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultVentilator; ?>" name="adultVentilator" id="adultVentilator" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultFiO2">FiO2:</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultFiO2; ?>" name="adultFiO2" id="adultFiO2" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultMode">Mode</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultMode; ?>" name="adultMode" id="adultMode" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultPSupport">P Support</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultPSupport; ?>" name="adultPSupport" id="adultPSupport" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultSetRate">Set Rate</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultSetRate; ?>" name="adultSetRate" id="adultSetRate" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultMaxFlow">Max Flow</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultMaxFlow; ?>" name="adultMaxFlow" id="adultMaxFlow" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultSetVt">Set Vt</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultSetVt; ?>" name="adultSetVt" id="adultSetVt" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultFlowSens">Flow Sens</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultFlowSens; ?>" name="adultFlowSens" id="adultFlowSens" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultIBWVt">IBWVt</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultIBWVt; ?>" name="adultIBWVt" id="adultIBWVt" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultItime">I-time</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultItime; ?>" name="adultItime" id="adultItime" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultPEEPCPAP">PEEP/CPAP</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultPEEPCPAP; ?>" name="adultPEEPCPAP" id="adultPEEPCPAP" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultHumidityTemp">Humidity temp</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultHumidityTemp; ?>" name="adultHumidityTemp" id="adultHumidityTemp" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultOtherSettings">Other Settings</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <textarea type="text" class="form-control input-md" name="adultOtherSettings" id="adultOtherSettings" rows="2" readonly><?php echo $adultOtherSettings; ?></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>


                            <div class="col-md-6 p-0 mb-10 mobile-padding-zero mobile-collaps-view">
                                <!-- <div class="col-md-6 text-center"> -->
                                <!-- <label>
                                        <b>Patient Parameters</b>
                                    </label> -->
                                <div class="collapsible">
                                    <p style="text-align: center;"><b>Patient Parameters </b></p>
                                    <i class="fa fa-chevron-down desktop-hide" aria-hidden="true"></i>
                                </div>
                                <div class="content">
                                    <div class="col-md-12 p-0">
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultPiP">PiP</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultPiP; ?>" name="adultPiP" id="adultPiP" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultPlat">Plat</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultPlat; ?>" name="adultPlat" id="adultPlat" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultRR">RR</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultRR; ?>" name="adultRR" id="adultRR" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultMAP">MAP</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultMAP; ?>" name="adultMAP" id="adultMAP" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultVE">VE</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultVE; ?>" name="adultVE" id="adultVE" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultSpontVt">Spont Vt</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultSpontVt; ?>" name="adultSpontVt" id="adultSpontVt" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultIE">I:E</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultIE; ?>" name="adultIE" id="adultIE" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultCsta">Csta</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <textarea type="text" class="form-control input-md" name="adultCsta" id="adultCsta" rows="2" readonly><?php echo $adultCsta; ?></textarea>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultRaw">Raw</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <textarea type="text" class="form-control input-md" name="adultRaw" id="adultRaw" rows="2" readonly><?php echo $adultRaw; ?></textarea>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultVte">Vte</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultVte; ?>" name="adultVte" id="adultVte" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">

                                            <label class="  control-label" for="adultSuction" style="margin-right: 3px;">Suction</label>
                                            <label class="radio-inline control-label"><input type="radio" class=" input-md" value="Yes" name="adultSuction" <?php if ($adultSuction == 'Yes') { ?> selected="true" <?php } ?> readonly>Yes</label>
                                            <label class="radio-inline control-label"><input type="radio" class=" input-md" value="No" name="adultSuction" <?php if ($adultSuction == 'No') { ?>selected="true" <?php } ?> readonly>No</label>

                                        </div>
                                        <div class="col-md-6">
                                            <label class=" control-label" for="adultCough" style="margin-right: 3px;">Cough</label>
                                            <label class="radio-inline control-label"><input type="radio" class=" input-md" value="Yes" name="adultCough" readonly>Yes</label>
                                            <label class="radio-inline control-label"><input type="radio" class="input-md" value="No" name="adultCough" readonly> No</label>

                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultSputumAmount">Sputum Amount / Color</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultSputumAmount; ?>" name="adultSputumAmount" id="adultSputumAmount" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="col-md-12 control-label" for="adultRecommendations">Student Recommendations</label>
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <textarea type="text" class="form-control input-md" name="adultRecommendations" id="adultRecommendations" rows="1" readonly><?php echo $adultRecommendations; ?></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12 p-0 mb-10 mobile-padding-zero mobile-collaps-view">
                                <!-- <div class="row text-center margin_bottom_ten">
                                    <label class="control-label" for=""><b>Alarms</b></label>
                                </div> -->
                                <div class="collapsible">
                                    <p style="text-align: center;"><b>Alarms </b></p>
                                    <i class="fa fa-chevron-down desktop-hide" aria-hidden="true"></i>
                                </div>
                                <div class="content">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultLowHiPiP">Low / Hi PiP</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultLowHiPiP; ?>" name="adultLowHiPiP" id="adultLowHiPiP" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultLowHiVte">Low / Hi Vte</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultLowHiVte; ?>" name="adultLowHiVte" id="adultLowHiVte" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultLowHiVe">Low / Hi Ve</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultLowHiVe; ?>" name="adultLowHiVe" id="adultLowHiVe" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultLowHiRR">Low / Hi RR</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultLowHiRR; ?>" name="adultLowHiRR" id="adultLowHiRR" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- </div>
                                    <div class="row "> -->
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultApneaAlert">Apnea Alert</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultApneaAlert; ?>" name="adultApneaAlert" id="adultApneaAlert" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-9">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultOtherAlarms">Other Alarms</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <textarea type="text" class="form-control input-md" name="adultOtherAlarms" id="adultOtherAlarms" rows="2" readonly><?php echo $adultOtherAlarms; ?></textarea>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>

                            <!-- BiPAP / O2 via NC settings section start -->
                            <div class="col-md-12 p-0 mb-10 mobile-padding-zero mobile-collaps-view">
                                <!-- <div class="row text-center margin_bottom_ten">
                                    <label class="control-label" for=""><b>BiPAP / O2 via NC settings</b></label>
                                </div> -->
                                <div class="collapsible">
                                    <p style="text-align: center;"><b>Orderd Settings </b></p>
                                    <i class="fa fa-chevron-down desktop-hide" aria-hidden="true"></i>
                                </div>
                                <div class="content">
                                    <!-- <div class="row"> -->
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultIPAP">IPAP</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultIPAP; ?>" name="adultIPAP" id="adultIPAP" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultEPAP">EPAP</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultEPAP; ?>" name="adultEPAP" id="adultEPAP" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultRate">Rate</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultRate; ?>" name="adultRate" id="adultRate" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultFiO21">FiO2</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultFiO21; ?>" name="adultFiO21" id="adultFiO21" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultItimeSetting">I-time</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultItimeSetting; ?>" name="adultItimeSetting" id="adultItimeSetting" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultRise">Rise</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultRise; ?>" name="adultRise" id="adultRise" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- </div> -->

                                    <!-- <div class="row"> -->
                                    <div class="col-md-2">
                                        <div class="form-group ">
                                            <label class="col-md-12 control-label" for="adultRamp">Ramp</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <select id="adultRamp" name="adultRamp" class="form-control input-md  select2_single " disabled>
                                                    <option value="">Select</option>
                                                    <option value="Yes" <?php if ($adultRamp == "Yes") { ?> selected="true" <?php } ?>>Yes</option>
                                                    <option value="No" <?php if ($adultRamp == "No") { ?> selected="true" <?php } ?>>No</option>

                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- </div> -->
                                </div>
                            </div>

                            <!-- PFT section start -->
                            <div class="col-md-12 p-0 mb-10 mobile-padding-zero mobile-collaps-view">
                                <!-- <div class="row text-center margin_bottom_ten">
                                    <label class="control-label" for=""><b>PFT</b></label>
                                </div> -->

                                <div class="collapsible">
                                    <p style="text-align: center;"><b>PFT </b></p>
                                    <i class="fa fa-chevron-down desktop-hide" aria-hidden="true"></i>
                                </div>
                                <div class="content">
                                    <!-- <div class="row"> -->
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultFVC">FVC</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultFVC; ?>" name="adultFVC" id="adultFVC" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultFEF25">FEF 25-75</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultFEF25; ?>" name="adultFEF25" id="adultFEF25" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultFEV1">FEV1</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultFEV1; ?>" name="adultFEV1" id="adultFEV1" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultPEFR">PEFR</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultPEFR; ?>" name="adultPEFR" id="adultPEFR" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultFEV1FVC">FEV1/FVC</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultFEV1FVC; ?>" name="adultFEV1FVC" id="adultFEV1FVC" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- </div> -->

                                    <!-- <div class="row"> -->
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultLungVolumes">Lung Volumes and % Predicted</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultLungVolumes; ?>" name="adultLungVolumes" id="adultLungVolumes" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- </div> -->
                                    <!-- <div class="row"> -->
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="col-md-12 control-label" for="adultInterpretationPFT">Interpretation</label>
                                            <div class="col-md-12 col-sm-12 col-xs-12" style="padding-right: 30px;">
                                                <textarea type="text" class="form-control input-md" name="adultInterpretationPFT" id="adultInterpretationPFT" rows="2" readonly><?php echo $adultInterpretationPFT; ?></textarea>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- </div> -->
                                </div>
                            </div>


                            <div class="col-md-12 p-0 mb-10 mobile-padding-zero mobile-collaps-view">
                                <!-- <div class="row"> -->
                                <div class="col-md-6 mobile-padding-4 text-center">
                                    <div class="form-group mx-0">
                                        <label class="control-label" for="">
                                            <b>List ALL Medications w/Indications for use
                                            </b>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6 mobile-padding-4 text-center">
                                    <div class="form-group mx-0">
                                        <label class="control-label" for="">
                                            <b>Recommended Modifications to Care Plan: (if required)</b>
                                        </label>
                                    </div>
                                </div>
                                <!-- </div> -->
                                <div class="" id="MedicationDiv" style="margin-right: 5px;">
                                    <?php
                                    if ($caseStudyId) {
                                        // DECODE JSON FOR PRINT DATA
                                        $adultMedicationsUseList = json_decode($adultMedicationsUseList, true);
                                        $adultModificationCarePlanList = json_decode($adultModificationCarePlanList, true);
                                        $result = '';
                                        //MEARGE ARRAY 
                                        if ($adultMedicationsUseList && $adultModificationCarePlanList)
                                            $result = array_map('array_merge', $adultMedicationsUseList, $adultModificationCarePlanList);
                                        $listId = 0;

                                        if ($result) {
                                            foreach ($result as $key => $value) {
                                                $listId = $key + 1;
                                                $value = ($value); // convert object to array
                                                // PRINT HTML
                                                $divHtml = '<div class="col-md-12 p-0" id="row_' . $listId . '" isrow="' . $listId . '"> <div class="col-md-6"> <div class="form-group">  <div class="col-md-12    col-sm-12 col-xs-12 mobile-block"><label class="control-label labelcount mr-5" for="adultMedicationsUse' . $listId . '">' . $listId . '</label> <input type="text" class="form-control input-md" value="' . $value[0] . '" name="adultMedicationsUseList[' . $listId . '][]" id="adultMedicationsUse' . $listId . '" readonly> </div> </div> </div> <div class="col-md-6"> <div class="form-group" style="display: flex;align-items: center;"> <div class="col-md-12 col-sm-12 col-xs-12"> <textarea type="text" class="form-control input-md" rows="2" cols="5" readonly name="adultModificationCarePlanList[' . $listId . '][]" id="adultModificationCarePlan' . $listId . '">' . $value[1] . '</textarea> </div> </div> </div> </div>';
                                                echo $divHtml;
                                            }
                                        }

                                    ?>

                                    <?php } ?>
                                </div>
                                <!-- <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="col-md-6 control-label" for="adultMedicationsUse1">1</label>
                                                <div class="col-md-6 col-sm-4 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse1; ?>" readonly name="adultMedicationsUse1" id="adultMedicationsUse1">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <div class="col-md-12 col-sm-4 col-xs-12">
                                                    <textarea type="text" class="form-control input-md" rows="1" readonly cols="5" name="adultModificationCarePlan1" id="adultModificationCarePlan1"><?php echo $adultModificationCarePlan1; ?></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="col-md-6 control-label" for="adultMedicationsUse2">2</label>
                                                <div class="col-md-6 col-sm-4 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse2; ?>" readonly name="adultMedicationsUse2" id="adultMedicationsUse2">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <div class="col-md-12 col-sm-4 col-xs-12">
                                                    <textarea type="text" class="form-control input-md" rows="1" readonly cols="5" name="adultModificationCarePlan2" id="adultModificationCarePlan2"><?php echo $adultModificationCarePlan2; ?></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="col-md-6 control-label labelcount" for="adultMedicationsUse3">3</label>
                                                <div class="col-md-6 col-sm-4 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse3; ?>" readonly name="adultMedicationsUse3" id="adultMedicationsUse3">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <div class="col-md-12 col-sm-4 col-xs-12">
                                                    <textarea type="text" class="form-control input-md" rows="1" readonly cols="5" name="adultModificationCarePlan3" id="adultModificationCarePlan3"><?php echo $adultModificationCarePlan3; ?></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="col-md-6 control-label labelcount" for="adultMedicationsUse4">4</label>
                                                <div class="col-md-6 col-sm-4 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse4; ?>" readonly name="adultMedicationsUse4" id="adultMedicationsUse4">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <div class="col-md-12 col-sm-4 col-xs-12">
                                                    <textarea type="text" class="form-control input-md" rows="1" readonly cols="5" name="adultModificationCarePlan4" id="adultModificationCarePlan4"><?php echo $adultModificationCarePlan4; ?></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="col-md-6 control-label labelcount" for="adultMedicationsUse5">5</label>
                                                <div class="col-md-6 col-sm-4 col-xs-12">
                                                    <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse5; ?>" readonly name="adultMedicationsUse5" id="adultMedicationsUse5">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <div class="col-md-12 col-sm-4 col-xs-12">
                                                    <textarea type="text" class="form-control input-md" rows="1" readonly cols="5" name="adultModificationCarePlan5" id="adultModificationCarePlan5"><?php echo $adultModificationCarePlan5; ?></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div> -->
                                <!-- ----------------------------Show More-------------------------------------- -->
                                <!-- <div class="row">
                                        <div class="col-md-12 form-group justify-content-center" style="width: 173px;margin: 7px;padding-left: 23px;margin-left: 504px;margin-top: -6px;">
                                            <input type="button" class="form-control input-md showMore_6_10" value="Show More Rows">
                                        </div>
                                    </div> -->
                                <!-- ------------------------------------------------------------------ -->
                                <div class="row hidecol_6 hide">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="col-md-6 control-label labelcount" for="adultMedicationsUse6">6</label>
                                            <div class="col-md-6 col-sm-4 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse6; ?>" readonly name="adultMedicationsUse6" id="adultMedicationsUse6">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="col-md-12 col-sm-4 col-xs-12">
                                                <textarea type="text" class="form-control input-md" rows="1" readonly cols="5" name="adultModificationCarePlan6" id="adultModificationCarePlan6"><?php echo $adultModificationCarePlan6; ?></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row hidecol_6 hide">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="col-md-6 control-label labelcount" for="adultMedicationsUse7">7</label>
                                            <div class="col-md-6 col-sm-4 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse7; ?>" readonly name="adultMedicationsUse7" id="adultMedicationsUse7">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="col-md-12 col-sm-4 col-xs-12">
                                                <textarea type="text" class="form-control input-md" rows="1" readonly cols="5" name="adultModificationCarePlan7" id="adultModificationCarePlan7"><?php echo $adultModificationCarePlan7; ?></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row hidecol_6 hide">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="col-md-6 control-label labelcount" for="adultMedicationsUse8">8</label>
                                            <div class="col-md-6 col-sm-4 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse8; ?>" readonly name="adultMedicationsUse8" id="adultMedicationsUse8">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="col-md-12 col-sm-4 col-xs-12">
                                                <textarea type="text" class="form-control input-md" rows="1" readonly cols="5" name="adultModificationCarePlan8" id="adultModificationCarePlan8"><?php echo $adultModificationCarePlan8; ?></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row hidecol_6 hide">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="col-md-6 control-label labelcount" for="adultMedicationsUse9">9</label>
                                            <div class="col-md-6 col-sm-4 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse9; ?>" readonly name="adultMedicationsUse9" id="adultMedicationsUse9">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="col-md-12 col-sm-4 col-xs-12">
                                                <textarea type="text" class="form-control input-md" rows="1" readonly cols="5" name="adultModificationCarePlan9" id="adultModificationCarePlan9"><?php echo $adultModificationCarePlan9; ?></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row hidecol_6 hide">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="col-md-6 control-label labelcount" for="adultMedicationsUse10">10</label>
                                            <div class="col-md-6 col-sm-4 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse10; ?>" readonly name="adultMedicationsUse10" id="adultMedicationsUse10">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="col-md-12 col-sm-4 col-xs-12">
                                                <textarea type="text" class="form-control input-md" rows="1" readonly cols="5" name="adultModificationCarePlan10" id="adultModificationCarePlan10"><?php echo $adultModificationCarePlan10; ?></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- ----------------------------Show More-------------------------------------- -->
                                <div class="row showLess_1 hide">
                                    <div class="col-md-6 form-group justify-content-center" style="width: 164px;margin-left: 446px;">
                                        <input type="button" class="form-control input-md showMore_10_15" value="show More Rows">
                                    </div>
                                    <div class="col-md-6 form-group justify-content-center" style="width: 160px;margin-left: -4px;">
                                        <input type="button" class="form-control input-md showLess_10_6" value="Show Less Rows">
                                    </div>
                                </div>
                                <!-- ------------------------------------------------------------------ -->
                                <div class="row hidecol_10 hide">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="col-md-6 control-label labelcount" for="adultMedicationsUse10">11</label>
                                            <div class="col-md-6 col-sm-4 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse11; ?>" readonly name="adultMedicationsUse11" id="adultMedicationsUse11">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="col-md-12 col-sm-4 col-xs-12">
                                                <textarea type="text" class="form-control input-md" rows="1" readonly cols="5" name="adultModificationCarePlan11" id="adultModificationCarePlan11"><?php echo $adultModificationCarePlan11; ?></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row hidecol_10 hide">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="col-md-6 control-label labelcount" for="adultMedicationsUse10">12</label>
                                            <div class="col-md-6 col-sm-4 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse12; ?>" readonly name="adultMedicationsUse12" id="adultMedicationsUse12">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="col-md-12 col-sm-4 col-xs-12">
                                                <textarea type="text" class="form-control input-md" rows="1" readonly cols="5" name="adultModificationCarePlan12" id="adultModificationCarePlan12"><?php echo $adultModificationCarePlan12; ?></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row  hidecol_10 hide">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="col-md-6 control-label labelcount" for="adultMedicationsUse13">13</label>
                                            <div class="col-md-6 col-sm-4 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse13; ?>" readonly name="adultMedicationsUse13" id="adultMedicationsUse13">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="col-md-12 col-sm-4 col-xs-12">
                                                <textarea type="text" class="form-control input-md" rows="1" readonly cols="5" name="adultModificationCarePlan13" id="adultModificationCarePlan13"><?php echo $adultModificationCarePlan13; ?></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row  hidecol_10 hide">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="col-md-6 control-label labelcount" for="adultMedicationsUse14">14</label>
                                            <div class="col-md-6 col-sm-4 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse14; ?>" readonly name="adultMedicationsUse14" id="adultMedicationsUse14">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="col-md-12 col-sm-4 col-xs-12">
                                                <textarea type="text" class="form-control input-md" rows="1" readonly cols="5" name="adultModificationCarePlan14" id="adultModificationCarePlan14"><?php echo $adultModificationCarePlan14; ?></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row  hidecol_10 hide">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="col-md-6 control-label labelcount" for="adultMedicationsUse15">15</label>
                                            <div class="col-md-6 col-sm-4 col-xs-12">
                                                <input type="text" class="form-control input-md" value="<?php echo $adultMedicationsUse15; ?>" readonly name="adultMedicationsUse15" id="adultMedicationsUse15">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="col-md-12 col-sm-4 col-xs-12">
                                                <textarea type="text" class="form-control input-md" rows="1" readonly cols="5" name="adultModificationCarePlan15" id="adultModificationCarePlan15"><?php echo $adultModificationCarePlan15; ?></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- ----------------------------Show Less-------------------------------------- -->
                                <div class="row showLess_2 hide">
                                    <div class="col-md-12 form-group justify-content-center" style="width: 168px;margin: 7px;padding-left: 23px;margin-left: 503px;margin-top: -6px;">
                                        <input type="button" class="form-control input-md showLess_15_10" value="Show Less Rows">
                                    </div>

                                </div>
                                <!-- ------------------------------------------------------------------ -->
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-3 flex-end">
                    <button class="button btn-navigate-form-step button-prev" type="button" step_number="3">Prev</button>
                    <button class="button next btn-navigate-form-step" type="button" step_number="5">Next</button>
                </div>
            </section>

            <!-- Step 5 Content, default hidden on page load. -->
            <section id="step-5" class="form-step d-none pt-0" data-parsley-validate>
                <div class="row margin_bottom_ten w-full" style="margin: 10px auto;">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group m-0">
                                <label class="control-label" for=""><b>Student Comment </b></label>
                            </div>
                        </div>
                        <div class="col-md-12 text-left" style="margin-top: 5px;">
                            <div class="form-group">
                                <?php //if ($studentcomments != '') { ?>
                                    <div class="col-md-12 col-sm-4 col-xs-12">
                                        <textarea type="text" class="form-control input-md" name="studentcomments" id="studentcomments" rows="5" readonly><?php echo $studentcomments; ?></textarea>

                                    </div>
                                <?php //} else {
                                ?>
                                    <!-- <div class="col-md-12 col-sm-4 col-xs-12">
                                        <textarea type="text" class="form-control input-md" name="studentcomments" id="studentcomments" rows="5" readonly><?php echo $studentcomments; ?></textarea>

                                    </div> -->
                                <?php //} ?>
                            </div>
                        </div>
                    </div>
                    <!-- school comments 21042021-->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group m-0">
                                <label class="control-label" for=""><b>School Comment </b></label>
                            </div>
                        </div>
                        <div class="col-md-12 text-left" style="margin-top: 5px;">
                            <div class="form-group">
                                <?php //if ($school_comments != '') { ?>
                                    <div class="col-md-12 col-sm-4 col-xs-12">
                                        <textarea type="text" class="form-control input-md" name="school_comments" id="school_comments" rows="5" readonly><?php echo $school_comments; ?></textarea>
                                    </div>
                                <?php //} else {
                                ?>
                                    <!-- <div class="col-md-12 col-sm-4 col-xs-12">
                                        <textarea type="text" class="form-control input-md" name="school_comments" id="school_comments" rows="5" readonly><?php echo $school_comments; ?></textarea>
                                    </div> -->
                                <?php //} ?>
                            </div>
                        </div>
                    </div>
                    <!-- school comments 21042021-->
                    <!-- clinician comments 21042021-->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group m-0">
                                <label class="control-label" for=""><b>Clinician Comment </b></label>
                            </div>
                        </div>
                        <div class="col-md-12 text-left">
                            <div class="form-group">
                                <div class="col-md-12">
                                    <textarea name="clinician_comments" rows="5" id="clinician_comments" class="form-control"><?php echo $clinician_comments; ?></textarea>

                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- clinician comments 21042021-->
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group" style="display: flex;margin-left: 0;">
                            <button class="button btn-navigate-form-step button-prev" type="button" step_number="4">Prev</button>
                            <!-- <label class="col-md-2 control-label"></label> -->
                            <!-- <div class="col-md-12 col-sm-12 col-xs-12 flex-end" style="gap: 15px;">
                                <?php if (!$isClinicianSignoff) { ?>
                                    <button id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
                                <?php } ?>
                                <a href="caseStudyList.html" class="btn btn-default">Cancel</a>
                            </div> -->
                            <div class="col-md-12 col-sm-12 col-xs-12 flex-end">
                                <?php if ($view != 'V') { ?>
                                    <button id="btnSubmit" name="btnSubmit" class="btn btn-success mr-15">Save</button>
                                <?php } ?>
                                <?php if ($IsMobile) { ?>
                                    <a type="button" href="<?php echo BASE_PATH ?>/webRedirect.html?status=Cancel&type=adult" class="btn btn-default">Cancel</a>
                                <?php } else { ?>
                                    <a href="caseStudyList.html" class="btn btn-default">Cancel</a>
                                <?php } ?>

                            </div>
                        </div>
                    </div>
                </div>
                <!-- <div class="mt-3 flex-end" style="">
                    <button class="button btn-navigate-form-step button-prev" type="button" step_number="4">Prev</button>
                    <button class="button submit-btn" type="submit">Save</button>
                </div> -->
            </section>

        </form>

    </div>

    <?php include('includes/footer.php'); ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script src="https://cdn.ckeditor.com/ckeditor5/35.0.1/classic/ckeditor.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>


    <script>
        $('.next').on('click', function() {
            event.preventDefault();

            var isValid = $('#step-1').parsley().validate();

            if (!isValid) {
                return false;
            }
        });
        /**
         * Define a function to navigate betweens form steps.
         * It accepts one parameter. That is - step number.
         */
        const navigateToFormStep = (stepNumber) => {
            /**
             * Hide all form steps.
             */
            document.querySelectorAll(".form-step").forEach((formStepElement) => {
                formStepElement.classList.add("d-none");
            });
            /**
             * Mark all form steps as unfinished.
             */
            document.querySelectorAll(".form-stepper-list").forEach((formStepHeader) => {
                formStepHeader.classList.add("form-stepper-unfinished");
                formStepHeader.classList.remove("form-stepper-active", "form-stepper-completed");
            });
            /**
             * Show the current form step (as passed to the function).
             */
            document.querySelector("#step-" + stepNumber).classList.remove("d-none");
            /**
             * Select the form step circle (progress bar).
             */
            const formStepCircle = document.querySelector('li[step="' + stepNumber + '"]');
            /**
             * Mark the current form step as active.
             */
            formStepCircle.classList.remove("form-stepper-unfinished", "form-stepper-completed");
            formStepCircle.classList.add("form-stepper-active");
            /**
             * Loop through each form step circles.
             * This loop will continue up to the current step number.
             * Example: If the current step is 3,
             * then the loop will perform operations for step 1 and 2.
             */
            for (let index = 0; index < stepNumber; index++) {
                /**
                 * Select the form step circle (progress bar).
                 */
                const formStepCircle = document.querySelector('li[step="' + index + '"]');
                /**
                 * Check if the element exist. If yes, then proceed.
                 */
                if (formStepCircle) {
                    /**
                     * Mark the form step as completed.
                     */
                    formStepCircle.classList.remove("form-stepper-unfinished", "form-stepper-active");
                    formStepCircle.classList.add("form-stepper-completed");
                }
            }
        };
        /**
         * Select all form navigation buttons, and loop through them.
         */
        // document.querySelectorAll(".btn-navigate-form-step").forEach((formNavigationBtn) => {
        //     /**
        //      * Add a click event listener to the button.
        //      */
        //     formNavigationBtn.addEventListener("click", () => {
        //         /**
        //          * Get the value of the step.
        //          */
        //         const stepNumber = parseInt(formNavigationBtn.getAttribute("step_number"));
        //         /**
        //          * Call the function to navigate to the target form step.
        //          */
        //         navigateToFormStep(stepNumber);
        //     });
        // });

        document.querySelectorAll(".btn-navigate-form-step").forEach((formNavigationBtn) => {
            formNavigationBtn.addEventListener("click", () => {
                const stepNumber = parseInt(formNavigationBtn.getAttribute("step_number"));

                if (stepNumber === 2) { // Check if navigating to the second step
                    const isValidStep1 = $('#step-1').parsley().validate(); // Trigger validation for step 1

                    if (!isValidStep1) {
                        return false;
                    }
                }

                if (stepNumber === 3) { // Check if navigating to the second step
                    const isValidStep3 = $('#step-3').parsley().validate(); // Trigger validation for step 1

                    if (!isValidStep3) {
                        return false;
                    }
                }

                if (stepNumber === 4) { // Check if navigating to the second step
                    const isValidStep4 = $('#step-4').parsley().validate(); // Trigger validation for step 1

                    if (!isValidStep4) {
                        return false;
                    }
                }

                if (stepNumber === 5) { // Check if navigating to the second step
                    const isValidStep5 = $('#step-5').parsley().validate(); // Trigger validation for step 1

                    if (!isValidStep5) {
                        return false;
                    }
                }

                // Proceed to navigate to the desired step if validation passes or for other steps
                navigateToFormStep(stepNumber);
            });
        });

        $('.form-stepper-list').click(function() {
            stepId = $(this).attr('step');
            stepNextId = $('.form-stepper-active').attr('step');
            // if (stepId < stepNextId)
            const isValidSteps = $('#step-' + stepNextId).parsley().validate(); // Trigger validation for step 1

            if (!isValidSteps) {
                return false;
            }
            navigateToFormStep(stepId);
        });
    </script>

    <!-- collapsible start -->
    <script>
        const collapsibles = document.querySelectorAll('.collapsible');

        collapsibles.forEach(collapsible => {
            collapsible.addEventListener('click', () => {
                const content = collapsible.nextElementSibling;
                const icon = collapsible.querySelector('i.fa-chevron-down');

                // Check if the content is currently visible
                const isActive = content.classList.contains('active');

                // Close all content sections and reset arrows
                document.querySelectorAll('.content').forEach(item => {
                    item.classList.remove('active');
                });

                document.querySelectorAll('i.fa-chevron-down').forEach(item => {
                    item.classList.remove('rotate');
                });

                // Toggle the content's active state and arrow rotation
                content.classList.toggle('active', !isActive);
                icon.classList.toggle('rotate', !isActive);
            });
        });
    </script>
    <!-- collapsible end -->



    <script type="text/javascript">
        $(".select2_single").select2();
        $('#select2-cborotation-container').addClass('required-select2');

        $(window).load(function() {

            $('#formCaseStudy').parsley().on('field:validated', function() {
                    var ok = $('.parsley-error').length === 0;
                })
                .on('form:submit', function() {
                    ShowProgressAnimation();
                    return true; // Don't submit form for this demo
                });

            $('#caseStudydate').datetimepicker({
                format: 'MM/DD/YYYY'
            });
            $('#ClinicianDate').datetimepicker({
                format: 'MM/DD/YYYY'
            });
            $('#schoolDate').datetimepicker({
                format: 'MM/DD/YYYY'
            });
            $('#adultAdmissionDate').datetimepicker({
                format: 'MM/DD/YYYY'
            });


        });

        $('.showMore_6_10').click(function() {

            $('.hidecol_6').removeClass('hide');
            $(this).addClass('hide');
            $('.showLess_1').removeClass('hide');
        });
        $('.showLess_10_6').click(function() {
            $('.hidecol_6').addClass('hide');
            $('.showMore_6_10').removeClass('hide');
            $('.showLess_1').addClass('hide');
        });

        $('.showMore_10_15').click(function() {
            $('.hidecol_6, .hidecol_10').removeClass('hide');
            $('.showLess_1').addClass('hide');
            $('.showLess_2').removeClass('hide');
            $('.showLess_15_10').removeClass('hide');
        });
        $('.showLess_15_10').click(function() {

            $('.hidecol_10').addClass('hide');
            $('.showLess_1').removeClass('hide');
            $('.showLess_10').removeClass('hide');
            $(this).addClass('hide');
        });
    </script>
</body>

</html>