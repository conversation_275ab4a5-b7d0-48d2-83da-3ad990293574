header {
    text-align: center;
    margin-bottom: 30px;
}

header h1 {
    color: white;
    font-size: 2.5rem;
    font-weight: 300;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Upload Section */
.upload-section {
    margin-bottom: 30px;
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    width: 30%;
    /* height: 320px; */
}

.upload-area {
    background: white;
    /* border: 3px dashed #ddd; */
    border-radius: 12px;
    /* padding: 60px 20px; */
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    height: 100%;
}

.upload-area:hover {
    border-color: #5cb85c;
    background: #f8f9ff;
}

.upload-area.dragover {
    border-color: #5cb85c;
    background: #f0f4ff;
    transform: scale(1.02);
}

.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.upload-icon {
    width: 48px;
    height: 48px;
    color: #5cb85c;
}

.upload-content p {
    font-size: 1.2rem;
    font-weight: 500;
    color: #333;
}

.file-types {
    color: #666;
    font-size: 0.9rem;
}

/* Cropper Section */
/*  */

.controls {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    align-items: center;
}

.control-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.control-group label {
    font-weight: 600;
    color: #555;
    white-space: nowrap;
}

.mode-buttons {
    display: flex;
    gap: 5px;
}

.mode-btn {
    padding: 8px 16px;
    border: 2px solid #ddd;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
}

.mode-btn:hover {
    border-color: #5cb85c;
}

.mode-btn.active {
    background: #5cb85c;
    color: white;
    border-color: #5cb85c;
}

.zoom-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.image-container:hover {
    overflow: hidden;
} 

.zoom-btn {
    width: 32px;
    height: 32px;
    min-width: 32px;
    min-height: 32px;
    border: 2px solid #ddd;
    background: white;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 18px;
    transition: all 0.2s ease;
}

.zoom-btn:hover {
    border-color: #5cb85c;
    color: #5cb85c;
}

.zoom-level {
    min-width: 50px;
    text-align: center;
    font-weight: 600;
    color: #555;
}

.zoom-slider {
    width: 120px;
    height: 6px;
    border-radius: 3px;
    background: #ddd;
    outline: none;
    cursor: pointer;
}

.zoom-slider::-webkit-slider-thumb {
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #5cb85c;
    cursor: pointer;
}

.action-btn {
    padding: 10px 20px;
    border: 2px solid #ddd;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.2s ease;
}

.action-btn:hover {
    border-color: #5cb85c;
    color: #5cb85c;
}

.action-btn.primary {
    background: #5cb85c;
    color: white;
    border-color: #5cb85c;
}

.action-btn.primary:hover {
    background: #5cb85c;
}

/* Image Container */



/* Add these new styles for image dragging */
.image-container.dragging-image {
    cursor: grabbing;
}

.image-container.dragging-image #previewImage {
    transition: none; /* Disable transition during drag for smoother movement */
}

/* Background removal toggle button */
#bgRemovalToggle {
    background: white;
    color: #555;
    border: 2px solid #ddd;
    transition: all 0.2s ease;
}

#bgRemovalToggle:hover {
    border-color: #5cb85c;
    color: #5cb85c;
}

#bgRemovalToggle.active {
    background: #5cb85c;
    color: white;
    border-color: #5cb85c;
}

/* Ensure crop selection stays above the image */
.crop-selection {
    position: absolute;
    border: 2px solid #5cb85c;
    background: rgba(102, 126, 234, 0.1);
    cursor: move;
    pointer-events: all;
    min-width: 50px;
    min-height: 50px;
    z-index: 10; /* Ensure crop selection is above the image */
    box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5);
}

/* Remove the duplicate box-shadow in the ::before pseudo-element */
.crop-selection::before {
    content: none;
}

/* Remove scrollbar styles */
.image-container:hover {
    overflow: hidden; /* Hide scrollbars */
}

/* Crop Overlay */
.crop-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.crop-selection::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5);
    pointer-events: none;
}

/* Resize handles removed - only dragging allowed */

/* Preview Section */
/* .preview-section {
    text-align: center;
    width: 50%;
}

.preview-section h3 {
    margin-bottom: 15px;
    margin-top: 0;
    text-align: left;
    color: #555;
} */

/* #previewCanvas {
    border: 2px solid #ddd;
    border-radius: 8px;
    width: 100%;
    max-width: 400px;
    max-height: 200px;
} */


.cropper-section {
    width: 70%;
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Image Container */
.image-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px; 
    background: #f8f9fa;
    border-radius: 8px;
    overflow: hidden;
    width: 55%;
}

/* Crop Overlay */
.crop-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.crop-selection {
    position: absolute;
    border: 2px solid #5cb85c;
    background: rgba(102, 126, 234, 0.1);
    cursor: move;
    pointer-events: all;
    min-width: 50px;
    min-height: 50px;
    z-index: 10;
    box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5);
    /* Add these properties to maintain aspect ratio */
    box-sizing: border-box;
    transform-origin: center center;
}

/* Image Style */
#previewImage {
    max-width: 100%;
    max-height: 100%;
    display: block;
    transition: transform 0.2s ease;
    cursor: move;
    transform-origin: center center;
    user-select: none;
    -webkit-user-drag: none;
}
/* #imageContainer {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    position: relative !important;
    overflow: hidden !important;
}

#previewImage {
    transform-origin: center center !important;
    position: relative !important;
    max-width: 100%;
    max-height: 100%;
} */


 /* Cropped image state styling */
        .upload-area.cropped-state {
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f4f8 100%);
            border: 2px solid #667eea;
        }

        .upload-area.cropped-state:hover {
            background: linear-gradient(135deg, #f0f4ff 0%, #e0f0f6 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
        }

        .modal-lg {
            width: 90%;
            max-width: 1200px;
        }

        .cropped-preview {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 10px;
        }

        .success-message {
            color: #01A750;
            margin-top: 5px;
            font-weight: bold;
        }

        /* Make sure the cropper is visible in the modal */
        #cropperModal #cropperSection {
            display: block !important;
        }
        
        .upload-section {
            margin-bottom: 30px;
            border: none !important;
            background: white;
            border-radius: 12px;
            padding: 22px !important;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            width: 53%;
        }
        
        @media screen and (max-width: 500px) {
			.formSubHeading{
				font-size: 16px;
				margin-bottom: 12px;
				padding-bottom: 0;
			}
			.breadcrumb-bg{
				margin-bottom: 5px;
			}
		}
/* Responsive */
@media (max-width: 768px) {
    .controls {
        flex-direction: column;
        align-items: stretch;
    }

    .control-group {
        justify-content: space-between;
    }

    header h1 {
        font-size: 2rem;
    }
}
