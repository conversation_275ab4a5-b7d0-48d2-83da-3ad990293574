
<?php

// Include necessary files and classes
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsSchool.php');
include('../class/clsStudent.php');
include('../class/clsCaseStudy.php');
include('../setRequest.php');
include('../class/clsSendEmails.php');
include('../class/clsSMTPSettings.php');
include_once('../class/clsLogger.php');

// Initialize variables
$retcaseStudyId = 0;
$type = $_POST['type'] ?? '';
$caseStudyId = $_POST['caseStudyId'] ?? 0;
$studentId = $_POST['studentId'] ?? 0;
$sectionTitle = $_POST['sectionTitle'] ?? '';
$IsMobile = $_POST['IsMobile'] ?? 0;
$currentDate = date('Y-m-d H:i:s');
$status = 'Error';
$url = '';

// Create a new CaseStudy object
$objCaseStudy = new clsCaseStudy();
$objCaseStudy->createdBy = $studentId;
$objCaseStudy->updatedBy = $studentId;
$objCaseStudy->createdDate = $currentDate;
$objCaseStudy->updatedDate = $currentDate;

// Process PACR case study type
if ($type == 'PACR' && $sectionTitle != '') {
    switch ($sectionTitle) {
        case 'details':
            $TimeZone = $_SESSION["loggedStudentSchoolTimeZone"] ?? '';
            $caseStudydate = GetDateStringInServerFormat($_POST['caseStudydate'] ?? '');
            $caseStudydate = str_replace('00:00:00', '12:00 PM', $caseStudydate);
            $caseStudydate = date('Y-m-d H:i', strtotime($caseStudydate));
            $rotationId = DecodeQueryData($_POST['cborotation'] ?? '');
            $hospitalId = $_POST['cbohospitalsites'] ?? 0;
            $hospitalSiteUnitId = $_POST['cbohospitalsiteunits'] ?? 0;
            $pacrCohort = $_POST['pacrCohort'] ?? '';
            $pacrWeek = $_POST['pacrWeek'] ?? '';

            $objCaseStudy->schoolId = $currentSchoolId;
            $objCaseStudy->studentId = $studentId;
            $objCaseStudy->rotationId = $rotationId;
            $objCaseStudy->hospitalSiteId = $hospitalId;
            $objCaseStudy->hospitalSiteUnitId = $hospitalSiteUnitId;
            $objCaseStudy->caseStudydate = $caseStudydate;
            $objCaseStudy->type = $type;
            $objCaseStudy->pacrCohort = $pacrCohort;
            $objCaseStudy->pacrWeek = $pacrWeek;

            $retcaseStudyId = $objCaseStudy->SaveCaseStudyPACRDetailsSection($caseStudyId);
            if ($retcaseStudyId > 0) {
                $status = 'Success';
            }
            break;

        case 'demographics':
            $objCaseStudy->pacrAdmissionDate = $_POST['pacrAdmissionDate'] ?? '';
            $objCaseStudy->pacrPtAge = $_POST['pacrPtAge'] ?? '';
            $objCaseStudy->pacrSex = $_POST['pacrSex'] ?? '';
            $objCaseStudy->pacrCC = $_POST['pacrCC'] ?? '';
            $objCaseStudy->pacrDx = $_POST['pacrDx'] ?? '';
            $objCaseStudy->pacrHx = $_POST['pacrHx'] ?? '';
            $objCaseStudy->pacrPhysicalExam = $_POST['pacrPhysicalExam'] ?? '';
            $objCaseStudy->pacrBreathSounds = $_POST['pacrBreathSounds'] ?? '';

            $retcaseStudyId = $objCaseStudy->SaveCaseStudyPACRDemographicsSection($caseStudyId);
            if ($retcaseStudyId > 0) {
                $status = 'Success';
            }
            break;

        case 'vitals':
            // Collect vitals data into an array
            $VitalsArray = [
                'pacrTime1' => GetDateStringInServerFormat($_POST['pacrTime1'] ?? ''),
                'pacrTime2' => GetDateStringInServerFormat($_POST['pacrTime2'] ?? ''),
                'pacrTime3' => GetDateStringInServerFormat($_POST['pacrTime3'] ?? ''),
                'pacrHR1' => $_POST['pacrHR1'] ?? '',
                'pacrHR2' => $_POST['pacrHR2'] ?? '',
                'pacrHR3' => $_POST['pacrHR3'] ?? '',
                'pacrBP1' => $_POST['pacrBP1'] ?? '',
                'pacrBP2' => $_POST['pacrBP2'] ?? '',
                'pacrBP3' => $_POST['pacrBP3'] ?? '',
                'pacrRR1' => $_POST['pacrRR1'] ?? '',
                'pacrRR2' => $_POST['pacrRR2'] ?? '',
                'pacrRR3' => $_POST['pacrRR3'] ?? '',
                'pacrTemp1' => $_POST['pacrTemp1'] ?? '',
                'pacrTemp2' => $_POST['pacrTemp2'] ?? '',
                'pacrTemp3' => $_POST['pacrTemp3'] ?? '',
                'pacrSpO21' => $_POST['pacrSpO21'] ?? '',
                'pacrSpO22' => $_POST['pacrSpO22'] ?? '',
                'pacrSpO23' => $_POST['pacrSpO23'] ?? '',
                'pacrFIO21' => $_POST['pacrFIO21'] ?? '',
                'pacrFIO22' => $_POST['pacrFIO22'] ?? '',
                'pacrFIO23' => $_POST['pacrFIO23'] ?? '',
                'pacrInerpret' => $_POST['pacrInerpret'] ?? '',
                'pacrMapFormula' => $_POST['pacrMapFormula'] ?? '',
                'pacrMap1BP' => $_POST['pacrMap1BP'] ?? '',
                'pacrMap2BP' => $_POST['pacrMap2BP'] ?? '',
                'pacrMap3BP' => $_POST['pacrMap3BP'] ?? '',
                'visi1Check' => $_POST['visi1Check'] ?? 0,
                'visi2Check' => $_POST['visi2Check'] ?? 0,
                'visi3Check' => $_POST['visi3Check'] ?? 0,
                'pacrMapElevated' => $_POST['pacrMapElevated'] ?? '',
                'pacrMapLow' => $_POST['pacrMapLow'] ?? '',
            ];
            $VitalsArrayList = json_encode($VitalsArray);

            $objDB = new clsDB();
            $objDB->UpdateSingleColumnValueToTable('casestudypacr', 'pacrVitals', $VitalsArrayList, 'caseStudyPacrId', $caseStudyId);
            unset($objDB);
            $retcaseStudyId = $caseStudyId;
            if ($retcaseStudyId > 0) {
                $status = 'Success';
            }
            break;

        case 'cbc':
            // Collect CBG data into an array
            $CBGArray = [
                'pacrCBGDate' => $_POST['pacrCBGDate'] ?? '',
                'pacrNa' => $_POST['pacrNa'] ?? '',
                'pacrK' => $_POST['pacrK'] ?? '',
                'pacrCl' => $_POST['pacrCl'] ?? '',
                'pacrCa' => $_POST['pacrCa'] ?? '',
                'pacrBun' => $_POST['pacrBun'] ?? '',
                'pacrGlucose' => $_POST['pacrGlucose'] ?? '',
                'pacrPtt' => $_POST['pacrPtt'] ?? '',
                'pacrAlbumin' => $_POST['pacrAlbumin'] ?? '',
                'pacrSensitivity' => $_POST['pacrSensitivity'] ?? '',
                'pacrAfb' => $_POST['pacrAfb'] ?? '',
                'pacrCreatine' => $_POST['pacrCreatine'] ?? '',
                'pacrRbc' => $_POST['pacrRbc'] ?? '',
                'pacrHb' => $_POST['pacrHb'] ?? '',
                'pacrHct' => $_POST['pacrHct'] ?? '',
                'pacrWbc' => $_POST['pacrWbc'] ?? '',
                'pacrBaso' => $_POST['pacrBaso'] ?? '',
                'pacrLymph' => $_POST['pacrLymph'] ?? '',
                'pacrEosin' => $_POST['pacrEosin'] ?? '',
                'pacrBnp' => $_POST['pacrBnp'] ?? '',
                'pacrTropinin' => $_POST['pacrTropinin'] ?? '',
                'pacrLactate' => $_POST['pacrLactate'] ?? '',
                'pacrIntake' => $_POST['pacrIntake'] ?? '',
                'pacrTotalCO2' => $_POST['pacrTotalCO2'] ?? '',
                'pacrPh' => $_POST['pacrPh'] ?? '',
                'pacrPaCO2' => $_POST['pacrPaCO2'] ?? '',
                'pacrPaO2' => $_POST['pacrPaO2'] ?? '',
                'pacrHCO3' => $_POST['pacrHCO3'] ?? '',
                'pacrBE' => $_POST['pacrBE'] ?? '',
                'pacrCoHb' => $_POST['pacrCoHb'] ?? '',
                'pacrSaO2' => $_POST['pacrSaO2'] ?? '',
                'pacrFIO2' => $_POST['pacrFIO2'] ?? '',
            ];
            $CBGArrayList = json_encode($CBGArray);
            $pacrCBCCheckValues = json_encode($_POST['pacrCBCCheckValues'] ?? []);

            $objCaseStudy->pacrCBCDetails = $CBGArrayList;
            $objCaseStudy->pacrCBCCheckValues = $pacrCBCCheckValues;
            $objCaseStudy->pacrInterpretPatient = $_POST['pacrInterpretPatient'] ?? '';
            $objCaseStudy->pacrInterpretABGStatus = $_POST['pacrInterpretABGStatus'] ?? '';
            $objCaseStudy->pacrAaFormula = $_POST['pacrAaFormula'] ?? '';
            $objCaseStudy->pacrAaNormalvalue = $_POST['pacrAaNormalvalue'] ?? '';
            $objCaseStudy->pacrAaIsNormal = $_POST['pacrAaCheck'] ?? 0;
            $objCaseStudy->pacrAaAbnormal = $_POST['pacrAaAbnormal'] ?? '';
            $objCaseStudy->pacrCaO2 = $_POST['pacrCaO2'] ?? '';
            $objCaseStudy->pacrNormalCaO2 = $_POST['pacrNormalCaO2'] ?? '';
            $objCaseStudy->pacrCaO2Isnormal = $_POST['pacrCaO2Check'] ?? 0;
            $objCaseStudy->pacrCaO2Abnormal = $_POST['pacrCaO2Abnormal'] ?? '';
            $objCaseStudy->pacrProcedures = $_POST['pacrProcedures'] ?? '';
            $objCaseStudy->pacrPFTList = $_POST['pacrPFTList'] ?? '';
            $objCaseStudy->pacrCXRList = $_POST['pacrCXRList'] ?? '';

            $retcaseStudyId = $objCaseStudy->SaveCaseStudyPACRCbcSection($caseStudyId);
            if ($retcaseStudyId > 0) {
                $status = 'Success';
            }
            break;

        case 'medications':
            // Collect Hemodynamics data into an array
            $HemodynamicsArray = [
                'pacrSvO2' => $_POST['pacrSvO2'] ?? '',
                'pacrCVP' => $_POST['pacrCVP'] ?? '',
                'pacrCO' => $_POST['pacrCO'] ?? '',
                'pacrPCWP' => $_POST['pacrPCWP'] ?? '',
                'pacrPAP' => $_POST['pacrPAP'] ?? '',
                'pacrICP' => $_POST['pacrICP'] ?? '',
            ];
            $HemodynamicsArrayList = json_encode($HemodynamicsArray);
            $pacrCBCCheckValues = json_encode($_POST['pacrCBCCheckValues'] ?? []); // This seems to be duplicated from 'cbc' section. Verify if intended.

            $objCaseStudy->pacrMedications = json_encode($_POST['pacrMedications'] ?? []);
            $objCaseStudy->pacrClassifications = json_encode($_POST['pacrClassifications'] ?? []);
            $objCaseStudy->pacrModeOfctions = json_encode($_POST['pacrModeOfctions'] ?? []);
            $objCaseStudy->pacrDosage = json_encode($_POST['pacrDosage'] ?? []);
            $objCaseStudy->pacrRespiratoryOrders = $_POST['pacrRespiratoryOrders'] ?? '';
            $objCaseStudy->pacrIndications = $_POST['pacrIndications'] ?? '';
            $objCaseStudy->pacrGoals = $_POST['pacrGoals'] ?? '';
            $objCaseStudy->pacrRTOrder = $_POST['pacrRTOrder'] ?? '';
            $objCaseStudy->pacrVentilator = $_POST['pacrVentilator'] ?? '';
            $objCaseStudy->pacrABGResults = $_POST['pacrABGResults'] ?? '';
            $objCaseStudy->pacrHemodynamicsList = $HemodynamicsArrayList;
            $objCaseStudy->pacrHemodynamicCheckvalues = $_POST['pacrHemodynamicCheckvalues'] ?? ''; // This was empty in original code, assigned from POST
            $objCaseStudy->pacrCBCCheckValues = $pacrCBCCheckValues;
            $objCaseStudy->pacrSummery = $_POST['pacrSummery'] ?? '';

            $retcaseStudyId = $objCaseStudy->SaveCaseStudyPACRMedicationsSection($caseStudyId);
            if ($retcaseStudyId > 0) {
                $status = 'Success';
            }
            break;

        case 'comments':
            $studentcomments = $_POST['studentcomments'] ?? '';
            $objDB = new clsDB();
            $objDB->UpdateSingleColumnValueToTable('casestudypacr', 'studentcomments', $studentcomments, 'caseStudyPacrId', $caseStudyId);
            $objDB->UpdateSingleColumnValueToTable('casestudypacr', 'status', '1', 'caseStudyPacrId', $caseStudyId);
            unset($objDB);

            // For Clinician Reviews
            $rsClinicianReviews = $objCaseStudy->GetMaxClinicianReviews($caseStudyId);
            if ($rsClinicianReviews) { // Check if data exists
                $reviewClinicianName = $rsClinicianReviews['clinicianName'];
                $clinicianId = $rsClinicianReviews['clinicianId'];
                $reviewDate = date('m/d/Y', strtotime($rsClinicianReviews['reviewDate']));

                $objStudent = new clsStudent();
                $rowStudent = $objStudent->GetStudentDetails($studentId);
                $fullName = '';
                if ($rowStudent) {
                    $firstName = ($rowStudent['firstName']);
                    $lastName = ($rowStudent['lastName']);
                    $fullName = $firstName . ' ' . $lastName;
                }
                unset($objStudent);

                $objDB = new clsDB();
                $clinicianEmail = $objDB->GetSingleColumnValueFromTable('clinician', 'email', 'clinicianId', $clinicianId);
                unset($objDB);

                // Send mail to clinician
                $objSendEmails = new clsSendEmails($currentSchoolId);
                // Ensure $currenSchoolLogoImagePath and $currenschoolDisplayname are defined or accessed globally
                $objSendEmails->SendReviewMailToClinician($clinicianEmail, $reviewClinicianName, $fullName, $reviewDate, $currenSchoolLogoImagePath ?? '', $currenschoolDisplayname ?? '');
                unset($objSendEmails);
            }

            $retcaseStudyId = $caseStudyId;
            if ($retcaseStudyId > 0) {
                $status = 'Success';
                if ($caseStudyId == 0) { // New case study
                    $url = $IsMobile ? BASE_PATH . '/webRedirect.html?status=Success&type=PACR' : 'caseStudyList.html?caseStudyId=' . EncodeQueryData($caseStudyId) . '&status=Added';
                } else { // Existing case study
                    $url = $IsMobile ? BASE_PATH . '/webRedirect.html?status=Success&type=PACR' : 'caseStudyList.html?status=Updated';
                }
            }
            break;
    }

    if ($status == 'Success') {
        // Audit Log
        $objLog = new clsLogger();
        $action = ($caseStudyId > 0) ? $objLog::EDIT : $objLog::ADD;
        $userType = $objLog::STUDENT;
        $objCaseStudy->saveCaseStudyAuditLog($retcaseStudyId, $type, $studentId, $userType, $action, $IsMobile);
        unset($objLog);
    }
} else if ($type == 'floor' && $sectionTitle != '') {
    switch ($sectionTitle) {
        case 'details':
            $TimeZone = $_SESSION["loggedStudentSchoolTimeZone"] ?? '';
            $caseStudydate = GetDateStringInServerFormat($_POST['caseStudydate'] ?? '');
            $caseStudydate = str_replace('00:00:00', '12:00 PM', $caseStudydate);
            $caseStudydate = date('Y-m-d H:i', strtotime($caseStudydate));
            $rotationId = DecodeQueryData($_POST['cborotation'] ?? '');
            $hospitalId = $_POST['cbohospitalsites'] ?? 0;
            $hospitalSiteUnitId = $_POST['cbohospitalsiteunits'] ?? 0;
            $floorCohort  = ($_POST['floorCohort']);
            $floorAdmissionDate  = ($_POST['floorAdmissionDate']);
            $floorPtAge  = ($_POST['floorPtAge']);
            $floorHt  = ($_POST['floorHt']);
            $floorSex  = ($_POST['floorSex']);
            $floorSmoking  = ($_POST['floorSmoking']);
            $floorChiefComplaint  = ($_POST['floorChiefComplaint']);
            $floorRespiratory  = ($_POST['floorRespiratory']);
            $floorPastMedicalHx  = ($_POST['floorPastMedicalHx']);
            $floorOtherPulmonaryProblems  = ($_POST['floorOtherPulmonaryProblems']);

            $objCaseStudy->schoolId  = $currentSchoolId;
            $objCaseStudy->studentId = $studentId;
            $objCaseStudy->rotationId = $rotationId;
            $objCaseStudy->caseStudydate = $caseStudydate;
            $objCaseStudy->type = $type;
            $objCaseStudy->floorCohort = $floorCohort;
            $objCaseStudy->floorAdmissionDate = $floorAdmissionDate;
            $objCaseStudy->floorPtAge = $floorPtAge;
            $objCaseStudy->floorSex = $floorSex;
            $objCaseStudy->floorHt = $floorHt;

            $objCaseStudy->floorSmoking = $floorSmoking;
            $objCaseStudy->floorChiefComplaint = $floorChiefComplaint;
            $objCaseStudy->floorRespiratory = $floorRespiratory;
            $objCaseStudy->floorPastMedicalHx = $floorPastMedicalHx;
            $objCaseStudy->floorOtherPulmonaryProblems = $floorOtherPulmonaryProblems;

            $retcaseStudyId = $objCaseStudy->SaveCaseStudyFloorDetailsSection($caseStudyId);
            if ($retcaseStudyId > 0) {
                $status = 'Success';
            }
            break;
        case 'vitals':
            $floorHR  = ($_POST['floorHR']);
            $floorSpontRR  = ($_POST['floorSpontRR']);
            $floorBP  = ($_POST['floorBP']);
            $floorTemp  = ($_POST['floorTemp']);
            $floorSpO2  = ($_POST['floorSpO2']);
            $floorIO  = ($_POST['floorIO']);
            $floorBreathSounds  = ($_POST['floorBreathSounds']);
            $floorLevelofActivity  = ($_POST['floorLevelofActivity']);

            $objCaseStudy->floorHR = $floorHR;
            $objCaseStudy->floorSpontRR = $floorSpontRR;
            $objCaseStudy->floorBP = $floorBP;
            $objCaseStudy->floorTemp = $floorTemp;
            $objCaseStudy->floorSpO2 = $floorSpO2;
            $objCaseStudy->floorIO = $floorIO;
            $objCaseStudy->floorBreathSounds = $floorBreathSounds;
            $objCaseStudy->floorLevelofActivity = $floorLevelofActivity;

            $retcaseStudyId = $objCaseStudy->SaveCaseStudyFloorVitalsSection($caseStudyId);
            if ($retcaseStudyId > 0) {
                $status = 'Success';
            }
            break;
        case 'readings':
            $floorNa  = ($_POST['floorNa']);
            $floorK  = ($_POST['floorK']);
            $floorCl  = ($_POST['floorCl']);
            $floorWBC  = ($_POST['floorWBC']);
            $floorHgb  = ($_POST['floorHgb']);
            $floorHct  = ($_POST['floorHct']);

            $floorCO2  = ($_POST['floorCO2']);
            $floorBUN  = ($_POST['floorBUN']);
            $floorGlucose  = ($_POST['floorGlucose']);
            $floorPlatelets  = ($_POST['floorPlatelets']);

            $floorINR  = ($_POST['floorINR']);
            $floorSputumCult  = ($_POST['floorSputumCult']);
            $floorCreatinine  = ($_POST['floorCreatinine']);
            $floorLabInterpretation  = ($_POST['floorLabInterpretation']);
            $floorXRayInterpretation  = ($_POST['floorXRayInterpretation']);
            $floorEKG  = ($_POST['floorEKG']);
            $floorTrachSize  = ($_POST['floorTrachSize']);

            $floorTrachType  = ($_POST['floorTrachType']);
            $floorCuffPressure  = ($_POST['floorCuffPressure']);
            $floorpH  = ($_POST['floorpH']);
            $floorPaCO2  = ($_POST['floorPaCO2']);
            $floorHCO3  = ($_POST['floorHCO3']);

            $floorFVC  = ($_POST['floorFVC']);
            $floorFEF25  = ($_POST['floorFEF25']);
            $floorFEF1  = ($_POST['floorFEF1']);
            $floorPaO2  = ($_POST['floorPaO2']);
            $floorSaO2  = ($_POST['floorSaO2']);
            $floorFiO2  = ($_POST['floorFiO2']);
            $floorPEFR  = ($_POST['floorPEFR']);
            $floorFEV1  = ($_POST['floorFEV1']);
            $floorDateBloodGas  = ($_POST['floorDateBloodGas']);
            $floorLungValues  = ($_POST['floorLungValues']);
            $floorInterpretationABG  = ($_POST['floorInterpretationABG']);

            $floorInterpretationPFT  = ($_POST['floorInterpretationPFT']);
            $floorInterpretationPAO2  = ($_POST['floorInterpretationPAO2']);
            $floorInterpretationAO2  = ($_POST['floorInterpretationAO2']);
            $floorInterpretationCaO2  = ($_POST['floorInterpretationCaO2']);
            $floorInterpretationPFRatio  = ($_POST['floorInterpretationPFRatio']);

            $floorIPAP  = ($_POST['floorIPAP']);
            $floorEPAP  = ($_POST['floorEPAP']);
            $floorRate  = ($_POST['floorRate']);
            $floorFiO2Setting  = ($_POST['floorFiO2Setting']);

            $floorFiO2  = ($_POST['floorFiO2']);
            $floorItime  = ($_POST['floorItime']);

            $floorRise  = ($_POST['floorRise']);
            $floorRamp  = ($_POST['floorRamp']);
            $floorHumidityTemp  = ($_POST['floorHumidityTemp']);
            $floorSuction  = isset($_POST['floorSuction']) ? 'Yes' : 'No';
            $floorCough  = isset($_POST['floorCough']) ? 'Yes' : 'No';
            $floorSputumAmount  = ($_POST['floorSputumAmount']);

            $objCaseStudy->floorNa = $floorNa;
            $objCaseStudy->floorK = $floorK;
            $objCaseStudy->floorCl = $floorCl;
            $objCaseStudy->floorWBC = $floorWBC;
            $objCaseStudy->floorHgb = $floorHgb;
            $objCaseStudy->floorHct = $floorHct;
            $objCaseStudy->floorCO2 = $floorCO2;
            $objCaseStudy->floorBUN = $floorBUN;
            $objCaseStudy->floorGlucose = $floorGlucose;
            $objCaseStudy->floorPlatelets = $floorPlatelets;
            $objCaseStudy->floorINR = $floorINR;
            $objCaseStudy->floorSputumCult = $floorSputumCult;
            $objCaseStudy->floorCreatinine = $floorCreatinine;
            $objCaseStudy->floorLabInterpretation = $floorLabInterpretation;
            $objCaseStudy->floorXRayInterpretation = $floorXRayInterpretation;
            $objCaseStudy->floorEKG = $floorEKG;
            $objCaseStudy->floorTrachSize = $floorTrachSize;
            $objCaseStudy->floorTrachType = $floorTrachType;
            $objCaseStudy->floorCuffPressure = $floorCuffPressure;
            $objCaseStudy->floorpH = $floorpH;
            $objCaseStudy->floorPaCO2 = $floorPaCO2;
            $objCaseStudy->floorHCO3 = $floorHCO3;
            $objCaseStudy->floorFVC = $floorFVC;
            $objCaseStudy->floorFEF25 = $floorFEF25;
            $objCaseStudy->floorFEF1 = $floorFEF1;
            $objCaseStudy->floorPaO2 = $floorPaO2;
            $objCaseStudy->floorSaO2 = $floorSaO2;
            $objCaseStudy->floorFiO2 = $floorFiO2;
            $objCaseStudy->floorPEFR = $floorPEFR;
            $objCaseStudy->floorFEV1 = $floorFEV1;
            $objCaseStudy->floorDateBloodGas = $floorDateBloodGas;
            $objCaseStudy->floorLungValues = $floorLungValues;
            $objCaseStudy->floorInterpretationABG = $floorInterpretationABG;
            $objCaseStudy->floorInterpretationPFT = $floorInterpretationPFT;
            $objCaseStudy->floorInterpretationPAO2 = $floorInterpretationPAO2;
            $objCaseStudy->floorInterpretationAO2 = $floorInterpretationAO2;
            $objCaseStudy->floorInterpretationCaO2 = $floorInterpretationCaO2;
            $objCaseStudy->floorInterpretationPFRatio = $floorInterpretationPFRatio;
            $objCaseStudy->floorIPAP = $floorIPAP;
            $objCaseStudy->floorEPAP = $floorEPAP;
            $objCaseStudy->floorRate = $floorRate;
            $objCaseStudy->floorFiO2Setting = $floorFiO2Setting;
            $objCaseStudy->floorItime = $floorItime;
            $objCaseStudy->floorRise = $floorRise;
            $objCaseStudy->floorRamp = $floorRamp;
            $objCaseStudy->floorHumidityTemp = $floorHumidityTemp;
            $objCaseStudy->floorSuction = $floorSuction;
            $objCaseStudy->floorCough = $floorCough;
            $objCaseStudy->floorSputumAmount = $floorSputumAmount;

            $retcaseStudyId = $objCaseStudy->SaveCaseStudyFloorReadingsSection($caseStudyId);
            if ($retcaseStudyId > 0) {
                $status = 'Success';
            }
            break;
        case 'medications':
            $flooMedicationsUseList  = ($_POST['flooMedicationsUseList']);
            $floorModificationCarePlanList  = ($_POST['floorModificationCarePlanList']);
            $flooMedicationsUseList = json_encode($_POST['flooMedicationsUseList']);
            $floorModificationCarePlanList = json_encode($_POST['floorModificationCarePlanList']);
            $objDB = new clsDB();
            $objDB->UpdateSingleColumnValueToTable('casestudyfloor', 'flooMedicationsUseList', $flooMedicationsUseList, 'caseStudyFloorId', $caseStudyId);
            $objDB->UpdateSingleColumnValueToTable('casestudyfloor', 'floorModificationCarePlanList', $floorModificationCarePlanList, 'caseStudyFloorId', $caseStudyId);

            unset($objDB);
            $retcaseStudyId = $caseStudyId;
            if ($retcaseStudyId > 0) {
                $status = 'Success';
            }
            break;
        case 'comments':
            $studentcomments = $_POST['studentcomments'] ?? '';
            $objDB = new clsDB();
            $objDB->UpdateSingleColumnValueToTable('casestudyfloor', 'studentcomments', $studentcomments, 'caseStudyFloorId', $caseStudyId);
            $objDB->UpdateSingleColumnValueToTable('casestudyfloor', 'status', '1', 'caseStudyFloorId', $caseStudyId);
            unset($objDB);
            $retcaseStudyId = $caseStudyId;
            if ($retcaseStudyId > 0) {
                $status = 'Success';
                if ($caseStudyId == 0) { // New case study
                    $url = $IsMobile ? BASE_PATH . '/webRedirect.html?status=Success&type=floorCaseStudy' : 'caseStudyList.html?caseStudyId=' . EncodeQueryData($caseStudyId) . '&status=Added';
                } else { // Existing case study
                    $url = $IsMobile ? BASE_PATH . '/webRedirect.html?status=Success&type=floorCaseStudy' : 'caseStudyList.html?status=Updated';
                }
            }
    }
    if ($status == 'Success') {
        // Audit Log
        $objLog = new clsLogger();
        $action = ($caseStudyId > 0) ? $objLog::EDIT : $objLog::ADD;
        $userType = $objLog::STUDENT;
        $objCaseStudy->saveCaseStudyAuditLog($retcaseStudyId, $type, $studentId, $userType, $action, $IsMobile);
        unset($objLog);
    }
} else if ($type == 'Adult' && $sectionTitle != '') {
    switch ($sectionTitle) {
        case 'details':
            $TimeZone = $_SESSION["loggedStudentSchoolTimeZone"] ?? '';
            $caseStudydate = GetDateStringInServerFormat($_POST['caseStudydate'] ?? '');
            $caseStudydate = str_replace('00:00:00', '12:00 PM', $caseStudydate);
            $caseStudydate = date('Y-m-d H:i', strtotime($caseStudydate));
            $rotationId = DecodeQueryData($_POST['cborotation'] ?? '');
            $hospitalId = $_POST['cbohospitalsites'] ?? 0;
            $hospitalSiteUnitId = $_POST['cbohospitalsiteunits'] ?? 0;
            $adultCohort  = isset($_POST['adultCohort']) ? $_POST['adultCohort'] : '';
            $adultAdmissionDate  = isset($_POST['adultAdmissionDate']) ? $_POST['adultAdmissionDate'] : '';

            $adultPtAge  = isset($_POST['adultPtAge']) ? $_POST['adultPtAge'] : '';
            $adultSex  = isset($_POST['adultSex']) ? $_POST['adultSex'] : '';
            $adultHt  = isset($_POST['adultHt']) ? $_POST['adultHt'] : '';
            $adultSmoking  = isset($_POST['adultSmoking']) ? $_POST['adultSmoking'] : '';

            $gestationalAOB = isset($_POST['gestationalAOB']) ? $_POST['gestationalAOB'] : '';
            $studentDOB = isset($_POST['studentDOB']) ? GetDateStringInServerFormat($_POST['studentDOB']) : '';
            if (!empty($studentDOB)) {
                $studentDOB = str_replace('00:00:00', '12:00 PM', $studentDOB);
                $studentDOB = date('Y-m-d H:i', strtotime($studentDOB));
            }

            $adultAPGAR  = isset($_POST['adultAPGAR']) ? $_POST['adultAPGAR'] : '';
            $adultRespiratory  = isset($_POST['adultRespiratory']) ? $_POST['adultRespiratory'] : '';
            $adultChiefComplaint  = isset($_POST['adultChiefComplaint']) ? $_POST['adultChiefComplaint'] : '';
            $adultPastMedicalHx  = isset($_POST['adultPastMedicalHx']) ? $_POST['adultPastMedicalHx'] : '';

            $adultMomsPARA  = isset($_POST['adultMomsPARA']) ? $_POST['adultMomsPARA'] : '';
            $adultParentSmokingHx  = isset($_POST['adultParentSmokingHx']) ? $_POST['adultParentSmokingHx'] : '';
            $adultOtherPulmonaryProblems  = isset($_POST['adultOtherPulmonaryProblems']) ? $_POST['adultOtherPulmonaryProblems'] : '';

            $objCaseStudy->schoolId = $currentSchoolId;
            $objCaseStudy->studentId = $studentId;
            $objCaseStudy->rotationId = $rotationId;
            $objCaseStudy->hospitalSiteId = $hospitalId;
            $objCaseStudy->hospitalSiteUnitId = $hospitalSiteUnitId;
            $objCaseStudy->caseStudydate = $caseStudydate;
            $objCaseStudy->type = $type;
            $objCaseStudy->adultCohort = $adultCohort;
            $objCaseStudy->adultAdmissionDate = $adultAdmissionDate;
            $objCaseStudy->adultPtAge = $adultPtAge;
            $objCaseStudy->adultSex = $adultSex;
            $objCaseStudy->adultHt = $adultHt;
            $objCaseStudy->adultSmoking = $adultSmoking;
            $objCaseStudy->gestationalAOB = $gestationalAOB;
            $objCaseStudy->studentDOB = $studentDOB;
            $objCaseStudy->adultAPGAR = $adultAPGAR;
            $objCaseStudy->adultRespiratory = $adultRespiratory;
            $objCaseStudy->adultChiefComplaint = $adultChiefComplaint;
            $objCaseStudy->adultPastMedicalHx = $adultPastMedicalHx;
            $objCaseStudy->adultMomsPARA = $adultMomsPARA;
            $objCaseStudy->adultParentSmokingHx = $adultParentSmokingHx;
            $objCaseStudy->adultOtherPulmonaryProblems = $adultOtherPulmonaryProblems;

            $retcaseStudyId = $objCaseStudy->SaveCaseStudyAdultDetailsSection($caseStudyId);
            if ($retcaseStudyId > 0) {
                $status = 'Success';
            }
            break;
        case 'vitals':
            $adultHR = $_POST['adultHR'] ?? '';
            $adultSpontRR = $_POST['adultSpontRR'] ?? '';
            $adultBP = $_POST['adultBP'] ?? '';
            $adultTemp = $_POST['adultTemp'] ?? '';
            $adultSpO2 = $_POST['adultSpO2'] ?? '';
            $adultIO = $_POST['adultIO'] ?? '';
            $adultBreathSounds = $_POST['adultBreathSounds'] ?? '';
            $adultLevelofActivity = $_POST['adultLevelofActivity'] ?? '';

            $objCaseStudy->adultHR = $adultHR;
            $objCaseStudy->adultSpontRR = $adultSpontRR;
            $objCaseStudy->adultBP = $adultBP;
            $objCaseStudy->adultTemp = $adultTemp;
            $objCaseStudy->adultSpO2 = $adultSpO2;
            $objCaseStudy->adultIO = $adultIO;
            $objCaseStudy->adultBreathSounds = $adultBreathSounds;
            $objCaseStudy->adultLevelofActivity = $adultLevelofActivity;

            $retcaseStudyId = $objCaseStudy->SaveCaseStudyAdultVitalsSection($caseStudyId);
            if ($retcaseStudyId > 0) {
                $status = 'Success';
            }
            break;
        case 'readings':
            $adultNa  = ($_POST['adultNa']);
            $adultK  = ($_POST['adultK']);
            $adultCl  = ($_POST['adultCl']);
            $adultWBC  = ($_POST['adultWBC']);
            $adultHgb  = ($_POST['adultHgb']);
            $adultHct  = ($_POST['adultHct']);

            $adultCO2  = ($_POST['adultCO2']);
            $adultBUN  = ($_POST['adultBUN']);
            $adultGlucose  = ($_POST['adultGlucose']);
            $adultPlatelets  = ($_POST['adultPlatelets']);

            $adultINR  = ($_POST['adultINR']);
            $adultSputumCult  = ($_POST['adultSputumCult']);
            $adultCreatinine  = ($_POST['adultCreatinine']);
            $adultLabInterpretation  = ($_POST['adultLabInterpretation']);
            $adultXRayInterpretation  = ($_POST['adultXRayInterpretation']);
            $adultETT  = ($_POST['adultETT']);
            $adultPosition  = ($_POST['adultPosition']);
            $adultTrachType  = ($_POST['adultTrachType']);
            $adult1  = ($_POST['adult1']);
            $adultCuffPressure  = ($_POST['adultCuffPressure']);

            $adultpH  = ($_POST['adultpH']);
            $adultPaCO2  = ($_POST['adultPaCO2']);
            $adultHCO3  = ($_POST['adultHCO3']);
            $adultSvO2  = ($_POST['adultSvO2']);


            $adultCO  = ($_POST['adultCO']);
            $adultPAP  = ($_POST['adultPAP']);
            $adultPaO2  = ($_POST['adultPaO2']);
            $adultSaO2  = ($_POST['adultSaO2']);
            $adultFiO2Lpm  = ($_POST['adultFiO2Lpm']);
            $adultCVP  = ($_POST['adultCVP']);
            $adultPCWP  = ($_POST['adultPCWP']);
            $adultICP  = ($_POST['adultICP']);
            $adultDateBloodGas  = ($_POST['adultDateBloodGas']);
            $adultInterpretationHemodynamics  = ($_POST['adultInterpretationHemodynamics']);

            $adultInterpretationABG  = ($_POST['adultInterpretationABG']);
            $adultEKGResults  = ($_POST['adultEKGResults']);
            $adultInterpretationPAO2  = ($_POST['adultInterpretationPAO2']);
            $adultInterpretationAO2  = ($_POST['adultInterpretationAO2']);
            $adultInterpretationCaO2  = ($_POST['adultInterpretationCaO2']);

            $adultInterpretationPFRatio  = ($_POST['adultInterpretationPFRatio']);

            $objCaseStudy->adultNa = $adultNa;
            $objCaseStudy->adultK = $adultK;
            $objCaseStudy->adultCl = $adultCl;
            $objCaseStudy->adultWBC = $adultWBC;
            $objCaseStudy->adultHgb = $adultHgb;
            $objCaseStudy->adultHct = $adultHct;
            $objCaseStudy->adultCO2 = $adultCO2;
            $objCaseStudy->adultBUN = $adultBUN;
            $objCaseStudy->adultGlucose = $adultGlucose;
            $objCaseStudy->adultPlatelets = $adultPlatelets;
            $objCaseStudy->adultINR = $adultINR;
            $objCaseStudy->adultSputumCult = $adultSputumCult;
            $objCaseStudy->adultCreatinine = $adultCreatinine;
            $objCaseStudy->adultLabInterpretation = $adultLabInterpretation;
            $objCaseStudy->adultXRayInterpretation = $adultXRayInterpretation;
            $objCaseStudy->adultETT = $adultETT;
            $objCaseStudy->adultPosition = $adultPosition;
            $objCaseStudy->adultTrachType = $adultTrachType;
            $objCaseStudy->adult1 = $adult1;
            $objCaseStudy->adultCuffPressure = $adultCuffPressure;
            $objCaseStudy->adultpH = $adultpH;
            $objCaseStudy->adultPaCO2 = $adultPaCO2;
            $objCaseStudy->adultHCO3 = $adultHCO3;
            $objCaseStudy->adultSvO2 = $adultSvO2;
            $objCaseStudy->adultCO = $adultCO;
            $objCaseStudy->adultPAP = $adultPAP;
            $objCaseStudy->adultPaO2 = $adultPaO2;
            $objCaseStudy->adultSaO2 = $adultSaO2;
            $objCaseStudy->adultFiO2Lpm = $adultFiO2Lpm;
            $objCaseStudy->adultCVP = $adultCVP;
            $objCaseStudy->adultPCWP = $adultPCWP;
            $objCaseStudy->adultICP = $adultICP;
            $objCaseStudy->adultDateBloodGas = $adultDateBloodGas;
            $objCaseStudy->adultInterpretationHemodynamics = $adultInterpretationHemodynamics;
            $objCaseStudy->adultInterpretationABG = $adultInterpretationABG;
            $objCaseStudy->adultEKGResults = $adultEKGResults;
            $objCaseStudy->adultInterpretationPAO2 = $adultInterpretationPAO2;
            $objCaseStudy->adultInterpretationAO2 = $adultInterpretationAO2;
            $objCaseStudy->adultInterpretationCaO2 = $adultInterpretationCaO2;
            $objCaseStudy->adultInterpretationPFRatio = $adultInterpretationPFRatio;

            $retcaseStudyId = $objCaseStudy->SaveCaseStudyAdultReadingsSection($caseStudyId);
            if ($retcaseStudyId > 0) {
                $status = 'Success';
            }
            break;
        case 'medications':
            $adultVentilator  = ($_POST['adultVentilator']);
            $adultFiO2  = ($_POST['adultFiO2']);
            $adultPiP  = ($_POST['adultPiP']);
            $adultPlat  = ($_POST['adultPlat']);

            $adultRR  = ($_POST['adultRR']);
            $adultMode  = ($_POST['adultMode']);
            $adultPSupport  = ($_POST['adultPSupport']);
            $adultMAP  = ($_POST['adultMAP']);
            $adultVE  = ($_POST['adultVE']);
            $adultSetRate  = ($_POST['adultSetRate']);
            $adultMaxFlow  = ($_POST['adultMaxFlow']);
            $adultSpontVt  = ($_POST['adultSpontVt']);
            $adultIE  = ($_POST['adultIE']);
            $adultSetVt  = ($_POST['adultSetVt']);

            $adultFlowSens  = ($_POST['adultFlowSens']);
            $adultCsta  = ($_POST['adultCsta']);
            $adultRaw  = ($_POST['adultRaw']);
            $adultVte  = ($_POST['adultVte']);
            $adultIBWVt  = ($_POST['adultIBWVt']);
            $adultItime  = ($_POST['adultItime']);
            $adultPEEPCPAP  = ($_POST['adultPEEPCPAP']);
            $adultHumidityTemp  = ($_POST['adultHumidityTemp']);
            $adultSputumAmount  = ($_POST['adultSputumAmount']);
            $adultOtherSettings  = ($_POST['adultOtherSettings']);
            $adultCough = '';
            if (isset($_POST['adultCough'])) {
                $adultCough  =  $_POST['adultCough'];
            }
            $adultSuction = '';
            if (isset($_POST['adultSuction'])) {
                $adultSuction  =  $_POST['adultSuction'];
            }
            $adultRecommendations  = ($_POST['adultRecommendations']);
            $adultLowHiPiP  = ($_POST['adultLowHiPiP']);

            $adultLowHiVte  = ($_POST['adultLowHiVte']);
            $adultLowHiVe  = ($_POST['adultLowHiVe']);
            $adultLowHiRR  = ($_POST['adultLowHiRR']);
            $adultApneaAlert  = ($_POST['adultApneaAlert']);
            $adultOtherAlarms  = ($_POST['adultOtherAlarms']);
            $adultIPAP  = ($_POST['adultIPAP']);
            $adultEPAP  = ($_POST['adultEPAP']);
            $adultRate  = ($_POST['adultRate']);
            $adultFiO21  = ($_POST['adultFiO21']);
            $adultItimeSetting  = ($_POST['adultItimeSetting']);

            $adultSpontVt  = ($_POST['adultSpontVt']);
            $adultItime  = ($_POST['adultItime']);
            $adultRise  = ($_POST['adultRise']);

            $adultRamp  = ($_POST['adultRamp']);
            $adultFVC  = ($_POST['adultFVC']);
            $adultFEF25  = ($_POST['adultFEF25']);
            $adultFEV1  = ($_POST['adultFEV1']);
            $adultPEFR  = ($_POST['adultPEFR']);
            $adultFEV1FVC  = ($_POST['adultFEV1FVC']);
            $adultLungVolumes  = ($_POST['adultLungVolumes']);
            $adultInterpretationPFT  = ($_POST['adultInterpretationPFT']);
            $adultMedicationsUseList = json_encode($_POST['adultMedicationsUseList']);
            $adultModificationCarePlanList = json_encode($_POST['adultModificationCarePlanList']);

            $objCaseStudy->adultVentilator = $adultVentilator;
            $objCaseStudy->adultFiO2 = $adultFiO2;
            $objCaseStudy->adultPiP = $adultPiP;
            $objCaseStudy->adultPlat = $adultPlat;
            $objCaseStudy->adultRR = $adultRR;
            $objCaseStudy->adultMode = $adultMode;
            $objCaseStudy->adultPSupport = $adultPSupport;
            $objCaseStudy->adultMAP = $adultMAP;
            $objCaseStudy->adultVE = $adultVE;
            $objCaseStudy->adultSetRate = $adultSetRate;
            $objCaseStudy->adultMaxFlow = $adultMaxFlow;
            $objCaseStudy->adultSpontVt = $adultSpontVt;
            $objCaseStudy->adultIE = $adultIE;
            $objCaseStudy->adultSetVt = $adultSetVt;
            $objCaseStudy->adultFlowSens = $adultFlowSens;
            $objCaseStudy->adultCsta = $adultCsta;
            $objCaseStudy->adultRaw = $adultRaw;
            $objCaseStudy->adultVte = $adultVte;
            $objCaseStudy->adultIBWVt = $adultIBWVt;
            $objCaseStudy->adultItime = $adultItime;
            $objCaseStudy->adultPEEPCPAP = $adultPEEPCPAP;
            $objCaseStudy->adultHumidityTemp = $adultHumidityTemp;
            $objCaseStudy->adultSputumAmount = $adultSputumAmount;
            $objCaseStudy->adultOtherSettings = $adultOtherSettings;
            $objCaseStudy->adultSuction = $adultSuction;
            $objCaseStudy->adultCough = $adultCough;
            $objCaseStudy->adultRecommendations = $adultRecommendations;
            $objCaseStudy->adultLowHiPiP = $adultLowHiPiP;
            $objCaseStudy->adultLowHiVte = $adultLowHiVte;
            $objCaseStudy->adultLowHiVe = $adultLowHiVe;
            $objCaseStudy->adultLowHiRR = $adultLowHiRR;
            $objCaseStudy->adultApneaAlert = $adultApneaAlert;
            $objCaseStudy->adultOtherAlarms = $adultOtherAlarms;
            $objCaseStudy->adultIPAP = $adultIPAP;
            $objCaseStudy->adultEPAP = $adultEPAP;
            $objCaseStudy->adultRate = $adultRate;
            $objCaseStudy->adultFiO21 = $adultFiO21;
            $objCaseStudy->adultItimeSetting = $adultItimeSetting;
            $objCaseStudy->adultRise = $adultRise;
            $objCaseStudy->adultRamp = $adultRamp;
            $objCaseStudy->adultFVC = $adultFVC;
            $objCaseStudy->adultFEF25 = $adultFEF25;
            $objCaseStudy->adultFEV1 = $adultFEV1;
            $objCaseStudy->adultPEFR = $adultPEFR;
            $objCaseStudy->adultFEV1FVC = $adultFEV1FVC;
            $objCaseStudy->adultLungVolumes = $adultLungVolumes;
            $objCaseStudy->adultInterpretationPFT = $adultInterpretationPFT;
            $objCaseStudy->adultMedicationsUseList = $adultMedicationsUseList;
            $objCaseStudy->adultModificationCarePlanList = $adultModificationCarePlanList;

            $retcaseStudyId = $objCaseStudy->SaveCaseStudyAdultMedicationsSection($caseStudyId);
            if ($retcaseStudyId > 0) {
                $status = 'Success';
            }
            break;
        case 'comments':
            $studentcomments = $_POST['studentcomments'] ?? '';
            $objDB = new clsDB();
            $objDB->UpdateSingleColumnValueToTable('casestudyadult', 'studentcomments', $studentcomments, 'caseStudyAdultId', $caseStudyId);
            $objDB->UpdateSingleColumnValueToTable('casestudyadult', 'status', '1', 'caseStudyAdultId', $caseStudyId);
            unset($objDB);
            $retcaseStudyId = $caseStudyId;
            if ($retcaseStudyId > 0) {
                $status = 'Success';
                if ($caseStudyId == 0) { // New case study
                    $url = $IsMobile ? BASE_PATH . '/webRedirect.html?status=Success&type=adult' : 'caseStudyList.html?caseStudyId=' . EncodeQueryData($caseStudyId) . '&status=Added';
                } else { // Existing case study
                    $url = $IsMobile ? BASE_PATH . '/webRedirect.html?status=Success&type=adult' : 'caseStudyList.html?status=Updated';
                }

            }

            break;
    }
    if ($status == 'Success') {
        // Audit Log
        $objLog = new clsLogger();
        $action = ($caseStudyId > 0) ? $objLog::EDIT : $objLog::ADD;
        $userType = $objLog::STUDENT;
        $objCaseStudy->saveCaseStudyAuditLog($retcaseStudyId, $type, $studentId, $userType, $action, $IsMobile);
        unset($objLog);
    }

}
// Removed the empty `else if ($type == 'Floor')` block as it was not implemented.
// If you plan to add Floor case study logic, it should go here.

// Prepare and send the response
$resp = [
    'status' => $status,
    'caseStudyId' => $retcaseStudyId,
    'url' => $url,
];
echo json_encode($resp);
exit;
