<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');
include('../class/clsSoapNote.php');
include('../class/clsRotation.php');
include('../class/clsLocations.php');
include('../class/clsStudent.php');
include('../setRequest.php');
include('../class/clsPreceptorDetails.php');
include('../class/clsExternalPreceptors.php');

$selrotationId = 0;
$rotationId = 0;
$studentId = 0;
$studentSignatureDate = 0;
$currentstudentId = 0;
$encodedStudentId = 0;

if (isset($_GET['rotationId'])) {
    $encodedRotationId = $_GET['rotationId'];
    $DefaultrotationId = $_GET['rotationId'];
    $rotationId = DecodeQueryData($DefaultrotationId);
}

if (isset($_GET['studentId'])) {
    $studentId = $_GET['studentId'];
    $encodedStudentId = $_GET['studentId'];
    $currentstudentId = DecodeQueryData($_GET['studentId']);
}

$TimeZone =  isset($_SESSION["loggedUserSchoolTimeZone"]) ? $_SESSION["loggedUserSchoolTimeZone"] : '';
$loggedAsBackUserId = isset($_SESSION["loggedAsBackUserId"]) ? $_SESSION["loggedAsBackUserId"] : 0;
$loggedUserId = isset($_SESSION["loggedUserId"]) ? $_SESSION["loggedUserId"] : 0;

//Get All Soap Note List
$objSoapNote = new clsSoapNote();
$rowsSoapNoteData = $objSoapNote->GetAllSoapNote($currentSchoolId, $currentstudentId, $rotationId);
$totalSoapNoteCount = 0;
if ($rowsSoapNoteData != '') {
    $totalSoapNoteCount = mysqli_num_rows($rowsSoapNoteData);
}
unset($objSoapNote);

$status = isset($_GET["status"]) ? $_GET["status"] : '';
$rotationStatus = checkRotationStatus($rotationId);

//rotation
$objrotation = new clsRotation();
$bedCrumTitle = 'Rotation';
$rotationtitle = '';
//Get Rotation Name
$rotation = $objrotation->GetRotationBySchool($currentSchoolId);
$rowsrotation = $objrotation->GetrotationTitleForInteraction($rotationId, $currentSchoolId);
$rotationtitle = isset($rowsrotation['title']) ? $rowsrotation['title'] : '';

//For Student Name
$objStudent = new clsStudent();
$rowsStudents = $objStudent->GetSingleStudent($currentSchoolId, $currentstudentId);
$studentfullname = isset($rowsStudents['firstName'], $rowsStudents['lastName']) ? $rowsStudents['firstName'] . ' ' . $rowsStudents['lastName'] : '';
unset($objStudent);

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>Soap Note</title>
    <?php include('includes/headercss.php'); ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <?php include("includes/datatablecss.php") ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">

    <style>
        .input-group-addon {
            position: absolute;
            right: 7px;
            /* width: 100%; */
            z-index: 99;
            width: 35px;
            margin: auto;
            top: 5px;
            border-left: 1px solid #ccc;
            border-radius: 50% !important;
            padding: 10px -2px;
            height: 35px;
            /* background: #01A750; */
            /* color: #fff; */
            color: #555;
            background: #f6f9f9;
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .panel-default>.panel-heading {
            background-color: transparent !important;
        }

        .btn-success,
        .btn-default {
            padding: 8px 25px;
            border-radius: 10px;
        }

        .panel {
            border-radius: 14px !important;
        }

        .input-group-addon {
            position: absolute;
            right: 7px;
            /* width: 100%; */
            z-index: 99;
            width: 35px;
            margin: auto;
            top: 5px;
            border-left: 1px solid #ccc;
            border-radius: 50% !important;
            padding: 10px -2px;
            height: 35px;
            /* background: #01A750; */
            /* color: #fff; */
            color: #555;
            background: #f6f9f9;
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .panel-default>.panel-heading {
            background-color: transparent !important;
        }

        .btn-success,
        .btn-default {
            padding: 8px 25px;
            border-radius: 10px;
        }

        .panel {
            border-radius: 14px !important;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 45px !important;
        }

        .required-select2 {
            border-left: solid 3px red !important;
            border-radius: 12px !important;
        }

        .select2-container--default .select2-selection--single {
            background-color: #f6f9f9 !important;
            cursor: default !important;
            height: 45px !important;
            border-radius: 10px !important;
        }

        .select2-container--default .select2-selection--single {
            border: none !important;
        }

        .panel,
        .form-group {
            margin-bottom: 10px;
        }

        .bootstrap-datetimepicker-widget {
            border-radius: 12px !important;
        }

        .form-control {
            height: 45px;
        }

        .input-group {
            width: 100%;
        }

        /* .form-horizontal .form-group {
                margin-right: 0;
                margin-left: 0;
            } */

        @media screen and (max-width: 500px) {
            .panel-body ol {
                padding-left: 20px;
            }

            .panel-default>.panel-heading {
                padding-left: 5px;
            }

            /* .container-zero{
                    padding: 0;
                } */

            div.dataTables_wrapper div.dataTables_length {
                text-align: left !important;
                margin-top: 10px;
            }
        }
    </style>
</head>

<body>

    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <?php if ($currentstudentId) { ?>
                        <li class="active"><a href="clinical.html">Clinical</a></li>
                        <li class="active"><a href="clinical.html"><?php echo ($studentfullname); ?></a></li>
                    <?php } else if ($rotationId > 0) { ?>
                        <li class="active"><a href="rotations.html"><?php echo $bedCrumTitle; ?></a></li>
                        <li class="active"><a href="rotations.html"><?php echo ($rotationtitle); ?></a></li>
                    <?php } ?>
                    <li class="active">Soap Note</li>
                </ol>
            </div>
        </div>
    </div>

    <div class="container">
        <?php
        if (isset($_GET["status"])) {
            $message = "";
            $alertType = "alert-success";

            switch ($_GET["status"]) {
                case "Added":
                    $message = "Activity Sheet added successfully.";
                    break;
                case "Updated":
                    $message = "Activity Sheet updated successfully.";
                    break;
                case "Error":
                    $message = "Error occurred.";
                    $alertType = "alert-danger";
                    break;
            }

            if ($message) {
                echo '<div class="alert ' . $alertType . ' alert-dismissible fade in" role="alert">';
                echo '<button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>';
                echo $message;
                echo '</div>';
            }
        }
        ?>

        <div id="divTopLoading">Loading...</div>

        <div class="row">
            <?php if ($currentstudentId) { ?>
                <div class="col-md-4 pull-right">
                    <div class="form-group">
                        <label class="col-md-3 col-form-label" for="cboRotation" style="margin-top:8px">Rotation</label>
                        <div class="col-md-9 pr-1">
                            <select id="cboRotation" name="cboRotation" class="form-control input-md select2_single">
                                <option value="" selected>Select</option>
                                <?php
                                if ($rotation != "") {
                                    while ($row = mysqli_fetch_assoc($rotation)) {
                                        $selrotationIdDropdown = $row['rotationId'];
                                        $name = stripslashes($row['title']);
                                ?>
                                        <option value="<?php echo EncodeQueryData($selrotationIdDropdown); ?>" <?php if ($rotationId == $selrotationIdDropdown) { ?> selected <?php } ?>>
                                            <?php echo ($name); ?>
                                        </option>
                                <?php
                                    }
                                }
                                ?>
                            </select>
                            <input type="hidden" name="hidenstudentid" id="hidenstudentid" value="<?php echo EncodeQueryData($currentstudentId); ?>">
                        </div>
                    </div>
                </div>
            <?php } ?>
        </div>
        <br>
                        <div class="col-md-12" style="overflow-x: auto;">




        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Student Name</th>
                    <th>Rotation</th>
                    <th style="text-align: center">Clinician/Preceptor Details</th>
                    <th>Clinician/Preceptor <br>Sign Date </th>
                    <th>Student Sign Date </th>
                    <th style="text-align: center">Action</th>
                </tr>
            </thead>
            <tbody>
                <?php
                if ($totalSoapNoteCount > 0) {
                    while ($row = mysqli_fetch_array($rowsSoapNoteData)) {

                        $soapNoteId = ($row['soapNoteId']);
                        $soapNoteDate = stripslashes($row['soapNoteDate']);
                        $rotationName = stripslashes($row['rotationName']);
                        $isSendToPreceptor = stripslashes($row['isSendToPreceptor']);
                        $externalPreceptorId = stripslashes($row['preceptorId']);
                        $clinicianName = stripslashes($row['clinicianName']);
                        $studentName = stripslashes($row['studentName']);
                        $preceptorStatus = stripslashes($row['status']);
                        $rotationId = $row['rotationId'];
                        $studentSignatureDate = stripslashes($row['studentSignatureDate']);

                        $soapNoteDate = date("m/d/Y", strtotime($soapNoteDate));
                        if ($soapNoteDate != '12/31/1969' && $soapNoteDate != '' && $soapNoteDate != '11/30/-0001' && $soapNoteDate != '0000-00-00') {
                            $soapNoteDate = converFromServerTimeZone($soapNoteDate, $TimeZone);
                            $soapNoteDate = date("m/d/Y", strtotime($soapNoteDate));
                        } else
                            $soapNoteDate = '';

                        $isSendToPreceptor = stripslashes($row['isSendToPreceptor']);

                        if ($isSendToPreceptor == 0) {
                            $clinicianSignatureDate = stripslashes($row['clinicianSignatureDate']); //clinician signature date
                            if ($clinicianSignatureDate != '12/31/1969' && $clinicianSignatureDate != '' && $clinicianSignatureDate != '11/30/-0001' && $clinicianSignatureDate != '0000-00-00') {
                                $clinicianSignatureDate = converFromServerTimeZone($clinicianSignatureDate, $TimeZone);
                                $clinicianSignatureDate = date("m/d/Y", strtotime($clinicianSignatureDate));
                            } else
                                $clinicianSignatureDate = '-';
                        } else {
                            $clinicianSignatureDate = stripslashes($row['signatureDate']); //preceptor signature date
                            if ($clinicianSignatureDate != '12/31/1969' && $clinicianSignatureDate != '' && $clinicianSignatureDate != '11/30/-0001' && $clinicianSignatureDate != '0000-00-00') {
                                $clinicianSignatureDate = converFromServerTimeZone($clinicianSignatureDate, $TimeZone);
                                $clinicianSignatureDate = date("m/d/Y", strtotime($clinicianSignatureDate));
                            } else
                                $clinicianSignatureDate = '-';
                        }

                        $studentSignatureDate  = stripslashes($row['studentSignatureDate']); //student signature date
                        if ($studentSignatureDate  != '12/31/1969' && $studentSignatureDate  != '' && $studentSignatureDate  != '11/30/-0001' && $studentSignatureDate  != '0000-00-00') {
                            $studentSignatureDate = converFromServerTimeZone($studentSignatureDate, $TimeZone);
                            $studentSignatureDate  = date("m/d/Y", strtotime($studentSignatureDate));
                        } else
                            $studentSignatureDate  = '-';

                        $isCompletedStatus = '';
                        $objExternalPreceptors = new clsExternalPreceptors();

                        $externalPreceptorDetail = $objExternalPreceptors->GetExternalPreceptorDetail($externalPreceptorId);
                        $preceptorId = isset($externalPreceptorDetail['id']) ? $externalPreceptorDetail['id'] : 0;
                        $preceptorFirstName = isset($externalPreceptorDetail['firstName']) ? $externalPreceptorDetail['firstName'] : '';
                        $preceptorLastName = isset($externalPreceptorDetail['lastName']) ? $externalPreceptorDetail['lastName'] : '';
                        $preceptorFullName = $preceptorFirstName . ' ' . $preceptorLastName;
                        $preceptorMobileNum = isset($externalPreceptorDetail['mobile_num']) ? $externalPreceptorDetail['mobile_num'] : '';
                        $formatted_number = '';
                        if ($preceptorMobileNum != '') {
                            $mobileNum = str_replace("-", "", $preceptorMobileNum);

                            $masked_number = substr_replace($mobileNum, "XXXXXX", 0, 6);
                            $formatted_number = substr($masked_number, 0, 3) . "-" . substr($masked_number, 3, 3) . "-" . substr($mobileNum, 6);
                        }
                        $isCompletedStatus = $preceptorStatus ? 'Completed' : 'Pending';
                        $objDB = new clsDB();
                ?>
                        <tr>
                            <td>
                                <?php echo ($soapNoteDate);
                                ?>
                            </td>
                            <td>
                                <?php echo ($studentName);
                                ?>
                            </td>
                            <td>
                                <?php echo ($rotationName);
                                ?>
                            </td>
                            <td class="<?php if ($preceptorMobileNum == 0) {
                                            echo 'text-center';
                                        }  ?>">
                                <?php if ($isSendToPreceptor) { ?>
                                    Name: <?php echo $preceptorFullName; ?> <br>
                                    Phone: <?php echo $formatted_number; ?> <br>
                                    Status: <?php echo $isCompletedStatus; ?> <br>

                                <?php } else { ?>
                                    <?php echo $clinicianName; ?>
                                <?php } ?>
                            </td>
                            <td align="center">
                                <?php echo ($clinicianSignatureDate); ?>
                            </td>
                            <td align="center">
                                <?php echo ($studentSignatureDate); ?>
                            </td>
                            <td style="text-align: center">
                                <?php
                                $rotationStatus = checkRotationStatus($rotationId);
                                ?>
                                <a href="addSoapNote.html?editsoapNoteId=<?php echo (EncodeQueryData($soapNoteId)); ?><?php if ($currentstudentId){?>&studentId=<?php echo (EncodeQueryData($currentstudentId));}else { ?>&rotationId=<?php echo EncodeQueryData($rotationId); }?>&view=V">View</a>
                                <?php if ($loggedAsBackUserId) {  ?>
                                    | <a href="javascript:void(0);" class="deleteAjaxRow" soapNoteId="<?php echo EncodeQueryData($soapNoteId); ?>" studentId="<?php echo ($selectedStudentId); ?>">Delete</a>

                                <?php }  ?>
                            </td>
                        </tr>
                <?php
                    }
                }
                unset($objrotation);
                ?>
            </tbody>

        </table>
    </div>
    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <?php include('../includes/procedureStepsModal.php'); ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        $(window).load(function() {
            $("#divTopLoading").addClass('hide');
            $(".select2_single").select2();
          
        });
        var current_datatable = $("#datatable-responsive").DataTable({
            "scrollX": true,
            "responsive": false,
            "ordering": true,
            "order": [
                [0, "desc"]
            ],

            "aoColumns": [{
                    "sWidth": "10%"
                },
                {
                    "sWidth": "20%",
                    "sClass": "alignCenter"

                }, {
                    "sWidth": "20%",
                    "sClass": "alignCenter"

                },
                {
                    "sWidth": "20%",
                    "sClass": "alignCenter"

                }, {
                    "sWidth": "15%",
                    "sClass": "alignCenter"

                }, {
                    "sWidth": "15%",
                    "sClass": "alignCenter",
                },
                {
                    "sWidth": "20%",
                    "sClass": "alignCenter",
                }
            ]
        });

        function ShowEvaluationDetails(eleObj) {
            var preceptorId = $(eleObj).attr('preceptorId');
            var preceptorNum = $(eleObj).attr('preceptorMobileNum');
            var soapNoteId = $(eleObj).attr('soapNoteId');
            var rotationId = $(eleObj).attr('rotationId');
            var title = $(eleObj).attr('title');

            $('#preceptorId').val(preceptorId);
            $('#preceptorNum').val(preceptorNum);
            $('#soapNoteId').val(soapNoteId);
            $('#rotationId').val(rotationId);
            $('#evaluationType').val('soapnote');
            $('#title').text(title);
        }

        $(document).on('click', '.btnSendSms', function() {
            var data = $('#resendForm').serialize();
            var mobileNo = $('#preceptorNum').val();
            var rsmobileNo = mobileNo.replace(/[_-]/g, '');
            var mobileNoLength = rsmobileNo.length;

            if (mobileNoLength != 10) {
                alertify.error('Invalid Mob Number')
                return false;
            }

            $.ajax({
                type: "POST",
                url: "../ajax/send_evaluation_sms_to_preceptor.html",
                data: {
                    data
                },
                success: function(data) {
                    alertify.success('Sent');
                    // console.log(data);
                    // window.location.reload();
                    setTimeout(function() {
                        window.location.reload();
                    }, 2000);
                    // history.go(0);
                }
            });


        });

        // ajax call for deleteAjaxRow
        $(document).on('click', '.deleteAjaxRow', function() {
            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var soapNoteId = $(this).attr('soapNoteId');
            var userId = '<?php echo EncodeQueryData($loggedUserId); ?>';
            var isUser = 1; //for Admin
            alertify.confirm('Soap Note: ', 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: soapNoteId,
                        userId: userId,
                        isUser: isUser,
                        type: 'soapnote'
                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        alertify.success('Deleted');
                    }
                });
            }, function() {});
        });
        $("#cboRotation").change(function() {
            var encodedRotationId = $(this).val();
            var encodedStudentId = '<?php echo $encodedStudentId; ?>';

            redirectUrl = 'soapNoteList.html';

            if (encodedRotationId != '')

            redirectUrl = redirectUrl + "?rotationId=" + encodedRotationId + "&studentId=" + encodedStudentId;

            if (encodedStudentId != '') {
                if (encodedRotationId != '')
                    redirectUrl = redirectUrl + "&studentId=" + encodedStudentId;
                else
                    redirectUrl = redirectUrl + "?studentId=" + encodedStudentId;

            }

            window.location.href = redirectUrl;
        });
    </script>
</body>

</html>