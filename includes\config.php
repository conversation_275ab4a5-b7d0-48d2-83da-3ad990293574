<?php
// Load environment variables
require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/../egypt/');
$dotenv->load();

// Get current environment from .env file
$currentEnv = $_ENV['APP_ENV'] ?? 'local';

// Environment detection can override .env setting
$host = $_SERVER['HTTP_HOST'] ?? $_SERVER['SERVER_NAME'];

// Set environment-specific variables
switch ($currentEnv) {
	case 'production':
		define("DEVELOPMENT", "0");
		// Database settings
		define("DB_HOST", $_ENV['PROD_DENMARK_HOST']);
		define("DB_USER", $_ENV['PROD_DENMARK_CLOSE']);
		define("DB_PASSWORD", $_ENV['PROD_DENMARK_OPEN']);
		define("DB_NAME", $_ENV['PROD_DENMARK_NAME']);
		define("BASE_PATH", $_ENV['PROD_BASE_PATH']);
		define("ROOT_PATH", $_ENV['PROD_ROOT_PATH']);

		// Chat settings
		define("CHAT_CLIENT_ID_LOCAL", $_ENV['PROD_CALIFORNIA_ID']);
		define("CHAT_CLIENT_SECRET_KEY_LOCAL", $_ENV['PROD_CALIFORNIA_HANG']);
		define("CHAT_APIKEY_LOCAL", $_ENV['PROD_CALIFORNIA_LONG']);
		define("CHAT_DOMAIN_BACKEND", $_ENV['PROD_CALIFORNIA_BACKEND']);
		define("CHAT_DOMAIN_FRONTEND", $_ENV['PROD_CALIFORNIA_FRONTEND']);
		define("CHAT_USER_SERVICE", $_ENV['PROD_CALIFORNIA_SERVICE']);
		define("CHAT_NOTIFICATION_SERVICE", $_ENV['PROD_CALIFORNIA_NOTI']);

		// Audit log settings
		define("AUDIT_LOG_CLIENT_ID", $_ENV['PROD_ARIZONA_ID']);
		define("AUDIT_LOG_APIKEY", $_ENV['PROD_ARIZONA_LONG']);
		define("AUDIT_LOG_DOMAIN_BACKEND", $_ENV['PROD_ARIZONA_BACKEND']);
		define("AUDIT_LOG_DOMAIN_FRONTEND", $_ENV['PROD_ARIZONA_FRONTEND']);
		define("AUDIT_LOG_SERVICE_KEY", $_ENV['PROD_ARIZONA_SERVICE']);
		define("AUDIT_LOG_STATE_KEY", $_ENV['PROD_ARIZONA_STATE']);

		// Mailgun settings
		define("CT_MAXICO", $_ENV['PROD_MAXICO']);
		define("CT_MAXICO_DOMAIN", $_ENV['PROD_MAXICO_MAIN']);
		define("CT_MAXICO_FROM_EMAIL", $_ENV['PROD_MAXICO_EMAIL']);
		define("CT_MAXICO_FROM_NAME", $_ENV['PROD_MAXICO_NAME']);
		break;

	case 'uat':
		define("DEVELOPMENT", "0");
		// Database settings
		define("DB_HOST", $_ENV['UAT_DENMARK_HOST']);
		define("DB_USER", $_ENV['UAT_DENMARK_CLOSE']);
		define("DB_PASSWORD", $_ENV['UAT_DENMARK_OPEN']);
		define("DB_NAME", $_ENV['UAT_DENMARK_NAME']);
		define("BASE_PATH", $_ENV['UAT_BASE_PATH']);
		define("ROOT_PATH", $_ENV['UAT_ROOT_PATH']);

		// Chat settings
		define("CHAT_CLIENT_ID_LOCAL", $_ENV['STAGING_CALIFORNIA_ID']);
		define("CHAT_CLIENT_SECRET_KEY_LOCAL", $_ENV['STAGING_CALIFORNIA_HANG']);
		define("CHAT_APIKEY_LOCAL", $_ENV['STAGING_CALIFORNIA_LONG']);
		define("CHAT_DOMAIN_BACKEND", $_ENV['STAGING_CALIFORNIA_BACKEND']);
		define("CHAT_DOMAIN_FRONTEND", $_ENV['STAGING_CALIFORNIA_FRONTEND']);
		define("CHAT_USER_SERVICE", $_ENV['STAGING_CALIFORNIA_SERVICE']);
		define("CHAT_NOTIFICATION_SERVICE", $_ENV['STAGING_CALIFORNIA_NOTI']);

		// Audit log settings
		define("AUDIT_LOG_CLIENT_ID", $_ENV['STAGING_ARIZONA']);
		define("AUDIT_LOG_APIKEY", $_ENV['STAGING_ARIZONA_LONG']);
		define("AUDIT_LOG_DOMAIN_BACKEND", $_ENV['STAGING_ARIZONA_BACKEND']);
		define("AUDIT_LOG_DOMAIN_FRONTEND", $_ENV['STAGING_ARIZONA_FRONTEND']);
		define("AUDIT_LOG_SERVICE_KEY", $_ENV['STAGING_ARIZONA_SERVICE']);
		define("AUDIT_LOG_STATE_KEY", $_ENV['STAGING_ARIZONA_STATE']);

		// Mailgun settings
		define("CT_MAXICO", $_ENV['STAGING_MAXICO']);
		define("CT_MAXICO_DOMAIN", $_ENV['STAGING_MAXICO_MAIN']);
		define("CT_MAXICO_FROM_EMAIL", $_ENV['STAGING_MAXICO_SUPPORT']);
		define("CT_MAXICO_FROM_NAME", $_ENV['STAGING_MAXICO_NAME']);
		break;

	case 'staging':
		define("DEVELOPMENT", "0");
		// Database settings
		define("DB_HOST", $_ENV['STAGING_DENMARK_HOST']);
		define("DB_USER", $_ENV['STAGING_DENMARK_CLOSE']);
		define("DB_PASSWORD", $_ENV['STAGING_DENMARK_OPEN']);
		define("DB_NAME", $_ENV['STAGING_DENMARK_NAME']);
		define("BASE_PATH", $_ENV['STAGING_BASE_PATH']);
		define("ROOT_PATH", $_ENV['STAGING_ROOT_PATH']);

		// Chat settings
		define("CHAT_CLIENT_ID_LOCAL", $_ENV['STAGING_CALIFORNIA_ID']);
		define("CHAT_CLIENT_SECRET_KEY_LOCAL", $_ENV['STAGING_CALIFORNIA_HANG']);
		define("CHAT_APIKEY_LOCAL", $_ENV['STAGING_CALIFORNIA_LONG']);
		define("CHAT_DOMAIN_BACKEND", $_ENV['STAGING_CALIFORNIA_BACKEND']);
		define("CHAT_DOMAIN_FRONTEND", $_ENV['STAGING_CALIFORNIA_FRONTEND']);
		define("CHAT_USER_SERVICE", $_ENV['STAGING_CALIFORNIA_SERVICE']);
		define("CHAT_NOTIFICATION_SERVICE", $_ENV['STAGING_CALIFORNIA_NOTI']);

		// Audit log settings
		define("AUDIT_LOG_CLIENT_ID", $_ENV['STAGING_ARIZONA']);
		define("AUDIT_LOG_APIKEY", $_ENV['STAGING_ARIZONA_LONG']);
		define("AUDIT_LOG_DOMAIN_BACKEND", $_ENV['STAGING_ARIZONA_BACKEND']);
		define("AUDIT_LOG_DOMAIN_FRONTEND", $_ENV['STAGING_ARIZONA_FRONTEND']);
		define("AUDIT_LOG_SERVICE_KEY", $_ENV['STAGING_ARIZONA_SERVICE']);
		define("AUDIT_LOG_STATE_KEY", $_ENV['STAGING_ARIZONA_STATE']);

		// Mailgun settings
		define("CT_MAXICO", $_ENV['STAGING_MAXICO']);
		define("CT_MAXICO_DOMAIN", $_ENV['STAGING_MAXICO_MAIN']);
		define("CT_MAXICO_FROM_EMAIL", $_ENV['STAGING_MAXICO_SUPPORT']);
		define("CT_MAXICO_FROM_NAME", $_ENV['STAGING_MAXICO_NAME']);
		break;

	case 'local':
	default:
		define("DEVELOPMENT", "0");
		// Database settings
		define("DB_HOST", $_ENV['LOCAL_DENMARK_HOST']);
		define("DB_USER", $_ENV['LOCAL_DENMARK_CLOSE']);
		define("DB_PASSWORD", $_ENV['LOCAL_DENMARK_OPEN']);
		define("DB_NAME", $_ENV['LOCAL_DENMARK_NAME']);
		define("BASE_PATH", $_ENV['LOCAL_BASE_PATH']);
		define("ROOT_PATH", $_ENV['LOCAL_ROOT_PATH']);

		// Chat settings
		define("CHAT_CLIENT_ID_LOCAL", $_ENV['LOCAL_CALIFORNIA_ID']);
		define("CHAT_CLIENT_SECRET_KEY_LOCAL", $_ENV['LOCAL_CALIFORNIA_HANG']);
		define("CHAT_APIKEY_LOCAL", $_ENV['LOCAL_CALIFORNIA_LONG']);
		define("CHAT_DOMAIN_BACKEND", $_ENV['LOCAL_CALIFORNIA_BACKEND']);
		define("CHAT_DOMAIN_FRONTEND", $_ENV['LOCAL_CALIFORNIA_FRONTEND']);
		define("CHAT_USER_SERVICE", $_ENV['LOCAL_CALIFORNIA_SERVICE']);
		define("CHAT_NOTIFICATION_SERVICE", $_ENV['LOCAL_CALIFORNIA_NOTI']);

		// Audit log settings
		define("AUDIT_LOG_CLIENT_ID", $_ENV['LOCAL_ARIZONA']);
		define("AUDIT_LOG_APIKEY", $_ENV['LOCAL_ARIZONA_LONG']);
		define("AUDIT_LOG_DOMAIN_BACKEND", $_ENV['LOCAL_ARIZONA_BACKEND']);
		define("AUDIT_LOG_DOMAIN_FRONTEND", $_ENV['LOCAL_ARIZONA_FRONTEND']);
		define("AUDIT_LOG_SERVICE_KEY", $_ENV['LOCAL_ARIZONA_SERVICE']);
		define("AUDIT_LOG_STATE_KEY", $_ENV['LOCAL_ARIZONA_STATE']);

		// Mailgun settings
		define("CT_MAXICO", $_ENV['LOCAL_MAXICO']);
		define("CT_MAXICO_DOMAIN", $_ENV['LOCAL_MAXICO_MAIN']);
		define("CT_MAXICO_FROM_EMAIL", $_ENV['LOCAL_MAXICO_EMAIL']);
		define("CT_MAXICO_FROM_NAME", $_ENV['LOCAL_MAXICO_NAME']);
		break;
}

// Application settings
define("APPLICATION_NAME", "Clinical Trac");
define("DEFAULT_DOMAIN_NAME", "development.com");
define("DEFAULT_DOMAIN_SLUG", "development");
define("API_SECURITY_KEY", $_ENV['ALASKA_LONG']);
define("API_ACCESS_KEY", $_ENV['ALASKA_ACCESS']);
define("API_ACCESS_PASSWORD", $_ENV['ALASKA_OPEN']);
define("TOKEN_SECURITY_KEY", $_ENV['ALASKA_TOKEN']);

// Role configuration
define("APPLICANTION_SUPERADMIN_ROLE_ID", "1");
define("DEFAULT_RANK", serialize([
	"Freshman" => "1",
	"Sophomore" => "2",
	"Junior " => "3",
	"Senior " => "4",
	"Graduate" => "5",
	"IRR" => "6",
	"Dropout" => "7"
]));

define("DEFAULT_CLINCIAN_ROLE", serialize([
	["Coordinator", 1, 'C'],
	["Faculty", 2, 'F'],
	["Preceptor", 3, 'P'],
	["Advisory", 4, 'A'],
	["HR", 5, 'H']
]));

define("DEFAULT_SEMESTER", serialize(["Spring" => "1", "Fall" => "2", "Winter" => "3"]));

// Timezone settings
date_default_timezone_set('America/Chicago');
define("SERVER_TIMEZONE", "America/Chicago");

// Canvas integration
define("CanvasToken", $_ENV['CHICAGO_TOKEN']);

// Twilio SMS configuration
define("ACCOUNT_SID", $_ENV['TENNESSEE_ACCOUNT_SID']);
define("AUTH_TOKEN_ID", $_ENV['TENNESSEE_TOKEN']);
define("REGISTER_NUMBER", $_ENV['TENNESSEE_REGISTER_NUMBER']);

// SendGrid email configuration
define("SENDGRID_API_KEY", $_ENV['SAMOA_LONG']);

// TeleSign SMS configuration
define("TS_CUSTOMER_ID", $_ENV['TX_CUSTOMER_ID']);
define("TS_API_KEY", $_ENV['TX_LONG']);

// ClickSend SMS configuration
define("COLORADO_USERNAME", isset($_ENV['COLORADO_USERNAME']) ? $_ENV['COLORADO_USERNAME'] : '');
define("COLORADO_LONG", isset($_ENV['COLORADO_LONG']) ? $_ENV['COLORADO_LONG'] : '');
