<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsPEF.php');
include('../setRequest.php');

$isPEFType = isset($_GET['isPEFType']) ? DecodeQueryData($_GET['isPEFType']) : 0;
$sectionMasterId = isset($_GET['sectionMasterId']) ? (int)DecodeQueryData($_GET['sectionMasterId']) : 0;

$title = "Add PEF Evaluation Section  ";
if ($isPEFType == 1)
    $page_title = "Add PEF II";
else
    $page_title = "Add PEF I";

$bedCrumTitle = 'Add';
$PEFSectionId = 0;
$title = '';
$sortOrder  = '';
$TopicTitleId = '';
$view = '';
$bedCrumTitle = 'Add';

$sortOrder = '';
if ($isPEFType == 0) {
    $activeType = 'pef1';
} else {
    $activeType = 'pef2';
}
$view = isset($_GET['view']) ? $_GET['view'] : '';

if (isset($_GET['editid'])) //Edit Mode
{
    $PEFSectionId = DecodeQueryData($_GET['editid']);

    if ($view == 1) {
        if ($isPEFType == 1)
            $page_title = "View PEF II ";
        else
            $page_title = "View PEF I ";
    } else {
        if ($isPEFType == 1)
            $page_title = "Edit PEF II ";
        else
            $page_title = "Edit PEF I ";
    }

    if ($view == '1') {
        $bedCrumTitle = 'view';
    } else {
        $bedCrumTitle = 'Edit';
    }

    //For Checkoff Topic Details
    $objPef = new clsPEF();
    $row = $objPef->GetPefEvaluationSectionDetail($PEFSectionId, $isPEFType);
    unset($objPef);
    $title  = stripslashes($row['title']);
    $sortOrder  = stripslashes($row['sortOrder']);
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($page_title); ?></title>
    <?php include('includes/headercss.php'); ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">

</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="settings.html">Settings</a></li>
                    <li>
                        <a href="pefEvaluationSectionList.html?active=<?php echo ($isPEFType == 1 ? 'pef2' : 'pef1'); ?>&isPEFType=<?php echo EncodeQueryData($isPEFType); ?>">
                            <?php echo ($isPEFType == 1) ? "PEF II Evaluation Section" : "PEF I Evaluation Section"; ?>
                        </a>
                    </li>
                    <li class="active"><?php echo ($bedCrumTitle); ?></li>
                </ol>
            </div>
        </div>
    </div>

    <div class="container">

        <form id="frmpefevaluation" data-parsley-validate class="form-horizontal" method="POST" action="addPEFEvaluationSectionSubmit.html?editid=<?php echo (EncodeQueryData($PEFSectionId)); ?>&isPEFType=<?php echo (EncodeQueryData($isPEFType)); ?>">
            <input type="hidden" name="isPEFType" id="" value="<?php echo $isPEFType; ?>">
            <div class="row">
                <div class="col-md-6">

                    <!-- Text input-->
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtsection"> Section</label>
                        <div class="col-md-12">
                            <input id="txtsection" name="txtsection" value="<?php echo ($title); ?>" type="text" class="form-control input-md required-input" required>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtsortorder">Section Number</label>
                        <div class="col-md-12">
                            <input id="txtsortorder" name="txtsortorder" value="<?php echo ($sortOrder); ?>" type="text" class="form-control input-md required-input" required>

                        </div>
                    </div>

                </div>
            </div>
            <div class="form-group">
                <!-- <label class="col-md-2 control-label"></label> -->
                <div class="col-md-12" style="display: flex; justify-content: center; margin: 20px 0; gap: 15px;">
                    <?php if ($view == 1) { ?>
                        <a type="button" href="pefEvaluationSectionList.html?sectionMasterId=<?php echo (EncodeQueryData($sectionMasterId)); ?>&active=<?php echo ($isPEFType == 1 ? 'pef2' : 'pef1'); ?>" class="btn btn-default">Cancel</a>
                    <?php } else { ?>
                        <button id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
                        <a type="button" href="pefEvaluationSectionList.html?active=<?php echo ($isPEFType == 1 ? 'pef2' : 'pef1'); ?>"
                            class="btn btn-default">Cancel</a>
                    <?php } ?>
                </div>

            </div>
        </form>

    </div>

    <?php include('includes/footer.php'); ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="https://cdn.ckeditor.com/ckeditor5/35.0.1/classic/ckeditor.js"></script>
    <script type="text/javascript">
        $(".select2_tags").select2({
            'placeholder': 'Select'
        });
        $(window).load(function() {

            $('#frmpefevaluation').parsley().on('field:validated', function() {
                    var ok = $('.parsley-error').length === 0;
                })
                .on('form:submit', function() {
                    ShowProgressAnimation();
                    return true;
                });
        });
    </script>
    <?php if ($view == 1): ?>
        <script>
            document.addEventListener("DOMContentLoaded", function() {
                // Disable all form fields inside the form only
                document.querySelectorAll('#frmpefevaluation input, #frmpefevaluation select, #frmpefevaluation textarea, #frmpefevaluation button').forEach(function(el) {
                    el.setAttribute('disabled', true);
                });

                // Optional: enable Cancel button so user can go back
                document.querySelectorAll('.btn-default').forEach(function(el) {
                    el.removeAttribute('disabled');
                });
            });
        </script>
    <?php endif; ?>


</body>

</html>