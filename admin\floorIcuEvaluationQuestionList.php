<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');
include('../class/clsFloorTherapyAndICUEvaluation.php');
include('../setRequest.php');

$isFloorIcuType = isset($_GET['isFloorIcuType']) ? DecodeQueryData($_GET['isFloorIcuType']) : 0;
$sectionMasterId = 0;
$counter = 1;
if (isset($_GET['sectionMasterId'])) {
    $sectionMasterId = $_GET['sectionMasterId'];
    $sectionMasterId = DecodeQueryData($_GET['sectionMasterId']);
}
if ($isFloorIcuType == 0) {
    $activeType = 'floor';
} else {
    $activeType = 'icu';
}
$totalCIevaluation = 0;
$objFloorTherapy = new clsFloorTherapyAndICUEvaluation();
$PefEvaluationquestion = $objFloorTherapy->GetAllFloorIcuEvaluationQuestionToSetting($sectionMasterId, $isFloorIcuType);

$totalCIevaluation = ($PefEvaluationquestion != '') ? mysqli_num_rows($PefEvaluationquestion)  : 0;
$loggedAsBackUserId = isset($_SESSION["loggedAsBackUserId"]) ? $_SESSION["loggedAsBackUserId"] : 0;
$loggedUserIsRolePrimary = isset($_SESSION["loggedUserIsRolePrimary"]) ? $_SESSION["loggedUserIsRolePrimary"] : 0;
$loggedUserId = $_SESSION["loggedUserId"];
$objDB = new clsDB();
$isPrimaryUser = 0;
$isPrimaryUser = $objDB->GetSingleColumnValueFromTable('systemusermaster', 'isPrimaryUser', 'systemUserMasterId', $loggedUserId);
unset($objDB);

?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title>Floor Therapy and ICU/ABG Evaluation Steps</title>
    <?php include('includes/headercss.php'); ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <?php include("includes/datatablecss.php") ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">

</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="settings.html">Setting </a></li>
                    
                    <li>
                          <a href="floorIcuEvaluationSectionList.html?active=<?php echo ($isFloorIcuType == 1 ? 'icu' : 'floor'); ?>&isFloorIcuType=<?php echo EncodeQueryData($isFloorIcuType); ?>">
                            <?php echo ($isFloorIcuType == 1) ? "ICU/ABG Evaluation Section" : "Floor Therapy Evaluation Section"; ?>
                        </a>
                    <li class="active">Steps</li>
                </ol>
            </div>
            <?php

            if ($isCurrentSchoolSuperAdmin == 1) {
            ?>
                <div class="pull-right">
                    <ol class="breadcrumb">
                        <a href="addFloorIcuEvaluationQuestions.html?sectionMasterId=<?php echo EncodeQueryData($sectionMasterId); ?>&isFloorIcuType=<?php echo EncodeQueryData($isFloorIcuType); ?>">Add</a>
                    </ol>
                </div>
            <?php
            }
            ?>
        </div>
    </div>

    <div class="container">
        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button>Question added successfully.
                </div>
        <?php
            }
        } ?>
        <div id="divTopLoading">Loading...</div>


        <div class="row">
            <div class="col-md-10  margin_bottom_ten"></div>
            <div class="col-md-2  margin_bottom_ten">
                <div class="form-group">

                </div>
            </div>
        </div>
        <table id="datatable-responsive" class="table table-bordered  nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr>
                    <th>Sr. No</th>
                    <th style="text-align: center">Sort Order</th>
                    <th>Steps Title</th>
                    <th>Floor Therapy and <br> ICU/ABG Type</th>
                    <th>Type</th>
                    <th style="text-align: center">Action</th>
                </tr>
            </thead>
            <tbody>
                <?php
                if ($totalCIevaluation > 0) {
                    while ($row = mysqli_fetch_array($PefEvaluationquestion)) {
                      
                        $isFloorIcuType = $row['isType'];
                        if ($isFloorIcuType == 1)
                            $isPEF = 'ICU/ABG';
                        else
                            $isPEF = 'Floor Therapy';
                        $questionId = $row['questionId'];

                        $optionText = $row['optionText'];
                        $questionType = $row['questionType'];
                        $sortOrder = isset($row['sortOrder']) ? $row['sortOrder'] : '';
                        $actiontype = '';
                        $assignedQuestionId = $row['questionId'];

                        if ($assignedQuestionId > 0)
                            $actiontype = "false";
                        else
                            $actiontype = "true";

                       
                            $schoolQuestionTitle = $optionText;
                        
                ?>
                        <tr>
                            <td><?php echo ($counter); ?></td>
                            <td style="text-align: center"><?php echo ($sortOrder); ?></td>
                            <td title="<?php echo ($optionText); ?>" style="white-space: normal; word-wrap: break-word;">
                                <?php echo ($schoolQuestionTitle);
                                ?>
                            </td>
                            <td class="text-center"><?php echo ($isPEF); ?></td>
                            <td><?php echo ($questionType); ?></td>
                            <td style="text-align: center">
                                <?php if ($isCurrentSchoolSuperAdmin == 1) { ?>
                                    <!-- Superadmin: Edit + Delete -->
                                    <a href="addFloorIcuEvaluationQuestions.html?editid=<?php echo EncodeQueryData($questionId); ?>&sectionMasterId=<?php echo EncodeQueryData($sectionMasterId); ?>&isFloorIcuType=<?php echo (EncodeQueryData($isFloorIcuType)); ?>">Edit</a>
                                    <?php if ($isCurrentSchoolSuperAdmin == 1) { ?>
                                        | <a href="javascript:void(0);" class="deleteAjaxRow" title="<?php echo ($optionText); ?>" QuestionId="<?php echo EncodeQueryData($questionId); ?>">Delete</a>
                                    <?php } ?>
                                <?php } else { ?>
                                    <!-- Admin: View only -->
                                    <a href="addFloorIcuEvaluationQuestions.html?editid=<?php echo EncodeQueryData($questionId); ?>&sectionMasterId=<?php echo EncodeQueryData($sectionMasterId); ?>&isFloorIcuType=<?php echo (EncodeQueryData($isFloorIcuType)); ?>&view=1">View</a>
                                <?php } ?>
                            </td>
                        </tr>
                <?php
                        $counter++;
                    }
                }
                ?>
            </tbody>
        </table>
    </div>
    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>

    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>

    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        $(window).load(function() {

            $("#divTopLoading").addClass('hide');

        });
        var current_datatable = $("#datatable-responsive").DataTable({
           
            //  autoWidth: false,

            "aoColumns": [{
                    "sWidth": "5%",
                    "bSortable": true
                },
                {
                    "sWidth": "10%"
                },
                {
                    "sWidth": "50%"
                },
                {
                    "sWidth": "15%"
                },
                {
                    "sWidth": "10%"
                },
                {
                    "sWidth": "10%",
                    "bSortable": false
                }
            ],

            "aLengthMenu": [
                [10, 25, 50, 100, -1],
                [10, 25, 50, 100, "All"]
            ],
            "iDisplayLength": 50,
             "responsive" :"true",
        });


        //Delete Questions
        $(document).on('click', '.deleteAjaxRow', function() {

            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var QuestionId = $(this).attr('QuestionId');
            var title = $(this).attr('title');
            var isCurrentSchoolSuperAdmin = "<?php echo $isCurrentSchoolSuperAdmin; ?>";
            var userId = '<?php echo EncodeQueryData($_SESSION['loggedUserId']); ?>';

            alertify.confirm('PEF Evaluation Question: ' + title, 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: QuestionId,
                        isCurrentSchoolSuperAdmin: isCurrentSchoolSuperAdmin,
                        userId: userId,
                        type: 'FloorIcuEvaluationStep'
                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        alertify.success('Deleted');
                    }
                });
            }, function() {});

        });
    </script>
</body>

</html>