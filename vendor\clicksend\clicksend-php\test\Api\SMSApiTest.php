<?php
/**
 * SMSApiTest
 * PHP version 5
 *
 * @category Class
 * @package  ClickSend
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * ClickSend v3 API
 *
 * This is an official SDK for [ClickSend](https://clicksend.com)  Below you will find a current list of the available methods for clicksend.  *NOTE: You will need to create a free account to use the API. You can register [here](https://dashboard.clicksend.com/#/signup/step1/)..*
 *
 * OpenAPI spec version: 3.1
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * ClickSend Codegen version: 3.0.51
 */
/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Please update the test case below to test the endpoint.
 */

namespace ClickSend;

use ClickSend\Configuration;
use ClickSend\ApiException;
use <PERSON>lickSend\ObjectSerializer;
use PHPUnit\Framework\TestCase;

/**
 * SMSApiTest Class Doc Comment
 *
 * @category Class
 * @package  ClickSend
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */
class SMSApiTest extends TestCase
{

    /**
     * Setup before running any test cases
     */
    public static function setUpBeforeClass(): void
    {
    }

    /**
     * Setup before running each test case
     */
    public function setUp(): void
    {
    }

    /**
     * Clean up after running each test case
     */
    public function tearDown(): void
    {
    }

    /**
     * Clean up after running all test cases
     */
    public static function tearDownAfterClass(): void
    {
    }

    /**
     * Test case for smsCancelAllPut
     *
     * Update all scheduled message as cancelled.
     *
     */
    public function testSmsCancelAllPut()
    {
    }

    /**
     * Test case for smsCancelByMessageIdPut
     *
     * Update scheduled message as cancelled.
     *
     */
    public function testSmsCancelByMessageIdPut()
    {
    }

    /**
     * Test case for smsHistoryExportGet
     *
     * Export all sms history.
     *
     */
    public function testSmsHistoryExportGet()
    {
    }

    /**
     * Test case for smsHistoryGet
     *
     * Get all sms history.
     *
     */
    public function testSmsHistoryGet()
    {
    }

    /**
     * Test case for smsInboundGet
     *
     * Get all inbound sms.
     *
     */
    public function testSmsInboundGet()
    {
    }

    /**
     * Test case for smsInboundPost
     *
     * Create inbound sms.
     *
     */
    public function testSmsInboundPost()
    {
    }

    /**
     * Test case for smsInboundReadByMessageIdPut
     *
     * Mark inbound SMS as read.
     *
     */
    public function testSmsInboundReadByMessageIdPut()
    {
    }

    /**
     * Test case for smsInboundReadPut
     *
     * Mark inbound SMS as read.
     *
     */
    public function testSmsInboundReadPut()
    {
    }

    /**
     * Test case for smsPricePost
     *
     * Calculate sms price.
     *
     */
    public function testSmsPricePost()
    {
    }

    /**
     * Test case for smsReceiptsByMessageIdGet
     *
     * Get a Specific Delivery Receipt.
     *
     */
    public function testSmsReceiptsByMessageIdGet()
    {
    }

    /**
     * Test case for smsReceiptsGet
     *
     * Get all delivery receipts.
     *
     */
    public function testSmsReceiptsGet()
    {
    }

    /**
     * Test case for smsReceiptsPost
     *
     * Add a delivery receipt.
     *
     */
    public function testSmsReceiptsPost()
    {
    }

    /**
     * Test case for smsReceiptsReadPut
     *
     * Mark delivery receipts as read.
     *
     */
    public function testSmsReceiptsReadPut()
    {
    }

    /**
     * Test case for smsSendPost
     *
     * Send sms message(s).
     *
     */
    public function testSmsSendPost()
    {
    }

    /**
     * Test case for smsTemplatesByTemplateIdDelete
     *
     * Delete sms template.
     *
     */
    public function testSmsTemplatesByTemplateIdDelete()
    {
    }

    /**
     * Test case for smsTemplatesByTemplateIdPut
     *
     * Update sms template.
     *
     */
    public function testSmsTemplatesByTemplateIdPut()
    {
    }

    /**
     * Test case for smsTemplatesGet
     *
     * Get lists of all sms templates.
     *
     */
    public function testSmsTemplatesGet()
    {
    }

    /**
     * Test case for smsTemplatesPost
     *
     * Create sms template.
     *
     */
    public function testSmsTemplatesPost()
    {
    }
}
