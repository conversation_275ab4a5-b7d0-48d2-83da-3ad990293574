<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsSchool.php');
include('../class/clsCountryStateMaster.php');
include('../setRequest.php');

//Get Location
// $currentSchoolId='';
$objCountryStateMaster = new clsCountryStateMaster();
$countries = $objCountryStateMaster->GetAllCountry();
unset($objCountryStateMaster);

//Get School Type
$schoolName = '';
$contactPerson = '';
$email = '';
$phone = '';
$cellPhone = '';
$address1 = '';
$address2 = '';
$image_name = '';
$defaultSchoolImagePath = '';
$defaultImagePath = '';
$city = '';
$zip = "";
$coarcId = "";
$dbStateId = '';
$dbCountryId = '';
$chkManageRoles = '';
$title = "Add";
$domainName = '';
$isDomainActive = "0";
$displayName = "";
$contactEmail = "";
$code = '';
$slug = '';
$cancelUrl = 'schools.html';
$schoolId = 0;
$contractStartDate = '';
$accountPayableContact = '';
$accountPayableEmail = '';
$accountPayablePhone = '';
$timeZoneId = '';
$contractPeriodId = '';
$contractStartDate = '';
$billingId = '';
$defaultStudentCheckoff  = 1;
$isdefaultCiEval = 1;
$isClinicianReports = 0;
$checkoffType = 0;
$countryCode = '';

if (isset($_GET['id'])) {
    $schoolId = $_GET['id'];
    $schoolId = DecodeQueryData($schoolId);
}
$objSchool = new clsSchool();
$rowContarctPeriods = $objSchool->GetContarctPeriods($currentSchoolId);
$rowBilling = $objSchool->GetBilling($currentSchoolId);
$rowTimezone = $objSchool->GetTimezone($currentSchoolId);

if ($schoolId > 0) //Edit Mode
{
    $title = "Edit";


    $row = $objSchool->GetSchoolDetails($schoolId);


    if ($row == '') {
        header('location:school.html');
        exit;
    }

    $schoolName  = stripslashes($row['title']);
    $displayName  = stripslashes($row['displayName']);
    $contactPerson  = stripslashes($row['contactPerson']);
    $email  = stripslashes($row['email']);
    $phone  = stripslashes($row['phone']);
    $cellPhone  = stripslashes($row['cellPhone']);
    $address1  = stripslashes($row['address1']);
    $address2  = stripslashes($row['address2']);
    $zip  = stripslashes($row['zip']);
    $countryCode  = ($row['countryCode'] != 0) ? $row['countryCode'] : '';
    $city  = stripslashes($row['city']);
    $dbStateId  = stripslashes($row['stateId']);
    $domainName  = $row['domainName'];
    $isDomainActive  = $row['isDomainActive'];
    $contactEmail  = $row['contactEmail'];
    $code  = $row['code'];
    $slug  = $row['slug'];
    $coarcId  = stripslashes($row['coarcId']);
    $billingId  = stripslashes($row['billingId']);
    $contractPeriodId  = stripslashes($row['contractPeriodId']);
    $timeZoneId  = stripslashes($row['timeZoneId']);
    $contractStartDate  = ($row['contractStartDate']);
    $contractStartDate = date('m/d/Y', strtotime($contractStartDate));
    $accountPayableContact  = stripslashes($row['accountPayableContact']);
    $accountPayableEmail  = stripslashes($row['accountPayableEmail']);
    $accountPayablePhone  = stripslashes($row['accountPayablePhone']);
    $defaultStudentCheckoff = $row['isActiveCheckoffForStudent'];
    $isdefaultCiEval = $row['isDefaultCiEval'];
    $isClinicianReports = $row['isClinicianReports'];
    $image_name = stripslashes($row['logoName']);
    $checkoffType = stripslashes($row['checkoffType']);

    $defaultSchoolImagePath = GetSchoolImagePath($schoolId, $image_name);

    //Read Country From State
    $objCountryStateMaster = new clsCountryStateMaster();
    $dbCountryId = $objCountryStateMaster->GetParentIdFromChildId($dbStateId);
    unset($objLocation);
}

unset($objSchool);

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($title); ?> School</title>
    <?php include('includes/headercss.php'); ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/css/cropper.css">


    <style>
        .input-group-addon {
            position: absolute;
            right: 7px;
            /* width: 100%; */
            z-index: 99;
            width: 35px;
            margin: auto;
            top: 5px;
            border-left: 1px solid #ccc;
            border-radius: 50% !important;
            padding: 10px -2px;
            height: 35px;
            /* background: #01A750; */
            /* color: #fff; */
            color: #555;
            background: #f6f9f9;
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .btn-success,
        .btn-default {
            padding: 8px 25px;
            border-radius: 10px;
        }


        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 45px !important;
        }

        .required-select2 {
            border-left: 3px solid red !important;
            border-radius: 12px !important;
        }

        .select2-container--default .select2-selection--single {
            background-color: #f6f9f9 !important;
            cursor: default !important;
            height: 45px !important;
            border-radius: 10px !important;
        }

        .select2-container--default .select2-selection--single {
            border: none !important;
        }

        .bootstrap-datetimepicker-widget {
            border-radius: 12px !important;
        }

        .form-control {
            height: 45px;
        }


        .input-group {
            width: 100%;
        }

        .required-input {
            border-left: 3px solid red !important;
        }

        .input-group-addon {
            background: transparent;
        }

        .formSubHeading {
            border-bottom: 2px solid #d9d6d657;
            padding: 3px 0;
            position: relative;
        }

        input[type="file"] {
            background-color: #fff !important;
        }

    </style>
</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="schools.html">Schools</a></li>
                    <li class="active"><?php echo ($title); ?></li>
                </ol>
            </div>

        </div>
    </div>

    <div class="container">

        <form id="frmSchool" data-parsley-validate class="form-horizontal" method="POST" action="addschoolsubmit.html?id=<?php echo (EncodeQueryData($schoolId)); ?>" enctype="multipart/form-data">
            <!-- Hidden field to store cropped image data -->
            <input type="hidden" id="fileLogo" name="fileLogo" value="">
            <input type="hidden" id="hasCroppedImage" name="hasCroppedImage" value="0">

            <div class="row">
                <div class="col-md-12">

                    <div class="formSubHeading">School Information</div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtSchoolCode">School Code</label>
                                <div class="col-md-12">
                                    <input id="txtSchoolCode" placeholder="Leave blank to auto generate" name="txtSchoolCode" type="text" value="<?php echo ($code); ?>" class="form-control input-md">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">

                            <div class="form-group">
                                <label class="col-md-12 control-label" for="coarcId">Coarc Id</label>
                                <div class="col-md-12">
                                    <input id="coarcId" name="coarcId" type="number" placeholder="" value="<?php echo ($coarcId); ?>" required class="form-control input-md required-input">

                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <!-- Text input-->
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtSchoolName">School Name</label>
                                <div class="col-md-12">
                                    <input id="txtSchoolName" name="txtSchoolName" type="text" value="<?php echo ($schoolName); ?>" placeholder="" class="form-control input-md required-input" required="">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtSlug">Slug</label>
                                <div class="col-md-12">
                                    <input id="txtSlug" name="txtSlug" type="text" value="<?php echo ($slug); ?>" placeholder="" class="form-control input-md required-input" required="">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <!-- Text input-->
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtDisplayName">Display Name</label>
                                <div class="col-md-12">
                                    <input id="txtDisplayName" name="txtDisplayName" type="text" value="<?php echo ($displayName); ?>" placeholder="" class="form-control input-md required-input" required="">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">



                            <!-- Text input-->
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtEmail">Email</label>
                                <div class="col-md-12">
                                    <input id="txtEmail" name="txtEmail" type="email" placeholder="" value="<?php echo ($email); ?>" required class="form-control input-md required-input">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtContactPerson">Contact Person</label>
                                <div class="col-md-12">
                                    <input id="txtContactPerson" name="txtContactPerson" data-parsley-pattern="^(?:[A-Za-z]+[ -])*[A-Za-z]+$" type="text" value="<?php echo ($contactPerson); ?>" placeholder="" class="form-control input-md required-input" required="">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <!-- Text input-->
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtPhone">Phone</label>
                                <div class="col-md-12">
                                    <input id="txtPhone" name="txtPhone" type="text" placeholder="" data-inputmask-alias="************" value="<?php echo ($phone); ?>" required class="form-control input-md required-input">

                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtcellPhone">Cell Phone</label>
                                <div class="col-md-12">
                                    <input id="txtcellPhone" name="txtcellPhone" type="text" data-inputmask-alias="************" placeholder="" value="<?php echo ($cellPhone); ?>" required class="form-control input-md required-input">

                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtContactEmail">Contact Email</label>
                                <div class="col-md-12">
                                    <input id="txtContactEmail" name="txtContactEmail" type="email" placeholder="" value="<?php echo ($contactEmail); ?>" required class="form-control input-md required-input">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="formSubHeading mt-15">Address Information</div>
                    <div class="row">
                        <div class="col-md-6">
                            <!-- Text input-->
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtAddress1">Address 1</label>
                                <div class="col-md-12">
                                    <input id="txtAddress1" name="txtAddress1" type="text" placeholder="" value="<?php echo ($address1); ?>" required class="form-control input-md required-input">

                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <!-- Text input-->
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtAddress2">Address 2</label>
                                <div class="col-md-12">
                                    <input id="txtAddress2" name="txtAddress2" type="text" placeholder="" value="<?php echo ($address2); ?>" class="form-control input-md">

                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="cboCountry">Country</label>
                                <div class="col-md-12">
                                    <select id="cboCountry" name="cboCountry" class="form-control step1 input-md  select2_single" required>
                                        <option value="" selected>Select</option>
                                        <?php

                                        if ($countries != "") {
                                            while ($row = mysqli_fetch_assoc($countries)) {
                                                $location_id  = $row['location_id'];
                                                $name  = stripslashes($row['name']);

                                        ?>
                                                <option value="<?php echo ($location_id); ?>" <?php if ($dbCountryId == $location_id) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                                        <?php

                                            }
                                        }
                                        ?>
                                    </select>
                                    <div id="error-cboCountry"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <!-- Select Basic -->
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtCity">City</label>
                                <div class="col-md-12">
                                    <input id="txtCity" name="txtCity" type="text" placeholder="" value="<?php echo ($city); ?>" required class="form-control input-md required-input">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <!-- Select Basic -->
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="cboState">State</label>
                                <div class="col-md-12">
                                    <select id="cboState" name="cboState" class="form-control step2 input-md select2_single" required data-parsley-errors-container="#error-cboState">
                                        <option value="" selected>Select</option>
                                    </select>
                                    <div id="error-cboState"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <!-- Text input-->
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtZipCode">Zip Code</label>
                                <div class="col-md-12">
                                    <input id="txtZipCode" name="txtZipCode" type="text" placeholder="" value="<?php echo ($zip); ?>" required class="form-control input-md required-input">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="txtCountryCode">Country Code</label>
                                <div class="col-md-12">
                                    <input id="txtCountryCode" name="txtCountryCode" type="text" placeholder="" value="<?php //echo ($countryCode);
                                                                                                                        ?>" class="form-control input-md" disabled>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="formSubHeading">System Information</div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="cbotimeZone">Time Zone</label>
                                <div class="col-md-12">
                                    <select id="cbotimeZone" name="cbotimeZone" class="form-control input-md required-input select2_single" required data-parsley-errors-container="#error-txtTimezone">
                                        <option value="" selected>Select</option>

                                        <?php
                                        if ($rowTimezone != "") {
                                            while ($row = mysqli_fetch_assoc($rowTimezone)) {
                                                $seltimeZoneId  = $row['timeZoneId'];
                                                $locations  = stripslashes($row['locations']);
                                        ?>

                                                <option value="<?php echo ($seltimeZoneId); ?>" <?php if ($timeZoneId == $seltimeZoneId) { ?> selected="true" <?php } ?>><?php echo ($locations); ?></option>
                                        <?php

                                            }
                                        }
                                        ?>
                                    </select>
                                    <div id="error-txtTimezone"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <!-- Text input-->
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="cbobilling">Billing</label>
                                <div class="col-md-12">
                                    <select id="cbobilling" name="cbobilling" class="form-control input-md required-input select2_single" required data-parsley-errors-container="#error-txtBilling">
                                        <option value="" selected>Select</option>

                                        <?php
                                        if ($rowBilling != "") {
                                            while ($row = mysqli_fetch_assoc($rowBilling)) {
                                                $selbillingId  = $row['billingId'];
                                                $billingTitle  = stripslashes($row['billingTitle']);
                                        ?>

                                                <option value="<?php echo ($selbillingId); ?>" <?php if ($billingId == $selbillingId) { ?> selected="true" <?php } ?>><?php echo ($billingTitle); ?></option>
                                        <?php

                                            }
                                        }
                                        ?>
                                    </select>
                                    <div id="error-txtBilling"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <!-- Text input-->
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="ContractStartDate">Contract Start Date</label>
                                <div class="col-md-12">
                                    <div class='input-group date' id='ContractStartDate'>

                                        <input type='text' name="ContractStartDate" id="ContractStartDate" class="form-control input-md required-input rotation_date dateInputFormat" value="<?php echo ($contractStartDate); ?>" placeholder="MM-DD-YYYY" required data-parsley-errors-container="#error-txtDate" />
                                        <span class="input-group-addon">
                                            <span class="glyphicon glyphicon-calendar"></span>
                                        </span>
                                    </div>
                                    <div id="error-txtDate"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <!-- Text input-->
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="cbocontractPeriod">Contract Period</label>
                                <div class="col-md-12">
                                    <select id="cbocontractPeriod" name="cbocontractPeriod" class="form-control input-md required-input select2_single" required data-parsley-errors-container="#error-txtContract">
                                        <option value="" selected>Select</option>

                                        <?php
                                        if ($rowContarctPeriods != "") {
                                            while ($row = mysqli_fetch_assoc($rowContarctPeriods)) {
                                                $selcontractPeriodId  = $row['contractPeriodId'];
                                                $contractPeriod  = stripslashes($row['contractPeriod']);

                                        ?>
                                                <option value="<?php echo ($selcontractPeriodId); ?>" <?php if ($contractPeriodId == $selcontractPeriodId) { ?> selected="true" <?php } ?>><?php echo ($contractPeriod); ?></option>
                                        <?php

                                            }
                                        }
                                        ?>
                                    </select>
                                    <div id="error-txtContract"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="AccountContactPerson">Account Payable Contact</label>
                                <div class="col-md-12">
                                    <input id="AccountContactPerson" name="AccountContactPerson" data-parsley-pattern="^(?:[A-Za-z]+[ -])*[A-Za-z]+$" type="text" value="<?php echo ($accountPayableContact); ?>" placeholder="" class="form-control input-md">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="AccountEmail">Account Payable Email</label>
                                <div class="col-md-12">
                                    <input id="AccountEmail" name="AccountEmail" type="email" placeholder="" value="<?php echo ($accountPayableEmail); ?>" class="form-control input-md">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6"> <!-- Text input-->
                            <div class="form-group">
                                <label class="col-md-12 control-label" for="AccountPhone">Account Payable Phone</label>
                                <div class="col-md-12">
                                    <input id="AccountPhone" name="AccountPhone" type="text" placeholder="" value="<?php echo ($accountPayablePhone); ?>" class="form-control input-md">

                                </div>
                            </div>
                        </div>
                    </div>
                    <!--- NEW DIV---->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="formSubHeading">Setting</div>

                            <!--<div class="form-group">
							 <label class="col-md-4 control-label" for="isCheckoff"></label>
							 <div class="col-md-8">
								<label> <input id="isCheckoff"  name="isCheckoff"   value="0"   type="radio"  class="input-md" <?php //if($defaultStudentCheckoff == 0) {
                                                                                                                                ?> checked <?php //}
                                                                                                                                            ?> > Student Checkoff.</label>
							</div>
                        </div>

						<div class="form-group">
							 <label class="col-md-4 control-label" for="isCheckoff"></label>
							 <div class="col-md-8">
								<label> <input id="isCheckoff"  name="isCheckoff"   value="1"   type="radio"  class="input-md" <?php //if($defaultStudentCheckoff == 1) {
                                                                                                                                ?>checked <?php //}
                                                                                                                                            ?> > Clinician Checkoff.</label>
							</div>
                        </div> -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="col-md-12 control-label" for="cbotimeZone">Checkoff</label>
                                        <div class="col-md-12">
                                            <select id="isCheckoff" name="isCheckoff" class="form-control input-md required-input select2_single" required>

                                                <option value="1" <?php if ($defaultStudentCheckoff == "1") echo 'selected="selected"'; ?>>Standard</option>
                                                <option value="0" <?php if ($defaultStudentCheckoff == "0") echo 'selected="selected"'; ?>>Advanced</option>
                                                <option value="2" <?php if ($defaultStudentCheckoff == "2") echo 'selected="selected"'; ?>>Military</option>


                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group defaultCIEval">
                                        <label class="col-md-12 control-label">Auto Approved CI Evaluation</label>
                                        <div class="col-md-12">
                                            <select id="isdefaultCiEval" name="isdefaultCiEval" class="form-control input-md required-input select2_single" required>
                                                <option value="1" <?php if ($isdefaultCiEval == "1") echo 'selected="selected"'; ?>>Yes</option>
                                                <option value="0" <?php if ($isdefaultCiEval == "0") echo 'selected="selected"'; ?>>No</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <!-- for access to clinician reports -->
                                    <div class="form-group">
                                        <label class="col-md-12 control-label" for="cbotimeZone">Enable Reports To Clinicains</label>
                                        <div class="col-md-12">
                                            <select id="isClinicianReports" name="isClinicianReports" class="form-control input-md required-input select2_single" required>

                                                <option value="0" <?php if ($isClinicianReports == "0") echo 'selected="selected"'; ?>>No</option>
                                                <option value="1" <?php if ($isClinicianReports == "1") echo 'selected="selected"'; ?>>Yes</option>


                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group defaultCIEval">
                                        <label class="col-md-12 control-label" style="padding-top: 0">Checkoff Type</label>
                                        <input style="width: 27px; vertical-align: text-top;" type="radio" id="checkoffTypeBoth" name="checkoffType" value="0" <?php if ($checkoffType == 0 && $checkoffType != '') echo 'checked'; ?>> Both<input style="width: 27px; vertical-align: text-top;" type="radio" id="checkoffTypeA" name="checkoffType" value="1" <?php if ($checkoffType == 1 && $checkoffType != '') echo 'checked'; ?>>A<input style="width: 27px; vertical-align: text-top;" type="radio" id="checkoffTypeAA" name="checkoffType" value="2" <?php if ($checkoffType == 2 && $checkoffType != '') echo 'checked'; ?>>AA
                                        <?php if ($currentSchoolId == 127) { ?>
                                            <input style="width: 27px; vertical-align: text-top;" type="radio" id="checkoffTypeMupa" name="checkoffType" value="3" <?php if ($checkoffType == 3 && $checkoffType != '') echo 'checked'; ?>> Mansfield
                                        <?php } ?>

                                        <!-- <input style="width: 27px; vertical-align: text-top;" type="radio" id="pacrMap1BPNormal" name="visi1Check" value="0" > normal<input style="width: 27px; vertical-align: text-top;" type="radio" id="pacrMap1BPAbnormal" name="visi1Check" value="1" <?php if ($visi1Check == 1) echo 'checked'; ?>>abnormal -->

                                    </div>
                                </div>
                            </div>

                            <div class="formSubHeading hide">Domain Information</div>
                            <div class="form-group hide">
                                <label class="col-md-12 control-label" for="chkIsDomainActive">Is Domain Active</label>
                                <div class="col-md-12">
                                    <input type="checkbox" <?php if ($isDomainActive) echo 'checked'; ?> id="chkIsDomainActive" name="chkIsDomainActive">
                                </div>
                            </div>
                            <div class="form-group hide">
                                <label class="col-md-12 control-label" for="txtDomainName">Domain Name</label>
                                <div class="col-md-12">
                                    <input id="txtDomainName" name="txtDomainName" <?php if ($isDomainActive) echo 'required'; ?> placeholder="example.com" type="text" placeholder="" value="<?php echo ($domainName); ?>" class="form-control input-md <?php if ($isDomainActive) echo 'required-input'; ?> ">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="formSubHeading mt-15">School Images</div>


                </div>
            </div>

            <div style="display: flex; gap: 40px;">
                <section class="upload-section" id="uploadSection">
                    <div class="upload-area" id="uploadArea">
                        <input style="visibility: hidden;" type="file" id="fileInput" accept="image/*" hidden>
                        <div class="upload-content">
                            <svg class="upload-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                <polyline points="7,10 12,15 17,10"></polyline>
                                <line x1="12" y1="15" x2="12" y2="3"></line>
                            </svg>
                            <p>Click to browse or drag and drop an image</p>
                            <span class="file-types">Supports: JPG, PNG, GIF, WebP</span>
                        </div>
                    </div>
                </section>

                <!-- Cropper Section -->
                <section class="cropper-section" id="cropperSection" style="display: none;">
                    <div class="controls">
                        <div class="control-group">
                            <div class="mode-buttons">
                                <button type="button" class="mode-btn active" data-mode="rectangle">Rectangle</button>
                                <button type="button" class="mode-btn" data-mode="square">Square</button>
                            </div>
                        </div>
                        <div class="control-group">
                            <button type="button" class="action-btn" id="resetBtn">Reset</button>
                            <button type="button" class="action-btn" id="backBtn">Back</button>
                            <button type="button" class="action-btn" id="bgRemovelToggle" style="display: none;">Remove Background</button>
                            <button type="button" class="action-btn primary" id="cropBtn" style="display: none;">Crop & Save</button>
                        </div>
                        <div class="control-group">
                            <label>Zoom:</label>
                            <div class="zoom-controls">
                                <button type="button" class="zoom-btn" id="zoomOut">-</button>
                                <span class="zoom-level" id="zoomLevel">100%</span>
                                <button type="button" class="zoom-btn" id="zoomIn">+</button>
                                <input type="range" id="zoomSlider" min="20" max="300" value="100" class="zoom-slider">
                            </div>
                        </div>


                    </div>
                    <div style="display: flex; gap: 20px; align-items: flex-start;">
                        <div class="image-container" id="imageContainer">
                            <img id="previewImage" src="" alt="Preview">
                            <div class="crop-overlay" id="cropOverlay">
                                <div class="crop-selection" id="cropSelection"></div>
                            </div>
                        </div>

                        <div class="preview-section">
                            <h4>Cropped Preview</h4>
                            <!-- <canvas id="previewCanvas"></canvas> -->
                            <canvas id="previewCanvas" name='previewCanvas'></canvas>
                        </div>
                    </div>

                </section>




            </div>
            <div class="form-group">
                <!-- <label class="col-md-2 control-label"></label> -->
                <div class="col-md-12" style="display: flex; justify-content: center; margin: 20px 0;gap: 15px;">
                    <button id="btnSubmit" class="btn btn-success" name="btnSubmit">Save</button>
                    <a type="button" href="<?php echo $cancelUrl; ?>" class="btn btn-default">Cancel</a>
                </div>
            </div>
        </form>
    </div>

    <?php include('includes/footer.php'); ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/jquery.cascadingdropdown.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/inputmask/jquery.inputmask.bundle.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/inputmask/inputmask.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/cropper.js"></script>


    <script type="text/javascript">
        $('#txtEmail').blur(function() {
            $('#txtContactEmail').val($('#txtEmail').val())
        });

        //$('#txtSchoolName').blur(function(){$('#txtSlug').val($('#txtSchoolName').val())});


        <?php if ($schoolId == 0) { ?>

            $("#txtSchoolName").change(function() {

                var pagetitle = $('#txtSchoolName').val();
                var pageslug = $.trim($('#txtSlug').val());
                if (pageslug == "") {
                    pageslug = pagetitle;
                }
                pageslug = pageslug.toLowerCase();
                pageslug = pageslug.replace(/[^a-zA-Z0-9]+/g, '-');
                //$("#txtSlug").val(pageslug);

                $.ajax({
                    type: "POST",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_check_duplicate_slug.html",
                    data: {
                        slug: pageslug,
                        type: 'School'
                    },
                    success: function(responseData) {
                        if (responseData == 1) {
                            //$("#txtSlug").val(pageslug);
                            alertify.error('School slug available.');
                            $("#txtSchoolName").val('').focus();
                        } else {
                            $("#txtSlug").val(pageslug);
                        }
                    }
                });

            });
        <?php } ?>

        $("#txtSlug").change(function() {
            var currentpageslug = $(this).val();
            $.ajax({
                type: "POST",
                url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_check_duplicate_slug.html",
                data: {
                    slug: currentpageslug,
                    type: 'School'
                },
                success: function(responseData) {
                    if (responseData == 1) {

                        alertify.error('School slug available.');
                        $("#txtSlug").val('').focus();
                    }
                }
            });

        });


        $(window).load(function() {



            $(".select2_single").select2();
            $('#select2-cboSchoolType-container').addClass('required-select2');
            $('#select2-cboCountry-container').addClass('required-select2');
            $('#select2-cboState-container').addClass('required-select2');
            $('#select2-cbotimeZone-container').addClass('required-select2');
            $('#select2-cbotimeZone-container').addClass('required-select2');
            $('#select2-cbobilling-container').addClass('required-select2');
            $('#select2-cbocontractPeriod-container').addClass('required-select2');

            var defaultCheckoff = $("#isCheckoff").val();

            // if(defaultCheckoff == 1)
            //     $(".defaultCIEval").addClass("show");
            // else
            //     $(".defaultCIEval").addClass("hide");

            // $("#isCheckoff").change(function(){
            //    var isCheckoff = $(this).val();
            //    if(isCheckoff == 1)
            //    {
            //     $(".defaultCIEval").addClass("show");
            //     $(".defaultCIEval").removeClass("hide");
            //    }
            //    else {
            //      $(".defaultCIEval").addClass("hide");
            //      $(".defaultCIEval").removeClass("show");
            //    }

            // });


            $('.image-preview').magnificPopup({
                type: 'image'
            });

            $('#frmSchool').parsley().on('field:validated', function() {
                    var ok = $('.parsley-error').length === 0;
                })
                .on('form:submit', function() {
                    ShowProgressAnimation();
                    $('#txtCountryCode').prop('disabled', false);

                    // Check if we have a cropped image and no regular file upload
                    var hasCroppedImage = $('#hasCroppedImage').val() === '1';
                    var hasFileUpload = $('#fileLogo')[0].files.length > 0;

                    // If we have a cropped image but no file upload, we need to handle this
                    if (hasCroppedImage && !hasFileUpload) {
                        // The cropped image data is already in the hidden field
                        console.log('Submitting form with cropped image data');
                    }

                    return true;
                });

            $('#ContractStartDate').datetimepicker({

                format: 'MM/DD/YYYY'
                //minDate: new Date()

            });

            <?php

            if ($dbCountryId == 0) {
            ?>
                $('#cboCountry').val('224').trigger('change');
            <?php
            }
            ?>

            $('#frmSchool').cascadingDropdown({
                selectBoxes: [{
                        selector: '.step1',
                        selected: '<?php echo ($dbCountryId); ?>'
                    },
                    {
                        selector: '.step2',
                        selected: '<?php echo ($dbStateId); ?>',
                        requires: ['.step1'],
                        requireAll: true,
                        source: function(request, response) {

                            $.getJSON('<?php echo ($dynamicOrgUrl); ?>/ajax/getStates.html', request, function(data) {
                                response($.map(data, function(item, index) {
                                    return {
                                        label: item['StateName'],
                                        value: item['StateId']
                                    };
                                }));
                            });
                        }
                    }
                ]

            });


        });
        //domain logic
        $('#chkIsDomainActive').click(function() {
            if ($(this).prop('checked') == true) {
                $('#txtDomainName').addClass('required-input');
                $('#txtDomainName').attr('required', '');
            } else {
                $('#txtDomainName').removeClass('required-input');
                $('#txtDomainName').removeAttr('required');
            }
        });

        $('#cboCountry').on('change', function() {

            var countryId = $(this).val();
            // console.log(countryId);
            var schoolId = '<?php echo $currentSchoolId; ?>';

            $.ajax({
                type: "POST",
                url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_get_country_code.html",
                data: {
                    countryId: countryId,
                    schoolId: schoolId
                },
                success: function(responseData) {
                    var countryCode = '+' + responseData;
                    $('#txtCountryCode').val(countryCode);
                }
            });
        });
    </script>
   
    <script>
        // Initialize the image cropper when the page loads
        $(document).ready(function() {
            new ImageCropper();
            
            // Check if default school image exists and display it
            const defaultImagePath = '<?php echo $defaultSchoolImagePath; ?>';
            if (defaultImagePath && defaultImagePath !== '') {
                const $uploadArea = $('#uploadArea');
                const $uploadContent = $('.upload-content');
                
                // Add cropped state class to indicate we have an image
                $uploadArea.addClass('cropped-state');
                
                // Update the content with the existing image
                $uploadContent.html(`
                    <div style="display: flex; flex-direction: column; align-items: center; gap: 10px;">
                        <img src="${defaultImagePath}?randId=<?php echo time(); ?>" 
                             style="max-width: 150px; max-height: 150px; border-radius: 8px; border: 2px solid #667eea; object-fit: contain; box-shadow: 0 4px 8px rgba(0,0,0,0.1);" 
                             alt="School Logo Preview">
                        <div style="text-align: center;">
                            <p style="color: green; font-weight: bold; margin: 5px 0; font-size: 14px;">✓ Current school logo</p>
                            <span class="file-types" style="color: #666; font-size: 12px;">Click to select a different image if needed</span>
                        </div>
                    </div>
                `);
            }
        });
    </script>
</body>

</html>
